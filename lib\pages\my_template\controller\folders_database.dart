import 'dart:io';

import 'package:text/application.dart';
import 'package:text/pages/my_template/abstract/my_template_database_abstract.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/utils/niimbot_db_config.dart';
import 'package:text/utils/niimbot_db_helper.dart';
import 'package:text/utils/niimbot_db_model.dart';

class FoldersDataBase extends MyTemplateDataBaseAbstract {
  @override
  Future add(List<dynamic> dbModels) async {
    var result = await NiimbotDbHelper.dbInsertDBData(TableDescrp.folder, List<NiimbotDbModel?>.from(dbModels));
    return result.isNotEmpty;
  }

  Future addToType(List<FolderModel> dbModels, {type = 0}) async {
    var models = dbModels.map((e) => e.copyWith(type: e.shared == null ? 0 : 1)).toList();
    // models.forEach((element) {
    //   delete(id: element.id.toString());
    // });//批量删除不可调用异常方法循环，采用事务批量删除
    await NiimbotDbHelper.dbDeleteDbDataByIds(TableDescrp.folder, models.map((e) => e.id.toString()).toList(),
        idName: Platform.isIOS ? "xyid" : "ID");
    var result = await NiimbotDbHelper.dbInsertDBData(TableDescrp.folder, models);
    return result.isNotEmpty;
  }

  Future addToShareIds(List<FolderModel> dbModels, {type = 2}) async {
    var models = dbModels.map((e) => e.copyWith(type: type)).toList();
    List<FolderModel> dbFolderModel = [];
    List<FolderModel> modelsExist = await shareMeQuery(type: 2);
    models.forEach((element) async {
      // List<FolderModel> models = await shareMeQueryForId(
      //   folderId: element.id!.toInt(),
      // );
      element.sharedUserIds?.add(Application.user!.userId.toString());
      dbFolderModel.add(element);
      // if (models.isNotEmpty) {
      //   models.forEach((element) {
      //     var userIds = element.sharedUserIds;
      //     delete(id: element.id.toString());
      //     if (!userIds!.contains(Application.user!.userId.toString())) {
      //       userIds.add(Application.user!.userId.toString());
      //     }
      //     element.sharedUserIds = userIds;
      //     dbFolderModel.add(element);
      //   });
      // } else {
      //   element.sharedUserIds?.add(Application.user!.userId.toString());
      //   dbFolderModel.add(element);
      // }
    });
    var result = await NiimbotDbHelper.dbInsertDBData(TableDescrp.folder, dbFolderModel);
    return result.isNotEmpty;
  }

  @override
  Future delete({String? id, type = 0}) async {
    var result;
    if (type == 0 || type == 1) {
      result = id == null
          ? await NiimbotDbHelper.dbDeleteDBDataWhere(TableDescrp.folder,
              where: Platform.isIOS ? "user_id = ?" : "USER_ID = ?", whereArgs: [Application.user!.userId])
          : await NiimbotDbHelper.dbDeleteDBDataWhere(TableDescrp.folder,
              where: Platform.isIOS ? "xyid = ? and user_id = ?" : "ID = ? and USER_ID = ?",
              whereArgs: [id, Application.user!.userId]);
    } else {
      var sharedUserIdsWhere = Platform.isIOS ? " and sharedUserIds like ?" : " and SHARED_USER_IDS like ?";
      var where = "";
      var whereArgs = [];
      if (id == null) {
        where = Platform.isIOS ? "sharedUserIds like ?" : "SHARED_USER_IDS like ?";
        whereArgs = [Application.user!.userId];
        result = await NiimbotDbHelper.dbDeleteDBDataWhere(TableDescrp.folder, where: where, whereArgs: whereArgs);
      } else {
        where = Platform.isIOS ? "xyid = ?" + sharedUserIdsWhere : "ID = ?" + sharedUserIdsWhere;
        whereArgs = [id, Application.user!.userId];
        result = await NiimbotDbHelper.dbDeleteDBDataWhere(TableDescrp.folder, where: where, whereArgs: whereArgs);
      }
    }
    return result;
  }

  @override
  Future<List<FolderModel>> query({String? id, int pageIndex = 1, int type = 0}) async {
    List whereArguments;
    dynamic userId;
    var where = Platform.isIOS ? "user_id = ?" : "USER_ID = ?";
    if (Platform.isIOS) {
      userId = Application.user == null ? "" : Application.user!.id.toString();
      whereArguments = [userId];
      if (type != 0) {
        where += "and type = ?";
        whereArguments.add(type);
      }
      if (id != null) {
        where += "and xyid = ?";
        whereArguments.add(id);
      }
    } else {
      userId = Application.user == null ? 0 : Application.user!.userId;
      whereArguments = [userId];
      if (type != 0) {
        where += "and TYPE = ?";
        whereArguments.add(type);
      }
      if (id != null) {
        where += "and ID = ?";
        whereArguments.add(int.tryParse(id) ?? 0);
      }
    }
    // 添加按 gmtModified 降序排序
    String orderBy = Platform.isIOS ? "gmt_modified DESC" : "GMT_MODIFIED DESC";
    List<Map<String, dynamic>> map = await NiimbotDbHelper.dbQueryDBDataWhere(TableDescrp.folder,
        where: where, whereArgs: whereArguments, orderBy: orderBy);
    List<FolderModel> models = [];
    map.forEach((element) {
      if (element["xyid"] == "0") return;
      models.add(FolderModel.fromDBJson(element));
    });
    return models;
  }

  Future<List<FolderModel>> shareMeQuery({
    String? id,
    int pageIndex = 1,
    int type = 0,
    String? folderId,
  }) async {
    List whereArguments;
    dynamic userId;
    var where = Platform.isIOS ? "sharedUserIds like ?" : "SHARED_USER_IDS like ?";
    if (Platform.isIOS) {
      userId = Application.user == null ? "" : Application.user!.userId.toString();
      whereArguments = ["%$userId%"];
      where += "and type = ?";
      whereArguments.add(type);
      if (id != null) {
        where += "and xyid = ?";
        whereArguments.add(id);
      }
    } else {
      userId = Application.user == null ? 0 : Application.user!.userId;
      whereArguments = ["%$userId%"];
      where += "and TYPE = ?";
      whereArguments.add(type);
      if (id != null) {
        where += "and ID = ?";
        whereArguments.add(int.tryParse(id) ?? 0);
      }
    }
    // 添加按 gmtModified 降序排序
    String orderBy = Platform.isIOS ? "gmt_modified DESC" : "GMT_MODIFIED DESC";
    List<Map<String, dynamic>> map = await NiimbotDbHelper.dbQueryDBDataWhere(TableDescrp.folder,
        where: where, limit: 100, offset: (pageIndex - 1) * 100, whereArgs: whereArguments, orderBy: orderBy);
    // List<Map<String, dynamic>> map = await NiimbotDbHelper.dbQueryDBDataWhere(TableDescrp.folder,
    //     where: "type = ?", limit: 10, offset: (pageIndex - 1) * 10, whereArgs: [2]);
    List<FolderModel> models = [];
    map.forEach((element) {
      if (element["xyid"] == "0") return;
      models.add(FolderModel.fromDBJson(element));
    });
    return models;
  }

  Future<List<FolderModel>> shareMeQueryForId({
    int? folderId,
  }) async {
    List<Map<String, dynamic>> map = await NiimbotDbHelper.dbQueryDBDataWhere(TableDescrp.folder,
        where: Platform.isIOS ? "xyid = ?" : "ID = ?", whereArgs: [folderId]);
    List<FolderModel> models = [];
    map.forEach((element) {
      if (element["xyid"] == "0") return;
      models.add(FolderModel.fromDBJson(element));
    });
    return models;
  }

  @override
  Future update(List<dynamic> dbModels, {type = 0}) async {
    var models = dbModels.map((e) => e.copyWith(type: type)).toList();
    var result = await NiimbotDbHelper.dbUpDateDBData(TableDescrp.folder, models);
    return result;
  }

  Future batchDelete(List<dynamic> dbModels) {
    return Future.value();
  }
}
