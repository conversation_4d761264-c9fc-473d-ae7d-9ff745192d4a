package com.niimbot.baselibrary.core

import com.niimbot.fastjson.JSON
import com.blankj.utilcode.util.LogUtils
import com.niimbot.baselibrary.core.SLSAuthentication
import com.niimbot.baselibrary.core.SlsAuthCacheManager
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.sensorsdata.analytics.android.sdk.external.SLSTokenData
import com.sensorsdata.analytics.android.sdk.external.SLSTokenEmitter

/**
 * @ClassName: SLSTokenEmitter
 * @Author: <PERSON><PERSON>owen
 * @Date: 2022/3/8 16:25
 * @Description: 获取token
 */
class CommonSLSTokenEmitter: SLSTokenEmitter {

    var stsToken = ""
    var userAgent = ""
    private var slsAuthCacheManager:SlsAuthCacheManager = SlsAuthCacheManager.getInstance()
    override fun emit(): SLSTokenData? {
        LogUtils.e("CommonSLSTokenEmitter---> emit()")
        return try {
            if (stsToken.isNullOrEmpty()) return null
            var cacheToken = slsAuthCacheManager.getSlsAuthentication(stsToken)
            if(cacheToken != null){
                return cacheToken.transformToSlsTokenData()
            }
            val responseBody = DokitOkGo.post<String>(stsToken)
                .headers("Connection", "keep-alive")
                .headers("need_encrypt", "1")
                .execute().body?.string()
            if(responseBody.isNullOrEmpty()) return null
            var json= JSON.parseObject((JSON.parseObject(responseBody).getString("data")),SLSAuthentication::class.java)
            var token:SLSTokenData? = null
            json?.let {
                slsAuthCacheManager.setToken(stsToken,json)
                token = it.transformToSlsTokenData()
            }
            token
        } catch (e: Exception) {
            LogUtils.e("CommonSLSTokenEmitter failed")
            e.printStackTrace()
            slsAuthCacheManager.clearToken(stsToken)
            null
        }
    }
}
