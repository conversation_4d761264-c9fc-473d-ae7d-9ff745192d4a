import 'dart:async';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:get/get.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/data_source_wrapper.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/escape_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/good_lib_field_info.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/template_task.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/rfid_bind/rfid_info_manager.dart';

Logger _logger = Logger("ExcelTransformManager", on: kDebugMode);

/// Excel导入新结构转换管理 目标是去除externalData task等结构
class ExcelTransformManager {
  String? documentPath;
  TemplateData? templateData;

  /// 列头数组
  List<String>? headers;

  /// excel内容二维数据：一维为行数，二维为当前行数据
  List<List<String>>? rowData;
  Map<String, DataSourceWrapper> dataSourceWrapperMap = {};

  String? excelId;

  factory ExcelTransformManager() => sharedInstance();

  static ExcelTransformManager? _excelTransformManager;

  static ExcelTransformManager sharedInstance() {
    if (_excelTransformManager == null) {
      _excelTransformManager = new ExcelTransformManager._internal();
    }
    return _excelTransformManager!;
  }

  ExcelTransformManager._internal();

  // getter 方法
  DataSource? getDataSource() {
    if (templateData?.dataSource?.isEmpty ?? true) {
      return null;
    }
    return templateData!.dataSource?.firstOrNull;
  }

  setDataSourceWrapper(DataSourceWrapper? dataSourceWrapper) {
    if (dataSourceWrapper == null) {
      return;
    }
    headers = dataSourceWrapper.headers;
    rowData = dataSourceWrapper.rowData;
    excelId = dataSourceWrapper.excelId;
    templateData?.dataSource = [dataSourceWrapper.dataSource];
    dataSourceWrapperMap[dataSourceWrapper.dataSource.hash] =
        dataSourceWrapper;
    }

  resetExcelContent(String? excelHash) {
    Map dataSourceWrapperMap = ExcelTransformManager().dataSourceWrapperMap;
    DataSourceWrapper wrapper = dataSourceWrapperMap[excelHash];
    headers = wrapper.headers;
    rowData = wrapper.rowData;
  }

  List<String>? getElementDataBind() {
    final dataSource = getDataSource();
    if (dataSource == null) {
      return null;
    }
    String filePath = getExcelFilePath();
    List<String> sheets = getSheetsFix(filePath);
    List<String>? dataBind = null;
    if (dataSource.type == DataSourceType.commodity) {
      dataBind = ["", ""];
    } else {
      String sheetName = sheets.first ?? "Sheet1";
      // dataBind = [dataSource?.hash, dataSource?.name];
      dataBind = [dataSource.hash, sheetName];
        }

    return dataBind;
  }

  ///获取标准单元格占位符---A4
  String getCellPlaceHolder(int bindingColumn) {
    if (bindingColumn <= -1) {
      return "";
    }
    // ExcelPageInfo pageInfo = templateData.bindInfo;
    // int row = 0;
    // if (pageInfo != null) {
    //   row = pageInfo.page;
    // }
    String columnLetter = NiimbotExcelUtils.indexToLetters(bindingColumn + 1);
    return "${columnLetter}0";
  }

  String escapeValueModify(JsonElement jsonElement, int modifyPage,
      String escapeValueModify, bool addContentTitle, bool parseContentAffix) {
    DataBindModify? elementModify =
        getElementModify(jsonElement, currentExcelRow: modifyPage);
    if (elementModify != null) {
      String applyTitle = "";
      String applyDelimiter = "";
      if (addContentTitle && (elementModify.useTitle ?? false)) {
        applyTitle = elementModify.title ?? '';
        applyDelimiter = elementModify.delimiter ?? '';
      }
      if (jsonElement.isBindingCommodity()) {
        //如果绑定列已经被删除了，则显示空：<空>
        int bindingColumn = jsonElement.getBindingColumn();
        GoodLibFieldInfo? field = null;
        try {
          field = GoodFieldManager().goodFieldList.firstWhereOrNull((e)=> e.columnIndex == bindingColumn);
        }  catch (e, s) {
          debugPrint('异常信息:\n $e');
        }
        if(field != null && field.isDeleted == true){
          applyTitle = addContentTitle ? "${intlanguage("app100000949", "空")}" : "";
          escapeValueModify = "<${intlanguage("app100000949", "空")}>";
        }else{
          escapeValueModify = escapeValueModify.isEmpty
              ? "<${intlanguage("app100001135", "空")}>".replaceAll("%", "${elementModify.title}")
              : escapeValueModify;
        }
        if (elementModify.useTitle == true) {
          String title = escapeValueModify;
          escapeValueModify = applyTitle + applyDelimiter + title;
        }

      } else {
        String applyValue = elementModify.value ?? escapeValueModify;
        String applyPrefix = "";
        String applySuffix = "";
        if (parseContentAffix) {
          applyPrefix = elementModify.prefix ?? '';
          applySuffix = elementModify.suffix ?? '';
        }
        if (applyTitle.isEmpty) {
          escapeValueModify = "$applyPrefix$applyValue$applySuffix";
        } else {
          escapeValueModify =
              "$applyTitle$applyDelimiter$applyPrefix$applyValue$applySuffix";
        }
      }
      return escapeValueModify;
    } else {
      return escapeValueModify;
    }
  }

  ///获取转义值
  String getBindingValue(JsonElement jsonElement, int? pageIndex,
      bool addContentTitle, bool parseContentAffix) {
    String id = jsonElement.id;
    if (jsonElement.isMirrorElement()) {
      id = jsonElement.mirrorId;
    }
    String escapeValue = "";
    if (jsonElement.isBindingCommodity() && (rowData?.isEmpty ?? true)) {
      escapeValue = escapeValueModify(jsonElement, -1, escapeValue,
              addContentTitle, parseContentAffix) ??
          "";
      return escapeValue;
    }

    ///转义信息先从缓存的excel数据里面读取,没有的话再从excel插件里面读取
    if (rowData != null && (rowData?.isNotEmpty ?? false)) {
      int columIndex = jsonElement.getBindingColumn();
      int excelRow = getExcelRow(pageIndex ?? 0);
      int rowDataIndex;
      if (isCustomHeader()) {
        rowDataIndex = excelRow - 1;
      } else {
        rowDataIndex = excelRow - 2;
      }
      escapeValue = columIndex >= rowData![rowDataIndex].length ? "" : rowData![rowDataIndex][columIndex];
      escapeValue = escapeValueModify(jsonElement, excelRow, escapeValue, addContentTitle, parseContentAffix);
      _logger.log("rowData escapeValue=$escapeValue");
    } else {
      escapeValue = NiimbotDataSourceUtils.getElementBindValueSync(
        id,
        jsonElement.value!,
        jsonElement.dataBind,
        templateData?.dataSource,
        templateData?.modify,
        (pageIndex ?? 0) + 1,
        documentPath!,
      );
      _logger.log("excel plugin escapeValue=$escapeValue");
    }
    // String escapeValue = NiimbotDataSourceUtils.getElementBindValueSync(id, jsonElement.value, jsonElement.dataBind,
    //     templateData.dataSource, templateData.modify, pageIndex + 1, documentPath);
    return escapeValue;
  }

  ///获取元素修改信息
  ///2024/11/4 Ice_Liu 画板更新商品库时会同步到电子价签，画板新增商品库时，值为空会显示“title+空”，此时会填充title到DatabindModify中，导致电子价签显示了title
  ///因此此处增加getRealContentTitle，只针对电子价签
  DataBindModify? getElementModify(JsonElement jsonElement,
      {int? currentExcelRow = null, bool getRealContentTitle = false}) {
    try{
      if (!jsonElement.isBindingElement()) {
        return null;
      }
      final modify = templateData?.modify;
      if (modify == null || modify.isEmpty) {
        return null;
      }
      String elementId = jsonElement.id;
      if (jsonElement.isMirrorElement()) {
        elementId = jsonElement.mirrorId;
      }
      Map<String, DataBindModify>? modifyMap = modify[elementId];
      if (modifyMap != null) {
        DataBindModify? globalModify = modifyMap['0'];
        CellAddress cellAddress =
        NiimbotExcelUtils.decodeCellIndex(jsonElement.value!);
        final rowModify = modifyMap[(currentExcelRow ??
            ExcelTransformManager.sharedInstance().getCurrentExcelRow())
            .toString()];
        bool useTitle = rowModify?.useTitle ?? globalModify?.useTitle ?? false;
        String delimiter = rowModify?.delimiter ?? globalModify?.delimiter ?? "：";
        String title = "";
        if (useTitle || (jsonElement.isBindingCommodity() && (!getRealContentTitle || jsonElement.hasContentTitle()))) {
          title = rowModify?.title ??
              globalModify?.title ??
              (getExcelHeaders().length > cellAddress.c
                  ? getExcelHeaderByColumnIndex(cellAddress.c)
                  : "");
        }
        String fullTitle = useTitle ? "$title$delimiter" : "";
        String prefix = rowModify?.prefix ?? globalModify?.prefix ?? "";
        final val = rowModify?.value ?? globalModify?.value ?? null;
        String suffix = rowModify?.suffix ?? globalModify?.suffix ?? "";

        DataBindModify realModify = DataBindModify(
            useTitle: useTitle,
            delimiter: delimiter,
            title: title,
            value: val,
            prefix: prefix,
            suffix: suffix);
        return realModify;
      }
      return null;
    }catch(e,s){
      return null;
    }

  }

  ///当前页
  int getCurrentPage() {
    final pageInfo = templateData?.bindInfo;
    int row = 1;
    if (pageInfo != null) {
      row = pageInfo.page ?? 1;
      if (row == 0) {
        row = 1;
      }
    }
    return row;
  }

  ///当前页在原始Excel的行号（非自定义表头的情况，行号从表头开始计算）
  int getCurrentExcelRow() {
    int currentPage = getCurrentPage();
    return getExcelRow(currentPage - 1);
  }

  ///获取元素原始的修改信息
  Map<String, DataBindModify>? getElementOriginModify(String elementId) {
    if (templateData?.modify?.isEmpty ?? true) {
      templateData?.modify = {};
    }
    final modify = templateData?.modify;
    Map<String, DataBindModify>? modifyMap =
        modify?.putIfAbsent(elementId, () => {});
    return modifyMap;
  }

  ///获取exel表头
  List<String> getExcelHeaders() {
    if (headers?.isNotEmpty ?? false) {
      return headers!;
    }
    String filePath = getExcelFilePath();
    DataSource? dataSource = templateData?.dataSource?.firstOrNull;
    List<String> sheets = getSheetsFix(filePath);
    String sheetName = sheets.firstOrNull ?? "Sheet1";
    //获取第一行内容作为表头
    List<String> excelHeaders =
        NiimbotExcelUtils.getSheetRow(filePath, sheetName, 1);
    return excelHeaders;
  }

  ///获取exel对应列表头内容
  String getExcelHeaderByColumnIndex(int columnIndex) {
    List<String> excelHeaders = getExcelHeaders();
    if (excelHeaders.length > columnIndex && columnIndex >=0) {
      String header = excelHeaders[columnIndex];
      if (header.isEmpty) {
        String columnLetter = NiimbotExcelUtils.indexToLetters(columnIndex + 1);
        header = "${intlanguage("app100001121", "列")}${columnLetter}";
      }
      return header;
    } else {
      return "";
    }
  }

  Future<DataSourceWrapper> getExcelContent() async {
    DataSource? dataSource = templateData?.dataSource?.firstOrNull;
    List<String> headers = getExcelHeaders();
    this.headers = headers;
    int columns = headers.length;
    String endColumnLetter = NiimbotExcelUtils.indexToLetters(columns);
    String range = "A2:$endColumnLetter${getExcelRowCount()}";
    print("ExcelTransformManager range=$range");
    //获取第一行内容作为表头
    String filePath = getExcelFilePath();
    List<String> sheets = getSheetsFix(filePath);
    String sheetName = sheets.firstOrNull ?? "Sheet1";
    List<List<String>> excelContent =
        await NiimbotDataSourceUtils.getDataSourceRange(
      dataSource!,
      sheetName,
      range,
    );
    rowData = excelContent;
    print("ExcelTransformManager headers=$headers");
    print("ExcelTransformManager excelContent=$rowData");
    DataSourceWrapper wrapper = DataSourceWrapper(
      headers: headers,
      rowData: excelContent,
      dataSource: dataSource,
    );
    return wrapper;
  }

  int getExcelRowCount({bool filterEmptyRow = false}) {
    if (rowData?.isNotEmpty ?? false) {
      return _getRowCount(rowData!, filterEmptyRow: filterEmptyRow);
    }
    int count = 0;
    if (templateData?.dataSource?.firstOrNull?.type != DataSourceType.commodity) {
      String filePath = getExcelFilePath();
      DataSource? dataSource = templateData?.dataSource?.firstOrNull;
      List<String> sheets = getSheetsFix(filePath);
      String sheetName = sheets.firstOrNull ?? "Sheet1";
      // int count = NiimbotExcelUtils.getSheetRowCount(filePath, sheetName);
      List<List<String>> excelContent =
          NiimbotExcelUtils.getContent(filePath, sheetName);
      count = _getRowCount(excelContent);
      if (dataSource?.headers?.isNotEmpty ?? false) {
        var headerInfo = dataSource!.headers![sheetName];

        ///设定第一行为表头的时候，页数-1
        if (headerInfo is int && count > 0) {
          count = count - 1;
        }
      }
      print("ExcelTransformManager rowCount=$count");
    }
    return count;
  }

  List<String> getSheetsFix(String filePath) {
    try {
      return NiimbotExcelUtils.getSheets(filePath);
    }  catch (e, s) {

    }
    return ['Sheet1'];
  }

  int _getRowCount(List<List<String>> allRowData,
      {bool filterEmptyRow = false}) {
    int count = 0;
    if (allRowData != 0 && allRowData.isNotEmpty) {
      for (int i = 0; i < allRowData.length; i++) {
        if (!filterEmptyRow || !isEmptyRow(allRowData[i])) {
          count++;
        }
      }
    }
    return count;
  }

  ///是否为空行
  bool isEmptyRow(List<String> oneRowData) {
    if (oneRowData.isEmpty) {
      return true;
    }
    for (int i = 0; i < oneRowData.length; i++) {
      if (oneRowData[i].trim().isNotEmpty) {
        return false;
      }
    }
    return true;
  }

  String getExcelFilePath() {
    DataSource? dataSource = templateData?.dataSource?.firstOrNull;
    String filePath = NiimbotDataSourceUtils.getLocalDataSourcePath(
      documentPath!,
      dataSource!.hash,
    );
    return filePath;
  }

  ///excel新数据源DataSource转换为旧数据源ExternalData
  ExternalData? transformDataSourceToExternalData() {
    final dataSource = getDataSource();
    if (dataSource == null) {
      return null;
    }
    List<List<String>> columnData = [];
    List<String> customHeaders = [];
    if(dataSource.type == DataSourceType.commodity){
      GoodFieldManager().goodFieldList.sort((a, b) => (a.columnIndex ?? 0).compareTo(b.columnIndex ?? 0));
      int columnSize = (headers ?? []).length;
      for (int i = 0; i < columnSize; i++) {
        bool isDeletedField = GoodFieldManager().goodFieldList[i].isDeleted ?? false;
        if(isDeletedField){
          customHeaders.add("");
        }else{
          customHeaders.add(headers![i]);
        }
        List<String> oneColumnData = [];
        for (int j = 0; j < (rowData ?? []).length; j++) {
          List<String> oneRow = rowData![j];
          oneColumnData.add(isDeletedField ? "": oneRow[i]);
        }
        columnData.add(oneColumnData);
      }
    }else{
      customHeaders = headers ?? [];
      int columnSize = (headers ?? []).length;
      for (int i = 0; i < columnSize; i++) {
        List<String> oneColumnData = [];
        for (int j = 0; j < (rowData ?? []).length; j++) {
          List<String> oneRow = rowData![j];
          oneColumnData.add(oneRow[i]);
        }
        columnData.add(oneColumnData);
      }
    }

    ExternalData externalData = ExternalData(externalDataList: [
      ExternalListModel(
          name: dataSource.name ?? "", data: ExternalListModelData(columnHeaders: customHeaders, columns: columnData))
    ], id: dataSource.type == DataSourceType.commodity ? "0" :(excelId ?? "0"), fileName: dataSource.name);

    return externalData;
  }

  ///excel新数据源修改信息TemplateModify转换为旧数据源修改信息TemplateTask
  TemplateTask? transformTemplateModifyToTemplateTask() {
    TemplateModify? modify = templateData?.modify;
    if (modify == null || modify.isEmpty) {
      return null;
    }
    modify = TemplateUtils.cloneModify(templateData!.modify);
    TemplateTask task = TemplateTask();
    task.externalDataID = excelId ?? "0";
    Map<String, Map<String, String>>? modifyData = modify?.map((key, value) {
      Map<String, String> subMap = value.map((subKey, dataBindModify) {
        //task结构中修改行从0开始
        return MapEntry(
            (int.tryParse(subKey) ?? 0 - 1).toString(), dataBindModify.value ?? "null");
      });
      return MapEntry(key, subMap);
    });

    modifyData?.forEach((key, value) {
      value.removeWhere((key, value) => (key == "-1" || value == "null"));
    });

    task.modifyData = modifyData;
    return task;
  }

  changeSelectRows(List<Range> ranges) {
    DataSource? dataSource = getDataSource();
    dataSource?.range = ranges;
  }

  /// 是否为自定义表头
  /// "headers": {
  ///         // 为数据源中的表自定义表头
  ///         "Sheet1": ["名称", "规格", "主条码"],
  ///         // 指定第一行为表头
  ///         "Sheet2": 1
  ///       }
  bool isCustomHeader() {
    final dataSource = getDataSource();
    if (dataSource?.headers?.isEmpty ?? true) {
      return false;
    }
    dynamic header = dataSource!.headers!.values.first;
    if (header == null || header.runtimeType == int) {
      return false;
    }
    return true;
  }

  /// 选择行范围
  List<Range> getSelectRanges() {
    final dataSource = getDataSource();
    if (dataSource == null || (rowData?.isEmpty ?? true)) {
      return [];
    }
    if ((dataSource.range?.isEmpty ?? true)) {
      if (isCustomHeader()) {
        return [Range(s: 1, e: rowData!.length)];
      } else {
        return [Range(s: 2, e: rowData!.length + 1)];
      }
    }
    return dataSource.range!;
  }

  /// 已选中行数
  int getSelectRowCount({int minCount = 0}) {
    List<Range> ranges = getSelectRanges();
    List<int> rows = transformRangesToRows(ranges);
    int selectCount = rows.length;
    if (selectCount < minCount) {
      selectCount = minCount;
    }
    return selectCount;
  }

  /// 通过画板显示页码的索引（从0开始），获取在原始Excel的行号（非自定义表头从2开始，自定义表头从1开始）
  int getExcelRow(int subPageIndex) {
    List<Range> ranges = getSelectRanges();
    List<int> rows = transformRangesToRows(ranges);
    if (subPageIndex < rows.length) {
      return rows[subPageIndex];
    }
    return subPageIndex + 1;
  }

  /// 判断行是否被选中
  /// [rowDataIndex] 原始Excel去除表头后，在rowData对应的索引（从0开始）
  bool isRowSelect(int rowDataIndex) {
    //原始Excel文件对应的行
    int excelRow;
    if (isCustomHeader()) {
      excelRow = rowDataIndex + 1;
    } else {
      excelRow = rowDataIndex + 2;
    }
    List<Range> range = getSelectRanges();
    if (range.isEmpty) {
      return false;
    }
    for (Range item in range) {
      if (excelRow >= item.s && excelRow <= item.e) {
        return true;
      }
    }
    return false;
  }

  /// Range列表格式转化为离散行号的数组
  /// [range] 已选行的range数组
  List<int> transformRangesToRows(List<Range> ranges) {
    List<int> array = [];
    for (int i = 0; i < ranges.length; i++) {
      Range item = ranges[i];
      for (int row = item.s; row <= item.e; row++) {
        array.add(row);
      }
    }
    return array;
  }

  /// 离散行号转化为Range列表
  /// [array] 保存离散行号的数组
  List<Range> transformRowsToRanges(List<int> rows) {
    if (rows.isEmpty) {
      return [];
    }
    if (rows.length == 1) {
      return [Range(s: rows.first, e: rows.first)];
    }
    List<int> sortedArray = List.from(rows)..sort((a, b) => a.compareTo(b));
    int start = sortedArray.first;
    int end = sortedArray.first;
    List<Range> ranges = [];
    for (int i = 1; i < sortedArray.length; i++) {
      int rowNumber = sortedArray[i];
      if (rowNumber == end + 1) {
        end = rowNumber;
      } else {
        Range range = Range(s: start, e: end);
        ranges.add(range);
        start = rowNumber;
        end = rowNumber;
      }
      if (rowNumber == sortedArray.last) {
        Range range = Range(s: start, e: end);
        ranges.add(range);
      }
    }
    return ranges;
  }

  /// 两个选中行范围是否相同
  /// [range1] 行范围1
  /// [range2] 行范围2
  bool isSameRange(List<Range> range1, List<Range> range2) {
    List<Range> compareRange1 = range1 ?? [];
    List<Range> compareRange2 = range2 ?? [];
    if (compareRange1.length != compareRange2.length) {
      return false;
    }
    for (int i = 0; i < compareRange1.length; i++) {
      if (compareRange1[i].s != compareRange2[i].s ||
          compareRange1[i].e != compareRange2[i].e) {
        return false;
      }
    }
    return true;
  }

  /// 选行范围格式化（容错处理）
  /// [range] 选中行的范围
  List<Range> formatRange(List<Range> range) {
    if (range.length == 0 || range.length == 1) {
      return range;
    }
    int start = range.first.s;
    int end = range.first.e;
    for (Range item in range) {
      if (item.s < start) {
        start = item.s;
      }
      if (item.e > end) {
        end = item.e;
      }
    }
    List<int> array = [];
    for (int i = start; i <= end; i++) {
      if (isRowInRange(i, range)) {
        array.add(i);
      }
    }
    return transformRowsToRanges(array);
  }

  /// 指定行是否在选中行的范围内
  /// [row] 行号（从1开始）
  /// [range] 选中行的范围
  bool isRowInRange(int row, List<Range> range) {
    for (Range item in range) {
      if (row >= item.s && row <= item.e) {
        return true;
      }
    }
    return false;
  }

  /// 获取画板当前页码指定行对应单元格cell的值
  /// [column] 行号（从1开始）
  String? getCellValue(int column){
    if (rowData?.isNotEmpty??false) {
      int excelRow = EscapeUtils.getExcelRow(templateData!, rowData!, templateData!.currentPageIndex);
      int rowDataIndex;
      if (EscapeUtils.isCustomHeader(templateData!)) {
        rowDataIndex = excelRow - 1;
      } else {
        rowDataIndex = excelRow - 2;
      }
      return rowData![rowDataIndex][column - 1];
    }
    return null;
  }

  clearData() {
    RfidInfoManager().setRfidBindingColumn(null);
    templateData = null;
    headers?.clear();
    headers = null;
    rowData?.clear();
    rowData = null;
    excelId = null;
    dataSourceWrapperMap.clear();
    CanvasHelper.printChannelCode = null;
  }
}
