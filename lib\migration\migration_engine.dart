import 'package:flutter/cupertino.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/template/util/template_detail_db_utils.dart';

import 'migration_handler.dart';

class MigrationEngine {
  final MigrationHandler handler;
  final ValueNotifier<MigrationStatus> status = ValueNotifier(MigrationStatus.idle);

  MigrationEngine(this.handler);

  Future<void> execute({int batchSize = 100}) async {
    status.value = MigrationStatus.running(0, 0);

    final total = await handler.sourceCount;
    int processed = 0;

    while (processed < total) {
      final items = await handler.getBatch(0, batchSize);
      if (items.isEmpty) {
        break;
      }

      final isar = DBManagerUtil.instance.isar;
      await isar.writeTxn(() async {
        for (final item in items) {
          await handler.migrateItem(item);
          processed++;
          status.value = MigrationStatus.running(processed, total);
        }
      });

      // await Future.delayed(Duration(milliseconds: 50));
    }

    status.value = MigrationStatus.completed(total);
    bool isMigrated = await handler.isMigrated;
    if (!isMigrated) {
      await handler.markAsMigrated();
    }
    if (handler is TemplateMigrationHandler && !isMigrated) {
      //将数据库中localType=sync状态的模版更新为localType=default
      await TemplateDetailDBUtils.resetSyncTemplatesToDefault();
    }
  }
}

//迁移进度状态
class MigrationStatus {
  final int current;
  final int total;
  final bool isDone;

  MigrationStatus.running(this.current, this.total) : isDone = false;

  MigrationStatus.completed(this.total)
      : current = total,
        isDone = true;

  static final idle = MigrationStatus.running(0, 0);
}
