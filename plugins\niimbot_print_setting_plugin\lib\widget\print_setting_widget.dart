import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:nety/nety.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/model/color.dart';
import 'package:niimbot_print_setting_plugin/model/update_field_manager.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:niimbot_print_setting_plugin/utils/custom_popup_menu.dart';
import 'package:niimbot_print_setting_plugin/utils/image_slider_thumb.dart';
import 'package:niimbot_print_setting_plugin/utils/max_length_number_input.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';
import 'package:niimbot_print_setting_plugin/utils/tick_mark_thumb.dart';
import 'package:niimbot_print_setting_plugin/widget/select_link_copies_field_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';

class PrintSettingWidget extends StatefulWidget {
  final PrintSettingLogic logic;

  PrintSettingWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintSettingWidgetState createState() => _PrintSettingWidgetState(logic);
}

class _PrintSettingWidgetState extends State<PrintSettingWidget> {
  final PrintSettingLogic logic;
  late List _modelList;

  //份数模式
  late List _copiesSettingModeList;
  late String copiesSettingMode;
  var totalPage;
  var isShowSpeedAndQuality = false;
  FocusNode lessNode = FocusNode();
  FocusNode moreNode = FocusNode();
  FocusNode countNode = FocusNode();
  TextEditingController rangeLessController = TextEditingController();
  TextEditingController rangeMoreController = TextEditingController();
  TextEditingController printCountController = TextEditingController();

  _PrintSettingWidgetState(this.logic);

  @override
  void initState() {
    super.initState();
    countNode.unfocus();
    logic.isMultipleTemplates();
    _refreshPage();
    _checkPrintPriority();
    _initPrintCount();
    // logic.refreshPrintDividerStatus();
    logic.stream.listen((data) {
      if (data["action"] == "printerConnectState") {
        // logic.refreshPrintDividerStatus();
        Future.delayed(Duration(milliseconds: 1000), () {
          _refreshPage();
          _checkPrintPriority();
        });
      } else if (data["action"] == "refreshTemplate") {
        //    logic.isMultipleTemplates();
        _refreshPage();
        _checkPrintPriority();
      }
    });
    // 通过焦点变化来完成输入框内容处理
    lessNode.addListener(() {
      // 检查lessNode是否获得了焦点，如果没有，则执行以下代码
      if (!lessNode.hasFocus) {
        // 获取rangeLessController输入框中的值
        var value = rangeLessController.text;
        // 如果输入的值为空，则默认为'1'
        if (value.isEmpty) {
          value = '1';
        }
        // 将输入的字符串转换为整数
        int less = int.parse(value);
        // 如果转换后的值小于1，则将其设置为1
        if (less < 1) {
          less = 1;
        }
        var containsSerialElement =
            logic.parameter!.templateMoudle!['elements'].any((element) => element['type'] == 'serial');
        if (less > logic.printData.pageEnd) {
          if (containsSerialElement && less < 1000 && !logic.style.isDataSource) {
            logic.printData.pageEnd = less;
          } else {
            if (less <= logic.style.pageMax) {
              logic.printData.pageEnd = less;
            } else {
              less = logic.printData.pageEnd;
            }
          }
        }
        // 将转换后的值更新到rangeLessController输入框中
        rangeLessController.text = less.toString();
        // 更新打印数据中的pageBegin字段
        logic.printData.pageBegin = less;
        // 将打印数据中的pageEnd字段更新到rangeMoreController输入框中
        rangeMoreController.text = logic.printData.pageEnd.toString();
      }
      logic.update([UpdateFieldManager.setting, UpdateFieldManager.preview]);
    });
    moreNode.addListener(() {
      if (!moreNode.hasFocus) {
        var value = rangeMoreController.text;
        if (value.isEmpty) {
          value = '1';
        }
        int more = int.parse(value);
        if (more < 1) {
          more = 1;
        }
        var containsSerialElement =
            logic.parameter!.templateMoudle!['elements'].any((element) => element['type'] == 'serial');
        if (containsSerialElement && more < 1000 && !logic.style.isDataSource) {
          logic.style.pageMax = more;
          logic.update([UpdateFieldManager.preview]);
        }
        if (more > logic.style.pageMax) {
          more = logic.style.pageMax;
        }

        if (more < logic.printData.pageBegin) {
          logic.printData.pageBegin = more;
        }
        rangeMoreController.text = more.toString();
        logic.printData.pageEnd = more;
        rangeLessController.text = logic.printData.pageBegin.toString();

        if (logic.style.pageIndex + 1 > logic.style.pageMax) {
          logic.style.pageIndex = logic.style.pageMax - 1;
        }
      }
      logic.update([UpdateFieldManager.setting, UpdateFieldManager.printButton]);
    });
    countNode.addListener(() {
      if (!countNode.hasFocus) {
        var value = printCountController.text;
        if (value.isEmpty) {
          value = '1';
        }
        int count = int.parse(value);
        if (count <= 0) {
          count = 1;
        }
        printCountController.text = count.toString();
        logic.printData.printCount = count;

        if(logic.taskType != PrintTaskType.batch){
          SharedPreferences.getInstance().then((sp) {
            sp.setInt("PrintCount", logic.printData.printCount);
          });
        }

        //  logic.update([UpdateFieldManager.settingCount]);
      }
      logic.update([UpdateFieldManager.setting, UpdateFieldManager.printButton, UpdateFieldManager.previewCount]);
    });
  }

  _refreshPage() {
    _modelList = [logic.getI18nString("app100001171", "清晰度优先"), logic.getI18nString("app100001172", "速度优先")];
    _copiesSettingModeList = [logic.getI18nString("app100001937", "设置份数"), logic.getI18nString("app100001938", "关联份数")];
    // 判断是否为多份多页打印
    bool isMultipleCopiesMultiplePages = (logic.printData.pageEnd - logic.printData.pageBegin + 1) > 1;
    if (isMultipleCopiesMultiplePages) {
      _copiesSettingModeList[0] = logic.getI18nString("app100002006", "打印套数");
    }
    if (logic.printCopiesMode == PrintCopiesMode.settingCopies) {
      copiesSettingMode = _copiesSettingModeList[0]; //  设置份数/打印套数
    } else {
      copiesSettingMode = _copiesSettingModeList[1]; // 关联份数
    }
    rangeLessController.text = logic.printData.pageBegin.toString();
    rangeMoreController.text = logic.printData.pageEnd.toString();
  }

  @override
  void dispose() {
    super.dispose();
    countNode.dispose();
    moreNode.dispose();
    lessNode.dispose();
  }

  _initPrintCount() async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    var count = sp.getInt("PrintCount") ?? 1;
    //批量打印份数始终为1
    if(logic.taskType == PrintTaskType.batch){
      count = 1;
    }
    logic.printData.printCount = count;
    printCountController.text = logic.printData.printCount.toString();
  }

  _checkPrintPriority() async {
    // 获取硬件模型并设置打印密度
    var densityInfo =
        await logic.channel.getCurrentPrintDensityInfo(logic.parameter!.templateMoudle!["consumableType"].toString());
    logic.printData.printDensity = densityInfo["density"];
    logic.style.solubilitySetStart = double.parse(densityInfo["solubilitySetStart"].toString());
    logic.style.solubilitySetEnd = double.parse(densityInfo["solubilitySetEnd"].toString());
    int solubilityStart = logic.style.solubilitySetStart.toInt();
    int solubilityEnd = logic.style.solubilitySetEnd.toInt();
    if (logic.printData.printDensity < solubilityStart) {
      logic.printData.printDensity = solubilityStart;
    }
    if (logic.printData.printDensity > solubilityEnd) {
      logic.printData.printDensity = solubilityEnd;
    }
    debugPrint("打印浓度参数获取: ${logic.style.solubilitySetEnd}");

    // SharedPreferences sp = await SharedPreferences.getInstance();
    // var count = sp.getInt("PrintCount") ?? 1;
    // //批量打印份数始终为1
    // if(logic.taskType == PrintTaskType.batch){
    //   count = 1;
    // }
    // logic.printData.printCount = count;
    // printCountController.text = logic.printData.printCount.toString();
    if (NiimbotPrintSDK().store.connectedPrinter != null) {
      logic.printData.printPriority = NiimbotPrintSDK().store.connectedPrinter!.printQualityMode?.value ?? 1;

      logic.style.modelNameTitle = _modelList[logic.printData.printPriority];
      // 更新设置和打印按钮的状态
    }

    var qualitySupportLength = NiimbotPrintSDK().store.connectedPrinter?.printQualitySupport?.length ?? 1;
    if (NiimbotPrintSDK().store.connectedPrinter != null &&
        qualitySupportLength > 1 &&
        NiimbotPrintSDK().store.connectedPrinter!.printQualityMode != null) {
      logic.style.isShowPrintPriority = true;
    } else {
      logic.style.isShowPrintPriority = false;
      if (isShowSpeedAndQuality) {
        isShowSpeedAndQuality = false;
        Navigator.of(context).pop();
      }
    }
    logic.update([
      UpdateFieldManager.setting,
      UpdateFieldManager.printButton,
      UpdateFieldManager.previewCount,
      UpdateFieldManager.density
    ]);
  }

  KeyboardActionsConfig _buildConfigCount() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: countNode,
          onTapAction: () {},
        ),
      ],
      defaultDoneWidget: GestureDetector(
        onTap: () {
          lessNode.unfocus();
          moreNode.unfocus();
          countNode.unfocus();
        },
        child: Text(
          widget.logic.getI18nString('app01031', '完成'),
          style: const TextStyle(
            color: Color(0xFF262626),
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  KeyboardActionsConfig _buildConfigLess() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: lessNode,
          onTapAction: () {},
        ),
      ],
      defaultDoneWidget: GestureDetector(
        onTap: () {
          lessNode.unfocus();
          moreNode.unfocus();
          countNode.unfocus();
        },
        child: Text(
          widget.logic.getI18nString('app01031', '完成'),
          style: const TextStyle(
            color: Color(0xFF262626),
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  KeyboardActionsConfig _buildConfigMore() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: moreNode,
          onTapAction: () {},
        ),
      ],
      defaultDoneWidget: GestureDetector(
        onTap: () {
          lessNode.unfocus();
          moreNode.unfocus();
          countNode.unfocus();
        },
        child: Text(
          widget.logic.getI18nString('app01031', '完成'),
          style: const TextStyle(
            color: Color(0xFF262626),
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.setting,
        builder: (logic) {
          return logic.parameter?.templateMoudleNew != null
              ? Container(
                  child: Column(
                    children: [_speedAndQuality(), _settingItems()],
                  ),
                )
              : SizedBox();
        });
  }

  _speedAndQuality() {
    return Visibility(
      visible: logic.style.isShowPrintPriority,
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(12)),
        ),
        margin: EdgeInsetsDirectional.fromSTEB(16, 0, 12, 12),
        padding: EdgeInsetsDirectional.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Text(
              logic.style.printModelTitle,
              style: logic.style.printModelStyle,
            ),
            Expanded(child: Container()),
            CustomPopupMenuButton(
              padding: EdgeInsets.all(0),
              itemBuilder: (BuildContext context) {
                return [
                  _PopupMenuItem(_modelList[0], isFirst: true, isSelected: logic.style.modelNameTitle == _modelList[0]),
                  _PopupMenuItem(_modelList[1], isLast: true, isSelected: logic.style.modelNameTitle == _modelList[1]),
                ];
              },
              offset: Offset(Directionality.of(context) == TextDirection.ltr ? 0 : 10, 30),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 10,
              onSelected: (value) {
                isShowSpeedAndQuality = false;
                logic.style.modelNameTitle = value;
                logic.printData.printPriority = logic.style.modelNameTitle == _modelList[0] ? 0 : 1;
                logic.channel.trackEvent("click", "024_293_262",
                    eventData: {"b_name": logic.printData.printPriority == 0 ? "清晰度优先" : "速度优先"});
                logic.update([UpdateFieldManager.setting]);
              },
              onOpened: () {
                isShowSpeedAndQuality = true;
                logic.channel.trackEvent("click", "024_293");
              },
              onCanceled: () {
                isShowSpeedAndQuality = false;
              },
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      // color: ThemeColor.listBackground,
                    ),
                    constraints: const BoxConstraints(
                      maxWidth: 100.0, // 最大宽度为 300 像素
                    ),
                    child: Text(
                      logic.style.modelNameTitle,
                      style: logic.style.modelStyle,
                    ),
                  ),
                  const SizedBox(
                    width: 5,
                  ),
                  SvgIcon(
                    logic.style.modelSelectIcon,
                    matchTextDirection: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _buildPrintCopies() {
    bool copiesClickEnable = true;
    List<String> headers = [];
    if (logic.isExcelTemplate()) {
      copiesClickEnable = true;
      headers = logic.parameter?.templateMoudleNew?.dataSources?[0]?.rowData?[0] ?? [];
    } else {
      copiesClickEnable = false;
    }
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      child: Row(
        children: [
          CustomPopupMenuButton(
            enabled: copiesClickEnable,
            padding: const EdgeInsets.all(0),
            itemBuilder: (BuildContext context) {
              return [
                _PopupMenuItem(_copiesSettingModeList[0],
                    isFirst: true, isSelected: logic.printCopiesMode == PrintCopiesMode.settingCopies),
                _PopupMenuItem(_copiesSettingModeList[1],
                    isLast: true, isSelected: logic.printCopiesMode == PrintCopiesMode.linkCopies),
              ];
            },
            offset: Offset(Directionality.of(context) == TextDirection.ltr ? 0 : 10, 30),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 10,
            onSelected: (value) {
              copiesSettingMode = value;
              if (copiesSettingMode == _copiesSettingModeList[0]) {
                logic.printCopiesMode = PrintCopiesMode.settingCopies;
              } else {
                logic.printCopiesMode = PrintCopiesMode.linkCopies;
                logic.channel.trackEvent("click", "024_402");
              }
              logic.update([UpdateFieldManager.settingCount, UpdateFieldManager.previewCount]);
            },
            onOpened: () {},
            onCanceled: () {},
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    // color: ThemeColor.listBackground,
                  ),
                  constraints: const BoxConstraints(
                    maxWidth: 100.0, // 最大宽度为 300 像素
                  ),
                  child: Text(
                    copiesSettingMode,
                    style: logic.style.printModelStyle,
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
                Offstage(
                  offstage: !copiesClickEnable,
                  child: SvgIcon(
                    logic.style.modelSelectIcon,
                    matchTextDirection: true,
                  ),
                ),
              ],
            ),
          ),
          Expanded(child: Container()),
          Stack(children: [
            //关联份数模式
            Offstage(
              offstage: copiesSettingMode != _copiesSettingModeList[1],
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: (){
                  showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      isDismissible: false,
                      enableDrag: false,
                      barrierColor: const Color(0xFF000000).withOpacity(0.35),
                      builder: (BuildContext context) {
                        // 返回 PrintSlowPage 作为底部导航的内容
                        return SelectLinkCopiesFieldWidget(fields: headers,logic: logic,currentIndex: logic.currentCopiesLinkFieldIndex);
                      }
                  ).then((result){
                    if(result != null){
                      logic.currentCopiesLinkFieldIndex = result;
                      logic.update([UpdateFieldManager.settingCount, UpdateFieldManager.previewCount]);
                    }

                  });
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      color: Colors.transparent,
                      padding: const EdgeInsets.only(left: 20,top: 12,bottom: 12),
                      alignment: AlignmentDirectional.centerEnd,
                      child: Text(
                        logic.currentCopiesLinkFieldIndex == -1 ? logic.getI18nString("app100001939", "请选择"): headers[logic.currentCopiesLinkFieldIndex],
                        overflow: TextOverflow.ellipsis,
                        style: logic.currentCopiesLinkFieldIndex == -1 ?logic.style.printJustOneBtnStyle : logic.style.printModelStyle,
                      ),
                    ),
                    SvgIcon(
                      logic.style.deviceMsgIcon,
                      side: 20,
                      color: logic.currentCopiesLinkFieldIndex == -1 ?KColor.RED : KColor.BLACK,
                      matchTextDirection: true,
                    ),
                    const SizedBox(width: 2),
                  ],
                ),
              ),
            ),
            //设置份数模式
            Offstage(
              offstage: copiesSettingMode != _copiesSettingModeList[0],
              child: Row(children: [
                GestureDetector(
                  onTap: () {
                    if (logic.printData.printCount == 1) {
                      printCountController.text = 1.toString();
                    } else {
                      logic.printData.printCount -= 1;
                      printCountController.text = logic.printData.printCount.toString();
                    }
                    if(logic.taskType != PrintTaskType.batch){
                      SharedPreferences.getInstance().then((sp) {
                        sp.setInt("PrintCount", logic.printData.printCount);
                      });
                    }

                    logic.update([UpdateFieldManager.settingCount, UpdateFieldManager.previewCount]);
                  },
                  child: Container(
                    color: Colors.transparent,
                    padding: EdgeInsetsDirectional.fromSTEB(15, 5, 12, 5),
                    child: SvgIcon(
                      'assets/reduce.svg',
                      color: logic.printData.printCount == 1 ? Color(0xFFCCCCCC) : Color(0xFF2E2E2E),
                    ),
                  ),
                ),
                Container(
                  width: 50,
                  height: 32,
                  alignment: Alignment.center,
                  child: KeyboardActions(
                    autoScroll: false,
                    disableScroll: true,
                    config: _buildConfigCount(),
                    child: CupertinoTextField(
                      controller: printCountController,
                      focusNode: countNode,
                      maxLength: 3,
                      textInputAction: TextInputAction.done,
                      textAlign: TextAlign.center,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*$')),
                      ],
                      keyboardType: const TextInputType.numberWithOptions(signed: false, decimal: true),
                      style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                      decoration: BoxDecoration(
                        color: Color(0x14747480), // 设置背景色
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                    ),
                  ),
                ),
                Container(
                  child: GestureDetector(
                    onTap: () {
                      if (logic.printData.printCount < 999) {
                        logic.printData.printCount += 1;
                        printCountController.text = logic.printData.printCount.toString();
                        if(logic.taskType != PrintTaskType.batch){
                          SharedPreferences.getInstance().then((sp) {
                            sp.setInt("PrintCount", logic.printData.printCount);
                          });
                        }

                        logic.update([UpdateFieldManager.settingCount, UpdateFieldManager.previewCount]);
                      }
                    },
                    child: Container(
                      color: Colors.transparent,
                      padding: EdgeInsetsDirectional.fromSTEB(12, 5, 10, 5),
                      child: SvgIcon(
                        'assets/add.svg',
                        color: logic.printData.printCount == 999 ? Color(0xFFCCCCCC) : Color(0xFF2E2E2E),
                      ),
                    ),
                  ),
                ),
              ],),
            )
          ],)



        ],
      ),
    );
  }
  _settingItems() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      margin: EdgeInsetsDirectional.fromSTEB(16, 0, 16, 12),
      child: Column(
        children: [
          logic.style.isShowRange && logic.taskType != PrintTaskType.miniApp ? _printRange() : SizedBox(),
          _printCount(),
          _printDivider(),
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(16, 0, 0, 0),
            child: Divider(
              height: 1,
              thickness: 0.5,
              color: Color(0xFFEBEBEB),
            ),
          ),
          _printDensity()
        ],
      ),
    );
  }

  _printRange() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 0),
          child: Row(
            children: [
              Text(logic.getI18nString("app100001741", "打印范围"), style: logic.style.printModelStyle),
              Expanded(child: Container()),
              Container(
                width: 50,
                height: 32,
                alignment: Alignment.center,
                child: KeyboardActions(
                  autoScroll: false,
                  disableScroll: true,
                  config: _buildConfigLess(),
                  child: CupertinoTextField(
                    controller: rangeLessController,
                    focusNode: lessNode,
                    textInputAction: TextInputAction.done,
                    textAlign: TextAlign.center,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*$')), MaxLengthNumberFormatter(8)],
                    keyboardType: const TextInputType.numberWithOptions(signed: false, decimal: true),
                    style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                    decoration: BoxDecoration(
                      color: Color(0x14747480), // 设置背景色
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsetsDirectional.symmetric(horizontal: 6),
                child: SvgIcon(
                  'assets/line.svg',
                ),
              ),
              Container(
                width: 50,
                height: 32,
                alignment: Alignment.center,
                child: KeyboardActions(
                  autoScroll: false,
                  disableScroll: true,
                  config: _buildConfigMore(),
                  child: CupertinoTextField(
                    controller: rangeMoreController,
                    focusNode: moreNode,
                    textInputAction: TextInputAction.done,
                    textAlign: TextAlign.center,
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*$')), MaxLengthNumberFormatter(8)],
                    keyboardType: const TextInputType.numberWithOptions(signed: false, decimal: true),
                    style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                    decoration: BoxDecoration(
                      color: Color(0x14747480), // 设置背景色
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(16, 12, 0, 0),
          child: Divider(
            height: 1,
            thickness: 0.5,
            color: Color(0xFFEBEBEB),
          ),
        )
      ],
    );
  }

  _printCount() {
    if (printCountController.text.isEmpty) {
      printCountController.text = "1";
    }
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.settingCount,
        builder: (logic) {
          return _buildPrintCopies();
        });
  }

  Widget _printDivider() {
    return GetBuilder<PrintSettingLogic>(
      id: UpdateFieldManager.divider,
      builder: (logic) {
        return logic.supportPrintDividerSet ? Padding(
          padding: const EdgeInsetsDirectional.only(start: 16.0),
          child: Column(
            children: [
              Divider(
                height: 1,
                thickness: 0.5,
                color: Color(0xFFEBEBEB),
              ),
              Container(
                height: 44,
                padding: const EdgeInsetsDirectional.only(end: 16.0),
                child: Row(
                  children: [
                    Text(logic.style.printDividerTitle),
                    Spacer(),
                    CupertinoSwitch(
                        value: logic.printDividerSetStatus,
                        activeColor: logic.style.printDividerSwitchColor,
                        onChanged: (bool value) async {
                          logic.savePrintDividerStatus(value);
                        })
                  ],
                ),
              ),
            ],
          ),
        ) : const SizedBox.shrink();
      }
    );
  }

  _printDensity() {
    debugPrint("打印浓度参数: ${logic.style.solubilitySetEnd}");
    return GetBuilder<PrintSettingLogic>(
        id: UpdateFieldManager.density,
        builder: (logic) {
          return logic.printData.printDensity == 0
              ? SizedBox()
              : Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(16, 12, 16, 0),
                        child: Row(
                          children: [
                            Text(logic.style.printDensityTitle, style: logic.style.printModelStyle),
                          ],
                        ),
                      ),
                      logic.parameter!.templateMoudle == null
                          ? SizedBox()
                          : Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    child: SliderTheme(
                                        data: SliderThemeData(
                                            inactiveTrackColor: logic.style.sliderBgColor,
                                            trackHeight: 5,
                                            tickMarkShape: logic.style.solubilitySetEnd > 9 ? null : TickMarkThumb(),
                                            thumbShape: ImageSliderThumb(size: const Size(20, 20))),
                                        child: Slider(
                                            value: (logic.printData.printDensity.toDouble() - 1),
                                            min: logic.style.solubilitySetStart - 1,
                                            max: logic.style.solubilitySetEnd - 1,
                                            divisions: logic.style.solubilitySetEnd.toInt() - 1,
                                            thumbColor: logic.style.sliderThemeColor,
                                            activeColor: logic.style.sliderActiveColor,
                                            onChanged: (value) {
                                              logic.printData.printDensity = double.parse(value.toString()).toInt() + 1;
                                              logic.channel.setPrintDensity(logic.printData.printDensity.toInt());
                                              logic.update([UpdateFieldManager.density]);
                                            })),
                                  ),
                                ),
                                Container(
                                    width: 30,
                                    padding: EdgeInsetsDirectional.only(end: 12),
                                    child: Text(
                                      '${(logic.printData.printDensity.toInt()).toString()}',
                                      maxLines: 2,
                                      textAlign: TextAlign.start,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          color: logic.style.sliderTitle, fontSize: 14, fontWeight: FontWeight.w400),
                                    ))
                              ],
                            ),
                    ]);
        });
  }

  CustomPopupMenuItem _PopupMenuItem(String name,
      {bool isFirst = false, bool isLast = false, bool isSelected = false}) {
    return CustomPopupMenuItem(
      value: name,
      height: 5,
      padding: EdgeInsets.zero,
      child: Container(
        padding: EdgeInsetsDirectional.symmetric(vertical: 0, horizontal: 0),
        child: Column(
          children: [
            isFirst
                ? Container()
                : Container(
                    width: double.infinity,
                    child: Divider(
                      height: 1,
                      thickness: 0.5,
                      color: Color(0xFFEBEBEB),
                    ),
                  ),
            Container(
                padding: EdgeInsetsDirectional.fromSTEB(
                    10,
                    isFirst ? 4 : 10,
                    5,
                    isFirst
                        ? 13
                        : isLast
                            ? 4
                            : 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        isSelected
                            ? SvgIcon(
                                'assets/icon_select.svg',
                                fit: BoxFit.fill,
                                color: Color(0xFF000000),
                                width: 22,
                                height: 21,
                              )
                            : Container(
                                width: 22,
                              ),
                        SizedBox(
                          width: 5,
                        ),
                        Container(
                          width: 120,
                          child: Text(
                            name,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                            style: TextStyle(
                              color: Color(0xFF161616),
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                )),
          ],
        ),
      ),
    );
  }
}
