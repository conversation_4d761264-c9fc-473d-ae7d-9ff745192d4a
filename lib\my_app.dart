import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_canvas_plugins_interface/config/canvas_theme_data.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_file_info.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:lifecycle/lifecycle.dart';
import 'package:niim_login/login_plugin/login_plugin_api.dart';
import 'package:niim_login/login_plugin/pages/login_home_page.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/pages/change_content_page_wrapper.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/print_channel.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_row_page_wrapper.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:show_fps/show_fps.dart';
import 'package:text/business/print/print_count_business.dart';
import 'package:text/login_module_container.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/C1/view/c1_home_page.dart';
import 'package:text/pages/cable/cable_canvas_page.dart';
import 'package:text/pages/canvas/impl/advance_qr_code_impl.dart';
import 'package:text/pages/canvas/impl/canvas_industry_template_impl.dart';
import 'package:text/pages/canvas/impl/excel_import_impl.dart';
import 'package:text/pages/canvas/impl/font_panel_impl.dart';
import 'package:text/pages/canvas/impl/goods_import_impl.dart';
import 'package:text/pages/canvas/impl/label_import_impl.dart';
import 'package:text/pages/canvas/impl/loading_toast_impl.dart';
import 'package:text/pages/canvas/impl/web_page_impl.dart';
import 'package:text/pages/create/view/create_label_home_page.dart';
import 'package:text/pages/etag/goods_select/choose_goods_middle_page.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_to_canvas_transition_page.dart';
import 'package:text/pages/industry_template/search_template/feedback_page_refactored.dart';
import 'package:text/pages/industry_template/select_label/label_details_page.dart';
import 'package:text/pages/industry_template/select_label/label_web_page.dart';
import 'package:text/pages/meProfile/me_devices_page.dart';
import 'package:text/pages/meProfile/me_profile_page.dart';
import 'package:text/pages/meProfile/nps_alert/npsPage.dart';
import 'package:text/pages/message/message_list_page.dart';
import 'package:text/pages/message/model/message_type.dart';
import 'package:text/pages/message/sub_message_list_page.dart';
import 'package:text/pages/my_template/my_template_page.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/share_member_manager_page.dart';
import 'package:text/pages/network_detection/network_detection_page.dart';
import 'package:text/pages/print_history/print_history_widget.dart';
import 'package:text/pages/scan/scan_code_login_page.dart';
import 'package:text/pages/setting/pasteboard_setting.dart';
import 'package:text/pages/user/delete_account/logoff_step_tow_social_page.dart';
import 'package:text/pages/user/delete_account/logoff_step_two_page.dart';
import 'package:text/pages/user/email_binding_page.dart';
import 'package:text/pages/user/my_account_page.dart';
import 'package:text/pages/user/phone_binding_page.dart';
import 'package:text/pages/user/vip_nps_grade_page.dart';
import 'package:text/pages/user_guide/guide_empty_page.dart';
import 'package:text/pages/user_guide/select_print_page.dart';
import 'package:text/pages/user_guide/welcome_page.dart';
import 'package:text/print/print_history_manager.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/print_setting_channel.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/vipTrial/vip_trial_manager.dart';
import 'package:text/vipTrial/vip_trial_transpratent_page.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'application.dart';
import 'business/print/print_log_business.dart';
import 'cache/cache_helper.dart';
import 'log_utils.dart';
import 'migration/migration_manager.dart';
import 'network/dio_utils.dart';
import 'pages/canvas/impl/user_center/canvas_user_impl.dart';
import 'pages/canvas/niimbot_canvas_page.dart';
import 'pages/etag/home/<USER>/tag_home_view.dart';
import 'pages/industry_template/home/<USER>/industry_template_page.dart';
import 'pages/industry_template/search_template/search_template_page.dart';
import 'pages/industry_template/select_label/label_category_list_model.dart';
import 'pages/message/provider/message_service.dart';
import 'pages/my_template/page/personal_template/search_personal_template_page.dart';
import 'tools/bottom_sheet_tools.dart';
import 'tools/to_Native_Method_Channel.dart';
import 'widget/web_page.dart';

GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  MyAppState createState() => MyAppState();
}

class MyAppState extends State with WidgetsBindingObserver {
  String token = "";

  late Map<String, FlutterBoostRouteFactory> routerMap = {
    ///标签纸详情页路由页面
    'labelDetailPage': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            Map arguments = settings?.arguments as Map;
            int source = 1;
            if (arguments['isFromScan'] is int) {
              source = arguments['isFromScan'] == 1 ? 3 : 1;
            } else if (arguments['isFromScan'] is bool) {
              source = arguments['isFromScan'] ? 3 : 1;
            }
            String jsonData = arguments['jsonData'] ?? "";
            String labelId = arguments['labelId'] ?? "";
            String shopSource = arguments['shopSource'] ?? "";
            Map<String, dynamic> templateData = jsonDecode(jsonData);
            Item item = Item.fromJson(templateData);
            // return LabelDetailContainerPage(item);
            return LabelDetailsPage(
              item,
              isFullScreen: true,
              shopSource: shopSource,
              source: source,
            );
          });
    },
    'selectDeviceSeries': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            Map arguments = settings?.arguments as Map;
            bool isFromAppStart = arguments["isFromAppStart"] == "1";
            return SelectPrinterPage(isFromAppStart: (isFromAppStart));
          });
    },
    'pasteboardSetting': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return const PasteboardSettingPage();
          });
    },
    'printSetting': (RouteSettings? settings, String? uniqueId) {
      ToNativeMethodChannel().setFlutterVCCanSideslip(true);
      Map<String, dynamic> arguments = settings?.arguments as Map<String, dynamic>;
      return PageRouteBuilder(
          opaque: true,
          settings: settings,
          allowSnapshotting: false,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return PrintSettingManager(context, arguments, (String stringCode, String defaultStr,
                {List<String>? param}) {
              return intlanguage(stringCode, defaultStr, param: param);
            }).setPageType(PrintPageType.high).setIsNavigator(false).show(context, PrintSettingChannel());
          });
    },
    'printSettingDialog': (RouteSettings? settings, String? uniqueId) {
      Map<String, dynamic> arguments = settings?.arguments as Map<String, dynamic>;
      return PageRouteBuilder(
          opaque: true,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return PrintSettingManager(context, arguments, (String stringCode, String defaultStr,
                {List<String>? param}) {
              return intlanguage(stringCode, defaultStr, param: param);
            }).setPageType(PrintPageType.low).setIsNavigator(false).show(context, PrintSettingChannel());
          });
    },
    'C1PrintPage': (RouteSettings? settings, String? uniqueId) {
      Map<String, dynamic> arguments = {};
      try {
        arguments = settings?.arguments as Map<String, dynamic>? ?? {};
      } catch (_, __) {}
      return PageRouteBuilder(
          opaque: false,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return PrintSettingManager(context, arguments, (String stringCode, String defaultStr,
                {List<String>? param}) {
              return intlanguage(stringCode, defaultStr, param: param);
            }).setPageType(PrintPageType.none).setIsNavigator(false).show(context, PrintSettingChannel());
          });
    },
    'printNullUiShowProgress': (RouteSettings? settings, String? uniqueId) {
      Map<String, dynamic> arguments = settings?.arguments as Map<String, dynamic>;
      return PageRouteBuilder(
          opaque: true,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return PrintSettingManager(context, arguments, (String stringCode, String defaultStr,
                {List<String>? param}) {
              return intlanguage(stringCode, defaultStr, param: param);
            }).setPageType(PrintPageType.none).setIsNavigator(false).show(context, PrintSettingChannel());
          });
    },
    'welcome': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return WelcomePage();
          });
    },
    'eTag': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      Application.setAuthorization(token);
      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return TagHomePage();
          });
    },
    'myTemplate': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      int fromShareCode = arguments["fromShareCode"] as int? ?? 0;
      Application.saveToken(token);
      Application.setAuthorization(token);
      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return MyTemplatePage(
              fromShareCode: fromShareCode,
            );
          });
    },
    'meProfile': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return MeProfilePage();
          });
    },
    'login': (RouteSettings? settings, String? uniqueId) {
      GraphQLUtils.sharedInstance().configCustomHeaders({"languageCode": Application.currentAppLanguageType});
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LoginModuleContainer(
              child: LoginHomePage(
                needGetNiimbotAccount: true,
                isNavigatorManager: true,
              ),
              isLoginPage: true,
            );
          });
    },
    'bind': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      bool isMain = arguments["isMain"] == "1";
      bool showBindSwitch = arguments["showBindSwitch"] == "1";
      Log.d("token: $token 是否中国大陆:$isMain 是否展示切换:$showBindSwitch");
      Application.saveToken(token);
      if (token.isNotEmpty) {
        GraphQLUtils.sharedInstance().setAuthorization(token);
      }

      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LoginModuleContainer(
              child: isMain
                  ? PhoneBindingPage(fromNative: true, showBindSwitch: showBindSwitch)
                  : EmailBindingPage("", fromNative: true, showBindSwitch: showBindSwitch),
            );
          });
    },
    'userCenter': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      Application.setAuthorization(token);

      if (token.isNotEmpty) {
        GraphQLUtils.sharedInstance().setAuthorization(token);
      }

      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LoginModuleContainer(child: MyAccountPage(token));
          });
    },
    'printHistory': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      GraphQLUtils.sharedInstance().setAuthorization(token);
      // 同步历史记录
      PrintHistoryManager().syncHistoryToServer();
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return const PrintHistoryWidget();
          });
    },
    'templateFeedback': (RouteSettings? settings, String? uniqueId) {
      Map? arguments = settings?.arguments as Map?;
      String source = "";
      if (arguments != null && arguments.isNotEmpty) {
        source = arguments["source"];
      }
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            // return const FeedbackPage();
            return FeedbackPageRefactored(
              source: source,
            );
          });
    },
    'industryTemplate': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      Application.setAuthorization(token);

      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            Map<String, dynamic> map = settings?.arguments as Map<String, dynamic>;

            return IndustryTemplatePage();
          });

      // GraphQLUtils.sharedInstance().setAuthorization(token);
      // return MaterialPageRoute(
      //     settings: settings,
      //     builder: (_) {
      //       return const PrintHistoryWidget();
      //     });
    },
    'industryToCanvasTransitionPage': (RouteSettings? settings, String? uniqueId) {
      return PageRouteBuilder(
          opaque: false,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return FadeTransition(
              opacity: animation,
              child: const IndustryToCanvasTransitionPage(),
            );
          });
    },
    'searchIndustryTemplate': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return SearchTemplatePage();
          });
    },
    'toC1HelpCenterEvent': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            // 防止编码问题
            var url = Uri.encodeFull(Application.c1HeplcenterBaseUrl);
            return WebPage(
              url: url,
            );
          });
    },
    'searchMyTemplate': (RouteSettings? settings, String? uniqueId) {
      var arguments = settings?.arguments;
      TemplateOperateState operateType = TemplateOperateState.normal;
      if (arguments != null && arguments is Map && arguments.isNotEmpty && arguments["operateType"] != null) {
        TemplateOperateState? state = TemplateOperateState.values.firstWhere(
          (e) => e.toString() == arguments["operateType"],
        );
        operateType = state;
      }
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return SearchPersonalTemplatePage(operateType: operateType);
          });
    },
    'labelWeb': (RouteSettings? settings, String? uniqueId) {
      Map? arguments = settings?.arguments as Map?;
      String title = '';
      String url = '';
      bool isFullScreen = false;
      if (arguments != null && arguments.isNotEmpty) {
        title = arguments["title"];
        url = arguments["url"];
        isFullScreen = arguments["isFullScreen"];
      }
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LabelWebPage(title: title, url: url, isFullScreen: isFullScreen);
          });
    },
    'shareMemberManager': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return ShareMemberManagerPage();
          });
    },
    'meDevicesPage': (RouteSettings? settings, String? uniqueId) {
      return PageRouteBuilder(
          opaque: false,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return FadeTransition(
              opacity: animation,
              child: MeDevicesPage(),
            );
          });
    },
    'createLabelHome': (RouteSettings? settings, String? uniqueId) {
      return PageRouteBuilder(
          opaque: false,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return FadeTransition(
              opacity: animation,
              child: const CreateLabelHome(),
            );
          });
    },
    'vipNPSGrade': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      GraphQLUtils.sharedInstance().setAuthorization(token);
      return PageRouteBuilder(
          opaque: false,
          settings: settings,
          pageBuilder: (BuildContext context, Animation<double> animation, Animation<double> secondaryAnimation) {
            return FadeTransition(
              opacity: animation,
              child: const VIPNPSGradePage(),
            );
          });
    },
    'canvas': (RouteSettings? settings, String? uniqueId) {
      Log.d("新画板进入");
      Map arguments = (settings?.arguments as Map) ?? {};
      String jsonData = arguments['jsonData'] ?? "";
      String fontCategories = arguments["fontCategories"] ?? "";
      String fontPath = arguments["fontPath"] ?? "";
      //
      String fontDefaultFile = arguments["fontDefaultFile"] ?? "";
      // 2023/12/20 Ice_Liu 因为需求修改为所有情况都替换识别的rfid，所以此字段作为是否新建的模板使用
      int isNewTemplate = arguments["needRfidTemplate"] ?? 0;
      //所有情况都要替换rfid识别到的标签模板
      int needRfidTemplate = 1;
      bool needDownloadFonts = arguments["need_download_fonts"] ?? true;
      Locale locale = Locale(arguments['local'] ?? 'zh') ?? const Locale('zh');
      // 语言参数，目前只作为素材缓存路径拼接使用,保持原生端一致性，Android可不传
      String language = arguments['arguments'] ?? 'zh-cn';
      // 画板入口来源
      String source = arguments['source'] ?? '';
      String defaultSelect = arguments['defaultSelect'] ?? '';
      String printChannelCode = arguments['printChannelCode'] ?? '';
      // 文件导入信息
      CanvasFileInfo? canvasFileInfo;
      if (arguments['fileInfo'] != null) {
        Map fileInfoMap = arguments['fileInfo'];
        canvasFileInfo = CanvasFileInfo.fromMap(Map<String, dynamic>.from(fileInfoMap));
      }
      if (printChannelCode.isNotEmpty) {
        PrintChannelCode? printChannelCodeObj = PrintChannelCodeExtension.fromFeatureCode(printChannelCode);
        CanvasHelper.printChannelCode = printChannelCodeObj;
      }
      if (kDebugMode) {
        // Log.d(arguments.toString());
      }
      // 禁止手势滑动，既有业务处理，和FlutterBoost的enablePopGesture可能冲突，但是setFlutterVCCanSideslip优先级更高
      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return NiimbotCanvasPage(
              uniqueId: uniqueId,
              jsonData: jsonData,
              fontCategories: fontCategories,
              fontPath: fontPath,
              fontDefaultFile: fontDefaultFile,
              needRfidTemplate: needRfidTemplate,
              needDownloadFonts: needDownloadFonts,
              locale: Application.getFlutterLocale(),
              language: language,
              source: source,
              isNewTemplate: isNewTemplate,
              defaultSelectType: defaultSelect,
              canvasFileInfo: canvasFileInfo,
            );
          });
    },
    'cableCanvas': (RouteSettings? settings, String? uniqueId) {
      Log.d("新画板进入");
      Map arguments;
      if (settings?.arguments == null) {
        arguments = {};
      } else {
        arguments = (settings?.arguments as Map) ?? {};
      }
      String jsonData = arguments['jsonData'] ?? "";
      if (kDebugMode) {
        // Log.d(arguments.toString());
      }
      String fontPath =
          arguments["fontPath"] ?? '/data/user/0/com.gengcon.android.jccloudprinter/files/font_manager/custom_and_vip';
      // 禁止手势滑动，既有业务处理，和FlutterBoost的enablePopGesture可能冲突，但是setFlutterVCCanSideslip优先级更高
      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return CableCanvasPage(
              templateJsonData: jsonData,
              fontPath: fontPath,
            );
          });
    },
    'socialAccountLogout': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      GraphQLUtils.sharedInstance().setAuthorization(token);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LogoffStepTwoSocialPage();
          });
    },
    'mainAccountLogout': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String token = arguments["token"] as String? ?? '';
      Application.saveToken(token);
      GraphQLUtils.sharedInstance().setAuthorization(token);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return LogoffStepTwoPage();
          });
    },
    'nspAlert': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return NpsPage(data: arguments);
          });
    },
    'vipTrial': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String vipTrialCode = arguments["vipTrialCode"] ?? "";
      String vipType = arguments["vipType"] ?? "";
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return VipTrialTranspratentPage(
              vipTrialPrivilegeCode: vipTrialCode,
              vipType: vipType,
            );
          });
    },
    'scanCodeLogin': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String scanCode = arguments["scanCode"];
      String client = arguments["client"];
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return ScanCodeLoginPage(scanCode: scanCode, client: client);
          });
    },
    'C1': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String fontPath = arguments["fontPath"] ?? "";
      String shareCode = arguments["shareCode"] ?? "";
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return C1HomePage(
              fontPath: fontPath,
              shareCode: shareCode,
            );
          });
    },
    'showAppInfo': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return NetworkDetectorPage();
          });
    },
    'goodsChoosePage': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String data = arguments["json"];
      //TemplateUtils.transformTemplateJsonData(data);
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return TransparentBottomSheetPage(ChooseGoodsMiddlePage(data));
          });
    },
    'changeExcelRowPage': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String data = arguments["json"];
      bool showRfid = arguments["showRfid"] ?? false;
      bool isFolderShare = arguments["isFolderShare"] ?? false;
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return TransparentBottomSheetPage(ExcelRowPageWrapper(
              nativeTemplateJson: data,
              showRfid: showRfid,
              isFolderShare: isFolderShare,
            ));
          });
    },
    'changeContentPage': (RouteSettings? settings, String? uniqueId) {
      Map arguments = settings?.arguments as Map;
      String data = arguments["json"];
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return TransparentBottomSheetPage(ChangeContentPageWrapper(
              nativeTemplateJson: data,
            ));
          });
    },
    'messageCenter': (RouteSettings? settings, String? uniqueId) {
      // Map? arguments = settings?.arguments as Map?;
      // int unreadCount = 0;
      // if(arguments != null){
      //   unreadCount = arguments["unreadCount"];
      // }
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return MessageListPage();
          });
    },
    'subMessageListPage': (RouteSettings? settings, String? uniqueId) {
      Map? arguments = settings?.arguments as Map?;
      String category = MessageType.system;
      int isPush = 0;
      if (arguments != null) {
        category = arguments["category"];
        isPush = arguments["isPush"];
      }
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return SubMessageListPage(category: category, isPush: isPush);
          });
    },
    'guideEmptyPage': (RouteSettings? settings, String? uniqueId) {
      return MaterialPageRoute(
          settings: settings,
          builder: (_) {
            return GuideEmptyPage();
          });
    },
  };

  Route<dynamic>? routeFactory(RouteSettings settings, String? uniqueId) {
    FlutterBoostRouteFactory? func = routerMap[settings.name];
    Log.d('routeFactory uniqueId: $uniqueId');
    Application.boostTopContainerId = uniqueId;
    return func?.call(settings, uniqueId);
  }

  @override
  void initState() {
    Log.init();
    debugPrint('flutter页面初始化');
    super.initState();
    LoginPluginApi.enterLoginHomePage = () {
      Application.isInLoginPage = true;
    };
    LoginPluginApi.leaveLoginHomePage = () {
      Application.isInLoginPage = false;
    };
    DioUtils.initContext(context);
    Application.initRouter();
    RfidManager.instance.buildRiskCheck(RiskRequestCode.APPSTART.value);
    PrintLogBusiness().uploadPrintDataLog();
    // 因为Application是static类型，GetX、Provider无法生效，其次尽量不破坏内部property结构，stream消耗相比直接callBack更大，
    // 目前直接使用CallBack返回，可能架构设计不太优雅
    Application.fresh = () {
      setState(() {});
    };
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getDeviceSeriesList();
      CanvasPluginManager().excelImportImpl = ExcelImportImpl();
      CanvasPluginManager().goodsImportImpl = GoodsImportImpl();
      CanvasPluginManager().advanceQRCodeImpl = AdvanceQRCodeInterImpl();
      CanvasPluginManager().loadingToastImpl = LoadingToastImpl();
      CanvasPluginManager().fontPanelImpl = FontPanelImpl();
      CanvasPluginManager().webPageImpl = WebPageImpl();
      CanvasPluginManager().canvasIndustryTemplateInterface = CanvasIndustryTemplateImpl();
      CanvasPluginManager().labelImpl = LabelImportImpl();
      // 此处提前初始化，导致CanvasUserImpl提前初始化，还未进入画板导致未监听后续登录退出变化，
      // 所以在NiimbotCanvasPage的initState中重新赋予新的userModel
      // 后续排查后可去掉
      CanvasUserCenter(creator: () => CanvasUserImpl());
      Log.d("CanvasUserCenter user id ${CanvasUserCenter().userId}");
      Application.refreshHttpAgent();
      CacheHelper.getInstance().cacheActionWhenStartApp(context);

      // 刷新VIP试用管理，登录状态与未登录状态返回的试用权益不一样，不同用户返回的试用权益也不一样，所以要等到加载缓存中的用户token后再调用
      VipTrialManager().getAllTrialActivities();
      if (Application.isLogin) {
        MessageService().fetchMessageUnreadCount().then((unreadCount) {
          ToNativeMethodChannel().notifyUnreadMessageCount(unreadCount);
        });
        //获取用户打印张数
        PrintCountBusiness().requestUserPrintCount();
      }
      NiimbotLogTool.writeLogToFile({"MigrationManager": '开始检测数据迁移'});
      MigrationManager().setupDataMigration().then((_) {
        if (Application.isLogin) {
          TemplateManager().localFileToService();
        }
      });
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed: //从后台切换前台，界面可见
        break;
      case AppLifecycleState.paused: // 界面不可见，后台
        break;
      case AppLifecycleState.detached: // APP结束时调用
        break;
      case AppLifecycleState.hidden: // 用于windows的多窗口模式，移动端也能收到回调
        break;
    }
  }

  _onError(Object err) {
    Log.e('Native error:$err');
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Widget appBuilder(Widget home) {
    return RefreshConfiguration(
      headerBuilder: _customHeader,
      footerBuilder: _customFooter,
      enableScrollWhenTwoLevel: false,
      hideFooterWhenNotFull: true,
      headerTriggerDistance: 20,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          if (!Application.isEnableGesture) return;
          FocusScopeNode currentFocus = FocusScope.of(context);
          if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
            FocusManager.instance.primaryFocus?.unfocus();
          }
        },
        child: CanvasTheme(
          themeData: CanvasThemeData.canvasThemeDataDefault(),
          language: Application.currentAppLanguageType,
          child: MaterialApp(
            navigatorKey: GlobalKey(debugLabel: 'main-MaterialApp'),
            // navigatorKey: navigatorKey,
            navigatorObservers: [LifecycleObserver()],
            home: ShowFPS(
                alignment: Alignment.topRight,
                visible: false,
                showChart: false,
                borderRadius: BorderRadius.all(Radius.circular(11)),
                child: home),
            // 必须加上builder参数，否则showDialog等会出问题
            builder: EasyLoading.init(builder: (BuildContext context, Widget? child) {
              return MediaQuery(
                  data: MediaQuery.of(context).copyWith(boldText: false, textScaler: TextScaler.linear(1.0)),
                  child: child ?? Spacer());
            }),
            debugShowCheckedModeBanner: false,
            theme: ThemeData(
              useMaterial3: false,
              canvasColor: Colors.transparent,
              // fontFamily: Platform.isIOS ? 'PingFangSC-Regular,PingFang SC' : "Roboto"
              /*fontFamily: Platform.isIOS ? 'PingFang SC' : "Roboto"*/
            ),
            locale: Application.getFlutterLocale(),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate
            ],
            supportedLocales: const [
              Locale('en', ""),
              Locale.fromSubtags(languageCode: "zh", scriptCode: "Hans"),
              Locale.fromSubtags(languageCode: "zh", countryCode: "HK"),
              Locale.fromSubtags(languageCode: "zh", countryCode: "TW"),
              Locale('ja', "JP"),
              Locale('fr', "FR"),
              Locale('es', "ES"),
              Locale('de', "DE"),
              Locale('ru', "RU"),
              Locale('it', "IT"),
              Locale('ko', "KR"),
              Locale('th', "TH"),
              Locale('ar', "SA"),
              Locale('pl', "PL"),
              Locale('id', "ID"),
              Locale('pt', "PT"),
              Locale('nl', "NL"),
              Locale('cs', "CZ"),
              Locale('tr', "TR"),
              Locale('hi', "IN"),
            ],
            title: intlanguage('app00520', '精臣云打印'),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Application.allContext = context;
    return FlutterBoostApp(
      routeFactory,
      appBuilder: appBuilder,
    );
  }
}

extension SeriseList on MyAppState {
  /// 获取机型系列列表
  getDeviceSeriesList({Function? success, Function? error}) {
    DioUtils.instance.requestNetwork<Map>(Method.post, HttpApi.deviceSeriesList,
        queryParameters: null, isList: true, needLogin: false, onSuccessList: (List<Map> data) {
      ToNativeMethodChannel.setNativeDeviceSeriesList(data);
      if (success != null) {
        success(data);
      }
    }, onError: (code, message) {
      if (error != null) {
        error(code, message);
      }
    });
  }
}

extension SmartRefresher on MyAppState {
  Widget _customHeader() {
    return DefaultTextStyle(
      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
      child: CustomHeader(
        completeDuration: Duration.zero,
        builder: (BuildContext context, RefreshStatus? mode) {
          switch (mode) {
            case RefreshStatus.idle:
            case RefreshStatus.canRefresh:
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SvgIcon(
                        'assets/images/industry_template/home/<USER>',
                        width: 20,
                        height: 20,
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Container(
                          constraints: BoxConstraints(
                            maxWidth: 150,
                          ),
                          child: Text(intlanguage('app100000656', '下拉即可刷新...'))),
                    ],
                  ),
                  const SizedBox(
                    height: 17,
                  ),
                ],
              );
            case RefreshStatus.refreshing:
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CupertinoActivityIndicator(
                        radius: 10,
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Text(intlanguage('app100000657', '刷新中...')),
                    ],
                  ),
                  const SizedBox(
                    height: 17,
                  ),
                ],
              );
            default:
              return const SizedBox.shrink();
          }
        },
      ),
    );
  }

  Widget _customFooter() {
    return DefaultTextStyle(
      style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
      child: CustomFooter(
        height: 80,
        builder: (BuildContext context, LoadStatus? mode) {
          switch (mode) {
            case LoadStatus.idle:
            case LoadStatus.canLoading:
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SvgIcon(
                        'assets/images/industry_template/home/<USER>',
                        width: 20,
                        height: 20,
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Container(
                        width: 100,
                        child: Text(
                          intlanguage('app100000674', '向上滑动即可刷新...'),
                          maxLines: 2,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 17,
                  ),
                ],
              );
            case LoadStatus.loading:
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const CupertinoActivityIndicator(
                        radius: 10,
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Text(intlanguage('app100000657', '刷新中...')),
                    ],
                  ),
                  const SizedBox(
                    height: 17,
                  ),
                ],
              );
            case LoadStatus.noMore:
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 20.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(width: 100, child: Text(intlanguage('app100000672', '没有更多内容了...'))),
                      ],
                    ),
                  ],
                ),
              );
            default:
              return const SizedBox.shrink();
          }
        },
      ),
    );
  }
}
