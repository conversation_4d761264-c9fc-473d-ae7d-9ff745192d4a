//
//  JCLogUploadManager.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/11/17.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCLogUploadManager.h"
#import "JCBluetoothManager+Connect.h"
#import "JCPrintHistoryHelper.h"
#import "JCSSLTokenCacheHelper.h"
#import "JCAppMethodChannel.h"
@interface JCLogUploadManager()

@property(nonatomic,copy) XYBlock1 printContentUploadBlock;
@property(nonatomic,copy) NSString *logToken;
@property(nonatomic,copy) NSString *printContentToken;
@property(nonatomic, strong)dispatch_queue_t uploadQueue;
@end

@implementation JCLogUploadManager
static JCLogUploadManager *selfClass =nil;
+ (instancetype)sharedInstance {
    static JCLogUploadManager *_logInstabce = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _logInstabce = [[JCLogUploadManager alloc] init];
    });
    return _logInstabce;
}

- (instancetype)init {
    if (self = [super init]) {
        selfClass = self;
        self.uploadQueue = dispatch_queue_create("jc_upload_log_serial_queue", DISPATCH_QUEUE_SERIAL);
    }
    return self;
}

- (void)uploadLogInfoInFlutterWithParms:(NSString *)content logType:(NSString *)logType logUploadBlock:(XYBlock1)uploadBlock requestMethord:(NSString *)methordString{
    NSMutableDictionary *logInfo = [NSMutableDictionary dictionaryWithDictionary:@{@"contentInfo":content}];
    [logInfo setValue:logType forKey:@"topic"];
    [logInfo setValue:methordString forKey:@"stsPath"];
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"uploadLogInfoInFlutter" arguments:logInfo result:^(id  _Nullable result) {
        if ([result isKindOfClass:[NSDictionary class]]) {
            NSDictionary *resultInfo = result;
            uploadBlock(resultInfo[@"uploadLogSuccess"],@"");
        }
    }];
}

- (void)writeLogInfoToFileInFlutter:(NSString *)content writeBlock:(XYBlock1)writeBlock needShowLogInConsole:(BOOL)needShowLogInConsole{
    if(needShowLogInConsole){
        NSLog(@"%@",content);
    }
    NSMutableDictionary *logInfo = [NSMutableDictionary dictionaryWithDictionary:@{@"contentInfo":content}];
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"writeLogInfoToFile" arguments:logInfo result:^(id  _Nullable result) {
        if ([result isKindOfClass:[NSDictionary class]]) {
            NSDictionary *resultInfo = result;
            writeBlock(resultInfo[@"uploadLogSuccess"],@"");
        }
    }];
}

- (void)uploadLogInfoWithLogContent:(NSString *)content logType:(NSString *)logType logUploadBlock:(XYBlock1)uploadBlock requestMethord:(NSString *)methordString{
    //self.logUploadBlock = uploadBlock;
    JCSSLTokenCacheHelper * cacheHelper = [JCSSLTokenCacheHelper shareInstance];
    NSMutableDictionary * parms = [self getCommonParmsWith:methordString content:content logType:logType];
    NSDictionary * cacheData = [cacheHelper getCacheDataWithKey:methordString];
    if(cacheData != nil){
        [self logUploadConfigWithCallBack:^(NSNumber * isSuccess, NSString * errorMsg) {
            if([isSuccess boolValue] == false){
                [cacheHelper clearCacheKey:methordString];
            }
            if(uploadBlock != nil){
                uploadBlock(isSuccess,errorMsg);
            }
        } slsRequestDic:cacheData logType:logType requestRecords:@[parms]];
    }else{
      [@{@"needDesRequest":@"1"} java_getWithModelType:nil Path:methordString hud:nil Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            if(requestDic.allValues.count > 0){
                [cacheHelper addCacheDataWithKey:methordString data:requestDic];
                [self logUploadConfigWithCallBack:^(NSNumber * isSuccess, NSString * errorMsg) {
                    if([isSuccess boolValue] == false){
                        [cacheHelper clearCacheKey:methordString];
                    }
                    if(uploadBlock != nil){
                        uploadBlock(isSuccess,errorMsg);
                    }
                } slsRequestDic:requestDic logType:logType requestRecords:@[parms]];
            }else{
                [cacheHelper clearCacheKey:methordString];
                [self logUpdateSuccess:NO errMsg:XY_LANGUAGE_TITLE_NAMED(@"", @"日志鉴权失败")];
            }
        } failure:^(NSString *msg, id model) {
            [cacheHelper clearCacheKey:methordString];
            [self logUpdateSuccess:NO errMsg:msg];
        }];
    }
}

-(NSMutableDictionary *)getCommonParmsWith:(NSString *)methordString content:(NSString *)content logType:(NSString *)logType{
    NSMutableDictionary *parms = [NSMutableDictionary dictionary];
    NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
    NSDictionary *lastPrinterInfo = [JCBluetoothManager printerConnectStatusCache];
    NSString *lastPrinter = lastPrinterInfo[@"lastPrinter"];
    if([methordString isEqualToString:J_get_sls_Log_Token]){
        [parms setValue:UN_NIL(m_userModel.userid) forKey:@"user_id"];
        [parms setValue:[XYTool getCurrentTimesWithoutTSZ] forKey:@"record_time"];
        [parms setValue:UN_NIL(deviceId) forKey:@"device_id"];
        [parms setValue:[XYTool deviceName] forKey:@"phone_brand"];
        [parms setValue:[UIDevice currentDevice].systemVersion forKey:@"system_version"];
        [parms setValue:@"ios" forKey:@"system_type"];
        [parms setValue:UN_NIL(lastPrinter) forKey:@"macNo"];
        [parms setValue:[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"] forKey:@"app_version_name"];
        [parms setValue:UN_NIL(content) forKey:logType];
    }else{
        [parms setValue:[XYTool getNowTimeTimestamp] forKey:@"logTime"];
        [parms setValue:UN_NIL(content) forKey:logType];
    }
    return parms;
}

- (void)uploadLogInfoWithParms:(NSDictionary*)parms logType:(NSString *)logType logUploadBlock:(XYBlock1)uploadBlock requestMethord:(NSString *)methordString{
    JCSSLTokenCacheHelper * cacheHelper = [JCSSLTokenCacheHelper shareInstance];
    NSDictionary * cacheData = [cacheHelper getCacheDataWithKey:methordString];
    if(cacheData != nil){
        [self logUploadConfigWithCallBack:^(NSNumber * isSuccess, NSString * errorMsg) {
            if([isSuccess boolValue] == false){
                [cacheHelper clearCacheKey:methordString];
            }
        } slsRequestDic:cacheData logType:logType requestRecords:@[parms]];
        if(uploadBlock){
            uploadBlock([NSNumber numberWithBool:YES],@"");
        }
    }else{
        [@{} java_getWithModelType:nil Path:methordString hud:nil Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            if(requestDic.allValues.count > 0){
                [cacheHelper addCacheDataWithKey:methordString data:requestDic];
                [self logUploadConfigWithCallBack:^(NSNumber * isSuccess, NSString * errorMsg) {
                    if([isSuccess boolValue] == false){
                        [cacheHelper clearCacheKey:methordString];
                    }
                } slsRequestDic:requestDic logType:logType requestRecords:@[parms]];
                if(uploadBlock){
                    uploadBlock([NSNumber numberWithBool:YES],@"");
                }
            }else{
                if(uploadBlock){
                    uploadBlock([NSNumber numberWithBool:NO],@"");
                }
                [cacheHelper clearCacheKey:methordString];
                [self logUpdateSuccess:NO errMsg:XY_LANGUAGE_TITLE_NAMED(@"", @"日志鉴权失败")];
            }
        } failure:^(NSString *msg, id model) {
            if(uploadBlock){
                uploadBlock([NSNumber numberWithBool:NO],msg);
            }
            [cacheHelper clearCacheKey:methordString];
        }];
    }

}

- (void)uploadLogInfoWithParms:(NSDictionary*)parms logType:(NSString *)logType project:(NSString *)project logStore:(NSString *)logStore logUploadBlock:(XYBlock1)uploadBlock {
    NSString * path = @"sts/ststoken";
    JCSSLTokenCacheHelper * cacheHelper = [JCSSLTokenCacheHelper shareInstance];
    NSDictionary * cacheData = [cacheHelper getCacheDataWithKey:path];
    if(cacheData != nil){
        [self configParmsAndLogUploadWithData:cacheData parms:parms logType:logType project:project logStore:logStore key:path];
        if(uploadBlock){
            uploadBlock([NSNumber numberWithBool:YES],@"");
        }
    }else{
        [@{@"needDesRequest":@"1"} java_postWithModelType:nil Path:path hud:nil Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            if(requestDic.allValues.count > 0){
                [cacheHelper addCacheDataWithKey:path data:requestDic];
                [self configParmsAndLogUploadWithData:requestDic parms:parms logType:logType project:project logStore:logStore key:path];
                if(uploadBlock){
                    uploadBlock([NSNumber numberWithBool:YES],@"");
                }
            }else{
                if(uploadBlock){
                    uploadBlock([NSNumber numberWithBool:NO],@"");
                }
                [cacheHelper clearCacheKey:path];
                [self logUpdateSuccess:NO errMsg:XY_LANGUAGE_TITLE_NAMED(@"", @"日志鉴权失败")];
            }
        } failure:^(NSString *msg, id model) {
            if(uploadBlock){
                uploadBlock([NSNumber numberWithBool:NO],msg);
            }
            [cacheHelper clearCacheKey:path];
        }];
    }

}

-(void)configParmsAndLogUploadWithData:(NSDictionary *)requestDic parms:(NSDictionary*)parms logType:(NSString *)logType project:(NSString *)project logStore:(NSString *)logStore key:(NSString *)key{
    NSMutableDictionary *dict = [requestDic mutableCopy];
    NSString *endpoint = dict[@"endpoint"];
    if ([endpoint hasPrefix:@"https://"]) {
        endpoint = [endpoint stringByReplacingOccurrencesOfString:@"https://" withString:@""];
    }
    [dict setValue:endpoint forKey:@"endpoint"];
    [dict setValue:project forKey:@"project"];
    [dict setValue:logStore forKey:@"logStore"];
    //[self logUploadConfigWith:dict logType:logType requestRecords:@[parms]];
    [self logUploadConfigWithCallBack:^(NSNumber * isSuccess,NSString * errorMsg) {
        if([isSuccess boolValue] == false){
            JCSSLTokenCacheHelper * cacheHelper = [JCSSLTokenCacheHelper shareInstance];
            [cacheHelper clearCacheKey:key];
        }
    } slsRequestDic:dict logType:logType requestRecords:@[parms]];
}


- (void)logUploadConfigWith:(NSDictionary *)slsRequestDic
                    logType:(NSString *)logType
             requestRecords:(NSArray<NSDictionary *> *)records {
    dispatch_async(self.uploadQueue, ^{
        [self _logUploadConfigWith:slsRequestDic logType:logType requestRecords:records];
    });
}

- (void)logUploadConfigWithCallBack:(XYBlock1)callBack slsRequestDic:(NSDictionary *)slsRequestDic
                    logType:(NSString *)logType
             requestRecords:(NSArray<NSDictionary *> *)records {
    dispatch_async(self.uploadQueue, ^{
        self.logUploadBlock = callBack;
        [self _logUploadConfigWith:slsRequestDic logType:logType requestRecords:records];
    });
}

- (void)_logUploadConfigWith:(NSDictionary *)slsRequestDic
                    logType:(NSString *)logType
             requestRecords:(NSArray<NSDictionary *> *)records {
    // endpoint前需要加 https://
    NSString* endpoint = [NSString stringWithFormat:@"https://%@",[slsRequestDic objectForKey:@"endpoint"]];
    NSString* project = [slsRequestDic objectForKey:@"project"];
    NSString* logstore = [slsRequestDic objectForKey:@"logStore"];
    NSString* accesskeyid = [slsRequestDic objectForKey:@"accessKeyId"];
    NSString* accesskeysecret = [slsRequestDic objectForKey:@"accessKeySecret"];
    NSString *securityToken = [slsRequestDic objectForKey:@"securityToken"];
    // 按 logstore 区分缓存文件, 避免缓存的数据上报到错误的日志桶
    NSString *appLogPath = [NSString stringWithFormat:@"%@_%@.txt", RESOURCE_LOG_PATH, logstore ?: @""];
//    LogProducerConfig* config = [[LogProducerConfig alloc] initWithEndpoint:endpoint project:project logstore:logstore accessKeyID:accesskeyid accessKeySecret:accesskeysecret];
//    // 指定sts token 创建config，过期之前调用ResetSecurityToken重置token
    LogProducerConfig* config = [[LogProducerConfig alloc] initWithEndpoint:endpoint project:project logstore:logstore accessKeyID:accesskeyid accessKeySecret:accesskeysecret securityToken:securityToken];
    // 设置主题
    [config SetTopic:logType];
    // 设置tag信息，此tag会附加在每条日志上
    // 每个缓存的日志包的大小上限，取值为1~5242880，单位为字节。默认为1024 * 1024
    [config SetPacketLogBytes:1024*1024*2];
    // 每个缓存的日志包中包含日志数量的最大值，取值为1~4096，默认为1024
    [config SetPacketLogCount:1024];
    // 被缓存日志的发送超时时间，如果缓存超时，则会被立即发送，单位为毫秒，默认为3000
    [config SetPacketTimeout:5000];
    // 单个Producer Client实例可以使用的内存的上限，超出缓存时add_log接口会立即返回失败
    // 默认为64 * 1024 * 1024
    [config SetMaxBufferLimit:64*1024*1024];
    // 发送线程数，默认为1
    [config SetSendThreadCount:1];

    // 1 开启断点续传功能， 0 关闭
    // 每次发送前会把日志保存到本地的binlog文件，只有发送成功才会删除，保证日志上传At Least Once
    [config SetPersistent:1];
    // 持久化的文件名，需要保证文件所在的文件夹已创建。

    [config SetPersistentFilePath:appLogPath];
    // 是否每次AddLog强制刷新，高可靠性场景建议打开
    [config SetPersistentForceFlush:1];
    // 持久化文件滚动个数，建议设置成10。
    [config SetPersistentMaxFileCount:20];
    // 每个持久化文件的大小，建议设置成1-10M
    [config SetPersistentMaxFileSize:1024*1024*2];
    // 本地最多缓存的日志数，不建议超过1M，通常设置为65536即可
    [config SetPersistentMaxLogCount:65536];
#if DEBUG
    [LogProducerConfig Debug];
#endif
    //创建client
    LogProducerClient *client = [[LogProducerClient alloc] initWithLogProducerConfig:config callback:on_iaplog_send_done];

    for (NSDictionary *parms in records) {
        Log* log = [[Log alloc] init];
        int logTime = [[NSDate date] timeIntervalSince1970];
    //    //不设置默认当前时间
        [log SetTime:logTime];
        for (NSString *key in parms.allKeys) {
            id value = [parms objectForKey:key];
            if([value isKindOfClass:[NSString class]]){
                NSString *valueArrStr = [parms objectForKey:key];
                [log putContent:key value:UN_NIL(valueArrStr)];
            }else if([value isKindOfClass:[NSArray class]]){
                NSArray *valueArr = [parms objectForKey:key];
                NSError *error = nil;
                NSData *jsonData = [NSJSONSerialization dataWithJSONObject:valueArr options:NSJSONWritingPrettyPrinted error:&error];
                NSString *valueArrStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                [log putContent:key value:UN_NIL(valueArrStr)];
            }

        }
        // addLog第二个参数flush，是否立即发送，1代表立即发送，不设置时默认为0
        LogProducerResult res = [client AddLog:log flush:1];
    }
    // 记录耗时
    CFTimeInterval start = CFAbsoluteTimeGetCurrent();
    [client DestroyLogProducer];
    CFTimeInterval duration = CFAbsoluteTimeGetCurrent() - start;
    if (duration > 100) {
        BLYLogWarn(@"LogProducerClient DestroyLogProducer cost: %f", duration);
    }
}

void on_iaplog_send_done(const char * config_name, log_producer_result result, size_t log_bytes, size_t compressed_bytes, const char * req_id, const char * message, const unsigned char * raw_buffer, void * userparams) {
    if (result == LOG_PRODUCER_OK) {
        [selfClass logUpdateSuccess:YES errMsg:nil];
    } else {
        [selfClass logUpdateSuccess:NO errMsg:XY_LANGUAGE_TITLE_NAMED(@"app01458", @"日志上传失败")];
    }
}

- (void)logUpdateSuccess:(BOOL)uploadResult errMsg:(NSString *)errMsg{
    dispatch_async(dispatch_get_main_queue(), ^{
        if(self.logUploadBlock){
            self.logUploadBlock([NSNumber numberWithBool:uploadResult],errMsg);
        }
    });

}
@end
