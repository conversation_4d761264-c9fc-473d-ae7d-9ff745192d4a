//
//  JCShopBaseWebViewController.m
//  Runner
//
//  Created by xy on 2018/6/13.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopBaseWebViewController.h"
#import "JCShopMallsViewController.h"
#import "JCLabelCustomizeViewController.h"
#import <WebKit/WebKit.h>
#import <AlipaySDK/AlipaySDK.h>
#import <AudioToolbox/AudioToolbox.h>
#import "JCShopAliPay.h"
#import "sys/utsname.h"
#import "QQLBXScanViewController.h"
#import <ZLPhotoBrowser/ZLPhotoBrowser.h>
#import "Global.h"
#import "StyleDIY.h"
#import "ONImagePickerController.h"
#import "Runner-Swift.h"
#import "NiimbotJsApi.h"
@interface JCShopBaseWebViewController ()<WKUIDelegate,WKNavigationDelegate,WKScriptMessageHandler,CLLocationManagerDelegate,UINavigationControllerDelegate>

@property(nonatomic,strong) UIView *networkErrorView;
@property(nonatomic,strong) NSString *oldUrl;
@property(nonatomic,assign) NSInteger loadType;

@property (nonatomic,strong) LBXScanResult *scanResult;
@property (nonatomic,strong) CLLocationManager *locationManager;
@property(nonatomic,strong) UIPercentDrivenInteractiveTransition *interactiveTransition;
@end

@implementation JCShopBaseWebViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.loadType = 0;
        self.isNeedBackApp = YES;
        self.logStepArr = [NSMutableArray array];

        [self addStepLogDescr:@"商城首页基类初始化init"];
        self.statusBackColor = 0xFFFFFF;
        self.configuration = [[WKWebViewConfiguration alloc] init];
        self.configuration.userContentController = [WKUserContentController new];
        self.configuration.allowsInlineMediaPlayback = YES;
        WKPreferences *preferences = [WKPreferences new];
        preferences.javaScriptCanOpenWindowsAutomatically = YES;
        self.configuration.preferences = preferences;
        
        // 设置脚本消息处理器
        [self setupScriptMessageHandlers];
        
        self.webView = [[DWKWebView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight  -  (iPhoneX?88:64)) configuration:self.configuration];
        self.webView.scrollView.scrollEnabled = NO;
      self.webView.scrollView.bounces = NO;
      // TODO 商城需要全屏 -wy
//        self.webView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
        self.webView.navigationDelegate = self;
        self.webView.DSUIDelegate = self;
      [self.webView addJavascriptObject:[[NiimbotJsApi alloc] initWithWeb:self] nameSpace:nil];
      
      //屏蔽侧滑返回手势
//      UIScreenEdgePanGestureRecognizer *edgePan = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(edgePan:)];
//      edgePan.edges = UIRectEdgeLeft;
//      [self.webView addGestureRecognizer:edgePan];

        [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionOld | NSKeyValueObservingOptionNew context:nil];
        self.progressView = [[UIProgressView alloc]initWithFrame:CGRectMake(0, 0,kScreenWidth, 2)];
        CGAffineTransform transform = CGAffineTransformMakeScale(1.0f, 2.0f);
        self.progressView.transform = transform;//设定宽高
        self.progressView.progressTintColor = HEX_RGB(0x537FB7);
        [self resetWKWebViewUA];

    }
    return self;
}

///当前Webview是否能返回
///[self.webView canGoBack]也来web端的导航栈实现,不一定准确,所以我们认为首页的商城都能goback,因为其不存在pop事件,但是即使调用了webview的goback事件,如果web端的导航栈不是线性的也无法返回
- (BOOL)webViewCanGoBack {
  return [self.webView canGoBack] || [self isHomePageShop];
}
- (void)edgePan:(UIScreenEdgePanGestureRecognizer *)recognizer {
  
  if ([self webViewCanGoBack]) {
    if(recognizer.state == UIGestureRecognizerStateBegan){
      [self.webView goBack];
    }
  }else{
    CGPoint translation = [recognizer translationInView:self.view];
    CGFloat percent = translation.x / self.view.bounds.size.width;
    switch (recognizer.state) {
      case UIGestureRecognizerStateBegan:
        self.interactiveTransition = [[UIPercentDrivenInteractiveTransition alloc] init];
        [self.navigationController popViewControllerAnimated:YES];
        break;
      case UIGestureRecognizerStateChanged:
        [self.interactiveTransition updateInteractiveTransition:percent];
        break;
      case UIGestureRecognizerStateEnded:
      case UIGestureRecognizerStateCancelled:
      {
        CGPoint velocity1 = [recognizer velocityInView:self.view];
        // 根据滑动速度和进度决定是否完成转场
        if (percent > 0.5 || velocity1.x > 1000) {
          [self.interactiveTransition finishInteractiveTransition];
        } else {
          [self.interactiveTransition cancelInteractiveTransition];
        }
        self.interactiveTransition = nil;
        break;
      }
      default:
        break;
    }
  }
}

- (void)setIsFullScreen:(BOOL)isFullScreen {
    _isFullScreen = isFullScreen;
    if (isFullScreen) {
        self.webView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
    } else {
        self.webView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight  -  (iPhoneX?88:64));
    }
}

- (void)setScrollEnabled:(BOOL)scrollEnabled {
    self.webView.scrollView.scrollEnabled = scrollEnabled;
}

// 设置脚本消息处理器
- (void)setupScriptMessageHandlers {
    [self.configuration.userContentController addScriptMessageHandler:self name:@"backHome"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"back_home"]; // 兼容JCShopAliPay
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_shopScan"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"scanCodeSearchLabelResult"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Enter_Detail"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Leave_Detail"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Show_Tabbar"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_Go_Home"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"jcdy_StatusBar_Color"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"gotoAmazonShop"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"tabShopShowNumChanged"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"nativeFunction"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"webEventTrack"];
    [self.configuration.userContentController addScriptMessageHandler:self name:@"loginOnToApp"];
    // 选择图片
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getAlbum"];
    // 拍照
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getPhotograph"];
    // 加载完毕通知
    [self.configuration.userContentController addScriptMessageHandler:self name:@"loadingEnd"];
    // 获取打印机名称
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getPrinterType"];
    // 获取剪贴板内容
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getClipboardContent"];
    // 请求定位权限
    [self.configuration.userContentController addScriptMessageHandler:self name:@"requestLocationPermission"];
    // 触发马达震动
    [self.configuration.userContentController addScriptMessageHandler:self name:@"doHaptics"];
    // 获取连接的机器信息
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getUserMachineInfo"];
    // 获取设备屏幕数据
    [self.configuration.userContentController addScriptMessageHandler:self name:@"getDeviceScreenData"];
}



#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    NSLog(@"基类收到脚本消息:%@",message.name);
    
    if ([message.name isEqualToString:@"backHome"] || [message.name isEqualToString:@"back_home"]) {//点击返回
        [self.navigationController popViewControllerAnimated:YES];
        if([m_userModel.is_FirstShop isEqualToString:@"1"]){
            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
                
            }];
        }
    }else if ([message.name isEqualToString:@"jcdy_shopScan"]) {//商城扫描
        [self doSaoMa];
    }else if ([message.name isEqualToString:@"loginOnToApp"]) {//获取原生商城token
        [self getTokenFromNative];
    }else if ([message.name isEqualToString:@"scanCodeSearchLabelResult"]) {//商城扫描结果
        NSNumber *searchResult = message.body;
        [[XYCenter sharedInstance] scanTrackWithSearchResult:self.scanResult searchtype:@"2" searchResult:searchResult.boolValue?@"1":@"0" source:@"5"];
    }else if ([message.name isEqualToString:@"jcdy_StatusBar_Color"]) {//状态栏颜色
        NSString *messageBody = message.body;
        [self statusBarBackgroundColor:messageBody];
    }else if ([message.name isEqualToString:@"jcdy_Show_Tabbar"]) {//展示底部tabbar
      NSNumber *num = message.body;
        if(!self.isHomePageShop){
            return;
        }
        UIViewController *rootVC = ((AppDelegate *)[UIApplication sharedApplication].delegate).mainVC;
        if([rootVC isKindOfClass:[UITabBarController class]]){
            UITabBarController *root = (UITabBarController*)rootVC;
            UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
            if(![naviVC.visibleViewController isKindOfClass:[self class]]){
                return;
            }
        }
      self.tabBarController.tabBar.hidden = ![@1 isEqualToNumber:num];
      [self.view setNeedsLayout];
      [self.view layoutIfNeeded];
    }else if ([message.name isEqualToString:@"jcdy_Go_Home"]) {//返回app首页
        NSArray *arr = self.tabBarController.viewControllers;
        UINavigationController *navi = [arr safeObjectAtIndex:1];
        UIViewController *vc = navi.visibleViewController;
        if([vc isKindOfClass:[self class]]){
            self.tabBarController.selectedIndex = 3;
            [self.navigationController popToRootViewControllerAnimated:NO];
        }
    }else if ([message.name isEqualToString:@"gotoAmazonShop"]) {//跳转亚马逊
        NSString *messageBody = message.body;
        NSDictionary *messageDic = [messageBody xy_toDictionary];
        if(messageDic != nil){
            NSString *schemUrl = [messageDic objectForKey:@"schemeUrl"];
            NSString *browserUrl = [messageDic objectForKey:@"browserUrl"];
            NSString *urlString = [NSString stringWithFormat:@"%@%@",@"com.amazon.mobile.shopping://",schemUrl];
            NSURL *url = [NSURL URLWithString:urlString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }else if(!STR_IS_NIL(browserUrl)){
                NSURL *URL = [NSURL URLWithString:browserUrl];
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                      //  回调
                }];
            }
        }
    }else if ([message.name isEqualToString:@"nativeFunction"]) {//
        NSString *messageInfo = message.body;
        if(!STR_IS_NIL(messageInfo)){
            if([messageInfo containsString:@"?"]){
                NSDictionary *parmsDic = [XYTool dictionaryWithUrlString:messageInfo];
                NSString *actName = [parmsDic objectForKey:@"actName"];
                NSString *nativeRoute = [messageInfo componentsSeparatedByString:@"?"][0];
                [JCToNativeRouteHelp toNativePageWith:UN_NIL(nativeRoute) fromType:JC_H5Web eventTitle:actName origRout:messageInfo];
            }else{
                [JCToNativeRouteHelp toNativePageWith:messageInfo fromType:JC_H5Web eventTitle:@""];
            }
        }
    }else if ([message.name isEqualToString:@"webEventTrack"]) {//
        NSString *messageInfo = message.body;
        NSDictionary *messageDic = [messageInfo xy_toDictionary];
        if(messageDic.allKeys.count > 0){
            NSString *eventCode = messageDic[@"eventCode"];
            NSString *posCode = messageDic[@"posCode"];
            NSDictionary *eventTitle = messageDic[@"params"];
            if(eventTitle == nil || eventTitle.count == 0){
                eventTitle = @{};
            }
            JC_TrackWithparms(UN_NIL(eventCode),UN_NIL(posCode),eventTitle);
        }
    }else if ([message.name isEqualToString:@"tabShopShowNumChanged"]) {
        NSString *showNumber = message.body;
        if(!STR_IS_NIL(showNumber)){
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_SHOP_MALL_MSG_SHOW object:showNumber];
        }
    } else if ([message.name isEqualToString:@"getAlbum"]) {
        [self pickImageFromType:UIImagePickerControllerSourceTypePhotoLibrary maxSize:((NSNumber *)message.body).integerValue completion:^{
            // 基类默认完成回调
        }];
    } else if ([message.name isEqualToString:@"getPhotograph"]) {
        [self pickImageFromType:UIImagePickerControllerSourceTypeCamera maxSize:((NSNumber *)message.body).integerValue completion:^{
            // 基类默认完成回调
        }];
    } else if ([message.name isEqualToString:@"loadingEnd"]) {
        // 加载完毕，传递Token
        NSString *jsStr = xy_isLogin ? [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY"] : [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    } else if ([message.name isEqualToString:@"getPrinterType"]) {
        NSString *printerName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
        NSString *printerType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:NO];
        NSString *jsStr = [NSString stringWithFormat:@"iOSSetPrinterType('%@')", printerType];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            
        }];
    } else if ([message.name isEqualToString:@"getClipboardContent"]) {
        UIPasteboard *pasteboard = [UIPasteboard generalPasteboard];
        NSString *clipboardText = pasteboard.string ?: @"";
        NSString *jsStr = [NSString stringWithFormat:@"clipboardContentCallBack('%@')", clipboardText];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            if (error) {
                NSLog(@"getClipboard JS执行错误: %@", error);
            } else {
                NSLog(@"剪贴板内容已发送到前端: %@", clipboardText);
            }
        }];
    } else if ([message.name isEqualToString:@"requestLocationPermission"]) {
        [self requestLocationPermission];
    } else if ([message.name isEqualToString:@"jcdy_Enter_Detail"]) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
        self.detailStateView.hidden = NO;
    } else if ([message.name isEqualToString:@"jcdy_Leave_Detail"]) {
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
        self.detailStateView.hidden = YES;
    } else if ([message.name isEqualToString:@"doHaptics"]) {
        // 触发马达震动
        NSString *hapticType = message.body;
        [self performHapticFeedback:hapticType];
    } else if ([message.name isEqualToString:@"getUserMachineInfo"]) {
        // 获取连接的机器信息
        NSString *printerName = [JCBluetoothManager sharedInstance].connectedModel.name;
        BOOL isConnected = JC_IS_CONNECTED_PRINTER;
        
        NSDictionary *machineInfo = @{
            @"currentMachineName": STR_IS_NIL(printerName) ? @"" : printerName,
            @"isConnected": @(isConnected)
        };
        
        NSString *jsonString = machineInfo.xy_toJsonString;
        NSString *jsStr = [NSString stringWithFormat:@"machineInfoCallback(%@)", jsonString];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            if (error) {
                NSLog(@"getUserMachineInfo JS执行错误: %@", error);
            }
        }];
    } else if ([message.name isEqualToString:@"getDeviceScreenData"]) {
        // 获取设备屏幕数据
        CGSize screenSize = [UIScreen mainScreen].bounds.size;
        CGFloat scale = [UIScreen mainScreen].scale;
        NSString *deviceName = [XYTool deviceName];
        
        NSDictionary *screenData = @{
            @"width": @((NSInteger)screenSize.width),
            @"height": @((NSInteger)screenSize.height),
            @"scale": @(scale),
            @"deviceName": deviceName
        };
        
        NSString *jsonString = screenData.xy_toJsonString;
        NSString *jsStr = [NSString stringWithFormat:@"screenDataCallback(%@)", jsonString];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            if (error) {
                NSLog(@"getDeviceScreenData JS执行错误: %@", error);
            }
        }];
    }
}



- (void)addStepLogDescr:(NSString *)stepLogDescr{
    if([self isKindOfClass:[JCShopMallsViewController class]]){
        [self.logStepArr addObject:stepLogDescr];
    }
}
- (void)webView:(WKWebView *)webView runJavaScriptTextInputPanelWithPrompt:(NSString *)prompt defaultText:(nullable NSString *)defaultText initiatedByFrame:(WKFrameInfo *)frame completionHandler:(void (^)(NSString * __nullable result))completionHandler {
    NSError *err = nil;
    NSData *dataFromString = [prompt dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *payload = [NSJSONSerialization JSONObjectWithData:dataFromString options:NSJSONReadingMutableContainers error:&err];
    if (!err){
        NSString *type = [payload objectForKey:@"type"];
        if (type && [type isEqualToString:@"JSbridge"]){
            completionHandler([self getReturnValueWithPayload:payload]);
        }
    }
}
// 自定义方法
- (NSString *)getReturnValueWithPayload:(NSDictionary *)payload{
    NSString *returnValue = @"";
    NSString *functionName = [payload objectForKey:@"functionName"];
    if ([functionName isEqualToString:@"getPrinterType"]) {
        NSString *printerName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
        NSString *printerType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:NO];
        returnValue = printerType;
    }//
    return returnValue;
}

- (void)refreshWebViewWith:(NSString *)myUrlStr{};
// 使用浅色模式
- (UIUserInterfaceStyle)overrideUserInterfaceStyle
{
    // 使用浅色模式
    return UIUserInterfaceStyleLight;
}

- (void)resetWKWebViewUA{
    XYWeakSelf
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSString *oldUA = result;
        NSString *appId = [NSString stringWithFormat:@"AppId/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]];
        NSString *os = [NSString stringWithFormat:@"OS/%@",@"ios"];
        NSString *appVersionName = [NSString stringWithFormat:@"AppVersionName/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
        NSString *model = [NSString stringWithFormat:@"Model/%@",[XYTool deviceName]];
        NSString *systemVersion = [NSString stringWithFormat:@"SystemVersion/%@",[UIDevice currentDevice].systemVersion];
        NSString *deviceId = [NSString stringWithFormat:@"DeviceId/%@",[JCKeychainTool getDeviceIDInKeychain]];
        NSString *boundleId = [NSString stringWithFormat:@"boundleId/%@",[[NSBundle mainBundle] bundleIdentifier]];
        NSString *referer = @"referer/CP001Mobile";
        NSString *newUA =[NSString stringWithFormat:@"%@ %@ %@ %@ %@ %@ %@ %@ %@", oldUA,appId,os,appVersionName,model,systemVersion,deviceId,boundleId,referer];
        weakSelf.webView.customUserAgent = newUA;
//        [[NSUserDefaults standardUserDefaults] setObject:newUA forKey:@"niimbot-user-agent"];
//        [[NSUserDefaults standardUserDefaults] synchronize];
        NSLog(@"UA:%@",newUA);
    }];
}
- (void)loadUrl:(NSString *)url
{
    
    NSString *versionComplateString = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];
    if(!self.isNeedBackApp){
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin",url,self.entrance_type_id,userShopToken,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin" ,url,self.entrance_type_id,userShopToken,versionComplateString];
        }
    }else{
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin",url,self.entrance_type_id,userShopToken,versionComplateString];
        }else{
            url = [NSString stringWithFormat:@"%@?back_app=1&entrance_type_id=%@&token=%@&version=%@&platform_system_id=CP001&from=yundayin" ,url,self.entrance_type_id,userShopToken,versionComplateString];
        }
    }
    
    [self.progressView setAlpha:1.0f];
    if(self.jumpSource != nil && self.jumpSource.length > 0){
        url = [NSString stringWithFormat:@"%@&jumpSource=%@",url,self.jumpSource];
    }
    
    // 非中文环境且存在站点信息
    if (!([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] && STR_IS_NIL([[XYCenter sharedInstance] siteCode]))) {
        url = [url stringByAppendingFormat:@"&siteCode=%@&languageCode=%@", [[XYCenter sharedInstance] siteCode], [[XYCenter sharedInstance] abroadSiteLanguageCode]];
    }

    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页加载通过URL：%@",url]];
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0]];
    self.oldUrl = url;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(20 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if(self.loadType == 0){
            self.networkErrorView.hidden = NO;
            NSLog(@"定时检查 商城H5加载白屏失败");
        }else{
        }
    });
}
- (void)viewDidLoad {
    XYWeakSelf
    [super viewDidLoad];
    [self setVCBackImageView];
    [self.view addSubview:self.webView];
    [self.view addSubview:self.progressView];
    [self.view addSubview:self.networkErrorView];
    self.lsl_prefersNavigationBarHidden = YES;
    [self.networkErrorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.view);
    }];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow:) name:UIKeyboardWillShowNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWasHidden:) name:UIKeyboardDidHideNotification object:nil];
    // 添加应用前后台通知监听
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationWillEnterForeground:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationDidEnterBackground:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [self addStepLogDescr:@"商城首页基类viewDidLoad"];
}

- (void)colseCurrentPage{
    if(self.colseCurrentPageBlock){
        self.colseCurrentPageBlock();
    }else{
        [self.navigationController popViewControllerAnimated:YES];
    }
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
//  [super viewWillAppear:animated];
//  self.navigationController.delegate = self;
    [self addStepLogDescr:@"商城首页基类viewWillAppear"];
    if(self.webView.title == nil) {
        [self.webView reload];
    }
}

- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
//  if (self.navigationController.delegate == self) {
//      self.navigationController.delegate = nil;
//  }
}

//- (void)viewDidLayoutSubviews{
//  [super viewDidLayoutSubviews];
//  [self setupWebViewFrame];
//}
- (void)getTokenFromNative{
    [[JCLoginManager sharedInstance] checkLogin:^{
        
    } viewController:self loginSuccessBlock:^{
        // 加载完毕，传递Token
        NSString *jsStr = xy_isLogin ? [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY", @"1"] : [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY", @"0"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    }];
}

- (UIView *)networkErrorView{
    if(!_networkErrorView){
        _networkErrorView = [[UIView alloc] init];
        _networkErrorView.backgroundColor = HEX_RGB(0xE5E5E5);
        _networkErrorView.hidden = YES;
        UIImageView *networkErrImage = [[UIImageView alloc] init];
        networkErrImage.image = XY_IMAGE_NAMED(@"shopNetworkErr");
        [_networkErrorView addSubview:networkErrImage];
        
        UILabel *networkErrTipLabel = [[UILabel alloc] init];
        networkErrTipLabel.font = MY_FONT_Regular(14);
        networkErrTipLabel.numberOfLines = 0;
        networkErrTipLabel.textColor = HEX_RGB(0x666666);
        networkErrTipLabel.textAlignment = NSTextAlignmentCenter;
        networkErrTipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000121", @"当前网络状态不佳，请稍后再试");
        [_networkErrorView addSubview:networkErrTipLabel];
        
        UIButton *refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        refreshBtn.backgroundColor = HEX_RGB(0xFFFFFF);
        refreshBtn.layer.cornerRadius = 20;
        refreshBtn.titleLabel.font = MY_FONT_Regular(16);
        [refreshBtn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        [refreshBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") forState:UIControlStateNormal];
        [refreshBtn  addTarget:self action:@selector(refreshCurrentWeb) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:refreshBtn];
        
        UIButton *backBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        backBtn.backgroundColor = HEX_RGB(0xFFFFFF);
        backBtn.layer.cornerRadius = 20;
        backBtn.titleLabel.font = MY_FONT_Regular(16);
        [backBtn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        [backBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01179", @"返回") forState:UIControlStateNormal];
        [backBtn  addTarget:self action:@selector(colseCurrentPage) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:backBtn];
        if(self.isNeedBackApp){
            [networkErrImage mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.height.mas_equalTo(networkErrImage.mas_width).multipliedBy(0.5);
                make.centerY.equalTo(_networkErrorView).offset(-130);
            }];
            [networkErrTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.top.equalTo(networkErrImage.mas_bottom).offset(35);
            }];
            
            float btnTitleWidth1 = [XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:1000].width;
            [refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth1 + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(networkErrTipLabel.mas_bottom).offset(28);
            }];
            
            [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth1 + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(refreshBtn.mas_bottom).offset(20);
            }];
        }else{
            [networkErrImage mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.height.mas_equalTo(networkErrImage.mas_width).multipliedBy(0.5);
                make.centerY.equalTo(_networkErrorView).offset(-100);
            }];
            [networkErrTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
                make.centerX.equalTo(_networkErrorView);
                make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
                make.top.equalTo(networkErrImage.mas_bottom).offset(35);
            }];
            
            float btnTitleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:1000].width;
            [refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
                make.width.mas_equalTo(btnTitleWidth + 48);
                make.height.mas_equalTo(@40);
                make.centerX.equalTo(_networkErrorView);
                make.top.equalTo(networkErrTipLabel.mas_bottom).offset(28);
            }];
        }
        
    }
    return _networkErrorView;
}

- (void)refreshCurrentWeb{
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.oldUrl] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0]];
}

- (void)shopDetailStateView{
    float stateViewheight = iPhoneX?44:20;
    UIView *navBKView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, kScreenWidth, stateViewheight)];
    navBKView.backgroundColor = HEX_RGB(0xFFFFFF);
    [self.view addSubview:navBKView];
    self.detailStateView = navBKView;
}

- (void)statusBarBackgroundColor:(NSString *)colorValueString{
    NSDictionary *colorValueDic = [colorValueString xy_toDictionary];
    NSString *typeValue = [colorValueDic objectForKey:@"type"];
    NSString *colorValue = [colorValueDic objectForKey:@"color"];
    NSString *textColor = [NSString stringWithFormat:@"0x%@",[colorValue stringByReplacingOccurrencesOfString:@"#" withString:@""]];
    unsigned long red = strtoul([textColor UTF8String],0,16);
    if([typeValue isEqualToString:@"1"]){
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        XYTabBarController *currentTabbarVC = appDelegate.mainVC;
        XYNavigationController *navVC = currentTabbarVC.selectedViewController;
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        self.statusBarStyle = statusBarStyle;
        if([navVC.visibleViewController isKindOfClass:[self class]]){
            [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
        }
    }else{
        self.statusBarStyle = UIStatusBarStyleLightContent;
        [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    }
    self.detailStateView.backgroundColor = HEX_RGB(red);
    self.statusBackColor = red;
}

-(void)addObserverNotification{
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeHidden:) name:UIWindowDidBecomeHiddenNotification object:nil];
}

-(void)windowDidBecomeHidden:(NSNotification *)noti{
    UIWindow * win = (UIWindow *)noti.object;
    if(win){
        UIViewController *rootVC = win.rootViewController;
        NSArray<__kindof UIViewController *> *vcs = rootVC.childViewControllers;
        if([vcs.firstObject isKindOfClass:NSClassFromString(@"AVPlayerViewController")]){
        #pragma clang diagnostic push
        #pragma clang diagnostic ignored "-Wdeprecated"
            [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
        #pragma clang diagnostic pop
        }
    }
}

- (void)setVCBackImageView{
    UIView *backView = [[UIView alloc] initWithFrame:self.view.bounds];
    backView.backgroundColor = [UIColor whiteColor];
    [self.view addSubview:backView];
}

// 页面开始加载时调用
-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didStartProvisionalNavigation");
    NSLog(@"当前加载URL：%@",webView.URL);
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调开始加载通过URL：%@",webView.URL.absoluteString]];
}

// 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation{
    NSLog(@"内容开始返回:%@ 当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调内容开始返回通过URL：%@",webView.URL.absoluteString]];
}

// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{//这里修改导航栏的标题，动态改变
    NSLog(@"开始H5完毕:%@  当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    NSLog(@"%@",self.view);
    self.loadSuccess = YES;
    self.loadType = 1;
    self.networkErrorView.hidden = YES;
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"UA:%@",result);
    }];
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调加载H5完毕通过URL：%@",webView.URL.absoluteString]];
}

- (void)webViewWebContentProcessDidTerminate:(WKWebView *)webView NS_AVAILABLE(10_11, 9_0){
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调即将白屏通过URL：%@",webView.URL.absoluteString]];
    [self.webView reload];
}

// 页面加载失败时调用
-(void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    NSLog(@"请求失败，不显示webView");
    self.loadSuccess = NO;
    self.loadType = 2;
    NSDictionary *userInfo = error.userInfo;
    NSURL *errorFailingUrl = [userInfo objectForKey:@"NSErrorFailingURLKey"];
    NSString *errorFailingString = [errorFailingUrl absoluteString];
    if(error.code == 102 || [errorFailingString rangeOfString:@"iwantbacktoapp"].location != NSNotFound || [errorFailingString rangeOfString:@"alipay"].location != NSNotFound){
        [self.progressView setProgress:0.0f animated:YES];
        [self.progressView setAlpha:0.0f];
    }else{
        self.networkErrorView.hidden = NO;
        [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调didFailProvisional加载失败通过URL：%@",webView.URL.absoluteString]];
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error{
    [self addStepLogDescr:[NSString  stringWithFormat:@"商城首页webview回调didFailNavigation加载失败通过URL：%@",webView.URL.absoluteString]];
}

// 接收到服务器跳转请求之后再执行
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didReceiveServerRedirectForProvisionalNavigation");
}

#ifdef DEBUG
- (void)webView:(WKWebView *)webView didReceiveAuthenticationChallenge:(NSURLAuthenticationChallenge *)challenge completionHandler:(void (^)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler {
    NSURLCredential * credential = [[NSURLCredential alloc] initWithTrust:[challenge protectionSpace].serverTrust];
    completionHandler(NSURLSessionAuthChallengeUseCredential, credential);
}
#endif


- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    if (((NSHTTPURLResponse *)navigationResponse.response).statusCode == 200) {
        decisionHandler (WKNavigationResponsePolicyAllow);
    }else {
        decisionHandler(WKNavigationResponsePolicyCancel);
    }
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    if ([str containsString:@"alipayurl"]) {
        [self performSelector:@selector(gotoAliPay1) withObject:nil afterDelay:1];
    }
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            
        }
    }];
    
    if (isIntercepted) {
        actionPolicy = WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByRemovingPercentEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
        if(!bSucc) {
            [MBProgressHUD showToastWithMessageDarkColor:@"未检测到微信APP，请您先安装"];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    decisionHandler(actionPolicy);
    
}
- (void)gotoAliPay1
{
    [self.webView goBack];
    [self performSelector:@selector(gotoAliPay2) withObject:nil afterDelay:3];
}
- (void)gotoAliPay2
{
    JCShopAliPay *c1 = [[JCShopAliPay alloc] init];
    [self.navigationController pushViewController:c1 animated:YES];
}

// 监听事件处理
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    XYWeakSelf
    if ([keyPath isEqual:@"estimatedProgress"] && object == self.webView) {
        [self.progressView setAlpha:1.0f];
        [self.progressView setProgress:self.webView.estimatedProgress animated:YES];
        if (self.webView.estimatedProgress  >= 1.0f) {
            [UIView animateWithDuration:0.3 delay:0.3 options:UIViewAnimationOptionCurveEaseOut animations:^{
                [weakSelf.progressView setAlpha:0.0f];
            } completion:^(BOOL finished) {
                [weakSelf.progressView setProgress:0.0f animated:YES];
            }];
        }
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }else{
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

- (void)dealloc
{
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    
    // 清理脚本消息处理器
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"backHome"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"back_home"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_shopScan"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"scanCodeSearchLabelResult"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_Enter_Detail"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_Leave_Detail"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_Show_Tabbar"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_Go_Home"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcdy_StatusBar_Color"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"gotoAmazonShop"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"tabShopShowNumChanged"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"nativeFunction"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"webEventTrack"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"loginOnToApp"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getAlbum"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getPhotograph"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"loadingEnd"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getPrinterType"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getClipboardContent"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"requestLocationPermission"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"doHaptics"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getUserMachineInfo"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"getDeviceScreenData"];
    
    NSLog(@"控制器：%@释放",NSStringFromClass([self class]));
}

- (void)keyboardWillShow:(NSNotification *)aNotification
{
     //获取键盘的高度
    NSDictionary *userInfo = [aNotification userInfo];
    NSValue *aValue = [userInfo      objectForKey:UIKeyboardFrameEndUserInfoKey];
    CGRect keyboardRect = [aValue CGRectValue];
    int height = keyboardRect.size.height;
    NSString *jsStr = [NSString stringWithFormat:@"setKeyboardHeight('%@')",StringFromInt((NSInteger)height)];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//当键盘出现或改变时调用
- (void)keyboardWasHidden:(NSNotification *)aNotification
{
    int height = 0;
    NSString *jsStr = [NSString stringWithFormat:@"setKeyboardHeight('%@')",StringFromInt((NSInteger)height)];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

// 应用即将进入前台
- (void)applicationWillEnterForeground:(NSNotification *)aNotification
{
    NSString *jsStr = @"applicationWillEnterForeground()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"applicationWillEnterForeground JS执行错误: %@", error);
        } else {
            NSLog(@"applicationWillEnterForeground 事件已发送到前端");
        }
    }];
}

// 应用已进入后台
- (void)applicationDidEnterBackground:(NSNotification *)aNotification
{
    NSString *jsStr = @"applicationDidEnterBackground()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"applicationDidEnterBackground JS执行错误: %@", error);
        } else {
            NSLog(@"applicationDidEnterBackground 事件已发送到前端");
        }
    }];
}


- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - 导航代理 UINavigationControllerDelegate

- (id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController
                                  animationControllerForOperation:(UINavigationControllerOperation)operation
                                               fromViewController:(UIViewController *)fromVC
                                                 toViewController:(UIViewController *)toVC {
    // 返回自定义动画控制器
    return operation == UINavigationControllerOperationPop ? [[SlidePopAnimator alloc] initWithOperation:operation] : nil;
}

- (id<UIViewControllerInteractiveTransitioning>)navigationController:(UINavigationController *)navigationController
                         interactionControllerForAnimationController:(id<UIViewControllerAnimatedTransitioning>)animationController {
    // 返回交互式过渡对象
    return self.interactiveTransition;
}
#pragma mark - 通用方法实现

//扫码事件处理
- (void)doSaoMa
{
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
      [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:nil sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:mediaType completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self toScanGoodsCode];
                });
            }
        }];
    }else{
        [self toScanGoodsCode];
    }
}

- (void)toScanGoodsCode{
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = 4;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    
    vc.style = [StyleDIY qqStyle];
    vc.scanResultBlock = ^(LBXScanResult *result) {
        self.scanResult = result;
        NSString *code = result.strScanned;
        if (!STR_IS_NIL(code)) {
            NSString *jsStr = [NSString stringWithFormat:@"returnScanResult('%@')",code];
            [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
        }else{
            //扫描取消
            NSString *jsStr = @"cancelScan()";
            [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                NSLog(@"%@----%@",result, error);
            }];
            return;
        }
    };
    //镜头拉远拉近功能
    vc.isVideoZoom = YES;
    XYNavigationController *navVC = [[XYNavigationController alloc] initWithRootViewController:vc];
    [self presentViewController:navVC animated:YES completion:^{
        
    }];
}

// 选择图片方法 - 完整实现
- (void)pickImageFromType:(UIImagePickerControllerSourceType)sourceType maxSize:(NSInteger)maxSize completion:(void(^)(void))completion {
    void (^pickBlock)(void) = ^() {
        ONImagePickerController *imagePicker = [ONImagePickerController sharedInstance];
        float cutHeight = 0;
        cutHeight = kScreenWidth/1.6;
        [imagePicker showImagePickerWithPresentController:self sourceType:sourceType allowEdit:NO cutFrame:CGRectMake(0, (kScreenHeight - cutHeight)/2, kScreenWidth, cutHeight)];
        [imagePicker setChooseImageBlock:^(UIImage * _Nonnull image) {
            // 先Dismiss掉页面,前端展示Loading
            dispatch_async(dispatch_get_main_queue(), ^{
                [self dismissViewControllerAnimated:false completion:nil];
                NSString *jsStr = [NSString stringWithFormat:@"imageLoading()"];
                [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                    
                }];
                
            });
            NSString *base64;
            NSData *pngData = UIImagePNGRepresentation(image);
            NSInteger scale = pngData.length / maxSize;
            if (scale > 1) {
                // 按照比例进行压缩
                CGSize size = CGSizeApplyAffineTransform(image.size, CGAffineTransformMakeScale(1.0 / scale, 1.0 / scale));
                UIImage *targetImage = [image imageByScalingAndCroppingToSize: size];
                NSData *targetPngData = UIImagePNGRepresentation(targetImage);
                if (targetPngData.length > maxSize) {
                    // 转化为JPG压缩
                    base64 = UIImageJPEGRepresentation(image, 0.8).jk_base64EncodedString;
                } else {
                    base64 = targetPngData.jk_base64EncodedString;
                }
            } else {
                base64 = UIImageJPEGRepresentation(image, 0.8).jk_base64EncodedString;
            }
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *jsStr = [NSString stringWithFormat:@"imageDataCallback('%@')", UN_NIL(base64)];
                [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                    
                }];
            });
            if (completion) {
                completion();
            }
        }];
    };
    
    if (sourceType == UIImagePickerControllerSourceTypeCamera) {
        [self checkCameraAuthorization:^(bool isAuthorization) {
            if (isAuthorization) {
                pickBlock();
            }
        }];
    } else {
        pickBlock();
    }
}

// 检查相机权限
- (void)checkCameraAuthorization:(void (^)(bool))completion {
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
      [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{

            
        } sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:AVMediaTypeVideo completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    if (completion) {
                        completion(YES);
                    }
                });
            }
        }];
    }else{
        if (completion) {
            completion(YES);
        }
    }
}

// 定位权限方法 - 完整实现
- (void)requestLocationPermission {
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    
    if (status == kCLAuthorizationStatusAuthorizedWhenInUse || 
        status == kCLAuthorizationStatusAuthorizedAlways) {
        // 已有权限，直接回调
        [self notifyLocationPermissionStatus:YES];
    } 
    else if (status == kCLAuthorizationStatusNotDetermined) {
        // 未确定状态，请求权限
        self.locationManager = [[CLLocationManager alloc] init];
        self.locationManager.delegate = self;
        [self.locationManager requestWhenInUseAuthorization];
    } 
    else {
        // 已拒绝或受限制，提示用户
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                       message:XY_LANGUAGE_TITLE_NAMED(@"",@"请在“设置-隐私-定位服务” ， 允许精臣云打印访问你的位置信息")
              cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
                sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") 
                   cancelBlock:^{
                       [self notifyLocationPermissionStatus:NO];
                   } 
                     sureBlock:^{
                         NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
                         if([[UIApplication sharedApplication] canOpenURL:url]) {
                             [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
                         }
                         [self notifyLocationPermissionStatus:NO];
                     }];
         }
}

// 通知Web页面定位权限状态
- (void)notifyLocationPermissionStatus:(BOOL)hasPermission {
    NSString *jsStr = [NSString stringWithFormat:@"hasLocationPermission(%d)", hasPermission ? 1 : 0];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error) {
            NSLog(@"通知Web页面定位权限状态失败: %@", error);
        }
    }];
}

// 执行触觉反馈
- (void)performHapticFeedback:(NSString *)hapticType {
    if (@available(iOS 10.0, *)) {
        if ([hapticType isEqualToString:@"light"]) {
            UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleLight];
            [generator prepare];
            [generator impactOccurred];
        } else if ([hapticType isEqualToString:@"medium"]) {
            UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleMedium];
            [generator prepare];
            [generator impactOccurred];
        } else if ([hapticType isEqualToString:@"heavy"]) {
            UIImpactFeedbackGenerator *generator = [[UIImpactFeedbackGenerator alloc] initWithStyle:UIImpactFeedbackStyleHeavy];
            [generator prepare];
            [generator impactOccurred];
        } else if ([hapticType isEqualToString:@"selection"]) {
            UISelectionFeedbackGenerator *generator = [[UISelectionFeedbackGenerator alloc] init];
            [generator prepare];
            [generator selectionChanged];
        } else {
            // 默认使用传统震动
            AudioServicesPlaySystemSound(kSystemSoundID_Vibrate);
        }
    } else {
        // iOS 10以下版本使用传统震动
        AudioServicesPlaySystemSound(kSystemSoundID_Vibrate);
    }
}

// 定位管理器代理方法
- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    if (status == kCLAuthorizationStatusAuthorizedWhenInUse || 
        status == kCLAuthorizationStatusAuthorizedAlways) {
        [self notifyLocationPermissionStatus:YES];
    } else if (status == kCLAuthorizationStatusDenied || 
               status == kCLAuthorizationStatusRestricted) {
        [self notifyLocationPermissionStatus:NO];
    }
}

// 上传图片方法 - 完整实现
- (void)uploadImage:(UIImage *)image success:(void (^)(NSString *))successBlock {
    NSData *imageData = UIImagePNGRepresentation(image);
    NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
    [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":@"USER_IMG",@"param":deviceId} fullFileName:[NSString stringWithFormat:@"%@.png",[NSString jk_UUID]] fileData:imageData Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
            if (url && url.length > 0) {
                successBlock(url);
            }
        } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {
            [MBProgressHUD showToastWithMessageDarkColor:errMsg];
    }];
}

/*
 #pragma mark - Navigation
 // In a storyboard-based application, you will often want to do a little preparation before navigation
 - (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
 // Get the new view controller using [segue destinationViewController].
 // Pass the selected object to the new view controller.
 }
 */

// 标准化WebView框架设置
- (void)setupWebViewFrame {
  [self.view setFrame:UIScreen.mainScreen.bounds];
  
  //屏蔽webview全屏代码,如要使用全屏,注释此代码,放开下面注释的代码
  float stateViewheight = iPhoneX?44:20;
  [self.webView setFrame:CGRectMake(0, stateViewheight, kScreenWidth, kScreenHeight - stateViewheight - (iPhoneX?34:0))];

//  if(self.tabBarController.tabBar.isHidden){
//    self.webView.frame = self.view.bounds;
//  }else{
//    [self.webView setFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight - self.tabBarController.tabBar.height)];
//  }
    [self.view bringSubviewToFront:self.progressView];
}

@end

