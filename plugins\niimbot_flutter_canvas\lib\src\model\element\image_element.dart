import 'dart:io';
import 'dart:math';

import 'package:flutter/widgets.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/json_element.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_manager.dart';
import 'package:tuple/tuple.dart';

const String _fakeMaterialId = "fakeMaterial";

class ImageElement extends JsonElement {
  /// 解析 imageProcessingValue 字段，兼容字符串和数组类型
  static List<int> _parseImageProcessingValue(dynamic value) {
    if (value == null) {
      return [];
    }

    if (value is String) {
      // 当为字符串时，尝试解析为数字列表
      if (value.isEmpty) {
        return [];
      }

      // 移除可能的方括号和空格
      String cleanValue = value.trim();
      if (cleanValue.startsWith('[') && cleanValue.endsWith(']')) {
        cleanValue = cleanValue.substring(1, cleanValue.length - 1);
      }

      // 如果清理后为空，返回空数组
      if (cleanValue.isEmpty) {
        return [];
      }

      // 尝试按逗号分割字符串
      try {
        List<String> parts = cleanValue.split(',');
        List<int> result = [];
        for (String part in parts) {
          String trimmedPart = part.trim();
          if (trimmedPart.isNotEmpty) {
            result.add(int.parse(trimmedPart));
          }
        }
        return result;
      } catch (e) {
        // 如果解析失败，尝试将整个字符串作为单个数字
        try {
          return [int.parse(cleanValue)];
        } catch (e) {
          // 如果都失败了，返回空数组
          return [];
        }
      }
    }

    if (value is List) {
      return value.cast<int>();
    }

    if (value is int) {
      return [value];
    }

    // 其他类型返回空数组
    return [];
  }

  String? imageData;
  int imageProcessingType;
  List<int> imageProcessingValue;
  bool allowFreeZoom;

  ///图片本地保存路径
  String localUrl;

  ///图片保存在服务器的路径（素材已经保存在服务器不用重复上传服务器，本地图片需要上传oss服务器）
  String imageUrl;

  ///素材库图片的id，不是素材库图片时为空或"0"
  String? materialId;

  //素材id有值的情况下类型：图标=1，边框=2; 素材id 为空的情况下图片=1,涂鸦=2, 类型为2的能自由拉伸
  String? materialType;

  /// 是否点9图, 点9图作为图片下的特殊类型
  bool? isNinePatch;

  /// 点9图沙盒地址
  String? ninePatchLocalUrl;

  /// 点9图服务器地址
  String? ninePatchUrl;

  /// 点9图展示最小尺寸，业务属性，不必存储
  Rect? ninePatchMinSize;

  /// 点9图首次展示的四周放大比例, 逻辑参数，主要处理复制元素、镜像元素导致的比例错误，业务属性，不必存储
  Tuple2<double, double>? ninePatchInitialCornerScale;

  ImageElement(
      {required super.id,
      required super.x,
      required super.y,
      required super.width,
      required super.height,
      super.zIndex,
      super.rotate,
      super.isLock,
      super.mirrorId,
      super.isOpenMirror,
      super.mirrorType,
      super.paperColorIndex,
      super.hasVipRes,
      super.colorReverse,
      super.colorChannel,
      super.elementColor,
      this.imageData = '',
      required this.imageProcessingType,
      required this.imageProcessingValue,
      this.allowFreeZoom = false,
      required this.localUrl,
      this.imageUrl = '',
      this.materialId,
      this.materialType,
      this.isNinePatch,
      this.ninePatchUrl,
      this.ninePatchLocalUrl})
      : super(type: ElementItemType.image, value: null);

  ImageElement.fromJson(Map<String, dynamic> json)
      : imageData = json['imageData'] ?? '',
        imageProcessingType = json['imageProcessingType'],
        imageProcessingValue = _parseImageProcessingValue(json['imageProcessingValue']),
        allowFreeZoom = json['allowFreeZoom'] ?? false,
        localUrl = json["localUrl"] ?? json["localImageUrl"] ?? '',
        imageUrl = json["imageUrl"] ?? "",
        materialId = json["materialId"] == null
            ? ""
            : ((json["materialId"] is int) ? json["materialId"].toString() : json["materialId"]),
        materialType = json["materialType"] != null ? json["materialType"].toString() : "",
        isNinePatch = json["isNinePatch"] is bool ? json["isNinePatch"] ?? false : json["isNinePatch"] == 1,
        ninePatchLocalUrl = json["ninePatchLocalUrl"],
        ninePatchUrl = json["ninePatchUrl"],
        ninePatchMinSize = json["ninePatchMinSize"] is List && (json["ninePatchMinSize"] as List).length == 2
            ? Rect.fromLTWH(0, 0, json["ninePatchMinSize"][0], json["ninePatchMinSize"][1])
            : null,
        super.fromJson(json);

  ///构造假素材模型
  factory ImageElement.createFakeMaterial(String materialType) {
    return ImageElement(
      id: JsonElement.generateId(),
      width: 0,
      height: 0,
      x: 0,
      y: 0,
      imageProcessingType: 1,
      imageProcessingValue: [127],
      localUrl: '',
      materialId: _fakeMaterialId,
      materialType: materialType,
    );
  }

  // @override
  // Map<String, dynamic> antiEscapeValueToJson() {
  //   Map<String, dynamic> data = toJson();
  //   if (type == ElementItemType.image) {
  //     // 当保存的时候，imageData置空
  //     data['imageData'] = '';
  //   }
  //   return data;
  // }

  @override
  Map<String, dynamic> toJson({isResetState = false}) {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data.addAll(super.toJson());
    data['imageData'] = "";
    data['imageProcessingType'] = this.imageProcessingType;
    data['imageProcessingValue'] = this.imageProcessingValue;
    data['allowFreeZoom'] = this.allowFreeZoom ?? false;
    data['localUrl'] = this.localUrl;
    data['imageUrl'] = this.imageUrl;
    data['materialId'] = this.materialId;
    data['materialType'] = this.materialType;
    data['localImageUrl'] = this.localUrl;
    data['isNinePatch'] = this.isNinePatch;
    data['ninePatchLocalUrl'] = this.ninePatchLocalUrl;
    data['ninePatchUrl'] = this.ninePatchUrl;
    data['ninePatchMinSize'] = ninePatchMinSize == null ? [] : [ninePatchMinSize?.width, ninePatchMinSize?.height];
    return data;
  }

  /// 复制一份，默认重新生成 id
  @override
  JsonElement clone({
    bool isTableCell = false,
    bool keepId = false,
    bool isMirror = false,
    TemplateData? templateData,
  }) {
    var jsonElement = super.clone(isTableCell: isTableCell, keepId: keepId, templateData: templateData);
    // 点9图复制需要copy当前element的初始四周放大比例
    if ((jsonElement as ImageElement).isNinePatch ?? false) {
      jsonElement.ninePatchInitialCornerScale = this.ninePatchInitialCornerScale;
      // localUrl需要根据ID复制新的一份，注意isMirror代表镜像元素无需重写
      if (!isMirror) {
        File file = File(localUrl);
        String cloneLocalUrl = localUrl.replaceAll(id, jsonElement.id);
        // 如果路径一致则不copy
        if (cloneLocalUrl != localUrl) {
          file.copySync(cloneLocalUrl);
          jsonElement.localUrl = cloneLocalUrl;
        }
      }
    }
    return jsonElement;
  }

  @override
  bool hasVipSource() {
    bool isBoder = isMaterialBoder();
    MaterialCacheData? cacheData = isBoder
        ? MaterialManager.sharedInstance().materialBoderCacheData
        : MaterialManager.sharedInstance().materialCacheData;
    if ((materialId?.isNotEmpty ?? false) && (cacheData?.vipIdList.any((id) => id.toString() == materialId) ?? false)) {
      return true;
    }
    return false;
  }

  /// 0-图片，1-真素材，2-假素材
  int getImageType() {
    if ((materialId?.isEmpty ?? true) || materialId == "0") {
      return 0;
    }
    if (materialId != _fakeMaterialId) {
      return 1;
    }
    return 2;
  }

  bool isMaterial() {
    if ((materialId?.isEmpty ?? true) || materialId == "0") {
      return false;
    }
    return true;
  }

  bool isMaterialIcon() {
    if (materialId == null || (materialId ?? "").isEmpty || materialId == "0") {
      return false;
    }
    return materialType == "1";
  }

  /// 重置图片风格
  void resetStyle() {
    imageProcessingType = 3;
    imageProcessingValue = [imageProcessingValue[0], 150];
  }

  /// 是否16阶灰色风格
  bool is16grayStyle() {
    return imageProcessingType == 2;
  }

  @override
  double getItemDisplayMinWidth(BuildContext context) {
    var minWidth = super.getItemDisplayMinWidth(context);
    if (isNinePatch ?? false) {
      // 点9图拥有最小的宽高
      return max(ninePatchMinSize?.width ?? minWidth, minWidth);
    }
    return minWidth;
  }

  @override
  double getItemDisplayMinHeight() {
    var minHeight = super.getItemDisplayMinHeight();
    if (isNinePatch ?? false) {
      // 点9图拥有最小的宽高
      return max(ninePatchMinSize?.height ?? minHeight, minHeight);
    }
    return minHeight;
  }
}
