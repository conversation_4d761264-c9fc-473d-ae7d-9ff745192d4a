package com.niimbot.appframework_library.common.module.template

import android.text.TextUtils
import com.blankj.utilcode.util.ImageUtils
import com.niimbot.appframework_library.common.module.template.external.DataSource
import com.niimbot.appframework_library.common.module.template.external.ExcelPageInfo
import com.niimbot.appframework_library.common.module.template.external.ExternalData
import com.niimbot.appframework_library.common.module.template.external.ModifyElement
import com.niimbot.appframework_library.common.module.template.external.Task
import com.niimbot.appframework_library.common.module.template.goods.CommodityInfo
import com.niimbot.appframework_library.common.module.template.item.BaseItemModuleEx
import com.niimbot.appframework_library.common.module.template.item.FlowItemModule
import com.niimbot.appframework_library.common.module.template.item.ItemType
import com.niimbot.appframework_library.common.module.template.item.LineItemModule
import com.niimbot.appframework_library.common.module.template.item.PictureItemModule
import com.niimbot.appframework_library.common.module.template.item.RectItemModule
import com.niimbot.appframework_library.common.module.template.item.TextItemModule
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.module.template.item.barcode.BarcodeItemModule
import com.niimbot.appframework_library.common.module.template.item.qrcode.QrCodeItemModule
import com.niimbot.appframework_library.common.module.template.item.table.TableItemModule
import com.niimbot.appframework_library.common.module.template.profile.Profile
import com.niimbot.appframework_library.common.util.ExcelTransformUtil
import com.niimbot.appframework_library.common.util.TemplateUtils
import com.niimbot.appframework_library.utils.StringUtils
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.appframework_library.utils.font.FontUtils.getDefaultFontKey
import com.niimbot.fastjson.JSON
import com.niimbot.fastjson.JSONArray
import com.niimbot.fastjson.JSONObject
import com.niimbot.fastjson.annotation.JSONField
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.json2Any
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.LanguageUtil
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable
import java.util.UUID

class TemplateModuleLocal : LocalToRemote<TemplateModuleLocal, TemplateModuleR>, Serializable {
    companion object {
        fun fromJson(json: String): TemplateModuleLocal? {
            if (json.isEmpty()) {
                return null
            }

            val templateModuleLocal = json2Any(json, TemplateModuleLocal::class.java)
            val templateJsonObject = JSONObject.parseObject(json) ?: return templateModuleLocal
            val jsonArray =
                templateJsonObject["elements"] as? JSONArray
                    ?: return templateModuleLocal
            val itemModules = arrayListOf<BaseItemModuleEx>()
            for (i in jsonArray.indices) {
                val elementJsonObject = jsonArray.getJSONObject(i)
                val elementType = elementJsonObject.getString("type")
                val itemModule = when (ItemType.getItemType(elementType)) {
                    ItemType.TEXT -> {
                        json2Any(elementJsonObject.toJSONString(), TextItemModule::class.java)
                    }
                    ItemType.BARCODE -> {
                        json2Any(elementJsonObject.toJSONString(), BarcodeItemModule::class.java)
                    }
                    ItemType.QR_CODE -> {
                        json2Any(elementJsonObject.toJSONString(), QrCodeItemModule::class.java)
                    }
                    ItemType.TABLE -> {
                        json2Any(elementJsonObject.toJSONString(), TableItemModule::class.java)
                    }
                    ItemType.PICTURE -> {
                        json2Any(elementJsonObject.toJSONString(), PictureItemModule::class.java)
                    }
                    ItemType.FLOW -> {
                        json2Any(elementJsonObject.toJSONString(), FlowItemModule::class.java)
                    }
                    ItemType.TIME -> {
                        json2Any(elementJsonObject.toJSONString(), TimeItemModule::class.java)
                    }
                    ItemType.SHAPE -> {
                        json2Any(elementJsonObject.toJSONString(), RectItemModule::class.java)
                    }
                    ItemType.LINE -> {
                        json2Any(elementJsonObject.toJSONString(), LineItemModule::class.java)
                    }
                    else -> null
                }
                itemModule?.apply {
                    itemModules.add(itemModule)
                }
            }
            templateModuleLocal?.updateAllElements(itemModules)
            return templateModuleLocal
        }

        fun convert(t: TemplateModuleLocal): TemplateModuleR {
            return TemplateModuleLocal().localToRemote(t)
        }

        fun convert(t: List<TemplateModuleLocal>): List<TemplateModuleR> {
            return TemplateModuleLocal().localToRemoteList(t)
        }
    }

    //模板id
    var id: String = ""

    //云模板ID
    var cloudTemplateId = ""

    //模板名称
    var name: String = ""

    //标签多语言名称集合
    var names = arrayListOf<LabelNameInfo>()

    //模板描述
    var description: String = ""

    //缩略图url
    var thumbnail: String = ""
    //线缆数据
    var layoutSchema:String=""
    var supportedEditors: ArrayList<String> = arrayListOf()
    var inputAreas: Any? = null
    //背景图url
    var backgroundImage: String = ""
    var localBackgroundImageUrl: String = ""

    //多背景图时的选择索引
    var multipleBackIndex: Int = 0
    var previewImage: String = ""

    //模板宽度
    var width: Float = 0f

    //模板高度
    var height: Float = 0f

    //出纸方向
    var rotate: Int = 0

    //耗材类型，1-标签类，2-标牌类
    var consumableType: Int = 1

    var consumableTypeTextId: String? = ""

    //纸张类型， 1-间隙纸，2-黑标纸，3-连续纸，4-定孔纸，5-透明纸
    var paperType: Int = 1

    //是否是线缆标签，0-非线缆标签，1-线缆标签
    @JSONField(name = "isCable")
    var isCable: Boolean = false

    //尾巴长度（耗材类型为标签类，并且为线缆标签时处理该数据）
    var cableLength: Float = 0f

    //尾巴方向，0-上，1-右，2-下，3-左
    var cableDirection: Int = 0

    //模板上右下左边距
    var margin = arrayListOf(0f, 0f, 0f, 0f)

    /*模板使用到的字体集合，格式如下：
      {
          "宋体": "FONT_1.TTF",
          "黑体": "FONT_2.TTF",
          "楷体": "FONT_3.TTF",
          "默认字体": "FONT_4.TTF"
       }*/
    var usedFonts = mutableMapOf<String, String>().apply {
        put(
            getDefaultFontKey(),
            FontUtils.getDefaultFontFileName()
        )
    }

    //模板的元素集合
    var elements = arrayListOf<BaseItemModuleEx>()
    var profile = Profile()
    var labelId: String = ""

    //元素导入Excel的外部数据
    var externalData = ExternalData()

    //针对导入Excel修改的数据
    var task = Task()

    //    var dataSources = TemplateDataSources()
//    var dataSourceModifies = TemplateModify()
//    var dataSourceBindInfo = ExcelPageInfo()
    var dataSource: ArrayList<DataSource>? = arrayListOf<DataSource>()
    var modify: MutableMap<String, MutableMap<String, ModifyElement>>? =
        mutableMapOf<String, MutableMap<String, ModifyElement>>()
    var bindInfo: ExcelPageInfo? = ExcelPageInfo()
    var templateVersion: String? = null

    //导入Excel切换的当前页码
    var currentPage: Int = 1

    //导入Excel的总页码
    var totalPage: Int = 1

    var sql_id: Long? = null
    var local_thumb = ""
    var local_cloud_id = ""

    @JSONField(name = "is_need_move")
    var is_need_move = false
    var local_type = 0

    @JSONField(name = "is_move")
    var is_move = 0
    var local_private = 0
    var excel_id = ""

    @JSONField(name = "is_com")
    var is_com = 0

    //当前选择的背景
    var show_background = ""

    @JSONField(name = "isCheck")
    var isCheck = false
    var background = ""
    var localBackground = arrayListOf<String>()

    //是否来自老版本迁移的数据
    var fromOldVersion: Int = 0

    var templatePrintMode: Int = -1 //模板或者标签携带的打印模式属性

    //打印历史
    var printedProcessList: List<PrintHistoryInfo> = arrayListOf()

    /**模板来源，创建和更新时由服务端负责更新，本地只接收不做修改*/
    var platformCode = ""

    /**
     * 模板精度
     * 默认打印机点数为203
     */
    var paccuracyName = 203

    /**是否包含VIP资源*/
    var hasVIPRes = false

    /**是否VIP模板*/
    var vip = false

    /**是否商品模板*/
    var commodityTemplate = false


    /**
     * 2022/1/10 Ice_Liu 新增模板版本号
     * 用来表示模板版本，方便后端定向刷数据和前端版本控制
     * 老图像库版本统一为"2.0.4"
     * 新图像库版本从"3.0.0" 开始，后期涉及到数据变动变更时+1
     * 此版本号pc，移动端和臣小印统一，所以涉及到更改时可能造成混乱，需多沟通！！！
     */
    var version = "3.0.0"

    /**
     * 画板旋转需求新增字段
     * 标明画板是否经过旋转，如果旋转过，需要手动修改画板背景和预览背景显示方向
     */
    var canvasRotate = 0


    /**原模板的id:另存为或从云模板创建*/
    var originTemplateId: String = ""

    /**商品库模板中的商品库信息*/
    var commodityInfo: CommodityInfo? = null

    /**市场运营需求字段，标明是否用户模板可转化为云模板*/
    @JSONField(name = "isEdited")
    var isEdited = 0

    /**模板前景图片*/
    var contentThumbnail: String = ""
    var localContentThumb: String = ""

    var paperColor = arrayOf("0.0.0", "230.0.18")

    //新增flutter对应字段
    var labelNames = arrayListOf<LabelNameInfo>()

    fun getExcelHash(): String{
        if(dataSource != null && dataSource!!.isNotEmpty() && dataSource!![0].type == "excel"){
            return dataSource!![0].hash
        }
        return ""
    }

    fun isExcelTemplate(): Boolean{
        if(TextUtils.isEmpty(templateVersion) && externalData!= null && externalData.list != null && externalData.list.isNotEmpty()){
            return true
        }
        return dataSource != null && dataSource!!.isNotEmpty() && dataSource!![0].type == "excel"
    }

    fun isGoodTemplate(): Boolean{
        if(isGoodsTemplate()){
            return true
        }
        return dataSource != null && dataSource!!.isNotEmpty() && dataSource!![0].type == "commodity"
    }

    fun copy(): TemplateModuleLocal? {
        try {
            // 序列化
            val bos = ByteArrayOutputStream()
            val oos = ObjectOutputStream(bos)
            oos.writeObject(this)
            // 反序列化
            val bis = ByteArrayInputStream(bos.toByteArray())
            val ois = ObjectInputStream(bis)

            return ois.readObject() as? TemplateModuleLocal
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return null
    }

    fun updateLabelId() {
        this.labelId = this.profile.extrain.labelId
    }

    fun updateNames(names: List<LabelNameInfo>) {
        this.names.clear()
        this.names.addAll(names)
    }

    fun updateLabelNames(labelNames: List<LabelNameInfo>) {
        this.labelNames.clear()
        this.labelNames.addAll(labelNames)
    }

    fun updateAllElements(elements: ArrayList<BaseItemModuleEx>) {
        this.elements.clear()
        this.elements.addAll(elements)
    }

    fun updateMargin(margin: ArrayList<Float>) {
        this.margin.clear()
        this.margin.addAll(margin)
    }

    fun updateUsedFonts(usedFonts: MutableMap<String, String>) {
        this.usedFonts.clear()
        this.usedFonts.putAll(usedFonts)
    }

    fun updateExcelInfo(externalData: ExternalData, task: Task, currentPage: Int, totalPage: Int) {
        this.externalData = externalData
        this.task = task
        this.currentPage = currentPage
        this.totalPage = totalPage
    }

    fun isEdit(compareTemplate: TemplateModuleLocal): Boolean {
        if (compareTemplate.id != id) {
            return true
        }

        if (compareTemplate.name != name) {
            return true
        }

        if (compareTemplate.width != width) {
            return true
        }

        if (compareTemplate.height != height) {
            return true
        }

        if (compareTemplate.rotate != rotate) {
            return true
        }

        if (compareTemplate.profile.machineName != profile.machineName) {
            return true
        }

        if (compareTemplate.paperType != paperType) {
            return true
        }

        if (compareTemplate.isCable != isCable) {
            return true
        }

        if (compareTemplate.cableDirection != cableDirection) {
            return true
        }

        if (compareTemplate.cableLength != cableLength) {
            return true
        }

        if (compareTemplate.externalData.id != externalData.id) {
            return true
        }

        if (compareTemplate.task.modifyData.size != task.modifyData.size) {
            return true
        }

        for ((elementId, _) in task.modifyData) {
            if (task.modifyData[elementId] != compareTemplate.task.modifyData[elementId]) {
                return true
            }
        }

        if (compareTemplate.elements.size != elements.size) {
            return true
        }

        elements.forEach { item ->
            val compareElements =
                compareTemplate.elements.filter { it.id == item.id && it.type == item.type }
            var hasSame = false
            compareElements.forEach {
                if (!item.isEdit(it)) {
                    hasSame = true
                    return@forEach
                }
            }
            if (!hasSame) {
                logI("TemplateModuleLocal", "edit elementId = ${item.id}")
                return true
            }
        }

        return false
    }
    fun isAllTextElement(): Boolean{
        if(isExcelTemplate() || isGoodTemplate()){
            return false
        }
        return elements.all { it.type == "text" }
    }

    fun isOldPCExcelTemplate(): Boolean {
        return (!TextUtils.isEmpty(platformCode)
            && "CP001PC".equals(platformCode, ignoreCase = true)
            && dataSource.isNullOrEmpty()
            && TextUtils.isEmpty(templateVersion))&& externalData.list.isNotEmpty()
    }



    fun checkNotDownloadFontCodes(): ArrayList<String> {
        val fontCodes = arrayListOf<String>()
        elements.filterIsInstance<TextItemModule>().forEach {
            if (!it.fontCode.isNullOrEmpty() && !FontUtils.isDefaultFont(it.fontCode) && !FontUtils.isFontDownload(
                    it.fontCode
                )
            ) {
                if (!fontCodes.contains(it.fontCode)) {
                    fontCodes.add(it.fontCode)
                }
            }
        }
        elements.filterIsInstance<TableItemModule>().forEach { table ->
            table.cells.forEach { cell ->
                if (!cell.fontCode.isNullOrEmpty() && !FontUtils.isDefaultFont(cell.fontCode) && !FontUtils.isFontDownload(
                        cell.fontCode
                    )
                ) {
                    if (!fontCodes.contains(cell.fontCode)) {
                        fontCodes.add(cell.fontCode)
                    }
                }
            }
            table.combineCells.forEach { combineCell ->
                if (!combineCell.fontCode.isNullOrEmpty() && !FontUtils.isDefaultFont(combineCell.fontCode) && !FontUtils.isFontDownload(
                        combineCell.fontCode
                    )
                ) {
                    if (!fontCodes.contains(combineCell.fontCode)) {
                        fontCodes.add(combineCell.fontCode)
                    }
                }
            }
        }
        return fontCodes
    }


    override fun localToRemote(t: TemplateModuleLocal): TemplateModuleR {
        val templateModuleR = TemplateModuleR()
        templateModuleR.id = t.id
        templateModuleR.sourceOfIndustryTemplateId =
            if (t.cloudTemplateId == null) "" else t.cloudTemplateId
        templateModuleR.name = t.name
        templateModuleR.names = t.names
        templateModuleR.description = t.description
        templateModuleR.thumbnail = t.thumbnail
        templateModuleR.backgroundImage = t.backgroundImage
        templateModuleR.multipleBackIndex = t.multipleBackIndex
        templateModuleR.previewImage = t.previewImage
        templateModuleR.width = t.width
        templateModuleR.height = t.height
        templateModuleR.rotate = t.rotate
        templateModuleR.consumableType = t.consumableType
        templateModuleR.consumableTypeTextId = t.consumableTypeTextId
        templateModuleR.paperType = t.paperType
        templateModuleR.isCable = t.isCable
        templateModuleR.cableLength = t.cableLength
        templateModuleR.cableDirection = t.cableDirection
        templateModuleR.profile = t.profile.copy()!!
        templateModuleR.printMethodCode = t.templatePrintMode
        templateModuleR.printedProcessList = t.printedProcessList
        templateModuleR.show_background = t.show_background
        templateModuleR.updateMargin(t.margin)
        templateModuleR.updateUsedFonts(t.usedFonts)
        templateModuleR.updateAllElements(t.elements)
        templateModuleR.updateExcelInfo(t.externalData, t.task, t.currentPage, t.totalPage)

        templateModuleR.dataSources = t.dataSource
        templateModuleR.dataSourceModifies = t.modify
        templateModuleR.dataSourceBindInfo = t.bindInfo
        templateModuleR.templateVersion = t.templateVersion
        templateModuleR.fromOldVersion = t.fromOldVersion
        templateModuleR.paccuracyName = t.paccuracyName
        templateModuleR.hasVipRes = if (t.hasVIPRes == null) false else t.hasVIPRes
        templateModuleR.vip = if (t.vip == null) false else t.vip
        templateModuleR.commodityTemplate = t.commodityTemplate
        templateModuleR.version = t.version
        templateModuleR.canvasRotate = t.canvasRotate
        templateModuleR.originTemplateId = t.originTemplateId
        templateModuleR.commodityInfo = t.commodityInfo
        templateModuleR.paperColor = t.paperColor
        templateModuleR.labelNames = t.labelNames
        templateModuleR.isEdited = t.isEdited
        templateModuleR.contentThumbnail = t.contentThumbnail

        val copyTemplateModuleR = templateModuleR.copy()
        copyTemplateModuleR!!.elements.forEach {

            when (ItemType.getItemType(it.type)) {
                ItemType.TEXT -> {
                    val item = it as TextItemModule
                    item.value = oldImportValueToNewValue(item.value, item.dataBind)
                }
                ItemType.BARCODE -> {
                    val item = it as BarcodeItemModule
                    item.value = oldImportValueToNewValue(item.value, item.dataBind)
                }
                ItemType.QR_CODE -> {
                    val item = it as QrCodeItemModule
                    item.value = oldImportValueToNewValue(item.value, item.dataBind)
                }
                ItemType.TABLE -> {
                    val item = it as TableItemModule
                    item.cells.forEach { cell ->
                        cell.value = oldImportValueToNewValue(cell.value, cell.dataBind)
                    }
                    item.combineCells.forEach { cell ->
                        cell.value = oldImportValueToNewValue(cell.value, cell.dataBind)
                    }

                }
                else             -> {}
            }


        }

        return copyTemplateModuleR
    }

    fun oldImportValueToNewValue(oldValue: String, dataBind: java.util.ArrayList<String>?): String {
        if (dataBind != null && dataBind.isNotEmpty() && oldValue.contains("⊙")) {
            //将value从眼睛转换为A1
            val splitArray = oldValue.removeSurrounding("${'$'}{", "}").split("⊙")
            if (splitArray.isEmpty() || splitArray.size < 2) {
                return oldValue;
            }
            val column = ExcelTransformUtil.index2Letter(splitArray[1].toInt() + 1)
            return "$column${splitArray[0]}"
        } else {
            return oldValue
        }
    }

    override fun localToRemoteList(t: List<TemplateModuleLocal>): List<TemplateModuleR> {
        val templateModuleRList = arrayListOf<TemplateModuleR>()
        t.forEach {
            templateModuleRList.add(localToRemote(it))
        }
        return templateModuleRList
    }

    /**
     * 输出大小格式
     */
    fun getPrintSize(prefix: String = "x", suffix: String = "mm"): String {
        return StringUtils.getPrintSize(width, height, prefix, suffix)
    }

    fun getPaccuracy(): Float {
        return TemplateUtils.getAccuracyByPaccuracyName(paccuracyName)
    }

    /**
     * 是否云模板
     */
    fun isCloudTemplate(): Boolean {
        val templateType = profile.extrain.templateType
        return templateType == 1
    }

    /**
     * 是否云模板/云商品模板
     */
    fun isFromIndustry(): Boolean {
        return (profile.extrain.templateType == 2 && profile.extrain.userId?.isNullOrEmpty() == true) || isCloudTemplate()
    }

    /**
     * 是否能在设置界面修改
     */
    fun canChangeSize() = isCloudTemplate() || !profile.extrain.sourceId.isNullOrBlank()

    /**
     * 当前模板是否匹配RFID模板
     * @param barCode String
     * @return Boolean
     */
    fun isMatchRFIDTemplate(barCode: String) =
        (profile?.barcode == barCode
                || profile?.extrain?.sparedCode == barCode
                || profile?.extrain?.virtualBarCode == barCode
                || profile?.extrain?.amazonCodeBeijing == barCode
                || profile?.extrain?.amazonCodeWuhan == barCode
                || profile?.extrain?.barcodeCategoryMap?.values?.contains(barCode) == true)

    /**
     * 添加一个默认的居中显示的文本(模板高小于20是默认小五；高大于等于20默认是四号的文本元素)
     */
    fun addDefaultTextItem() {
        // 模板宽度不足小五号字显示换行，导致退出时对比判定错误
        val singleLineWidthWith5 = 24.0f
        val singleLineHeightWith5 = 26.25f
        val singleLineWidthWith4 = 37.84f
        var tWidth = <EMAIL>
        val templateHeight = <EMAIL>
        var tFontSize = 3.2f
        var tHeight = 3.75f
        var tX = 0.0f
        var tY = (<EMAIL> - 3.75f) / 2
        /// 宽度大于4号字体默认文本宽度时，字号为小4号字
        if (width > singleLineWidthWith4) {
            tFontSize = 4.2f
            tHeight = 5.76f
            tY = (templateHeight - 5.76f) / 2
        } else if (<EMAIL> < singleLineWidthWith5) {
            if (<EMAIL> > singleLineWidthWith5) {
                tWidth = 3.75f
                tHeight = singleLineHeightWith5
                tX = (<EMAIL> - tWidth) / 2
                tY = (<EMAIL> - singleLineHeightWith5) / 2
            } else {
                tWidth = 17.47f
                tHeight = 2.68f
                tX =
                    if (<EMAIL> - tWidth > 0) (<EMAIL> - tWidth) / 2 else 0f
                tY = (<EMAIL> - tHeight) / 2
                tFontSize = 2.3f
            }
        }
        elements.add(TextItemModule().apply {
            id = UUID.randomUUID().toString()
            value = LanguageUtil.findLanguageString("app00364", SuperUtils.superContext)
            width = tWidth
            height = tHeight
            x = tX
            y = tY
            type = ItemType.TEXT.type
            fontSize = tFontSize
            centerInCanvas = true
            textAlignHorizonral = 1
            //6.0.4版本改为顶对齐
            textAlignVertical = 0
            boxStyle = "auto-width"
            lineBreakMode = 1
        })

        if (!usedFonts.contains(getDefaultFontKey())) {
            usedFonts[getDefaultFontKey()] = FontUtils.getDefaultFontFileName()
        }
    }

    var x = 0.0f
    var y = 0.0f
    fun addElementTextItem(index: Int, key: String) {
        // 模板宽度不足小五号字显示换行，导致退出时对比判定错误
        val singleLineWidthWith5 = 24.0f
        val singleLineHeightWith5 = 26.25f
        val singleLineWidthWith4 = 37.84f
        var tWidth = <EMAIL>
        var templateHeight = <EMAIL>
        var tFontSize = 3.2f
        var tHeight = 3.75f
        val tX = x
        var tY = y
        /// 宽度大于4号字体默认文本宽度时，字号为4号字
        if (width > singleLineWidthWith4) {
            tFontSize = 4.9f
            tHeight = 5.76f
        } else if (<EMAIL> < singleLineWidthWith5) {
            if (<EMAIL> > singleLineWidthWith5) {
                tWidth = 3.75f
                tHeight = singleLineHeightWith5
            } else {
                tWidth = 17.47f
                tHeight = 2.68f
                tFontSize = 2.3f
            }
        }
        var tValue = ""

        when (key) {
            "name" -> {
                tValue = "B0"
            }
            "norm" -> {
                tValue = "E0"
            }
            "originPlace" -> {
                tValue = "C0"
            }
            "retailPrice" -> {
                tValue = "G0"
            }
            else -> {
            }
        }


        tY = tY + tHeight * index
        elements.add(TextItemModule().apply {
            id = UUID.randomUUID().toString()
            width = tWidth
            height = tHeight
            x = tX
            y = tY
            value = tValue
            type = ItemType.TEXT.type
            fontSize = tFontSize
            centerInCanvas = false
            textAlignHorizonral = 0
            dataBind = arrayListOf("", "")
        })

        if (!usedFonts.contains(getDefaultFontKey())) {
            usedFonts[getDefaultFontKey()] = FontUtils.getDefaultFontFileName()
        }
    }

    fun updateResourceVersion() {
        hasVIPRes = hasVipElement()
        if (elements.firstOrNull { it is BarcodeItemModule && it.fontSize > 25.4f } != null) {
            profile.extrain.resourceVersion = "5.9.0"
        } else if (elements.firstOrNull { it is TextItemModule && it.fontSize > 25.4f } != null) {
            profile.extrain.resourceVersion = "5.9.0"
        } else if (elements.firstOrNull { it is TableItemModule && it.cells != null && it.cells.isNotEmpty() && it.cells.firstOrNull { it -> it.fontSize > 25.4f } != null } != null) {
            profile.extrain.resourceVersion = "5.9.0"
        } else if (elements.firstOrNull { it is QrCodeItemModule && it.isForm } != null) {
            profile.extrain.resourceVersion = "5.8.3"
        } else if (elements.firstOrNull { it is TextItemModule && it.typesettingMode == 2 } != null) {
            profile.extrain.resourceVersion = "5.7.4"
        } else if (elements.firstOrNull { it is TextItemModule && it.typesettingMode == 3 } != null) {
            profile.extrain.resourceVersion = "5.3.5"
        } else if (templateVersion != null && !templateVersion!!.isEmpty() && dataSource != null && !dataSource!!.isEmpty()) {
            profile.extrain.resourceVersion = "5.10.0"
        } else {
            profile.extrain.resourceVersion =
                if (elements.firstOrNull { it is PictureItemModule && it.hasVipRes } != null || canvasRotate > 0 || vip) "5.3.0" else ""
        }
    }

    fun hasVipElement() = elements.any { it.hasVipRes }

    fun hasPaperColorElement() = elements.any {
        if (it is TableItemModule) {
            (null != it.contentColor && !it.contentColor.contentEquals(
                intArrayOf(
                    255,
                    0,
                    0,
                    0
                )
            )) || (null != it.lineColor && !it.lineColor.contentEquals(
                intArrayOf(
                    255,
                    0,
                    0,
                    0
                )
            ))
        } else {
            null != it.elementColor && !it.elementColor.contentEquals(
                intArrayOf(
                    255,
                    0,
                    0,
                    0
                )
            )
        }
    }

    fun clearVipFont() = elements.forEach { it.clearVipRes() }

    fun needVip() = hasVipElement() || vip || hasVipFont()||hasVIPRes

    fun hasVipFont(): Boolean {
        return usedFonts.values.firstOrNull { FontUtils.isFontVip(it.split(".")[0]) } != null
    }

    /**
     * 获取模板中活码id集合
     * @return String?
     */
    fun getLiveCodeIds(): String? {
        return elements.filterIsInstance<QrCodeItemModule>()?.filter { it.isLive }
            ?.map { it.liveCodeId }?.toHashSet()?.joinToString(",") { it }
    }

    fun getFormIds(): String? {
        return elements.filterIsInstance<QrCodeItemModule>()?.filter { it.isForm }
            ?.map { it.formId }?.toHashSet()?.joinToString(",") { it }
    }

    /**
     * 是否商品库模板
     * @return Boolean
     */
    fun isGoodsTemplate() = profile?.extrain?.templateType == 2 || commodityTemplate

    fun isExcelXLSTemplate(): Boolean {
        if(dataSource != null && dataSource!!.isNotEmpty() && dataSource!![0].type == "excel") {
            return dataSource!![0].name?.endsWith("xls") == true
        }
        return false
    }

    fun isLabel() = profile?.extrain?.templateClass == 0

    /**单次打印任务所携带的商品集合信息，仅用于打印页面的打印*/
    var goodsListInPrintTask = ArrayList<LinkedHashMap<String, String>>()

    fun parseForCommodity(jsonStr: String) {
        this.commodityInfo = JSON.parseObject(jsonStr, CommodityInfo::class.java)
    }

    fun extractCommodity(): Map<String, String> {
        val goodsInfo = LinkedHashMap<String, String>()
        elements.filterIsInstance<TextItemModule>().filter { it.fieldName.isNotBlank() }.forEach {
            goodsInfo[it.fieldName] = it.value
        }
        elements.filterIsInstance<BarcodeItemModule>().filter { it.fieldName.isNotBlank() }
            ?.forEach {
                goodsInfo[it.fieldName] = it.value
            }
        if (!profile.extrain.goodsCode.isNullOrBlank()) goodsInfo["barcode"] =
            profile.extrain.goodsCode
        if (goodsInfo.isNotEmpty() && commodityInfo == null) initCommodityInfo(goodsInfo)
        return goodsInfo
    }

    fun initCommodityInfo(goodsInfo: Map<String, String>) {
        if (goodsInfo.isNotEmpty() && commodityInfo == null) {
            commodityInfo = CommodityInfo().apply {
                goodsInfo.entries.forEach { this.freshValue(it.key, it.value) }
            }
        }
    }

    fun updateCommodityInfo() {
        commodityInfo?.let { commodity ->
            elements.filterIsInstance<TextItemModule>().filter { it.fieldName.isNotBlank() }
                .forEach {
                    commodity.freshValue(it.fieldName, it.value)
                }
            elements.filterIsInstance<BarcodeItemModule>().filter { it.fieldName.isNotBlank() }
                .forEach {
                    commodity.freshValue(it.fieldName, it.value)
                }
            if (profile.extrain.goodsCode.isNullOrBlank() && !commodity.barcode.isNullOrBlank()) profile.extrain.goodsCode =
                commodity.barcode
            if (commodity.barcode.isNullOrBlank()) commodity.barcode = profile.extrain.goodsCode
        }
//        if (commodityInfo?.name?.isNullOrBlank() == true) commodityInfo = null
    }

    fun getGoodsBarCode(): String {
        var barcode = profile.extrain.goodsCode
        if (barcode.isNullOrEmpty()) {
            barcode =
                elements.filterIsInstance<BarcodeItemModule>().filter { it.fieldName.isNotBlank() }
                    .firstOrNull()?.value ?: ""
        }
        return barcode
    }

    fun hasGoodsItem(): Boolean {
        return elements.filterIsInstance<TextItemModule>().filter { it.fieldName.isNotBlank() }
            .isNullOrEmpty() != true || elements.filterIsInstance<BarcodeItemModule>()
            .filter { it.fieldName.isNotBlank() }.isNullOrEmpty() != true
    }

    fun getBackgroundWidth() = try {
        localBackground.getOrNull(multipleBackIndex)?.let {
            ImageUtils.getSize(it).getOrNull(0)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }

    /**
     * 行业模板进入画板时会混入当前连接的打印机识别到的标签纸
     * 当两者宽高比不一致时，需要旋转标签纸背景适配模板
     */
    fun freshCanvasRotate(originTemplate: TemplateModuleLocal) {
        val templateAspectRatio = originTemplate.width / originTemplate.height
        val labelAspectRatio = width / height
        canvasRotate = 0
        if ((templateAspectRatio > 1 && labelAspectRatio < 1) || (templateAspectRatio < 1 && labelAspectRatio > 1)) {
            canvasRotate += 90
        }
        if (canvasRotate != 0) {
            val tempHeight = height
            height = width
            width = tempHeight
            val newRotate = (rotate + canvasRotate / 90 * 270) % 360
            rotate = newRotate

            // 线缆尾巴方向调整，一旦旋转，按照目前逻辑是右边翻转90度，所以+1
            if (this.cableDirection != -1) {
                this.cableDirection = if (cableDirection + 1 == 4)  0 else cableDirection + 1;
                this.cableDirection = cableDirection % 4;
            }
        }
    }

    fun sortElementForFlutter() {
        val textItems = ArrayList<BaseItemModuleEx>()
        val shapeItems = ArrayList<BaseItemModuleEx>()
        val picItems = ArrayList<BaseItemModuleEx>()
        elements.forEach {
            when (it.type) {
                ItemType.LINE.type, ItemType.SHAPE.type -> {
                    shapeItems.add(it)
                }
                ItemType.PICTURE.type -> {
                    if ((it as PictureItemModule).materialType == "2") {
                        // 2024/1/18 Ice_Liu 边框和形状设为同一层级
                        shapeItems.add(it)
                    } else {
                        picItems.add(it)
                    }
                }
                ItemType.TABLE.type -> {
                    picItems.add(it)
                }
                else -> {
                    textItems.add(it)
                }
            }
        }
        elements.clear()
        elements.addAll(shapeItems)
        elements.addAll(picItems)
        elements.addAll(textItems)
        //使PC兼容移动端的视图层级
        elements.forEachIndexed { index, baseItemModuleEx ->
            baseItemModuleEx.zIndex = index
        }
    }


    fun canEdit(isCheckBackground:Boolean=true): Boolean {
        if (height == 0f || width == 0f) {
            return false
        }
        val filterLocalBackground = localBackground.filter { it.trim().isNotEmpty() }
        if (!backgroundImage.isNullOrEmpty() && backgroundImage.split(",").size != filterLocalBackground.size) {
            return false
        }
        elements.filterIsInstance<PictureItemModule>().forEach {
            if(it.isNinePatch){
                if ((TextUtils.isEmpty(it.ninePatchLocalUrl) || !File(it.ninePatchLocalUrl).exists())) {
                    return false
                }
            }else{
                if ((TextUtils.isEmpty(it.localUrl) || !File(it.localUrl).exists())) {
                    return false
                }
            }

        }
        if(isCheckBackground){
            for ( backgroundPath in filterLocalBackground) {
                if (backgroundPath.isNullOrBlank()) {
                    continue
                }
                if (!File(backgroundPath).exists()) {
                    return false
                }
            }
        }

        return true
    }

    fun updateCloudTemplateId(cloudId: String) {
        if (cloudTemplateId.isNullOrEmpty()) {
            cloudTemplateId = cloudId
        }
    }

    fun completionUsedFonts() {
        if (!usedFonts.contains(getDefaultFontKey())) {
            usedFonts[getDefaultFontKey()] = FontUtils.getDefaultFontFileName()
        }
    }

    /**
     * 只关联了Excel一行数据，或只关联了商品库数据
     */
    fun isBindDataSourceOnlyOneRow(): Boolean {
        val excelSheets = externalData.list
        if (!excelSheets.isNullOrEmpty()) {
            val excelColumns = excelSheets[0].data.columns
            //excel或商品库只有一行
            if (excelColumns.isNotEmpty() && excelColumns[0].size == 1) {
                return true
            }
        }
        val ranges = dataSource?.firstOrNull()?.range
        if (ranges != null && ranges.size == 1) {
            val range = ranges.first()
            //excel只选择了一行
            if (range.s == range.e) {
                return true
            }
        }
        return false
    }

    /**
     *  只有商品库字段没有关联内容
     */
    fun isEmptyGoods(): Boolean {
        if(dataSource?.firstOrNull()?.type == "commodity") {
            val columnData = externalData.list.firstOrNull()?.data?.columns
            if (columnData.isNullOrEmpty() || columnData.all { it.isNullOrEmpty() }) {
                val textItemModules = elements.filterIsInstance<TextItemModule>()
                if (textItemModules.any { it.value.contains("⊙") } == true) {
                    return true
                }
                val barcodeItemModules = elements.filterIsInstance<BarcodeItemModule>()
                if (barcodeItemModules.any { it.value.contains("⊙") } == true) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * 是否关联了数据源
     */
    fun isBindDataSource(): Boolean {
        val columnData = externalData.list.getOrNull(0)?.data?.columns
        if (!columnData.isNullOrEmpty() && columnData.any { !it.isNullOrEmpty() }) {
            return true
        }
        if (!goodsListInPrintTask.isNullOrEmpty()) {
            return true
        }
        if (isEmptyGoods()) {
            return true
        }
        return false
    }

    override fun toString(): String {
        return "TemplateModuleLocal(id='$id', name='$name', names=$names, description='$description', thumbnail='$thumbnail', backgroundImage='$backgroundImage', multipleBackIndex=$multipleBackIndex, previewImage='$previewImage', width=$width, height=$height, rotate=$rotate, consumableType=$consumableType, paperType=$paperType, isCable=$isCable, cableLength=$cableLength, cableDirection=$cableDirection, margin=$margin, usedFonts=$usedFonts, elements=$elements, profile=$profile, labelId='$labelId', externalData=$externalData, task=$task, currentPage=$currentPage, totalPage=$totalPage, sql_id=$sql_id, local_thumb='$local_thumb', local_cloud_id='$local_cloud_id', is_need_move=$is_need_move, local_type=$local_type, is_move=$is_move, local_private=$local_private, excel_id='$excel_id', is_com=$is_com, show_background='$show_background', isCheck=$isCheck, localBackground=$localBackground, fromOldVersion=$fromOldVersion, templatePrintMode=$templatePrintMode, printedProcessList=$printedProcessList, platformCode='$platformCode', paccuracyName=$paccuracyName, hasVIPRes=$hasVIPRes, vip=$vip, commodityTemplate=$commodityTemplate, version='$version', canvasRotate=$canvasRotate, originTemplateId='$originTemplateId', commodityInfo=$commodityInfo, isEdited=$isEdited, paperColor=${paperColor.contentToString()}, goodsListInPrintTask=$goodsListInPrintTask)"
    }
}
