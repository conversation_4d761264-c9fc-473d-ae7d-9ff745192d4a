/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Ứng dụng này sẽ truy cập vào quyền máy ảnh của bạn trong chức năng quét mã vạch, nhận dạng văn bản và chụp ảnh, có cho phép mở máy ảnh không?";
NSBluetoothPeripheralUsageDescription = "Ứng dụng này sẽ truy cập vào quyền Bluetooth của bạn trong dịch vụ kết nối máy in, có cho phép mở Bluetooth không?";
NSBluetoothAlwaysUsageDescription = "Ứng dụng này sẽ truy cập vào quyền Bluetooth của bạn trong dịch vụ kết nối máy in, có cho phép mở Bluetooth không?";
NSContactsUsageDescription = "Ứng dụng này sẽ truy cập vào quyền danh bạ của bạn trong dịch vụ đọc danh bạ, có cho phép mở danh bạ không?";
NSMicrophoneUsageDescription = "Ứng dụng này sẽ truy cập vào quyền microphone của bạn trong dịch vụ nhận dạng giọng nói, có cho phép mở microphone không?";
NSPhotoLibraryUsageDescription = "Quyền này sẽ được sử dụng để in tài liệu hình ảnh, nhận dạng mã vạch, nhận dạng mã QR, nhận dạng văn bản, thiết lập ảnh đại diện tùy chỉnh...Vui lòng \"Cho phép truy cập vào tất cả các ảnh\" để đảm bảo NIIMBOT có thể truy cập vào album ảnh trong in khi in. Nếu bạn sử dụng \"Chọn ảnh...\", thì tất cả các ảnh chưa được chọn và tất cả các ảnh được thêm vào sau này sẽ không thể truy cập trong NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Để thuận tiện cho việc sử dụng mạng Wi-Fi gần bạn, NIIMBOT yêu cầu quyền định vị của bạn";
NSLocationAlwaysUsageDescription = "Để thuận tiện cho việc sử dụng mạng Wi-Fi gần bạn, NIIMBOT yêu cầu quyền định vị của bạn";
NSLocationAlwaysAndWhenInUseUsageDescription = "Để thuận tiện cho việc sử dụng mạng Wi-Fi gần bạn, NIIMBOT yêu cầu quyền định vị của bạn";
NSSpeechRecognitionUsageDescription = "Ứng dụng này cần sự cho phép của bạn để truy cập vào nhận dạng giọng nói, có cho phép mở nhận dạng giọng nói không?";
NSLocalNetworkUsageDescription = "Ứng dụng này cần truy cập ​Mạng cục bộ (LAN)​​ cho các dịch vụ tìm kiếm thiết bị mạng LAN và cấu hình mạng.";
"UILaunchStoryboardName" = "LaunchScreen";
