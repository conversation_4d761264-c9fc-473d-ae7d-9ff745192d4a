/* 
  InfoPlist.strings
  XYFrameWork

  Created by z<PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Това приложение изисква разрешение за камерата, за да изпълнява функциите за сканиране на баркод, разпознаване на текст и заснемане на снимки. Разрешаване на достъпа до камерата?";
NSBluetoothPeripheralUsageDescription = "Това приложение изисква разрешение за Bluetooth, за да може да използва услугите за свързване на принтер. Разрешаване на Bluetooth достъп?";
NSBluetoothAlwaysUsageDescription = "Това приложение изисква разрешение за Bluetooth, за да може да използва услугите за свързване на принтер. Разрешаване на Bluetooth достъп?";
NSContactsUsageDescription = "Това приложение изисква разрешение за контакти, за да чете информация за контакти. Разрешаване на достъпа до контакти?";
NSMicrophoneUsageDescription = "Това приложение изисква разрешение за микрофон, за да използва услуги за разпознаване на глас. Разрешаване на достъпа до микрофона?";
NSPhotoLibraryUsageDescription = "Това разрешение ще се използва за отпечатване на материали с изображения, разпознаване на баркодове, сканиране на QR кодове, разпознаване на текст и персонализирана настройка на аватар. „Разрешете достъп до всички снимки“, за да осигурите нормален достъп до албума в NIIMBOT. Ако изберете „Избор на снимки...“, всички неизбрани снимки и бъдещи допълнения няма да бъдат достъпни в NIIMBOT.";
NSLocationWhenInUseUsageDescription = "За да улесни свързването ви с близките Wi-Fi мрежи, NIIMBOT изисква разрешение за местоположение";
NSLocationAlwaysUsageDescription = "За да улесни свързването ви с близките Wi-Fi мрежи, NIIMBOT изисква разрешение за местоположение";
NSLocationAlwaysAndWhenInUseUsageDescription = "За да улесни свързването ви с близките Wi-Fi мрежи, NIIMBOT изисква разрешение за местоположение";
NSSpeechRecognitionUsageDescription = "Това приложение изисква съгласието ви за достъп до услуги за гласово разпознаване. Разрешаване на достъпа до услуги за гласово разпознаване?";
NSLocalNetworkUsageDescription = "Това приложение се нуждае от достъп до ​Локална мрежа (LAN)​​ за услуги за търсене на LAN устройства и конфигуриране на мрежата.";
"UILaunchStoryboardName" = "LaunchScreen";
