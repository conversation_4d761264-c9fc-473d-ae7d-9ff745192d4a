import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:nety/exceptions/niimbot_nety_exception.dart';
import 'package:nety/models/copy_wrapper.dart';
import 'package:nety/models/index.dart';
import 'package:nety/models/niimbot_printer.dart';
import 'package:nety/models/niimbot_printer_print_progress_info.dart';
import 'package:nety/nety.dart';
import 'package:nety/store.dart';
import 'package:niimbot_print_setting_plugin/print/c1_print_config.dart';
import 'package:niimbot_print_setting_plugin/print/model/print_advert_model.dart';
import 'package:niimbot_print_setting_plugin/print/printer_strategy_manager.dart';
import 'package:niimbot_print_setting_plugin/print/widget/niimbot_print_button.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:provider/provider.dart';

import '../../extensions/change_notifer.dart';
import '../../utils/svg_icon.dart';
import '../model/print_state_model.dart';
import '../print_config.dart';
import '../print_manager.dart';
import 'niimbot_print_finish_danger_widget.dart';
import 'package:text/ad/print_ad_manager.dart';
import 'package:text/ad/ad_model.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'niimbot_print_finish_ad_widget.dart';
import 'package:niimbot_print_setting_plugin/print_setting_manager.dart'; // Correct PrintTaskType import

class NiimbotPrintProgressWidget extends StatefulWidget {
  final VoidCallback onClose;
  final VoidCallback onPrinterCompleted;
  final VoidCallback onPrinterContinued;
  final PrintConfig config;

  const NiimbotPrintProgressWidget({
    super.key,
    required this.onClose,
    required this.onPrinterCompleted,
    required this.onPrinterContinued,
    required this.config,
  });

  @override
  State<NiimbotPrintProgressWidget> createState() => _NiimbotPrintProgressWidgetState();
}

class _NiimbotPrintProgressWidgetState extends State<NiimbotPrintProgressWidget> {
  late void Function() _disposePrintError;
  final ValueNotifier<bool> isPausing = ValueNotifier(false);
  final ValueNotifier<bool> isCanceling = ValueNotifier(false);
  final ValueNotifier<bool> isContinuing = ValueNotifier(false);
  bool inited = false;
  NiimbotPrinterPrintProgressInfo? printProgressInfo;
  int copysNumber = 1;
  int printedCopysNumber = 0;
  bool printTaskCanceled = false;
  String? capId;
  int isRePrint = 0;
  bool _adTracked = false;

  @override
  void initState() {
    super.initState();
    inited = false;
    _disposePrintError = widget.config.watch<PrintConfig, NiimbotNetyExceptionCode?>(
      (v) => v.printError,
      (_, __) {
        setState(() {});
      },
    );
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        inited = true;
      });
    });

    Map<String, dynamic>? customData = PrintManager().printSettingLogic.parameter!.customData;
    capId = customData?["uniAppInfo"]?["uniAppId"];
    isRePrint = customData?["uniAppInfo"]?["isRePrint"] ?? 0;
  }

  @override
  void dispose() {
    super.dispose();
    _disposePrintError();
  }

  // 判断当前打印广告是否展示
  Future<AdPopupDecision?> _loadAdPopupDecision() async {
    final advertModel = await PrintAdManager.fetchCanShowAdvert();
    if (advertModel == null) return null;

    final logic = PrintManager().printSettingLogic;
    final uniAppId = capId ?? '';

    bool shouldShowAd = false;

    // 小程序不显示广告
    if (logic.taskType == PrintTaskType.miniApp || logic.taskType == PrintTaskType.printC1) {
      shouldShowAd = false;
    } else if (logic.taskType == PrintTaskType.miniApp || logic.taskType == PrintTaskType.printC1) {
      shouldShowAd = !(await logic.channel.isTriggerMiniProgramNpsPop(uniAppId: uniAppId));
    } else if (logic.taskType != PrintTaskType.miniApp &&
        logic.taskType != PrintTaskType.printNullUi &&
        logic.taskType != PrintTaskType.printNullUiShowProgress) {
      shouldShowAd = !(await logic.channel.isTriggerNpsPop());
      if (shouldShowAd) {
        shouldShowAd = !(await logic.channel.isTriggerMarketRatingPop());
      }
    }

    if (shouldShowAd) {
      if (!_adTracked) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "024_325",
          "ext": {
            'a_name': advertModel.activityName ?? "",
            'b_name': advertModel.name ?? "",
            'banner_id': advertModel.id ?? "",
          }
        });
      }
      _adTracked = true;
      PrintAdManager.recordAdShown();
      return AdPopupDecision(showAd: true, adModel: advertModel);
    }
    return null;
  }

  /// 取消打印
  onPrinterStopPrint() async {
    debugPrint("YMY 点击了取消打印");
    isCanceling.value = true;
    try {
      if ((printProgressInfo?.errorCode ?? 0) != 0 &&
          printProgressInfo?.status == NiimbotPrinterPrintStatus.terminated) {
        debugPrint("YMY 打印异常 不调用SDK取消方法，直接取消弹窗");
      } else {
        await NiimbotPrintSDK().printerCancelPrint();
      }
      debugPrint("YMY 取消打印异步await结束");
      _onDismissAlertEvent(NiimbotPrinterPrintStatus.terminated);
    } catch (e) {
      _onDismissAlertEvent(NiimbotPrinterPrintStatus.terminated);
    }
    isCanceling.value = false;
  }

  ///取消打印弹窗
  _onDismissAlertEvent(NiimbotPrinterPrintStatus printerStatus) {
    debugPrint("YMY 取消打印弹窗消失");
    if (mounted) {
      setState(() {
        inited = false;
      });
    }
    Future.delayed(const Duration(microseconds: 400), () {
      if (printerStatus == NiimbotPrinterPrintStatus.completed) {
        widget.onPrinterCompleted();
      } else {
        widget.onClose();
      }
    });
  }

  /// 暂停打印
  onPrinterPausePrint() async {
    isPausing.value = true;
    try {
      final res = await NiimbotPrintSDK().printerPausePrint();
      debugPrint('暂停：$res');
    } catch (e) {}
    isPausing.value = false;
  }

  /// 继续打印
  onPrinterContinuePrint() async {
    isContinuing.value = true;
    try {
      widget.onPrinterContinued();
      final densityType = await PrinterStrategyManager().printCheckRFIDPrintStrategy(
          PrintManager().printSettingLogic, PrintManager().uniqueValue, (languageCode, descrp) {
        return PrintManager().getI18nString(languageCode, descrp);
      });
      if (densityType != StrategyType.forbid) {
        int density = widget.config.originalDensity;
        if (densityType == StrategyType.lowDensity0) {
          density = 0;
        } else if (densityType == StrategyType.lowDensity1) {
          density = -1;
        } else if (densityType == StrategyType.lowDensity2) {
          density = -2;
        }
        debugPrint("currentPrint density: continue $density");
        if (densityType == StrategyType.batchforbid) {
          NiimbotPrinter? connectedPrinter = NiimbotPrintSDK().store.connectedPrinter;
          if (connectedPrinter != null && connectedPrinter.progressInfo != null) {
            NiimbotPrintSDK().store.updatePrinter(connectedPrinter.copyWith(
                  progressInfo: CopyWrapper.value(
                    connectedPrinter.progressInfo
                        ?.copyWith(fixEnableTotal: connectedPrinter.progressInfo!.printedTotal + 1),
                  ),
                ));
          }
        }
        final res = await NiimbotPrintSDK().printerContinuePrint(density);
        debugPrint('printerContinuePrint-----$res');
      }
    } catch (e) {
      debugPrint('printerContinuePrintErr-----$e');
    }
    isContinuing.value = false;
  }

  @override
  Widget build(BuildContext context) {
    NiimbotPrinter? printPrinter = context.select<NiimbotPrintSDKStore, NiimbotPrinter?>((v) => v.connectedPrinter);
    NiimbotPrinterPrintProgressInfo? currentPrintProgressInfo = printPrinter?.progressInfo;
    if (currentPrintProgressInfo == null && printPrinter != null && printProgressInfo == null) {
      //打印前 防止红屏
      return Container();
    }
    if (currentPrintProgressInfo != null) {
      //记录当前打印进度
      int errorCode = currentPrintProgressInfo.errorCode ?? 0;
      if (currentPrintProgressInfo.errorCode == 1 && printPrinter?.coverStatus == 1) {
        errorCode = 0;
      }
      printProgressInfo = currentPrintProgressInfo.copyWith(errorCode: CopyWrapper.value(errorCode));
      copysNumber = printProgressInfo!.total;
      printedCopysNumber = printProgressInfo!.printedTotal;
    }
    int errorCode = printProgressInfo?.errorCode ?? 0;
    if (errorCode > 0) {
      debugPrint("打印报错中断");
    }
    PrintStateModel? currentState;
    final isC1Data = widget.config is C1PrintConfig;
    if (printPrinter == null) {
      //打印机断开场景
      errorCode = 23;
      printProgressInfo = printProgressInfo?.copyWith(
          errorCode: CopyWrapper.value(errorCode), status: NiimbotPrinterPrintStatus.terminated);
      currentState = PrintStateModel.buildStatus(
          errorCode: errorCode,
          printStatus: printProgressInfo!.status,
          printerStatus: NiimbotPrinterStatus.disconnected,
          isC1Data: isC1Data);
    } else {
      //根据错误码、状态码获取当前打印状态描述
      if (printProgressInfo != null) {
        currentState = PrintStateModel.buildStatus(
            errorCode: errorCode,
            printStatus: printProgressInfo!.status,
            printerStatus: printPrinter.status,
            isC1Data: isC1Data);
      }
    }
    //危废小程序打印完成，弹出危废打印完成弹窗
    if (printProgressInfo!.status == NiimbotPrinterPrintStatus.completed && capId == "__CAP__SPR666G") {
      widget.onPrinterCompleted();
      Future.delayed(const Duration(milliseconds: 200), () {
        _showPrintFinishForDanger(isRePrint == 1 ? true : false);
      });

      return const SizedBox.shrink();
    }
    // TODO 需要和SDK确认  -wy
    String statusMessage = "";
    if (printProgressInfo!.waitTime > 0) {
      statusMessage = PrintManager().getI18nString('app100000421', '为保护打印头，低速打印中...');
    }

    // 新增：广告弹窗展示逻辑
    if (printProgressInfo!.status == NiimbotPrinterPrintStatus.completed) {
      return FutureBuilder<AdPopupDecision?>(
        future: _loadAdPopupDecision(),
        builder: (context, snapshot) {
          final decision = snapshot.data;
          if (decision != null && decision.showAd && decision.adModel != null) {
            Future.delayed(const Duration(seconds: 4), () {
              _onDismissAlertEvent(printProgressInfo!.status);
            });
            return NiimbotPrintFinishAdWidget(
              adData: decision.adModel!,
              onClose: () {
                _onDismissAlertEvent(printProgressInfo!.status);
              },
            );
          } else {
            Future.delayed(const Duration(seconds: 2), () {
              _onDismissAlertEvent(printProgressInfo!.status);
            });
            return _buildPrintFinishUI(currentState);
          }
        },
      );
    }

    double width = MediaQuery.of(context).size.width;
    return Stack(
      children: [
        Container(
          color: const Color.fromRGBO(0, 0, 8, 0.1),
        ),
        AnimatedPositioned(
            left: 14,
            bottom: (!inited) ? -300 : 30,
            duration: const Duration(milliseconds: 350),
            curve: Curves.easeInOut,
            child: Container(
                width: width - 28,
                decoration: const BoxDecoration(
                    color: Colors.white, borderRadius: BorderRadiusDirectional.all(Radius.circular(14))),
                child: Padding(
                  padding: const EdgeInsets.only(left: 20, top: 20, bottom: 20, right: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: printProgressInfo!.status == NiimbotPrinterPrintStatus.completed
                        ? MainAxisAlignment.center
                        : MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: (printProgressInfo!.status == NiimbotPrinterPrintStatus.completed)
                        ? [
                            // 完成UI已被抽取到 _buildPrintFinishUI
                          ]
                        : [
                            SizedBox(
                                child: Center(
                              child: Text(
                                currentState?.title ?? PrintManager().getI18nString("app01163", "正在打印"),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF262626)),
                              ),
                            )),
                            const SizedBox(
                              height: 6,
                            ),
                            if (currentState != null)
                              currentState.content != null
                                  ? SizedBox(
                                      child: Center(
                                          child: Text(
                                      '${currentState.content}',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(
                                          fontSize: 14, fontWeight: FontWeight.w400, color: Color(0xFF262626)),
                                    )))
                                  : const SizedBox(width: 0),
                            const SizedBox(
                              height: 10,
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 0),
                              child: currentState != null
                                  ? Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisAlignment: _isReadyToPrintState()
                                                    ? MainAxisAlignment.start
                                                    : MainAxisAlignment.spaceBetween,
                                                children: _isReadyToPrintState()
                                                    ? [
                                                        const CupertinoActivityIndicator(
                                                          radius: 7.5,
                                                          color: Colors.grey,
                                                          animating: true,
                                                        ),
                                                        const SizedBox(width: 5),
                                                        (printProgressInfo!.status == NiimbotPrinterPrintStatus.init ||
                                                                printProgressInfo!.status ==
                                                                    NiimbotPrinterPrintStatus.ready)
                                                            ? Text(
                                                                PrintManager().getI18nString("app01163", "准备打印"),
                                                                overflow: TextOverflow.ellipsis,
                                                                style: const TextStyle(
                                                                    fontSize: 14,
                                                                    fontWeight: FontWeight.w400,
                                                                    color: Color(0xFF999999)),
                                                              )
                                                            : Text(
                                                                PrintManager()
                                                                    .getI18nString("app100000921", "数据传输中..."),
                                                                overflow: TextOverflow.ellipsis,
                                                                style: const TextStyle(
                                                                    fontSize: 14,
                                                                    fontWeight: FontWeight.w400,
                                                                    color: Color(0xFF999999)),
                                                              ),
                                                      ]
                                                    : [
                                                        if (_printingProgressDescr(printProgressInfo).isNotEmpty)
                                                          Text(
                                                            _printingProgressDescr(printProgressInfo),
                                                            overflow: TextOverflow.ellipsis,
                                                            style: const TextStyle(
                                                                fontSize: 14,
                                                                fontWeight: FontWeight.w400,
                                                                color: Color(0xFF999999)),
                                                          ),
                                                        Text(
                                                          '$printedCopysNumber/$copysNumber',
                                                          overflow: TextOverflow.ellipsis,
                                                          style: const TextStyle(
                                                              fontSize: 14,
                                                              fontWeight: FontWeight.w400,
                                                              color: Color(0xFF999999)),
                                                        )
                                                      ],
                                              ),
                                              const SizedBox(
                                                height: 6,
                                              ),
                                              LinearProgressIndicator(
                                                value: _printProgressInt(printProgressInfo),
                                                minHeight: 8,
                                                borderRadius: BorderRadius.circular(4),
                                                backgroundColor: const Color(0xFFEBEBEB),
                                                valueColor: AlwaysStoppedAnimation<Color>(
                                                  (printProgressInfo!.status == NiimbotPrinterPrintStatus.terminated ||
                                                          printProgressInfo!.status ==
                                                              NiimbotPrinterPrintStatus.pausing)
                                                      ? const Color(0xFF3C3C43).withOpacity(0.15)
                                                      : PrintManager().getCurrentPrintSettingThemeColor(),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    )
                                  : Container(),
                            ),
                            const SizedBox(
                              height: 22,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (printProgressInfo!.status != NiimbotPrinterPrintStatus.completed)
                                  Expanded(
                                      child: Padding(
                                          padding: const EdgeInsets.only(left: 0),
                                          child: ValueListenableBuilder(
                                              valueListenable: isCanceling,
                                              builder: (_, value, __) {
                                                return NiimbotPrintButton(
                                                  height: 44,
                                                  type: NiimbotButtonType.secondary,
                                                  isLoading: value,
                                                  text: value ? "" : PrintManager().getI18nString("app00036", "取消打印"),
                                                  onPressed: onPrinterStopPrint,
                                                );
                                              }))),
                                if ((printProgressInfo!.status == NiimbotPrinterPrintStatus.ready ||
                                        printProgressInfo!.status == NiimbotPrinterPrintStatus.printing) &&
                                    printPrinter != null)
                                  Expanded(
                                      child: Padding(
                                          padding: const EdgeInsets.only(left: 10),
                                          child: ValueListenableBuilder(
                                              valueListenable: isPausing,
                                              builder: (context, value, child) {
                                                return NiimbotPrintButton(
                                                  height: 44,
                                                  type: NiimbotButtonType.alert,
                                                  isLoading: value,
                                                  text: value ? "" : PrintManager().getI18nString("app01249", "暂停打印"),
                                                  onPressed: onPrinterPausePrint,
                                                  foregroundThemeColor:
                                                      PrintManager().getCurrentPrintSettingThemeColor(),
                                                );
                                              }))),
                                if (printProgressInfo!.status == NiimbotPrinterPrintStatus.pausing &&
                                    printPrinter != null)
                                  Expanded(
                                      child: Padding(
                                          padding: const EdgeInsets.only(left: 10),
                                          child: ValueListenableBuilder(
                                            valueListenable: isContinuing,
                                            builder: (_, value, __) {
                                              return NiimbotPrintButton(
                                                height: 44,
                                                type: NiimbotButtonType.alert,
                                                isLoading: value,
                                                text: value ? "" : PrintManager().getI18nString("app01586", "继续打印"),
                                                onPressed: onPrinterContinuePrint,
                                                foregroundThemeColor: PrintManager().getCurrentPrintSettingThemeColor(),
                                              );
                                            },
                                          ))),
                              ],
                            ),
                          ],
                  ),
                ))),
      ],
    );
  }

  // 新增：抽取原有完成UI为私有方法，便于复用
  Widget _buildPrintFinishUI(PrintStateModel? currentState) {
    return Stack(
      children: [
        Container(
          color: const Color.fromRGBO(0, 0, 8, 0.1),
        ),
        Positioned(
          left: 14,
          bottom: 30,
          child: Container(
            width: MediaQuery.of(context).size.width - 28,
            decoration: const BoxDecoration(
                color: Colors.white, borderRadius: BorderRadiusDirectional.all(Radius.circular(14))),
            child: Padding(
              padding: const EdgeInsets.only(left: 20, top: 20, bottom: 20, right: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 45),
                  const SvgIcon('assets/printSuccess.svg'),
                  const SizedBox(height: 8),
                  SizedBox(
                    child: Center(
                      child: Text(
                        currentState?.title ?? PrintManager().getI18nString('app01438', '打印完成'),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF262626)),
                      ),
                    ),
                  ),
                  const SizedBox(height: 45),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  bool _isReadyToPrintState() {
    return (printProgressInfo != null &&
        printProgressInfo!.printedTotal < 1 &&
        printProgressInfo!.errorCode == 0 &&
        printProgressInfo!.status != NiimbotPrinterPrintStatus.terminated &&
        printProgressInfo!.status != NiimbotPrinterPrintStatus.pausing);
  }

  //总分数打印进度 用于进度条计算
  double _printProgressInt(NiimbotPrinterPrintProgressInfo? printProgressInfo) {
    final value = printProgressInfo != null && printProgressInfo.total != 0
        ? printProgressInfo.printedTotal / printProgressInfo.total
        : 0.0;
    return value;
  }

  //分页及份数进度
  String _printingProgressDescr(NiimbotPrinterPrintProgressInfo? printProgressInfo) {
    String progressDescr = '';
    String pageDesc = '';
    String copiesDesc = '';
    if (printProgressInfo == null) return progressDescr;

    if ((printProgressInfo.batchTemplatePrintCopiesList ?? []).isNotEmpty) {
      // 区分PrintConfig类型，C1直接展示总打印份数
      if (widget.config is C1PrintConfig) {
        return '';
      } else {
        pageDesc = PrintManager().getI18nString('app100001796', '第\$1S/\$2S页', param: [
          printProgressInfo.printedPage.toString(),
          printProgressInfo.batchTemplatePrintCopiesList!.length.toString()
        ]);
        copiesDesc = PrintManager().getI18nString('app100001797', '第\$1S/\$2S份', param: [
          printProgressInfo.printedCopies.toString(),
          (printProgressInfo.batchTemplatePrintCopiesList![
                  (printProgressInfo.printedPage - 1) < 0 ? 0 : (printProgressInfo.printedPage - 1)])
              .toString()
        ]);
      }
    } else if (printProgressInfo.page > 1 && printProgressInfo.copies > 1) {
      pageDesc = PrintManager().getI18nString('app100001796', '第\$1S/\$2S页',
          param: [printProgressInfo.printedPage.toString(), printProgressInfo.page.toString()]);
      copiesDesc = PrintManager().getI18nString('app100001797', '第\$1S/\$2S份',
          param: [printProgressInfo.printedCopies.toString(), printProgressInfo.copies.toString()]);
    }
    if (pageDesc.isNotEmpty && copiesDesc.isNotEmpty) {
      progressDescr = '$pageDesc-$copiesDesc';
    }
    return progressDescr;
  }

  /// 危废打印完成弹窗
  _showPrintFinishForDanger(bool isRePrint) async {
    BuildContext context = PrintManager().printSettingLogic.pageManager.context;
    showGeneralDialog(
      context: context,
      barrierDismissible: false,
      useRootNavigator: false,
      barrierColor: Colors.black.withOpacity(0.05),
      pageBuilder: (BuildContext buildContext, Animation<double> animation, Animation<double> secondaryAnimation) {
        return SafeArea(
          child: NiimbotPrintFinishDangerWidget(isRePrint),
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        // 定义从下往上的动画
        const begin = Offset(0.0, 1.0); // 动画开始时的位置（屏幕下方）
        const end = Offset.zero; // 动画结束时的位置（屏幕中心）
        const curve = Curves.easeInOut; // 动画的过渡曲线

        var tween = Tween(begin: begin, end: end).chain(CurveTween(curve: curve));
        var offsetAnimation = animation.drive(tween);

        return SlideTransition(position: offsetAnimation, child: child);
      },
    );
  }
}
