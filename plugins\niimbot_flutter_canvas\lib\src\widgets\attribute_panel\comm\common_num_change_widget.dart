import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/num_step_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_theme_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/add_subtract_text_button.dart';

///通用数值变化控件---间距，字距等
class CommonNumChangeWidget<T extends num> extends StatelessWidget {
  final String? title;

  /// 最小值
  final T min;

  /// 最大值
  final T max;

  /// 当前值
  final T value;

  /// 步进值
  final T stepValue;
  final ValueChanged<T>? valueChanged;
  final bool canInput;
  final RegExp? allowInputRegExp;
  final ThrottleAddCallback? throttleAddCallback;
  final ThrottleSubtractCallback? throttleSubtractCallback;

  /// 是否显示标题
  final bool isShowTitle;
  final bool isEnable;
  const CommonNumChangeWidget(
      {super.key,
      this.title,
      required this.min,
      required this.max,
      required this.stepValue,
      required this.value,
      this.valueChanged,
      this.canInput = false,
      this.isShowTitle = true,
      this.isEnable = true,
      this.allowInputRegExp = null,
      this.throttleAddCallback = null,
      this.throttleSubtractCallback = null});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      alignment: Alignment.centerLeft,
      padding: const EdgeInsetsDirectional.fromSTEB(12, 0, 8, 0),
      child: Row(
        // crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (isShowTitle) ...[
            Text(
              title ?? "",
              style: CanvasTheme.of(context).attributeTitleTextStyle,
            ),
            const Spacer(),
            NumStepWidget<T>(
              isEnable: isEnable,
              max: max,
              min: min,
              value: value,
              stepValue: stepValue,
              canInput: canInput,
              allowInputRegExp: allowInputRegExp,
              valueChanged: (T v) {
                valueChanged?.call(v);
              },
              throttleAddCallback: throttleAddCallback,
              throttleSubtractCallback: throttleSubtractCallback,
              isExpand: false,
            ),
          ],
          if (!isShowTitle)
            Expanded(
              child: NumStepWidget<T>(
                isEnable: isEnable,
                max: max,
                min: min,
                value: value,
                stepValue: stepValue,
                canInput: canInput,
                allowInputRegExp: allowInputRegExp,
                valueChanged: (T v) {
                  valueChanged?.call(v);
                },
                throttleAddCallback: throttleAddCallback,
                throttleSubtractCallback: throttleSubtractCallback,
                isExpand: true,
              ),
            ),
        ],
      ),
    );
  }
}
