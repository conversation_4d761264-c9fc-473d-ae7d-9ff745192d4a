//
//  JCTemplateData+DetailComplate.m
//  Runner
//
//  Created by xingling xu on 2020/6/3.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTemplateData+External.h"
#import "JCElementModel+Transfer.h"
#import "JCFontManager.h"

@implementation JCTemplateData (DetailComplate)

//检查模板状态及资源完整性
- (BOOL)checkTemplateDetailByBackImage:(BOOL)checkBackImage containFont:(BOOL)checkFont{
    BOOL hasComplateDetail = NO;
    if([self checkTemplateThumbSourceComplate] &&
       [self checkTemplateElementsSourceComplate] &&
       [self checkTemplateNinePatchElementsSourceComplate]) {
        hasComplateDetail = YES;
    }
    if(checkBackImage && ![self checkTemplateBackImageSourceComplate]){
        hasComplateDetail = NO;
    }
    if(checkFont && [self checkIsNeedDownloadFont]){
        hasComplateDetail = NO;
    }
    return hasComplateDetail;
}

//检查模板状态及资源完整性
- (BOOL)checkTemplateComplate{
    BOOL hasComplateDetail = NO;
    if([self checkTemplateElementsSourceComplate] &&
       [self checkTemplateNinePatchElementsSourceComplate] &&
       [self checkTemplateBackImageSourceComplate]) {
        hasComplateDetail = YES;
    }
    return hasComplateDetail;
}


//检查模板状态及资源不完整原因
- (NSString *)checkTemplateDetailUnComplateReason{
    NSString *errorMsg = @"";
    if(self.localType == JCLocalType_Default){
        errorMsg = @"详情未同步";
    }else if(![self checkTemplateThumbSourceComplate]){
        errorMsg = @"缩略图未下载";
    }else if(![self checkTemplateElementsSourceComplate]){
        errorMsg = @"元素图未下载";
    }else if(![self checkTemplateNinePatchElementsSourceComplate]){
      errorMsg = @"点9图元素图未下载";
    }else if(![self checkTemplateBackImageSourceComplate]){
        errorMsg = @"背景图未下载";
    }else if(![self checkIsNeedDownloadFont]){
        errorMsg = @"字体未下载";
    }
    return errorMsg;
}

//检查是否需要下载字体
- (BOOL)checkIsNeedDownloadFont{
    NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:self].count;
    NSInteger needChangLangCount = [[JCFontManager sharedManager] needChangLangDownloadFont:self].count;
    if([[JCFontManager sharedManager] isNeedDownloadFonts:self]){
        if(noFontCount != 0){
            if(needChangLangCount == 0){
                return YES;
            }else{
                return YES;
            }
        }else{
            if(needChangLangCount != 0){
                return NO;
            }else{
                return YES;
            }
        }
    }else{
        return NO;
    }
}

//检查模板元素数据完整性
- (BOOL)checkTemplateElementsSourceComplate{
    BOOL sourceIsComplete = YES;
    for (JCElementModel *element in self.elements) {
        if([element.type isEqualToString:@"image"]){//检查图片元素是否已下载
          if(![self isImageValidAtPath:element.localUrl] && !STR_IS_NIL(element.imageUrl)){
                NSLog(@"批量打印-----检查%@元素不存在",self.idStr);
                sourceIsComplete = NO;
            }
        }
    }
    return sourceIsComplete;
}

- (BOOL)isImageValidAtPath:(NSString *)imagePath {
    // 1. 判断文件是否存在
    if (![[NSFileManager defaultManager] fileExistsAtPath:imagePath]) {
        NSLog(@"图片文件不存在");
        return NO;
    }

    // 2. 判断文件大小是否为 0（空文件）
    NSDictionary *attributes = [[NSFileManager defaultManager] attributesOfItemAtPath:imagePath error:nil];
    if ([attributes fileSize] == 0) {
        NSLog(@"图片文件大小为 0");
        return NO;
    }

    // 3. 尝试用 UIImage 加载，判断是否能正确解码
    UIImage *image = [UIImage imageWithContentsOfFile:imagePath];
    if (image == nil) {
        NSLog(@"图片解码失败，可能是损坏的图片");
        return NO;
    }

    // ✅ 所有检查通过，图片有效
    return YES;
}

// 检查模板点9图元素数据完整性
- (BOOL)checkTemplateNinePatchElementsSourceComplate{
    BOOL sourceIsComplete = YES;
    for (JCElementModel *element in self.elements) {
        if([element.type isEqualToString:@"image"] && element.isNinePatch){ //检查图片点9图元素是否已下载
            if(![[NSFileManager defaultManager] fileExistsAtPath:element.ninePatchLocalUrl] && !STR_IS_NIL(element.ninePatchUrl)){
                NSLog(@"批量打印-----检查%@点9图元素不存在",self.idStr);
                sourceIsComplete = NO;
            }
        }
    }
    return sourceIsComplete;
}

//检查模板缩略图数据完整性
- (BOOL)checkTemplateThumbSourceComplate{
    BOOL sourceIsComplete = YES;
    if(![[NSFileManager defaultManager] fileExistsAtPath:self.localThumbnail] && !STR_IS_NIL(self.thumbnail)){
        NSLog(@"批量打印-----检查缩略图不存在");
        sourceIsComplete = NO;
    }
    return sourceIsComplete;
}

//检查模板背景图数据完整性
- (BOOL)checkTemplateBackImageSourceComplate{
    BOOL sourceIsComplete = YES;
    if(STR_IS_NIL(self.backgroundImage)){
        return sourceIsComplete;
    }else if([self.backgroundImage componentsSeparatedByString:@","].count > 1){
        NSArray *backImages = [self.backgroundImage componentsSeparatedByString:@","];
        for(NSInteger index = 0;index < backImages.count;index++){
            NSString *backPath = self.localBackground[index];
            if(![[NSFileManager defaultManager] fileExistsAtPath:backPath] ||STR_IS_NIL(backPath)){
                sourceIsComplete = NO;
                break;
            }
        }
        if(!sourceIsComplete){
            for(NSInteger index = 0;index < backImages.count;index++){
                NSString *backPath = self.localBackground[index];
                if(![[NSFileManager defaultManager] fileExistsAtPath:backPath] || STR_IS_NIL(backPath)){
                    sourceIsComplete = NO;
                    break;
                }
            }
        }
    }else{
        NSString *backPath = self.backgroundImage;
        if(!STR_IS_NIL(backPath)){
          NSString *localBackPath = self.localBackground[0];
          if(STR_IS_NIL(localBackPath) || (![[NSFileManager defaultManager] fileExistsAtPath:localBackPath] && ![[NSFileManager defaultManager] fileExistsAtPath:[self replaceSandBoxRealPathWith:localBackPath]])){
              sourceIsComplete = NO;
          }
        }
    }
    if(!sourceIsComplete) NSLog(@"批量打印-----检查背景图不存在");
    return sourceIsComplete;
}

- (NSString *)replaceSandBoxRealPathWith:(NSString *)oldPath{
  // 获取当前沙盒 Documents 目录
  NSString *currentDocumentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES).firstObject;

  // 获取当前沙盒的 UUID 根路径（截取到 Documents）
  NSRange docRange = [currentDocumentsPath rangeOfString:@"/Documents"];
  NSString *currentUUIDPath = [currentDocumentsPath substringToIndex:docRange.location];

  // 获取旧路径中的 Documents 相对路径
  NSRange oldDocRange = [oldPath rangeOfString:@"/Documents"];
  NSString *relativePath = [oldPath substringFromIndex:oldDocRange.location];

  // 拼接新路径
  NSString *newPath = [currentUUIDPath stringByAppendingString:relativePath];

  NSLog(@"替换后的路径: %@", newPath);
  return newPath;
}
@end
