// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'print_data_log_model.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetPrintDataLogModelCollection on Isar {
  IsarCollection<PrintDataLogModel> get printDataLogModels => this.collection();
}

const PrintDataLogModelSchema = CollectionSchema(
  name: r'PrintDataLogModel',
  id: -1464512476904303229,
  properties: {
    r'addTime': PropertySchema(
      id: 0,
      name: r'addTime',
      type: IsarType.string,
    ),
    r'allTimes': PropertySchema(
      id: 1,
      name: r'allTimes',
      type: IsarType.string,
    ),
    r'applicationVersion': PropertySchema(
      id: 2,
      name: r'applicationVersion',
      type: IsarType.string,
    ),
    r'city': PropertySchema(
      id: 3,
      name: r'city',
      type: IsarType.string,
    ),
    r'cloudTemplateId': PropertySchema(
      id: 4,
      name: r'cloudTemplateId',
      type: IsarType.string,
    ),
    r'commodityCount': PropertySchema(
      id: 5,
      name: r'commodityCount',
      type: IsarType.long,
    ),
    r'country': PropertySchema(
      id: 6,
      name: r'country',
      type: IsarType.string,
    ),
    r'deviceId': PropertySchema(
      id: 7,
      name: r'deviceId',
      type: IsarType.string,
    ),
    r'deviceRegionCode': PropertySchema(
      id: 8,
      name: r'deviceRegionCode',
      type: IsarType.long,
    ),
    r'device_id_dot': PropertySchema(
      id: 9,
      name: r'device_id_dot',
      type: IsarType.string,
    ),
    r'district': PropertySchema(
      id: 10,
      name: r'district',
      type: IsarType.string,
    ),
    r'extraData': PropertySchema(
      id: 11,
      name: r'extraData',
      type: IsarType.string,
    ),
    r'firmwareVersion': PropertySchema(
      id: 12,
      name: r'firmwareVersion',
      type: IsarType.string,
    ),
    r'hardwareVersion': PropertySchema(
      id: 13,
      name: r'hardwareVersion',
      type: IsarType.string,
    ),
    r'height': PropertySchema(
      id: 14,
      name: r'height',
      type: IsarType.string,
    ),
    r'illegalCode': PropertySchema(
      id: 15,
      name: r'illegalCode',
      type: IsarType.string,
    ),
    r'isCloudTemplate': PropertySchema(
      id: 16,
      name: r'isCloudTemplate',
      type: IsarType.bool,
    ),
    r'latitude': PropertySchema(
      id: 17,
      name: r'latitude',
      type: IsarType.string,
    ),
    r'longitude': PropertySchema(
      id: 18,
      name: r'longitude',
      type: IsarType.string,
    ),
    r'macNo': PropertySchema(
      id: 19,
      name: r'macNo',
      type: IsarType.string,
    ),
    r'machineId': PropertySchema(
      id: 20,
      name: r'machineId',
      type: IsarType.string,
    ),
    r'machineStatus': PropertySchema(
      id: 21,
      name: r'machineStatus',
      type: IsarType.bool,
    ),
    r'number': PropertySchema(
      id: 22,
      name: r'number',
      type: IsarType.string,
    ),
    r'offlinePrint': PropertySchema(
      id: 23,
      name: r'offlinePrint',
      type: IsarType.bool,
    ),
    r'oneCode': PropertySchema(
      id: 24,
      name: r'oneCode',
      type: IsarType.string,
    ),
    r'paperLengthUsedQuantity': PropertySchema(
      id: 25,
      name: r'paperLengthUsedQuantity',
      type: IsarType.string,
    ),
    r'paperLengthUsedQuantitySum': PropertySchema(
      id: 26,
      name: r'paperLengthUsedQuantitySum',
      type: IsarType.string,
    ),
    r'phoneBrand': PropertySchema(
      id: 27,
      name: r'phoneBrand',
      type: IsarType.string,
    ),
    r'printChannel': PropertySchema(
      id: 28,
      name: r'printChannel',
      type: IsarType.string,
    ),
    r'printFinishTime': PropertySchema(
      id: 29,
      name: r'printFinishTime',
      type: IsarType.long,
    ),
    r'printStrategy': PropertySchema(
      id: 30,
      name: r'printStrategy',
      type: IsarType.string,
    ),
    r'printStyle': PropertySchema(
      id: 31,
      name: r'printStyle',
      type: IsarType.string,
    ),
    r'printType': PropertySchema(
      id: 32,
      name: r'printType',
      type: IsarType.long,
    ),
    r'printedProcess': PropertySchema(
      id: 33,
      name: r'printedProcess',
      type: IsarType.string,
    ),
    r'probationPrivilege': PropertySchema(
      id: 34,
      name: r'probationPrivilege',
      type: IsarType.string,
    ),
    r'province': PropertySchema(
      id: 35,
      name: r'province',
      type: IsarType.string,
    ),
    r'recordSource': PropertySchema(
      id: 36,
      name: r'recordSource',
      type: IsarType.long,
    ),
    r'recordType': PropertySchema(
      id: 37,
      name: r'recordType',
      type: IsarType.long,
    ),
    r'rfidPrintNumber': PropertySchema(
      id: 38,
      name: r'rfidPrintNumber',
      type: IsarType.string,
    ),
    r'rfidSerialNumber': PropertySchema(
      id: 39,
      name: r'rfidSerialNumber',
      type: IsarType.string,
    ),
    r'ribbonUsed': PropertySchema(
      id: 40,
      name: r'ribbonUsed',
      type: IsarType.double,
    ),
    r'sourceId': PropertySchema(
      id: 41,
      name: r'sourceId',
      type: IsarType.string,
    ),
    r'status': PropertySchema(
      id: 42,
      name: r'status',
      type: IsarType.long,
    ),
    r'street': PropertySchema(
      id: 43,
      name: r'street',
      type: IsarType.string,
    ),
    r'successTimes': PropertySchema(
      id: 44,
      name: r'successTimes',
      type: IsarType.string,
    ),
    r'systemType': PropertySchema(
      id: 45,
      name: r'systemType',
      type: IsarType.string,
    ),
    r'systemVersion': PropertySchema(
      id: 46,
      name: r'systemVersion',
      type: IsarType.string,
    ),
    r'templeteId': PropertySchema(
      id: 47,
      name: r'templeteId',
      type: IsarType.string,
    ),
    r'uniqueValue': PropertySchema(
      id: 48,
      name: r'uniqueValue',
      type: IsarType.string,
    ),
    r'userId': PropertySchema(
      id: 49,
      name: r'userId',
      type: IsarType.long,
    ),
    r'width': PropertySchema(
      id: 50,
      name: r'width',
      type: IsarType.string,
    )
  },
  estimateSize: _printDataLogModelEstimateSize,
  serialize: _printDataLogModelSerialize,
  deserialize: _printDataLogModelDeserialize,
  deserializeProp: _printDataLogModelDeserializeProp,
  idName: r'id',
  indexes: {},
  links: {},
  embeddedSchemas: {},
  getId: _printDataLogModelGetId,
  getLinks: _printDataLogModelGetLinks,
  attach: _printDataLogModelAttach,
  version: '3.1.0+1',
);

int _printDataLogModelEstimateSize(
  PrintDataLogModel object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.addTime;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.allTimes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.applicationVersion;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.city;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cloudTemplateId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.country;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.deviceId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.device_id_dot;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.district;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.extraData;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.firmwareVersion;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.hardwareVersion;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.height;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.illegalCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.latitude;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.longitude;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.macNo;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.machineId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.number;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.oneCode;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.paperLengthUsedQuantity;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.paperLengthUsedQuantitySum;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.phoneBrand;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.printChannel;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.printStrategy;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.printStyle;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.printedProcess;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.probationPrivilege;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.province;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.rfidPrintNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.rfidSerialNumber;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.sourceId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.street;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.successTimes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.systemType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.systemVersion;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.templeteId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.uniqueValue;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.width;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _printDataLogModelSerialize(
  PrintDataLogModel object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.addTime);
  writer.writeString(offsets[1], object.allTimes);
  writer.writeString(offsets[2], object.applicationVersion);
  writer.writeString(offsets[3], object.city);
  writer.writeString(offsets[4], object.cloudTemplateId);
  writer.writeLong(offsets[5], object.commodityCount);
  writer.writeString(offsets[6], object.country);
  writer.writeString(offsets[7], object.deviceId);
  writer.writeLong(offsets[8], object.deviceRegionCode);
  writer.writeString(offsets[9], object.device_id_dot);
  writer.writeString(offsets[10], object.district);
  writer.writeString(offsets[11], object.extraData);
  writer.writeString(offsets[12], object.firmwareVersion);
  writer.writeString(offsets[13], object.hardwareVersion);
  writer.writeString(offsets[14], object.height);
  writer.writeString(offsets[15], object.illegalCode);
  writer.writeBool(offsets[16], object.isCloudTemplate);
  writer.writeString(offsets[17], object.latitude);
  writer.writeString(offsets[18], object.longitude);
  writer.writeString(offsets[19], object.macNo);
  writer.writeString(offsets[20], object.machineId);
  writer.writeBool(offsets[21], object.machineStatus);
  writer.writeString(offsets[22], object.number);
  writer.writeBool(offsets[23], object.offlinePrint);
  writer.writeString(offsets[24], object.oneCode);
  writer.writeString(offsets[25], object.paperLengthUsedQuantity);
  writer.writeString(offsets[26], object.paperLengthUsedQuantitySum);
  writer.writeString(offsets[27], object.phoneBrand);
  writer.writeString(offsets[28], object.printChannel);
  writer.writeLong(offsets[29], object.printFinishTime);
  writer.writeString(offsets[30], object.printStrategy);
  writer.writeString(offsets[31], object.printStyle);
  writer.writeLong(offsets[32], object.printType);
  writer.writeString(offsets[33], object.printedProcess);
  writer.writeString(offsets[34], object.probationPrivilege);
  writer.writeString(offsets[35], object.province);
  writer.writeLong(offsets[36], object.recordSource);
  writer.writeLong(offsets[37], object.recordType);
  writer.writeString(offsets[38], object.rfidPrintNumber);
  writer.writeString(offsets[39], object.rfidSerialNumber);
  writer.writeDouble(offsets[40], object.ribbonUsed);
  writer.writeString(offsets[41], object.sourceId);
  writer.writeLong(offsets[42], object.status);
  writer.writeString(offsets[43], object.street);
  writer.writeString(offsets[44], object.successTimes);
  writer.writeString(offsets[45], object.systemType);
  writer.writeString(offsets[46], object.systemVersion);
  writer.writeString(offsets[47], object.templeteId);
  writer.writeString(offsets[48], object.uniqueValue);
  writer.writeLong(offsets[49], object.userId);
  writer.writeString(offsets[50], object.width);
}

PrintDataLogModel _printDataLogModelDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = PrintDataLogModel();
  object.addTime = reader.readStringOrNull(offsets[0]);
  object.allTimes = reader.readStringOrNull(offsets[1]);
  object.applicationVersion = reader.readStringOrNull(offsets[2]);
  object.city = reader.readStringOrNull(offsets[3]);
  object.cloudTemplateId = reader.readStringOrNull(offsets[4]);
  object.commodityCount = reader.readLongOrNull(offsets[5]);
  object.country = reader.readStringOrNull(offsets[6]);
  object.deviceId = reader.readStringOrNull(offsets[7]);
  object.deviceRegionCode = reader.readLongOrNull(offsets[8]);
  object.device_id_dot = reader.readStringOrNull(offsets[9]);
  object.district = reader.readStringOrNull(offsets[10]);
  object.extraData = reader.readStringOrNull(offsets[11]);
  object.firmwareVersion = reader.readStringOrNull(offsets[12]);
  object.hardwareVersion = reader.readStringOrNull(offsets[13]);
  object.height = reader.readStringOrNull(offsets[14]);
  object.id = id;
  object.illegalCode = reader.readStringOrNull(offsets[15]);
  object.isCloudTemplate = reader.readBoolOrNull(offsets[16]);
  object.latitude = reader.readStringOrNull(offsets[17]);
  object.longitude = reader.readStringOrNull(offsets[18]);
  object.macNo = reader.readStringOrNull(offsets[19]);
  object.machineId = reader.readStringOrNull(offsets[20]);
  object.machineStatus = reader.readBoolOrNull(offsets[21]);
  object.number = reader.readStringOrNull(offsets[22]);
  object.offlinePrint = reader.readBoolOrNull(offsets[23]);
  object.oneCode = reader.readStringOrNull(offsets[24]);
  object.paperLengthUsedQuantity = reader.readStringOrNull(offsets[25]);
  object.paperLengthUsedQuantitySum = reader.readStringOrNull(offsets[26]);
  object.phoneBrand = reader.readStringOrNull(offsets[27]);
  object.printChannel = reader.readStringOrNull(offsets[28]);
  object.printFinishTime = reader.readLongOrNull(offsets[29]);
  object.printStrategy = reader.readStringOrNull(offsets[30]);
  object.printStyle = reader.readStringOrNull(offsets[31]);
  object.printType = reader.readLongOrNull(offsets[32]);
  object.printedProcess = reader.readStringOrNull(offsets[33]);
  object.probationPrivilege = reader.readStringOrNull(offsets[34]);
  object.province = reader.readStringOrNull(offsets[35]);
  object.recordSource = reader.readLongOrNull(offsets[36]);
  object.recordType = reader.readLongOrNull(offsets[37]);
  object.rfidPrintNumber = reader.readStringOrNull(offsets[38]);
  object.rfidSerialNumber = reader.readStringOrNull(offsets[39]);
  object.ribbonUsed = reader.readDoubleOrNull(offsets[40]);
  object.sourceId = reader.readStringOrNull(offsets[41]);
  object.status = reader.readLongOrNull(offsets[42]);
  object.street = reader.readStringOrNull(offsets[43]);
  object.successTimes = reader.readStringOrNull(offsets[44]);
  object.systemType = reader.readStringOrNull(offsets[45]);
  object.systemVersion = reader.readStringOrNull(offsets[46]);
  object.templeteId = reader.readStringOrNull(offsets[47]);
  object.uniqueValue = reader.readStringOrNull(offsets[48]);
  object.userId = reader.readLongOrNull(offsets[49]);
  object.width = reader.readStringOrNull(offsets[50]);
  return object;
}

P _printDataLogModelDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readStringOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readStringOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readStringOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readBoolOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (reader.readStringOrNull(offset)) as P;
    case 21:
      return (reader.readBoolOrNull(offset)) as P;
    case 22:
      return (reader.readStringOrNull(offset)) as P;
    case 23:
      return (reader.readBoolOrNull(offset)) as P;
    case 24:
      return (reader.readStringOrNull(offset)) as P;
    case 25:
      return (reader.readStringOrNull(offset)) as P;
    case 26:
      return (reader.readStringOrNull(offset)) as P;
    case 27:
      return (reader.readStringOrNull(offset)) as P;
    case 28:
      return (reader.readStringOrNull(offset)) as P;
    case 29:
      return (reader.readLongOrNull(offset)) as P;
    case 30:
      return (reader.readStringOrNull(offset)) as P;
    case 31:
      return (reader.readStringOrNull(offset)) as P;
    case 32:
      return (reader.readLongOrNull(offset)) as P;
    case 33:
      return (reader.readStringOrNull(offset)) as P;
    case 34:
      return (reader.readStringOrNull(offset)) as P;
    case 35:
      return (reader.readStringOrNull(offset)) as P;
    case 36:
      return (reader.readLongOrNull(offset)) as P;
    case 37:
      return (reader.readLongOrNull(offset)) as P;
    case 38:
      return (reader.readStringOrNull(offset)) as P;
    case 39:
      return (reader.readStringOrNull(offset)) as P;
    case 40:
      return (reader.readDoubleOrNull(offset)) as P;
    case 41:
      return (reader.readStringOrNull(offset)) as P;
    case 42:
      return (reader.readLongOrNull(offset)) as P;
    case 43:
      return (reader.readStringOrNull(offset)) as P;
    case 44:
      return (reader.readStringOrNull(offset)) as P;
    case 45:
      return (reader.readStringOrNull(offset)) as P;
    case 46:
      return (reader.readStringOrNull(offset)) as P;
    case 47:
      return (reader.readStringOrNull(offset)) as P;
    case 48:
      return (reader.readStringOrNull(offset)) as P;
    case 49:
      return (reader.readLongOrNull(offset)) as P;
    case 50:
      return (reader.readStringOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _printDataLogModelGetId(PrintDataLogModel object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _printDataLogModelGetLinks(
    PrintDataLogModel object) {
  return [];
}

void _printDataLogModelAttach(
    IsarCollection<dynamic> col, Id id, PrintDataLogModel object) {
  object.id = id;
}

extension PrintDataLogModelQueryWhereSort
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QWhere> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension PrintDataLogModelQueryWhere
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QWhereClause> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension PrintDataLogModelQueryFilter
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QFilterCondition> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'addTime',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'addTime',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'addTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'addTime',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'addTime',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'addTime',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      addTimeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'addTime',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'allTimes',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'allTimes',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'allTimes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'allTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'allTimes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'allTimes',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      allTimesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'allTimes',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'applicationVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'applicationVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'applicationVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'applicationVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'applicationVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'applicationVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      applicationVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'applicationVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'city',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'city',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'city',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'city',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'city',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'city',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cityIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'city',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cloudTemplateId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cloudTemplateId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cloudTemplateId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cloudTemplateId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cloudTemplateId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cloudTemplateId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      cloudTemplateIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cloudTemplateId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'commodityCount',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'commodityCount',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'commodityCount',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'commodityCount',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'commodityCount',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      commodityCountBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'commodityCount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'country',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'country',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'country',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'country',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'country',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'country',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      countryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'country',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'deviceId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'deviceId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'deviceId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'deviceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'deviceId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deviceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'deviceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'deviceRegionCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'deviceRegionCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'deviceRegionCode',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'deviceRegionCode',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'deviceRegionCode',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      deviceRegionCodeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'deviceRegionCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'device_id_dot',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'device_id_dot',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'device_id_dot',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'device_id_dot',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'device_id_dot',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'device_id_dot',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      device_id_dotIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'device_id_dot',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'district',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'district',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'district',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'district',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'district',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'district',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      districtIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'district',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'extraData',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'extraData',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'extraData',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'extraData',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'extraData',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'extraData',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      extraDataIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'extraData',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'firmwareVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'firmwareVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'firmwareVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'firmwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'firmwareVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'firmwareVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      firmwareVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'firmwareVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'hardwareVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'hardwareVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hardwareVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'hardwareVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'hardwareVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hardwareVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      hardwareVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'hardwareVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'height',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'height',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'height',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'height',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'height',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      heightIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'height',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'illegalCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'illegalCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'illegalCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'illegalCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'illegalCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'illegalCode',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      illegalCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'illegalCode',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      isCloudTemplateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'isCloudTemplate',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      isCloudTemplateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'isCloudTemplate',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      isCloudTemplateEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isCloudTemplate',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'latitude',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'latitude',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'latitude',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'latitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'latitude',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'latitude',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      latitudeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'latitude',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'longitude',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'longitude',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'longitude',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'longitude',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'longitude',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'longitude',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      longitudeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'longitude',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'macNo',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'macNo',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'macNo',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'macNo',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'macNo',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'macNo',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      macNoIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'macNo',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'machineId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'machineId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'machineId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'machineId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'machineId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'machineId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineStatusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'machineStatus',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineStatusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'machineStatus',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      machineStatusEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'machineStatus',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'number',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'number',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'number',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'number',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'number',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'number',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      numberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'number',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      offlinePrintIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'offlinePrint',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      offlinePrintIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'offlinePrint',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      offlinePrintEqualTo(bool? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'offlinePrint',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'oneCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'oneCode',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'oneCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'oneCode',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'oneCode',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'oneCode',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      oneCodeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'oneCode',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'paperLengthUsedQuantity',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'paperLengthUsedQuantity',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'paperLengthUsedQuantity',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'paperLengthUsedQuantity',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'paperLengthUsedQuantity',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperLengthUsedQuantity',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantityIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'paperLengthUsedQuantity',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'paperLengthUsedQuantitySum',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'paperLengthUsedQuantitySum',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'paperLengthUsedQuantitySum',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'paperLengthUsedQuantitySum',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'paperLengthUsedQuantitySum',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'paperLengthUsedQuantitySum',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      paperLengthUsedQuantitySumIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'paperLengthUsedQuantitySum',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'phoneBrand',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'phoneBrand',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'phoneBrand',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'phoneBrand',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'phoneBrand',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'phoneBrand',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      phoneBrandIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'phoneBrand',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printChannel',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printChannel',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printChannel',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'printChannel',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'printChannel',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printChannel',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printChannelIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'printChannel',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printFinishTime',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printFinishTime',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printFinishTime',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printFinishTime',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printFinishTime',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printFinishTimeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printFinishTime',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printStrategy',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printStrategy',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printStrategy',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'printStrategy',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'printStrategy',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printStrategy',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStrategyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'printStrategy',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printStyle',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printStyle',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printStyle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'printStyle',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'printStyle',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printStyle',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printStyleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'printStyle',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'printedProcess',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'printedProcess',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'printedProcess',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'printedProcess',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'printedProcess',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'printedProcess',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      printedProcessIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'printedProcess',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'probationPrivilege',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'probationPrivilege',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'probationPrivilege',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'probationPrivilege',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'probationPrivilege',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'probationPrivilege',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      probationPrivilegeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'probationPrivilege',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'province',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'province',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'province',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'province',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'province',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'province',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      provinceIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'province',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recordSource',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recordSource',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordSource',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordSource',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordSource',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordSourceBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordSource',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recordType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recordType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordType',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      recordTypeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'rfidPrintNumber',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'rfidPrintNumber',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'rfidPrintNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'rfidPrintNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'rfidPrintNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rfidPrintNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidPrintNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'rfidPrintNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'rfidSerialNumber',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'rfidSerialNumber',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'rfidSerialNumber',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'rfidSerialNumber',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'rfidSerialNumber',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'rfidSerialNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      rfidSerialNumberIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'rfidSerialNumber',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'ribbonUsed',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'ribbonUsed',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'ribbonUsed',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'ribbonUsed',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'ribbonUsed',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      ribbonUsedBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'ribbonUsed',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'sourceId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'sourceId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'sourceId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'sourceId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'sourceId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'sourceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      sourceIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'sourceId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'status',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'status',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      statusBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'status',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'street',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'street',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'street',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'street',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'street',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'street',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      streetIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'street',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'successTimes',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'successTimes',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'successTimes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'successTimes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'successTimes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'successTimes',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      successTimesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'successTimes',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'systemType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'systemType',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'systemType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'systemType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'systemType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'systemType',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'systemType',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'systemVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'systemVersion',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'systemVersion',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'systemVersion',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'systemVersion',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'systemVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      systemVersionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'systemVersion',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'templeteId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'templeteId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'templeteId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'templeteId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'templeteId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'templeteId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      templeteIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'templeteId',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'uniqueValue',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'uniqueValue',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'uniqueValue',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'uniqueValue',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'uniqueValue',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'uniqueValue',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      uniqueValueIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'uniqueValue',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'userId',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'userId',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'userId',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'userId',
        value: value,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      userIdBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'userId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'width',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'width',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'width',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'width',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'width',
        value: '',
      ));
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterFilterCondition>
      widthIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'width',
        value: '',
      ));
    });
  }
}

extension PrintDataLogModelQueryObject
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QFilterCondition> {}

extension PrintDataLogModelQueryLinks
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QFilterCondition> {}

extension PrintDataLogModelQuerySortBy
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QSortBy> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByAddTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addTime', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByAddTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addTime', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByAllTimes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allTimes', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByAllTimesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allTimes', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByApplicationVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'applicationVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByApplicationVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'applicationVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCloudTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudTemplateId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCloudTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudTemplateId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCommodityCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCount', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCommodityCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCount', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCountry() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByCountryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDeviceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDeviceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDeviceRegionCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceRegionCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDeviceRegionCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceRegionCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDevice_id_dot() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'device_id_dot', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDevice_id_dotDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'device_id_dot', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDistrict() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'district', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByDistrictDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'district', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByExtraData() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extraData', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByExtraDataDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extraData', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByFirmwareVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firmwareVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByFirmwareVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firmwareVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByHardwareVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByHardwareVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByIllegalCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'illegalCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByIllegalCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'illegalCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByIsCloudTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudTemplate', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByIsCloudTemplateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudTemplate', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByLatitude() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'latitude', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByLatitudeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'latitude', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByLongitude() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'longitude', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByLongitudeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'longitude', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMacNo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'macNo', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMacNoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'macNo', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMachineId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMachineIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMachineStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineStatus', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByMachineStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineStatus', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'number', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'number', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByOfflinePrint() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'offlinePrint', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByOfflinePrintDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'offlinePrint', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByOneCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'oneCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByOneCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'oneCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPaperLengthUsedQuantity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantity', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPaperLengthUsedQuantityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantity', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPaperLengthUsedQuantitySum() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantitySum', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPaperLengthUsedQuantitySumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantitySum', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPhoneBrand() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneBrand', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPhoneBrandDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneBrand', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintChannel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printChannel', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintChannelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printChannel', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintFinishTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printFinishTime', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintFinishTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printFinishTime', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintStrategy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStrategy', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintStrategyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStrategy', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintStyle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStyle', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintStyleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStyle', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintedProcess() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printedProcess', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByPrintedProcessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printedProcess', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByProbationPrivilege() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'probationPrivilege', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByProbationPrivilegeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'probationPrivilege', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByProvince() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'province', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByProvinceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'province', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRecordSource() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordSource', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRecordSourceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordSource', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRecordType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRecordTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRfidPrintNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidPrintNumber', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRfidPrintNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidPrintNumber', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRfidSerialNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidSerialNumber', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRfidSerialNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidSerialNumber', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRibbonUsed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'ribbonUsed', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByRibbonUsedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'ribbonUsed', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySourceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySourceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByStreet() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'street', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByStreetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'street', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySuccessTimes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successTimes', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySuccessTimesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successTimes', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySystemType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySystemTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySystemVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortBySystemVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByTempleteId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templeteId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByTempleteIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templeteId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByUniqueValue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'uniqueValue', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByUniqueValueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'uniqueValue', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByWidth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      sortByWidthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.desc);
    });
  }
}

extension PrintDataLogModelQuerySortThenBy
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QSortThenBy> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByAddTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addTime', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByAddTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'addTime', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByAllTimes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allTimes', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByAllTimesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'allTimes', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByApplicationVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'applicationVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByApplicationVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'applicationVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'city', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCloudTemplateId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudTemplateId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCloudTemplateIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cloudTemplateId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCommodityCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCount', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCommodityCountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'commodityCount', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCountry() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByCountryDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'country', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDeviceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDeviceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDeviceRegionCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceRegionCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDeviceRegionCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'deviceRegionCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDevice_id_dot() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'device_id_dot', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDevice_id_dotDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'device_id_dot', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDistrict() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'district', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByDistrictDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'district', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByExtraData() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extraData', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByExtraDataDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'extraData', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByFirmwareVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firmwareVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByFirmwareVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'firmwareVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByHardwareVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByHardwareVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hardwareVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByHeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByHeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'height', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByIllegalCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'illegalCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByIllegalCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'illegalCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByIsCloudTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudTemplate', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByIsCloudTemplateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isCloudTemplate', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByLatitude() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'latitude', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByLatitudeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'latitude', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByLongitude() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'longitude', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByLongitudeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'longitude', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMacNo() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'macNo', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMacNoDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'macNo', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMachineId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMachineIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMachineStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineStatus', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByMachineStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'machineStatus', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'number', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'number', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByOfflinePrint() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'offlinePrint', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByOfflinePrintDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'offlinePrint', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByOneCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'oneCode', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByOneCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'oneCode', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPaperLengthUsedQuantity() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantity', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPaperLengthUsedQuantityDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantity', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPaperLengthUsedQuantitySum() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantitySum', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPaperLengthUsedQuantitySumDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'paperLengthUsedQuantitySum', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPhoneBrand() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneBrand', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPhoneBrandDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'phoneBrand', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintChannel() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printChannel', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintChannelDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printChannel', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintFinishTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printFinishTime', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintFinishTimeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printFinishTime', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintStrategy() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStrategy', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintStrategyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStrategy', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintStyle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStyle', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintStyleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printStyle', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintedProcess() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printedProcess', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByPrintedProcessDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'printedProcess', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByProbationPrivilege() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'probationPrivilege', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByProbationPrivilegeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'probationPrivilege', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByProvince() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'province', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByProvinceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'province', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRecordSource() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordSource', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRecordSourceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordSource', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRecordType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRecordTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRfidPrintNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidPrintNumber', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRfidPrintNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidPrintNumber', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRfidSerialNumber() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidSerialNumber', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRfidSerialNumberDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'rfidSerialNumber', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRibbonUsed() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'ribbonUsed', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByRibbonUsedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'ribbonUsed', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySourceId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySourceIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'sourceId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByStatusDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'status', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByStreet() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'street', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByStreetDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'street', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySuccessTimes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successTimes', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySuccessTimesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successTimes', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySystemType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemType', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySystemTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemType', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySystemVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemVersion', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenBySystemVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'systemVersion', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByTempleteId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templeteId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByTempleteIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'templeteId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByUniqueValue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'uniqueValue', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByUniqueValueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'uniqueValue', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByUserIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'userId', Sort.desc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByWidth() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.asc);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QAfterSortBy>
      thenByWidthDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'width', Sort.desc);
    });
  }
}

extension PrintDataLogModelQueryWhereDistinct
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct> {
  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByAddTime({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'addTime', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByAllTimes({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'allTimes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByApplicationVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'applicationVersion',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct> distinctByCity(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'city', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByCloudTemplateId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cloudTemplateId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByCommodityCount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'commodityCount');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByCountry({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'country', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByDeviceId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'deviceId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByDeviceRegionCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'deviceRegionCode');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByDevice_id_dot({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'device_id_dot',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByDistrict({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'district', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByExtraData({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'extraData', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByFirmwareVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'firmwareVersion',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByHardwareVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hardwareVersion',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByHeight({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'height', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByIllegalCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'illegalCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByIsCloudTemplate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isCloudTemplate');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByLatitude({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'latitude', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByLongitude({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'longitude', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct> distinctByMacNo(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'macNo', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByMachineId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'machineId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByMachineStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'machineStatus');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'number', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByOfflinePrint() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'offlinePrint');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByOneCode({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'oneCode', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPaperLengthUsedQuantity({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'paperLengthUsedQuantity',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPaperLengthUsedQuantitySum({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'paperLengthUsedQuantitySum',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPhoneBrand({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'phoneBrand', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintChannel({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printChannel', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintFinishTime() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printFinishTime');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintStrategy({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printStrategy',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintStyle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printStyle', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printType');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByPrintedProcess({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'printedProcess',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByProbationPrivilege({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'probationPrivilege',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByProvince({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'province', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByRecordSource() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordSource');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByRecordType() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordType');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByRfidPrintNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'rfidPrintNumber',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByRfidSerialNumber({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'rfidSerialNumber',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByRibbonUsed() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'ribbonUsed');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctBySourceId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'sourceId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByStatus() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'status');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByStreet({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'street', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctBySuccessTimes({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'successTimes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctBySystemType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'systemType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctBySystemVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'systemVersion',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByTempleteId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'templeteId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByUniqueValue({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'uniqueValue', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct>
      distinctByUserId() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'userId');
    });
  }

  QueryBuilder<PrintDataLogModel, PrintDataLogModel, QDistinct> distinctByWidth(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'width', caseSensitive: caseSensitive);
    });
  }
}

extension PrintDataLogModelQueryProperty
    on QueryBuilder<PrintDataLogModel, PrintDataLogModel, QQueryProperty> {
  QueryBuilder<PrintDataLogModel, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> addTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'addTime');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      allTimesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'allTimes');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      applicationVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'applicationVersion');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> cityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'city');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      cloudTemplateIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cloudTemplateId');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations>
      commodityCountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'commodityCount');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> countryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'country');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      deviceIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'deviceId');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations>
      deviceRegionCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'deviceRegionCode');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      device_id_dotProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'device_id_dot');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      districtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'district');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      extraDataProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'extraData');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      firmwareVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'firmwareVersion');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      hardwareVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hardwareVersion');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> heightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'height');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      illegalCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'illegalCode');
    });
  }

  QueryBuilder<PrintDataLogModel, bool?, QQueryOperations>
      isCloudTemplateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isCloudTemplate');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      latitudeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'latitude');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      longitudeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'longitude');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> macNoProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'macNo');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      machineIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'machineId');
    });
  }

  QueryBuilder<PrintDataLogModel, bool?, QQueryOperations>
      machineStatusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'machineStatus');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> numberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'number');
    });
  }

  QueryBuilder<PrintDataLogModel, bool?, QQueryOperations>
      offlinePrintProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'offlinePrint');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> oneCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'oneCode');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      paperLengthUsedQuantityProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'paperLengthUsedQuantity');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      paperLengthUsedQuantitySumProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'paperLengthUsedQuantitySum');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      phoneBrandProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'phoneBrand');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      printChannelProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printChannel');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations>
      printFinishTimeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printFinishTime');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      printStrategyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printStrategy');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      printStyleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printStyle');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations> printTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printType');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      printedProcessProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'printedProcess');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      probationPrivilegeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'probationPrivilege');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      provinceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'province');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations>
      recordSourceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordSource');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations> recordTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordType');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      rfidPrintNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'rfidPrintNumber');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      rfidSerialNumberProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'rfidSerialNumber');
    });
  }

  QueryBuilder<PrintDataLogModel, double?, QQueryOperations>
      ribbonUsedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'ribbonUsed');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      sourceIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'sourceId');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations> statusProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'status');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> streetProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'street');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      successTimesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'successTimes');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      systemTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'systemType');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      systemVersionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'systemVersion');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      templeteIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'templeteId');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations>
      uniqueValueProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'uniqueValue');
    });
  }

  QueryBuilder<PrintDataLogModel, int?, QQueryOperations> userIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'userId');
    });
  }

  QueryBuilder<PrintDataLogModel, String?, QQueryOperations> widthProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'width');
    });
  }
}
