//
//  XYWKWebViewController.m
//  Runner
//
//  Created by xy on 2018/6/13.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

 
#import "JCVIPDetailViewController.h"
#import "JCWebViewMoreAlert.h"
#import <WebKit/WebKit.h>
#import <AlipaySDK/AlipaySDK.h>
#import "sys/utsname.h"
#import "WXApi.h"
#import "JCLabelApplicationViewController.h"
#import "NMWebViewController.h"
#import "JCShopNormalVC.h"

//设备高宽
#define kScreenWidth        ([UIScreen mainScreen].bounds.size.width)
#define kScreenHeight       ([UIScreen mainScreen].bounds.size.height)


//跳转界面
#import "JCShopAliPay.h"

//#define SafeAreaTopHeight (kScreenHeight == 812.0 ? 45 : 20)
@interface XYWKWebViewController ()<WKUIDelegate,WKNavigationDelegate,UIGestureRecognizerDelegate>

@property(nonatomic,strong) UIView *networkErrorView;
@property(nonatomic,strong) NSString *shareUrl;
@property(nonatomic,strong) NSArray *ScriptHandlerNameArr;
@property(nonatomic,strong) UIButton *backBtn;
@end

@implementation XYWKWebViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.isSupportShare = YES;
        self.showWebTitle = NO;
        self.statusBackColor = 0xFFFFFF;
        self.configuration = [[WKWebViewConfiguration alloc] init];
        self.configuration.userContentController = [WKUserContentController new];
        WKPreferences *preferences = [WKPreferences new];
        preferences.javaScriptCanOpenWindowsAutomatically = YES;
        self.configuration.preferences = preferences;
        self.webView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight - (self.isFullScreen ? 0 : (iPhoneX?88:64))) configuration:self.configuration];
        self.webView.scrollView.scrollEnabled = YES;
        self.webView.navigationDelegate = self;
        self.webView.UIDelegate = self;
        [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionNew context:NULL];
        [self.webView addObserver:self forKeyPath:@"title" options:NSKeyValueObservingOptionNew context:NULL];
        if(@available(iOS 16.4, *)){
//            self.webView.inspectable = true;
        }
        self.progressView = [[UIProgressView alloc]initWithFrame:CGRectMake(0, 0,kScreenWidth, 2)];
        CGAffineTransform transform = CGAffineTransformMakeScale(1.0f, 2.0f);
        self.progressView.transform = transform;//设定宽高
        self.progressView.progressTintColor = COLOR_BLUE;
        [self resetWKWebViewUA];
    }
    return self;
}

- (instancetype)initWithUrl:(NSString *)url{
    self = [super init];
    if (self) {
        self.statusBackColor = 0xFFFFFF;
        NSString *token = STR_IS_NIL(m_userModel.token)?@"":[NSString stringWithFormat:@"%@ %@",@"bearer",m_userModel.token];
        NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
        if (!STR_IS_NIL(url)) {
          NSDictionary * params = [XYTool dictionaryWithUrlString:url];
            if ([params objectForKey:@"fullSceen"] != nil && [[params objectForKey:@"fullSceen"] isEqualToString:@"1"]) {
                self.isFullScreen = true;
            }
        }
        
        if([url rangeOfString:@"?"].location != NSNotFound){
            url = [NSString stringWithFormat:@"%@&lang=%@",url,XY_JC_LANGUAGE_REAL];
        }else{
            url = [NSString stringWithFormat:@"%@?lang=%@" ,url,XY_JC_LANGUAGE_REAL];
        }
        userAgent = [userAgent stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
        url = [NSString stringWithFormat:@"%@&userAgent=%@" ,url,userAgent];
        self.shareUrl = url;
        if(!STR_IS_NIL(token)){
            token = [token stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
            url = [NSString stringWithFormat:@"%@&token=%@",url,token];
        }
        if([[JCBluetoothManager sharedInstance] getConnectState]){
            NSString *printerName = JC_CURRENT_CONNECTED_PRINTER;
            NSString * machineId = [self getMachineIdWithPrintName:printerName];
            url = [NSString stringWithFormat:@"%@&machineId=%@",url,machineId];
        }else{
            //新版本全屏h5页面不需要判断机器链接状态 直接取上次链接的机器
            if (self.isFullScreen) {
                NSString *printerName = [[NSUserDefaults standardUserDefaults] valueForKey:CENTERLASTCONNECTPRINTERNAME];
                NSString * machineId = [self getMachineIdWithPrintName:printerName];
                url = [NSString stringWithFormat:@"%@&machineId=%@",url,machineId ?: @""];
            }
        }
        
        url = [NSString stringWithFormat:@"%@&pay_channel=ApplePay" ,url];
        self.url = url;
        self.configuration = [[WKWebViewConfiguration alloc] init];
        self.configuration.userContentController = [WKUserContentController new];
        WKPreferences *preferences = [WKPreferences new];
        preferences.javaScriptCanOpenWindowsAutomatically = YES;
        self.configuration.preferences = preferences;
        self.webView = [[WKWebView alloc]initWithFrame:CGRectMake(0, 0, kScreenWidth, kScreenHeight - (self.isFullScreen ? 0 :  (iPhoneX?88:64))) configuration:self.configuration];
        self.webView.scrollView.scrollEnabled = YES;
        self.webView.navigationDelegate = self;
        self.webView.UIDelegate = self;
        [self.webView addObserver:self forKeyPath:@"estimatedProgress" options:NSKeyValueObservingOptionOld | NSKeyValueObservingOptionNew context:nil];
        [self.webView addObserver:self forKeyPath:@"title" options:NSKeyValueObservingOptionNew context:NULL];
        if(@available(iOS 16.4, *)){
//            self.webView.inspectable = true;
        }
        self.progressView = [[UIProgressView alloc]initWithFrame:CGRectMake(0, 0,kScreenWidth, 2)];
        CGAffineTransform transform = CGAffineTransformMakeScale(1.0f, 2.0f);
        self.progressView.transform = transform;//设定宽高
        self.progressView.progressTintColor = HEX_RGB(0x537FB7);
        [self resetWKWebViewUA];
        // 熔断显示错误视图
        if(m_currentServerState == ServerState_Stop) {
          self.networkErrorView.hidden = NO;
        }
    }
    return self;
}

-(NSString *)getMachineIdWithPrintName:(NSString *)printName{
    NSString *printerName = printName;
    NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:YES];
    NSString *wherString = [NSString stringWithFormat:@"where name = '%@'",printType];
    NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:wherString];
    NSString *machineID = nil;
    if(printerInfoArr.count > 0){
        JCPrinterModel *printerModel = printerInfoArr[0];
        machineID = printerModel.xyid;
    }
    return machineID;
}

- (void)loadUrl:(NSString *)url
{
    [self loadUrl:url isNeedToken:YES];
}

- (void)loadUrl:(NSString *)url isNeedToken:(BOOL)isNeedToken{
    
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    if([url rangeOfString:@"?"].location != NSNotFound){
        url = [NSString stringWithFormat:@"%@&lang=%@",url,XY_JC_LANGUAGE_REAL];
    }else{
        url = [NSString stringWithFormat:@"%@?lang=%@" ,url,XY_JC_LANGUAGE_REAL];
    }
    
    userAgent = [userAgent stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
    url = [NSString stringWithFormat:@"%@&pay_channel=ApplePay&userAgent=%@" ,url,userAgent];
    self.shareUrl = url;
    if(isNeedToken){
        NSString *token = STR_IS_NIL(m_userModel.token)?@"":[NSString stringWithFormat:@"%@ %@",@"bearer",m_userModel.token];
        if(!STR_IS_NIL(token)){
            token = [token stringByAddingPercentEncodingWithAllowedCharacters:[NSCharacterSet URLQueryAllowedCharacterSet]];
            url = [NSString stringWithFormat:@"%@&token=%@",url,token];
        }
    }
    
    self.url = url;
    [self.progressView setAlpha:1.0f];
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:url]  cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20]];
    [self.webView reload];
}

- (void)leftButtonTouch:(UIButton *)sender {
    if (!self.webView) {
        [self performNativeBack];
        return;
    }

    // 检查是否定义了 navigateBack
    [self.webView evaluateJavaScript:@"typeof navigateBack !== 'undefined'" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        if (error || ![result isKindOfClass:[NSNumber class]] || ![result boolValue]) {
            NSLog(@"navigateBack 不存在或 JS 执行出错：%@", error.localizedDescription);
            [self performNativeBack];
        } else {
            NSLog(@"navigateBack 存在，尝试调用...");

            // 调用 navigateBack()
            [self.webView evaluateJavaScript:@"navigateBack()" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                if (error || ![result isKindOfClass:[NSNumber class]] || ![result boolValue]) {
                    NSLog(@"navigateBack 执行失败或返回非 true：%@", error.localizedDescription);
                    [self performNativeBack];
                } else {
                    NSLog(@"navigateBack 执行成功：%@", result);
                    // JS 成功处理返回，无需做原生操作
                }
            }];
        }
    }];
}

- (void)performNativeBack {
    if ([self.webView canGoBack]) {
        [self.webView goBack];
    } else {
        if (self.navigationController.viewControllers.count <= 1) {
            [self.navigationController dismissViewControllerAnimated:YES completion:nil];
        } else {
            [self.navigationController popViewControllerAnimated:YES];
        }

        if (self.backBlock) {
            self.backBlock();
        }
    }
}

- (void)initNavigationBar{
    if(self.isSupportShare){
        [self showBarButton:NAV_RIGHT imageName:@"rightMoreBtn"];
    }
}

- (void)rightButtonTouch:(UIButton *)sender{
    [super rightButtonTouch:sender];
    NSString *graphqlStr = @"mutation createShorty($update: ShortyDto!) {\n createShorty(update: $update)\n{\nshortUrl\n}\n}\n";
    [@{@"update":@{@"url":UN_NIL(self.shareUrl)}} jc_graphQLRequestWith:graphqlStr hud:@"" graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
        NSDictionary *shortUrlDic = [requestDic objectForKey:@"createShorty"];
        NSString *shortUrl = [shortUrlDic objectForKey:@"shortUrl"];
        if(STR_IS_NIL(shortUrl)) return;
        JCWebViewMoreAlert *moreAlert = [[JCWebViewMoreAlert alloc] initWithContentString:shortUrl];
        [moreAlert setOperateBlock:^(id x) {
//            [self shareContentToWeChat:@"微信分享" linkUrl:@"https://www.baidu.com" description:@"百度分享"];
        }];
        [moreAlert showContentAlert];
    } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    }];
}

- (UIView *)networkErrorView{
    if(!_networkErrorView){
        _networkErrorView = [[UIView alloc] init];
        _networkErrorView.backgroundColor = HEX_RGB(0xE5E5E5);
        _networkErrorView.hidden = YES;
        UIImageView *networkErrImage = [[UIImageView alloc] init];
        networkErrImage.image = XY_IMAGE_NAMED(@"shopNetworkErr");
        [_networkErrorView addSubview:networkErrImage];
        
        UILabel *networkErrTipLabel = [[UILabel alloc] init];
        networkErrTipLabel.font = MY_FONT_Regular(14);
        networkErrTipLabel.numberOfLines = 0;
        networkErrTipLabel.textColor = HEX_RGB(0x666666);
        networkErrTipLabel.textAlignment = NSTextAlignmentCenter;
        networkErrTipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100000121", @"当前网络状态不佳，请稍后再试");
        [_networkErrorView addSubview:networkErrTipLabel];
        
        UIButton *refreshBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        refreshBtn.backgroundColor = HEX_RGB(0xFFFFFF);
        refreshBtn.layer.cornerRadius = 20;
        refreshBtn.titleLabel.font = MY_FONT_Regular(16);
        [refreshBtn setTitleColor:COLOR_NEW_THEME forState:UIControlStateNormal];
        [refreshBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") forState:UIControlStateNormal];
        [refreshBtn  addTarget:self action:@selector(refreshCurrentWeb) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:refreshBtn];
        
        UIButton *backBtn = [[UIButton alloc] initNavigationButton:[[UIImage imageNamed:@"search_bar_back"] imageFlippedForRightToLeftLayoutDirection]];
     
        [backBtn addTarget:self action:@selector(leftButtonTouch:) forControlEvents:UIControlEventTouchUpInside];
        [_networkErrorView addSubview:backBtn];
      
      BOOL noNav = self.navigationController == nil || self.navigationController.isNavigationBarHidden;
      backBtn.hidden = !noNav;
      self.backBtn = backBtn;
          
      CGFloat statusBarHeight = UIApplication.sharedApplication.statusBarFrame.size.height;
      CGFloat navBarHeight = 44.0; // 系统导航栏高度
      CGFloat buttonSize = 30.0;

      // 让按钮在原来导航栏中垂直居中
      CGFloat topOffset = statusBarHeight + (navBarHeight - buttonSize) / 2.0;

      [backBtn mas_makeConstraints:^(MASConstraintMaker *make) {
          make.top.equalTo(_networkErrorView.mas_top).offset(topOffset);
          make.leading.mas_equalTo(_networkErrorView.mas_leading).offset(20);
          make.size.mas_equalTo(CGSizeMake(buttonSize, buttonSize));
      }];
        
        [networkErrImage mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_networkErrorView);
            make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
            make.height.mas_equalTo(networkErrImage.mas_width).multipliedBy(0.5);
            make.centerY.equalTo(_networkErrorView).offset(-100);
        }];
        [networkErrTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(_networkErrorView);
            make.width.mas_equalTo(@(SCREEN_WIDTH - XY_AutoWidth(100)));
            make.top.equalTo(networkErrImage.mas_bottom).offset(35);
        }];
        
        float btnTitleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app100000048", @"重新加载") jk_sizeWithFont:MY_FONT_Regular(16) constrainedToWidth:1000].width;
        [refreshBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.width.mas_equalTo(btnTitleWidth + 48);
            make.height.mas_equalTo(@40);
            make.centerX.equalTo(_networkErrorView);
            make.top.equalTo(networkErrTipLabel.mas_bottom).offset(28);
        }];
        
    }
    return _networkErrorView;
}

- (void)refreshCurrentWeb{
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.url] cachePolicy:NSURLRequestReloadIgnoringCacheData timeoutInterval:15.0]];
}

- (void)resetWKWebViewUA{
    XYWeakSelf
    [self.webView evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSString *oldUA = result;
        NSString *appId = [NSString stringWithFormat:@"AppId/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleIdentifier"]];
        NSString *os = [NSString stringWithFormat:@"OS/%@",@"ios"];
        NSString *appVersionName = [NSString stringWithFormat:@"AppVersionName/%@",[[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"]];
        NSString *model = [NSString stringWithFormat:@"Model/%@",[XYTool deviceName]];
        NSString *systemVersion = [NSString stringWithFormat:@"SystemVersion/%@",[UIDevice currentDevice].systemVersion];
        NSString *deviceId = [NSString stringWithFormat:@"DeviceId/%@",[JCKeychainTool getDeviceIDInKeychain]];
        NSString *boundleId = [NSString stringWithFormat:@"boundleId/%@",[[NSBundle mainBundle] bundleIdentifier]];
        NSString *referer = @"referer/CP001Mobile";
        NSString *newUA =[NSString stringWithFormat:@"%@ %@ %@ %@ %@ %@ %@ %@ %@", oldUA,appId,os,appVersionName,model,systemVersion,deviceId,boundleId,referer];
        weakSelf.webView.customUserAgent = newUA;
//        [[NSUserDefaults standardUserDefaults] setObject:newUA forKey:@"niimbot-user-agent"];
//        [[NSUserDefaults standardUserDefaults] synchronize];
        NSLog(@"UA:%@",newUA);
    }];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setVCBackImageView];
    XYWeakSelf
    [self.view addSubview:self.webView];
    [self.view addSubview:self.networkErrorView];
    [self.webView setAllowsBackForwardNavigationGestures:self.isFullScreen ? false : true];
    [self.networkErrorView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(weakSelf.view);
    }];
    [self.webView loadRequest:[NSURLRequest requestWithURL:[NSURL URLWithString:self.url]  cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:20]];
    //进度条
    [self.view addSubview:self.progressView];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_TOKEN_CHANGED object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshVipOperateStatus:) name:JCNOTICATION_VIP_OPERATE_SUCCESS object:nil];
    // 给webview添加监听
    // Do any additional setup after loading the view, typically from a nib.
    [self addObserverNotification];
    if(self.navigationController.viewControllers.count <= 1){
        [self showBarButton:NAV_LEFT imageName:@"left_close"];
    }else{
        [self showBarButton:NAV_LEFT imageName:@"new_nav_back"];
    }
    JCWeakScriptMessageDelegate *weakScriptMessageDelegate = [[JCWeakScriptMessageDelegate alloc]   initWithDelegate:self];
  
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"goBackApp"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"goHardwareDoc"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"downloadApp"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"nativeFunction"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"webEventTrack"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"openHyperlinkWithSystemBrowser"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"share2Wechat"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"goToCYAppDownloadHalfPanel"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"jcappay"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"saveQrcodeImage"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"loginOnToApp"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"refreshUserInfo"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"jumpToIM"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"goBack"];
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"openIonic"];
    // 请求定位权限
    [self.configuration.userContentController addScriptMessageHandler:self name:@"openShopMall"];
  
    //jumpToDefaultBrowse
    [self.configuration.userContentController addScriptMessageHandler:weakScriptMessageDelegate name:@"jumpToDefaultBrowse"];
    self.weakScriptMessageDelegate = weakScriptMessageDelegate;
    if(self.isFullScreen){
        self.lsl_prefersNavigationBarHidden = YES;
        self.webView.frame = CGRectMake(0, 0, kScreenWidth, kScreenHeight);
        [self addPanGesture];
    } else {
       self.lsl_prefersNavigationBarHidden = NO;
    }
}

-(void)addPanGesture{
   
    UIScreenEdgePanGestureRecognizer *edgePanGesture = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(swipe_handle:)];
    edgePanGesture.edges = UIRectEdgeLeft;
    edgePanGesture.delegate = self;
    [self.webView addGestureRecognizer:edgePanGesture];
}

- (void)swipe_handle:(UIGestureRecognizer *)recognizer {

    if (recognizer.state == UIGestureRecognizerStateEnded) {
        // 把侧滑事件交给h5来处理
        [self.webView evaluateJavaScript:@"onBackPressed()" completionHandler:^(id _Nullable result, NSError * _Nullable error) {
                
        }];
    }
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    return YES;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    NSLog(@"即将展现H5");
  BOOL noNav = self.navigationController == nil || self.navigationController.isNavigationBarHidden;
  self.backBtn.hidden = !noNav;
}

- (void)viewDidAppear:(BOOL)animated {
  [super viewDidAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    NSLog(@"即将离开H5");
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (self.viewDidDisappearBlock) {
        self.viewDidDisappearBlock();
    }
}

- (void)dealloc{
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"goBackApp"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"goHardwareDoc"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"downloadApp"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"nativeFunction"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"webEventTrack"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"openHyperlinkWithSystemBrowser"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"share2Wechat"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"goToCYAppDownloadHalfPanel"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jcappay"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"saveQrcodeImage"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"loginOnToApp"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"refreshUserInfo"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jumpToIM"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"goBack"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"openIonic"];
    [self.configuration.userContentController removeScriptMessageHandlerForName:@"jumpToDefaultBrowse"];
    [self.webView removeObserver:self forKeyPath:@"estimatedProgress"];
    [self.webView removeObserver:self forKeyPath:@"title"];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)shopDetailStateView{
    float stateViewheight = iPhoneX?44:20;
    UIView *navBKView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, stateViewheight)];
    navBKView.backgroundColor = HEX_RGB(0xFFFFFF);
    [self.view addSubview:navBKView];
    self.detailStateView = navBKView;
}

-(void)addObserverNotification{
    
    [[NSNotificationCenter defaultCenter]addObserver:self selector:@selector(windowDidBecomeHidden:) name:UIWindowDidBecomeHiddenNotification object:nil];
}

#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
  if ([message.name isEqualToString:@"goBackApp"]) {//点击返回
        [self performNativeBack];
  } else if ([message.name isEqualToString:@"goHardwareDoc"]) {//点击查看硬件说明
        NSString *loadImageUrl = message.body;
        if(loadImageUrl.length > 0){
            loadImageUrl = XY_Check_UrlString(loadImageUrl);
            XYWKWebViewController *machineDetailVC = [[XYWKWebViewController alloc] initWithUrl:[NSString stringWithFormat:@"%@#/hardwareDoc?imageUrl=%@",ServerURL1,loadImageUrl]];
            machineDetailVC.title = XY_LANGUAGE_TITLE_NAMED(@"app01130", @"硬件说明");
            [self.navigationController pushViewController:machineDetailVC animated:YES];
        }
    }else if ([message.name isEqualToString:@"downloadApp"]) {//H5点击下载app事件
        NSString *downloadUrl = message.body;
        if(downloadUrl.length > 0){
            [[UIApplication sharedApplication] openURL:XY_URLWithString(downloadUrl) options:@{} completionHandler:^(BOOL success) {}];
        }
    }else if ([message.name isEqualToString:@"nativeFunction"]) {//
        NSString *messageInfo = message.body;
        if(!STR_IS_NIL(messageInfo)){
            if([messageInfo containsString:@"?"]){
                NSDictionary *parmsDic = [XYTool dictionaryWithUrlString:messageInfo];
                NSString *actName = [parmsDic objectForKey:@"actName"];
                NSString *nativeRoute = [messageInfo componentsSeparatedByString:@"?"][0];
                [JCToNativeRouteHelp toNativePageWith:UN_NIL(nativeRoute) fromType:JC_H5Web eventTitle:actName origRout:messageInfo];
            }else{
                [JCToNativeRouteHelp toNativePageWith:messageInfo fromType:JC_H5Web eventTitle:@""];
            }
        }
    }else if ([message.name isEqualToString:@"webEventTrack"]) {//
        NSString *messageInfo = message.body;
        NSDictionary *messageDic = [messageInfo xy_toDictionary];
        if(messageDic.allKeys.count > 0){
            NSString *eventCode = messageDic[@"eventCode"];
            NSString *posCode = messageDic[@"posCode"];
            NSDictionary *eventTitle = messageDic[@"params"];
            if(eventTitle == nil || eventTitle.count == 0){
                eventTitle = @{};
            }
            JC_TrackWithparms(UN_NIL(eventCode),UN_NIL(posCode),eventTitle);
        }
    } else if ([message.name isEqualToString:@"openHyperlinkWithSystemBrowser"]){
        NSString *browserUrl = message.body;
        if(!STR_IS_NIL(browserUrl)){
            NSURL *URL = [NSURL URLWithString:browserUrl];
            [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                  //  回调
            }];
        }
    }  else if ([message.name isEqualToString:@"saveQrcodeImage"]){
        NSString *imageDownloadInfo = message.body;
        if(!STR_IS_NIL(imageDownloadInfo)){
            if([imageDownloadInfo hasPrefix:@"http"]){
                [JCActivityCodeHelp downloadAndSaveImageToAlbumWith:imageDownloadInfo];
            }else{
                NSData *imageData = [[NSData alloc] initWithBase64EncodedString:[imageDownloadInfo componentsSeparatedByString:@","][1] options:NSDataBase64DecodingIgnoreUnknownCharacters];
                UIImage *image = [UIImage imageWithData:imageData];
                __block MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
                progressHUD.label.text = @"";
                [JCActivityCodeHelp saveImageFinished:image withProgress:progressHUD];
            }
        }
    }
    else if ([message.name isEqualToString:@"share2Wechat"]){
        NSString *messageBody = message.body;
        NSDictionary *messageDic = [messageBody xy_toDictionary];
        if([messageDic isKindOfClass:[NSDictionary class]]){
            [self shareContentToWeChatWithShareInfo:messageDic];
        }
    }else if([message.name isEqualToString:@"loginOnToApp"]){
        [self getTokenFromNative];
    }else if([message.name isEqualToString:@"openIonic"]){
      NSString *messageBody = message.body;
      NSDictionary *messageDic = [messageBody xy_toDictionary];
      if(messageDic.count > 0){
        NSString *appId = messageDic[@"appId"];
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:self loginSuccessBlock:^{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:appId needKeepLive:YES parms:nil receiveUniappData:^(id x) {

                }];
            });
        }];
      }
    }else if([message.name isEqualToString:@"refreshUserInfo"]){
        JCNCPost(JCNOTICATION_SYNCH_CHANGE);
    }else if([message.name isEqualToString:@"jumpToIM"]){
        __block MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
        progressHUD.label.text = @"";
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getIMLink" arguments:@{} result:^(NSString *url) {
            if (!STR_IS_NIL(url)) {
                [progressHUD hideAnimated:NO];
                UIViewController *currentVC = [XYTool getCurrentVC];
                NMWebViewController *webViewController = [[NMWebViewController alloc] init];
                webViewController.isSupportShare = NO;
                [webViewController loadUrl:url];
                [currentVC.navigationController pushViewController:webViewController animated:YES];
            }
        }];
    }else if ([message.name isEqualToString:@"goBack"]){
      if (self.navigationController == nil || self.navigationController.viewControllers.count == 1) {
          [self dismissViewControllerAnimated:YES completion:^{
                          
          }];
      } else {
          [self.navigationController popViewControllerAnimated:YES];
      }
    }else if ([message.name isEqualToString:@"jumpToDefaultBrowse"]){
        NSString *browserUrl = message.body;
        if(!STR_IS_NIL(browserUrl)){
            NSURL *URL = [NSURL URLWithString:browserUrl];
            [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                  //  回调
            }];
        }
    }
    else if ([message.name isEqualToString:@"goToCYAppDownloadHalfPanel"]){
        NSString *actName = message.body;
        [JCToNativeRouteHelp jumpToCYAppWith:@"NIIM://cloudprinter/rlogin" appstore:JC_CXY_APP_STORE action: actName];
    }if ([message.name isEqualToString:@"jcappay"]){
        if(m_user_vip){
            JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
        }else{
            NSString *sku = message.body;
            [JCIAPHelper openViewWithAlert:self
                               needOpenTip:NO
                            isUseVipSource:NO
                                   success:^{
                
            }
                                   failure:^(NSString * _Nonnull msg, id  _Nonnull model) {
                
            }
                                sourceInfo:@{@"anchorSubscribeId": [NSString stringWithFormat:@"%@",sku]}];
        }
    } else if ([message.name isEqualToString:@"openShopMall"]) {
      // 打开商城
      UIViewController *topVc = [XYTool getCurrentVC];
      NSString *url = message.body;
      if (NETWORK_STATE_ERROR) {
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return;
      }
      if (!STR_IS_NIL(url)) {
          JCShopNormalVC *shopVc = [[JCShopNormalVC alloc] initWithShopAppointUrl:url];
          shopVc.entrance_type_id = @"3";
          shopVc.jumpSource = @"out_station_active_page";
          shopVc.isNeedBackApp = YES;
          if(topVc.navigationController == nil){
              XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:shopVc];
              [topVc presentViewController:nav animated:YES completion:^{

              }];
          }else{
              [topVc.navigationController pushViewController:shopVc animated:YES];
          }
      }
  }
    
}

- (void)getTokenFromNative{
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:self loginSuccessBlock:^{

    }];
}

-(void)windowDidBecomeHidden:(NSNotification *)noti{
    
    UIWindow * win = (UIWindow *)noti.object;
    
    if(win){
        
        UIViewController *rootVC = win.rootViewController;
        
        NSArray<__kindof UIViewController *> *vcs = rootVC.childViewControllers;
        
        if([vcs.firstObject isKindOfClass:NSClassFromString(@"AVPlayerViewController")]){
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated"
            [[UIApplication sharedApplication] setStatusBarHidden:NO withAnimation:UIStatusBarAnimationNone];
#pragma clang diagnostic pop
        }
    }
}

- (void)setVCBackImageView{
    XYWeakSelf
    UIImageView *backImageView = [[UIImageView alloc] init];
    backImageView.backgroundColor = [UIColor clearColor];
    backImageView.image = XY_IMAGE_NAMED(@"背景图");
    [self.view addSubview:backImageView];
    [backImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.top.trailing.bottom.equalTo(weakSelf.view).offset(0);
    }];
    [self.view sendSubviewToBack:backImageView];
}

// 页面开始加载时调用
-(void)webView:(WKWebView *)webView didStartProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didStartProvisionalNavigation");
    NSLog(@"当前加载URL：%@",webView.URL);
}
// 当内容开始返回时调用
- (void)webView:(WKWebView *)webView didCommitNavigation:(WKNavigation *)navigation{
    NSLog(@"内容开始返回:%@ 当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
}
// 页面加载完成之后调用
- (void)webView:(WKWebView *)webView didFinishNavigation:(WKNavigation *)navigation{//这里修改导航栏的标题，动态改变
    NSLog(@"开始H5完毕:%@  当前加载URL：%@",[XYTool getCurrentTimes],webView.URL);
    NSLog(@"%@",self.view);
    self.loadSuccess = YES;
//    self.networkErrorView.hidden = YES;
}

// 页面加载失败时调用
-(void)webView:(WKWebView *)webView didFailProvisionalNavigation:(WKNavigation *)navigation withError:(NSError *)error{
    NSLog(@"请求失败，不显示webView");
    self.loadSuccess = NO;
    if(self.loadFieldBlock){
        self.loadFieldBlock(@"");
    }
    if(error.code == 102){
        [self.progressView setProgress:0.0f animated:YES];
        [self.progressView setAlpha:0.0f];
    }else{
        self.networkErrorView.hidden = NO;
    }
}

- (void)webView:(WKWebView *)webView didFailNavigation:(null_unspecified WKNavigation *)navigation withError:(NSError *)error{
    
}

// 接收到服务器跳转请求之后再执行
- (void)webView:(WKWebView *)webView didReceiveServerRedirectForProvisionalNavigation:(WKNavigation *)navigation{
    NSLog(@"didReceiveServerRedirectForProvisionalNavigation");
}

- (void)webView:(WKWebView *)webView decidePolicyForNavigationResponse:(WKNavigationResponse *)navigationResponse decisionHandler:(void (^)(WKNavigationResponsePolicy))decisionHandler {
    if (((NSHTTPURLResponse *)navigationResponse.response).statusCode == 200 || ((NSHTTPURLResponse *)navigationResponse.response).statusCode == 521) {
        decisionHandler (WKNavigationResponsePolicyAllow);
    }else {
        decisionHandler(WKNavigationResponsePolicyCancel);
    }
}

-(WKWebView *)webView:(WKWebView *)webView createWebViewWithConfiguration:(WKWebViewConfiguration *)configuration forNavigationAction:(WKNavigationAction *)navigationAction windowFeatures:(WKWindowFeatures *)windowFeatures

{
    NSLog(@"createWebViewWithConfiguration");
    if (!navigationAction.targetFrame.isMainFrame) {
        [webView loadRequest:navigationAction.request];
    }
    return nil;

}
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    __block WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByRemovingPercentEncoding];
    
    if(navigationAction.targetFrame == nil) {
        //webview内超链接跳转支持
        [webView loadRequest:navigationAction.request];
        decisionHandler(WKNavigationActionPolicyAllow);
        return;
    }else if(([urlString hasPrefix:@"http://"] || [urlString hasPrefix:@"https://"]) && ![urlString hasPrefix:@"https://apps.apple.com/cn/app"]){
        self.shareUrl = urlString;
        decisionHandler(actionPolicy);
        return;
    }
    NSURL*url = [NSURL URLWithString:XY_Check_UrlString(urlString)];
    BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
    if(bSucc) {
        actionPolicy =WKNavigationActionPolicyCancel;
        [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
        }];
    }
    decisionHandler(actionPolicy);
}

- (BOOL)isLinkUrl:(NSString * )linkStr{
 NSString*emailRegex = @"((http[s]{0,1}|ftp)://[a-zA-Z0-9\\-.]+(?::(\\d+))?(?:(?:/[a-zA-Z0-9\\-._?,'+\\&%$=~*!():@\\\\]*)+)?)|(www.[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&*+?:_/=<>]*)?)";
    NSPredicate *predicate = [NSPredicate predicateWithFormat:@"SELF MATCHES  %@",emailRegex];
    return [predicate evaluateWithObject:linkStr];
}

// 监听事件处理
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    if ([keyPath isEqual:@"estimatedProgress"] && object == self.webView) {
        [self.progressView setAlpha:1.0f];
        [self.progressView setProgress:self.webView.estimatedProgress animated:YES];
        if (self.webView.estimatedProgress  >= 1.0f) {
            [UIView animateWithDuration:0.3 delay:0.3 options:UIViewAnimationOptionCurveEaseOut animations:^{
                [self.progressView setAlpha:0.0f];
            } completion:^(BOOL finished) {
                [self.progressView setProgress:0.0f animated:YES];
            }];
        }
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }else if ([keyPath isEqualToString:@"title"]) {
        if (object == self.webView && !STR_IS_NIL(self.webView.title) && self.showWebTitle && ![self.webView.title isEqualToString:@"NIIMBOT"]) {
            self.title = self.webView.title;
        }
    }
    else{
//        [super observeValueForKeyPath:keyPath ofObject:object change:change context:context];
    }
}

- (void)loginChanged:(NSNotification *)notification{
    NSString *token = STR_IS_NIL(m_userModel.token)?@"":[NSString stringWithFormat:@"%@ %@",@"bearer",m_userModel.token];
    NSString *jsStr = [NSString stringWithFormat:@"appLoginSuccess('%@')",token];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

- (void)refreshVipOperateStatus:(NSNotification *)notification{
    NSString *isVip = m_user_vip?@"1":@"0";
    NSString *token = m_userModel.token;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:@{@"isVip":isVip,@"token":UN_NIL(token)} options:0 error:nil];
    NSString *dataStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSString *jsStr = [NSString stringWithFormat:@"vipModalCallback('%@')",dataStr];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)shareContentToWeChatWithShareInfo:(NSDictionary *)shareInfo{
    NSString *title = shareInfo[@"title"];
    NSString *description = shareInfo[@"description"];
    NSString *shareUrl = shareInfo[@"shareUrl"];
    NSString *icon = shareInfo[@"icon"];
    NSNumber *needBindMainAccount = shareInfo[@"needBindMainAccount"];
    if(![WXApi isWXAppInstalled]){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01193",@"您没有安装手机微信")];
        return;
    }
    if(needBindMainAccount.boolValue){
        if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
            [[JCLoginManager sharedInstance] checkBindWithviewController:self withBindType:1 withResponse:^{
                
            } withComplete:^(id x) {
                [self shareContentToWeChatWithShareInfo:shareInfo];
            }];
            return;
        }
    }
    UIImageView *imageView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, 0, 0)];
    
    [imageView sd_setImageWithURL:XY_URLWithString(icon) completed:^(UIImage * _Nullable image, NSError * _Nullable error, SDImageCacheType cacheType, NSURL * _Nullable imageURL) {
        SendMessageToWXReq *sendReq = [[SendMessageToWXReq alloc] init];

        sendReq.bText = NO;//不使用文本信息

        sendReq.scene = 0;//0 = 好友列表 1 = 朋友圈 2 = 收藏

        //创建分享内容对象

        WXMediaMessage *urlMessage = [WXMediaMessage message];

        urlMessage.title = title;//分享标题

        urlMessage.description = description;//分享描述/分享图片,使用SDK的setThumbImage方法可压缩图片大小
        urlMessage.thumbData = UIImagePNGRepresentation(image);
        //创建多媒体对象

        WXWebpageObject *webObj = [WXWebpageObject object];

        webObj.webpageUrl = shareUrl;//分享链接
        
        //完成发送对象实例

        urlMessage.mediaObject = webObj;

        sendReq.message = urlMessage;
        //发送分享信息

        [WXApi sendReq:sendReq completion:^(BOOL success) {
            
        }];
    }];
    [self.view addSubview:imageView];
}

@end

