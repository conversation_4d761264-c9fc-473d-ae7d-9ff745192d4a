package melon.south.com.mainlibrary.settings.activity

import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.gengcon.print.draw.view.operate.menubar.detail.common.FontManagerHelper
import com.gengcon.print.draw.view.operate.menubar.detail.common.FontTypeListManager
import com.niimbot.appframework_library.common.module.font.FontLibBean
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.appframework_library.utils.font.FontUtils.customFontFile
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.utiliylibray.util.json2Array
import com.niimbot.viplibrary.VipHelper
import com.qyx.languagelibrary.LanguageTextView
import com.qyx.languagelibrary.utils.TextHookUtil
import melon.south.com.baselibrary.base.BaseActivity
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.util.showToast
import melon.south.com.mainlibrary.R
import melon.south.com.mainlibrary.databinding.ActivityFontBinding
import melon.south.com.mainlibrary.settings.adapter.FontInfoBean
import melon.south.com.mainlibrary.settings.adapter.FontManagerAdapterNew


class FontManagerActivity : BaseActivity(), LoginDataEnum.LoginStatusChangeListener {
    private val binding by viewBinding(ActivityFontBinding::inflate)
    private var zhAdapter: FontManagerAdapterNew = FontManagerAdapterNew()
    private var fontCountryAdapter = CountryAdapter()
    private var isDestroy = false
    private val zhData: ArrayList<FontInfoBean> = arrayListOf()
    private var currentSort: FontLibBean.ClassifyBean? = null
    private var currentPosition = 0
    private val fontTypeListManager = FontTypeListManager()

    override fun getBindingView() = binding.root

    override fun getLayoutId() = R.layout.activity_font

    override fun init() {
        mSuperTitleBar?.setText("app00201")
        binding.fontRecyclerView.layoutManager = LinearLayoutManager(mActivity)
        zhAdapter.fontTypeListManager = fontTypeListManager
        binding.fontRecyclerView.adapter = zhAdapter
        binding.fontRecyclerView.isFocusableInTouchMode = false
        zhAdapter.fontTypeListener = object: FontManagerAdapterNew.OnFontTypeListener{
            override fun pickFontType(fontLibBean: FontLibBean) {
                FontManagerHelper.showFontDetailDialog(this@FontManagerActivity, fontLibBean)
            }

            override fun scrollToSelectFontType(position: Int) {
            }
        }
        zhAdapter.deleteListener = { position ->
            try{
                if (position >= 0 && position < zhAdapter.data.size) {
                    val font = zhAdapter.data[position]
                    if(font.fontLibBean.code.equals("ZT001", true)
                        || font.fontLibBean.code.equals("ZT063", true)
                        || font.fontLibBean.code.equals("ZT825", true)) {
                        showToast("app100000253")
                        zhAdapter.notifyItemChanged(position)
                    } else {
                        showLoading()
                        FontManagerHelper.removeUserFont(font.fontLibBean){
                            dismissLoading()
                            if (it) {
                                FontUtils.deleteFont(font.fontLibBean.code)
                                zhAdapter.data.removeAt(position)
                                zhAdapter.notifyItemRemoved(position)
                                zhAdapter.notifyDataSetChanged()
                            }
                        }
                    }
                }
            } catch(e: Exception) {e.printStackTrace()}
        }


        binding.ctRv.adapter = fontCountryAdapter
        fontCountryAdapter.setOnItemClickListener { _, _, position ->
                currentSort = fontCountryAdapter.data[position]
                zhAdapter.isSwipeEnable = currentSort?.isMyList() == true
                currentPosition = position
                if (fontCountryAdapter.choose != position) {
                    binding.ctRv.scrollToPosition(position)
                    fontCountryAdapter.choose = position
                    fontCountryAdapter.notifyDataSetChanged()
                    forceUpdate()
                }
        }
        if (!customFontFile.exists()) {
            customFontFile.mkdirs()
        }
        initSortForFont{
            fontCountryAdapter.setNewData(it)
            if (null == currentSort) {
                    currentSort = fontCountryAdapter.data[0]
                    zhAdapter.isSwipeEnable = currentSort?.isMyList() == true
            }
            forceUpdate()
            fontCountryAdapter.choose = currentPosition
            fontCountryAdapter.notifyDataSetChanged()
            binding.ctRv.scrollToPosition(currentPosition)
        }
    }

    override fun onDestroy() {
        FontUtils.downloadStatusArray.clear()
        FontUtils.recycle()
        isDestroy = true
        super.onDestroy()
    }

    /**
     * 获取字体
     */
    private fun getFontInfo() {
        currentSort?.let {
            fontTypeListManager.getFontInfo(it, "ZT001")
        }
    }

    private var hasUpdate = false
    private fun forceUpdate() {
        if (!hasUpdate) {
            showLoading()
            hasUpdate = true
            FontUtils.getAllFontFromNet{_, _, _ ->
                getFontInfo()
                dismissLoading()
            }
        } else {
            getFontInfo()
        }
    }

    private fun initSortForFont(listener: ((List<FontLibBean.ClassifyBean>) -> Unit)) {
        // 先从本地加载数据，快速渲染界面
        FlutterMethodInvokeManager.getFontClassify(
            isFromLocal = true,
            success = { fontClassifyJson ->
                try {
                    val fontLibBeanList = json2Array(fontClassifyJson, FontLibBean.ClassifyBean::class.java) as ArrayList<FontLibBean.ClassifyBean>
                    if (fontLibBeanList.isNotEmpty()) {
                        FontUtils.fontClassifyList.clear()
                        FontUtils.fontClassifyList.addAll(fontLibBeanList)
                        processSortData(listener)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

                // 再从网络获取最新数据并刷新界面
                FlutterMethodInvokeManager.getFontClassify(
                    isFromLocal = false,
                    success = { networkFontClassifyJson ->
                        try {
                            val networkFontLibBeanList = json2Array(networkFontClassifyJson, FontLibBean.ClassifyBean::class.java) as ArrayList<FontLibBean.ClassifyBean>
                            if (networkFontLibBeanList.isNotEmpty()) {
                                FontUtils.fontClassifyList.clear()
                                FontUtils.fontClassifyList.addAll(networkFontLibBeanList)
                                processSortData(listener)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    },
                    fail = { errorMessage ->
                        // 网络请求失败，不做处理，保持本地数据
                    }
                )
            },
            fail = { errorMessage ->
                // 本地数据加载失败，直接从网络获取
                FlutterMethodInvokeManager.getFontClassify(
                    isFromLocal = false,
                    success = { networkFontClassifyJson ->
                        try {
                            val networkFontLibBeanList = json2Array(networkFontClassifyJson, FontLibBean.ClassifyBean::class.java) as ArrayList<FontLibBean.ClassifyBean>
                            if (networkFontLibBeanList.isNotEmpty()) {
                                FontUtils.fontClassifyList.clear()
                                FontUtils.fontClassifyList.addAll(networkFontLibBeanList)
                                processSortData(listener)
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    },
                    fail = { networkErrorMessage ->
                        // 网络也失败，使用空数据
                        processSortData(listener)
                    }
                )
            }
        )
    }

    private fun processSortData(listener: ((List<FontLibBean.ClassifyBean>) -> Unit)) {
        var result = ArrayList<FontLibBean.ClassifyBean>().apply{ addAll(FontUtils.fontClassifyList)}
        var targetIndex = result
            .indexOfFirst { TextHookUtil.getInstance().languageName.equals(it.languageCode, true) }
            .run{
                if (this < 0) {
                    result.indexOfFirst { TextHookUtil.LANGUAGE_EN.equals(it.languageCode, true)}
                } else {
                    this
                }
            }
        if (targetIndex > 0) {
            result.add(0, result.removeAt(targetIndex))
        }
        result.add(0, FontLibBean.ClassifyBean().apply {
            classifyId = "-1"
            classifyName = "app00838"
            languageCode = ""
        })
        listener.invoke(result)
    }

    override fun onResume() {
        super.onResume()
        zhAdapter.notifyDataSetChanged()
    }

    override fun loginStatusChange(isLogin: Boolean) {
        zhAdapter.notifyDataSetChanged()
    }


    inner class CountryAdapter(dataList: List<FontLibBean.ClassifyBean>? = null) : BaseQuickAdapter<FontLibBean.ClassifyBean, BaseViewHolder>(
        com.gengcon.print.draw.R.layout.item_font_country, dataList) {

        var choose = 0
        override fun convert(helper: BaseViewHolder, item: FontLibBean.ClassifyBean?) {
            helper.itemView.isSelected = helper.layoutPosition == choose
            helper.getView<LanguageTextView>(com.gengcon.print.draw.R.id.tv_title_name)?.text = item?.classifyName
        }
    }
}
