package com.gengcon.android.jccloudprinter.flutterActivity;

import static melon.south.com.baselibrary.util.ToastUtils.showToast;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ColorUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.gengcon.android.jccloudprinter.R;
import com.gengcon.android.jccloudprinter.importExcel.ImportExcelHelper;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import com.niimbot.appframework_library.BaseApplication;
import com.niimbot.appframework_library.dialog.CustomDialog;
import com.niimbot.baselibrary.geetest.GeetestHelper;
import com.niimbot.baselibrary.loading.GlobalLoadingHelper;
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils;
import com.niimbot.storelibrary.excel.activity.ExcelHookActivity;
import com.niimbot.viplibrary.VipHelper;
import com.niimbot.viplibrary.VipType;

import java.util.Objects;

import io.flutter.embedding.android.FlutterActivityLaunchConfigs.BackgroundMode;
import melon.south.com.baselibrary.util.StatusBarUtil;
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter;

public class TransparentActionBarActivity extends FlutterBoostActivity {
  String listener;
  FlutterBoostRouteOptions options;
  public boolean isDrawboardPage = false;

  @NonNull
  @Override
  protected BackgroundMode getBackgroundMode() {
    return super.getBackgroundMode();
  }

  @Override
  protected void onCreate(Bundle savedInstanceState) {
    android.util.Log.e("============relaunchApp=========", "savedInstanceState : $savedInstanceState, appInit: ${BaseApplication.appInit}");
    if (savedInstanceState != null && !BaseApplication.appInit) {
      AppUtils.relaunchApp();
    }
    /*
     * 全透状态栏
     */
    //21表示5.0
    if(getUrl().equals("printSetting")){
//        overridePendingTransition(R.anim.slide_in_up,0);
      overridePendingTransition(com.niimbot.appframework_library.R.anim.enter_from_right,0);
    }
    super.onCreate(savedInstanceState);
//    Window window = getWindow();
//    window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//    window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//      | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
//    window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//    window.setStatusBarColor(Color.TRANSPARENT);
    // Android 15 及以上版本：确保内容不侵入系统栏
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM && shouldFlutterPageApply15()) {
      if(getUrl().equals("printSetting")){
        applyInsetsV2(getWindow().getDecorView(),false,false);
      }else if(getUrl().equals("printNullUiShowProgress")){
        applyInsetsV2(getWindow().getDecorView(),false,true);
      }else if(getUrl().equals("C1")){
        applyInsetsV2(getWindow().getDecorView(),false,false);
      }else{
        applyInsets(getWindow().getDecorView());
      }

    }else{
      Window window = getWindow();
      window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
      window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
      window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
      window.setStatusBarColor(Color.TRANSPARENT);
    }

    GeetestHelper.INSTANCE.init(this);
  }
  private boolean shouldFlutterPageApply15(){
    boolean result = true;
    String url = getUrl();
    if(url.equals("canvas") || url.equals("cableCanvas") /*|| getUrl().equals("printSetting")*/){
      result = false;
    }
    return result;
  }
  protected void applyInsets(View view) {
    ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
      int topInset = 0;
      int bottomInset = 0;

      // 获取系统栏的 insets
      android.view.WindowInsets windowInsets = (android.view.WindowInsets) insets.toWindowInsets();
      if (windowInsets != null) {
        topInset = windowInsets.getInsets(android.view.WindowInsets.Type.statusBars()).top;
        bottomInset = windowInsets.getInsets(android.view.WindowInsets.Type.navigationBars()).bottom;
      }
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        v.setBackgroundColor(getResources().getColor(R.color.white));
      }

      // 设置适当的 padding 以避免内容被系统栏遮挡
      v.setPadding(
        v.getPaddingLeft(),  // 保持原有的左右 padding
        v.getPaddingTop(),
        v.getPaddingRight(),
        bottomInset  // 为导航栏预留空间
      );

      return insets;
    });
  }

  protected void applyInsetsV2(View view,Boolean needTopPadding,Boolean needBottomPadding) {
    ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
      int topInset = 0;
      int bottomInset = 0;

      // 获取系统栏的 insets
      android.view.WindowInsets windowInsets = (android.view.WindowInsets) insets.toWindowInsets();
      if (windowInsets != null) {
        topInset = windowInsets.getInsets(android.view.WindowInsets.Type.statusBars()).top;
        bottomInset = windowInsets.getInsets(android.view.WindowInsets.Type.navigationBars()).bottom;
      }
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        v.setBackgroundColor(getResources().getColor(R.color.white));
      }

      // 设置适当的 padding 以避免内容被系统栏遮挡
      v.setPadding(
        v.getPaddingLeft(),  // 保持原有的左右 padding
        needTopPadding ? topInset : 0,
        v.getPaddingRight(),
        needBottomPadding ? bottomInset : 0  // 为导航栏预留空间
      );

      return insets;
    });
  }

  @Override
  protected void onUpdateSystemUiOverlays() {

  }

  @Override
  protected void onDestroy() {
    GlobalLoadingHelper.INSTANCE.dismissLoading();
    KeyboardUtils.hideSoftInput(this);
    super.onDestroy();

    GeetestHelper.INSTANCE.cancel();
  }

  @Override
  protected void onStop() {
    GlobalLoadingHelper.INSTANCE.dismissLoading();
    super.onStop();
  }
  @Override
  public void onBackPressed() {
    // 什么都不做，或者添加其他自定义逻辑
    super.onBackPressed();
  }


  @SuppressLint("WrongConstant")
  @Override
  protected void onStart() {
    super.onStart();
  }


  @Override
  protected void onPause() {
    super.onPause();
  }

  @Override
  protected void onNewIntent(@NonNull Intent intent) {
    super.onNewIntent(intent);
  }

  @SuppressLint("WrongConstant")
  @Override
  public void onResume() {
    super.onResume();
  }


  @Override
  public void onPostResume() {
    super.onPostResume();
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM && shouldFlutterPageApply15()) {
//      com.niimbot.appframework_library.utils.AppUtils.INSTANCE.setStatusBarColor(this, getResources().getColor(melon.south.com.baselibrary.R.color.white));
      com.niimbot.appframework_library.utils.AppUtils.INSTANCE.setStatusBarLightColor(this, ColorUtils.getColor(R.color.transparent),true);
    }else{
      StatusBarUtil.setStatusBarTranslucent(this, true);
    }
  }

  @Override
  protected void onActivityResult(int requestCode, int resultCode, Intent data) {
    super.onActivityResult(requestCode, resultCode, data);
    if(requestCode == ImportExcelHelper.RESULT_IMPORT_EXCEL_FROM_THIRD){
      if(resultCode == Activity.RESULT_OK){
        ExcelHookActivity.ToUploadBean uploadBean = ExcelHookActivity.Companion.getCallData();
        if(uploadBean != null){
          String fileName = uploadBean.getFileName();
          byte[] fileData = uploadBean.getFileData();
          if(fileName != null && !fileName.isEmpty() && fileData != null && fileData.length > 0){
            thirdImportExcelListener.confirmImport(fileName, fileData);
          }
          else{
            if(thirdImportExcelListener != null){
              thirdImportExcelListener.cancelImport();
            }
          }
        }
        else{
          if(thirdImportExcelListener != null){
            thirdImportExcelListener.cancelImport();
          }
        }
      }
    }else if(requestCode==VipHelper.RESULT_IMPORT_VIP){
      String status = data != null ? data.getStringExtra("status") : null;
      boolean isGoogle = data != null && data.getBooleanExtra("isGoogle", false);
      Log.i("wang", "pay result status=" + status + " isGoogle=" + isGoogle);
      Activity activity=this;
      runOnUiThread(() -> {
        if (VipHelper.INSTANCE.isShowBenefits() && !isGoogle) {
          if (status != null && !status.isEmpty()) {
            VipHelper.INSTANCE.checkBillStatusResultResponse(activity, status, null,null,null, VipType.CABLE);
          } else {
            CustomDialog.showBlueOneButtonDialog(activity, "开通失败", "您可以尝试重新购买", "我知道了", new CustomDialog.OnChoiceListener() {

              @Override
              public void onChoice(boolean isConfirm) {
              }
            });
          }
        }
      });

    }
    else{
      //换头像闪退问题特殊处理
      new Handler().postDelayed(() -> TransparentActionBarActivity.super.onActivityResult(requestCode, resultCode, data), 500);
    }
  }

  @Override
  public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
    XPermissionUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults);
    super.onRequestPermissionsResult(requestCode, permissions, grantResults);
    if(listener == null){
      return;
    }
    if (listener.equals("ScanBarcode")) {
      if (grantResults[0] == 0) {
        boolean justCallbackScanCode = false;
        if(options.arguments().containsKey("justCallbackScanCode")){
          justCallbackScanCode = (Boolean)options.arguments().get("justCallbackScanCode");
        }
        SyncTemplatePresenter.INSTANCE.startScanActivity(
          this,
          options.requestCode(),
          justCallbackScanCode
        );
      }
    }
  }


  public void setPermissionListener(String listener, FlutterBoostRouteOptions options) {
    this.listener = listener;
    this.options = options;
  }

  @Override
  public void finish() {
    super.finish();
    if(getUrl().equals("printSetting")){
//      overridePendingTransition(0, R.anim.slide_out_down);
      overridePendingTransition(0, com.niimbot.appframework_library.R.anim.exit_to_right);
    }
  }

  public IThirdImportExcelListener thirdImportExcelListener;
  public interface IThirdImportExcelListener{
    void confirmImport(@NonNull String fileName, @NonNull byte[] fileData);
    void cancelImport();
  }
}
