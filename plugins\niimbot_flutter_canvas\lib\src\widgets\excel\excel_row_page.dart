// ignore_for_file: must_be_immutable

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_row_transform.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/plane_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_row_list_page.dart';
//import 'package:niimbot_flutter_canvas/src/widgets/excel/adjustable_bottom_sheet.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_row_search_page.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_row_selected.dart';

import '../components/item_divider.dart';

typedef SelectedCallBack = void Function(List<ExcelRowModel> value);

typedef SelectedResultCallBack = void Function(List<int> value);

GlobalKey<ExcelRowSearchState> searchPageKey = GlobalKey();

class ExcelRowPage extends StatefulWidget {
  List<ExcelHeaderModel> headers;
  bool isSelectAll = true;
  List<ExcelRowModel> rowsData;
  SelectedResultCallBack? confirmSelectedCallBack;
  bool fromNativePrint;

  ExcelRowPage({
    super.key,
    required this.headers,
    required this.rowsData,
    required this.isSelectAll,
    this.confirmSelectedCallBack,
    this.fromNativePrint = false,
  });

  @override
  State<StatefulWidget> createState() =>
      ExcelRowState(rowsData: rowsData, isSelectAll: isSelectAll);
}

class ExcelRowState extends State<ExcelRowPage> {
  ExcelRowState({required this.rowsData, required this.isSelectAll});

  late String keyword;
  bool isSearching = false;
  bool isOpenKeyboard = false;
  List<ExcelRowModel> rowsData;
  bool isSelectAll;
  late Map<int, double> heightCache;
  late int refer;

  @override
  void initState() {
    super.initState();
    refer = widget.fromNativePrint ? 2 : 1;
    CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
      "track": "view",
      "posCode": "108_316_313",
      "ext": {"refer": refer}
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
      ),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(mainAxisSize: MainAxisSize.max, children: [
        isSearching
            ? Expanded(child: getSearchPage())
            : Expanded(
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    height: 48,
                    decoration: const BoxDecoration(
                        color: ThemeColor.background,
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12))),
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsetsDirectional.only(start: 16),
                          child: PlaneButton(
                            child: Text(
                              intlanguage('app100000692', '取消'),
                              style: const TextStyle(
                                  color: ThemeColor.title,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                            onTap: () {
                              Navigator.of(context).pop();
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_316_314",
                                "ext": {"refer": refer}
                              });
                            } ,
                          ),
                        ),
                        const Spacer(),
                        ConstrainedBox(
                          constraints: BoxConstraints(
                              maxWidth:
                                  MediaQuery.sizeOf(context).width - 200),
                          child: Text(
                            widget.fromNativePrint ? intlanguage("app100001646", "选内容并打印") : intlanguage("app100001115", '选择行'),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                color: ThemeColor.mainTitle,
                                fontSize: 17,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        const Spacer(),
                        widget.fromNativePrint ? Container(width: 48) :Padding(
                          padding: EdgeInsetsDirectional.only(end: 16),
                          child: PlaneButton(
                            child: Text(
                              intlanguage('app00048', '确定'),
                              style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                            onTap: () {
                              List<int> data =
                                  ExcelRowModel.getSelectedResultData(rowsData,
                                      withHeader: true);
                              if (data.isEmpty) {
                                //LoadingMix.showToast(intlanguage('app100000842', '文件上传至多300KB'));
                                LoadingMix.showToast(
                                    intlanguage("app100001116", '选择的行不能为空'));
                                return;
                              }
                              widget.confirmSelectedCallBack?.call(data);
                              Navigator.of(context).pop();
                              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                "track": "click",
                                "posCode": "108_316_315",
                                "ext": {"refer": refer}
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  getSearchWidget(),
                  const ItemDivider(),
                  Expanded(
                    child: ExcelRowListWidget(
                      headers: widget.headers,
                      rowsData: rowsData,
                      selectedCallBack: (value) {
                        rowsData = value;
                        isSelectAll = ExcelRowModel.isSelectedAll(rowsData);
                        setState(() {});
                      },
                    ),
                  ), //ExcelRowListWidget
                  _buildBottomBar(),
                ],
              )),
      ]),
    );
  }

  Widget getSearchPage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: getExcelSearchPage(),
        ),
        _buildBottomBar(),
      ],
    );
  }

  Widget getExcelSearchPage() {
    return WillPopScope(
      onWillPop: () async {
        if (isOpenKeyboard){
          return false;
        } else if (isSearching){
          isSelectAll = ExcelRowModel.isSelectedAll(rowsData);
          isSearching = false;
          setState(() {});
          return false;
        }
        return true;
      },
      child: ExcelRowSearchPage(
        key: searchPageKey,
        rowsDatas: rowsData,
        headers: widget.headers,
        cancelClick: () {
          isSelectAll = ExcelRowModel.isSelectedAll(rowsData);
          isSearching = false;
          setState(() {});
        },
        isKeyboardShow: (value) {
          isOpenKeyboard = value;
          setState(() {});
        },
        selectedCallBack: (value) {
          isSelectAll = ExcelRowModel.isSelectedAll(value);
          ExcelRowModel.configData(rowsData, value);
          setState(() {});
        },
        searchResultCallBack: (value){
          isSelectAll = value;
          setState(() {

          });
        },
      ),
    );
  }

  //搜索框
  getSearchWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      child: Container(
        height: 32,
        decoration: BoxDecoration(
          color: ThemeColor.listBackground,
          borderRadius: BorderRadius.circular(16),
        ),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            isSearching = true;
            setState(() {});
            CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
              "track": "click",
              "posCode": "108_316_290",
              "ext": {
              }
            });
          },
          child: Row(
            children: [
              const SizedBox(
                width: 12,
              ),
              const SvgIcon(
                'assets/excel/search_icon.svg',
                matchTextDirection: true,
                color: Colors.grey,
                width: 16,
                height: 16,
              ),
              Expanded(
                  child: Container(
                padding: const EdgeInsetsDirectional.only(start: 8),
                child: Text(
                  intlanguage("app100001117", "搜索Excel内容"),
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF999999),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    double bottomPadding = Platform.isAndroid ? ScreenUtil().bottomBarHeight : 24;
    int selectedDataCount = ExcelRowModel.getSelectedData(rowsData).length;
    return Align(
      alignment: Alignment.bottomCenter,
      child: isOpenKeyboard
          ? SizedBox.shrink()
          : Container(
              padding: EdgeInsetsDirectional.only(end: 16,bottom: bottomPadding),
              height: (80-24)+ bottomPadding,
              alignment: Alignment.centerLeft,
        decoration: BoxDecoration(
          color: Colors.white, // Background color
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03), // Shadow color (with opacity)
              offset: Offset(0, -6), // Vertical offset (upwards)
              blurRadius: 5, // Blur radius
              spreadRadius: 0, // Spread radius
            ),

          ],
        ),
              child: Row(
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      selectAllClick();
                    },
                    child: Container(
                      //color: ThemeColor.background,
                      child: Row(
                        children: [
                          Container(
                              padding: const EdgeInsetsDirectional.only(
                                  top: 10, bottom: 10, start: 16, end: 6),
                              child: isSelectAll
                                  ? Image.asset(
                                      "assets/excel/icon_good_check.png",
                                      width: 20,
                                      height: 20,
                                      package: 'niimbot_flutter_canvas',
                                    )
                                  : Image.asset(
                                      "assets/excel/icon_good_uncheck.png",
                                      width: 20,
                                      height: 20,
                                      package: 'niimbot_flutter_canvas',
                                    )),
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: 60,
                            ),
                            child: Text(intlanguage('app00506', '全选'),
                                maxLines: 3,
                                style: TextStyle(
                                    fontSize: 13,
                                    fontWeight: FontWeight.w600,
                                    color: ThemeColor.subtitle)),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  GestureDetector(
                    //点击已选
                    onTap: () {
                      if (selectedDataCount == 0) {
                        return;
                      }
                      showSelectedRows();
                    },
                    child: Container(
                      child: Row(
                        children: [
                          Container(
                            constraints: BoxConstraints(
                              maxWidth: 85,
                            ),
                            child: Text(
                              intlanguage('app100001110', '已选\$行',
                                  param: [selectedDataCount.toString()]),
                              maxLines: 4,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color: ThemeColor.brand,
                              ),
                            ),
                          ),
                          Offstage(
                            offstage: selectedDataCount == 0,
                            child: SvgIcon(
                              'assets/excel/icon_arrow_up.svg',
                              color: Colors.red,
                              width: 18,
                              height: 18,
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                  const Spacer(),
                  Offstage(
                    offstage: widget.fromNativePrint == false,
                    child: GestureDetector(
                      onTap: (){
                        List<int> data =
                        ExcelRowModel.getSelectedResultData(rowsData,
                            withHeader: true);
                        if (data.isEmpty) {
                          //LoadingMix.showToast(intlanguage('app100000842', '文件上传至多300KB'));
                          LoadingMix.showToast(
                              intlanguage("app100001116", '选择的行不能为空'));
                          return;
                        }
                        widget.confirmSelectedCallBack?.call(data);
                        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                          "track": "click",
                          "posCode": "108_316_315",
                          "ext": {"refer": refer}
                        });
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 36,
                        padding: EdgeInsets.symmetric(horizontal: 20),
                        decoration: const BoxDecoration(
                          color: ThemeColor.brand,
                          borderRadius: BorderRadius.all(Radius.circular(44)),
                        ),
                        child: Text(intlanguage('app01031', '完成'),
                        style: const TextStyle(
                            color: ThemeColor.background,
                            fontSize: 16,
                            fontWeight: FontWeight.w600),),
                      ),
                    ),
                  )
                ],
              ),
            ),
    );
  }

  selectAllClick() {
    if (isSearching) {
      //当前搜索页面没有数据的时候点击全选不更新全选状态
      if (searchPageKey.currentState?.showRowData == null ||
          (searchPageKey.currentState?.showRowData?.length ?? 0) == 0) {
        return;
      }
      isSelectAll = !isSelectAll;
      searchPageKey.currentState?.selectedAll(isSelectAll);
    } else {
      isSelectAll = !isSelectAll;
      if (isSelectAll) {
        for (var element in rowsData) {
          element.isSelected = true;
        }
      } else {
        for (var element in rowsData) {
          element.isSelected = false;
        }
      }
    }

    setState(() {});
  }

  showSelectedRows() {
    List<ExcelRowModel> selectedRow = ExcelRowModel.getSelectedData(rowsData);
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        //enableDrag: false,
        isScrollControlled: true,
        context: context,
        builder: (_) {
          return ExcelSelectedRowListWidget(
            headers: widget.headers,
            rowsData: selectedRow,
            cancelCallBack: (value) {
              ExcelRowModel.cancelSelectedStatus(value, rowsData);
              isSelectAll = ExcelRowModel.isSelectedAll(rowsData);
              setState(() {});
            },
          );
        });
  }
}
