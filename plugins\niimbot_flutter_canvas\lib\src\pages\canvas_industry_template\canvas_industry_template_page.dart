import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/canvas_industry_template_state.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/search_template/search_template_page.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/canvas_industry_template_item_widget.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/material_indicator_widget.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/no_template_result_widget.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/normal_button.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/size_select_page.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/template_sort_page.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/attr_widget_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:nimbot_state_manager/controller_adapters/state_management_adapter.dart';
import 'package:nimbot_state_manager/widget_adapter/state_manage_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

class CanvasIndustryTemplatePage extends StatefulWidget {
  /// 状态管理适配器
  IStateManagementAdapter adapter;
  double industryTemplateHeight;

  /// 点击事件处理函数，接收一个Map参数
  Function(Map<String, dynamic>?)? onTap;

  /// 面板状态
  AttrPanelState? panelState;

  /// 刷新事件处理函数
  Function? onRefreshTap;

  /// 下拉事件处理函数
  Function? onPullDownTap;

  CanvasIndustryTemplatePage(this.adapter,
      {Key? key, this.onTap, this.onRefreshTap, this.onPullDownTap, this.panelState, this.industryTemplateHeight = 0})
      : super(key: key);

  @override
  _CanvasIndustryTemplatePageState createState() => _CanvasIndustryTemplatePageState(adapter);
}

class _CanvasIndustryTemplatePageState extends State<CanvasIndustryTemplatePage> {
  IStateManagementAdapter adapter;

  _CanvasIndustryTemplatePageState(this.adapter);

  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 初始化画布行业选择状态为未选择
    adapter.getState().sceneSelect = CanvasIndustrySelectedState.none;
    // 记录行业模板展示事件
    adapter.getController().trackIndustryTemplateShow();
    // 添加滚动控制器监听器
    _scrollController.addListener(() {
      // 当滚动位置超过预设阈值时执行逻辑
      if (_scrollController.position.pixels > adapter.getState().scrollOffset && adapter.getState().scrollOffset > 0) {
        // 当面板状态为正常时，触发刷新回调
        // 执行面板刷新逻辑
        if (widget.panelState == AttrPanelState.normal) {
          widget.onRefreshTap?.call();
        }
      }
      // 更新滚动偏移量
      adapter.getState().scrollOffset = _scrollController.position.pixels;
    });
  }

  @override
  void dispose() {
    adapter.getState().lastPosition = 0.0;
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ThemeColor.background,
      child: Material(
        child: StateManageWidget.dynamicTypes(
          adapter, // 传入 IStateManagementAdapter 实例
          "all", // 传入状态字符串
          (context) => adapter.getState().isSearchTemplate
              // 使用SearchTemplatePage构建页面，用于展示搜索模板
              ? SearchTemplatePage(
                  // 传入控制器，用于页面与业务逻辑的交互
                  adapter.getController(),
                  // 传入panelState，用于控制面板的状态
                  widget.panelState!,
                  // 处理搜索模板数据更新的回调函数
                  (value) {
                    // 当value不为空且为Map类型时，尝试更新标签模型
                    if (value != null && value is Map) {
                      try {
                        // 将value中的labelData转换为Map，并更新标签模型
                        adapter.getController().updateLabelModel(Map<String, dynamic>.from(value['labelData']));
                      } catch (error) {
                        // 捕获并处理异常，避免影响后续逻辑
                      }
                    }
                    // 设置搜索模板状态为false，恢复到默认状态
                    adapter.getState().isSearchTemplate = false;
                    // 刷新数据，显示全部内容
                    adapter.getController().update(["all"]);
                  },
                  // 处理下拉刷新的回调函数
                  widget.onPullDownTap!,
                  // 处理取消搜索模板的回调函数
                  () {
                    // 设置搜索模板状态为false，恢复到默认状态
                    adapter.getState().isSearchTemplate = false;
                    // 刷新数据，显示全部内容
                    adapter.getController().update(["all"]);
                  },
                  // 处理刷新操作的回调函数
                  () {
                    // 调用onRefreshTap回调函数，执行刷新操作
                    widget.onRefreshTap?.call();
                  },
                  // 处理条目点击的回调函数
                  (model) {
                    // 调用onTap回调函数，传入点击的模型对象
                    widget.onTap?.call(model);
                  })
              : Container(
                  color: ThemeColor.listBackground,
                  child: Column(
                    children: [
                      StateManageWidget.dynamicTypes(
                        adapter, // 传入 IStateManagementAdapter 实例
                        "cardStatus", // 传入状态字符串
                        (context) => _checkFactorWidget(), // 传入构建函数
                      ),
                      StateManageWidget.dynamicTypes(
                        adapter, // 传入 IStateManagementAdapter 实例
                        "templateListStatus", // 传入状态字符串
                        (context) => _slidingUpPanel(context), // 传入构建函数
                      ),

                      /// 顶部弹窗及内容组件
                      // _slidingUpPanel(context),
                    ],
                  ),
                ), // 传入构建函数
        ),
      ),
    );
  }

  _checkFactorWidget() {
    /// 根据硬件名称的长度，截断名称以保持长度不超过12个字符。
    /// 如果硬件名称的长度超过12个字符，为了适应显示或格式要求，将其缩短为前12个字符加上省略号。
    if (adapter.getState().hardWareWithSizeName.length > 12) {
      adapter.getState().hardWareWithSizeName = adapter.getState().hardWareWithSizeName.substring(0, 12) + '...';
    }

    return Container(
      padding: EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
      decoration: const BoxDecoration(
          color: ThemeColor.background,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SizeSelectPage(adapter, adapter.getState().sceneSelect == CanvasIndustrySelectedState.industrySize,
                      widget.panelState!, () {
                    widget.onRefreshTap?.call();
                  }),
                  SizedBox(
                    width: 20,
                  ),
                  AnimationButton(adapter.getState(), adapter.getState().sortName,
                      adapter.getController().isIndustryScenes(), ThemeColor.COLOR_161616, () {
                    adapter.getController().topDialogClickListener();
                    widget.onRefreshTap?.call();
                  }),
                ],
              ),
              _searchWidget(context)
            ],
          ),
        ],
      ),
    );
  }

  _searchWidget(BuildContext context) {
    return Container(
      width: 100,
      margin: EdgeInsetsDirectional.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: ThemeColor.COLOR_F5F5F5,
        borderRadius: BorderRadius.circular(20),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          adapter.getController().updateSearchPage();
          widget.onRefreshTap?.call();
        },
        child: Container(
          padding: EdgeInsetsDirectional.symmetric(vertical: 7),
          child: Row(
            children: [
              const SizedBox(
                width: 10,
              ),
              SvgIcon(
                'assets/common/search_icon.svg',
                color: ThemeColor.COLOR_BFBFBF,
                useDefaultColor: false,
              ),
              Expanded(
                  child: Container(
                      padding: const EdgeInsetsDirectional.only(start: 4),
                      child: Text(
                        intlanguage('app100000494', '搜索模板名称、内容、关键字'),
                        style: const TextStyle(
                          color: ThemeColor.subtitle,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ))),
              // const SizedBox(
              //   width: 40,
              // ),
            ],
          ),
        ),
      ),
    );
  }

  _slidingUpPanel(BuildContext context) {
    return Expanded(
      child: SlidingUpPanel(
        controller: adapter.getState().panelController,
        minHeight: 0,
        maxHeight: MediaQuery.sizeOf(context).height * 0.6,
        slideDirection: SlideDirection.DOWN,
        isDraggable: false,
        backdropEnabled: true,
        backdropColor: Colors.black,
        backdropOpacity: 0.2,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12.0),
          bottomRight: Radius.circular(12.0),
        ),
        onPanelSlide: (position) {
          // 检查当前动画的收起状态
          if (adapter.getState().lastPosition > position + 0.01) {
            // 当前场景为行业场景时，重置场景选择为无
            // 正在收起
            if (adapter.getState().sceneSelect == CanvasIndustrySelectedState.industryScenes) {
              adapter.getState().sceneSelect = CanvasIndustrySelectedState.none;
            }
          }
          // 更新动画的最后位置
          adapter.getState().lastPosition = position;
        },
        onPanelOpened: () {
          CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
            "track": "show",
            "posCode": "011_191",
            "ext": {'type1': adapter.getState().sizeMenuList[adapter.getState().onSizeSelectIndex], 'source': 2}
          });
        },
        onPanelClosed: () {},
        panel: Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.0),
                bottomRight: Radius.circular(12.0),
              ),
            ),
            child: TemplateSortPage(adapter)),
        body: Container(
          color: ThemeColor.background,
          padding: EdgeInsets.fromLTRB(14, 0, 14, 0),
          child: _templatePageState(context),
        ),
      ),
    );
  }

  ///首页状态显示
  _templatePageState(BuildContext context) {
    num bottomHeight = MediaQuery.sizeOf(context).height - widget.industryTemplateHeight + 20;

    switch (adapter.getState().homeState) {
      case CanvasIndustryHomeState.empty:
        //是否仅显示我要反馈按钮
        bool onlyFeedback = false;
        double top = 100;
        if (widget.panelState == AttrPanelState.normal) {
          top = 50;
          onlyFeedback = true;
        }
        return Container(
            padding: EdgeInsets.only(top: top),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                NoTemplateResultWidget(
                  onFeedbackTap: () async {
                    CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                      "track": "click",
                      "posCode": "011_453_455",
                      "ext": {"source": 1}
                    });
                    bool? feedbackResult = await CanvasPluginManager().hostMethodImpl?.jumpToTemplateFeedbackPage();
                    if (feedbackResult == true) {
                      setState(() {
                        adapter.getState().notifyState = true;
                      });
                    }
                  },
                  hasFeedback: adapter.getState().notifyState,
                  onlyDisplayFeedbackBtn: onlyFeedback,
                ),
              ],
            ));
      case CanvasIndustryHomeState.loading:
        return Container(
            padding: EdgeInsets.only(bottom: bottomHeight.toDouble()),
            child: Stack(
              children: [
                MediaQuery.removePadding(
                  child: MasonryGridView.count(
                    crossAxisCount: 2,
                    mainAxisSpacing: 15,
                    crossAxisSpacing: 15,
                    itemCount: adapter.getState().categoryListModel.length,
                    //  shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return CanvasIndustryTemplateItemWidget(
                        model: adapter.getState().categoryListModel[index],
                        index: index,
                        showTransitionPageState: adapter.getState().showTransitionPageState,
                        onTap: (model) {
                          widget.onTap?.call(model?.toJson());
                        },
                      );
                    },
                  ),
                  context: context,
                  removeTop: true,
                ),
                Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.only(bottom: 80),
                  child: const CupertinoActivityIndicator(
                    radius: 20,
                    animating: true,
                  ),
                ),
              ],
            ));
      case CanvasIndustryHomeState.error:
        return Container(
            padding: const EdgeInsets.only(bottom: 300),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  "assets/common/no_net.png",
                  package: 'niimbot_flutter_canvas',
                  fit: BoxFit.contain,
                ),
                Text(
                  intlanguage('app100000625', '当前网络状态异常'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: NormalButton(
                    title: intlanguage('app100000626', '重新试试	'),
                    height: 40,
                    width: context.width / 3.3,
                    textColor: ThemeColor.brand,
                    decoration:
                        BoxDecoration(color: ThemeColor.listBackground, borderRadius: BorderRadius.circular(30)),
                    selectedClosure: () {
                      adapter.getState().page = 1;
                      adapter.getState().categoryListModel.clear();
                      adapter.getController().getIndustryList();
                    },
                  ),
                ),
              ],
            ));
      case CanvasIndustryHomeState.showContainer:
        return Container(
            color: ThemeColor.background,
            padding: EdgeInsets.only(bottom: bottomHeight.toDouble()),
            child: MediaQuery.removePadding(
              context: context,
              removeTop: true,
              child: SmartRefresher(
                controller: adapter.getState().refreshController,
                enablePullUp: true,
                enablePullDown: true,
                onRefresh: () {
                  widget.onPullDownTap?.call();
                  adapter.getState().refreshController.refreshCompleted();
                },
                header: DeleteAnimationHeader(
                  backgroundColor: Colors.blue,
                  color: Colors.white,
                ),
                onLoading: () {
                  adapter.getController().onLoading();
                },
                child: MasonryGridView.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                  controller: _scrollController,
                  itemCount: adapter.getState().categoryListModel.length,
                  itemBuilder: (context, index) {
                    return CanvasIndustryTemplateItemWidget(
                      model: adapter.getState().categoryListModel[index],
                      index: index,
                      showTransitionPageState: adapter.getState().showTransitionPageState,
                      onTap: (model) {
                        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                          "track": "click",
                          "posCode": "011_032_039",
                          "ext": {
                            'type': 2,
                            'temp_id': model?.id,
                            'pos': index,
                            'tab_id': model?.marketingCategoryId,
                            'is_vip': model?.isVipRes == true ? 1 : 0,
                            'type1': adapter.getState().sizeMenuList[adapter.getState().onSizeSelectIndex],
                            'type2': adapter.getState().sortName,
                            'source': 2
                          }
                        });
                        widget.onTap?.call(model?.toJson());
                      },
                    );
                  },
                ),
              ),
            ));
      default:
        return Container();
    }
  }

  showIconToast() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 18,
                width: 18,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: ThemeColor.background),
              ),
              Image.asset(
                'assets/images/industry_template/home/<USER>',
                height: 20,
                width: 20,
              ),
            ],
          ),
          SizedBox(
            width: 12.0,
          ),
          Text(
            intlanguage('app100000629', '反馈成功!'),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.background),
          ),
        ],
      ),
    );
  }
}

class AnimationButton extends StatefulWidget {
  CanvasIndustryTemplateState state;
  bool isSelected;
  var name;
  final Color iconColor;
  final Function selectedClosure;

  AnimationButton(this.state, this.name, this.isSelected, this.iconColor, this.selectedClosure);

  @override
  State<AnimationButton> createState() => _AnimationButtonState();
}

class _AnimationButtonState extends State<AnimationButton> with SingleTickerProviderStateMixin {
  AnimationController? _animationController;

  @override
  void initState() {
    _animationController =
        AnimationController(vsync: this, duration: const Duration(milliseconds: 250), lowerBound: 0.0, upperBound: 0.5);
    super.initState();
  }

  @override
  void didUpdateWidget(covariant AnimationButton oldWidget) {
    if (widget.isSelected) {
      setState(() {
        _animationController?.forward();
      });
    } else {
      setState(() {
        _animationController?.reverse();
      });
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        child: Container(
            color: ThemeColor.background,
            alignment: Alignment.center,
            height: 44,
            padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 5),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  constraints: BoxConstraints(
                    maxWidth: 75,
                  ),
                  child: Text(
                    widget.name,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_161616),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(
                  width: 2,
                ),
                RotationTransition(
                  turns: _animationController!,
                  child: SvgIcon(
                    'assets/common/arrow_down_black.svg',
                    color: ThemeColor.COLOR_161616,
                    useDefaultColor: false,
                  ),
                ),
              ],
            )),
        onTap: () {
          widget.selectedClosure?.call();
        });
  }
}
