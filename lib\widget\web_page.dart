import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:text/utils/theme_color.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebPage extends StatefulWidget {
  final String title;
  final String url;
  final String rightCloseText;
  final bool isFullScreen;
  const WebPage({Key? key, this.title = "", this.url = "", this.rightCloseText = '', this.isFullScreen = false})
      : super(key: key);

  @override
  _WebPageState createState() => _WebPageState();
}

class _WebPageState extends State<WebPage> {
  double? progress = 0.0;
  String webTitle = '';
  final Completer<WebViewController> _controller = Completer<WebViewController>();
  @override
  Widget build(BuildContext context) {
    return getContentWidget();
  }

  Widget getContentWidget() {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      padding: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(
        children: [
          progress! >= 1
              ? Container()
              : LinearProgressIndicator(
                  value: progress,
                  minHeight: 2,
                  backgroundColor: ThemeColor.border,
                  valueColor: AlwaysStoppedAnimation(ThemeColor.brand),
                ),
          // Expanded(
          //   child: InAppWebView(
          //     initialUrlRequest: URLRequest(url: Uri.parse(widget.url)),
          //     onScrollChanged: (InAppWebViewController controller, int x, int y) {},
          //     onProgressChanged: (InAppWebViewController controller, int value) {
          //       progress = value / 100;
          //       setState(() {});
          //     },
          //   ),
          // ),
          Expanded(
            child: WebView(
              initialUrl: widget.url,
              javascriptMode: JavascriptMode.unrestricted,
              backgroundColor: Colors.white,
              onWebViewCreated: (WebViewController webViewController) {
                _controller.complete(webViewController);
              },
              // },
              onPageStarted: (String url) {
                // isLoading = true;
              },
              onPageFinished: (String url) {
                _getTitle();
                setState(() {
                  // isLoading = false; // 页面加载完成，更新状态
                });
              },
              onWebResourceError: (error) {
                setState(() {
                  // isLoading = false;
                  // isError = true;
                });
              },
              javascriptChannels: <JavascriptChannel>{
                JavascriptChannel(
                  name: 'goBack',
                  onMessageReceived: (JavascriptMessage message) {
                    Navigator.of(context).pop();
                  },
                ),
                JavascriptChannel(
                  name: 'openHyperlinkWithSystemBrowser',
                  onMessageReceived: (JavascriptMessage message) async {
                    var uri = Uri.parse(message.message);
                    if (await canLaunchUrl(uri)) {
                      // Launch the App
                      await launchUrl(uri,
                          mode: Platform.isIOS ? LaunchMode.externalApplication : LaunchMode.platformDefault);
                    }
                  },
                ),
              },
              onProgress: (int value) {
                progress = value / 100;
                setState(() {
                  // isLoading = false; // 页面加载完成，更新状态
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  _getTitle() {
    _controller.future.then((value) {
      value.getTitle().then((title) {
        webTitle = title ?? "";
        setState(() {});
      });
    });
  }
}
