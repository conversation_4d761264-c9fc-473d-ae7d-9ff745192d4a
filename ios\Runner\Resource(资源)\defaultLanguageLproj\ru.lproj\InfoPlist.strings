/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Приложению требуется доступ к камере для сканирования штрих-кодов, распознавания текста и съемки. Разрешить использование камеры?";
NSBluetoothPeripheralUsageDescription = "Приложению требуется доступ к Bluetooth для подключения принтера. Разрешить использование Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Приложению требуется доступ к Bluetooth для подключения принтера. Разрешить использование Bluetooth?";
NSContactsUsageDescription = "Приложению требуется доступ к контактам для чтения списка контактов. Разрешить доступ к контактам?";
NSMicrophoneUsageDescription = "Приложению требуется доступ к микрофону для распознавания речи. Разрешить использование микрофона?";
NSPhotoLibraryUsageDescription = "Данное разрешение необходимо для печати изображений, распознавания штрих-кодов и QR-кодов, распознавания текста и установки пользовательского аватара. Выберите «Разрешить доступ ко всем фотографиям» для корректной работы с галереей в приложении «NIIMBOT». При выборе «Выбрать фотографии...» невыбранные и новые фотографии будут недоступны в приложении.";
NSLocationWhenInUseUsageDescription = "Для использования ближайших сетей Wi-Fi приложению «NIIMBOT» требуется разрешение на определение местоположения";
NSLocationAlwaysUsageDescription = "Для использования ближайших сетей Wi-Fi приложению «NIIMBOT» требуется разрешение на определение местоположения";
NSLocationAlwaysAndWhenInUseUsageDescription = "Для использования ближайших сетей Wi-Fi приложению «NIIMBOT» требуется разрешение на определение местоположения";
NSSpeechRecognitionUsageDescription = "Приложению требуется ваше разрешение для доступа к функции распознавания речи. Разрешить доступ к распознаванию речи?";
NSLocalNetworkUsageDescription = "Это приложение требует доступа к ​Локальной сети (LAN)​​ для службы поиска устройств в локальной сети и настройки сети.";
"UILaunchStoryboardName" = "LaunchScreen";
