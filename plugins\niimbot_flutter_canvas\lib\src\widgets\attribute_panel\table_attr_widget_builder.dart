import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:provider/provider.dart';

import '/src/localization/localization_public.dart';
import '/src/model/canvas_element.dart';
import '/src/model/element/table_cell_element.dart';
import '/src/model/element/table_element.dart';
import '/src/model/template_data.dart';
import '/src/provider/elements_data_changed_notifier.dart';
import '/src/widgets/attribute_panel/comm/common_import_widget.dart';
import '/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';
import '/src/widgets/attribute_panel/comm/value_slider_widget.dart';
import '/src/widgets/box_frame/canvas_box_frame.dart';
import '/src/widgets/canvas/canvas_theme_widget.dart';
import '/src/widgets/components/item_divider.dart';
import './comm/multi_color_high_light.dart';
import 'attr_widget_builder.dart';
import 'comm/common_num_change_widget.dart';
import 'comm/text_font_panel_mini_widget.dart';
import 'comm/text_font_panel_widget.dart';
import 'element_attribute_panel.dart';
import 'handler/table_panel_handler.dart';

class LineModeType {
  final int lineMode;
  final String title;

  LineModeType(this.lineMode, this.title);
}

class TableAttrWidgetBuilder extends AttrWidgetBuilder {
  final List<LineModeType> lineModeTypes = [
    LineModeType(LineMode.FIX_W_ADAPT_H, '字号缩小以适应单元格'),
    LineModeType(LineMode.FIX_W_ADAPT_H, '单元格变高以适应文本'),
  ];

  TableAttrWidgetBuilder(
      AttrPanelState attrPanelState,
      double height,
      ElementValueEditorDisplay onElementValueEditorDisplay,
      ImportExcelFromElementCall importExcelFromElementCall,
      ChangeDataManualInputField changeDataManualInputField,
      ChangeColumnIndexCall changeColumnIndexCall,
      Function(Function, {bool isPopLogin})? guideBuyVip)
      : super(attrPanelState, height,
            onElementValueEditorDisplay: onElementValueEditorDisplay,
            importExcelFromElementCall: importExcelFromElementCall,
            changeDataManualInputField: changeDataManualInputField,
            changeColumnIndexCall: changeColumnIndexCall,
            guideBuyVip: guideBuyVip);

  @override
  bool canScroll({required int position, required CanvasElement element}) {
    int targetPosition = 0;
    if ((element.data as TableElement).getFocusedCells().length == 1) {
      targetPosition = 1;
    }
    return position == targetPosition ? false : true;
  }

  @override
  List<MenuTag> getMenus(List<CanvasElement> canvasElements) {
    int index = 0;

    /// 仅选中一个 table
    /// 判断只有一个单元格或一个合并单元格,显示写入面板
    bool importVisible = (canvasElements ?? []).length == 1 &&
        (canvasElements.first.data as TableElement).getFocusedCells().length ==
            1;

    menus = [
      if (importVisible)
        MenuTag(
            index: index++,
            text: intlanguage('app100000810', '写入'),
            isSelected: false,
            trackName: '写入'),
      MenuTag(
          index: index++,
          text: intlanguage("app00005", "表格"),
          isSelected: true,
          trackName: '表格'),
      MenuTag(
          index: index++,
          text: intlanguage("app00002", "文本"),
          isSelected: false,
          trackName: '文本'),
      MenuTag(
          index: index++,
          text: intlanguage('app01005', '字体'),
          isSelected: false,
          trackName: '字体'),
      MenuTag(
          index: index++,
          text: intlanguage('app00168', '对齐'),
          isSelected: false,
          trackName: '对齐'),
    ];
    return menus;
  }

  @override
  Widget buildAttrWidget(MenuTag menuTag, List<CanvasElement> canvasElements,
      BuildContext context, VoidCallback? refresh) {
    TemplateData? templateData = CanvasObjectSharedWidget.canvasDataOf(context);
    if (menuTag.text == intlanguage("app00005", "表格")) {
      return basicStyle(canvasElements, context, refresh);
    } else if (menuTag.text == intlanguage("app00002", "文本")) {
      return textStylePanel(canvasElements, context, refresh);
    } else if (menuTag.text == intlanguage('app01005', '字体')) {
      return textFontPanel(canvasElements, context, refresh, guideBuyVip);
    } else if (menuTag.text == intlanguage('app00168', '对齐')) {
      return layoutPanel(canvasElements, context, refresh);
    } else if (menuTag.text == intlanguage('app100000810', '写入')) {
      TableCellElement cellElement =
          (canvasElements.first.data as TableElement).getFocusedCells().first;
      return Padding(
        padding:
            const EdgeInsets.only(top: 10, left: 16, right: 16, bottom: 10),
        child: CommonImportWidget(
          canvasElement: cellElement.toCanvasElement(),
          focusedElement: canvasElements.first,
          columnIndex: cellElement.bindingColumn ?? 0,
          onElementValueEditorDisplay: onElementValueEditorDisplay,
          importExcelFromElementCall: importExcelFromElementCall,
          changeColumnIndexCall: changeColumnIndexCall,
          changeDataManualInputField: changeDataManualInputField,
        ),
      );
    }
    return Container(
      color: Colors.transparent,
      width: double.infinity,
      height: 100,
    );
  }

  @override
  Widget textFontPanel(List<CanvasElement> canvasElements, BuildContext context,
      VoidCallback? refresh, Function(Function)? guideBuyVip) {
    TableElement tableElement = canvasElements.first.data as TableElement;
    final focusCells = tableElement.getFocusedCells();

    if (focusCells.length == 0) {
      /// 未选中则对全表属性进行更新
      focusCells.addAll(tableElement.cells);
      focusCells.addAll(tableElement.combineCells);
    } else {
      /// 合并的单元格, 则将合并中的第一个原始单元格也作为修改目标
      focusCells
          .where((element) => element.isCombine())
          .toList()
          .forEach((element) {
        focusCells.add(element.firstCellOfCombine!);
      });
    }

    if (focusCells.length == 0) {
      return Container();
    }

    return AnimatedSwitcher(
        duration: Duration(milliseconds: animatedDurationMilliseconds),
        child: /*attrPanelState == AttrPanelState.shortest*/ false
            ? TextFontPanelMiniWidget(
                canvasElements: focusCells.map((e) => e.toCanvasElement()).toList(),
                refresh: () {
                  tableElement.resetCellsImageCache();
                  refresh?.call();
                },
              )
            : TextFontPanelWidget(
                canvasElements: focusCells.map((e) => e.toCanvasElement()).toList(),
                guideBuyVip: guideBuyVip,
                isTableCellText: true,
                refresh: () {
                  tableElement.resetCellsImageCache();
                  refresh?.call();
                },
                height: height,
              ));
  }

  double getSmallerRowHeight(
      TableElement tableElement, List<TableCellElement> focusCells) {
    if (focusCells.length == 0) {
      double smalllestValue = tableElement.rowHeight[0];
      for (var i = 0; i < tableElement.rowHeight.length; i++) {
        if (tableElement.rowHeight[i] < smalllestValue) {
          smalllestValue = tableElement.rowHeight[i];
        }
      }
      return smalllestValue;
    } else {
      double smalllestValue = tableElement.rowHeight[
          focusCells.first.isCombine()
              ? focusCells.first.firstCellOfCombine!.rowIndex
              : focusCells.first.rowIndex];
      for (var i = 0; i < focusCells.length; i++) {
        double height = 0;
        if (focusCells[i].isCombine()) {
          height = tableElement
              .rowHeight[focusCells[i].firstCellOfCombine!.rowIndex];
        } else {
          height = tableElement.rowHeight[focusCells[i].rowIndex];
        }
        if (height < smalllestValue) {
          smalllestValue = height;
        }
      }
      return smalllestValue;
    }
  }

  double getSmallerColumnWidth(
      TableElement tableElement, List<TableCellElement> focusCells) {
    if (focusCells.length == 0) {
      double smalllestValue = tableElement.columnWidth[0];
      for (var i = 0; i < tableElement.columnWidth.length; i++) {
        if (tableElement.columnWidth[i] < smalllestValue) {
          smalllestValue = tableElement.columnWidth[i];
        }
      }
      return smalllestValue;
    } else {
      double smalllestValue = tableElement.columnWidth[
          focusCells.first.isCombine()
              ? focusCells.first.firstCellOfCombine!.columnIndex
              : focusCells.first.columnIndex];
      for (var i = 0; i < focusCells.length; i++) {
        double height = 0;
        if (focusCells[i].isCombine()) {
          height = tableElement
              .columnWidth[focusCells[i].firstCellOfCombine!.columnIndex];
        } else {
          height = tableElement.columnWidth[focusCells[i].columnIndex];
        }
        if (height < smalllestValue) {
          smalllestValue = height;
        }
      }
      return smalllestValue;
    }
  }

  /// 样式
  Widget basicStyle(List<CanvasElement> canvasElements, BuildContext context,
      VoidCallback? refresh) {
    TableElement tableElement = canvasElements.first.data as TableElement;
    List<TableCellElement> focusCells = tableElement.getFocusedCells();

    // double rowHeight = focusCells.length == 0
    //     ? 5.0
    //     : tableElement.rowHeight[
    //         focusCells.first.isCombine() ? focusCells.first.firstCellOfCombine.rowIndex : focusCells.first.rowIndex];
    //
    // double columnWidth = focusCells.length == 0
    //     ? 15.0
    //     : tableElement.columnWidth[focusCells.first.isCombine()
    //         ? focusCells.first.firstCellOfCombine.columnIndex
    //         : focusCells.first.columnIndex];

    double rowHeight = getSmallerRowHeight(tableElement, focusCells);
    if (rowHeight < 1.0) {
      rowHeight = 1.0;
    } else if (rowHeight > TableCellElement.cellHeightMax) {
      rowHeight = TableCellElement.cellHeightMax;
    }
    double columnWidth = getSmallerColumnWidth(tableElement, focusCells);
    if (columnWidth < 1.0) {
      columnWidth = 1.0;
    } else if (columnWidth > TableCellElement.cellWidthMax) {
      columnWidth = TableCellElement.cellWidthMax;
    }

    ///取行高最小值

    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    return KeyboardVisibilityBuilder(builder: (_, bool isKeyBoardVisible) {
      return Container(
        // padding: EdgeInsets.all(16),
        color: Colors.white,
        constraints:
            BoxConstraints(maxHeight: max(MediaQuery.viewInsetsOf(context).bottom + 100, height), minHeight: height),
        child: SingleChildScrollView(
          child: Container(
            padding: EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                      color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                      borderRadius: BorderRadius.all(Radius.circular(12))),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CommonNumChangeWidget<int>(
                        isEnable: tableElement.isLock == 0,
                        title: intlanguage('app100000777', '行数'),
                        value: tableElement.row,
                        canInput: tableElement.isLock == 0,
                        allowInputRegExp: RegExp(r'^(?:[1-9]|1[0-9]|20)$'),
                        max: 20,
                        min: 1,
                        stepValue: 1,
                        valueChanged: (v) {
                          ///添加删除行
                          int changeRowCount = v - tableElement.row;
                          TablePanelHandler.handleChangeRow(context, canvasElements, changeRowCount);
                          refresh?.call();
                        },
                      ),
                      ItemDivider(),
                      CommonNumChangeWidget<int>(
                        isEnable: tableElement.isLock == 0,
                        title: intlanguage('app100000778', '列数'),
                        value: tableElement.column,
                        canInput: tableElement.isLock == 0,
                        allowInputRegExp: RegExp(r'^(?:[1-9]|1[0-9]|20)$'),
                        max: 20,
                        min: 1,
                        stepValue: 1,
                        valueChanged: (v) {
                          ///添加删除列
                          int changeColumnCount = v - tableElement.column;
                          TablePanelHandler.handleChangeColumn(context, canvasElements, changeColumnCount);
                          refresh?.call();
                        },
                      ),
                    ],
                  ),
                ),
                Container(
                    margin: EdgeInsets.only(top: 10, bottom: 10),
                    decoration: BoxDecoration(
                        color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                        borderRadius: BorderRadius.all(Radius.circular(12))),
                    width: MediaQuery.sizeOf(context).width,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ValueSliderWidget<double>(
                          key: Key(focusCells.map((e) => e.id).join('') + '-row-height'),
                          min: 1.0,
                          max: TableCellElement.cellHeightMax,
                          value: rowHeight,
                          stepValue: 0.1,
                          enable: tableElement.isLock == 0,
                          divisions: (TableCellElement.cellHeightMax - 1).toInt() * 10,
                          title: intlanguage('app01017', '行高'),
                          width: MediaQuery.sizeOf(context).width - 16 * 2,
                          valueChanged: (double value) {
                            if (focusCells.length == 0) {
                              for (int index = 0; index < tableElement.row; index++) {
                                tableElement.rowHeight[index] = value;
                              }
                            } else {
                              focusCells.forEach((element) {
                                if (element.isCombine()) {
                                  tableElement.rowHeight[element.firstCellOfCombine!.rowIndex] = value;
                                } else {
                                  tableElement.rowHeight[element.rowIndex] = value;
                                }
                              });
                            }
                            tableElement.resetCellsImageCache();
                            Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                .setDataChangedElements(canvasElements);
                          },
                        ),
                        ItemDivider(),
                        ValueSliderWidget<double>(
                          key: Key(focusCells.map((e) => e.id).join('') + '-column-width'),
                          min: 1.0,
                          max: TableCellElement.cellWidthMax,
                          value: columnWidth,
                          stepValue: 0.1,
                          enable: tableElement.isLock == 0,
                          divisions: (TableCellElement.cellWidthMax - 1).toInt() * 10,
                          title: intlanguage('app01018', '列宽'),
                          width: MediaQuery.sizeOf(context).width - 16 * 2,
                          valueChanged: (double value) {
                            if (focusCells.length == 0) {
                              for (int index = 0; index < tableElement.column; index++) {
                                tableElement.columnWidth[index] = value;
                              }
                            } else {
                              focusCells.forEach((element) {
                                if (element.isCombine()) {
                                  tableElement.columnWidth[element.firstCellOfCombine!.columnIndex] = value;
                                } else {
                                  tableElement.columnWidth[element.columnIndex] = value;
                                }
                              });
                            }
                            tableElement.resetCellsImageCache();
                            Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                .setDataChangedElements(canvasElements);
                          },
                        ),
                      ],
                    )),

                ///线粗
                Container(
                  height: 44,
                  decoration: BoxDecoration(
                      color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                      borderRadius: BorderRadius.all(Radius.circular(12))),
                  child: Center(
                    child: CommonNumChangeWidget<double>(
                      title: intlanguage('app00154', '线粗'),
                      value: tableElement.lineWidth.digitsRoundOff(1).toDouble(),
                      max: 3.0,
                      min: 0.2,
                      stepValue: 0.1,
                      isEnable: tableElement.isLock == 0,
                      valueChanged: (v) {
                        tableElement.lineWidth = double.parse(v.toStringAsFixed(1));

                        /// 过细情况下，打印机打印线条不清晰
                        if (tableElement.lineWidth == 0.2) {
                          tableElement.lineWidth = 0.25;
                        } else if (tableElement.lineWidth == 0.3) {
                          tableElement.lineWidth = 0.35;
                        }

                        tableElement.combineCells.forEach((element) {
                          element.imageCache = null;
                        });
                        Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                            .setDataChangedElements(canvasElements);
                      },
                    ),
                  ),
                ),

                ///文本溢出，先屏蔽
                // Container(
                //   decoration: BoxDecoration(
                //       color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                //       borderRadius: BorderRadius.all(Radius.circular(12))),
                //   margin: EdgeInsets.only(bottom: 10),
                //   padding: Directionality.of(context) == TextDirection.rtl
                //       ? EdgeInsets.fromLTRB(12, 0, 20, 0)
                //       : EdgeInsets.fromLTRB(20, 0, 12, 0),
                //   child: Row(
                //     children: [
                //       Text(
                //         "文本溢出",
                //         style: CanvasTheme.of(context).attributeTitleTextStyle,
                //       ),
                //       Expanded(
                //           child: ItemsSelectorPopUpWidget(
                //         key: Key('ItemsSelectorPopUpWidget_${tableElement.lineMode}'),
                //         items: lineModeTypes.map((e) => e.title).toList(),
                //         initializeIndex: 0,
                //         itemsSelectedChanged: (int index) {
                //           // canvasElements.forEach((canvasElement) {
                //           // });
                //           //
                //           // Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                //           //     .setDataChangedElements(canvasElements);
                //         },
                //         popHeight: 100,
                //         popWidth: 232,
                //       )),
                //     ],
                //   ),
                // ),
                Container(
                    margin: EdgeInsets.only(top: 10),
                    decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                        color: CanvasTheme.of(context).attributeGroupBackgroundColor),
                    child: Column(
                      children: [
                        MultiColourHighLightWidget(
                          multiColorIndex: tableElement.contentColorChannel,
                          valueChanged: (int redBlackValue) {
                            List<String>? paperColors = CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
                            String? colorValue = paperColors?[redBlackValue];
                            colorValue = "255." + (colorValue ?? "");
                            List<int> elementColor = colorValue.split(".").map((e) => int.parse(e)).toList();
                            canvasElements.forEach((canvasElement) {
                              TableElement element = canvasElement.data as TableElement;
                              element.contentColorChannel = redBlackValue;
                              element.contentColor = elementColor;
                            });
                            Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                .setDataChangedElements(canvasElements);
                          },
                          colorTypeStr: intlanguage('app100000430', '文本颜色'),
                          colorList: printColor.isNotEmpty
                              ? [printColor]
                              : CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor,
                        ),
                        const ItemDivider(),
                        MultiColourHighLightWidget(
                          multiColorIndex: tableElement.lineColorChannel,
                          valueChanged: (int redBlackValue) {
                            List<String>? paperColors = CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
                            String? colorValue = paperColors?[redBlackValue];
                            colorValue = "255." + (colorValue ?? "");
                            List<int> elementColor = colorValue.split(".").map((e) => int.parse(e)).toList();
                            canvasElements.forEach((canvasElement) {
                              TableElement element = canvasElement.data as TableElement;
                              element.lineColorChannel = redBlackValue;
                              element.lineColor = elementColor;
                            });
                            Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                .setDataChangedElements(canvasElements);
                          },
                          colorTypeStr: intlanguage('app100000431', '线条颜色'),
                          colorList: printColor.isNotEmpty ?? false
                              ? [printColor]
                              : CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor,
                        )
                      ],
                    )),
              ],
            ),
          ),
        ),
      );
    });
  }

  /// 文本样式
  @override
  Widget textStylePanel(List<CanvasElement> canvasElements,
      BuildContext context, VoidCallback? refresh) {
    TableElement tableElement = canvasElements.first.data as TableElement;
    List<TableCellElement> focusCells = tableElement.getFocusedCells();

    if (focusCells.length == 0) {
      /// 未选中则对全表属性进行更新
      focusCells.addAll(tableElement.cells);
      focusCells.addAll(tableElement.combineCells);
    } else {
      /// 合并的单元格, 则将合并中的第一个原始单元格也作为修改目标
      focusCells
          .where((element) => element.isCombine())
          .toList()
          .forEach((element) {
        focusCells.add(element.firstCellOfCombine!);
      });
    }

    if (focusCells.length == 0) {
      return Container();
    }

    return AnimatedSwitcher(
        duration: Duration(milliseconds: animatedDurationMilliseconds),
        child: TextAttrPanelWidget(
          canvasElements: focusCells.map((e) => e.toCanvasElement()).toList(),
          refresh: () {
            tableElement.resetCellsImageCache();
            refresh?.call();
          },
          isTableCellText: true,
          tableCellTextColorChanged: (value) {
            List<String>? paperColors =
                CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
            String? colorValue = paperColors?[value];
            colorValue = "255." + (colorValue ?? "");
            List<int> elementColor =
                colorValue.split(".").map((e) => int.parse(e)).toList();
            canvasElements.forEach((canvasElement) {
              TableElement element = canvasElement.data as TableElement;
              element.contentColorChannel = value;
              element.contentColor = elementColor;
            });
            Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                .setDataChangedElements(canvasElements);
          },
        ));
  }
}
