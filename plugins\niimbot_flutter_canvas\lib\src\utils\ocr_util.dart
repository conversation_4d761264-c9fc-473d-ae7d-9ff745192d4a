import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart' as canvas;
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/TemplateInputArea.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/line_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/ocr_scan_recognition.dart';
import 'package:niimbot_flutter_canvas/src/utils/isolate_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/utils/toast_util.dart';
import 'package:niimbot_lego/niimbot_lego.dart' as niimbot_lego;
import 'package:path_provider/path_provider.dart';

import '../model/element/text_element_style.dart';
import '../widgets/attribute_panel/comm/font_file_manager.dart';

class OcrUtils {
  static const String KEY_OCR_COMPOSE_RESULT = "key_ocr_compose_result";
  static const int TYPE_OCR_RECOGNITION = 1;
  static const int TYPE_OCR_CORRECT = 2;
  static Future<TemplateData> generateRfidReplaceTemplate(TemplateData originalTemplate, TemplateData label,
      {int textMode = 1, bool ignoreSize = false}) async {
    String labelCategaryId = CanvasPluginManager().hostMethodImpl!.isProductEnv() ? "270" : "1";
    if (label.profile.extrain.industryId == labelCategaryId &&
        originalTemplate.dataSource?.isNotEmpty == true &&
        originalTemplate.isGoodsLabelTemplate()) {
      Map<String, dynamic>? templateInfo =
          await CanvasPluginManager().hostMethodImpl!.getTemplateLayoutFronLabel(label.toJson());
      if (templateInfo != null) {
        TemplateData templateData = TemplateData.fromJson(templateInfo);
        templateData.currentPageIndex = 0;
        if (templateData.bindInfo != null) {
          templateData.bindInfo!.page = 1;
          ;
        }
        if (templateData.dataSource?.isNotEmpty ?? false) {
          templateData.currentPageIndex = originalTemplate.currentPageIndex;
          templateData.totalPage = originalTemplate.totalPage;
          templateData.dataSource = originalTemplate.dataSource;
          templateData.bindInfo = originalTemplate.bindInfo;
        }
        templateData.id = originalTemplate.id;
        return Future.value(templateData);
      } else {
        if (originalTemplate.width == label.width && originalTemplate.height == label.height && !ignoreSize) {
          // showToast(msg: "相同的标签纸尺寸");
          originalTemplate.mergeTemplate(label);
          return Future.value(label);
        }
      }
    } else {
      if (originalTemplate.width == label.width && originalTemplate.height == label.height && !ignoreSize) {
        // showToast(msg: "相同的标签纸尺寸");
        if ((label.inputAreas?.length ?? 0) > 1) {
          label.inputAreas = [TemplateInputArea(x: 0, y: 0, w: label.width, h: label.height)];
        }
        originalTemplate.mergeTemplate(label);
        return Future.value(label);
      }
    }
    if ((label.inputAreas?.length ?? 0) > 1) {
      label.inputAreas = [TemplateInputArea(x: 0, y: 0, w: label.width, h: label.height)];
    }
    return generateRFIDReplaceTemplate2(originalTemplate, label, textMode: textMode);
  }

  ///*
  /// industryNetworkData 当前模板网络请求原始的TemplateData信息
  /// labelTemplateData 识别到的标签纸TemplateData信息
  ///*/
  static Future<TemplateData> generateRFIDReplaceTemplate2(
      TemplateData industryNetworkData, TemplateData labelTemplateData,
      {int textMode = 1}) async {
    ///需默自动应用识别到的标签模板信息
    TemplateData originalLabel = TemplateData.fromJson(labelTemplateData.toJson());
    TemplateData target = industryNetworkData.mergeTemplate(labelTemplateData);
    List<TemplateInputArea> inputAreas = originalLabel.inputAreas ?? [];
    num originRotate = originalLabel.rotate;
    target
      ..rotate = originalLabel.rotate
      ..id = industryNetworkData.id
      ..canvasRotate = originalLabel.canvasRotate
      ..width = industryNetworkData.width
      ..height = industryNetworkData.height
      ..cableDirection = originalLabel.cableDirection;
    if ((industryNetworkData.canvasRotate == 90) &&
        (industryNetworkData.inputAreas?.length == 1)) {
      TemplateInputArea inputArea = industryNetworkData.inputAreas![0];
      num newx = industryNetworkData.width! - inputArea.y! - inputArea.h!;
      num newy = inputArea.x!;
      num width = inputArea.h!;
      num height = inputArea.w!;
      target.inputAreas = [TemplateInputArea(h: height, w: width, x: newx, y: newy)];
    } else {
      target.inputAreas = industryNetworkData.inputAreas;
    }
    target.rotate = 0;
    // 兼容画板的替换，处理TextElement的兼容性
    for (var element in target.elements) {
      if (element is TextElement) {
        if (element.boxStyle == TextElementBoxStyle.empty && (element.textStyle == null || element.textStyle.isEmpty)) {
          element.boxStyle = TextElementBoxStyle.autoHeight;
          element.textStyle = [TextElementTextStyle.norm];
        }
      }
    }

    TemplateData? templateData = await _generateReplaceTemplate(target, originalLabel, textMode: textMode);
    if (templateData != null) {
      int? cableDirectionTemp = originalLabel.cableDirection as int?;
      int canvasRotate = 0;
      if (templateData.rotate > 0) {
        // int times = (rotate / 90).toInt();
        if (cableDirectionTemp != null && cableDirectionTemp != -1) {
          cableDirectionTemp = (cableDirectionTemp + 1) % 4;
        }
        canvasRotate = 90;
      }
      num rotate = originRotate + (360 - templateData.rotate);
      if (rotate >= 360) {
        rotate = rotate % 360;
      }

      target
        ..elements = templateData.elements
        ..canvasElements = templateData.canvasElements
        ..elementIds = templateData.elementIds
        ..width = templateData.width
        ..height = templateData.height
        ..cableDirection = cableDirectionTemp
        ..rotate = rotate
        ..canvasRotate = canvasRotate
        ..inputAreas = inputAreas;
      target.completeMirrorElement();
      return target;
    }
    target.completeMirrorElement();
    return target;
  }

  static Future<TemplateData> generateIndustryReplaceTemplate(TemplateData labelMix, TemplateData _templateData,
      {int textMode = 1}) async {
    TemplateData label = TemplateData.fromJson(_templateData.toJson());
    label.mergeIndustryTemplate(labelMix);
    label.profile.extrain.templateClass = 1;
    if (labelMix.width == label.width && labelMix.height == label.height) {
      // showToast(msg: "相同的标签纸尺寸");
      return label;
    }
    label.canvasRotateReset();
    TemplateData? templateData = await _generateReplaceTemplate(labelMix, label, textMode: textMode);
    if (templateData != null) {
      if (templateData.rotate > 0) label.canvasRotateBy();
      label
        ..elements = templateData.elements
        ..canvasElements = templateData.canvasElements
        ..elementIds = templateData.elementIds;
    }
    label.completeMirrorElement();
    return label;
  }

  ///
  /// 调用lego库，根据原始模板的elements布局，配合替换的标签纸，生成新的模板
  ///
  static Future<TemplateData?> _generateReplaceTemplate(TemplateData originalTemplate, TemplateData label,
      {int textMode = 1}) async {
    TemplateData? templateData;
    try {
      final target = label.toJson();
      target['editableRegion'] = _initEditRegion(label);
      final source = originalTemplate.toJson();
      final sourceStr = jsonEncode(source);
      final targetStr = jsonEncode(target);
      templateData =
          await IsolateUtil().invoke<TemplateData?, List<dynamic>>(_scaleTemplateV2, [sourceStr, targetStr, textMode]);
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
      debugPrint('调用栈信息:\n $s');
    }
    return templateData;
  }

  static TemplateData? _scaleTemplateV2(List<dynamic> params) {
    String sourceStr = params[0];
    String targetStr = params[1];
    int textMode = params[2];
    String templateJsonStr = niimbot_lego.scaleTemplateV2(sourceStr, targetStr, textMode);
    final templateData = TemplateData.fromJson(jsonDecode(templateJsonStr));
    return templateData;
  }

  ///
  /// 计算目标模板的可编辑区域
  ///
  static List<Map<String, dynamic>> _initEditRegion(TemplateData label) {
    // 获取打印区域
    final rect = CanvasPluginManager().nativeMethodImpl?.getPrintArea(
        label.width?.toDouble() ?? 0,
        label.height?.toDouble() ?? 0,
        label.consumableType.toString(),
        label.paperType.toString(),
        label.rotate.toInt(),
        label.designWidth?.toInt() ?? 0,
        label.cableDirection?.toInt() ?? 0,
        label.cableLength.toDouble(),
        label.canvasRotate);

    // 初始化编辑区域
    final Map<String, dynamic> editRegion = {
      'x': 0,
      'y': 0,
      'width': label.width,
      'height': label.height,
    };

    // if (label.layoutSchema.isNotEmpty) {
    //   try {
    //     // 如果有layoutSchema，从boxes中获取第一个元素的值
    //     final layoutSchema = jsonDecode(label.layoutSchema);
    //     final boxes = layoutSchema['boxes'];
    //     if (boxes != null && boxes is List && boxes.isNotEmpty) {
    //       final firstBox = boxes[0];
    //       editRegion['x'] = _parseDoubleValue(firstBox['x']);
    //       editRegion['y'] = _parseDoubleValue(firstBox['y']);
    //       editRegion['width'] = _parseDoubleValue(firstBox['width']);
    //       editRegion['height'] = _parseDoubleValue(firstBox['height']);
    //     }
    //   } catch (e) {
    //     debugPrint('解析layoutSchema失败: $e');
    //   }
    // } else if (rect != null && rect.length == 4) {
    //   // // 如果layoutSchema为空，根据rect和label的宽高计算
    //   editRegion['x'] = rect[2].dp2mm();
    //   editRegion['y'] = rect[0].dp2mm();
    //   editRegion['width'] = ((label.designWidth ?? 0) - rect[3] - rect[2]).dp2mm();
    //   editRegion['height'] = ((label.designHeight ?? 0) - rect[1] - rect[0]).dp2mm();
    // }
    if (label.inputAreas?.isNotEmpty == true) {
      TemplateInputArea inputArea = label.inputAreas![0];
      editRegion['x'] = _parseDoubleValue(inputArea.x);
      editRegion['y'] = _parseDoubleValue(inputArea.y);
      editRegion['width'] = _parseDoubleValue(inputArea.w);
      editRegion['height'] = _parseDoubleValue(inputArea.h);
    }

    return [editRegion];
  }

  /// 安全地解析字符串为double值
  static double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;

    if (value is num) {
      return value.toDouble();
    }

    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }

    return 0.0;
  }

  /**
   * 调用lego库生成排版后的模版数据，并将排版的图片进行本地转存
   */
  static Future<TemplateData> _generateComposeTemplate(String imageBase64, String ocrRecognitionResult,
      String barcodeResult, double canvasWidth, double canvasHeight) async {
    String templateJsonStr = niimbot_lego.photoConvertRenderJson(
        imageBase64, ocrRecognitionResult, barcodeResult, canvasWidth, canvasHeight);
    TemplateData templateData = TemplateData.fromJson(jsonDecode(templateJsonStr));
    if (templateData.localBackground == null) {
      templateData.localBackground = [];
    }

    ///图片元素存到本地localUrl
    await Future.forEach(templateData.elements, (element) async {
      JsonElement jsonElement = element as JsonElement;
      if (jsonElement.width <= 0) {
        jsonElement.width = 0;
      }
      if (jsonElement.height <= 0) {
        jsonElement.height = 0;
      }
      if (element.type == ElementItemType.image) {
        ImageElement imageElement = element as ImageElement;
        // if (imageElement.imageData != null ) {
        //   String localPath = await OcrUtils.saveLegoImage(imageElement.imageData!);
        //   imageElement.localUrl = localPath;
        // }
        if (imageElement.imageUrl != null && imageElement.imageUrl.isNotEmpty) {
          imageElement.localUrl = imageElement.imageUrl;
          imageElement.imageUrl = "";
          // Uint8List bytesData = await File(imageElement.localUrl).readAsBytes();
          // String imageEncoded = base64.encode(bytesData);
          // imageElement.imageData = imageEncoded;
        }
      }
      if (element.type == ElementItemType.barcode) {
        BarCodeElement barCodeElement = element as BarCodeElement;
        barCodeElement.textHeight = barCodeElement.fontSize + 0.2;
      }
      if (element.type == ElementItemType.line) {
        LineElement lineElement = element as LineElement;
        if (lineElement.lineWidth < 0.2) {
          lineElement.lineWidth = 0.2;
        }
      }
      if (element.type == ElementItemType.text) {
        TextElement textElement = element as TextElement;
        textElement.lineBreakMode = 1;
      }
      if (element.type == ElementItemType.table) {
        TableElement tableElement = element as TableElement;
        tableElement.cells.forEach((cell) {
          cell.lineBreakMode = 1;
        });
        tableElement.combineCells.forEach((cell) {
          cell.lineBreakMode = 1;
        });
      }
    });
    return templateData;
  }

  /**
   * 组装随拍随打排版结果V2
   */
  static Future<TemplateData> constructOcrComposeResultV2(
      String ocrWordsResult, String imagePath, double canvasWidth, double canvasHeight) async {
    List<Map>? scanResult = await scanBarcodeFromImage(imagePath);
    List<CodesResult> codesList = [];
    if (scanResult != null && scanResult.isNotEmpty) {
      codesList = scanResult.map((e) {
        List<Offset> points = e["location"] as List<Offset>;
        double left = points.map((e) => e.dx).reduce((min, e) => e < min ? e : min);
        double top = points.map((e) => e.dy).reduce((min, e) => e < min ? e : min);
        double right = points.map((e) => e.dx).reduce((max, e) => e > max ? e : max);
        double bottom = points.map((e) => e.dy).reduce((max, e) => e > max ? e : max);
        CodesResult codesResult = CodesResult(
            text: e["result"],
            type: e["codeFormat"],
            location: Location(left: left, top: top, width: right - left, height: bottom - top));
        return codesResult;
      }).toList();
    }

    ///组装ocr以及scan结果 给到lego进行排版
    // String imageBase64 = await OcrUtils.getImageBase64Data(File(imagePath));
    TemplateData templateData =
        await _generateComposeTemplate(imagePath, ocrWordsResult, jsonEncode(codesList), canvasWidth, canvasHeight);
    return templateData;
  }

  /**
   * 扫描指定路径下的图片获取条码相关信息
   */
  static Future<List<Map>> scanBarcodeFromImage(String imagePath) async {
    Completer<List<Map>> completer = Completer();
    MobileScannerController scanController = MobileScannerController(torchEnabled: false);
    BarcodeCapture? barCodeResult;
    scanController.barcodes.where((event) => barCodeResult == null ? true : false).listen((scanData) {
      barCodeResult = scanData;
      List<Map<String, dynamic>> result = _processBarcodeCapture(barCodeResult!);
      completer.complete(result);
      scanController.dispose();
    });
    BarcodeCapture? isBarCode = await scanController.analyzeImage(imagePath);
    if (isBarCode == null) {
      completer.complete([]);
      scanController.dispose();
    } else {
      List<Map<String, dynamic>> result = _processBarcodeCapture(isBarCode!);
      completer.complete(result);
      scanController.dispose();
    }
    return completer.future;
  }

  static List<Map<String, dynamic>> _processBarcodeCapture(BarcodeCapture barCodeResult) {
    List<Map<String, dynamic>> result = [];
    barCodeResult.barcodes.forEach((barCode) {
      String rawValue = barCode.rawValue!;
      if (rawValue == null && barCode.rawBytes != null) {
        try {
          String utf16String = String.fromCharCodes(barCode.rawBytes!);
          // String uft8String = utf8.decode(barCode.rawBytes, allowMalformed: true);
          rawValue = utf16String;
        } catch (e) {}
      }
      String elementType = canvas.ElementItemType.text;
      int codeType = 0;
      if (barCode.format == BarcodeFormat.aztec ||
          barCode.format == BarcodeFormat.pdf417 ||
          barCode.format == BarcodeFormat.dataMatrix ||
          barCode.format == BarcodeFormat.qrCode) {
        /// 二维码
        elementType = canvas.ElementItemType.qrcode;

        if (barCode.format == BarcodeFormat.aztec) {
          codeType = canvas.QrcodeType.AZTEC;
        } else if (barCode.format == BarcodeFormat.pdf417) {
          codeType = canvas.QrcodeType.PDF417;
        } else if (barCode.format == BarcodeFormat.dataMatrix) {
          codeType = canvas.QrcodeType.DATA_MATRIX;
        } else if (barCode.format == BarcodeFormat.qrCode) {
          codeType = canvas.QrcodeType.QR_CODE;
        }
      } else if (barCode.format == BarcodeFormat.code128 ||
          barCode.format == BarcodeFormat.upcA ||
          barCode.format == BarcodeFormat.upcE ||
          barCode.format == BarcodeFormat.ean8 ||
          barCode.format == BarcodeFormat.ean13 ||
          barCode.format == BarcodeFormat.code93 ||
          barCode.format == BarcodeFormat.code39 ||
          barCode.format == BarcodeFormat.codebar ||
          barCode.format == BarcodeFormat.itf) {
        /// 一维码
        elementType = canvas.ElementItemType.barcode;

        if (barCode.format == BarcodeFormat.code128) {
          codeType = canvas.BarcodeType.CODE128;
        } else if (barCode.format == BarcodeFormat.upcA) {
          codeType = canvas.BarcodeType.UPC_A;
        } else if (barCode.format == BarcodeFormat.upcE) {
          codeType = canvas.BarcodeType.UPC_E;
        } else if (barCode.format == BarcodeFormat.ean8) {
          codeType = canvas.BarcodeType.EAN8;
        } else if (barCode.format == BarcodeFormat.ean13) {
          codeType = canvas.BarcodeType.EAN13;
        } else if (barCode.format == BarcodeFormat.code93) {
          codeType = canvas.BarcodeType.CODE93;
        } else if (barCode.format == BarcodeFormat.code39) {
          codeType = canvas.BarcodeType.CODE39;
        } else if (barCode.format == BarcodeFormat.codebar) {
          codeType = canvas.BarcodeType.CODE_BAR;
        } else if (barCode.format == BarcodeFormat.itf) {
          codeType = canvas.BarcodeType.ITF25;
        }
      }
      var map = {
        'result': rawValue,
        'elementType': elementType,
        'codeFormat': barCode.format.barcodeFormatStr,
        'location': barCode.corners
      };
      result.add(map);
    });
    return result;
  }

  /**
   * 裁剪后放缩图片
   */
  static Future<File> imageResize(File file, double canvasWidth, double canvasHeight) async {
    // final imageEncoded = await getImageBase64Data(file);
    // //调用lego进行图片放缩---返回放缩后图片的base64编码
    // String imageBase64 = niimbot_lego.imageResize(imageEncoded,canvasWidth,canvasHeight);
    // //解码生成文件
    // Uint8List bytes = base64.decode(imageBase64);
    // String path = await generateFilePath();
    // File tempFile = File(path);
    // await tempFile.writeAsBytes(bytes);
    // return tempFile;
    String originPath = file.path;
    print("originPath_resize $originPath");
    String resizePath = niimbot_lego.imageResize(originPath, canvasWidth, canvasHeight);
    print("originPath_resize_after $resizePath");
    return File(resizePath);
  }

  /**
   * 矫正图片V2
   */
  static Future<File> correctImageV2(File originFile, double canvasWidth, double canvasHeight) async {
    //矫正前，用lego压缩下图片
    // File file = await compressImage(originFile);
    File file = await legoCompressImage(originFile, canvasWidth, canvasHeight);
    print("originFile path=${originFile.path}    compressPath=${file.path}");
    Map<String, dynamic>? imageCorrectResult =
        await CanvasPluginManager().hostMethodImpl?.requestOcr(file.path, OcrUtils.TYPE_OCR_CORRECT);
    if (imageCorrectResult?.isNotEmpty == true && imageCorrectResult?.containsKey("image") == true) {
      String legoCorrectImageBase64 = imageCorrectResult!["image"];
      //解码生成文件
      Uint8List bytes = base64.decode(legoCorrectImageBase64);
      String correctImageFilePath = await generateFilePath();
      File correctImageFile = File(correctImageFilePath);
      await correctImageFile.writeAsBytes(bytes);
      return correctImageFile;
    } else {
      return file;
    }
  }

  /**
   * 获取图片base64编码
   */
  static Future<String> getImageBase64Data(File file) async {
    Uint8List bytesData = await file.readAsBytes();
    final imageEncoded = base64.encode(bytesData);
    return imageEncoded;
  }

  /**
   * 生成临时文件路径
   */
  static Future<String> generateFilePath() async {
    String filePath = (await getApplicationDocumentsDirectory()).path + "/jc_temp_file";
    if (!Directory(filePath).existsSync()) {
      Directory(filePath).createSync(recursive: true);
    }
    return "$filePath/${DateTime.now().millisecondsSinceEpoch.toString()}.jpg";
  }

  static Future<String> saveLegoImage(String imageDataBase64) async {
    Uint8List bytes = base64.decode(imageDataBase64);
    String imageFilePath = await generateFilePath();
    File imageFile = File(imageFilePath);
    await imageFile.writeAsBytes(bytes);
    return imageFilePath;
  }

  /**
   * 矫正图片
   */
  static Future<File> legoCompressImage(File file, double canvasWidth, double canvasHeight) async {
    // final imageEncoded = await getImageBase64Data(file);
    // //调用lego进行图片矫正---返回矫正后图片的base64编码
    // String legoCompressImageBase64 = niimbot_lego.imageCompress(imageEncoded,canvasWidth,canvasHeight);
    // //解码生成文件
    // Uint8List bytes = base64.decode(legoCompressImageBase64);
    // String path = await generateFilePath();
    // File tempFile = File(path);
    // await tempFile.writeAsBytes(bytes);
    // return tempFile;
    String originPath = file.path;
    print("originPath_compress $originPath");
    String compressPath = niimbot_lego.imageCompress(originPath, canvasWidth, canvasHeight);
    print("originPath_compress_after $compressPath");
    return File(compressPath);
  }
}

class OcrScene {
  static const String OCR_SCENE_PHOTO_PRINT = "photo_print";
}

extension BarcodeFormatStr on BarcodeFormat {
  String get barcodeFormatStr {
    switch (this) {
      case BarcodeFormat.upcA:
        return "UPC_A";
      case BarcodeFormat.upcE:
        return "UPC_E";
      case BarcodeFormat.code128:
        return "CODE_128";
      case BarcodeFormat.code39:
        return "CODE_39";
      case BarcodeFormat.code93:
        return "CODE_93";
      case BarcodeFormat.codebar:
        return "CODABAR";
      case BarcodeFormat.ean13:
        return "EAN_13";
      case BarcodeFormat.ean8:
        return "EAN_8";
      case BarcodeFormat.itf:
        return "ITF";
      case BarcodeFormat.qrCode:
        return "QR_CODE";
      case BarcodeFormat.dataMatrix:
        return "DATA_MATRIX";
      case BarcodeFormat.pdf417:
        return "PDF_417";
      case BarcodeFormat.aztec:
        return "AZTEC";
      default:
        return "unknown";
    }
  }
}
