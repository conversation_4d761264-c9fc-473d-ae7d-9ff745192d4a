//
//  JCPrintPortalsHelper.h
//  Runner
//
//  Created by aiden on 2025/4/22.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface JCTemplateFunctionHelper : NSObject

+ (void)toPrint:(JCTemplateData *)templateDetail;

+ (void)toEdit:(JCTemplateData *)templateDetail;

+ (void)checkFontDownload:(JCTemplateData *)templateData complate:(XYNormalBlock)complate;

+ (void)getTemplateDetailRequest:(JCTemplateData *)templateDetail complate:(XYBlock)complateCallback;

+ (void)getTemplateDetailRequestById:(NSString *)templateId complate:(XYBlock)complateCallback needLoading:(BOOL)isNeedLoading;

+ (void)showTemplteNeedUpgrade:(JCTemplateData *)templateData;

+ (void)getLayoutTemplate:(JCTemplateData *)templateDetail callback:(void(^)(JCTemplateData *))callback;
@end

NS_ASSUME_NONNULL_END
