import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:developer' as FullLog;
import 'dart:ui' as ui;

import 'package:collection/collection.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:file/file.dart' as FilePackage;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_canvas_plugins_interface/config/element_create_required_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/advance_qr_code_model.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/plugin/import_file_info.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_picker_android/image_picker_android.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';
import 'package:intl/intl.dart' hide TextDirection;
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:netal_plugin/utils/index.dart';
import 'package:niimbot_excel/models/data_source.dart' as TemplateDataSource;
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/date_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/graph_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/layout_schema_model.dart';
import 'package:niimbot_flutter_canvas/src/model/element/line_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/serial_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/model/goods_field_element_cache.dart';
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_flutter_canvas/src/model/pdf/pdf_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/stack_manager.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/floating_bar_visible_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/smart_tips_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/template_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_netwok_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/rfid_util.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/advanceQRCode/live_code_list_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/photo_crop_pdf_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/ocr/photo_crop_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/webview/custom_web_page.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/handler/layout_panel_handler.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/handler/table_panel_handler.dart';
import 'package:niimbot_flutter_canvas/src/widgets/box_frame/batch_template_preview.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_core.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_data_mix_excel_extension.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_dialog.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/ninepatch/nine_patch_image.dart';
import 'package:niimbot_flutter_canvas/src/widgets/elements/interactive_element.dart';
import 'package:niimbot_flutter_canvas/src/widgets/elements/table_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/material_panel/material_cache_manager.dart';

// import 'package:niimbot_template/niimbot_template.dart' as niimbot_template;
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:pdfx/pdfx.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tuple/tuple.dart';
import 'package:wechat_camera_picker/wechat_camera_picker.dart';

import '../assist/advanceQRCode/live_code_item.dart';

Logger _logger = Logger("MixNbCanvasData", on: kDebugMode);

/// 画板数据层
class CanvasDataMix extends StatefulWidget {
  const CanvasDataMix({
    super.key,
    this.startOffset,
    required this.defaultScale,
    required this.templateData,
    this.onElementFocusChanged,
    required this.onElementValueEditorDisplay,
    required this.onImageReplace,
    required this.onElementMaterialClicked,
    required this.labelInfo,
    required this.onLabelChange,
    required this.onAdvanceQRCodeInfoChanged,
    required this.onAttrPanelChange,
    required this.onElementFocusState,
    required this.rfidBind,
    required this.onElementMaterialBoderClicked,
    required this.onToLabelDetail,
    this.onInteractionUpdate,
  });

  final Offset? startOffset;
  final TemplateData? templateData;
  final double? defaultScale;

  /// 焦点变动 (元素选中状态变化)   changetype    1 拖拽 0 其他, -1 点击背景
  final void Function(List<CanvasElement>, int changetype)? onElementFocusChanged;

  /// 输入焦点变动
  /// [cursorElement] 当前编辑的元素，表格时 .data 是 cell element
  /// [focusedElement] 选中态元素，表格时 .data 是 table element，其余元素与 [cursorElement] 相同
  final void Function(CanvasElement cursorElement, CanvasElement focusedElement, {bool isDoubleClick})
      onElementValueEditorDisplay;

  //素材点击
  final void Function() onElementMaterialClicked;

  //边框素材点击
  final void Function() onElementMaterialBoderClicked;

  //去标签详情页
  final void Function() onToLabelDetail;

  final String labelInfo;

  final void Function() onLabelChange;
  final void Function(List<CanvasElement> canvasElements) onImageReplace;
  final void Function() onAttrPanelChange;
  final Function() onAdvanceQRCodeInfoChanged;
  final void Function(Offset offset, double scale)? onInteractionUpdate;

  // 元素状态
  final ElementFocusState Function() onElementFocusState;

  /// rfid绑定
  final void Function() rfidBind;

  @override
  CanvasDataMixState createState() => CanvasDataMixState();
}

class CanvasDataMixState extends State<CanvasDataMix> {
  ValueNotifier<bool> isTranslatingNotifier = ValueNotifier(true);

  bool _multiSelect = false;

  /// 标识是否正在进行旋转操作，用于防止旋转时隐藏toolbar
  bool _isRotating = false;

  List<CanvasElement> _focusedElements = [];
  GlobalKey<CanvasCoreState> _nbCanvasTemplateKey = GlobalKey();

  Offset _initRulerOffset = Offset(50 * 10.0 + 14, 50 * 10.0 - 74);

  TemplateData? get _templateData => widget.templateData;

  List<CanvasElement>? get _canvasElements => _templateData?.canvasElements;

  List<CanvasElement> get focusedElements => _focusedElements;

  RfidRepository rfidRepository = RfidRepository();

  /// 控制悬浮条是否显示
  set floatingBarVisible(bool visible) {
    FloatingBarVisibleNotifier().floatingBarVisible = visible;
    if (!visible) {
      FloatingBarHelper().dismissFloatingBar();
    }
  }

  bool get floatingBarVisible => FloatingBarVisibleNotifier().floatingBarVisible;

  set multiSelect(bool value) {
    if (_multiSelect != value) {
      _multiSelect = value;
      if (value == false) {
        _focusedElements.clear();
        SmartTipsNotifier().smartTipsMode = SmartTipsMode.none;
      } else {
        SmartTipsNotifier().smartTipsMode = SmartTipsMode.smartMultiSelect;
      }
      // LoadingMix.showToast(value ? "进入多选模式" : "进入单选模式");
      // LoadingMix.showToast(value ? "app01405" : "app01406");
    }
  }

  clearMultiSelectAndFocused({bool needFresh = false}) {
    if (needFresh) {
      setState(() {
        _logger.log("==============调用clearMultiSelectAndFocused---刷新");
        _multiSelect = false;
        _focusedElements.clear();
        SmartTipsNotifier().smartTipsMode = SmartTipsMode.none;
      });
    } else {
      _logger.log("==============调用clearMultiSelectAndFocused");
      _multiSelect = false;
      _focusedElements.clear();
      SmartTipsNotifier().smartTipsMode = SmartTipsMode.none;
    }
  }

  ///获取画板结果数据(预览数据/新添加元素)
  String? getCanvasJsonDATA() {
    if (_templateData != null) {
      return jsonEncode(_templateData?.toJson(escapeValue: false));
    } else {
      _logger.log("未获取到模板数据");
      return null;
    }
  }

  @override
  void initState() {
    super.initState();

    /// 撤销、恢复
    StackManager().snapCanvasElements(_canvasElements);
  }

  @override
  void dispose() {
    // canvasUpdateBloc.dispose();
    PdfBindInfoManager.instance.clearPageInfo();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ElementsDataChangedNotifier>(
        builder: (BuildContext context, ElementsDataChangedNotifier value, Widget? child) {
      /// 将正在编辑文本的 tableCell 对象转换为 table 对象
      final dataChangedCanvasElements = value.dataChangedCanvasElements?.map((e) {
        if (e.data is TableCellElement) {
          final tableCanvasElement = _canvasElements
              ?.singleWhereOrNull((element) => element.elementId == (e.data as TableCellElement).tableElement?.id);
          if (tableCanvasElement != null) {
            return tableCanvasElement;
          }
        }
        return e;
      }).toList();

      /// 处理元素属性更新
      handleElementsUpdated(dataChangedCanvasElements);

      if (dataChangedCanvasElements != null && dataChangedCanvasElements.length > 0) {
        /// 黑色工具条已显示的状态下，切换文本方向需要刷新工具条
        /// 检查是否文本元素方向改变
        // if (!_multiSelect &&
        //     dataChangedCanvasElements?.length == 1 &&
        //     FloatingBarVisibleNotifier().floatingBarVisible &&
        //     dataChangedCanvasElements.first.data is TextElement) {
        //   TextElement changedElement = (dataChangedCanvasElements.first.data as TextElement);
        //   JsonElement stackElement = StackManager().getElementBefore(changedElement.id);
        //   if (stackElement != null && stackElement.type == ElementItemType.text) {
        //     TextElement beforeElement = stackElement as TextElement;
        //     FloatingBarVisibleNotifier().forceDismiss();
        //     // if (beforeElement.typesettingMode != changedElement.typesettingMode ||
        //     //     beforeElement.lineMode != changedElement.lineMode ||
        //     //     beforeElement.rotate != changedElement.rotate) {
        //     //   Future.delayed(Duration(milliseconds: 100), () {
        //     //     FloatingBarVisibleNotifier().forceUpdate();
        //     //   });
        //     // }
        //   }
        // } else
        if (!_multiSelect &&
            dataChangedCanvasElements.length == 1 &&
            FloatingBarVisibleNotifier().floatingBarVisible &&
            dataChangedCanvasElements.first.data is DateElement &&
            (dataChangedCanvasElements.first.data as DateElement).isRefreshDateEnable()) {
        } else {
          // 旋转动作时不隐藏toolbar
          if (!_multiSelect && !_isRotating) FloatingBarVisibleNotifier().forceDismiss();
        }
        List<CanvasElement> needAddAssociateElements = [];
        dataChangedCanvasElements.forEach(
          (element) {
            CanvasElement? needAddElement = getAssociateElement(element);
            if (needAddElement != null) needAddAssociateElements.add(needAddElement);
          },
        );
        if (needAddAssociateElements.isNotEmpty) dataChangedCanvasElements.addAll(needAddAssociateElements);

        /// 撤销、恢复
        StackManager().updateElements(dataChangedCanvasElements, context);
        StackManager().snapCanvasElements(_canvasElements);
      }

      /// 重置元素数据变动的数组
      value.resetDataChangedElement();

      return CanvasCore(
        key: _nbCanvasTemplateKey,
        templateWidth: _templateData?.designWidth ?? 0,
        templateHeight: _templateData?.designHeight ?? 0,
        backgroundImage: _templateData?.backgroundImage ?? "",
        multipleBackIndex:
            (_templateData?.multipleBackIndex.toInt() ?? 0) < 0 ? 0 : _templateData!.multipleBackIndex.toInt(),
        localBackgrounds: _templateData?.localBackground ?? [],
        canvasRotate: _templateData?.canvasRotate.toDouble() ?? 0,
        readOnly: false,
        canvasElements: _canvasElements ?? [],
        focusedElements: _focusedElements,
        // multiSelect: _multiSelect,
        isMultiSelect: _isMultiSelectStatus,
        onTranslated: _handleBoxTranslated,
        onTranslateEnded: _handleBoxTranslateEnded,
        onScaleEndWithFontSize: (value) => _handleScaleEndWithFontSize(value.$1, value.$2, value.$3),
        onBoxEdit: _handleBoxEdit,
        onBoxReplace: _handleBoxReplace,
        onBoxDeleted: _handleBoxDeleted,
        onBoxTap: _handleSelectionChanged,
        onBoxCopied: _handleBoxCopied,
        onBoxLock: _handleBoxLock,
        onBoxRotate: _handleBoxRotate,
        onBoxAlign: _handleBoxAlign,
        onCellAction: _handleCellAction,
        onMergeCells: _handleMergeCells,
        onSplitCell: _handleSplitCells,
        onItemDoubleTap: _handleBoxDoubleTap,
        onMultiSelectChanged: _handleMultiSelectChanged,
        onSelectAll: _handleSelectAll,
        onSelectAllText: _handleSelectAllText,
        onSelectAllLine: _handleSelectAllLine,
        idBuilder: (element) => element.data.id,
        isItemFocused: isItemFocused,
        isLock: isLock,
        isMirror: isMirror,
        isAssociateElement: isAssociateElement,
        isResizeEnable: isResizeEnable,
        multiElementsRect: multiElementsRect,
        itemControlsBuilder: itemControlsBuilder,
        itemBuilder: itemBuilder,
        startOffset: widget.startOffset,
        defaultScale: widget.defaultScale,
        onInteractionUpdate: _onInteractionUpdate,
        fetchBatchInfo: () {
          if (!(_templateData?.existBatchBindingData() ?? false) &&
              PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(_templateData!) < 1) return null;
          int totle = 0;
          int pageIndex = 0;
          if ((_templateData!.existBatchBindingData())) {
            totle = _templateData?.pagesOfBatchData() ?? 0;
            pageIndex = _templateData?.currentPageIndex ?? 0;
          } else if (PdfBindInfoManager.instance.templateIsBindPdf(_templateData!)) {
            totle = PdfBindInfoManager.instance.getPDFEnablePrintMaxNumber(_templateData!);
            pageIndex = _templateData?.currentPageIndex ?? 0;
          }
          return [pageIndex, totle];
        },
        onBackgroundImageChange: _onTemplateBackgroundChangeTap,
        onBatchPreviewTap: _showBatchCopies,
        onSmartTipsTap: _onSmartTipsTap,
        labelInfo: widget.labelInfo,
        onLabelChange: widget.onLabelChange,
        onElementFocusState: widget.onElementFocusState,
        isShowHandleBtn: _isShowRightCorner(),
        rfidBind: widget.rfidBind,
        checkSelectAll: _checkSelectAll,
        checkSelectAllText: _checkSelectAllText,
        checkSelectAllLine: _checkSelectAllLine,
        materialModelSn: _templateData?.profile.extrain.materialModelSn ?? "",
        onClickLabelDetail: _labelClick,
      );
    });
  }

  bool _isMultiSelectStatus() {
    return _multiSelect;
  }

  _labelClick() {
    CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
      "track": "click",
      "posCode": "108_074_236",
      "ext": {"source": 1}
    });
    final labelInterface = CanvasPluginManager().labelImpl;
    labelInterface?.jumpLabelDetailPage(_templateData?.toJson(), widget.labelInfo, context);
    widget.onToLabelDetail?.call();
  }

  resetElementMirrorLocation(JsonElement item) {
    String value = "";
    double templateWidth = _templateData?.width?.toDouble() ?? 0;
    double templateHeight = _templateData?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);
    if (item.isOpenMirror == 1) {
      JsonElement jsonElement = item;
      String mirrorId = jsonElement.generateFixedMirrorId();
      for (var mirrorElement in (_templateData?.canvasElements ?? [])) {
        if (mirrorId == mirrorElement.data.id) {
          JsonElement elementModel = mirrorElement.data;
          Offset distanceToCenter = templateCenter -
              Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

          Offset mirrorPosition = templateCenter + distanceToCenter;
          if (jsonElement.mirrorType == 1) {
            /// 1: 画板中心 y 轴镜像
            elementModel.y = mirrorPosition.dy;
          } else if (jsonElement.mirrorType == 2) {
            /// 2: 画板中心 x 轴镜像
            elementModel.x = mirrorPosition.dx;
          } else {
            /// 0: 画板中心点镜像
            elementModel.x = mirrorPosition.dx;
            elementModel.y = mirrorPosition.dy;
          }
          elementModel.width = jsonElement.width;
          elementModel.height = jsonElement.height;
          break;
        }
      }
    }
  }

  /// 新增isCleanImageWidget参数，代表是否只返回干净的Image组件，防止wrap-Stack导致的包裹尺寸问题
  Widget itemBuilder(CanvasElement canvasElement, {bool isCleanImageWidget = false}) {
    print("=========itemBuilder, element: ${canvasElement.data.id}");
    JsonElement item = canvasElement.data;

    if (item.width <= 0 || item.height <= 0) {
      return Container();
    }
    bool itemIsSelected = _focusedElements.singleWhereOrNull((element) => element.data.id == item.id) != null;
    BoxConstraints boxConstraints = item.getBoxConstrains(context);
    if (!(item is TextElement && (item).isSupportBoxStyle())) {
      /// 文本框样式不能调整宽高，否则移动偏移错误
      if (item.width.mm2dp() < boxConstraints.minWidth) {
        item.width = boxConstraints.minWidth.dp2mm();
      } else if (item.width.mm2dp() > boxConstraints.maxWidth) {
        item.width = boxConstraints.maxWidth.dp2mm();
      }

      if (item.height.mm2dp() < boxConstraints.minHeight) {
        item.height = boxConstraints.minHeight.dp2mm();
      } else if (item.height.mm2dp() > boxConstraints.maxHeight) {
        item.height = boxConstraints.maxHeight.dp2mm();
      }
    }

    /// 表格焦点状态下自绘制
    if (item.type == ElementItemType.table) {
      /// 将 table 的宽高值按行高、列宽、线宽校正
      TableElement tableElement = item as TableElement;

      if (itemIsSelected) {
        return TableWidget(
          templateData: _templateData,
          tableElement: item,
          isFocus: itemIsSelected,
          usedFonts: _templateData?.usedFonts,
          onCellFocusChanged: () {
            if (_multiSelect) {
              if (_focusedElements.contains(canvasElement)) {
                _focusedElements.remove(canvasElement);
                widget.onElementFocusChanged?.call(_focusedElements, 0);
                _logger.log("多选元素，表格更新悬浮条数量：${_focusedElements.length}");
                FloatingBarHelper().selectSizeChanged(_focusedElements, true);
                if (_focusedElements.isEmpty) multiSelect = false;
              }
              return;
            }
            // if (isLock(canvasElement)) {
            //   FloatingBarVisibleNotifier().toggle();
            // } else {
            ///表格选中切换到表格元素选中时，工具条不能消失且需要刷新
            FloatingBarVisibleNotifier().forceUpdate();
            // }
            widget.onElementFocusChanged?.call([canvasElement], 0);
          },
          onCellDoubleTap: (TableCellElement element) {
            if (_multiSelect) {
              return;
            }

            /// 锁定状态下表格双击不响应编辑
            // if (canvasElement.data.isLock != 1) {
            widget.onElementValueEditorDisplay.call(element.toCanvasElement(), canvasElement, isDoubleClick: true);
            // }
          },
        );
      } else {
        /// 清除选中状态
        tableElement.cells.forEach((element) {
          element.focused = false;
        });
        tableElement.combineCells.forEach((element) {
          element.focused = false;
        });

        ///表格在非焦点状态下也采用自绘制方式，但是此时不响应事件，防止图片过大卡顿
        return TableWidget(
          templateData: _templateData,
          tableElement: item,
          isFocus: false,
          usedFonts: _templateData?.usedFonts,
        );
      }
    }
    if (item.type == ElementItemType.text) {
      _templateData?.updateUsedFont(item as TextElement);
    } else if (item.type == ElementItemType.table) {
      (item as TableElement).cells.forEach((element) {
        _templateData?.updateUsedFont(element);
      });
    } else if (item.type == ElementItemType.image && ((item as ImageElement).isNinePatch ?? false)) {
      // 加载.9图
      // NinePatchImage2的key主要由三部分构成，元素ID、边框ID、mmtopt转换，三者其一发生变化需要重新生成新的element
      return SizedBox(
          width: item.width.mm2dp().toDouble(),
          height: item.height.mm2dp().toDouble(),
          child: NinePatchImage2(
            key: ValueKey(item.id + (item.materialId ?? ' ') + DisplayUtil.dpRatio.toString()),
            elementId: item.id,
            materialId: item.materialId ?? ' ',
            imagePath: item.ninePatchLocalUrl ?? item.localUrl,
            screenshotsPath: item.localUrl,
            color: Color.fromARGB(item.elementColor?[0] ?? 255, item.elementColor?[1] ?? 0, item.elementColor?[2] ?? 0,
                item.elementColor?[3] ?? 0),
            ninePatchInitialCornerScale: item.ninePatchInitialCornerScale,
            stretchRectClosure: (Rect? value) {
              // 回写给ImageElement控制最小的展示Size
              var minWidth = ((value?.width ?? 1) / DisplayUtil.devicePixelRatio).toDouble();
              var minHeight = ((value?.height ?? 1) / DisplayUtil.devicePixelRatio).toDouble();
              setState(() {
                item.ninePatchMinSize = Rect.fromLTWH(0, 0, minWidth, minHeight);
              });
            },
            ninePatchInitialCornerScaleClosure: (Tuple2<double, double>? value) {
              item.ninePatchInitialCornerScale = value;
            },
          ));
    }

    // 在缩放拉伸状态(包含拉伸中以及拉伸结束后的一次渲染)下无论是否在编辑状态均需要重置isEditing
    bool? isEditing;
    if (item.isTextElement()) {
      if (canvasElement.currentDragType == DragType.scale || canvasElement.isShowBoxFitContain) {
        isEditing = (item as TextElement).isEditing;
        item.isEditing = false;
      }
    }

    // 获取图像库返回的图像数据
    NetalImageResult imageData = getElementImage(canvasElement);

    if (isEditing != null) {
      // 回写isEditing
      if (item.isTextElement()) {
        (item as TextElement).isEditing = isEditing;
      }
    }

    if (imageData.pixels.isNotEmpty) {
      /// 按图像库返回重新校正组件的宽高
      //文字方向做特殊处理
      if (item.isTextElement() && (item as TextElement).specialRelocation()) {
        double fixedHeight = imageData.height.toDouble().px2mm().toDouble();
        double fixedWidth = imageData.width.toDouble().px2mm().toDouble();
        //竖排文字，保持中心点不变，宽高调换后更新
        TextElement textElement = item;
        if (textElement.lastHeight != fixedHeight || textElement.lastWidth != fixedWidth) {
          Offset center = Offset(item.x.toDouble(), item.y.toDouble()) +
              Offset((textElement.lastWidth?.toDouble() ?? 0) / 2, (textElement.lastHeight?.toDouble() ?? 0) / 2);
          item.y = center.dy - fixedHeight / 2;
          item.x = center.dx - fixedWidth / 2;
          textElement.lastHeight = fixedHeight;
          textElement.lastWidth = fixedWidth;
        }

        item.height = fixedHeight;
        item.width = fixedWidth;
        if ((item).isSupportBoxStyle()) {
          /// 在模式三下是否需要更新字号, 在缩放拉伸状态下 或者 上次切换的文本方向是弧形 需要更新字号，否则不更新字号
          bool needUpdateFontSize = false;
          if ((canvasElement.currentDragType == DragType.scale || canvasElement.isShowBoxFitContain) ||
              (item.lastTypeSettingMode != null && item.lastTypeSettingMode == TypeSettingMode.TEXT_CURVE)) {
            needUpdateFontSize = true;
            item.lastTypeSettingMode = null;
          }

          /// 使用图像库返回字号
          (item)
              .updateFontSize(imageData.fontSize, imageData.fontSizeThreshold, needUpdateFontSize: needUpdateFontSize);
          (item).updateBoxStyleWithInt(imageData.boxStyle, canvasElement);
        }
      } else if (item.isTextElement()) {
        double fixedHeight = imageData.height.toDouble().px2mm().toDouble();
        double fixedWidth = imageData.width.toDouble().px2mm().toDouble();
        if ((item as TextElement).isSupportBoxStyle()) {
          print(
              "=========itemBuilder, textElement.size=${item.width}x${item.height} \n fixedWidth=${fixedWidth} fixedHeight=${fixedHeight}\n original-aspect-ratio=${item.width / item.height} fixed-aspect-ratio=${fixedWidth / fixedHeight}\n original-fontSize=${item.fontSize} fontSize=${imageData.fontSize}");
          // 检查是否是对角线拖拽
          bool isScaleDragging = canvasElement.isDragging && canvasElement.currentDragType == DragType.scale;

          (item).handleTextElementPostion(fixedWidth, fixedHeight, isScaleDragging, canvasElement.isShowBoxFitContain);

          /// 在模式三下是否需要更新字号, 在缩放拉伸状态下 或者 上次切换的文本方向是弧形 需要更新字号，否则不更新字号
          bool needUpdateFontSize = false;
          if ((canvasElement.currentDragType == DragType.scale || canvasElement.isShowBoxFitContain) ||
              (item.lastTypeSettingMode != null && item.lastTypeSettingMode == TypeSettingMode.TEXT_CURVE)) {
            needUpdateFontSize = true;
            item.lastTypeSettingMode = null;
          }
          (item)
              .updateFontSize(imageData.fontSize, imageData.fontSizeThreshold, needUpdateFontSize: needUpdateFontSize);
          (item).updateBoxStyleWithInt(imageData.boxStyle, canvasElement);
        } else {
          if (item.height != fixedHeight || item.width != fixedWidth) {
            // 文本旋转状态下宽高变化后校正 x,y
            double differenceHeight = fixedHeight - item.height;
            double differenceWidth = fixedWidth - item.width;

            Offset center = Offset(item.x.toDouble(), item.y.toDouble()) +
                Offset(item.width.toDouble() / 2, item.height.toDouble() / 2);

            if (item.rotate == 90) {
              Offset offsetCenter = center + Offset(-differenceHeight, differenceWidth) / 2;
              item.y = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dy;
              item.x = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dx;
            } else if (item.rotate == 180) {
              item.y -= (fixedHeight - item.height);
              item.x -= (fixedWidth - item.width);
            } else if (item.rotate == 270) {
              Offset offsetCenter = center + Offset(differenceHeight, -differenceWidth) / 2;
              item.y = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dy;
              item.x = (offsetCenter - Offset(fixedWidth, fixedHeight) / 2).dx;
            }
          }
          _logger.log(
              "text position x=${item.x} y=${item.y} w=${item.width} h=${item.height} imagew=$fixedWidth imageh=$fixedHeight");
          item.height = fixedHeight;
          item.width = fixedWidth;
        }
      }

      if (item.isTextElement()) {
        //先变换宽高 然后在切换文本方向的话，修复中心点不对的问题
        TextElement textElement = item as TextElement;
        textElement.lastWidth = item.width.toDouble();
        textElement.lastHeight = item.height.toDouble();
      }
      resetElementMirrorLocation(item);

      ///barcode qrcode 合规性检测
      if (item.type == ElementItemType.barcode || item.type == ElementItemType.qrcode) {
        String contentValue = "";
        if (item.isBindingElement()) {
          contentValue = _templateData?.escapeBindingValue(item, _templateData?.currentPageIndex,
                  addContentTitle: false, parseContentAffix: true) ??
              '';
        } else {
          contentValue = item.value ?? '';
        }
        if (item.type == ElementItemType.barcode) {
          BarCodeElement barCodeElement = item as BarCodeElement;
          if (contentValue.isNotEmpty) {
            item.checkResult =
                NetalUtils.barCodeContentCheck(barCodeElement.toNetal(contentValue: contentValue)) == false ? 1 : 0;
          } else {
            //条码内容为空，图像库会提供合规的默认值
            item.checkResult = 0;
          }
          _logger.log(
              "check result ${item.checkResult} ${item.id} isClean=${isCleanImageWidget} code type==${item.codeType} contentValue=$contentValue");
        } else if (item.type == ElementItemType.qrcode) {
          item.checkResult =
              NetalUtils.qrCodeContentCheck((item as QrCodeElement).toNetal(contentValue: contentValue)) == false
                  ? 1
                  : 0;
          _logger.log("check result ${item.checkResult} code type==${(item).codeType} contentValue=$contentValue");
        }
        // if (item.isOpenMirror == 0 && item.mirrorId.isNotEmpty) {
        //   //获取主元素
        //   CanvasElement? element = _canvasElements?.singleWhereOrNull((element) => element.elementId == item.mirrorId);
        //   if (element != null) {
        //     item.checkResult = (element.data).checkResult;
        //   }
        // }
      }
      // if (item.type == ElementItemType.qrcode &&  (item as QrCodeElement).codeType == QrcodeType.PDF417) {
      if (item.type == ElementItemType.qrcode) {
        item.height = imageData.height.toDouble().px2mm();
      }
      double width;
      double height;
      double scale;
      if (item is ImageElement) {
        double imagePxRatio = ImageElementHelper.getImagePxRatio(item);
        width = imageData.width * DisplayUtil.dpRatio / imagePxRatio;
        height = imageData.height * DisplayUtil.dpRatio / imagePxRatio;
        scale = imagePxRatio / DisplayUtil.dpRatio;
      } else {
        width = imageData.width.px2dp();
        height = imageData.height.px2dp();
        scale = DisplayUtil.pxRatio / DisplayUtil.dpRatio;
      }

      // 检查是否是对角线拖拽
      bool isScaleDragging = canvasElement.currentDragType == DragType.scale;
      // 文本元素对角线拖拽时，使用文本元素的boxFit，否则使用none
      BoxFit boxFit = item.isTextElement()
          ? isScaleDragging
              ? item.getTextElementBoxFit()
              : canvasElement.isShowBoxFitContain
                  ? BoxFit.contain
                  : BoxFit.none
          : isCleanImageWidget
              ? BoxFit.contain
              : BoxFit.fill;

      // 对角线拖拽结束后，重置标记
      canvasElement.isShowBoxFitContain = false;

      // 是否只返回干净的Image组件
      if (isCleanImageWidget) {
        var alignment = Alignment.center;
        alignment = adjustTextElementAlign(item, alignment);
        return Image.memory(
          imageData.pixels,
          /** 避免闪动 */
          gaplessPlayback: true,
          alignment: alignment,
          fit: boxFit,
          width: width,
          height: height,
          scale: scale,
        );
      } else {
        var alignment = Alignment.topLeft;
        alignment = adjustTextElementAlign(item, alignment);
        return Stack(
          clipBehavior: Clip.none,
          children: [
            Positioned(
                left: 0.0,
                top: 0.0,
                child: Image.memory(
                  imageData.pixels,
                  /** 避免闪动 */
                  gaplessPlayback: true,
                  alignment: alignment,
                  fit: boxFit,
                  width: width,
                  height: height,
                  scale: scale,
                )),
            (((item.type == ElementItemType.barcode || item.type == ElementItemType.qrcode) && item.checkResult != 0) ||
                    (item.isAdvanceQRCode() &&
                        AdvanceQRCodeManager().getAdvanceQRCodeInfo(item as QrCodeElement) != null &&
                        AdvanceQRCodeManager().getAdvanceQRCodeInfo(item)!.deleted!))
                ? getBarcodeWarningWidget(imageData.width, imageData.height, isAdvanceQRCode: item.isAdvanceQRCode())
                : Container(),
          ],
        );
      }
    }
    return Container();
  }

  /// 垂直模式必须调整填充布局模式，否则会出现闪动问题
  Alignment adjustTextElementAlign(JsonElement item, Alignment alignment) {
    if (item is TextElement && item.isSupportBoxStyle()) {
      TextElement textElement = item;
      if (textElement.getTextMode() == TextMode.Vertical || textElement.getTextMode() == TextMode.Horizontal_90) {
        alignment = Alignment.topRight;
      } else if (textElement.getTextMode() == TextMode.Horizontal) {
        if (textElement.textAlignHorizontal == 0) {
          alignment = Alignment.topLeft;
        } else if (textElement.textAlignHorizontal == 1) {
          alignment = Alignment.topCenter;
        } else if (textElement.textAlignHorizontal == 2) {
          alignment = Alignment.topRight;
        }
      }
    }
    return alignment;
  }

  Widget getBarcodeWarningWidget(int imageWidth, int imageHeight, {bool isAdvanceQRCode = false}) {
    double dpW = imageWidth.px2dp();
    double dpH = imageHeight.px2dp();
    double warningTextW = 115;
    double warningTextH = 26;
    if (dpW > warningTextW && !isAdvanceQRCode) {
      double offsetX = (dpW - warningTextW) / 2;
      double offsetY = (dpH - warningTextH) / 2;
      return Positioned(
          left: offsetX,
          top: offsetY,
          child: Container(
            width: warningTextW,
            height: warningTextH,
            decoration: BoxDecoration(color: ThemeColor.COLOR_FB4B42, borderRadius: BorderRadius.circular(40)),
            child: Center(
              child: Text(intlanguage("app01066", "不符合编码规范"),
                  style: TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.w400)),
            ),
          ));
    } else {
      double warningImageW = 26;
      double warningImageH = 26;
      double offsetX = (dpW - warningImageW) / 2;
      double offsetY = (dpH - warningImageH) / 2;
      return Positioned(
          left: offsetX,
          top: offsetY,
          child: Center(
            child: Image.asset(
              "assets/common/icon_warning.png",
              package: "niimbot_flutter_canvas",
              fit: BoxFit.cover,
              width: warningImageW,
              height: warningImageW,
            ),
          ));
    }
  }

  updateGoodsField() {
    _templateData?.getAllBindingExcelCanvasElements(true)?.forEach((element) {
      element.resetImageCache();
    });
    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
  }

  ///定时刷新实时时间
  updateAllRefreshDates() {
    ///用户没有开通vip不用刷新
    // if (CanvasUserCenter().vipType != VIPType.valid) {
    //   return;
    // }
    List<CanvasElement> dateList = [];
    _templateData?.canvasElements.forEach((element) {
      if (element.data is DateElement) {
        DateElement dateElement = element.data as DateElement;
        if (dateElement.isRefreshDateEnable()) {
          dateElement.time = DateTime.now().millisecondsSinceEpoch;

          ///为了节省性能，只刷新带有时间格式的
          if ((dateElement.timeFormat?.isNotEmpty ?? false)) {
            dateList.add(element);
          }
        }
      }
    });
    if (dateList.isNotEmpty) {
      // Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(dateList);
      dateList.forEach((element) {
        element.resetImageCache();
      });
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
    }
  }

  getElementImage(CanvasElement canvasElement) {
    int start1 = DateTime.now().millisecondsSinceEpoch;
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    Color? contentColor = Colors.black;
    if (printColor.isNotEmpty && !RfidUtil.is16GrayColorStr(printColor)) {
      contentColor = Color.fromARGB(255, int.parse(printColor.split(".")[0]), int.parse(printColor.split(".")[1]),
          int.parse(printColor.split(".")[2]));
    } else {
      contentColor = null;
    }
    JsonElement item = canvasElement.data;
    if (item.width <= 0 || item.height <= 0) {
      return null;
    }
    if (item.type == "table") {
      TableElement tableElement = item as TableElement;
      int lineColorChannel = tableElement.lineColorChannel ?? 0;
      if (lineColorChannel >= (_templateData?.paperColor ?? []).length) lineColorChannel = 0;
      tableElement.lineColor ??=
          ("255." + (_templateData?.paperColor[lineColorChannel] ?? "")).split(".").map((e) => int.parse(e)).toList();
      int contentColorChannel = tableElement.contentColorChannel ?? 0;
      if (contentColorChannel >= (_templateData?.paperColor ?? []).length) contentColorChannel = 0;
      tableElement.contentColor ??= ("255." + (_templateData?.paperColor[contentColorChannel] ?? ""))
          .split(".")
          .map((e) => int.parse(e))
          .toList();
      if (RfidUtil.supportSixteenGrayPrint) {
        tableElement.lineColor = [255, 0, 0, 0];
      }
    } else if (item.type == "image") {
      ImageElement imageElement = item as ImageElement;
      List<String> imagePaths = PdfBindInfoManager.instance.getPDFImagePathsWithElementId(imageElement.id);
      if (imagePaths.isNotEmpty) {
        //image数组 匹配 如果超出 则图片不显示
        imageElement.localUrl =
            _templateData!.currentPageIndex > imagePaths.length - 1 ? "" : imagePaths[_templateData!.currentPageIndex];
      }
      if (!RfidUtil.supportSixteenGrayPrint && imageElement.is16grayStyle()) {
        imageElement.resetStyle();
      }
    } else {
      int paperColorIndex = item.paperColorIndex ?? 0;
      if (paperColorIndex >= (_templateData?.paperColor ?? []).length) paperColorIndex = 0;
      item.elementColor ??=
          ("255." + (_templateData?.paperColor[paperColorIndex] ?? "")).split(".").map((e) => int.parse(e)).toList();
    }
    _logger.log("元素颜色:${canvasElement.data.elementColor}");
    if (item is BarCodeElement && item.codeType == BarcodeType.CODE_BAR) {
      if ((item.value?.length ?? 0) > 57) {
        item.value = item.value?.substring(0, 57);
      }
    }

    NetalImageResult imageData;
    if (canvasElement.imageCache == null) {
      /// 图像库预览时旋转由于应用层处理，将 rotate 处理成 0 给图像库
      int originRotate = item.rotate;
      item.rotate = 0;
      // String printColor = CanvasObjectSharedWidget.printColorOf(context);
      // String? ribbonColors = CanvasPluginManager().canvasConfigImpl?.getRfidColor();
      // bool isMultiColorRibbon = printColor.isEmpty == true && _isMultiColorRibbonFromData(ribbonColors);
      // int originPaperColorIndex = item.paperColorIndex;
      // int originColorChannel = item.colorChannel;
      // int originColorReverse = item.colorReverse;
      // List<int>? originElementColor = item.elementColor;
      // // 如果paperColor只有1个且colorReverse不为0，则colorReverse为1
      // // 如果printColor为空，且ribbonColors为多色，则paperColorIndex为0，colorChannel为1，colorReverse为1,
      // // elementColor为默认[255, 0, 0, 0], 具体的颜色会被contentColor替代
      // if ((printColor.isNotEmpty && item.colorReverse > 1) || (isMultiColorRibbon && item.colorReverse > 1)) {
      //   item.elementColor = [255, 0, 0, 0];
      //   item.paperColorIndex = 0;
      //   item.colorChannel = 1;
      //   item.colorReverse = 1;
      // }
      // // 如果printColor不为空，则paperColorIndex不为0，则paperColorIndex为0, 且elementColor为默认[255, 0, 0, 0]
      // if (printColor.isNotEmpty && item.paperColorIndex != 0) {
      //   item.elementColor = [255, 0, 0, 0];
      //   item.paperColorIndex = 0;
      // }
      _templateData?.usedFonts = CanvasObjectSharedWidget.canvasDataOf(context)?.usedFonts;
      var canvasData = TemplateData(
          width: _templateData?.width ?? 0,
          height: _templateData?.height ?? 0,
          canvasElements: [canvasElement],
          usedFonts: _templateData?.usedFonts ?? {});
      var elementCanvasDataJsonStr = jsonEncode(canvasData.toJson(
          escapeValue: true,
          escapeBindingMethod: (JsonElement jsonElement) {
            return _templateData?.escapeBindingValue(jsonElement, _templateData?.currentPageIndex,
                parseContentAffix: true);
          }));
      // item.paperColorIndex = originPaperColorIndex;
      // item.colorChannel = originColorChannel;
      // item.colorReverse = originColorReverse;
      // item.elementColor = originElementColor;
      item.rotate = originRotate;

      int start = DateTime.now().millisecondsSinceEpoch;
      _logger.log("==========getElementImage，运算时长： ${start - start1}ms");
      double pxRatio;
      if (item is ImageElement) {
        pxRatio = ImageElementHelper.getImagePxRatio(item);
      } else {
        pxRatio = DisplayUtil.pxRatio;
      }
      // FullLog.log("===============generateImageFromElementJson905: $elementCanvasDataJsonStr");
      imageData = NiimbotNetal.generateImageFromElementJson(
          jsonString: elementCanvasDataJsonStr, ratio: pxRatio, color: contentColor);
      canvasElement.imageCache = imageData;
      _logger.log("==========图像库，渲染时长： ${DateTime.now().millisecondsSinceEpoch - start}ms");
    } else {
      imageData = canvasElement.imageCache!;
    } //
    return imageData;
  }

  bool _isMultiColorRibbonFromData(String? ribbonColors) {
    if (ribbonColors?.isNotEmpty != true) return false;

    if (ribbonColors!.contains(',')) {
      List<String> colors = ribbonColors.split(',').map((c) => c.trim()).toList();
      return colors.length > 1 && colors.every((c) => c.isNotEmpty);
    }

    return false;
  }

  Widget itemControlsBuilder(item) {
    return ValueListenableBuilder(
        valueListenable: isTranslatingNotifier,
        child: Container(
          width: 300,
          height: 200,
          color: Colors.blue,
        ),
        builder: (_, bool isTranslating, cachedChild) {
          if (isTranslating) return Container();
          return cachedChild!;
        });
  }

  Rect? multiElementsRect() {
    if (_focusedElements.length > 1) {
      List<CanvasElement> multiElements = []..addAll(_focusedElements);

      /// 查找选中元素的镜像元素
      List<String> ids = _focusedElements.map((e) => e.data.id).toList();
      multiElements.addAll((_canvasElements ?? []).where((element) => ids.contains(element.data.mirrorId)));

      Rect? rect;
      multiElements.forEach((element) {
        Rect elementRect = element.rect;
        if (rect == null) {
          rect = elementRect;
        } else {
          rect = rect?.expandToInclude(elementRect);
        }
      });
      return rect;
    }
    return null;
  }

  bool isMirror(element) {
    return element.data.isOpenMirror == 0 && (element.data.mirrorId ?? '').length > 0;
  }

  bool isAssociateElement(element) {
    JsonElement currentElement = (element as CanvasElement).data;
    bool isAssociate = (currentElement is DateElement &&
        !currentElement.associated &&
        currentElement.associateId != null &&
        currentElement.associateId.isNotEmpty);
    return isAssociate;
  }

  CanvasElement? getAssociateElement(CanvasElement canvasElement) {
    CanvasElement? associateElement = null;
    if (canvasElement.data is! DateElement) return associateElement;
    DateElement dateElement = canvasElement.data as DateElement;
    if (dateElement.associateId.isEmpty) return associateElement;
    List<CanvasElement> associateElements = [];
    for (var element in _canvasElements!) {
      if (element.data is DateElement) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.associateId == dateElement.associateId) {
          associateElements.add(element);
        }
      }
    }
    if (associateElements.length == 2) {
      //未开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.id != dateElement.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 3) {
      //未开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.id != dateElement.id && dateElement.mirrorId != dateElement1.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 4) {
      //开启镜像
      associateElements.forEach((element) {
        DateElement dateElement1 = element.data as DateElement;
        if (dateElement1.isOpenMirror == 1 && dateElement1.id != dateElement.id) {
          associateElement = element;
        }
      });
    } else if (associateElements.length == 1) {
      //开启镜像
      DateElement dateElement1 = associateElements.first.data as DateElement;
      if (dateElement1.id != dateElement.id) associateElement = associateElements.first;
    }
    return associateElement;
  }

  refreshAssociateElement(CanvasElement canvasElement) {
    // return;
    debugPrint(
        "原始元素x:${canvasElement.data.x} y:${canvasElement.data.y} width:${canvasElement.data.width} height:${canvasElement.data.height}");
    if (canvasElement.data is DateElement) {
      DateElement dateElement = canvasElement.data as DateElement;
      CanvasElement? associateCanvasElement = getAssociateElement(canvasElement);
      if (associateCanvasElement != null) {
        DateElement associateElement = associateCanvasElement.data as DateElement;
        _canvasElements?.forEach((element) {
          num x = associateElement.x;
          num y = associateElement.y;
          bool associated = associateElement.associated;
          String id = associateElement.id;
          int hashCode = associateElement.hashCode;
          String? contentTitle = associateElement.contentTitle;
          int isLock = associateElement.isLock;
          if (element.elementId == associateElement.id) {
            DateElement refreshElement = dateElement.clone() as DateElement;
            refreshElement.x = associateElement.x;
            refreshElement.y = associateElement.y;
            refreshElement.associated = associateElement.associated;
            refreshElement.id = associateElement.id;
            refreshElement.validityPeriodUnit = associateElement.validityPeriodUnit;
            refreshElement.validityPeriod = associateElement.validityPeriod;
            refreshElement.validityPeriodNew = associateElement.validityPeriodNew;
            refreshElement.isOpenMirror = associateElement.isOpenMirror;
            refreshElement.mirrorId = associateElement.mirrorId;
            refreshElement.contentTitle = associateElement.contentTitle;
            refreshElement.isLock = associateElement.isLock;
            refreshElement.lastHeight = associateElement.lastHeight;
            refreshElement.lastWidth = associateElement.lastWidth;
            refreshElement.tagForRelocation = associateElement.tagForRelocation;
            element.data = refreshElement;
            // 如果不是拉伸，则重置imageCache
            if (canvasElement.currentDragType != DragType.scale) {
              element.resetImageCache();
            }
          }
        });
      }
    }
  }

  bool isResizeEnable(element) {
    // if (element.data is TextElement && (element.data as TextElement).typesettingMode == 3) return false;
    return true;
  }

  bool isLock(element) {
    /// 当前元素被锁定 or 当前为镜像但主体被锁定
    return element.data.isLock == 1 ||
        (_canvasElements ?? []).where((e) => e.data.mirrorId == element.data.id && e.data.isLock == 1).length > 0;
  }

  bool isItemFocused(element) {
    bool isFocused = _focusedElements.contains(element) ||
        _focusedElements.where((focusedElement) => focusedElement.data.id == element.data.mirrorId).length > 0 ||
        _focusedElements.where((focusedElement) {
              if (focusedElement.data is DateElement && element.data is DateElement) {
                DateElement dateElement1 = element.data;
                DateElement dateElement2 = focusedElement.data as DateElement;
                return dateElement1.associateId.isNotEmpty && dateElement1.associateId == dateElement2.associateId;
              } else {
                return false;
              }
            }).length >
            0;
    return isFocused;
  }

  void _handleMirrorElementsRefresh(List<CanvasElement>? dataChangedCanvasElements, BuildContext context) {
    if ((dataChangedCanvasElements ?? []).length == 0) {
      return;
    }

    _logger.log("=====================刷新镜像元素");
    for (var changedElement in dataChangedCanvasElements!) {
      /// 检测镜像是否需要刷新
      if (changedElement.mirrorNeedRefresh == true || changedElement.data.isOpenMirror == 1) {
        JsonElement jsonElement = changedElement.data;

        /// 移除旧的镜像体
        _canvasElements?.removeWhere((element) {
          // String mirrorId = jsonElement.generateFixedMirrorId();
          bool needRemove = element.data.mirrorId == jsonElement.id;
          if (!needRemove &&
              jsonElement is DateElement &&
              jsonElement.associateId.isNotEmpty &&
              jsonElement.id != element.elementId &&
              element.data is DateElement) {
            CanvasElement? canvasElement = getAssociateElement(changedElement);
            DateElement dateElement = element.data as DateElement;
            if (canvasElement != null) {
              DateElement associateElement = canvasElement.data as DateElement;
              needRemove = (associateElement.mirrorId != null && associateElement.mirrorId == dateElement.id);
            }
          }
          return needRemove;
        });
      }
    }
    dataChangedCanvasElements.removeWhere((element) {
      if (element.data.type == "date") {
        ///操作完整关联元素 移除第二元素
        DateElement associateElement = element.data as DateElement;
        return (!associateElement.associated &&
            associateElement.associateId.isNotEmpty &&
            _containsAssociateElement(dataChangedCanvasElements, element));
      } else {
        return false;
      }
    });
    for (var changedElement in dataChangedCanvasElements) {
      /// 检测镜像是否需要刷新
      if (changedElement.mirrorNeedRefresh == true || changedElement.data.isOpenMirror == 1) {
        JsonElement jsonElement = changedElement.data;

        /// 创建新的镜像体
        if (jsonElement.isOpenMirror == 1) {
          JsonElement mirrorElement = createMirrorElement(context, jsonElement);
          _canvasElements?.add(mirrorElement.toCanvasElement());
          if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
            CanvasElement? canvasElement = getAssociateElement(changedElement);
            if (canvasElement != null) {
              DateElement associateElement = canvasElement.data as DateElement;
              associateElement.isOpenMirror = 1;
              JsonElement mirrorElement = createMirrorElement(context, associateElement);
              _canvasElements?.add(mirrorElement.toCanvasElement());
            }
          }
        } else if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
          /// 遍历删除关联元素的镜像体
          _canvasElements?.forEach((element) {
            if (element.data is DateElement) {
              DateElement dateElement = element.data as DateElement;
              if (dateElement.associateId == jsonElement.associateId) {
                dateElement.isOpenMirror = 0;
                dateElement.mirrorId = "";
              }
            }
          });
        }
      }
    }
    // dataChangedCanvasElements.forEach((element) {});
  }

  ///检查是否包含完整关联元素
  bool _containsAssociateElement(List<CanvasElement> canvasElements, CanvasElement currentCanvas) {
    bool isContains = false;
    if (currentCanvas.data is! DateElement || canvasElements.length < 2) return isContains;
    DateElement dateElement = currentCanvas.data as DateElement;
    if (dateElement.associateId.isEmpty) return isContains;
    canvasElements.removeWhere((element) =>
        (element.data.isOpenMirror == 0 && element.data.mirrorId != null && element.data.mirrorId.isNotEmpty));
    List<CanvasElement> elements = List<CanvasElement>.from(canvasElements.where((element) {
      if (element.data.type != "date") {
        return false;
      } else {
        DateElement dateElement2 = element.data as DateElement;
        if (dateElement2.associateId == dateElement.associateId) {
          return true;
        } else {
          return false;
        }
      }
    }));
    if (elements != null && elements.length >= 2) {
      isContains = true;
    }
    return isContains;
  }

  JsonElement createMirrorElement(BuildContext context, JsonElement jsonElement) {
    double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);

    JsonElement mirrorElement = jsonElement.clone(isMirror: true);
    mirrorElement.id = jsonElement.generateFixedMirrorId();
    jsonElement.mirrorId = mirrorElement.id;
    mirrorElement.mirrorId = jsonElement.id;
    mirrorElement.isOpenMirror = 0;
    if (jsonElement.isBindingExcel()) {
      // _templateData.cloneElementSyncExcelTask(jsonElement.id, mirrorElement.id);
    }
    if ((jsonElement.mirrorType ?? 0) > 0) {
      /// x、y 轴镜像方式下，内容不做旋转
      mirrorElement.rotate = mirrorElement.rotate;
    } else {
      mirrorElement.rotate = (jsonElement.rotate + 180) % 360;
    }

    Offset distanceToCenter = templateCenter -
        Offset(jsonElement.x.toDouble() + jsonElement.width, jsonElement.y.toDouble() + jsonElement.height);

    Offset mirrorPosition = templateCenter + distanceToCenter;
    if (jsonElement.mirrorType == 1) {
      /// 1: 画板中心 y 轴镜像
      mirrorElement.y = mirrorPosition.dy;
    } else if (jsonElement.mirrorType == 2) {
      /// 2: 画板中心 x 轴镜像
      mirrorElement.x = mirrorPosition.dx;
    } else {
      /// 0: 画板中心点镜像
      mirrorElement.x = mirrorPosition.dx;
      mirrorElement.y = mirrorPosition.dy;
    }
    _checkElementPdfBind(jsonElement, mirrorElement.id);
    return mirrorElement;
  }

  var printMargins = [0, 0, 0, 0];
  var printOffsets = [0, 0, 0, 0];

  void _handleBoxTranslated(CanvasElement? canvasElement, DragUpdate dragUpdate, bool ignoreElement) {
    _logger.log("dragUpdate==${dragUpdate.toString()}");

    // 发送拖拽事件，用于隐藏toast
    CanvasEventBus.getDefault().post({'action': 'elementDragged'});

    if (!ignoreElement && null == canvasElement) {
      _logger.log("Error: canvasElement不可为空");
      return;
    }

    /// 待处理的元素集合, 主要处理多选模式下, 其他选中元素的属性及镜像更新
    List<CanvasElement> pendingCanvasElements = [];

    /// 单选拖动时, 不显示工具条
    if (!_multiSelect) {
      floatingBarVisible = false;
    }

    /// 校正旋转状态下的组件拉伸偏移
    Offset difference = dragUpdate.size - (canvasElement?.size ?? Size.zero) as Offset;
    Offset center = canvasElement?.rect.center ?? Offset.zero;

    if (dragUpdate.dragType == DragType.size ||
        dragUpdate.dragType == DragType.horizontalSize ||
        dragUpdate.dragType == DragType.verticalSize ||
        dragUpdate.dragType == DragType.scale) {
      if (canvasElement!.data is TextElement && (canvasElement.data as TextElement).isSupportBoxStyle()) {
        if (dragUpdate.dragType == DragType.size) {
          (canvasElement.data as TextElement).handleTextElementDiagonal();
        } else if (dragUpdate.dragType == DragType.horizontalSize) {
          (canvasElement.data as TextElement).handleTextElementHorizontal();
        } else if (dragUpdate.dragType == DragType.verticalSize) {
          (canvasElement.data as TextElement).handleTextElementVertical();
        } else if (dragUpdate.dragType == DragType.scale) {
          // // 为文本元素设置拉伸模式和行数
          // TextElement textElement = canvasElement.data as TextElement;
          // textElement.calcMode = 1; // 设置为拉伸中
          // // 从当前imageCache获取行数信息
          // if (canvasElement.imageCache != null) {
          //   textElement.lines = canvasElement.imageCache!.lineNumber;
          // }
          canvasElement.currentDragType = DragType.scale;
          canvasElement.data.width = dragUpdate.size.width.dp2mm();
          canvasElement.data.height = dragUpdate.size.height.dp2mm();
          // 更新ImageData数据
          canvasElement.imageCache = canvasElement.imageCache
              ?.copyWith(width: dragUpdate.size.width.dp2px().toInt(), height: dragUpdate.size.height.dp2px().toInt());
        }

        /// 处理镜像数据
        if ((canvasElement.data.mirrorId ?? '').length > 0) {
          CanvasElement? otherCanvasElement =
              _canvasElements?.singleWhereOrNull((e) => e.data.id == canvasElement.data.mirrorId);
          if (dragUpdate.dragType == DragType.size) {
            (otherCanvasElement?.data as TextElement).handleTextElementDiagonal();
          } else if (dragUpdate.dragType == DragType.horizontalSize) {
            (otherCanvasElement?.data as TextElement).handleTextElementHorizontal();
          } else if (dragUpdate.dragType == DragType.verticalSize) {
            (otherCanvasElement?.data as TextElement).handleTextElementVertical();
          } else if (dragUpdate.dragType == DragType.scale) {
            // // 为镜像文本元素设置拉伸模式和行数
            // if (otherCanvasElement?.data is TextElement) {
            //   TextElement otherTextElement = otherCanvasElement!.data as TextElement;
            //   otherTextElement.calcMode = 1; // 设置为拉伸中
            //   // 从当前imageCache获取行数信息
            //   if (otherCanvasElement.imageCache != null) {
            //     otherTextElement.lines = otherCanvasElement.imageCache!.lineNumber;
            //   }
            // }
            otherCanvasElement?.currentDragType = DragType.scale;
            otherCanvasElement?.data.width = dragUpdate.size.width.dp2mm();
            otherCanvasElement?.data.height = dragUpdate.size.height.dp2mm();
            // 更新ImageData数据
            otherCanvasElement?.imageCache = otherCanvasElement?.imageCache?.copyWith(
                width: dragUpdate.size.width.dp2px().toInt(), height: dragUpdate.size.height.dp2px().toInt());
          }

          ///处理关联元素及镜像
          CanvasElement? associateCanvasElement = getAssociateElement(canvasElement);
          if (associateCanvasElement != null) {
            CanvasElement? otherCanvasElement1 =
                _canvasElements?.singleWhere((e) => e.data.id == associateCanvasElement.data.mirrorId);
            if (dragUpdate.dragType == DragType.size) {
              (otherCanvasElement1?.data as TextElement).handleTextElementDiagonal();
            } else if (dragUpdate.dragType == DragType.horizontalSize) {
              (otherCanvasElement1?.data as TextElement).handleTextElementHorizontal();
            } else if (dragUpdate.dragType == DragType.verticalSize) {
              (otherCanvasElement1?.data as TextElement).handleTextElementVertical();
            } else if (dragUpdate.dragType == DragType.scale) {
              // // 为关联元素的镜像文本元素设置拉伸模式和行数
              // if (otherCanvasElement1?.data is TextElement) {
              //   TextElement otherTextElement1 = otherCanvasElement1!.data as TextElement;
              //   otherTextElement1.calcMode = 1; // 设置为拉伸中
              //   // 从当前imageCache获取行数信息
              //   if (otherCanvasElement1.imageCache != null) {
              //     otherTextElement1.lines = otherCanvasElement1.imageCache!.lineNumber;
              //   }
              // }
              otherCanvasElement1?.currentDragType = DragType.scale;
              otherCanvasElement1?.data.width = dragUpdate.size.width.dp2mm();
              otherCanvasElement1?.data.height = dragUpdate.size.height.dp2mm();
              // 更新ImageData数据
              otherCanvasElement1?.imageCache = otherCanvasElement1?.imageCache?.copyWith(
                  width: dragUpdate.size.width.dp2px().toInt(), height: dragUpdate.size.height.dp2px().toInt());
            }
          }
        }
      }

      // /// 校正旋转状态下的组件拉伸偏移
      // Offset difference = dragUpdate.size - canvasElement.size as Offset;
      // Offset center = canvasElement.rect.center;

      /// 只有非缩放类型才更新组件大小
      if (dragUpdate.dragType != DragType.scale) {
        canvasElement.size = dragUpdate.size;
      }

      if (canvasElement.elementType == ElementItemType.table) {
        //拉伸table 行高列宽及时变化
        widget.onAttrPanelChange.call();
      }
      if (canvasElement.rotate == 90) {
        /// 向左下延伸
        Offset offsetCenter = center + Offset(-difference.dy, difference.dx) / 2;
        canvasElement.offset = offsetCenter - Offset(canvasElement.size.width, canvasElement.size.height) / 2;
      } else if (canvasElement.rotate == 180) {
        /// 向左上
        canvasElement.offset = canvasElement.offset - difference;
      } else if (canvasElement.rotate == 270) {
        /// 向右上延伸
        Offset offsetCenter = center + Offset(difference.dy, -difference.dx) / 2;
        canvasElement.offset = offsetCenter - Offset(canvasElement.size.width, canvasElement.size.height) / 2;
      } else {
        /// 向右下延伸
        canvasElement.offset = dragUpdate.position;
      }
      pendingCanvasElements.add(canvasElement);
      CanvasElement? associateElement = getAssociateElement(canvasElement);
      if (associateElement != null) {
        Offset difference = (dragUpdate.size - associateElement.size) as Offset;
        Offset center = associateElement.rect.center;
        // scale拉伸前面已经处理过大小
        if (dragUpdate.dragType == DragType.scale) {
          associateElement.currentDragType = DragType.scale;
          associateElement.data.width = dragUpdate.size.width.dp2mm();
          associateElement.data.height = dragUpdate.size.height.dp2mm();
          // 更新ImageData数据
          associateElement.imageCache = associateElement.imageCache
              ?.copyWith(width: dragUpdate.size.width.dp2px().toInt(), height: dragUpdate.size.height.dp2px().toInt());
        } else {
          associateElement.size = dragUpdate.size;
        }
        if (associateElement.elementType == ElementItemType.table) {
          //拉伸table 行高列宽及时变化
          widget.onAttrPanelChange?.call();
        }
        if (associateElement.rotate == 90) {
          /// 向左下延伸
          Offset offsetCenter = center + Offset(-difference.dy, difference.dx) / 2;
          associateElement.offset =
              offsetCenter - Offset(associateElement.size.width, associateElement.size.height) / 2;
        } else if (associateElement.rotate == 180) {
          /// 向左上
          associateElement.offset = associateElement.offset - difference;
        } else if (associateElement.rotate == 270) {
          /// 向右上延伸
          Offset offsetCenter = center + Offset(difference.dy, -difference.dx) / 2;
          associateElement.offset =
              offsetCenter - Offset(associateElement.size.width, associateElement.size.height) / 2;
        } else {
          /// 向右下延伸
          // refreshAssociateElement(canvasElement);
          // associateElement.offset = dragUpdate.position;
        }
        refreshAssociateElement(canvasElement);
        pendingCanvasElements.add(associateElement);
      }
    } else {
      /// 位移差值
      Offset offset = ignoreElement ? dragUpdate.position : dragUpdate.position - canvasElement!.offset;

      /// 多选状态下, 所有选中元素跟随位移
      if (_multiSelect) {
        /// 如果多选状态下移动的是镜像元素，位移取反
        if (canvasElement?.data.isOpenMirror == 0 && (canvasElement?.data.mirrorId ?? '').length > 0) {
          offset = -offset;
        }
        _focusedElements.forEach((e) {
          /// 排除当前元素和镜像元素
          if (canvasElement != null &&
              e.elementId != canvasElement.elementId &&
              e.data.mirrorId != canvasElement.elementId) {
            /// 多选状态下移动的是镜像元素，位移取反
            if (e.data.isOpenMirror == 0 && (e.data.mirrorId ?? '').length > 0) {
              e.offset = e.offset - offset;
            } else {
              e.offset = e.offset + offset;
            }
            pendingCanvasElements.add(e);
          }
        });
      }

      if (!ignoreElement) {
        /// 更新当前元素
        canvasElement!.offset = dragUpdate.position;
        pendingCanvasElements.add(canvasElement);

        /// 未选中, 则自动选中
        CanvasElement? entityElement = canvasElement;
        if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
          entityElement =
              _canvasElements?.singleWhereOrNull((element) => element.data.id == canvasElement.data.mirrorId);
        }
        if (!_focusedElements.contains(entityElement)) {
          if (!_multiSelect) {
            _focusedElements.clear();
          }
          if (entityElement != null) {
            _focusedElements.add(entityElement);
            _logger.log("多选元素，滑动选择更新悬浮条数量：${_focusedElements.length}");
            FloatingBarHelper().selectSizeChanged(_focusedElements, true);
          }
        }
      }
    }

    double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);
    for (CanvasElement element in pendingCanvasElements) {
      // /// 自由旋转角度, 暂未使用
      // element.rotate = dragUpdate.rotate.toInt();

      /// 处理镜像数据
      if ((element.data.mirrorId ?? '').length > 0) {
        CanvasElement? otherCanvasElement =
            (_canvasElements ?? []).singleWhereOrNull((e) => e.data.id == element.data.mirrorId);

        /// 清理预览图片缓存
        if (dragUpdate.dragType == DragType.size ||
            dragUpdate.dragType == DragType.horizontalSize ||
            dragUpdate.dragType == DragType.verticalSize) {
          otherCanvasElement?.resetImageCache();
        }

        Offset mirrorPosition;

        if (element.data.isOpenMirror == 1) {
          /// 当前移动组件为主体
          Offset distanceToCenter = templateCenter -
              Offset(element.data.x.toDouble() + element.data.width, element.data.y.toDouble() + element.data.height);
          mirrorPosition = templateCenter + distanceToCenter;
        } else {
          /// 当前移动组件为镜像体
          Offset distanceToCenter =
              Offset(element.data.x.toDouble() + element.data.width, element.data.y.toDouble() + element.data.height) -
                  templateCenter;
          mirrorPosition = templateCenter - distanceToCenter;
        }

        if (element.data.mirrorType == 1) {
          /// 1: 画板中心 y 轴镜像
          otherCanvasElement!.data.x = element.data.x;
          otherCanvasElement.data.y = mirrorPosition.dy;
        } else if (element.data.mirrorType == 2) {
          /// 2: 画板中心 x 轴镜像
          otherCanvasElement!.data.x = mirrorPosition.dx;
          otherCanvasElement.data.y = element.data.y;
        } else {
          /// 0: 画板中心点镜像
          otherCanvasElement!.data.x = mirrorPosition.dx;
          otherCanvasElement.data.y = mirrorPosition.dy;
        }

        otherCanvasElement.data.width = element.data.width;
        otherCanvasElement.data.height = element.data.height;
      }
    }

    setState(() {
      if (!ignoreElement) {
        canvasElement!.isDragging = true;
      }
    });
  }

  void _handleBoxTranslateEnded(CanvasElement? canvasElement, bool ignoreElement) async {
    if (!ignoreElement && null == canvasElement) {
      _logger.log("Error: canvasElement不可为空");
      return;
    }

    /// 拖动结束
    if (canvasElement != null) {
      canvasElement.isDragging = false;
      // 重置文本元素的拉伸模式
      if (canvasElement.data is TextElement) {
        (canvasElement.data as TextElement).calcMode = 0;
      }
      canvasElement.currentDragType = null; // 清除拖拽类型
    }

    /// 待处理的元素集合, 主要处理多选模式下, 其他选中元素的属性及镜像更新
    List<CanvasElement> pendingCanvasElements = ignoreElement ? [] : [canvasElement!];

    /// 多选状态下, 所有选中元素跟随位移
    if (_multiSelect) {
      _focusedElements.forEach((e) {
        /// 排除当前元素
        if (canvasElement != null && e.elementId != canvasElement.elementId) {
          pendingCanvasElements.add(e);
        } else {
          pendingCanvasElements.add(e);
        }
      });
    } else {
      /// 如果当前拖动为锁定元素，切换选中状态
      if (canvasElement!.data.isLock == 1) {
        _focusedElements.clear();
        _focusedElements.add(canvasElement);
      }
    }

    /// 镜像数据替换为主体进行镜像缓存
    for (int index = 0; index < pendingCanvasElements.length; index++) {
      CanvasElement pendingCanvasElement = pendingCanvasElements[index];
      if ((pendingCanvasElement.data.mirrorId ?? '').length > 0 && pendingCanvasElement.data.isOpenMirror != 1) {
        /// 当前移动组件为镜像体
        CanvasElement? otherCanvasElement =
            _canvasElements?.singleWhereOrNull((element) => element.data.id == pendingCanvasElement.data.mirrorId);
        pendingCanvasElement = otherCanvasElement!;
        pendingCanvasElements[index] = pendingCanvasElement;
      }
    }

    // setState(() {});
    widget.onElementFocusChanged?.call(_focusedElements, 1);

    /// 注销松开手指弹出工具条的逻辑
    // if (!_multiSelect) {
    //   // floatingBarVisible = true;
    //   FloatingBarVisibleNotifier().forceUpdate();
    // }

    if (canvasElement == null || canvasElement.data.isLock != 1) {
      /// 撤销、恢复
      List<CanvasElement> needAddAssociateElements = [];
      pendingCanvasElements.forEach(
        (element) {
          CanvasElement? needAddElement = getAssociateElement(element);
          if (needAddElement != null) needAddAssociateElements.add(needAddElement);
        },
      );
      if (needAddAssociateElements.isNotEmpty) pendingCanvasElements.addAll(needAddAssociateElements);
      StackManager().updateElements(pendingCanvasElements, context);
      StackManager().snapCanvasElements(_canvasElements);
    }

    // 如果是点9图元素需要生成截图
    if (canvasElement?.data.type == ElementItemType.image &&
        ((canvasElement?.data as ImageElement).isNinePatch ?? false)) {
      // 回调事件
      void saveScreenshot(File? file) {
        ImageElement imageElement = canvasElement?.data as ImageElement;
        imageElement.localUrl = file!.path;
      }

      // 生成截图
      CanvasEventBus.getDefault().post({
        'action': 'NinePatchImage',
        'saveScreenshot': saveScreenshot,
        'elementId': canvasElement?.data.id,
        'materialId': (canvasElement?.data as ImageElement).materialId ?? ' '
      });
    }
  }

  void _handleScaleEndWithFontSize(CanvasElement canvasElement, double newFontSize, ControlPointType controlPointType) {
    // 更新字号, 标记此元素展示的BoxFit为contain，但是只能展示这一次，后续图像库渲染保持none
    CanvasElement? associateElement = getAssociateElement(canvasElement);
    CanvasElement? mirrorElement = null;
    // 获取关联的镜像元素
    CanvasElement? associateMirrorElement = null;

    /// 处理镜像数据
    if ((canvasElement.data.mirrorId ?? '').length > 0) {
      mirrorElement = _canvasElements?.singleWhereOrNull((e) => e.data.id == canvasElement.data.mirrorId);
    }

    if (associateElement != null && (associateElement.data.mirrorId).length > 0) {
      associateMirrorElement = _canvasElements?.singleWhereOrNull((e) => e.data.id == associateElement.data.mirrorId);
    }

    if (canvasElement.data is TextElement) {
      (canvasElement.data as TextElement).fontSize = newFontSize;
      canvasElement.isShowBoxFitContain = true;
      if (associateElement != null) {
        (associateElement.data as TextElement).fontSize = newFontSize;
        associateElement.isShowBoxFitContain = true;
      }
      if (mirrorElement != null) {
        (mirrorElement.data as TextElement).fontSize = newFontSize;
        mirrorElement.isShowBoxFitContain = true;
      }
      if (associateMirrorElement != null) {
        (associateMirrorElement.data as TextElement).fontSize = newFontSize;
        associateMirrorElement.isShowBoxFitContain = true;
      }
    }
    // 缩放结束后需要进行图像库渲染
    if (controlPointType == ControlPointType.scale) {
      canvasElement.resetImageCache();
      if (associateElement != null) {
        associateElement.resetImageCache();
      }
      if (mirrorElement != null) {
        mirrorElement.resetImageCache();
      }
      if (associateMirrorElement != null) {
        associateMirrorElement.resetImageCache();
      }
    }
  }

  void _handleBoxReplace(List<CanvasElement> canvasElements) {
    widget.onImageReplace(canvasElements);
    FloatingBarHelper().dismissFloatingBar();
  }

  void _handleBoxEdit(List<CanvasElement> canvasElements) {
    if (canvasElements.first.elementType == ElementItemType.table) {
      TableElement tableElement = canvasElements.first.data as TableElement;
      List<TableCellElement> focusCells = tableElement.getFocusedCells();
      if (focusCells.length == 1) {
        widget.onElementValueEditorDisplay
            .call(focusCells.first.toCanvasElement(), canvasElements.first, isDoubleClick: true);
        FloatingBarHelper().dismissFloatingBar();
      }
    } else {
      widget.onElementValueEditorDisplay.call(canvasElements.first, canvasElements.first, isDoubleClick: true);
      FloatingBarHelper().dismissFloatingBar();
    }
  }

  void _handleBoxDeleted(List<CanvasElement> canvasElements) {
    //to do 检查合并单元格
    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    //画板上总商品字段减去即将删除的商品库字段差值等于0 就要提示阻止这一次操作
    var totalCommdityCount = getBindingGoodsElementCount();
    var handleCommdityCount = getBindingGoodsElementCount(datas: canvasElements);
    if (canvasConfigIml!.isNeedGoodsField() && (totalCommdityCount - handleCommdityCount <= 0)) {
      LoadingMix.showToast("最少选择1个商品字段");
      return;
    }

    ///删除关联第二元素单独处理
    bool isDeleteAssociate = false;
    if (canvasElements.length == 1 && canvasElements.first.data.type == "date") {
      DateElement dateElement = canvasElements.first.data as DateElement;
      if (!dateElement.associated && dateElement.associateId.isNotEmpty) isDeleteAssociate = true;
    }
    List<CanvasElement> removedElements = []..addAll(canvasElements);
    _focusedElements.removeWhere((element) {
      CanvasElement? associateElement = getAssociateElement(element);
      if (canvasElements.contains(element)) {
        return true;
      } else {
        if (associateElement != null && canvasElements.contains(associateElement)) {
          return true;
        } else {
          return false;
        }
      }
    });
    if (isDeleteAssociate) {
      CanvasElement canvasElementAssociate = canvasElements.first;
      List<CanvasElement> changedElements = [];
      CanvasElement? associateElement = getAssociateElement(canvasElementAssociate);
      associateElement?.mirrorNeedRefresh = true;
      _canvasElements?.removeWhere((element) => (element.data.id == associateElement?.data.mirrorId ||
          element.data.id == canvasElementAssociate.data.mirrorId));
      changedElements.addAll([
        associateElement!.data.clone(keepId: true).toCanvasElement(),
        canvasElementAssociate.data.clone(keepId: true).toCanvasElement()
      ]);
      _canvasElements?.removeWhere((element) => element.elementId == canvasElementAssociate.elementId);
      DateElement dateElement = associateElement?.data as DateElement;
      dateElement.associateId = "";
      dateElement.associated = false;
      _focusedElements = [associateElement];
      StackManager().unBindAssociateElements([
        associateElement.data.clone(keepId: true).toCanvasElement(),
      ], context);
      StackManager().snapCanvasElements(_canvasElements);
      _handleMirrorElementsRefresh([associateElement], context);
    } else {
      canvasElements.forEach((canvasElement) {
        /// 删除镜像
        if ((canvasElement.data.mirrorId ?? '').length > 0) {
          removedElements.addAll(
              _canvasElements?.where((element) => element.data.id == canvasElement.data.mirrorId)?.toList() ?? []);
          _canvasElements?.removeWhere((element) => element.data.id == canvasElement.data.mirrorId);
        }
        CanvasElement? associateElement = getAssociateElement(canvasElement);
        removedElements.addAll(associateElement != null ? [associateElement] : []);
        if (associateElement != null && (canvasElement.data.mirrorId ?? '').length > 0) {
          removedElements.addAll(
              _canvasElements?.where((element) => element.data.id == associateElement.data.mirrorId)?.toList() ?? []);
          _canvasElements?.removeWhere((element) => element.data.id == associateElement.data.mirrorId);
        }
      });
      _canvasElements?.removeWhere((element) {
        CanvasElement? associateElement = getAssociateElement(element);
        if (canvasElements.contains(element)) {
          return true;
        } else {
          if (associateElement != null && canvasElements.contains(associateElement)) {
            return true;
          } else {
            return false;
          }
        }
      });
      if (_focusedElements.isEmpty) {
        multiSelect = false;
        floatingBarVisible = false;
      }

      // String excelIdBefore = _templateData.externalData?.id ?? "";
      String excelIdBefore = _templateData?.getExcelMd5() ?? "";
      ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
      // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
      // TemplateModify modifyBefore = TemplateUtils.cloneModify(ExcelTransformManager().templateData.modify);
      _templateData?.clearImportExcelIfNecessary();
      // TemplateModify modifyAfter = TemplateUtils.cloneModify(ExcelTransformManager().templateData.modify);
      //记录元素修改记录
      // StackManager().stashTemplateModifyRecord(modifyBefore, modifyAfter);
      // String excelIdAfter = _templateData.externalData?.id ?? "";
      String excelIdAfter = _templateData?.getExcelMd5() ?? "";
      excelBusinessSnap.excelFileChanged = excelIdBefore != excelIdAfter;
      if (excelBusinessSnap.excelFileChanged ?? false) {
        // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
        excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
        excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
        excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
        excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
      }

      /// 撤销、恢复
      if (excelBusinessSnap.excelFileChanged ?? false) {
        StackManager().removeElements(removedElements, context, excelBusinessSnap: excelBusinessSnap);
      } else {
        StackManager().removeElements(removedElements, context);
      }
      StackManager().snapCanvasElements(_canvasElements);
    }
    FloatingBarHelper().dismissFloatingBar();
    widget.onElementFocusChanged?.call(_focusedElements, 0);
  }

  int getBindingGoodsElementCount({List<CanvasElement>? datas}) {
    int count = 0;
    List<CanvasElement>? checkDatas = (datas != null && datas.isNotEmpty) ? datas : _canvasElements;
    for (var element in (checkDatas ?? [])) {
      if (element.elementType == ElementItemType.table) {
        TableElement table = element.data as TableElement;
        count = count + table.getBingCommodityCells().length;
      } else {
        count = count + (element.data.isBindingCommodity() ? 1 : 0);
      }
    }
    return count;
  }

  void selectDefaultElementWithType(String defaultElementWithType) {
    CanvasElement? needSelectElement;
    for (var element in _canvasElements!) {
      if (element.data.type == defaultElementWithType) {
        needSelectElement = element;
        break;
      }
    }
    _handleSelectionChanged(
      needSelectElement,
      voidCallback: () {
        FloatingBarHelper().dismissFloatingBar();
      },
    );
  }

  void _handleSelectionChanged(CanvasElement? canvasElement,
      {bool fromDoubleTap = false, bool needDelay = true, VoidCallback? voidCallback}) {
    _logger.log("_handleSelectionChanged");

    // 发送点击事件，用于隐藏toast
    CanvasEventBus.getDefault().post({'action': 'elementTapped'});
    final setStateBlock = () {
      setState(() {
        var entityElement = canvasElement;

        if (canvasElement == null) {
          /// 点击背景
          if (_multiSelect) {
            this.multiSelect = false;
          }
          _focusedElements.clear();
        } else {
          /// 选中镜像则代表选中实体
          if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
            entityElement =
                _canvasElements?.singleWhereOrNull((element) => element.data.id == canvasElement.data.mirrorId);
          }
          // if (canvasElement.data is DateElement) {
          //   DateElement dateElement = canvasElement.data;
          //   if (!dateElement.associated && (dateElement.associateId ?? '').length > 0) {
          //     entityElement = _canvasElements.singleWhere((element) {
          //       if (element.data is DateElement) {
          //         DateElement dateElement1 = element.data;
          //         return dateElement1.associated && dateElement1.associateId == dateElement.associateId;
          //       } else {
          //         return false;
          //       }
          //     }, orElse: () => null);
          //   }
          // }

          /// 多选状态不可选中锁定元素
          if (_multiSelect) {
            if (isLock(canvasElement)) {
              LoadingMix.showToast(intlanguage("app100000868", "锁定元素不支持多选"));
              return;
            }
          } else {
            if (_focusedElements.contains(entityElement)) {
              ///2023/12/20 Ice_Liu 选中状态下的元素，再次点击触发输入
              widget.onElementValueEditorDisplay.call(entityElement!, entityElement,
                  isDoubleClick: _focusedElements.first.data.isBindingCommodity() ? true : false);
              return;
            } else {
              _focusedElements.clear();
            }
          }

          if (entityElement != null) {
            if (_focusedElements.contains(entityElement)) {
              _focusedElements.remove(entityElement);
            } else {
              _focusedElements.add(entityElement);

              // /// 智能提示
              // Future.delayed(Duration(milliseconds: 500), () {
              //   SmartTipsNotifier().handleElementOnTap(entityElement.data);
              // });
            }
          }

          if (!_multiSelect && !fromDoubleTap) {
            // if (canvasElement.data.type == ElementItemType.text && fromDoubleTap) {
            //   // FloatingBarVisibleNotifier().forceUpdate();
            // } else {
            //   FloatingBarVisibleNotifier().forceUpdate();
            // }
            if (voidCallback != null) {
              voidCallback.call();
            } else {
              FloatingBarVisibleNotifier().forceUpdate();
            }
            return;
          } else {
            ///判断选中是否为空
            if (_focusedElements.isEmpty) {
              floatingBarVisible = false;
              multiSelect = false;
            } else {
              _logger.log("多选元素，更新悬浮条数量：${_focusedElements.length}");
              FloatingBarHelper().selectSizeChanged(_focusedElements, true);
            }
          }
        }
      });
      if (canvasElement != null && isLock(canvasElement)) {
        widget.onElementFocusChanged?.call(_focusedElements, _multiSelect ? 2 : 0);
        // FloatingBarVisibleNotifier().forceUpdate();
        return;
      }
      if (canvasElement == null) {
        /// 点击背景
        widget.onElementFocusChanged?.call(_focusedElements, _multiSelect ? 2 : -1);
      } else {
        /// 判断是否绑定元素是为了实现选中状态下再次点击，进入输入状态
        widget.onElementFocusChanged?.call(
            _focusedElements,
            _focusedElements.isEmpty
                ? 0
                : _multiSelect
                    ? 2
                    : _focusedElements.first.data.isBindingElement()
                        ? 1
                        : 0);
      }
    };
    if (needDelay) {
      Future.delayed(Duration.zero, () {
        setStateBlock();
      });
    } else {
      setStateBlock();
    }
  }

  //是否显示右下角拖拽的小圆点 多选状态下 当选中的元素大于等于2的时候隐藏调节杆
  bool _isShowRightCorner() {
    bool isShow = true;
    if (_multiSelect == true) {
      isShow = !((FloatingBarHelper().selectedCanvasElements?.length ?? 0) > 1);
    }
    return isShow;
  }

  void _handleBoxDoubleTap(CanvasElement canvasElement) {
    Future.delayed(Duration.zero, () {
      this.multiSelect = false;
      if (canvasElement.data.isLock != 1) {
        FloatingBarHelper().dismissFloatingBar();
      }

      CanvasElement? hostElement = null;
      if (canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) {
        hostElement =
            _canvasElements?.singleWhere((element) => element.data.id == canvasElement.data.mirrorId, orElse: null);
        // if (hostElement?.data.isLock == 1) {
        //   FloatingBarHelper().dismissFloatingBar();
        //   return;
        // }
      }

      /// 锁定元素不响应双击
      // if (canvasElement.data.isLock == 1) {
      //   FloatingBarHelper().dismissFloatingBar();
      //   return;
      // }

      if ((hostElement == null && !_focusedElements.contains(canvasElement)) ||
          (hostElement != null && !_focusedElements.contains(hostElement))) {
        _handleSelectionChanged(canvasElement, fromDoubleTap: true, needDelay: false);
      }

      // if (canvasElement.data.type == ElementItemType.table) {
      //   /// 表格元素双击即选中
      //   _handleSelectionChanged(canvasElement);
      // } else
      if (canvasElement.data.canDoubleClickToEditValue()) {
        CanvasElement focusElement = canvasElement;
        if (canvasElement.data.mirrorId.isNotEmpty && canvasElement.data.isOpenMirror == 0) {
          String mirrorId = canvasElement.data.mirrorId;
          for (var element in (_canvasElements ?? [])) {
            if (element.data.id == mirrorId) {
              focusElement = element;
            }
          }
        }
        widget.onElementValueEditorDisplay.call(focusElement, focusElement, isDoubleClick: true);
      }
    });
  }

  void _handleMultiSelectChanged(bool value) {
    setState(() {
      if (value == false) {
        _focusedElements.clear();
      }
      this.multiSelect = value;
    });
  }

  /// 是否存在待选中元素未锁定
  /// 没有待选中元素、或者存在待选中元素未锁定，返回true
  /// 所有待选中的元素都锁定，返回false
  bool _checkSelectAll() {
    return _checkMultiSelectSubAction((element) => true);
  }

  /// 是否存在待选中文本类元素（文本、时间和序列号）未锁定
  /// 没有待选中文本类元素、或者存在待选中文本类元素未锁定，返回true
  /// 所有待选中的文本类元素都锁定，返回false
  bool _checkSelectAllText() {
    return _checkMultiSelectSubAction((element) => element.isTextElement());
  }

  /// 是否存在待选中线条未锁定
  /// 没有待选中线条、或者存在待选中线条未锁定，返回true
  /// 所有待选中的线条都锁定，返回false
  bool _checkSelectAllLine() {
    return _checkMultiSelectSubAction((element) => element.type == ElementItemType.line);
  }

  bool _checkMultiSelectSubAction(bool test(JsonElement element)) {
    bool newSelectLocked = false;
    List<CanvasElement> newSelectElements = [];
    _canvasElements?.forEach((element) {
      JsonElement jsonElement = element.data;
      if (test(jsonElement) &&
          !jsonElement.isMirrorElement() &&
          !_focusedElements.any((it) => it.data.id == jsonElement.id)) {
        if (jsonElement.isLock != 1) {
          newSelectElements.add(element);
        } else {
          newSelectLocked = true;
        }
      }
    });
    if (newSelectLocked && newSelectElements.isEmpty) {
      LoadingMix.showToast(intlanguage("app100000868", "锁定元素不支持多选"));
      return false;
    }
    return true;
  }

  /// 选中所有元素
  void _handleSelectAll() {
    _handleMultiSelectSubAction((element) => true);
  }

  /// 选中所有文本类元素（文本、时间和序列号）
  void _handleSelectAllText() {
    _handleMultiSelectSubAction((element) => element.isTextElement());
  }

  /// 选中所有线条
  void _handleSelectAllLine() {
    _handleMultiSelectSubAction((element) => element.type == ElementItemType.line);
  }

  void _handleMultiSelectSubAction(bool test(JsonElement element)) {
    List<CanvasElement> retainSelectElements = [];
    _focusedElements.forEach((element) {
      JsonElement jsonElement = element.data;
      if (test(jsonElement)) {
        retainSelectElements.add(element);
      }
    });
    bool newSelectLocked = false;
    List<CanvasElement> newSelectElements = [];
    _canvasElements?.forEach((element) {
      JsonElement jsonElement = element.data;
      if (test(jsonElement) &&
          !jsonElement.isMirrorElement() &&
          !_focusedElements.any((it) => it.data.id == jsonElement.id)) {
        if (jsonElement.isLock != 1) {
          newSelectElements.add(element);
        } else {
          newSelectLocked = true;
        }
      }
    });
    if (_focusedElements.length != retainSelectElements.length || newSelectElements.isNotEmpty) {
      _focusedElements.clear();
      _focusedElements.addAll(retainSelectElements);
      _focusedElements.addAll(newSelectElements);
      FloatingBarHelper().selectSizeChanged(_focusedElements, true);
      widget.onElementFocusChanged?.call(_focusedElements, 0);
    }
    if (newSelectLocked) {
      LoadingMix.showToast(intlanguage("app100000868", "锁定元素不支持多选"));
    }
  }

  void _handleBoxCopied(List<CanvasElement> canvasElements) {
    // _focusedElements.clear();
    TemplateModify? originModify = TemplateUtils.cloneModify(_templateData?.modify);
    List<CanvasElement> addedElements = [];
    Map<String, String> copyInfo = {};
    List<CanvasElement> needCopyCanvasElements = [];
    canvasElements.forEach((element) {
      //复制关联元素 给副本重新生成关联id
      JsonElement jsonElement = element.data;
      if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
        if (copyInfo[jsonElement.associateId] != null) return;
        copyInfo[jsonElement.associateId] = JsonElement.generateId();
      }
      needCopyCanvasElements.add(element);
      //查询如果存在关联元素 也需要复制
      CanvasElement? associateElement = getAssociateElement(element);
      if (associateElement != null) {
        needCopyCanvasElements.add(associateElement);
      }
    });
    // 按照元素的y坐标进行排序
    needCopyCanvasElements.sort((a, b) => a.data.y.compareTo(b.data.y));

    needCopyCanvasElements.forEach((element) {
      JsonElement jsonElement = element.data.clone(templateData: _templateData);
      if (element.data.isBindingExcel()) {
        _templateData?.cloneElementSyncExcelTask(element.elementId, jsonElement.id);
      }
      _checkElementPdfBind(element.data, jsonElement.id);
      jsonElement.x += 2.0;
      jsonElement.y += 2.0;

      if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
        jsonElement.associateId = copyInfo[jsonElement.associateId]!;
      }
      if (jsonElement.isOpenMirror == 1) {
        /// 镜像实体被复制，也需新增一个镜像
        JsonElement mirrorElement = createMirrorElement(context, jsonElement);
        _canvasElements?.add(mirrorElement.toCanvasElement());
      }

      /// 添加实体
      CanvasElement canvasElement = jsonElement.toCanvasElement();
      _canvasElements?.add(canvasElement);

      addedElements.add(canvasElement);
    });
    //   JsonElement jsonElement = element.data.clone(templateData: _templateData);
    //   if (element.data.isBindingExcel()) {
    //     _templateData?.cloneElementSyncExcelTask(element.elementId, jsonElement.id);
    //   }
    //   _checkElementPdfBind(element.data, jsonElement.id);
    //   // jsonElement.x += 2.0;
    //   if (element.rotate == 90 || element.rotate == 270) {
    //     jsonElement.x += 1.0 + jsonElement.height;
    //   } else {
    //     jsonElement.y += 1.0 + jsonElement.height;
    //   }
    //   if (jsonElement is DateElement && jsonElement.associateId.isNotEmpty) {
    //     jsonElement.associateId = copyInfo[jsonElement.associateId]!;
    //   }
    //   if (jsonElement.isOpenMirror == 1) {
    //     /// 镜像实体被复制，也需新增一个镜像
    //     JsonElement mirrorElement = createMirrorElement(context, jsonElement);
    //     _canvasElements?.add(mirrorElement.toCanvasElement());
    //   }
    //
    //   /// 添加实体
    //   CanvasElement canvasElement = jsonElement.toCanvasElement();
    //   _canvasElements?.add(canvasElement);
    //
    //   addedElements.add(canvasElement);
    // });
    StackManager().stashTemplateModifyRecord(originModify, TemplateUtils.cloneModify(_templateData?.modify));

    /// 撤销、恢复
    StackManager().addElements(addedElements, context);
    StackManager().snapCanvasElements(_canvasElements);

    if (!_multiSelect) {
      _focusedElements.clear();
      if (_canvasElements?.last != null) {
        _focusedElements.add(_canvasElements!.last);
      }
    } else {
      _focusedElements.clear();
      _focusedElements.addAll(addedElements);
    }
    // List<CanvasElement> canvasElementChanged = [];
    // for (var element in _canvasElements) {
    //   JsonElement firstData = canvasElements.first.data;
    //   JsonElement elementData = element.data;
    //   if (elementData is DateElement &&
    //       firstData is DateElement &&
    //       elementData.associateId == firstData.associateId &&
    //       !elementData.associated &&
    //       firstData.associated) {
    //     canvasElementChanged = [element];
    //   }
    // }

    // Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElementChanged);
    widget.onElementFocusChanged?.call(_focusedElements, 0);
    FloatingBarVisibleNotifier().forceUpdate();
  }

  _checkElementPdfBind(JsonElement element, String newElementId) {
    if (element.type == "image" && PdfBindInfoManager.instance.elementIsBindPdf(element.id)) {
      PdfBindInfo bindInfoModel = PdfBindInfoManager.instance.getPDFBindInfoByElementId(element.id)!.copyWith();
      bindInfoModel.bindedElementId = newElementId;
      PdfBindInfoManager.instance.addNewPDFBindInfo(bindInfoModel);
    }
  }

  void _handleBoxRotate(List<CanvasElement> canvasElements) {
    _isRotating = true;
    canvasElements.forEach((element) {
      element.data.rotate += 90;
      element.data.rotate %= 360;
    });

    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
    // if (!_multiSelect) {
    //   floatingBarVisible = false;
    // }
    widget.onAttrPanelChange.call();

    // 延迟重置旋转标志，确保数据变化监听器能够正确处理
    Future.delayed(Duration(milliseconds: 100), () {
      _isRotating = false;
    });
  }

  void _handleBoxLock(List<CanvasElement> canvasElements) {
    /// 对齐第一个元素的锁定状态
    canvasElements.first.data.isLock = (canvasElements.first.data.isLock == 1) ? 0 : 1;
    List<String> mirrorIdList = [];
    canvasElements.forEach((element) {
      if (element.data.isOpenMirror == 1 && element.data.mirrorId.length > 0) {
        mirrorIdList.add(element.data.mirrorId);
      }
      CanvasElement? needAddElement = getAssociateElement(element);
      if (needAddElement != null) {
        mirrorIdList.add(needAddElement.data.id);
        if (needAddElement.data.isOpenMirror == 1 && needAddElement.data.mirrorId.length > 0) {
          mirrorIdList.add(needAddElement.data.mirrorId);
        }
      }
      if (element != canvasElements.first) {
        element.data.isLock = canvasElements.first.data.isLock;
      }
    });
    _canvasElements?.forEach((element) {
      if (mirrorIdList.contains(element.data.id)) {
        element.data.isLock = canvasElements.first.data.isLock;
      }
    });

    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
    List<CanvasElement> needAddAssociateElements = [];
    canvasElements.forEach(
      (element) {
        refreshAssociateElement(element);
        CanvasElement? needAddElement = getAssociateElement(element);
        needAddElement?.data.isLock = element.data.isLock;
        if (needAddElement != null) needAddAssociateElements.add(needAddElement);
      },
    );
    if (needAddAssociateElements.isNotEmpty) canvasElements.addAll(needAddAssociateElements);

    /// 撤销、恢复
    StackManager().updateElements(canvasElements, context);
    StackManager().snapCanvasElements(_canvasElements);

    ///锁定元素后取消多选
    multiSelect = false;

    /// 锁定元素后，刷新操作面板
    widget.onElementFocusChanged?.call([canvasElements.first], 3);
    FloatingBarVisibleNotifier().forceUpdate();
  }

  void _handleBoxAlign(int action, List<CanvasElement> canvasElements) {
    LayoutPanelHandler.handleLayoutAdjust(context, canvasElements, action);
  }

  /// 处理表格cell的悬浮条操作
  ///
  /// action说明：
  ///       0：删除选中行
  ///       1：选中行下方插入行
  ///       2：删除选中列
  ///       3：选中列右侧插入列
  ///       4：清空已选中cell的内容
  void _handleCellAction(int action, List<CanvasElement> canvasElements) {
    floatingBarVisible = false;
    TableElement tableElement = canvasElements.first.data as TableElement;
    if (action == 0) {
      // String excelIdBefore = _templateData.externalData?.id ?? "";
      String excelIdBefore = _templateData?.getExcelMd5() ?? "";
      ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
      // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
      List<JsonElement> elementsOfBefore = [];
      canvasElements.forEach((element) {
        elementsOfBefore.add(element.data.clone(keepId: true));
      });
      excelBusinessSnap.elementsOfBefore = elementsOfBefore;
      CellPosition bottomRight = tableElement.findFocusedCellsBottomRight();
      CellPosition topLeft = tableElement.findFocusedCellsTopLeft();
      bool result =
          TablePanelHandler.handleDelRowInRange(context, canvasElements, topLeft.rowIndex, bottomRight.rowIndex);
      if (!result) {
        return;
      }
      _templateData?.clearImportExcelIfNecessary();
      // String excelIdAfter = _templateData.externalData?.id ?? "";
      String excelIdAfter = _templateData?.getExcelMd5() ?? "";
      excelBusinessSnap.excelFileChanged = excelIdBefore != excelIdAfter;
      if (excelBusinessSnap.excelFileChanged ?? false) {
        // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
        excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
        excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
        excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
        List<JsonElement> elementsOfAfter = [];
        canvasElements.forEach((element) {
          elementsOfAfter.add(element.data.clone(keepId: true));
        });
        excelBusinessSnap.elementsOfAfter = elementsOfAfter;
        excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
        StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
      }
      StackManager()
          .stashTemplateModifyRecord(excelBusinessSnap.modifyBefore, TemplateUtils.cloneModify(_templateData?.modify));

      if (!(excelBusinessSnap.excelFileChanged ?? false)) {
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
      } else {
        canvasElements.forEach((element) {
          element.resetImageCache();
        });
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
      }

      ///选中单元格发生变化后，键盘写入要收起
      Future.delayed(const Duration(milliseconds: 200), () {
        widget.onElementFocusChanged?.call(_focusedElements, 0);
      });
    } else if (action == 2) {
      // String excelIdBefore = _templateData.externalData?.id ?? "";
      String excelIdBefore = _templateData?.getExcelMd5() ?? "";
      ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
      // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
      List<JsonElement> elementsOfBefore = [];
      canvasElements.forEach((element) {
        elementsOfBefore.add(element.data.clone(keepId: true));
      });
      excelBusinessSnap.elementsOfBefore = elementsOfBefore;
      CellPosition bottomRight = tableElement.findFocusedCellsBottomRight();
      CellPosition topLeft = tableElement.findFocusedCellsTopLeft();
      TablePanelHandler.handleDelColumnInRange(context, canvasElements, topLeft.columnIndex, bottomRight.columnIndex);
      _templateData?.clearImportExcelIfNecessary();
      // String excelIdAfter = _templateData.externalData?.id ?? "";
      String excelIdAfter = _templateData?.getExcelMd5() ?? "";
      excelBusinessSnap.excelFileChanged = excelIdBefore != excelIdAfter;
      if (excelBusinessSnap.excelFileChanged ?? false) {
        // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
        excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
        excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
        excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
        List<JsonElement> elementsOfAfter = [];
        canvasElements.forEach((element) {
          elementsOfAfter.add(element.data.clone(keepId: true));
        });
        excelBusinessSnap.elementsOfAfter = elementsOfAfter;
        excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
        StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
      }
      StackManager()
          .stashTemplateModifyRecord(excelBusinessSnap.modifyBefore, TemplateUtils.cloneModify(_templateData?.modify));
      if (!(excelBusinessSnap.excelFileChanged ?? false)) {
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
      } else {
        canvasElements.forEach((element) {
          element.resetImageCache();
        });
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
      }

      Future.delayed(const Duration(milliseconds: 200), () {
        widget.onElementFocusChanged?.call(_focusedElements, 0);
      });
    } else if (action == 1) {
      CellPosition bottomRight = tableElement.findFocusedCellsBottomRight();
      TablePanelHandler.handleAddRowAfterRowIndex(context, canvasElements, bottomRight.rowIndex);
      widget.onAttrPanelChange.call();
    } else if (action == 3) {
      CellPosition bottomRight = tableElement.findFocusedCellsBottomRight();
      TablePanelHandler.handleAddColumnAfterColumnIndex(context, canvasElements, bottomRight.columnIndex);
      widget.onAttrPanelChange.call();
    } else if (action == 4) {
      // String excelIdBefore = _templateData.externalData?.id ?? "";
      String excelIdBefore = _templateData?.getExcelMd5() ?? "";
      ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
      // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
      List<JsonElement> elementsOfBefore = [];
      canvasElements.forEach((element) {
        elementsOfBefore.add(element.data.clone(keepId: true));
      });
      excelBusinessSnap.elementsOfBefore = elementsOfBefore;
      CellPosition bottomRight = tableElement.findFocusedCellsBottomRight();
      CellPosition topLeft = tableElement.findFocusedCellsTopLeft();
      TemplateModify? originModify = TemplateUtils.cloneModify(_templateData?.modify);
      TablePanelHandler.handleClearCellData(context, canvasElements, topLeft, bottomRight);
      StackManager().stashTemplateModifyRecord(originModify, TemplateUtils.cloneModify(_templateData?.modify));

      _templateData?.clearImportExcelIfNecessary();
      // String excelIdAfter = _templateData.externalData?.id ?? "";
      String excelIdAfter = _templateData?.getExcelMd5() ?? "";
      excelBusinessSnap.excelFileChanged = excelIdBefore != excelIdAfter;
      if (excelBusinessSnap.excelFileChanged ?? false) {
        // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
        excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
        excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
        excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
        List<JsonElement> elementsOfAfter = [];
        canvasElements.forEach((element) {
          elementsOfAfter.add(element.data.clone(keepId: true));
        });
        excelBusinessSnap.elementsOfAfter = elementsOfAfter;
        excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
        StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
      }
      StackManager()
          .stashTemplateModifyRecord(excelBusinessSnap.modifyBefore, TemplateUtils.cloneModify(_templateData?.modify));
      if (!(excelBusinessSnap.excelFileChanged ?? false)) {
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
      } else {
        canvasElements.forEach((element) {
          element.resetImageCache();
        });
        Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
      }

      Future.delayed(const Duration(milliseconds: 200), () {
        widget.onElementFocusChanged?.call(_focusedElements, 0);
        widget.onAttrPanelChange.call();
      });
    }

    ///清除数据有可能也要刷新底部面板，因为键盘写入也要同步更新
    ///此处不在调用面板变化事件，onElementFocusChanged事件本身会刷新属性面板
    // widget.onAttrPanelChange?.call();
  }

  void _handleMergeCells(List<CanvasElement> canvasElements) {
    floatingBarVisible = false;
    TableElement tableElement = canvasElements.first.data as TableElement;

    // String excelIdBefore = _templateData.externalData?.id ?? "";
    String excelIdBefore = _templateData?.getExcelMd5() ?? "";
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
    excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
    excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
    excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
    excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    List<JsonElement> elementsOfBefore = [];
    canvasElements.forEach((element) {
      elementsOfBefore.add(element.data.clone(keepId: true));
    });
    excelBusinessSnap.elementsOfBefore = elementsOfBefore;

    /// 更新修改记录的 id
    /// 单元格合并时, 第一个原始单元格的 id 修改为合并后单元格的 id
    tableElement.mergeFocusedCellsClearImportExcel();
    Function(String, String) cloneCellSyncExcelTask = (originCellId, mergedCellId) {
      _templateData?.cloneElementSyncExcelTask(originCellId, mergedCellId);
    };
    TableCellElement? combineCellElement = tableElement.mergeFocusedCells(cloneCellSyncExcelTask);
    // _templateData.updateModifyRecordId(combineCellElement.firstCellOfCombine.id, combineCellElement.id);
    _templateData?.cloneElementSyncExcelTask(combineCellElement!.firstCellOfCombine!.id, combineCellElement.id);
    _templateData?.clearImportExcelIfNecessary();

    // String excelIdAfter = _templateData.externalData?.id ?? "";
    String excelIdAfter = _templateData?.getExcelMd5() ?? "";
    excelBusinessSnap.excelFileChanged = excelIdBefore != excelIdAfter;
    if (excelBusinessSnap.excelFileChanged ?? false) {
      // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
      List<JsonElement> elementsOfAfter = [];
      canvasElements.forEach((element) {
        elementsOfAfter.add(element.data.clone(keepId: true));
      });
      excelBusinessSnap.elementsOfAfter = elementsOfAfter;
      excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
      StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
    }
    StackManager()
        .stashTemplateModifyRecord(excelBusinessSnap.modifyBefore, TemplateUtils.cloneModify(_templateData?.modify));

    if (!(excelBusinessSnap.excelFileChanged ?? false)) {
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
    } else {
      canvasElements.forEach((element) {
        element.resetImageCache();
      });
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
    }

    Future.delayed(const Duration(milliseconds: 200), () {
      widget.onElementFocusChanged?.call(_focusedElements, 0);
    });
  }

  void _handleSplitCells(List<CanvasElement> canvasElements) {
    floatingBarVisible = false;
    TableElement tableElement = canvasElements.first.data as TableElement;
    List<TableCellElement> focusCells = tableElement.getFocusedCells();

    /// 将合并单元格的内容赋值到原始单元格
    focusCells.first.firstCellOfCombine?.value = focusCells.first.value;
    focusCells.first.firstCellOfCombine?.dataBind = focusCells.first.dataBind;
    focusCells.first.firstCellOfCombine?.bindingColumn = focusCells.first.firstCellOfCombine?.getBindingColumn() ?? -1;

    /// 更新修改记录的 id
    /// 单元格拆分时, 合并单元格的 id 修改为第一个原始单元格的 id
    // _templateData.updateModifyRecordId(focusCells.first.id, focusCells.first.firstCellOfCombine.id);
    _templateData?.cloneElementSyncExcelTask(focusCells.first.id, focusCells.first.firstCellOfCombine!.id);

    tableElement.splitFocusedCell();

    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);

    /// 2023/12/28 Ice_Liu 此处调用主要是为了拆分后输入焦点定位到当前cell
    widget.onElementFocusChanged?.call(_focusedElements, 0);
  }

  void _onInteractionUpdate(Offset offset, double scale) {
    // _logger.log(
    //     'Interaction Update - Focal point: $offset , Scale: $scale , initRulerOffset = $initRulerOffset');
    // _horizontalRulerKey.currentState
    //     .jump((_initRulerOffset - offset) * scale, scale);
    // _verticalRulerKey.currentState.jump(offset * scale, scale);
    // if (!_multiSelect) {
    //   floatingBarVisible = false;
    // }
    if (!_multiSelect) {
      FloatingBarHelper().dismissFloatingBar();
    }
  }

  void clearCanvas() {
    // String excelId = _templateData.externalData?.id ?? "";
    String excelId = _templateData?.getExcelMd5() ?? "";
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    if ((_templateData?.dataSource?.isNotEmpty ?? false) &&
        _templateData?.dataSource?.first.type == TemplateDataSource.DataSourceType.commodity) {
      excelBusinessSnap.excelFileChanged = true;
    } else {
      excelBusinessSnap.excelFileChanged = excelId.isNotEmpty;
    }
    if (excelBusinessSnap.excelFileChanged ?? false) {
      // excelBusinessSnap.externalDataBefore = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceBefore = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    }

    /// 清空 excel
    _templateData?.injectExcelData(null);

    /// 清空商品库
    _templateData?.injectGoodsData(null);

    // /// 清空数据绑定方式
    // StackManager().stashDataBindingModeRecord(_templateData.dataBindingMode, DataBindingMode.none);
    // _templateData.dataBindingMode = DataBindingMode.none;

    if (excelBusinessSnap.excelFileChanged ?? false) {
      // excelBusinessSnap.externalDataAfter = _templateData.cloneExternalData();
      excelBusinessSnap.dataSourceAfter = _templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexAfter = _templateData?.currentPageIndex;
      excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(_templateData?.modify);
      excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
    }

    /// 撤销、恢复
    if (excelBusinessSnap.excelFileChanged ?? false) {
      StackManager().removeElements(_canvasElements ?? [], context, excelBusinessSnap: excelBusinessSnap);
    } else {
      StackManager().removeElements(_canvasElements ?? [], context);
    }

    this.multiSelect = false;
    _canvasElements?.clear();
    _focusedElements.clear();

    /// 撤销、恢复
    StackManager().snapCanvasElements(_canvasElements);

    widget.onElementFocusChanged?.call(_focusedElements, 0);

    ///隐藏工具条
    floatingBarVisible = false;
  }

  ///调起修改背景图片流程
  _onTemplateBackgroundChangeTap(int index) {
    _logger.log("call 多背景修改--->index：$index");
    if (_templateData?.multipleBackIndex != index) {
      setState(() {
        _templateData?.multipleBackIndex = index;
      });
    }
  }

  /// 页数展示
  _showBatchCopies() {
    if (_multiSelect == true) {
      _handleMultiSelectChanged(false);
      widget.onElementFocusChanged?.call([], 0);
    }
    floatingBarVisible = false;
    Color? contentColor = Colors.black;
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    if (printColor.isNotEmpty && !RfidUtil.is16GrayColorStr(printColor)) {
      contentColor = Color.fromARGB(255, int.parse(printColor.split(".")[0]), int.parse(printColor.split(".")[1]),
          int.parse(printColor.split(".")[2]));
    } else {
      contentColor = null;
    }

    LoadingMix.showLoading();
    SharedPreferences.getInstance().then((sp) {
      int crossCount = sp.getInt("batch_cross_axis_count") ?? 2;
      showModalBottomSheet(
          barrierColor: CanvasTheme.of(context).barrierColor,
          isScrollControlled: true,
          context: context,
          backgroundColor: Colors.transparent,
          enableDrag: true,
          builder: (_) {
            return AnimatedPadding(
                padding: EdgeInsets.fromLTRB(0, 40, 0, 0),
                duration: const Duration(milliseconds: 100),
                child: BatchTemplatePreview(_templateData!, context, contentColor, crossCount, (pageIndex) {
                  if (_templateData?.currentPageIndex != pageIndex) {
                    setState(() {
                      _templateData?.currentPageIndex = pageIndex;
                      if (PdfBindInfoManager.instance.templateIsBindPdf(_templateData!)) {
                        PdfBindInfoManager.instance.currentPageIndex = pageIndex;
                      }
                      _templateData?.changePageInfo();
                      _templateData?.resetImageCache();
                    });
                    widget.onAttrPanelChange.call();
                  }
                }));
          });
    });
  }

  onAdvanceQRCodePreview(CanvasElement cursorElement, {required Function() onElementValueDisplay}) {
    QrCodeElement element = cursorElement.data as QrCodeElement;
    final qrCodeModel = AdvanceQRCodeManager().getAdvanceQRCodeInfo(element);
    if (qrCodeModel?.deleted ?? false) {
      if (qrCodeModel!.isForm ?? false) {
        LoadingMix.showToast(intlanguage("app100001148", "该表单已被删除"));
      } else {
        LoadingMix.showToast(intlanguage("app100001145", "该高级二维码已被删除"));
      }
      return;
    }
    // 非登录下不可用
    if (!CanvasUserCenter().isLogin) {
      String title = intlanguage('app00210', '当前未登录，请先登录！');
      String cancelDes = intlanguage('app00030', '取消');
      String confirmDes = intlanguage('app01191', '立即登录');
      showNimmbotDialog(context, title: title, cancelDes: cancelDes, confirmDes: confirmDes, confirmAction: () {
        // 无网络不可用
        (Connectivity().customCheckConnectivity()).then((value) {
          if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
            LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
            return;
          }
        });
        CanvasUserCenter().handleLogin(context, () {
          onAdvanceQRCodePreview(cursorElement, onElementValueDisplay: onElementValueDisplay);
        });
      });
    } else {
      String codeType = "";
      String codeId = "";
      if (element.isForm) {
        codeType = AdvanceQRCodeType.form.getStringValue();
        codeId = element.formId!;
      } else {
        codeType = AdvanceQRCodeType.liveCode.getStringValue();
        codeId = element.liveCodeId!;
      }

      ///加载H5
      (Connectivity().customCheckConnectivity()).then((value) async {
        // 无网络不可用
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
          return;
        } else {
          if (qrCodeModel != null && int.parse(qrCodeModel.userId!) == CanvasUserCenter().userId) {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
              builder: (BuildContext context) {
                String url = element.value!;
                url = "$url?disabled=1";
                return CustomWebPage(
                  title: intlanguage('app01102', '预览'),
                  url: url ?? '',
                );
              },
            ).then((value) {});

            ///判断是否属于当前用户 是：调起小程序 否：通过shortUrl 加载H5
            // if (qrCodeModel.template == "registration") {
            //   showModalBottomSheet(
            //     context: context,
            //     isScrollControlled: true,
            //     shape: const RoundedRectangleBorder(
            //         borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            //     builder: (BuildContext context) {
            //       String url = element.value!;
            //       url = "$url?disabled=1";
            //       return CustomWebPage(
            //         title: intlanguage('app01102', '预览'),
            //         url: url ?? '',
            //       );
            //     },
            //   ).then((value) {});
            // } else {
            //   final advanceQRCodeImpl = CanvasPluginManager().advanceQRCodeImpl;
            //   advanceQRCodeImpl?.previewAdvanceQRCode(context, codeId, codeType).then((value) {
            //     AdvanceQRCodeManager().updataAdvanceQRCodeCache(value);
            //     widget.onAdvanceQRCodeInfoChanged.call();
            //   });
            // }
          } else {
            if (Platform.isAndroid) {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                builder: (BuildContext context) {
                  String url = element.value!;
                  url = "$url?disabled=1";
                  return CustomWebPage(
                    title: intlanguage('app01102', '预览'),
                    url: url ?? '',
                  );
                },
              ).then((value) {});
            } else {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                builder: (BuildContext context) {
                  String url = element.value!;
                  url = "$url?disabled=1";
                  return CustomWebPage(
                    title: intlanguage('app01102', '预览'),
                    url: url ?? '',
                  );
                },
              ).then((value) {});
            }
          }
        }
      });
    }
  }

  /// 处理智能提示点击
  _onSmartTipsTap(SmartTipsAction action) {
    SmartTipsNotifier().smartTipsMode = SmartTipsMode.smartMultiSelect;
    _focusedElements.clear();

    if (action == SmartTipsAction.selectAll) {
      /// 添加排除镜像体外的所有元素为焦点状态
      _focusedElements.addAll((_canvasElements ?? []).where((canvasElement) =>
          !(canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0)));

      widget.onElementFocusChanged?.call(_focusedElements, 0);
    } else if (action == SmartTipsAction.selectAllTextElements) {
      /// 添加排除镜像体外的所有文本元素为焦点状态
      _focusedElements.addAll((_canvasElements ?? []).where((canvasElement) =>
          !(canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) &&
          canvasElement.elementType == ElementItemType.text));

      widget.onElementFocusChanged?.call(_focusedElements, 0);
    } else if (action == SmartTipsAction.selectAllLineElements) {
      /// 添加排除镜像体外的所有线条元素为焦点状态
      _focusedElements.addAll((_canvasElements ?? []).where((canvasElement) =>
          !(canvasElement.data.isOpenMirror == 0 && (canvasElement.data.mirrorId ?? '').length > 0) &&
          canvasElement.elementType == ElementItemType.line));

      widget.onElementFocusChanged?.call(_focusedElements, 0);
    } else if (action == SmartTipsAction.clearCanvas) {
      clearCanvas();
    }
  }

  /// 商品列表数据导入
  goodsImport() {
    /// 注入商品库数据
    _templateData?.resetImageCache();
    StackManager().updateElements(_templateData?.canvasElements ?? [], context);
    StackManager().snapCanvasElements(_canvasElements);

    setState(() {});
  }

  /// 商品显示属性变动
  onGoodsFieldsChanged(List<String> goodsFields) {
    List<String> originGoodsFields = _templateData?.goodsFields() ?? [];
    List<String> deletedGoodsFields = originGoodsFields.where((element) => !goodsFields.contains(element)).toList();
    List<String> addedGoodsFields = goodsFields.where((element) => !originGoodsFields.contains(element)).toList();

    if (kDebugMode) {
      _logger.log('移除的商品属性: $deletedGoodsFields, 新增的商品属性: $addedGoodsFields');
    }

    if (deletedGoodsFields.isNotEmpty) {
      /// 查询待删除的元素
      List<CanvasElement> deletedCanvasElements =
          (_canvasElements ?? []).where((element) => deletedGoodsFields.contains(element.data.fieldName)).toList();

      /// 缓存, 下次添加时复用
      GoodsFieldElementCache().cache(deletedCanvasElements);

      /// 画板上移除待删除元素
      _handleBoxDeleted(deletedCanvasElements);
    }

    if (addedGoodsFields.isNotEmpty) {
      /// 组装需要添加的字段元素
      List<CanvasElement> addedCanvasElements = [];
      addedGoodsFields.forEach((fieldName) {
        addedCanvasElements.add(GoodsFieldElementCache().getCanvasElement4Field(fieldName)!);
      });

      /// 添加元素
      addCanvasElements(addedCanvasElements, fromBusiness: true);
    }
  }

  _switchToGoodsDataBindingMode() {
    StackManager().stashDataBindingModeRecord(_templateData?.dataBindingMode, DataBindingMode.commodity);
    _templateData?.dataBindingMode = DataBindingMode.commodity;

    /// 清空 Excel 关联数据
    _templateData?.injectExcelData(null);

    /// 清空 Excel 列绑定关系
    List<CanvasElement> clearedCanvasElements = _templateData?.clearExcelColumnBinding() ?? [];
    if (clearedCanvasElements.length > 0) {
      clearedCanvasElements.forEach((clearedCanvasElement) {
        clearedCanvasElement.resetImageCache();
      });
    }

    /// 撤销、恢复
    if (clearedCanvasElements.isNotEmpty) {
      StackManager().updateElements(clearedCanvasElements, context);
    } else if (_canvasElements?.isNotEmpty == true) {
      StackManager().updateElements(_canvasElements!, context);
    } else {
      StackManager().updateExtra(context);
    }
    StackManager().snapCanvasElements(_canvasElements);

    setState(() {});
  }

  _switchToExcelDataBindingMode() {
    StackManager().stashDataBindingModeRecord(_templateData?.dataBindingMode, DataBindingMode.excel);
    _templateData?.dataBindingMode = DataBindingMode.excel;

    /// 清空商品库数据
    _templateData?.injectGoodsData(null);

    /// 清空 商品库 字段绑定关系
    List<CanvasElement> clearedCanvasElements = _templateData?.clearGoodsFieldBinding() ?? [];
    if (clearedCanvasElements.length > 0) {
      clearedCanvasElements.forEach((clearedCanvasElement) {
        clearedCanvasElement.resetImageCache();
      });
    }

    /// 撤销、恢复
    if (clearedCanvasElements.isNotEmpty) {
      StackManager().updateElements(clearedCanvasElements, context);
    } else if (_canvasElements?.isNotEmpty == true) {
      StackManager().updateElements(_canvasElements!, context);
    } else {
      StackManager().updateExtra(context);
    }
    StackManager().snapCanvasElements(_canvasElements);

    setState(() {});
  }

  /// 处理数据绑定方式切换
  Future<void> onDataBindingModeSwitch(String dataBindingMode) {
    if (_templateData?.dataBindingMode != dataBindingMode) {
      if (dataBindingMode == DataBindingMode.commodity) {
        /// 切换到商品库模式
        /// 存在旧 excel 数据, 需作提示
        if (_templateData?.existBatchBindingData() == true) {
          return showCustomDialog(
              context, intlanguage('app00032', '提示'), intlanguage('app100000787', '切换为商品库将会清空已选的 Excel 数据，确定清空吗？'),
              rightFunCall: () {
            _switchToGoodsDataBindingMode();
          });
        } else {
          _switchToGoodsDataBindingMode();
          return Future.value();
        }
      } else if (dataBindingMode == DataBindingMode.excel) {
        /// 切换到 excel 模式

        /// 存在旧 商品库 数据, 需作提示
        if (_templateData?.existBatchBindingData() == true) {
          return showCustomDialog(
              context, intlanguage('app00032', '提示'), intlanguage('app100000787', '切换为 Excel 将会清空已选的商品库数据，确定清空吗？'),
              rightFunCall: () {
            _switchToExcelDataBindingMode();
          });
        } else {
          _switchToExcelDataBindingMode();
          return Future.value();
        }
      }
    }
    return Future.value();
  }

  // /// excel 数据导入
  // excelImport(ExcelDataModel excelData, {bool isFromTableCell = false}) {
  //   /// 清空旧的修改记录
  //   _templateData.clearModifyRecord();
  //
  //   /// 清空旧的绑定关系
  //   List<CanvasElement> clearedCanvasElements = _templateData.clearExcelColumnBinding();
  //   List<CanvasElement> deleteCanvasElements = [];
  //   List<CanvasElement> updateCanvasElements = [];
  //   if (clearedCanvasElements.length > 0) {
  //     clearedCanvasElements.forEach((clearedCanvasElement) {
  //       clearedCanvasElement.resetImageCache();
  //       if (clearedCanvasElement.data is TableElement) {
  //         updateCanvasElements.add(clearedCanvasElement);
  //       } else {
  //         deleteCanvasElements.add(clearedCanvasElement);
  //       }
  //     });
  //     if (updateCanvasElements.length > 0) {
  //       /// 撤销、恢复
  //       StackManager().updateElements(clearedCanvasElements, context);
  //       StackManager().snapCanvasElements(_canvasElements);
  //     }
  //     if (deleteCanvasElements.length > 0) {
  //       _focusedElements.removeWhere((element) => deleteCanvasElements.contains(element));
  //       _canvasElements.removeWhere((element) => deleteCanvasElements.contains(element));
  //
  //       deleteCanvasElements.forEach((canvasElement) {
  //         /// 删除镜像
  //         if ((canvasElement.data.mirrorId ?? '').length > 0) {
  //           deleteCanvasElements.addAll(
  //               _canvasElements.where((element) => element.data.id == canvasElement.data.mirrorId)?.toList() ?? []);
  //           _canvasElements.removeWhere((element) => element.data.id == canvasElement.data.mirrorId);
  //
  //           canvasElement.data.isOpenMirror = 0;
  //           canvasElement.data.mirrorId = '';
  //         }
  //
  //         /// 撤销、恢复
  //         StackManager().removeElements(deleteCanvasElements, context);
  //         StackManager().snapCanvasElements(_canvasElements);
  //       });
  //     }
  //     if (updateCanvasElements.length > 0 || deleteCanvasElements.length > 0) {
  //       widget.onElementFocusChanged?.call(_focusedElements, 0);
  //       FloatingBarVisibleNotifier().forceUpdate();
  //     }
  //   }
  //
  //   bool result = _templateData.injectExcelData(excelData);
  //   if (!result) {
  //     _logger.log('inject excel data exception');
  //   }
  //
  //   if (excelData == null || isFromTableCell) {
  //     return;
  //   }
  //
  //   /// 创建元素
  //   bool focused = excelData.bindPairs.where((element) => element.open == true).length == 1;
  //   List<ExcelBindPair> bindPairs = excelData.bindPairs.where((element) => element.open == true).toList();
  //   if (excelData.displayHeader == true) {
  //     double x = 2.0;
  //     double y = 2.0;
  //     double elementWidth;
  //     double elementHeight;
  //     bindPairs.forEach((element) {
  //       elementWidth = _templateData.width - 4.0;
  //       if (element.bindElementType == ElementItemType.text) {
  //         if (elementWidth < 10.0) {
  //           elementWidth = 10.0;
  //         }
  //         elementHeight = 8.0;
  //         addCanvasElement(
  //             TextElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     width: elementWidth,
  //                     height: elementHeight,
  //                     x: x,
  //                     y: y,
  //                     fontSize: 3.2,
  //                     rotate: 0,
  //                     fontStyle: [],
  //                     textAlignHorizontal: 0,
  //                     letterSpacing: 0,
  //                     lineSpacing: 0,
  //                     value: "",
  //                     isTitle: excelData.displayHeader == true,
  //                     contentTitle: excelData.displayHeader == true ? excelData.headers[element.columnIndex] : null,
  //                     wordSpacing: 0,
  //                     lineMode: 2,
  //                     textAlignVertical: 1,
  //                     zIndex: 0,
  //                     fontFamily: 'ZT001',
  //                     fontCode: 'ZT001')
  //                 .toCanvasElement(),
  //             focused: focused);
  //         y += 4.0;
  //       } else if (element.bindElementType == ElementItemType.barcode) {
  //         if (elementWidth > 20.0) {
  //           elementWidth = 20.0;
  //         }
  //         elementHeight = 0.5 * elementWidth;
  //         addCanvasElement(
  //             BarCodeElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     width: elementWidth,
  //                     height: elementHeight,
  //                     x: x,
  //                     y: y,
  //                     rotate: 0,
  //                     fontSize: 3.2,
  //                     value: "123456",
  //                     textPosition: 0,
  //                     textHeight: 3.4,
  //                     codeType: element.codeType)
  //                 .toCanvasElement(),
  //             focused: focused);
  //         y = y + elementHeight + 2.0;
  //       } else if (element.bindElementType == ElementItemType.qrcode) {
  //         if (elementWidth > 10.0) {
  //           elementWidth = 10.0;
  //         }
  //         elementHeight = elementWidth;
  //         addCanvasElement(
  //             QrCodeElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     value: "123456",
  //                     width: elementWidth,
  //                     height: elementHeight,
  //                     x: x,
  //                     y: y,
  //                     rotate: 0,
  //                     correctLevel: 0,
  //                     codeType: element.codeType)
  //                 .toCanvasElement(),
  //             focused: focused);
  //         y = y + elementHeight + 2.0;
  //       }
  //     });
  //   } else {
  //     int index = 0;
  //     double x;
  //     double y = 2.0;
  //     double elementWidth;
  //     double elementHeight;
  //     double rowHeight = 0.0;
  //     bindPairs.forEach((element) {
  //       elementWidth = 0.5 * _templateData.width - 4.0;
  //       if (index % 2 == 0) {
  //         x = 2.0;
  //       } else {
  //         x = 0.5 * _templateData.width;
  //       }
  //       if (index % 2 == 0) {
  //         y += rowHeight;
  //         rowHeight = 0;
  //       }
  //       if (element.bindElementType == ElementItemType.text) {
  //         elementHeight = 8.0;
  //         addCanvasElement(
  //             TextElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     width: elementWidth,
  //                     height: elementHeight,
  //                     x: x,
  //                     y: y,
  //                     fontSize: 3.2,
  //                     rotate: 0,
  //                     fontStyle: [],
  //                     textAlignHorizontal: 0,
  //                     letterSpacing: 0,
  //                     lineSpacing: 0,
  //                     value: "",
  //                     contentTitle: excelData.displayHeader == true ? excelData.headers[element.columnIndex] : null,
  //                     wordSpacing: 0,
  //                     lineMode: 2,
  //                     textAlignVertical: 1,
  //                     zIndex: 0,
  //                     fontFamily: 'ZT001',
  //                     fontCode: 'ZT001')
  //                 .toCanvasElement(),
  //             focused: focused);
  //         if (rowHeight < elementHeight) {
  //           rowHeight = elementHeight;
  //         }
  //       } else if (element.bindElementType == ElementItemType.barcode) {
  //         if (elementWidth > 20.0) {
  //           elementWidth = 20.0;
  //         }
  //         elementHeight = 0.5 * elementWidth;
  //         addCanvasElement(
  //             BarCodeElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     width: elementWidth,
  //                     height: elementHeight,
  //                     x: x,
  //                     y: y,
  //                     rotate: 0,
  //                     fontSize: 3.2,
  //                     value: "123456",
  //                     textPosition: 0,
  //                     textHeight: 3.4,
  //                     codeType: element.codeType)
  //                 .toCanvasElement(),
  //             focused: focused);
  //         if (rowHeight < elementHeight + 2.0) {
  //           rowHeight = elementHeight + 2.0;
  //         }
  //       } else if (element.bindElementType == ElementItemType.qrcode) {
  //         if (elementWidth > 10.0) {
  //           elementWidth = 10.0;
  //         }
  //         elementHeight = elementWidth;
  //         addCanvasElement(
  //             QrCodeElement(
  //                     id: JsonElement.generateId(),
  //                     isBinding: 1,
  //                     bindingColumn: element.columnIndex,
  //                     value: "123456",
  //                     width: elementWidth,
  //                     height: elementWidth,
  //                     x: x,
  //                     y: y,
  //                     rotate: 0,
  //                     correctLevel: 0,
  //                     codeType: element.codeType)
  //                 .toCanvasElement(),
  //             focused: focused);
  //         if (rowHeight < elementHeight + 2.0) {
  //           rowHeight = elementHeight + 2.0;
  //         }
  //       }
  //       index++;
  //     });
  //   }
  // }

  /// 业务新建元素入口, 即业务自定义创建按钮进行的创建
  /// isSelectedFirst代表是否默认选中第一个元素, 默认选中最后一个
  void addElementFromBusiness(List<ElementCreateRequiredModel> elementCreateList,
      {bool ocrClick = false, bool isSelectedFirst = false}) {
    List<CanvasElement> canvasElements = [];
    double offsetY = 5.0;
    elementCreateList.forEach((e) {
      Rect? location = e.location;
      if ((location ?? Rect.zero) == Rect.zero) {
        location = Rect.fromLTWH(5.0, offsetY, 30.0, 10.0);
      }
      double x = location!.left;
      double y = location.top;
      if (!ocrClick) {
        Point point = _getAddElementPosition();
        x = point.x.toDouble();
        y = point.y.toDouble();
      }
      if (e.bindElementType == ElementItemType.text) {
        canvasElements.add(TextElement(
                id: JsonElement.generateId(),
                x: x,
                y: y,
                width: location.width,
                height: location.height,
                value: e.value ?? '',
                fontSize: 3.2,
                rotate: 0,
                fontStyle: [],
                textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
                letterSpacing: 0,
                lineSpacing: 0,
                wordSpacing: 0,
                lineMode: 2,
                lineBreakMode: 1,
                textAlignVertical: 0,
                // 6.0.4版本更换为顶对齐
                zIndex: 0,
                textDirection: CanvasHelper.currentTextDirection)
            .toCanvasElement());
        offsetY += (location.height - 5.0);
      } else if (e.bindElementType == ElementItemType.barcode) {
        canvasElements.add(BarCodeElement(
                id: JsonElement.generateId(),
                x: x,
                y: y,
                width: location.width,
                height: location.width / 2.0,
                value: e.value ?? '',
                rotate: 0,
                fontSize: 3.2,
                textPosition: 0,
                textHeight: 3.4,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width / 2.0 + 3.0);
      } else if (e.bindElementType == ElementItemType.qrcode) {
        canvasElements.add(QrCodeElement(
                id: JsonElement.generateId(),
                x: x,
                y: y,
                width: location.width,
                height: location.width,
                value: e.value ?? '',
                rotate: 0,
                correctLevel: 0,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width + 3.0);
      }
    });
    if (canvasElements.length > 0) {
      addCanvasElements(canvasElements, isSelectedFirst: isSelectedFirst, fromBusiness: true);
    }
  }

  List<CanvasElement> generateCanvasElements(List<ElementCreateRequiredModel> elementCreateList) {
    List<CanvasElement> canvasElements = [];
    double offsetY = 5.0;
    elementCreateList.forEach((e) {
      Rect location = e.location!;
      if ((location ?? Rect.zero) == Rect.zero) {
        location = Rect.fromLTWH(5.0, offsetY, 30.0, 10.0);
      }
      if (e.bindElementType == ElementItemType.text) {
        canvasElements.add(TextElement(
                id: JsonElement.generateId(),
                x: location.left,
                y: location.top,
                width: location.width,
                height: location.height,
                value: e.value ?? '',
                fontSize: 3.2,
                rotate: 0,
                fontStyle: [],
                textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
                letterSpacing: 0,
                lineSpacing: 0,
                wordSpacing: 0,
                lineMode: 2,
                lineBreakMode: 1,
                textAlignVertical: 0,
                // 6.0.4版本更换为顶对齐
                zIndex: 0,
                textDirection: CanvasHelper.currentTextDirection)
            .toCanvasElement());
        offsetY += (location.height - 5.0);
      } else if (e.bindElementType == ElementItemType.barcode) {
        canvasElements.add(BarCodeElement(
                id: JsonElement.generateId(),
                x: location.left,
                y: location.top,
                width: location.width,
                height: location.width / 2.0,
                value: e.value ?? '',
                rotate: 0,
                fontSize: 3.2,
                textPosition: 0,
                textHeight: 3.4,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width / 2.0 + 3.0);
      } else if (e.bindElementType == ElementItemType.qrcode) {
        canvasElements.add(QrCodeElement(
                id: JsonElement.generateId(),
                x: location.left,
                y: location.top,
                width: location.width,
                height: location.width,
                value: e.value ?? '',
                rotate: 0,
                correctLevel: 0,
                codeType: e.codeType)
            .toCanvasElement());
        offsetY += (location.width + 3.0);
      }
    });
    return canvasElements;
  }

  /// 创建高级二维码
  void onCreateAdvanceQRCodeElement(AdvanceQRCodeType codeType, Function() refreshCall) {
    // 非登录下不可用
    if (!CanvasUserCenter().isLogin) {
      String title = intlanguage('app00210', '当前未登录，请先登录！');
      String cancelDes = intlanguage('app00030', '取消');
      String confirmDes = intlanguage('app01191', '立即登录');
      showNimmbotDialog(context, title: title, cancelDes: cancelDes, confirmDes: confirmDes, confirmAction: () {
        CanvasUserCenter().handleLogin(context, () {
          showModalBottomSheet<LiveCodeItem>(
            context: context,
            barrierColor: Color(0x00000000).withOpacity(0.35),
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            isDismissible: false,
            enableDrag: false,
            isScrollControlled: true,
            builder: (context) => const LiveCodeListWidget(),
          ).then((selectedItem) {
            if (selectedItem != null) {
              // 处理返回的 item 数据
              Map<String, dynamic> advanceInfo = selectedItem.toJson();
              advanceInfo["isForm"] = 0;
              print("选择的二维码信息: ${selectedItem.title}, ${selectedItem.liveCodeName}");
              AdvanceQRCodeModel advanceQRCode = AdvanceQRCodeModel.fromJson(advanceInfo);
              AdvanceQRCodeManager().updataAdvanceQRCodeCache(advanceQRCode);
              addAdvanceQRCodeElement(advanceQRCode);
              CanvasPluginManager().advanceQRCodeImpl?.updateAdvanceQRCodeToNative(context, advanceInfo);
            }
          });
        });
      });
    } else {
      // 无网络不可用
      (Connectivity().customCheckConnectivity()).then((value) {
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
          return;
        } else {
          showModalBottomSheet<LiveCodeItem>(
            context: context,
            barrierColor: Color(0x00000000).withOpacity(0.35),
            backgroundColor: Colors.transparent,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            isDismissible: false,
            enableDrag: false,
            isScrollControlled: true,
            builder: (context) => const LiveCodeListWidget(),
          ).then((selectedItem) {
            if (selectedItem != null) {
              // 处理返回的 item 数据
              Map<String, dynamic> advanceInfo = selectedItem.toJson();
              advanceInfo["isForm"] = 0;
              print("选择的二维码信息: ${selectedItem.title}, ${selectedItem.liveCodeName}");
              AdvanceQRCodeModel advanceQRCode = AdvanceQRCodeModel.fromJson(advanceInfo);
              AdvanceQRCodeManager().updataAdvanceQRCodeCache(advanceQRCode);
              addAdvanceQRCodeElement(advanceQRCode);
              CanvasPluginManager().advanceQRCodeImpl?.updateAdvanceQRCodeToNative(context, advanceInfo);
            }
          });
        }
      });
    }
  }

  /// 创建form 表单
  void onCreateAdvanceQRCodeFormElement(AdvanceQRCodeType codeType, Function() refreshCall) {
    // 非登录下不可用
    if (!CanvasUserCenter().isLogin) {
      String title = intlanguage('app00210', '当前未登录，请先登录！');
      String cancelDes = intlanguage('app00030', '取消');
      String confirmDes = intlanguage('app01191', '立即登录');
      showNimmbotDialog(context, title: title, cancelDes: cancelDes, confirmDes: confirmDes, confirmAction: () {
        CanvasUserCenter().handleLogin(context, () {
          final advanceQRCodeImpl = CanvasPluginManager().advanceQRCodeImpl;
          advanceQRCodeImpl?.createAdvanceQRCode(context, codeType).then((value) {});
        });
      });
    } else {
      // 无网络不可用
      (Connectivity().customCheckConnectivity()).then((value) {
        if (value == ConnectivityResult.none || value == ConnectivityResult.bluetooth) {
          LoadingMix.showToast(intlanguage('app100000625', '当前网络状态异常'));
          return;
        } else {
          final advanceQRCodeImpl = CanvasPluginManager().advanceQRCodeImpl;
          advanceQRCodeImpl?.createAdvanceQRCode(context, codeType).then((value) {});
        }
      });
    }
  }

  /// 外部App打开的PDF文件，进行解析
  void onOpenImportPdfImageElement(ImportFileInfo fileInfo) async {
    Map<String, dynamic>? result = await canvasKey.currentState?.cropPdf(fileInfo, isPop: false);
    if (result != null) {
      PdfBindInfo pdfBindInfo = PdfBindInfo();
      pdfBindInfo.pdfName = result["pdfName"];
      pdfBindInfo.pdfMD5 = result["pdfMD5"];
      pdfBindInfo.pdfImagePaths = result["imagePaths"];
      PdfBindInfoManager.instance.addNewPDFBindInfo(pdfBindInfo);
      PdfBindInfoManager.instance.currentPageIndex = _templateData!.currentPageIndex;
      // 默认撑满标签纸
      _templateData!.resetImageCache();
      getThreshold(result['filePath'], '', result['width'], result['height'],
          isNeedFill: true, isPdf: true, isOutsidePdf: true, pdfBindInfo: pdfBindInfo);
    }
  }

  void onImportPdfImageElement(Function() refreshCall) async {
    //遍历查询 是否已有绑定Pdf的图片，一期只能插入一个图片
    for (var element in _templateData!.canvasElements) {
      JsonElement? imageElement = element.data;
      if (imageElement.type == "image" && PdfBindInfoManager.instance.elementIsBindPdf(imageElement.id)) {
        String title = intlanguage("app100001862", "当前仅支持选择一个PDF文件，请删除模板中的PDF内容后，再重新选择。");
        String rightFunStr = intlanguage("app00707", "我知道了");
        CanvasPluginManager()
            .nativeMethodImpl
            ?.sendTrackingToNative({"track": "show", "posCode": "108_395_359", "ext": {}});
        showCustomDialog(context, title, "",
            justSureButton: true,
            dismissOutSideTouch: false,
            rightFunStr: rightFunStr,
            rightFunCall: () {},
            sureButtonColor: Colors.black);
        return;
      }
    }
    Directory baseExcelFileDir = await getApplicationDocumentsDirectory();
    CanvasPluginManager().excelImportImpl!.getSupportSocialList().then((value) {
      List<String> socialList = [];
      value.forEach((element) {
        socialList.add(element);
      });
      showModalBottomSheet(
          barrierColor: Color(0xFF000000).withOpacity(0.35),
          context: context,
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
          enableDrag: false,
          builder: (_) {
            return FileImportTypePage(
              socialList,
              ImportFileType.pdf,
              fromExcel: true,
              selectFileCallback: (ImportFileInfo fileInfo) {
                canvasKey.currentState?.cropPdf(fileInfo);
              },
            );
          }).then((value) {
        if (value != null && value is Map) {
          PdfBindInfo pdfBindInfo = PdfBindInfo();
          pdfBindInfo.pdfName = value["pdfName"];
          pdfBindInfo.pdfMD5 = value["pdfMD5"];
          pdfBindInfo.pdfImagePaths = value["imagePaths"];
          PdfBindInfoManager.instance.addNewPDFBindInfo(pdfBindInfo);
          PdfBindInfoManager.instance.currentPageIndex = _templateData!.currentPageIndex;
          // 默认撑满标签纸
          _templateData!.resetImageCache();
          getThreshold(value['filePath'], '', value['width'], value['height'],
              isNeedFill: true, isPdf: true, pdfBindInfo: pdfBindInfo);
        }
      });
    });
  }

  void operateAdvanceQRCode(Map operateQRCodeInfo) {
    AdvanceQRCodeManager().updataAdvanceQRCodeCache(operateQRCodeInfo["data"]);
    if (operateQRCodeInfo["type"] == "select") {
      addAdvanceQRCodeElement(operateQRCodeInfo["data"]);
    } else {
      TemplateChangedNotifier().templateDataJsonData = _templateData?.generateCanvasJson();
    }
  }

  ///从高级二维码表单创建
  void addAdvanceQRCodeElement(AdvanceQRCodeModel advanceQRCode) {
    _logger.log("Directionality.of(context)： ${Directionality.of(context) == TextDirection.rtl}");
    final id = JsonElement.generateId();
    // List<CanvasElement> elements =
    //     _canvasElements.where((element) => element.elementType == ElementItemType.qrcode).toList();
    // double x = 1;
    // double y = 1;
    // if (elements != null && elements.length > 0) {
    //   x = (elements.length + 1) * 16.0.dp2mm();
    //   y = (elements.length + 1) * 16.0.dp2mm();
    // }
    Point point = _getAddElementPosition();
    double x = point.x.toDouble();
    double y = point.y.toDouble();
    bool isLive = false;
    String liveCodeId = "";
    bool isForm = false;
    String formId = "";
    if (advanceQRCode.isForm ?? false) {
      isForm = true;
      formId = advanceQRCode.id!;
    } else {
      isLive = true;
      liveCodeId = advanceQRCode.id!;
    }
    String qrcodeValue = advanceQRCode.shortUrl!;
    addCanvasElement(QrCodeElement(
            isLive: isLive,
            liveCodeId: liveCodeId,
            isForm: isForm,
            formId: formId,
            value: qrcodeValue,
            id: id,
            width: 10.0,
            height: 10.0,
            x: x,
            y: y,
            rotate: 0,
            correctLevel: 0,
            codeType: QrcodeType.QR_CODE)
        .toCanvasElement());
  }

  ///创建时间元素关联
  void createAssociateElement(BuildContext context, CanvasElement canvasElement) {
    DateElement jsonElement = canvasElement.data as DateElement;
    canvasElement.resetImageCache();
    double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;
    jsonElement.dateIsRefresh = 1;

    /// 画板中心点
    Offset templateCenter = Offset(templateWidth / 2.0, templateHeight / 2.0);
    DateElement associateElement = jsonElement.clone() as DateElement;
    if (associateElement.isOpenMirror == 1) {
      associateElement.mirrorId = JsonElement.generateId();
    }
    jsonElement.associateId = JsonElement.generateId();
    jsonElement.associated = true;
    associateElement.validityPeriod = 24;
    associateElement.validityPeriodNew = 24;
    associateElement.validityPeriodUnit = DateElementHelper.Associated_Hour;
    if (jsonElement.contentTitle?.isNotEmpty == true) {
      associateElement.contentTitle = intlanguage('app100001408', '保质期至');
    }
    associateElement.associateId = jsonElement.associateId;
    associateElement.timeFormat = jsonElement.timeFormat;
    if (jsonElement.typesettingMode == 2) {
      switch (jsonElement.rotate % 360) {
        case 0:
          associateElement.x = jsonElement.x - jsonElement.width - 1;
          break;
        case 90:
          associateElement.y = jsonElement.y + jsonElement.width + 1;
          break;
        case 180:
          associateElement.x = jsonElement.x + jsonElement.width + 1;
          break;
        case 270:
          associateElement.y = jsonElement.y + jsonElement.width + 1;
          break;
        default:
      }
    } else {
      switch (jsonElement.rotate % 360) {
        case 0:
          associateElement.y = jsonElement.y + jsonElement.height + 1;
          break;
        case 90:
          associateElement.x = jsonElement.x - jsonElement.height - 1;
          break;
        case 180:
          associateElement.y = jsonElement.y - jsonElement.height - 1;
          break;
        case 270:
          associateElement.x = jsonElement.x + jsonElement.height + 1;
          break;
        default:
      }
    }

    CanvasElement associateCanvasElement = associateElement.toCanvasElement();
    if (canvasElement.data.isOpenMirror == 1) {
      canvasElement.mirrorNeedRefresh = true;
    }
    addCanvasElement(
      associateCanvasElement,
      focused: false,
      stack: false,
    );
    StackManager().associateElements([canvasElement, getAssociateElement(canvasElement)!], context);
    StackManager().snapCanvasElements(_canvasElements);
    _focusedElements.clear();
    _focusedElements.add(canvasElement);
    widget.onElementFocusChanged?.call(_focusedElements, 0);
    _handleMirrorElementsRefresh([canvasElement], context);
    FloatingBarVisibleNotifier().forceUpdate();
    // List<CanvasElement> canvasElementChanged = [];
    // for (var element in _canvasElements) {
    //   JsonElement firstData = canvasElements.first.data;
    //   JsonElement elementData = element.data;
    //   if (elementData is DateElement &&
    //       firstData is DateElement &&
    //       elementData.associateId == firstData.associateId &&
    //       !elementData.associated &&
    //       firstData.associated) {
    //     canvasElementChanged = [element];
    //   }
    // }

    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(_focusedElements);
  }

  /// 工具栏基础元素新建入口
  void addElementBox(String identifier) {
    _logger.log("Directionality.of(context)： ${Directionality.of(context) == TextDirection.rtl}");
    final id = JsonElement.generateId();
    // List<CanvasElement> elements = _canvasElements.where((element) => element.elementType == identifier).toList();
    // double x = 1;
    // double y = 1;
    // if (elements != null && elements.length > 0) {
    //   x = (elements.length + 1) * 16.0.dp2mm();
    //   y = (elements.length + 1) * 16.0.dp2mm();
    // }
    Point point = _getAddElementPosition();
    double x = point.x.toDouble();
    double y = point.y.toDouble();
    if (ElementItemType.text == identifier) {
      addCanvasElement(TextElement(
              id: id,
              width: 24.0,
              height: 8.019293,
              x: x,
              y: y,
              fontSize: 3.2,
              rotate: 0,
              fontStyle: [],
              type: ElementItemType.text.toString(),
              textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
              letterSpacing: 0,
              lineSpacing: 0.0,
              value: intlanguage('app00364', '双击文本框编辑'),
              wordSpacing: 0,
              lineMode: 2,
              lineBreakMode: 1,
              textAlignVertical: 0,
              // 6.0.4版本更换为顶对齐
              zIndex: 0,
              boxStyle: TextElementBO.defaultBoxStyle(),
              textStyle: TextElementBO.defaultTextStyles(),
              textDirection: CanvasHelper.currentTextDirection)
          .toCanvasElement());
    } else if (ElementItemType.date == identifier) {
      addCanvasElement(DateElement(
              id: id,
              width: 36.819852,
              height: 8.019293,
              x: x,
              y: y,
              fontSize: 3.2,
              rotate: 0,
              fontStyle: [],
              typesettingMode: 1,
              textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
              letterSpacing: 0,
              lineSpacing: 0.0,
              value: DateFormat('yyyyMMddHHmmss').format(DateTime.now()),
              wordSpacing: 0,
              lineMode: 2,
              textAlignVertical: 0,
              // 6.0.4版本更换为顶对齐
              zIndex: 0,
              dateFormat: DateElementHelper.getDateOpen() ? DateElementHelper.getDateFormat() : "",
              timeFormat: DateElementHelper.getTimeOpen() ? DateElementHelper.getTimeFormat() : "",
              time: new DateTime.now().millisecondsSinceEpoch,
              contentTitle: DateElementHelper.getContentTitle(),
              isEditing: true,
              associated: false,
              associateId: '',
              timeUnit: "",

              /// 初次添加即为编辑模式
              boxStyle: TextElementBO.defaultBoxStyle(),
              textStyle: TextElementBO.defaultTextStyles(),
              dateIsRefresh: DateElementHelper.sp?.getInt(DateElementHelper.Recent_Instant_Time_Switch_Key) ?? 1)
          // DateElementHelper.sp.setBool(DateElementHelper.Recent_Instant_Time_Switch_Key, on);
          .toCanvasElement());
    } else if (ElementItemType.serial == identifier) {
      addCanvasElement(SerialElement(
              id: id,
              width: 13,
              height: 8.019293,
              x: x,
              y: y,
              fontSize: 3.2,
              rotate: 0,
              fontStyle: [],
              textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
              typesettingMode: 1,
              letterSpacing: 0,
              lineSpacing: 0.0,
              value: '1',
              wordSpacing: 0,
              lineMode: 2,
              textAlignVertical: 0,
              // 6.0.4版本更换为顶对齐
              zIndex: 0,
              prefix: '',
              startNumber: '01',
              suffix: '',
              incrementValue: 1,
              isEditing: true,

              /// 初次添加即为编辑模式
              boxStyle: TextElementBO.defaultBoxStyle(),
              textStyle: TextElementBO.defaultTextStyles(),
              fixLength: 2)
          .toCanvasElement());
    } else if (ElementItemType.qrcode == identifier) {
      addCanvasElement(QrCodeElement(
              value: "123456",
              id: id,
              width: 10.0,
              height: 10.0,
              x: x,
              y: y,
              rotate: 0,
              correctLevel: 0,
              codeType: QrcodeType.QR_CODE)
          .toCanvasElement());
    } else if (ElementItemType.barcode == identifier) {
      addCanvasElement(BarCodeElement(
              id: id,
              width: 20,
              height: 10,
              x: x,
              y: y,
              rotate: 0,
              fontSize: 3.2,
              value: "123456",
              textPosition: 0,
              textHeight: 3.4,
              codeType: BarcodeType.CODE128)
          .toCanvasElement());
    } else if (ElementItemType.image == identifier) {
      // if (_templateData != null) {
      //     String layoutSchema = _templateData!.layoutSchema;
      //     List<String> supportedEditors = _templateData!.supportedEditors;
      //     if (layoutSchema.isNotEmpty) {
      //       try {
      //         LayoutSchemaModel layoutSchemaModel = LayoutSchemaModel.fromJson(json.decode(layoutSchema));
      //       } catch (e) {
      //         print('版式解析出错');
      //       }
      //     }
      //   }
      pickImage();
      return;
    } else if (ElementItemType.material == identifier) {
      widget.onElementMaterialClicked.call();
      return;
    } else if (ElementItemType.border == identifier) {
      widget.onElementMaterialBoderClicked.call();
      return;
    } else if (ElementItemType.graph == identifier) {
      addCanvasElement(GraphElement(
          id: id,
          width: 10,
          height: 10,
          x: x,
          y: y,
          rotate: 0,
          lineWidth: 0.4,
          lineType: 1,
          graphType: 3,
          cornerRadius: 2,
          dashWidth: [1, 1]).toCanvasElement());
    } else if (ElementItemType.line == identifier) {
      addCanvasElement(LineElement(
          id: id,
          width: 20,
          height: 0.4,
          x: x,
          y: y,
          rotate: 0,
          lineWidth: 0.4,
          lineType: 1,
          dashWidth: [1, 1]).toCanvasElement());
    } else if (ElementItemType.table == identifier) {
      TableElement tableElement = TableElement(
          id: id,
          width: 21.2,
          height: 19.6,
          x: x,
          y: y,
          rotate: 0,
          lineWidth: 0.4,
          row: 3,
          column: 2,
          rowHeight: [5.0, 5.0, 5.0],
          columnWidth: [15.0, 15.0],
          cells: [],
          combineCells: []);
      tableElement
        ..cells = List.generate(
            6,
            (index) => TableCellElement(
                  id: JsonElement.generateId(),
                  rowIndex: index ~/ 2,
                  columnIndex: index % 2,
                  combineId: '',
                  width: 30.615756,
                  height: 8.019293,
                  x: 10,
                  y: 15,
                  fontSize: 3.2,
                  rotate: 0,
                  fontStyle: [],
                  textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
                  letterSpacing: 0,
                  lineSpacing: 0.0,
                  value: '',
                  wordSpacing: 0,
                  lineMode: 4,
                  lineBreakMode: 1,
                  textAlignVertical: 1,
                  zIndex: 0,
                )..tableElement = tableElement);

      addCanvasElement(tableElement.toCanvasElement());
    }
  }

  void addMaterial(MaterialItem materialItem, bool fromRecent, bool isBoder, bool isFromSearch) async {
    // 新建元素ID
    String? elementId = materialItem.isNinePatch ? JsonElement.generateId() : null;
    FilePackage.File? file = await _getMaterialCacheFile(materialItem, elementId: elementId);
    if (file == null) {
      return;
    }
    // 如果是点9图需要复制新的一份，作为点9图的本地数据，后续用于上传到云端
    FilePackage.File? ninePatchFile;
    if (materialItem.isNinePatch) {
      // 获取存储目录
      final ninePatchDir = Directory(file.dirname + '/ninePatch');
      if (!await ninePatchDir.exists()) {
        await ninePatchDir.create(recursive: true);
      }
      ninePatchFile = file.copySync(ninePatchDir.path +
          '/' +
          (_templateData?.id ?? '') +
          '_' +
          (elementId ?? '') +
          '_' +
          materialItem.id.toString() +
          '_' +
          CanvasTheme.langOf(context) +
          '.${file.basename.split('.').last}');
    }
    Uint8List bytesData = await file.readAsBytes();
    ui.Image image = await decodeImageFromList(bytesData);
    int width = image.width;
    int height = image.height;
    getThreshold(file.path, '', width, height,
        elementId: elementId,
        materialId: materialItem.id.toString(),
        isBoder: isBoder,
        isNinePatch: materialItem.isNinePatch,
        ninePatchUrl: ninePatchFile?.path);
    if (isFromSearch == true) {
      floatingBarVisible = false;
    }
    if (!fromRecent) {
      MaterialManager.sharedInstance().recordUseMaterial(materialItem, isBoder);
    }
  }

  void editMaterial(CanvasElement canvasElement, MaterialItem materialItem, bool fromRecent) async {
    ImageElement imageElement = canvasElement.data as ImageElement;
    if (imageElement.materialId == materialItem.id) {
      return;
    }
    FilePackage.File? file = await _getMaterialCacheFile(materialItem, elementId: imageElement.id);
    if (file == null) {
      return;
    }
    // 如果是点9图需要复制新的一份，作为点9图的本地数据，后续用于上传到云端
    FilePackage.File? ninePatchFile;
    if (materialItem.isNinePatch) {
      // 获取存储目录
      final ninePatchDir = Directory(file.dirname + '/ninePatch');
      if (!await ninePatchDir.exists()) {
        await ninePatchDir.create(recursive: true);
      }
      ninePatchFile = file.copySync(ninePatchDir.path +
          '/' +
          (_templateData?.id ?? '') +
          '_' +
          (canvasElement.elementId) +
          '_' +
          materialItem.id.toString() +
          '_' +
          CanvasTheme.langOf(context) +
          '.${file.basename.split('.').last}');
    }
    bool isBoder = imageElement.isMaterialBoder();
    Uint8List bytesData = await file.readAsBytes();
    ui.Image image = await decodeImageFromList(bytesData);
    int width = image.width;
    int height = image.height;
    imageElement.localUrl = file.path;
    imageElement.imageUrl = materialItem.image;
    imageElement.materialId = materialItem.id.toString();
    imageElement.isNinePatch = materialItem.isNinePatch;
    imageElement.ninePatchLocalUrl = ninePatchFile?.path;
    if (materialItem.isNinePatch) {
      // 重置图片后续保存可正常上传
      imageElement.imageUrl = '';
      imageElement.ninePatchUrl = materialItem.ninePatch?.previewUrl ?? '';
      // 重置反白, 点9不支持反白
      imageElement.colorReverse = 0;
    }
    //素材编辑维持原有的左边 根据素材元素适配高度
    if (isBoder == false) {
      imageElement.height = imageElement.width / width * height;
    } else {
      Rect rect;
      if (materialItem.isNinePatch) {
        double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
        double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;
        rect = Rect.fromLTWH(0, 0, templateWidth, templateHeight);
      } else {
        rect = getMaterialPositionInfo(width, height, isBoder: isBoder, materialId: materialItem.id.toString());
      }
      imageElement.height = rect.height;
      imageElement.width = rect.width;
      imageElement.x = rect.left;
      imageElement.y = rect.top;
    }
    //}
    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([canvasElement]);
    if (!fromRecent) {
      MaterialManager.sharedInstance().recordUseMaterial(materialItem, isBoder);
    }
    // Provider.of<MaterialRecentChangedNotifier>(context, listen: false).setDataChangedElements([]);
  }

  Future<FilePackage.File?> _getMaterialCacheFile(MaterialItem materialItem, {String? elementId}) async {
    ConnectivityResult connectivityResult = await Connectivity().customCheckConnectivity();
    FilePackage.File? file;
    bool isNetworkError = false;
    var materialCacheManager = await MaterialCacheManager.getInstance();
    // 区分image类型，当为点9图时，imageUrl应为点9图地址不是静态图地址
    String imageUrl = Uri.parse(materialItem.image).toString();
    if (materialItem.ninePatch?.identity ?? false) {
      imageUrl = materialItem.ninePatch?.previewUrl ?? imageUrl;
    }
    if (connectivityResult != ConnectivityResult.none &&
        connectivityResult != ConnectivityResult.bluetooth &&
        imageUrl.isURL) {
      FileInfo fileInfo = await materialCacheManager.downloadFile(imageUrl, key: materialItem.id.toString());
      file = fileInfo.file.copySync(fileInfo.file.dirname +
          '/' +
          (materialItem.isNinePatch
              ? ((elementId ?? materialItem.id.toString()) + '_' + materialItem.id.toString())
              : materialItem.id.toString()) +
          '_' +
          CanvasTheme.langOf(context) +
          '.${fileInfo.file.basename.split('.').last}');
    } else {
      isNetworkError = true;
      FileInfo? fileInfo = await materialCacheManager.getFileFromCache(materialItem.id.toString());
      // 避免破坏原有数据
      file = fileInfo?.file.copySync(fileInfo.file.dirname +
          '/' +
          (materialItem.isNinePatch
              ? ((elementId ?? materialItem.id.toString()) + '_' + materialItem.id.toString())
              : materialItem.id.toString()) +
          '_' +
          CanvasTheme.langOf(context) +
          '.${fileInfo.file.basename.split('.').last}');
    }
    bool exists = await file?.exists() ?? false;
    if (!exists) {
      LoadingMix.showToast(
          isNetworkError ? intlanguage('app100000625', '当前网络状态异常') : intlanguage("app100000815", "素材加载失败"));
      return null;
    }
    return file;
  }

  // Future<File> _getMaterialCacheFile(MaterialItem materialItem) async {
  //   ConnectivityResult connectivityResult = await Connectivity().customCheckConnectivity();
  //   FileInfo fileInfo;
  //   bool isNetworkError = false;
  //   if (connectivityResult != ConnectivityResult.none && connectivityResult != ConnectivityResult.bluetooth) {
  //     fileInfo = await DefaultCacheManager().downloadFile(materialItem.image);
  //   } else {
  //     isNetworkError = true;
  //     fileInfo = await DefaultCacheManager().getFileFromCache(materialItem.image);
  //   }
  //   if (fileInfo == null) {
  //     LoadingMix.showToast(isNetworkError ? LocalStringKey.networkError.tr() : LocalStringKey.materialLoadFail.tr());
  //     return null;
  //   }
  //   if (fileInfo.file == null) {
  //     LoadingMix.showToast(isNetworkError ? LocalStringKey.networkError.tr() : LocalStringKey.materialLoadFail.tr());
  //     return null;
  //   }
  //   bool exists = await fileInfo.file.exists();
  //   if (!exists) {
  //     LoadingMix.showToast(isNetworkError ? LocalStringKey.networkError.tr() : LocalStringKey.materialLoadFail.tr());
  //     return null;
  //   }
  //   return fileInfo.file;
  // }

  Point _getAddElementPosition() {
    double x;
    double y;
    List<JsonElement> jsonElements = (_canvasElements ?? [])
        .map((e) => e.data)
        .where((element) =>
            !element.isMirrorElement() &&
            (!(element is ImageElement) || (element.isMaterialIcon())) &&
            _isElementInCanvas(element))
        .toList();
    if (jsonElements.isNotEmpty) {
      x = 0;
      y = 0;
      jsonElements.forEach((jsonElement) {
        double tempX;
        double tempY;
        if (jsonElement.rotate == 90 || jsonElement.rotate == 270) {
          tempX = jsonElement.x.toDouble() + 0.5 * (jsonElement.width.toDouble() - jsonElement.height.toDouble());
          tempY = jsonElement.y.toDouble() + 0.5 * (jsonElement.height.toDouble() + jsonElement.width.toDouble());
        } else {
          tempX = jsonElement.x.toDouble();
          tempY = jsonElement.y.toDouble() + jsonElement.height.toDouble();
        }
        if (tempY > y) {
          x = tempX;
          y = tempY;
        }
      });
    } else {
      x = 16.0.dp2mm().toDouble();
      y = 16.0.dp2mm().toDouble();
    }
    return Point(x, y);
  }

  /// 元素是否整个包含在画板内
  bool _isElementInCanvas(JsonElement jsonElement) {
    double templateWidth = _templateData?.width?.toDouble() ?? 0;
    double templateHeight = _templateData?.height?.toDouble() ?? 0;
    double x = jsonElement.x.toDouble();
    double y = jsonElement.y.toDouble();
    double elementWidth = jsonElement.width.toDouble();
    double elementHeight = jsonElement.height.toDouble();
    if (jsonElement.rotate == 90 || jsonElement.rotate == 270) {
      x = x + 0.5 * (elementWidth - elementHeight);
      y = y + 0.5 * (elementHeight - elementWidth);
      double temp = elementWidth;
      elementWidth = elementHeight;
      elementHeight = temp;
    }
    return y >= 0 && y + elementHeight <= templateHeight;
  }

  void reset() {
    _nbCanvasTemplateKey.currentState?.animateResetInitialize();
    _nbCanvasTemplateKey.currentState?.handleBgPressed();
  }

  void cancelElementFocused() {
    _nbCanvasTemplateKey.currentState?.handleBgPressed();
  }

  void pickImage({List<CanvasElement>? canvasElements}) async {
    double bottomPadding = MediaQuery.of(context).padding.bottom;
    showModalBottomSheet(
        context: context,
        barrierColor: Color(0x00000000).withOpacity(0.35),
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        // isDismissible: false,
        enableDrag: false,
        isScrollControlled: true,
        builder: (context) => SafeArea(
              child: Container(
                height: 178 + (bottomPadding > 0 ? 0 : 15),
                color: Colors.transparent,
                padding: EdgeInsets.only(right: 12, left: 12),
                child: Column(
                  children: [
                    Container(
                      height: 110.5,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      child: Column(
                        children: [
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();

                              _pickFromPhotoAlbum(canvasElements: canvasElements);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                              height: 55,
                              child: Center(
                                  child: Text(
                                intlanguage("app100001879", '相册'),
                                style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: ui.FontWeight.w400),
                              )),
                            ),
                          ),
                          Container(
                            color: Color(0xFF3C3C43).withOpacity(0.09),
                            height: 0.5,
                          ),
                          //#
                          InkWell(
                            onTap: () {
                              Navigator.of(context).pop();
                              _takePhoto(canvasElements: canvasElements);
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(12), bottomRight: Radius.circular(12))),
                              height: 55,
                              child: Center(
                                  child: Text(
                                intlanguage("app00113", '拍照'),
                                style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: ui.FontWeight.w400),
                              )),
                            ),
                          ) //app00113
                        ],
                      ),
                    ),
                    InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        margin: EdgeInsets.only(top: 12),
                        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(12)),
                        height: 55,
                        child: Center(
                            child: Text(
                          intlanguage("app100000692", '取消'),
                          style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: ui.FontWeight.w400),
                        )),
                      ),
                    ),
                  ],
                ),
              ),
            ));
  }

  void _pickFromPhotoAlbum({List<CanvasElement>? canvasElements}) async {
    CanvasPluginManager().nativeMethodImpl!.sendTrackingToNative({"track": "click", "posCode": "108_396_361"});
    //图片
    if (Platform.isAndroid) {
      bool psState = await PermissionDialog.requestImage(context);
      if (psState) {
        final ImagePickerPlatform imagePickerImplementation = ImagePickerPlatform.instance;
        if (imagePickerImplementation is ImagePickerAndroid) {
          imagePickerImplementation.useAndroidPhotoPicker = true;
        }
        XFile? imageFile = await _pickImage();
        if (imageFile == null) {
          return;
        }
        _showCropWidget(imageFile.path, (result) {
          if (result != null && result is Map) {
            // 默认撑满标签纸

            Map areaMap = result['area'];
            getThreshold(result['filePath'], result['originFilePath'], result['width'], result['height'],
                isNeedFill: result['aspectRatio'],
                canvasElements: canvasElements,
                isFreedom: result['isFreedom'],
                areaModel: areaMap.isEmpty ? null : AreaModel.fromJson(result['area']));
          } else {
            _pickFromPhotoAlbum(canvasElements: canvasElements);
          }
        });
      }
    } else {
      /// 请求相册权限
      if (await PermissionUtils.checkPhotoAlbumGranted() != true) {
        // 展示alert
        showCupertinoAlert(context,
            title: intlanguage("app100000869", '照片权限未开启'),
            content: intlanguage("app01293", '请在“设置-精臣云打印-相册” ， 允许精臣云打印访问你的手机相册'),
            cancelDes: intlanguage("app100000866", '暂不'),
            confirmDes: intlanguage("app01308", '去设置'),
            cancelAction: () {}, confirmAction: () async {
          // 跳转设置页
          await PhotoManager.openSetting();
        });
        return;
      }

      XFile? imageFile = await _pickImage();
      if (imageFile == null) {
        return;
      }
      _showCropWidget(imageFile.path, (result) {
        if (result != null && result is Map) {
          Map areaMap = result['area'];
          getThreshold(result['filePath'], result['originFilePath'], result['width'], result['height'],
              isNeedFill: result['aspectRatio'],
              canvasElements: canvasElements,
              isFreedom: result['isFreedom'],
              areaModel: areaMap.isEmpty ? null : AreaModel.fromJson(result['area']));
        } else {
          _pickFromPhotoAlbum(canvasElements: canvasElements);
        }
      });
    }
  }

  Future<XFile?> _pickImage() async {
    final ImagePicker _picker = ImagePicker();
    // Pick an image
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.gallery, maxWidth: 2000, maxHeight: 2000);
      return image;
    } catch (e) {
      LoadingMix.showToast(intlanguage("app100001889", "暂不支持的图片格式"));
      return null;
    }
  }

  void _takePhoto({List<CanvasElement>? canvasElements}) async {
    CanvasPluginManager().nativeMethodImpl!.sendTrackingToNative({"track": "click", "posCode": "108_396_362"});
    getImage() async {
      showModalBottomSheet(
        context: context,
        barrierColor: Color(0x00000000).withOpacity(0.35),
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        isDismissible: false,
        enableDrag: false,
        isScrollControlled: true,
        builder: (context) => TakePhotosCameraPureWidget(),
      ).then((value) {
        if (value != null) {
          _showCropWidget(value.path, (result) {
            if (result != null && result is Map) {
              // 默认撑满标签纸
              Map areaMap = result['area'];
              getThreshold(result['filePath'], result['originFilePath'], result['width'], result['height'],
                  isNeedFill: result['aspectRatio'],
                  canvasElements: canvasElements,
                  isFreedom: result['isFreedom'],
                  areaModel: areaMap.isEmpty ? null : AreaModel.fromJson(result['area']));
            } else {
              _takePhoto();
            }
          });
        }
      });
    }

    late bool psState;
    // 首次会获取摄像头权限，判断摄像头权限
    if (Platform.isAndroid) {
      psState = await PermissionDialog.requestCamera(context);
      if (psState) {
        getImage();
      }
    } else {
      psState = await PermissionUtils.checkCameraGranted();
      if (psState) {
        getImage();
      } else {
        // 展示alert
        showCupertinoAlert(context,
            title: intlanguage("app100000865", '相机权限未开启'),
            content: intlanguage("app100000864", '无法使用相机，请前往“设置>精臣云打印>相机”中打开相机权限。'),
            cancelDes: intlanguage("app100000866", '暂不'),
            confirmDes: intlanguage("app01308", '去设置'),
            cancelAction: () {}, confirmAction: () async {
          // 跳转设置页
          await PhotoManager.openSetting();
        });
      }
    }
  }

  void getThreshold(String path, String originPath, int width, int height,
      {List<CanvasElement>? canvasElements,
      AreaModel? areaModel,
      bool isFreedom = true,
      String materialId = "",
      bool isNeedFill = false,
      bool isBoder = false,
      bool isNinePatch = false,
      String? ninePatchUrl,
      bool isPdf = false,
      bool isOutsidePdf = false,
      PdfBindInfo? pdfBindInfo,
      String? elementId}) async {
    Rect rect = Rect.fromLTWH(0.0, 0.0, 0.0, 0.0);
    double x1 = 0.0;
    double y1 = 0.0;
    if (areaModel != null) {
      if (isFreedom == false) {
        x1 = areaModel.x;
        y1 = areaModel.y;
        rect = Rect.fromLTWH(x1, y1, areaModel.width, areaModel.height);
      } else {
        rect = getMaterialPositionInfo(width, height, isNeedFill: isNeedFill, materialId: materialId, isBoder: isBoder);
        x1 = rect.left;
        y1 = rect.top;
      }
    } else {
      // 点9图默认铺满整个标签纸，无需计算合适的比例宽高
      if (isNinePatch) {
        double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
        double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;
        rect = Rect.fromLTWH(0, 0, templateWidth, templateHeight);
      } else {
        rect = getMaterialPositionInfo(width, height, isNeedFill: isNeedFill, materialId: materialId, isBoder: isBoder);
      }
      x1 = rect.left;
      y1 = rect.top;
      if (!isNeedFill && !isBoder) {
        Point point = _getAddElementPosition();
        x1 = point.x.toDouble();
        y1 = point.y.toDouble();
      }
    }

    int imageProcessingType = RfidUtil.isCurrentPaper16GrayColor(context) && materialId.isEmpty
        ? 2
        : materialId.isEmpty
            ? 3
            : 1;
    String _elementId = elementId ?? JsonElement.generateId();
    String materialType = isBoder ? "2" : "1";
    if (isPdf) {
      pdfBindInfo!.bindedElementId = _elementId;
      PdfBindInfoManager.instance.addNewPDFBindInfo(pdfBindInfo);
      if (pdfBindInfo.pdfImagePaths == null ||
          (pdfBindInfo.pdfImagePaths!.isNotEmpty &&
              _templateData!.currentPageIndex > pdfBindInfo.pdfImagePaths!.length - 1)) {
        path = "";
      } else {
        path = pdfBindInfo.pdfImagePaths![_templateData!.currentPageIndex];
      }
      imageProcessingType = 4;
    } else {
      //保存原图
      if (originPath.isNotEmpty) {
        // path 为本地裁剪图片保存地址，本地裁剪图片保存地址通过Uuid().v8()生成，此处将使用该地址保存原图，将原图和缩列图关联起来，编辑的时候可以通过localUrl来找到对应的原图
        saveImageToLocal(File(originPath), path);
      }
    }
    //如果canvasElements不为空，则清除缓存，修改图片localUrl

    if (canvasElements != null) {
      CanvasElement canvasElement = canvasElements.first;
      (canvasElement.data as ImageElement).localUrl = path;
      (canvasElement.data as ImageElement).isDefaultImage = false;
      (canvasElement.data as ImageElement).imageUrl = '';
      handleElementsUpdated(canvasElements); //镜像元素刷新
      canvasElements.forEach((element) {
        element.resetImageCache();
      });
      // canvasElement.resetImageCache();
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);

      // /// 撤销、恢复 把元素的变化放到撤销返回中
      StackManager().updateElements([canvasElement], context);
      StackManager().snapCanvasElements(_canvasElements);

      refreshImageStyle();
    } else {
      addCanvasElement(
          ImageElement(
                  id: _elementId,
                  width: rect.width,
                  height: rect.height,
                  x: x1,
                  y: y1,
                  rotate: 0,
                  //imageData: imageEncoded,
                  imageData: "",
                  imageProcessingType: imageProcessingType,
                  imageProcessingValue: imageProcessingType == 3 ? [5, 150] : [127],
                  localUrl: path,
                  isDefaultImage: false,
                  materialId: materialId,
                  allowFreeZoom: isBoder ? true : false,
                  materialType: materialType,
                  isNinePatch: isNinePatch,
                  ninePatchLocalUrl: ninePatchUrl)
              .toCanvasElement(),
          focused: !isPdf || !isOutsidePdf);
    }

    //floatingBarVisible = false;
  }

  Future<File?> saveImageToLocal(File sourceFile, String localPath) async {
    // 获取文档目录
    final directory = await getApplicationDocumentsDirectory();

    // 拼接目标目录路径
    final targetDirectoryPath = '${directory.path}/JCPrintCache/Image/OriginImage';

    // 创建目标目录（如果不存在）
    final targetDirectory = Directory(targetDirectoryPath);
    if (!await targetDirectory.exists()) {
      await targetDirectory.create(recursive: true);
    }
    // 拼接目标文件路径
    final targetFilePath = '$targetDirectoryPath/${path.basename(localPath)}';

    // 将源文件复制到目标目录
    File file = await sourceFile.copy(targetFilePath);
    return file;
  }

  Rect getMaterialPositionInfo(int width, int height,
      {bool isNeedFill = false, String materialId = "", bool isBoder = false}) {
    double templateWidth = CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble() ?? 0;
    double templateHeight = CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble() ?? 0;
    double elementWidth = width.px2mm().toDouble();
    double elementHeight = height.px2mm().toDouble();
    double originElementWidth = elementWidth;
    double x = 0;
    double y = 0;
    // ///添加素材时，默认大小和二维码一样
    if (materialId.isNotEmpty && isBoder == false) {
      elementWidth = 10.0;
      elementHeight = (10.0 * elementHeight) / originElementWidth;
    } else {
      if (elementWidth / elementHeight >= templateWidth / templateHeight) {
        ///图片宽高比大于等于画板宽高比，图片宽度与画板宽度进行比较
        //if (elementWidth > templateWidth || isNeedFill) {
        ///图片宽度大于画板宽度，图片按照画板宽度进行等比缩放
        elementWidth = templateWidth;
        elementHeight = elementWidth * height / width;
        // }else{

        // }
      } else {
        ///图片宽高比小于画板宽高比，图片高度与画板高度进行比较
        // if (elementHeight > templateHeight || isNeedFill) {
        ///图片高度大于画板高度，图片按照画板高度进行等比缩放
        elementHeight = templateHeight;
        elementWidth = elementHeight * width / height;
        // }
      }
    }
    x = max(0.5 * (templateWidth - elementWidth), 0);
    y = max(0.5 * (templateHeight - elementHeight), 0);
    Rect rect = Rect.fromLTWH(x, y, elementWidth, elementHeight);
    return rect;
  }

  ///从动态源批量添加元素到画板
  addCanvasElementsFromDynamicSource(List<CanvasElement> canvasElements, List<ExcelBindPair> bindPairs,
      {bool focus = false}) {
    _canvasElements?.addAll(canvasElements);

    ///从元素导入，导入多个元素后直接切到数据面板，导入单个元素则还是到样式
    ///换excel导入，直接切到数据面板
    _focusedElements.clear();
    if (bindPairs.length == 1 && focus == true && _canvasElements?.last != null) {
      _focusedElements.add(_canvasElements!.last);
      widget.onElementFocusChanged?.call(_focusedElements, 0);
    }

    // if (canvasElements.length > 1) {
    //   _handleMultiSelectChanged(true);
    //   _focusedElements.addAll(canvasElements);
    //   Future.delayed(Duration(milliseconds: 100), () {
    //     widget.onElementFocusChanged?.call(_focusedElements, 2);
    //     FloatingBarVisibleNotifier().forceUpdate();
    //   });
    //   return;
    // } else {
    //   _focusedElements.add(_canvasElements.last);
    //   widget.onElementFocusChanged?.call(_focusedElements, 0);
    // }
  }

  //刷新风格体验版图片显示
  refreshImageStyle() {
    widget.onElementFocusChanged?.call(_focusedElements, 0);
  }

  addCanvasElement(CanvasElement canvasElement, {bool focused = true, bool stack = true, bool fromBusiness = false}) {
    if (!(canvasElement.data is ImageElement)) {
      _logger.log("=============addCanvasElement: ${canvasElement.data.toJson()}");
    }

    /// 新建默认不显示工具条
    floatingBarVisible = false;
    addCanvasElements([canvasElement], focused: focused, stack: stack, fromBusiness: fromBusiness);
  }

  addCanvasElements(List<CanvasElement> canvasElements,
      {bool isSelectedFirst = false, bool focused = true, bool stack = true, bool fromBusiness = false}) {
    _canvasElements?.addAll(canvasElements);

    /// 撤销、恢复
    if (stack) {
      StackManager().addElements(canvasElements, context);
      StackManager().snapCanvasElements(_canvasElements);
    }

    ///导入多个元素后多选状态处理
    if (focused == false && stack == false && fromBusiness == true) {
      _handleMultiSelectChanged(true);
      _focusedElements.addAll(canvasElements);
      Future.delayed(Duration(milliseconds: 100), () {
        widget.onElementFocusChanged?.call(_focusedElements, 2);
        FloatingBarVisibleNotifier().forceUpdate();
      });
      return;
    }

    if (!_multiSelect) {
      _focusedElements.clear();
    }
    if (focused) {
      CanvasElement? element = isSelectedFirst ? canvasElements.first : _canvasElements?.last;
      if (element != null) {
        _focusedElements.add(element);
      }
    }

    widget.onElementFocusChanged?.call(_focusedElements, 0);
    if (_focusedElements.length == 1 && _focusedElements.first.data.type == ElementItemType.text /*&& !fromBusiness*/) {
      if (!fromBusiness) {
        widget.onElementValueEditorDisplay.call(_focusedElements.first, _focusedElements.first, isDoubleClick: false);
      }
      floatingBarVisible = false;
    } else {
      floatingBarVisible = true;
    }
  }

  /// 是否可以撤销
  bool canUndo() {
    return StackManager().canUndo();
  }

  /// 是否可以恢复
  bool canRedo() {
    return StackManager().canRedo();
  }

  /// 撤销操作
  bool undo() {
    bool result = StackManager().undo(_templateData, context, updatedCanvasElements: handleElementsUpdated2);
    if (result) {
      _focusedElements.removeWhere((element) => !(_canvasElements ?? []).contains(element));
      widget.onElementFocusChanged?.call(_focusedElements, 2);
      // if (!_multiSelect) {
      // FloatingBarVisibleNotifier().forceUpdate();
      // }

      /// 撤销、恢复
      StackManager().snapCanvasElements(_canvasElements);
    }
    return result;
  }

  /// 恢复操作
  bool redo() {
    bool result = StackManager().redo(_templateData, context, updatedCanvasElements: handleElementsUpdated2);
    if (result) {
      _focusedElements.removeWhere((element) => !(_canvasElements ?? []).contains(element));
      widget.onElementFocusChanged?.call(_focusedElements, 2);
      // if (!_multiSelect) {
      // FloatingBarVisibleNotifier().forceUpdate();
      // }

      /// 撤销、恢复
      StackManager().snapCanvasElements(_canvasElements);
    }
    return result;
  }

  /// 处理元素属性更新
  handleElementsUpdated(List<CanvasElement>? elements) {
    _logger.log("=====================handleElementsUpdated--刷新镜像元素");

    /// 重置数据变动 element 的图像缓存
    elements?.forEach((element) {
      refreshAssociateElement(element);
      element.resetImageCache();
    });

    /// 检测镜像是否需要刷新
    _handleMirrorElementsRefresh(elements, context);
  }

  /// 处理元素属性更新
  handleElementsUpdated2(List<CanvasElement>? elements) {
    _logger.log("=====================handleElementsUpdated2--刷新镜像元素");

    /// 重置数据变动 element 的图像缓存
    elements?.forEach((element) {
      element.resetImageCache();
    });

    /// 检测镜像是否需要刷新
    _handleMirrorElementsRefresh(elements, context);

    if (elements != null && elements.length == 1) {
      _focusedElements.clear();
      _focusedElements.addAll(elements);
    }
  }

  /// 输入焦点切换
  switchCursorFocus(CanvasElement canvasElement) {
    _handleBoxDoubleTap(canvasElement);
  }

  /// 图片导入增加裁剪流程
  _showCropWidget(String photoPath, Function callback) {
    showModalBottomSheet(
      context: context,
      barrierColor: Color(0x00000000).withOpacity(0.35),
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      builder: (context) {
        // AreaModel? areaModel = getTemplateAreaModel();
        // double? aspectRatio = areaModel != null ? areaModel.width / areaModel.height : null;
        AreaModel? areaModel;
        double? aspectRatio;
        if (_focusedElements.isNotEmpty) {
          aspectRatio = _focusedElements.first.data.width / _focusedElements.first.data.height;
        }
        return PhotoCropWidget(
          photoFilePath: photoPath,
          aspectRatio: aspectRatio,
          areaModel: areaModel,
        );
      },
    ).then((result) {
      callback.call(result);
    });
  }

  //进入裁剪面板前面，判断裁剪区域
  AreaModel? getTemplateAreaModel() {
    AreaModel? areaModel;
    if (_templateData != null) {
      String layoutSchema = _templateData!.layoutSchema;
      List<String> supportedEditors = _templateData!.supportedEditors;
      if (supportedEditors.contains('photo') && layoutSchema.isNotEmpty) {
        //是相框，并且存在版式
        try {
          LayoutSchemaModel layoutSchemaModel = LayoutSchemaModel.fromJson(json.decode(layoutSchema));
          List<AreaModel> areas = layoutSchemaModel.areas;
          if (areas.isNotEmpty) {
            areaModel = areas.first;
          }
        } catch (e) {
          return null;
        }
      }
    }
    return areaModel;
  }

  Future<Map<String, dynamic>?> cropPdf(ImportFileInfo fileInfo, {bool isPop = true}) async {
    List<String> imageFilePaths = await getImageFilePaths(fileInfo);
    if (imageFilePaths.isEmpty) {
      return null;
    }
    return await showModalBottomSheet(
      context: context,
      barrierColor: Color(0x00000000).withOpacity(0.35),
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      isDismissible: false,
      enableDrag: false,
      isScrollControlled: true,
      builder: (context) {
        return PhotoCropPDFWidget(
          photoFilePath: imageFilePaths[0],
          photoPaths: imageFilePaths,
        );
      },
    ).then((result) {
      if (result != null) {
        Map<String, dynamic> resultMap = Map<String, dynamic>.from(result);
        if (isPop) {
          Navigator.of(context).pop(resultMap);
        }
        return resultMap;
      }
    });
  }

  Future<List<String>> getImageFilePaths(ImportFileInfo fileInfo) async {
    PdfBindInfo pdfBindInfo = PdfBindInfo();
    pdfBindInfo.pdfName = fileInfo.fileName;
    LoadingMix.showLoading();
    final pdfImageDir;
    final pdfFileDir;
    // 获取应用的临时目录
    final tempDir = await getTemporaryDirectory();
    // 构建 pdf_image 文件夹的路径
    final pdfImageDirPath = '${tempDir.path}/pdf_image';
    var pdfFileDirPath = '${tempDir.path}/pdfFile';
    // 创建 Directory 对象
    pdfImageDir = Directory(pdfImageDirPath);
    pdfFileDir = Directory(pdfFileDirPath);
    // 检查文件夹是否存在
    if (!await pdfImageDir.exists()) {
      await pdfImageDir.create();
    }
    if (!await pdfFileDir.exists()) {
      await pdfFileDir.create();
    }
    String fileName = pdfBindInfo.pdfName?.replaceAll(".pdf", "") ?? "";
    // 设置 PDF 文件路径
    if (fileInfo.filePath.isEmpty) {
      PdfBindInfoManager.instance.PdfPath = pdfFileDirPath + "/${fileName}.pdf";
      final file = File(PdfBindInfoManager.instance.PdfPath);
      await file.writeAsBytes(fileInfo.fileData, flush: true);
    } else {
      PdfBindInfoManager.instance.PdfPath = fileInfo.filePath;
    }
    PdfBindInfoManager.instance.PdfPagePath = pdfImageDirPath + "/";
    final List<String> imageFilePaths = [];
    try {
      final document = await PdfDocument.openFile(PdfBindInfoManager.instance.PdfPath);
      PdfBindInfoManager.instance.PdfPageCount = document.pagesCount;
      PdfBindInfoManager.instance.PdfName = pdfBindInfo.pdfName ?? "";
      final page = await document.getPage(1);

      // PdfPageImage? pdfPerImage =
      //     await page.render(width: page.width, height: page.height, format: PdfPageImageFormat.jpeg);
      PdfPageImage? pdfPerImage =
          await PdfBindInfoManager.instance.handleChoosePdfSize(page, templateData: _templateData);
      var pdfPath = '${pdfImageDirPath}/${fileName}_0.jpg';
      await File(pdfPath).writeAsBytes(pdfPerImage!.bytes);
      imageFilePaths.add(pdfPath);
      await page.close();
      await document.close();

      LoadingMix.dismissLoading();
    } catch (e) {
      LoadingMix.showToast(intlanguage("app100001872", "pdf文件打开失败"));
      LoadingMix.dismissLoading();
    }
    return imageFilePaths;
  }
}
