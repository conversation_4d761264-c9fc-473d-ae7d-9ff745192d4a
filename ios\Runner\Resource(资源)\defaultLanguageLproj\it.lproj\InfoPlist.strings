/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Questo richiederà l'accesso alla fotocamera per funzioni come la scansione di codici a barre, il riconoscimento di testo e la fotografia, vuoi attivarlo?";
NSBluetoothPeripheralUsageDescription = "Questo attiverà il Bluetooth quando si collega la stampante, vuoi attivarlo?";
NSBluetoothAlwaysUsageDescription = "Questo attiverà il Bluetooth quando si collega la stampante, vuoi attivarlo?";
NSContactsUsageDescription = "Questo accederà alla tua rubrica durante la lettura dei contatti, vuoi attivarlo?";
NSMicrophoneUsageDescription = "Questo aprirà il microfono quando riconoscerà la voce, vuoi attivarlo?";
NSPhotoLibraryUsageDescription = "Questa autorizzazione serve per stampare immagini, riconoscere codici a barre/QR, testo e impostare immagini personalizzate. Consenti l'accesso a tutte le foto per garantire che l'album sia accessibile in NIIMBOT. Se selezioni “Seleziona foto...”, tutte le foto non selezionate e quelle aggiunte in futuro non saranno accessibili in NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Per facilitare l'utilizzo della rete Wi-Fi nelle vicinanze, NIIMBOT richiede l'autorizzazione alla localizzazione.";
NSLocationAlwaysUsageDescription = "Per facilitare l'utilizzo della rete Wi-Fi nelle vicinanze, NIIMBOT richiede l'autorizzazione alla localizzazione.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Per facilitare l'utilizzo della rete Wi-Fi nelle vicinanze, NIIMBOT richiede l'autorizzazione alla localizzazione.";
NSSpeechRecognitionUsageDescription = "Questo richiede il tuo consenso per accedere al riconoscimento vocale, vuoi attivarlo?";
NSLocalNetworkUsageDescription = "Questa app necessita l'accesso alla ​Rete locale (LAN)​​ per i servizi di ricerca dispositivi LAN e configurazione rete.";
"UILaunchStoryboardName" = "LaunchScreen";
