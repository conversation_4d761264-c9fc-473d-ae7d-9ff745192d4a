# Uncomment the next line to define a global platform for your project
source '****************:print/foundation/niimbot_specs.git'
platform :ios, '12.0'
# Comment the next line if you don't want to use dynamic frameworks
use_frameworks!
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

# Capacitor 小程序框架原生能力支持插件
# [https://capacitorjs.com/docs/plugins]
def capacitor_pods
  pod 'Capacitor', :path => '../@capacitor/ios'
  pod 'CapacitorCordova', :path => '../@capacitor/ios'
  pod 'CapacitorKeyboard', :path => '../@capacitor/keyboard'
  pod 'CapacitorDevice', :path => '../@capacitor/device'
  pod 'CapacitorApp', :path => '../@capacitor/app'
  pod 'CapacitorActionSheet', :path => '../@capacitor/action-sheet'
  pod 'CapacitorAppLauncher', :path => '../@capacitor/app-launcher'
  pod 'CapacitorBrowser', :path => '../@capacitor/browser'
  pod 'CapacitorCamera', :path => '../@capacitor/camera'
  pod 'CapacitorClipboard', :path => '../@capacitor/clipboard'
  pod 'CapacitorDialog', :path => '../@capacitor/dialog'
  pod 'CapacitorFilesystem', :path => '../@capacitor/filesystem'
  pod 'CapacitorGeolocation', :path => '../@capacitor/geolocation'
  #  pod 'CapacitorGoogleMaps', :path => '../@capacitor/google-maps'
  pod 'CapacitorHaptics', :path => '../@capacitor/haptics'
  pod 'CapacitorLocalNotifications', :path => '../@capacitor/local-notifications'
  pod 'CapacitorNetwork', :path => '../@capacitor/network'
  # pod 'CapacitorPreferences', :path => '../@capacitor/preferences'
  pod 'CapacitorPushNotifications', :path => '../@capacitor/push-notifications'
  pod 'CapacitorScreenReader', :path => '../@capacitor/screen-reader'
  pod 'CapacitorShare', :path => '../@capacitor/share'
  pod 'CapacitorSplashScreen', :path => '../@capacitor/splash-screen'
  pod 'CapacitorStatusBar', :path => '../@capacitor/status-bar'
  pod 'CapacitorTextZoom', :path => '../@capacitor/text-zoom'
  pod 'CapacitorToast', :path => '../@capacitor/toast'

  #  插件查询
  #  https://www.npmjs.com/package/@capacitor/local-notifications

  #  v3 版本插件版本备注
  #  npm install @capacitor/text-zoom@1.0.8
  #  npm install @capacitor/status-bar@1.0.8
  #  npm install @capacitor/splash-screen@1.2.2
  #  npm install @capacitor/share@1.1.2
  #  npm install @capacitor/screen-reader@1.0.8
  #  npm install @capacitor/push-notifications@1.0.9
  #  npm install @capacitor/network@1.0.7
  #  npm install @capacitor/motion@1.0.5
  #  npm install @capacitor/local-notifications@1.1.0
  #  npm install @capacitor/keyboard@1.2.3
  #  npm install @capacitor/http@0.0.3-0
  #  npm install @capacitor/haptics@1.1.4
  #  npm install @capacitor/geolocation@1.3.1
  #  npm install @capacitor/filesystem@1.1.0
  #  npm install @capacitor/dialog@1.0.7
  #  npm install @capacitor/device@1.1.2
  #  npm install @capacitor/clipboard@1.0.8
  #  npm install @capacitor/camera@1.3.1
  #  npm install @capacitor/app@1.1.1
  #  npm install @capacitor/app-launcher@1.0.9
  #  npm install @capacitor/action-sheet@1.0.8

end

target 'Runner' do
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  # 消除pod库中的警告
  inhibit_all_warnings!
  source 'https://git.jc-ai.cn/architect/ios/houndanalyticssdkpodspecs'
  source 'https://git.jc-ai.cn/architect/ios/onekeyloginpodspecs'
  # source 'https://github.com/CocoaPods/Specs.git'
  source 'https://git.jc-ai.cn/architect/ios/jcpodspec.git'
  source 'https://cdn.cocoapods.org/'

  # Capacitor 小程序框架原生能力支持插件
  capacitor_pods
#  pod 'Sentry'
  pod 'HoundAnalyticsSDK', '~> 0.1.8'
  pod 'YYWebImage'
  pod 'ZXCountDownView','~> 1.0.6'
  pod 'OneKeyLogin','~> 1.1.14'
  pod 'TABAnimated', '2.5.1'
  pod 'Masonry'
  pod 'SDWebImage', '~> 5.12.6'
  pod 'MyLayout'
  pod 'IQKeyboardManager', '6.5.12'
  pod 'GCDObjC'
  pod 'ReactiveCocoa', '~> 2.5'
  pod 'TMCache'
  pod 'AliyunOSSiOS', '~> 2.10.14'
  pod 'AliyunLogProducer', '3.1.11'
  pod 'YYModel'
  pod 'JSONModel'
  pod 'FMDB'
  pod 'YTKNetwork', '~> 3.0.6'
  pod 'MBProgressHUD'
  pod  'YKWoodpecker'
  pod 'Bugly', '~> 2.6.0'
  # 控制器切换
  pod 'HMSegmentedControl', '~> 1.5.5'
  pod 'MJRefresh'
  # 动效
  pod 'pop'
  # 数字小红点
  pod 'RKNotificationHub'
  # 照片选择器
  #  pod 'ZLPhotoBrowser'
  pod 'ZLPhotoBrowser', :git => 'https://git.jc-ai.cn/architect/ios/ZLPhotoBrowser.git'
  # sentry.io异常收集
  #pod 'Sentry', :git => 'https://git.jc-ai.cn/architect/ios/sentry-cocoa.git', :tag => '8.4.0'
  #pod 'Sentry/HybridSDK', :git => 'https://git.jc-ai.cn/architect/ios/sentry-cocoa.git', :tag => '8.4.0'
  #pod 'Sentry', '~> 8.4.0'
  pod 'SRCountdownTimer', :git => 'https://git.jc-ai.cn/architect/ios/countdowntimer.git'
  pod 'JailBrokenDetection', :git=> '****************:architect/ios/JailBrokenDetection.git', :branch=>'feature/ios_app_size'

  # 二维码
  pod 'LBXScan', '~> 2.5.1'
  pod 'LBXZBarSDK', '~> 1.3.5'
  # swift框架
  # pod 'PopupDialog', '~> 0.6'
  pod 'PGDatePicker', '>= 1.4.2'
  # pod 'LookinServer'
  pod 'SSZipArchive'
  # Semver 语义化版本对比
  pod 'Version'
  # Lookin
  pod 'LookinServer'
  # Lottie动画
  pod 'lottie-ios'

  #设备信息: 出新设备之后要及时更新
#  pod 'GBDeviceInfo'
  # 打印SDK
  pod 'JCAPI-iOS', '4.0.1-beta.6'
  # 图像库
  pod 'SkiaRenderLibrary-iOS', '1.9.3-dev.7'
  # 电子标签
  pod 'ETag-iOS', '1.0.0-beta.6'
  pod 'lego-iOS' , '3.0.0-rc.1'
  pod 'JCore', '4.8.0'
  pod 'JPush', '5.4.0'

end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'armv7 armv7s'
      config.build_settings['EXCLUDED_ARCHS[sdk=iphoneos*]'] = 'armv7 armv7s'
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      # fix Xcode 14 error: Signing for "xxx-xxx" requires a development team. Select a development team in the Signing & Capabilities editor.`
      config.build_settings['EXPANDED_CODE_SIGN_IDENTITY'] = ""
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
#      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      # flutter-permission-handler权限申请说明
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
      '$(inherited)',

      ## dart: PermissionGroup.calendar
      # 'PERMISSION_EVENTS=1',

      ## dart: PermissionGroup.reminders
      # 'PERMISSION_REMINDERS=1',

      ## dart: PermissionGroup.contacts
      # 'PERMISSION_CONTACTS=1',

      ## dart: PermissionGroup.camera
       'PERMISSION_CAMERA=1',

      ## dart: PermissionGroup.microphone
      # 'PERMISSION_MICROPHONE=1',

      ## dart: PermissionGroup.speech
      # 'PERMISSION_SPEECH_RECOGNIZER=1',

      ## dart: PermissionGroup.photos
       'PERMISSION_PHOTOS=1',

      ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
      # 'PERMISSION_LOCATION=1',

      ## dart: PermissionGroup.notification
       'PERMISSION_NOTIFICATIONS=1',

      ## dart: PermissionGroup.mediaLibrary
      # 'PERMISSION_MEDIA_LIBRARY=1',

      ## dart: PermissionGroup.sensors
      # 'PERMISSION_SENSORS=1',

      ## dart: PermissionGroup.bluetooth
      # 'PERMISSION_BLUETOOTH=1',

      ## dart: PermissionGroup.appTrackingTransparency
      # 'PERMISSION_APP_TRACKING_TRANSPARENCY=1',

      ## dart: PermissionGroup.criticalAlerts
      # 'PERMISSION_CRITICAL_ALERTS=1'
      ]

    end
  end
end

post_integrate do |installer|
  installer.pods_project.targets.each do |target|
     flutter_additional_ios_build_settings(target)
      target.build_configurations.each do |config|
        xcconfig_path = config.base_configuration_reference.real_path
        xcconfig = File.read(xcconfig_path)
        xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
        File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
      end
  end
  compiler_flags_key = 'COMPILER_FLAGS'
  project_path = 'Pods/Pods.xcodeproj'

  project = Xcodeproj::Project.open(project_path)
  project.targets.each do |target|
    target.build_phases.each do |build_phase|
      if build_phase.is_a?(Xcodeproj::Project::Object::PBXSourcesBuildPhase)
        build_phase.files.each do |file|
          if !file.settings.nil? && file.settings.key?(compiler_flags_key)
            compiler_flags = file.settings[compiler_flags_key]
            file.settings[compiler_flags_key] = compiler_flags.gsub(/-DOS_OBJECT_USE_OBJC=0\s*/, '')
          end
        end
      end
    end
  end
  project.save()
end

pre_install do |installer|
  ios_root=File.dirname(__FILE__)
  cmd="#{ios_root}/../gitHook/install.sh"
  puts("Pre_install: #{cmd}")
  system(cmd)
end
