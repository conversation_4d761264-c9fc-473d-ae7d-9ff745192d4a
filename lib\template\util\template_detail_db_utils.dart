import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:isar/isar.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/template/label_name_info.dart';
import 'package:niimbot_template/models/template/template_data_source.dart';
import 'package:niimbot_template/models/template/template_data_source_info.dart';
import 'package:niimbot_template/models/template/template_profile.dart';
import 'package:niimbot_template/models/template/template_profile_extra.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/template/template_input_area.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:niimbot_template/utils/template_parse_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/database/template/template_detail_model.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/template/constant/template_class.dart';
import 'package:text/template/constant/template_type.dart';
import 'package:text/template/model/template_list_model.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import '../../database/folder/folder_db_utils.dart';
import '../../pages/canvas/niimbot_canvas_page.dart';
import '../../pages/my_template/folder_template_sort_helper.dart';
import '../../pages/my_template/widget/sort_menu_dialog.dart';
import '../../utils/event_bus.dart';

class TemplateDetailDBUtils {
  ///已同步
  static const int SYNC = -1;

  ///默认状态
  static const int DEFAULT = 0;

  ///本地创建,未同步
  static const int CREATE = 1;

  ///本地更新，未同步
  static const int UPDATE = 2;

  ///本地删除，未同步
  static const int DELETE = 3;

  ///创建模板上传服务端后同步服务端返回的templateId
  static Future<TemplateData> createSyncServiceTemplateId(TemplateData templateData, String serviceTemplateId) async {
    TemplateData? template = await queryTemplateById(templateData.id!);
    TemplateData saveTemplate;
    //离线同步上传时 ，模版修改的情况
    if (template != null && template.profile.extra.createTime != templateData.profile.extra.createTime) {
      saveTemplate = template.copyWith(id: serviceTemplateId, local_type: UPDATE);
      saveTemplate.profile.extra.createTime = templateData.profile.extra.createTime;
    } else {
      saveTemplate = templateData.copyWith(id: serviceTemplateId, local_type: SYNC);
    }
    debugPrint("模板保存更新同步: ${templateData.id} 删除库中原始模版");
    await deleteTemplate(templateData);
    debugPrint("模板保存更新同步: ${templateData.id} 插入新模版");
    return await insertTemplate(saveTemplate);
  }

  ///迁移原生表中的模版
  static Future<void> migrationTemplateData(TemplateData templateData) async {
    TemplateData? template = await queryTemplateById(templateData.id!);
    //新表中已经存在则不处理
    if (template != null) {
      return;
    }
    NiimBotTemplateCanvasDataModel templateModel = await templateModelToIsarModel(templateData);
    debugPrint('迁移:模板缩略图地址 id: ${templateModel.templateId!} 地址: ${templateModel.localThumbnail}');
    NiimbotLogTool.writeLogToFile(
        {"MigrationManager": '迁移:模板缩略图地址 id: ${templateModel.templateId!} 地址: ${templateModel.localThumbnail}'});
    final isar = DBManagerUtil.instance.isar;
    await isar.niimBotTemplateCanvasDataModels.put(templateModel);
  }

  ///删除模板
  static Future<bool> deleteTemplate(TemplateData templateData) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateData.id).deleteAll();
    });
    return true;
  }

  ///删除行业模板
  static Future<void> deleteIndustryTemplate() async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.filter().templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD).deleteAll();
    });
  }

  ///批量删除模板
  static Future<bool> batchDeleteTemplate(List<TemplateData> templates) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await Future.forEach(templates, (templateData) async {
        await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateData.id).deleteAll();
      });
    });
    return true;
  }

  ///根据模版ids批量删除模板
  static Future<int> batchDeleteTemplateByIds(List<num> templatesIds) async {
    int deletedCount = 0;
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await Future.forEach(templatesIds, (templateId) async {
        int count =
            await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateId.toString()).deleteAll();
        deletedCount += count;
      });
    });
    return deletedCount;
  }

  ///删除模板
  static Future<bool> deleteTemplateById(String templateId) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateId).deleteAll();
    });
    return true;
  }

  /// 删除指定 folderId 下的所有模板
  static Future<bool> deleteTemplatesByFolderId(int folderId) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.filter().folderIdEqualTo(folderId.toString()).deleteAll();
    });
    return true;
  }

  ///插入模板
  static Future<TemplateData> insertTemplate(TemplateData templateData) async {
    NiimBotTemplateCanvasDataModel templateModel = await templateModelToIsarModel(templateData);
    NiimBotTemplateCanvasDataModel? templateModelLocal = await queryTemplateModelById(templateData.id!);
    if (templateModelLocal != null) {
      templateModel.lastPrintedAt = templateModelLocal.lastPrintedAt;
    }
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(templateModel);
    });
    return templateData;
  }

  static Future<TemplateData> insertOrUpdateTemplate(TemplateData templateData) async {
    return await insertTemplate(templateData);
  }

  ///批量插入模板
  static Future<void> batchInsertTemplate(List<TemplateData> templates) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await Future.forEach(templates, (templateData) async {
        NiimBotTemplateCanvasDataModel templateModel = await templateModelToIsarModel(templateData);
        NiimBotTemplateCanvasDataModel? templateModelLocal = await queryTemplateModelById(templateData.id!);
        if (templateModelLocal != null) {
          templateModel.lastPrintedAt = templateModelLocal.lastPrintedAt;
        }
        await isar.niimBotTemplateCanvasDataModels.put(templateModel);
      });
    });
  }

  ///更新模版缩略图
  static Future<void> updateTemplateLocalThumb(String templateId, String localThumbPath) async {
    final isar = DBManagerUtil.instance.isar;

    // 先查询模板是否存在
    TemplateData? templateData = await queryTemplateById(templateId);
    if (templateData == null) {
      return;
    }

    // 更新缩略图路径
    TemplateData modifyTemplate = templateData.copyWith(localThumbnail: localThumbPath);

    // 保存到数据库
    await isar.writeTxn(() async {
      NiimBotTemplateCanvasDataModel templateModel = await templateModelToIsarModel(modifyTemplate);
      NiimBotTemplateCanvasDataModel? templateModelLocal = await queryTemplateModelById(templateData.id!);
      if (templateModelLocal != null) {
        templateModel.lastPrintedAt = templateModelLocal.lastPrintedAt;
      }
      await isar.niimBotTemplateCanvasDataModels.put(templateModel);
    });
  }

  ///更新模版打印时间
  static Future<void> updateTemplatePrintTime(String printTime, String templateId) async {
    final isar = DBManagerUtil.instance.isar;

    // 先查询模板是否存在
    TemplateData? templateData = await queryTemplateById(templateId);
    if (templateData == null) {
      return;
    }

    // 更新打印时间
    TemplateData modifyTemplate = templateData.copyWith(
        profile: templateData.profile.copyWith(extra: templateData.profile.extra.copyWith(lastPrintedAt: printTime)));

    // 保存到数据库
    await isar.writeTxn(() async {
      NiimBotTemplateCanvasDataModel templateModel = await templateModelToIsarModel(modifyTemplate);
      await isar.niimBotTemplateCanvasDataModels.put(templateModel);
    });
    TemplateSortType templateSort = await FolderTemplateSortHelper.templateSortTypeFromSp();
    if (templateSort == TemplateSortType.printTime) {
      NiimbotEventBus.getDefault().post({"myTemplateRefresh": "", "isLoadFromLocal": true});
    }
  }

  ///更新模版userId
  static Future<void> updateTemplateDefaultUserId(String userId) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      List<NiimBotTemplateCanvasDataModel> templateModelList =
          await isar.niimBotTemplateCanvasDataModels.where().userIdEqualTo("0").findAll();

      for (final model in templateModelList) {
        model.userId = userId;
      }
      // 批量更新数据库
      await isar.niimBotTemplateCanvasDataModels.putAll(templateModelList);
    });
  }

  ///更新模板
  static Future<TemplateData> updateTemplate(TemplateData templateData) async {
    //服务端更新模板需要更新updateTime，所以传过来的templateData已经更新了updateTime
    TemplateData modifyTemplate = templateData.copyWith(local_type: SYNC);
    final isar = DBManagerUtil.instance.isar;
    NiimBotTemplateCanvasDataModel? modifyTemplateModel =
        await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateData.id).findFirst();
    if (modifyTemplateModel == null) {
      return modifyTemplate;
    }
    String? serviceUpdateTime = templateData.profile.extra.updateTime ?? "";
    String? localUpdateTime = modifyTemplateModel.updateTime ?? "";
    if (serviceUpdateTime.isNotEmpty &&
        localUpdateTime.isNotEmpty &&
        localUpdateTime.compareTo(serviceUpdateTime) > 0) {
      return modifyTemplate;
    }
    modifyTemplateModel.localType = modifyTemplate.local_type.toInt();
    modifyTemplateModel.updateTime = modifyTemplate.profile.extra.updateTime;
    // modifyTemplateModel.profile = jsonEncode(modifyTemplate.profile.toJson());
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(modifyTemplateModel);
    });
    return modifyTemplate;
  }

  ///批量更新模板
  static Future<void> batchUpdateTemplate(List<TemplateData> templates) async {
    final isar = DBManagerUtil.instance.isar;
    final List<NiimBotTemplateCanvasDataModel> modelsToPut = [];

    for (final templateData in templates) {
      final model = await templateModelToIsarModel(templateData);
      NiimBotTemplateCanvasDataModel? templateModelLocal = await queryTemplateModelById(templateData.id!);
      if (templateModelLocal != null) {
        model.lastPrintedAt = templateModelLocal.lastPrintedAt;
      }
      modelsToPut.add(model);
    }

    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.putAll(modelsToPut);
    });
  }

  ///服务端拉到的模板数据同步到本地数据库
  static Future<bool> syncServiceFileListModel(TemplateListModel fileListModel) async {
    await _syncServiceDeletedTemplates(fileListModel.deletedTemplates);
    await _syncServiceFileList(fileListModel.list);
    return true;
  }

  ///服务端的模板列表同步到本地数据库
  static Future<bool> _syncServiceDeletedTemplates(List<num>? deleteTemplates) async {
    if (deleteTemplates == null || deleteTemplates.isEmpty) {
      return true;
    }
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      for (int i = 0; i < deleteTemplates.length; i++) {
        String templateId = deleteTemplates[i].toString();
        await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateId).deleteAll();
      }
    });
    return true;
  }

  ///服务端查询到已删除的模板列表，本地数据库同步删除
  static Future<bool> _syncServiceFileList(List<TemplateData>? serviceTemplateList) async {
    if (serviceTemplateList == null || serviceTemplateList.isEmpty) {
      return true;
    }
    List<TemplateData> syncTemplateList = await _checkNeedSyncTemplateList(serviceTemplateList);
    if (syncTemplateList.isEmpty) {
      return true;
    }
    final isar = DBManagerUtil.instance.isar;
    for (int i = 0; i < syncTemplateList.length; i++) {
      TemplateData modifyTemplate = syncTemplateList[i].copyWith(local_type: SYNC);
      NiimBotTemplateCanvasDataModel modifyTemplateModel = await templateModelToIsarModel(modifyTemplate);
      NiimBotTemplateCanvasDataModel? templateModelLocal = await queryTemplateModelById(modifyTemplate.id!);
      if (templateModelLocal != null) {
        modifyTemplateModel.lastPrintedAt = templateModelLocal.lastPrintedAt;
      }
      await isar.writeTxn(() async {
        await isar.niimBotTemplateCanvasDataModels.put(modifyTemplateModel);
      });
    }
    return true;
  }

  ///校验服务端需要同步到本地数据库的模板列表
  static Future<List<TemplateData>> _checkNeedSyncTemplateList(List<TemplateData> serviceTemplateList) async {
    if (serviceTemplateList.isEmpty) {
      return serviceTemplateList;
    }
    List<TemplateData> syncTemplateList = [];
    for (int i = 0; i < serviceTemplateList.length; i++) {
      TemplateData serviceTemplate = serviceTemplateList[i];
      NiimBotTemplateCanvasDataModel? localTemplate = await queryTemplateModelById(serviceTemplate.id!);
      if (localTemplate == null) {
        syncTemplateList.add(serviceTemplate);
      } else {
        String serviceUpdateTime = serviceTemplate.profile.extra.updateTime ?? "";
        String localUpdateTime = localTemplate.updateTime ?? "";
        if (localTemplate.localType == DEFAULT || localTemplate.localType == SYNC) {
          if (localUpdateTime.isEmpty || serviceUpdateTime != localUpdateTime) {
            syncTemplateList.add(serviceTemplate);
          }
        } else if (localTemplate.localType == UPDATE) {
          if (localUpdateTime.isEmpty || serviceUpdateTime.compareTo(localUpdateTime) >= 0) {
            syncTemplateList.add(serviceTemplate);
          }
        }
      }
    }
    return syncTemplateList;
  }

  ///新建模板本地数据库标记
  static Future<TemplateData> markCreateTemplate(TemplateData templateData, bool keepFolderId) async {
    String templateId = DateTime.now().millisecondsSinceEpoch.toString();
    TemplateData createTemplate = templateData.copyWith(id: templateId, local_type: CREATE);
    String createTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    createTemplate.profile.extra.createTime = createTime;
    createTemplate.profile.extra.updateTime = createTime;
    createTemplate.profile.extra.lastPrintedAt = "";
    createTemplate.profile.extra.userId = getUserId();
    if (!keepFolderId) {
      createTemplate.profile.extra.folderId = "0";
    }
    if (createTemplate.profile.extra.templateType != 2) {
      createTemplate.profile.extra.templateType = 0;
    }
    if (createTemplate.profile.extra.templateClass == 0) {
      createTemplate.profile.extra.templateClass = 1;
    }
    NiimBotTemplateCanvasDataModel createTemplateModel = await templateModelToIsarModel(createTemplate);
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(createTemplateModel);
    });
    return createTemplate;
  }

  static String getUserId() {
    if (Application.user == null) {
      return "0";
    } else {
      // Handle different possible types of user.id more robustly
      var userId = Application.user!.id;

      if (userId == null) {
        return "0";
      } else if (userId is double) {
        return userId.toInt().toString();
      } else if (userId is int) {
        return userId.toString();
      } else {
        return userId.toString();
      }
    }
  }

  ///删除模板本地数据库标记
  static Future<bool> markDeleteTemplate(TemplateData templateData) async {
    final isar = DBManagerUtil.instance.isar;
    NiimBotTemplateCanvasDataModel? deleteTemplate =
        await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateData.id).findFirst();
    if (deleteTemplate == null) {
      return true;
    }
    deleteTemplate.localType = DELETE;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(deleteTemplate);
    });
    return true;
  }

  ///更新模板本地数据库标记
  static Future<TemplateData> markUpdateTemplate(TemplateData templateData) async {
    //attention----离线创建的模版进行更新的情况--相当于还是本地create状态,此时要保持createTime和updateTime一致
    int localType = templateData.local_type == CREATE ? CREATE : UPDATE;
    TemplateData modifyTemplate = templateData.copyWith(local_type: localType);
    String updateTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    modifyTemplate.profile.extra.updateTime = updateTime;
    if (localType == CREATE) {
      modifyTemplate.profile.extra.createTime = updateTime;
    }
    NiimBotTemplateCanvasDataModel modifyTemplateModel = await templateModelToIsarModel(modifyTemplate);
    debugPrint("模板保存更新: ${modifyTemplateModel.templateId}");
    Completer<TemplateData> result = Completer();
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(modifyTemplateModel);
      result.complete(modifyTemplate);
    });
    debugPrint("模板保存更新: ${modifyTemplateModel.templateId} 更新库完成");
    return result.future;
  }

  ///修改模板名称本地数据库标记
  static Future<TemplateData> markModifyTemplateName(
      TemplateData templateData, String templateName, String updateTime) async {
    int localType = templateData.local_type != CREATE ? UPDATE : CREATE;
    TemplateData modifyTemplate = templateData.copyWith(name: templateName, local_type: localType);
    modifyTemplate.profile.extra.updateTime = updateTime;
    if (localType == CREATE) {
      modifyTemplate.profile.extra.createTime = updateTime;
    }
    final isar = DBManagerUtil.instance.isar;
    NiimBotTemplateCanvasDataModel? modifyTemplateModel =
        await isar.niimBotTemplateCanvasDataModels.filter().templateIdEqualTo(templateData.id).findFirst();
    if (modifyTemplateModel == null) {
      return modifyTemplate;
    }
    modifyTemplateModel.localType = modifyTemplate.local_type.toInt();
    modifyTemplateModel.name = modifyTemplate.name;
    // modifyTemplateModel.profile = jsonEncode(modifyTemplate.profile.toJson());
    modifyTemplateModel.updateTime = modifyTemplate.profile.extra.updateTime;
    await isar.writeTxn(() async {
      await isar.niimBotTemplateCanvasDataModels.put(modifyTemplateModel);
    });
    return modifyTemplate;
  }

  ///从本地数据库检查模板是否已经存在
  static Future<bool> checkTemplateExist(String templateId) async {
    NiimBotTemplateCanvasDataModel? niimbotTemplateModel = await queryTemplateModelById(templateId);
    return niimbotTemplateModel != null;
  }

  ///自定义查询模板
  static Future<List<TemplateData>> customQueryTemplates(
      QueryBuilder<NiimBotTemplateCanvasDataModel, NiimBotTemplateCanvasDataModel, QAfterFilterCondition> queryBuilder,
      {int pageIndex = 1,
      int pageSize = 10,
      bool needParseElements = true,
      bool needCustomTemplateSort = true}) async {
    List<NiimBotTemplateCanvasDataModel> templateModelList = [];
    TemplateSortType templateSort = TemplateSortType.updateTime;
    if (needCustomTemplateSort) {
      templateSort = await FolderTemplateSortHelper.templateSortTypeFromSp();
    }
    if (templateSort == TemplateSortType.updateTime) {
      //更新时间排序查询模板
      templateModelList =
          await queryBuilder.sortByUpdateTimeDesc().offset((pageIndex - 1) * pageSize).limit(pageSize).findAll();
    } else if (templateSort == TemplateSortType.createTime) {
      //创建时间排序查询模板
      templateModelList =
          await queryBuilder.sortByCreateTimeDesc().offset((pageIndex - 1) * pageSize).limit(pageSize).findAll();
    } else if (templateSort == TemplateSortType.printTime) {
      // 如果打印次数大于0，则按照打印次数排序，否则按照更新时间排
      int printedCout = await queryBuilder.lastPrintedAtIsNotEmpty().countSync();
      if (printedCout > 0) {
        templateModelList =
            await queryBuilder.sortByLastPrintedAtDesc().offset((pageIndex - 1) * pageSize).limit(pageSize).findAll();
      } else {
        templateModelList =
            await queryBuilder.sortByUpdateTimeDesc().offset((pageIndex - 1) * pageSize).limit(pageSize).findAll();
      }
    }
    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData =
          await isarModelToTemplateModel(niimbotTemplateModel, needParseElements: needParseElements);
      templateList.add(templateData);
    }
    return templateList;
  }

  static Future<TemplateData?> queryTemplateById(String templateId) async {
    NiimBotTemplateCanvasDataModel? niimbotTemplateModel = await queryTemplateModelById(templateId);
    if (niimbotTemplateModel != null) {
      return isarModelToTemplateModel(niimbotTemplateModel);
    }
    return null;
  }

  /**
   * 根据onecode查询云模版列表
   */
  static Future<List<TemplateData>> queryCloudTemplateByScanCode(String oneCode) async {
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = await isar.niimBotTemplateCanvasDataModels
        .filter()
        .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
        .not()
        .localTypeEqualTo(DELETE)
        .group((q) => q
            .barcodeEqualTo(oneCode)
            .or()
            .sparedCodeEqualTo(oneCode)
            .or()
            .amazonCodeBeijingEqualTo(oneCode)
            .or()
            .amazonCodeWuhanEqualTo(oneCode)
            .or()
            .virtualBarCodeEqualTo(oneCode)
            .or()
            .barcodeCategoryMapContains(oneCode))
        .sortByCreateTimeDesc()
        .findAll();
    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel);
      templateList.add(templateData);
    }
    return templateList;
  }

  /**
   * 根据onecode查询标签列表
   */
  static Future<List<TemplateData>> queryRfidTemplateByScanCode(String oneCode) async {
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = await isar.niimBotTemplateCanvasDataModels
        .filter()
        .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
        .templateClassEqualTo(TemplateClass.LABEL)
        .group((q) => q.userIdEqualTo("-1").or().userIdIsEmpty())
        .group((q) => q
            .barcodeEqualTo(oneCode)
            .or()
            .sparedCodeEqualTo(oneCode)
            .or()
            .virtualBarCodeEqualTo(oneCode)
            .or()
            .amazonCodeBeijingEqualTo(oneCode)
            .or()
            .amazonCodeWuhanEqualTo(oneCode)
            .or()
            .barcodeCategoryMapContains(oneCode))
        .sortByCreateTimeDesc()
        .findAll();
    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel);
      templateList.add(templateData);
    }
    return templateList;
  }

  ///通过模板id从本地数据库查询模板
  static Future<NiimBotTemplateCanvasDataModel?> queryTemplateModelById(String templateId) async {
    final isar = DBManagerUtil.instance.isar;
    return await isar.niimBotTemplateCanvasDataModels
        .where()
        .templateIdEqualTo(templateId)
        .filter()
        .not()
        .localTypeEqualTo(DELETE)
        .findFirst();
  }

  ///分页查询模板列表
  static Future<List<TemplateData>> queryTemplateList(int page, {int pageSize = 10}) async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = await isar.niimBotTemplateCanvasDataModels
        .where()
        .userIdEqualTo(userId)
        .filter()
        .not()
        .localTypeEqualTo(DELETE)
        .sortByUpdateTimeDesc()
        .offset((page - 1) * pageSize)
        .limit(pageSize)
        .findAll();
    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel);
      templateList.add(templateData);
    }
    return templateList;
  }

  ///分页查询我的商品模板列表
  static Future<List<TemplateData>> queryMyGoodTemplateList(int page,
      {int pageSize = 10, String searchKey = ""}) async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = [];
    if (searchKey.isEmpty) {
      templateModelList = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId)
          .not()
          .localTypeEqualTo(DELETE)
          .templateTypeEqualTo("2")
          .sortByUpdateTimeDesc()
          .offset((page - 1) * pageSize)
          .limit(pageSize)
          .findAll();
    } else {
      templateModelList = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .templateTypeEqualTo("2")
          .nameContains(searchKey)
          .sortByUpdateTimeDesc()
          .offset((page - 1) * pageSize)
          .limit(pageSize)
          .findAll();
    }

    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel, needParseElements: false);
      templateList.add(templateData);
    }
    return templateList;
  }

  ///获取我的商品模版总数
  static Future<int> countMyGoodTemplateList({String searchKey = ""}) async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    int count = 0;
    if (searchKey.isEmpty) {
      count = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .templateTypeEqualTo("2")
          .count();
    } else {
      count = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .templateTypeEqualTo("2")
          .nameContains(searchKey)
          .count();
    }

    return count;
  }

  ///分页查询我的模板列表
  static Future<List<TemplateData>> queryMyTemplateList(int page, {int pageSize = 10, String searchKey = ""}) async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = [];
    if (searchKey.isEmpty) {
      templateModelList = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .not()
          .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
          .sortByUpdateTimeDesc()
          .offset((page - 1) * pageSize)
          .limit(pageSize)
          .findAll();
    } else {
      templateModelList = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .not()
          .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
          .nameContains(searchKey)
          .sortByUpdateTimeDesc()
          .offset((page - 1) * pageSize)
          .limit(pageSize)
          .findAll();
    }

    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel, needParseElements: false);
      templateList.add(templateData);
    }
    return templateList;
  }

  ///获取我的模版总数
  static Future<int> countMyTemplateList({String searchKey = ""}) async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    int count = 0;
    if (searchKey.isEmpty) {
      count = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .not()
          .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
          .count();
    } else {
      count = await isar.niimBotTemplateCanvasDataModels
          .filter()
          .userIdEqualTo(userId.toString())
          .not()
          .localTypeEqualTo(DELETE)
          .not()
          .templateTypeEqualTo(TemplateType.TEMPLATE_CLOUD)
          .nameContains(searchKey)
          .count();
    }

    return count;
  }

  ///查询需要同步到服务端的模板
  static Future<List<TemplateData>> queryAllLocalTemplateList() async {
    String userId = getUserId();
    final isar = DBManagerUtil.instance.isar;
    List<NiimBotTemplateCanvasDataModel> templateModelList = await isar.niimBotTemplateCanvasDataModels
        .where()
        .userIdEqualTo(userId.toString())
        .filter()
        .not()
        .localTypeEqualTo(SYNC)
        .and()
        .not()
        .localTypeEqualTo(DEFAULT)
        .sortByUpdateTime()
        .findAll();
    List<TemplateData> templateList = [];
    if (templateModelList.isEmpty) {
      return [];
    }
    for (int i = 0; i < templateModelList.length; i++) {
      NiimBotTemplateCanvasDataModel niimbotTemplateModel = templateModelList[i];
      TemplateData templateData = await isarModelToTemplateModel(niimbotTemplateModel);
      templateList.add(templateData);
    }
    return templateList;
  }

  ///数据库model转为 templateModel
  static Future<TemplateData> isarModelToTemplateModel(NiimBotTemplateCanvasDataModel isarModel,
      {bool needParseElements = true}) async {
    final templateVersion = isarModel.templateVersion;
    final isCable = isarModel.isCable;
    final profile = TemplateProfile(
        barcode: isarModel.barcode,
        hardwareSeriesId: isarModel.hardwareSeriesId,
        machineId: isarModel.machineId?.replaceAll('*', ''),
        machineName: isarModel.machineName,
        extra: TemplateProfileExtra(
          folderId: isarModel.folderId,
          userId: isarModel.userId,
          industryId: isarModel.industryId,
          commodityCategoryId: isarModel.commodityCategoryId,
          isDelete: isarModel.isDelete,
          createTime: isarModel.createTime,
          updateTime: isarModel.updateTime,
          sourceId: isarModel.sourceId,
          labelId: isarModel.labelId,
          lastPrintedAt: isarModel.lastPrintedAt,
          templateClass: isarModel.templateClass,
          templateType: TemplateParseUtils.parseNumberFromJSON(isarModel.templateType),
          amazonCodeBeijing: isarModel.amazonCodeBeijing,
          amazonCodeWuhan: isarModel.amazonCodeWuhan,
          virtualBarCode: isarModel.virtualBarCode,
          sparedCode: isarModel.sparedCode,
          goodsCode: isarModel.goodsCode,
          materialModelSn: isarModel.materialModelSn,
          barcodeCategoryMap: isarModel.barcodeCategoryMap == null || isarModel.barcodeCategoryMap!.isEmpty
              ? null
              : Map<String, String>.from(jsonDecode(isarModel.barcodeCategoryMap ?? '')),
        ));
    final commodityTemplate = TemplateParseUtils.parseBoolFromJSON(isarModel.commodityTemplate);
    final List<String>? paperColor = (isarModel.paperColor == null || isarModel.paperColor!.isEmpty)
        ? []
        : TemplateParseUtils.parseListFromJSON<String>(jsonDecode(isarModel.paperColor ?? ''));
    List<dynamic>? isarDataSource =
        (isarModel.dataSource == null || isarModel.dataSource!.isEmpty) ? [] : jsonDecode(isarModel.dataSource ?? '');
    // final dataSoucesModify = TemplateParse.parseTemplateModify(
    //     (isarModel.dataSourceModifies == null ||
    //         isarModel.dataSourceModifies!.isEmpty)
    //         ? null
    //         : jsonDecode(isarModel.dataSourceModifies ?? ''),
    //     null,
    //     null);
    // final dataSources = await TemplateParse.parseTemplateDataSource(
    //     isarDataSource,
    //     null,
    //     (isarModel.elements == null || isarModel.elements!.isEmpty)
    //         ? []
    //         : jsonDecode(isarModel.elements ?? ''),
    //     CanvasActionUtils.parseDataSourceResources,
    //     CanvasActionUtils.parseExcelDataToDataSources,
    //     dataSoucesModify,
    //     null,
    //     isarModel.currentPage);
    List<TemplateDataSource>? dataSources =
        isarDataSource?.map((e) => TemplateDataSource.fromJson(Map<String, dynamic>.from(e))).toList() ?? [];
    List<BaseElement>? elements;
    String? elementsJsonStr;
    if (needParseElements) {
      bool isBadElement = false;
      if ((isarModel.elements ?? "").isNotEmpty && !isarModel.elements!.contains("[")) {
        debugPrint("wangxuhao elements==${isarModel.elements}");
        isBadElement = true;
      }
      List<Map<String, dynamic>> elementList =
          List<Map<String, dynamic>>.from(jsonDecode(isarModel.elements ?? "[]") as List<dynamic>);
      for (int i = 0; i < elementList.length; i++) {
        Map<String, dynamic> element = elementList[i];
        if (element["type"] == "image") {
          if ((element["localUrl"] ?? "").isNotEmpty) {
            element["localUrl"] = getCurrentLocalUrl(element["localUrl"]);
          }
          if ((element["localImageUrl"] ?? "").isNotEmpty) {
            element["localImageUrl"] = getCurrentLocalUrl(element["localImageUrl"]);
          }
          if ((element["ninePatchLocalUrl"] ?? "").isNotEmpty) {
            element["ninePatchLocalUrl"] = getCurrentLocalUrl(element["ninePatchLocalUrl"]);
          }
        }
      }
      elements = (isarModel.elements == null || isarModel.elements!.isEmpty || isBadElement)
          ? []
          : await TemplateParse.parseTemplateElements(elementList,
              isNeedCheckFontDownload: false, isNeedParseTime: false);
    } else {
      elementsJsonStr = isarModel.elements;
    }

    final hasVipRes = TemplateParseUtils.parseBoolFromJSON(isarModel.hasVipRes);
    final vip = TemplateParseUtils.parseBoolFromJSON(isarModel.vip);
    final cableDirection = TemplateParseUtils.parseTemplateCableDirectionFromJSON(isarModel.cableDirection);
    final margin = TemplateParseUtils.parseListFromJSON<num>(jsonEncode(isarModel.margin));
    final platformCode = TemplateParseUtils.parseTemplatePlatformCodeFromJSON(isarModel.platformCode);
    Map<String, String> usedFonts = {"ZT001": "ZT001.ttf"};
    TemplateData template = TemplateData(
      id: TemplateParseUtils.parseStringFromJSON(isarModel.templateId),
      labelId: TemplateParseUtils.parseStringFromJSON(isarModel.labelId),
      labelNames: (isarModel.labelNames == null || isarModel.labelNames?.isEmpty == true)
          ? []
          : (TemplateParseUtils.parseListFromJSON(jsonDecode(isarModel.labelNames ?? ''))
                  ?.map((e) => LabelNameInfo.fromJson(e))
                  .toList() ??
              []),
      cloudTemplateId: TemplateParseUtils.parseStringFromJSON(isarModel.sourceId),
      originTemplateId: TemplateParseUtils.parseStringFromJSON(isarModel.originTemplateId),
      name: isarModel.name,
      // description: data['description'],
      thumbnail: isarModel.thumbnail,
      localThumbnailPath: getCurrentLocalUrl(isarModel.localThumbnail),
      backgroundImage: isarModel.backgroundImage,
      multipleBackIndex: isarModel.multipleBackIndex,
      localBackground: getCurrentLocalBackground(isarModel.localBackground?.split(',') ?? []),
      local_type: isarModel.localType ?? 0,
      width: isarModel.width ?? 50,
      height: isarModel.height ?? 30,
      rotate: isarModel.rotate,
      consumableType: isarModel.consumableType,
      paperType: isarModel.paperType,
      isCable: isCable,
      cableDirection: cableDirection,
      cableLength: isarModel.cableLength ?? 0.0,
      margin: margin,
      profile: profile,
      platformCode: platformCode,
      // accuracyName: data['paccuracyName'],
      commodityTemplate: commodityTemplate,
      totalPage: isarModel.totalPage ?? 1,
      currentPageIndex: isarModel.currentPage ?? 0,
      canvasRotate: isarModel.canvasRotate,
      paperColor: paperColor,
      elements: elements,
      elementsJsonStr: elementsJsonStr,
      usedFonts: usedFonts,
      hasVipRes: hasVipRes,
      vip: vip,
      isEdited: isarModel.isEditedNew?.toInt() ?? 0,
      dataSources: dataSources,
      templateVersion: templateVersion,
      dataSourceModifies: isarModel.dataSourceModifies?.isNotEmpty == true
          ? TemplateDataSourceModifiesExtension.fromJson(jsonDecode(isarModel.dataSourceModifies!))
          : null,
      dataSourceBindInfo: isarModel.dataSourceBindInfo?.isNotEmpty == true
          ? TemplateDataSourceInfo.fromJson(jsonDecode(isarModel.dataSourceBindInfo!))
          : null,
      layoutSchema: isarModel.layoutSchema,
      inputAreas: isarModel.inputAreas?.isNotEmpty == true
          ? (jsonDecode(isarModel.inputAreas!) as List<dynamic>?)
              ?.map((e) => TemplateInputArea.fromJson(Map<String, dynamic>.from(e)))
              .toList()
          : null,
      supportedEditors: isarModel.supportedEditors?.isNotEmpty == true
          ? TemplateParseUtils.parseListFromJSON<String>(jsonDecode(isarModel.supportedEditors ?? ''))
          : null,
    );
    // String localThumbPath = await template.getTemplateLocalThumbPath();
    // template = template.copyWith(localThumbnail:localThumbPath);
    return template;
  }

  ///templateModel转为数据库需要的model
  static Future<NiimBotTemplateCanvasDataModel> templateModelToIsarModel(
    TemplateData templateData, {
    int? localType,
    bool? arrowDetail,
    bool? isLabel,
  }) async {
    String userId = templateData.profile.extra.userId ?? "0";
    String? elements;
    if (templateData.elementsJsonStr?.isNotEmpty ?? false) {
      elements = templateData.elementsJsonStr;
    } else {
      elements = templateData.elements.isEmpty ? null : elementsString(templateData.elements);
    }
    if (elements?.isNotEmpty == true && !elements!.contains("[")) {
      debugPrint("wangxuhao elements==$elements");
    }
    return NiimBotTemplateCanvasDataModel(
        templateId: templateData.id,
        platformCode: templateData.platformCode.name,
        userId: userId,
        labelId: templateData.profile.extra.labelId,
        sourceId: templateData.profile.extra.sourceId,
        originTemplateId: templateData.originTemplateId,
        localType: templateData.local_type.toInt(),
        margin: templateData.margin.isEmpty ? null : jsonEncode(templateData.margin),
        machineName: templateData.profile.machineName,
        paperType: templateData.paperType,
        backgroundImage: templateData.backgroundImage,
        templateVersion: templateData.templateVersion,
        // elements: templateData.elements.isEmpty ? null : elementsString(templateData.elements),
        elements: elements,
        // values
        // :,
        industryId: templateData.profile.extra.industryId,
        templateClass: templateData.profile.extra.templateClass?.toInt(),
        canvasRotate: templateData.canvasRotate.toInt(),
        dataSource: (templateData.dataSources == null || templateData.dataSources!.isEmpty)
            ? null
            : jsonEncode(templateData.dataSources),
        dataSourceModifies: (templateData.dataSourceModifies == null || templateData.dataSourceModifies!.isEmpty)
            ? null
            : jsonEncode(templateData.dataSourceModifies),
        dataSourceBindInfo:
            templateData.dataSourceBindInfo == null ? null : jsonEncode(templateData.dataSourceBindInfo),
        names: (templateData.names == null || templateData.names!.isEmpty) ? null : jsonEncode(templateData.names),
        hardwareSeriesId: templateData.profile.hardwareSeriesId,
        name: templateData.name,
        consumableType: templateData.consumableType,
        sparedCode: templateData.profile.extra.sparedCode,
        barcode: templateData.profile.barcode,
        width: templateData.width.toDouble(),
        height: templateData.height.toDouble(),
        labelNames: templateData.labelNames.isEmpty ? null : jsonEncode(templateData.labelNames),
        materialModelSn: templateData.profile.extra.materialModelSn ?? '',
        folderId: templateData.profile.extra.folderId ?? '0',
        isDelete: false,
        paperColor: templateData.paperColor.isEmpty ? null : jsonEncode(templateData.paperColor),
        amazonCodeWuhan: templateData.profile.extra.amazonCodeWuhan,
        amazonCodeBeijing: templateData.profile.extra.amazonCodeBeijing,
        virtualBarCode: templateData.profile.extra.virtualBarCode,
        commodityInfo: '',
        barcodeCategoryMap: (templateData.profile.extra.barcodeCategoryMap == null ||
                templateData.profile.extra.barcodeCategoryMap!.isEmpty)
            ? null
            : jsonEncode(templateData.profile.extra.barcodeCategoryMap),
        updateTime: templateData.profile.extra.updateTime,
        lastPrintedAt: templateData.profile.extra.lastPrintedAt,
        templateType: templateData.profile.extra.templateType?.toString(),
        rotate: templateData.rotate.toInt(),
        isCable: templateData.isCable,
        isEditedNew: templateData.isEdited.toInt(),
        hasVipRes: templateData.hasVipRes,
        vip: templateData.vip,
        goodsCode: templateData.profile.extra.goodsCode,
        machineId: templateData.profile.machineId,
        commodityTemplate: templateData.commodityTemplate,
        totalPage: templateData.totalPage,
        currentPage: templateData.currentPageIndex,
        thumbnail: templateData.thumbnail,
        localThumbnail: templateData.localThumbnail,
        createTime: templateData.profile.extra.createTime,
        cableDirection: templateData.cableDirection?.value,
        isPrintHistory: false,
        commodityCategoryId: templateData.profile.extra.commodityCategoryId,
        cableLength: templateData.cableLength.toDouble(),
        hardwareSeriesName: '',
        multipleBackIndex: templateData.multipleBackIndex,
        localBackground: templateData.localBackground.join(','),
        arrowInBoard: arrowDetail,
        isLabel: isLabel,
        layoutSchema: templateData.layoutSchema,
        inputAreas: templateData.inputAreas == null ? null : jsonEncode(templateData.inputAreas),
        supportedEditors: templateData.supportedEditors == null ? null : jsonEncode(templateData.supportedEditors));
  }

  static String? elementsString(List<BaseElement> elements) {
    List<Map<String, dynamic>> list = [];
    for (var element in elements) {
      try {
        list.add(element.toJson());
      } catch (e) {
        debugPrint("元素解析失败: $e");
      }
    }
    return jsonEncode(list);
  }

  ///将已同步的模板状态重置为默认状态
  ///查询localTypeEqualTo(SYNC)的所有记录，并将localType字段更新为DEFAULT
  static Future<int> resetSyncTemplatesToDefault() async {
    final isar = DBManagerUtil.instance.isar;
    int updatedCount = 0;

    await isar.writeTxn(() async {
      // 查询所有localType为SYNC的记录
      List<NiimBotTemplateCanvasDataModel> syncTemplates =
          await isar.niimBotTemplateCanvasDataModels.filter().localTypeEqualTo(SYNC).findAll();

      if (syncTemplates.isEmpty) {
        return;
      }

      // 批量更新localType为DEFAULT
      for (final template in syncTemplates) {
        template.localType = DEFAULT;
      }

      // 批量保存到数据库
      await isar.niimBotTemplateCanvasDataModels.putAll(syncTemplates);
      updatedCount = syncTemplates.length;
      print("wangxuhao updata count==$updatedCount");
    });

    return updatedCount;
  }

  //替换本地真实沙盒路径
  static String _getSandboxPath(String documentsPath, String path) {
    String relativePath = path.substring(path.indexOf("Documents")).replaceAll("Documents", "");
    String sandboxRealyPath = "$documentsPath$relativePath";
    return sandboxRealyPath;
  }

  /// 获取处理后的本地缩略图路径
  static String getCurrentLocalUrl(String? localUrl) {
    if (Platform.isIOS) {
      if (localUrl?.isEmpty ?? true) return "";
      if (localUrl!.contains("Documents")) {
        String localDocumentsPath = Application.localCachePath;
        localUrl = _getSandboxPath(localDocumentsPath, localUrl);
      }
    }
    return localUrl ?? "";
  }

  /// 获取处理后的本地背景图路径列表
  static List<String> getCurrentLocalBackground(List<String>? localBackground) {
    if (Platform.isIOS) {
      if (localBackground?.isEmpty ?? true) return localBackground ?? [];
      if (localBackground!.any((path) => path.contains("Documents"))) {
        String localDocumentsPath = Application.localCachePath;
        localBackground = localBackground.map((path) => _getSandboxPath(localDocumentsPath, path)).toList();
      }
    }
    return localBackground ?? [];
  }
}
