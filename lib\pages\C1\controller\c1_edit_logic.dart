import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_template/models/copy_wrapper.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/text_element.dart';
import 'package:niimbot_template/models/font_size_config.dart';
import 'package:niimbot_template/models/template/tube_file_setting.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/models/values/composite_value_type.dart';
import 'package:niimbot_template/models/values/serial_value_type.dart';
import 'package:niimbot_template/models/values/template_value_type.dart';
import 'package:niimbot_template/models/values/text_value_type.dart';
import 'package:text/application.dart';
import 'package:text/pages/C1/common/toast_util.dart';
import 'package:text/pages/C1/model/c1_file_business.dart';
import 'package:text/pages/C1/model/c1_print_manager.dart';
import 'package:text/pages/C1/model/c1_template_version_manager.dart';
import 'package:text/pages/C1/model/define_model.dart';
import 'package:text/pages/C1/model/print_setting_config.dart';
import 'package:text/pages/C1/model/template_data_transform.dart';
import 'package:text/pages/C1/model/text_info.dart';
import 'package:text/pages/C1/view/c1_edit_serial_page.dart';
import 'package:text/pages/C1/view/c1_file_setting_page.dart';
import 'package:text/pages/C1/view/c1_preview_serial_page.dart';
import 'package:text/pages/C1/view/c1_print_setting_page.dart';
import 'package:text/pages/canvas/impl/loading_toast_impl.dart';
import 'package:text/pages/canvas/niimbot_canvas_page.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/widget/adjust_bottom_sheet.dart' as adjust_bottom_sheet;
import 'package:tuple/tuple.dart';

/// 段落状态
enum ParagraphStatus {
  /// 编辑状态
  isEditing,

  /// 编辑浮窗选中状态
  isEditingSelected,

  /// 无状态
  none,
}

/// 文本属性编辑状态
enum TextAttributeStatus {
  /// 无状态
  none,

  /// 字号
  fontSize,

  /// 字距
  fontSpacing,

  /// 竖排文字
  verticalText,

  /// 横排文字
  horizontalText,
}

/// 键盘事件编辑状态
enum KeyBoardActionStatus {
  /// 无状态
  none,

  /// 正常输入状态, 默认状态
  normal,

  /// 符号输入状态
  symbol,

  /// 序列号输入状态
  serial
}

/// C1编辑器管理类
class C1EditLogic extends GetxController {
  /// 模版数据
  late var templateData = Rx<TemplateData>(TemplateData(usedFonts: {}));

  /// 段落状态
  var paragraphStatus = ParagraphStatus.none.obs;

  /// 文本属性编辑状态
  var textAttributeStatus = TextAttributeStatus.none.obs;

  /// 键盘事件编辑状态
  var keyBoardActionStatus = KeyBoardActionStatus.none.obs;

  /// 选中段落 (item1- 段落模型，item2- 段落选中的行数，两行的情况下存在0/1的情况, 当前的Context)
  var selectedParagraph = Rxn<Tuple3<ValueTypeProtocol, int, BuildContext>>();

  String get title => consumableInfo + lengthInfo;

  /// 选中的段落行，存在双行的情况
  CompositeValueType? get selectedParagraphElement {
    Tuple3<ValueTypeProtocol, int, BuildContext>? selectedParagraphInfo = selectedParagraph();
    if (selectedParagraphInfo == null) {
      return null;
    }
    // 获取当前的段落模型
    CompositeValueType compositeValueType = selectedParagraphInfo.item1 as CompositeValueType;
    // 获取子段落模型
    return compositeValueType.valueObjects?[selectedParagraphInfo.item2] as CompositeValueType;
  }

  /// 选中的样式
  TextElement? get selectedElement {
    TextElement? _selectedElement;
    // 寻找当前选中段落的element
    List<BaseElement?> elements =
        this.templateData().elements.where((element) => element.id == selectedParagraphElement?.elementId).toList();
    if (elements.isNotEmpty) {
      BaseElement? element = elements.first;
      _selectedElement = (element as TextElement);
    } else {
      _selectedElement = null;
    }
    return _selectedElement;
  }

  /// 选中段落的字体属性
  FontSizeConfig get selectedParagraphFontSizeConfig {
    // 寻找当前选中段落的element
    BaseElement? element =
        templateData().elements.where((element) => element.id == selectedParagraphElement?.elementId).toList().first;
    double fontSize = (element as TextElement).fontSize.toDouble();
    FontSizeConfig fontSizeConfig = getFontSizeConfig(fontSize);
    return fontSizeConfig;
  }

  /// 根据当前字体大小获取字体配置
  FontSizeConfig getFontSizeConfig(double fontSize) {
    FontSizeConfig fontSizeConfig = fontSizeConfigs.last;
    for (int i = fontSizeConfigs.length - 1; i >= 0; i--) {
      double dSizeCurrent = (fontSizeConfigs[i].mm - fontSize).abs();
      double dSize = (fontSizeConfig.mm - fontSize).abs();
      if (dSizeCurrent < dSize) {
        fontSizeConfig = fontSizeConfigs[i];
      } else if (dSizeCurrent > dSize) {
        break;
      }
    }
    return fontSizeConfig;
  }

  /// 模版下的大概字号范围（单个字号）
  late List<FontSizeConfig> fontSizeConfigs;

  /// 计时器（用于字体值更新值防抖）
  Timer? timer;

  /// 选中的段落的监听器
  late Worker paragraphWorker;

  /// 编辑状态监听器
  late Worker paragraphStatusWorker;

  late int tubeType;
  late double tubeSpecs;

  TextLength get textLength =>
      templateData().tubeFileSetting?.autoWidth == true ? TextLength.autoWidth : TextLength.fixedWidth;

  late TextFormat textFormat;

  RepeatCountInputFormatter repeatCountInputFormat = RepeatCountInputFormatter();

  Map<String, ScrollController> _scrollControllers = {};

  bool fileChanged = false;

  /// 是否允许插入序号（当前正在编辑的段落如果已经有序号，则不允许再次插入序号）
  /// 限制条件：1.每个段落最多3组序号 2.序号组合最多生成1000段
  Rx<bool> allowInsertSerial = RxBool(false);

  num? selectTextFontSize;

  bool? selectTextBold;

  bool? selectTextUnderline;

  /// 是否选中对齐（默认居中对齐）
  bool? selectTextAlignH;

  bool? selectTextVerticalStyle;

  bool needRefresh = false;

  late double safeWidth;

  late double safeHeight;

  late String consumableInfo;

  late String lengthInfo;

  late TextInfo textInfo;

  NetalTextAlign fixLengthTextAlignHorizontal = NetalTextAlign.center;

  Function? importExcelGuide;

  late SyncServiceTemplateFun _syncServiceTemplateFun;

  C1EditLogic({
    required TemplateData template,
    required int tubeType,
    required double tubeSpecs,
    required TextFormat textFormat,
    required String consumableInfo,
    required String lengthInfo,
  }) : super() {
    this.templateData.value = template;
    this.tubeType = tubeType;
    this.tubeSpecs = tubeSpecs;
    this.textFormat = textFormat;
    this.consumableInfo = consumableInfo;
    this.lengthInfo = lengthInfo;
    bool autoWidth = template.tubeFileSetting?.autoWidth == true;
    if (!autoWidth) {
      BaseElement? element = template.elements.firstOrNull;
      fixLengthTextAlignHorizontal =
          element != null && element is TextElement ? element.textAlignHorizontal : NetalTextAlign.center;
    }
    safeWidth = TemplateDataTransform.getTemplateEditSafeWidth(this.templateData());
    safeHeight = TemplateDataTransform.getTemplateEditSafeHeight(this.templateData());
    // 粗略计算字号范围（单个字号）
    fontSizeConfigs = EditTemplateDataTransform.calcParagraphMaxFontSize(
        template.width.toDouble(), template.height.toDouble(), template.margin, textFormat);
    paragraphStatusWorker = ever(paragraphStatus, (status) {
      switch (status) {
        case ParagraphStatus.isEditing:
          // 重置键盘状态
          keyBoardActionStatus.value = KeyBoardActionStatus.normal;
          // 此处延迟是因为动画的原因，保证动画结束
          Future.delayed(const Duration(milliseconds: 250), () {
            // 重置字体横条状态
            textAttributeStatus.value = TextAttributeStatus.none;
          });
        case ParagraphStatus.isEditingSelected:
          break;
        case ParagraphStatus.none:
          // 选中值清空
          selectedParagraph.value = null;
          // 重置键盘状态
          keyBoardActionStatus.value = KeyBoardActionStatus.none;
          // 重置字体横条状态
          textAttributeStatus.value = TextAttributeStatus.none;
      }
    });
    TextElement textElement = template.elements.first as TextElement;
    textInfo = TextInfo(
        width: safeWidth,
        height: textElement.height,
        fontSize: textElement.fontSize,
        fontFamily: textElement.fontFamily,
        fontStyle: textElement.fontStyle,
        textAlignHorizontal: textElement.textAlignHorizontal,
        textAlignVertical: textElement.textAlignVertical,
        rotate: textElement.rotate,
        wordSpacing: textElement.wordSpacing,
        letterSpacing: textElement.letterSpacing,
        lineSpacing: textElement.lineSpacing,
        typesettingMode: textElement.typesettingMode,
        typesettingParam: textElement.typesettingParam,
        colorReverse: textElement.colorReverse,
        colorChannel: textElement.colorChannel,
        boxStyle: textElement.boxStyle,
        textStyle: textElement.textStyle);
    _syncServiceTemplateFun = (templateId, syncTemplate, fromBackground) {
      if (templateId == templateData().id) {
        _saveUpdateTemplateData(syncTemplate);
      }
    };
    C1FileBusiness.instance.isEditStatus = true;
  }

  refreshCurrentParagraph() {
    update([selectedParagraph()?.item1.id ?? '']);
  }

  @override
  void onInit() {
    super.onInit();
    C1FileBusiness.instance.addSyncServiceTemplateFun(_syncServiceTemplateFun);
  }

  @override
  void onReady() {
    super.onReady();
    // 监听段落状态变化，更新允许插入序号的状态
    paragraphStatusWorker = ever(paragraphStatus, (status) {
      switch (status) {
        case ParagraphStatus.isEditing:
          // 重置键盘状态
          keyBoardActionStatus.value = KeyBoardActionStatus.normal;
          // 此处延迟是因为动画的原因，保证动画结束
          Future.delayed(const Duration(milliseconds: 250), () {
            // 重置字体横条状态
            textAttributeStatus.value = TextAttributeStatus.none;
          });
          // 更新允许插入序号的状态
          updateAllowInsertSerialStatus();
        case ParagraphStatus.isEditingSelected:
          // 更新允许插入序号的状态
          updateAllowInsertSerialStatus();
          break;
        case ParagraphStatus.none:
          // 选中值清空
          selectedParagraph.value = null;
          // 重置键盘状态
          keyBoardActionStatus.value = KeyBoardActionStatus.none;
          // 重置字体横条状态
          textAttributeStatus.value = TextAttributeStatus.none;
      }
    });

    // 监听段落选择变化，更新允许插入序号的状态
    ever(selectedParagraph, (_) {
      if (paragraphStatus.value != ParagraphStatus.none) {
        updateAllowInsertSerialStatus();
      }
    });

    NiimbotEventBus.getDefault().register(this, (data) async {
      if (data is Map && data.containsKey("action")) {
        if (data["action"] == "uploadTemplate") {
          String templateId = data["templateId"];
          TemplateData serviceTemplate = await TemplateData.fromJson(jsonDecode(data["template"]));
          if (templateId == templateData().id) {
            _saveUpdateTemplateData(serviceTemplate);
          }
        }
      }
    });
    Future.delayed(Duration(milliseconds: 300), () {
      importExcelGuide?.call();
    });
  }

  @override
  void onClose() {
    super.onClose();
    C1FileBusiness.instance.removeSyncServiceTemplateFun(_syncServiceTemplateFun);
    _scrollControllers.values.forEach((element) {
      element.dispose();
    });
    TemplateDataTransform.dispose();
    C1FileBusiness.instance.isEditStatus = false;
  }

  /// 处理打印操作
  ///
  /// 检查模板内容是否合法，然后打开打印设置页面
  /// [context] - 当前BuildContext用于弹窗
  handlePrint(BuildContext context) async {
    // 检查是否有打印内容
    if ((templateData().values?.length ?? 0) == 0) {
      ToastUtil.showToast(context, intlanguage('app100001556', '当前没有打印内容'));
      return;
    }

    // 检查是否存在空白段落
    if (EditTemplateDataTransform.checkContainEmptyParagraph(templateData())) {
      ToastUtil.showToast(context, intlanguage('app100001555', '当前存在空白段落'));
      return;
    }

    // 加载耗材类型的通用打印配置
    int consumableType = templateData.value.consumableType;
    await PrintSettingConfig.loadCommonConfig(consumableType);

    // 启用全局手势，确保打印设置页可以正常交互
    Application.isEnableGesture = true;

    // 显示打印设置页面
    await showModalBottomSheet(
        context: context,
        barrierColor: Colors.black.withOpacity(0.35),
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        clipBehavior: Clip.antiAlias,
        enableDrag: false,
        isScrollControlled: true,
        builder: (_) {
          return C1PrintSettingPage(templateData.value, tubeType, tubeSpecs, textFormat);
        });

    // 恢复全局手势状态
    Application.isEnableGesture = false;
  }

  /// 更新序号插入按钮状态
  void updateAllowInsertSerialStatus() {
    // 初始状态默认不允许插入序号
    allowInsertSerial.value = false;

    // 检查选中段落是否存在
    if (selectedParagraphElement == null) {
      return;
    }

    // 获取当前段落信息
    CompositeValueType compositeValueType = selectedParagraphElement!;
    int columnIndex = selectedParagraph()!.item2;
    CompositeValueType paragraphValue = selectedParagraph()!.item1 as CompositeValueType;

    // 第一优先级：判断是否为双行模式，双行模式下两行只允许一行有序号
    bool isDoubleLine = textFormat == TextFormat.doubleLine;

    // 如果是双行模式，检查另一行是否存在序号
    if (isDoubleLine && paragraphValue.valueObjects!.length > 1) {
      // 确定另一行的索引
      int otherColumnIndex = columnIndex == 0 ? 1 : 0;
      // 获取另一行的值
      CompositeValueType otherColumnValue = paragraphValue.valueObjects![otherColumnIndex] as CompositeValueType;

      // 检查另一行是否包含序号
      for (var valueObj in otherColumnValue.valueObjects ?? []) {
        if (valueObj is SerialValueType) {
          // 如果另一行已有序号，则禁止当前行添加序号
          return;
        }
      }
    }

    // 第二优先级：检查当前行序号数量是否已达上限(3个)
    int serialCount = 0;
    for (var valueObj in compositeValueType.valueObjects ?? []) {
      if (valueObj is SerialValueType) {
        serialCount++;
      }
    }

    // 超过3个序号时不允许添加
    if (serialCount >= 3) {
      return;
    }

    // 第三优先级：检查序号组合是否超出1000段上限
    int totalCombinations = _calculateSerialCombinations(compositeValueType);
    if (totalCombinations >= 1000) {
      return;
    }

    // 通过所有检查，允许添加序号
    allowInsertSerial.value = true;
  }
}

/// 设置段落重复次数，复制段落、插入段落、删除段落
extension paragraphAction on C1EditLogic {
  ScrollController getParagraphScrollController(String paragraphId) {
    if (_scrollControllers.containsKey(paragraphId)) {
      return _scrollControllers[paragraphId]!;
    }
    _scrollControllers[paragraphId] = ScrollController();
    return _scrollControllers[paragraphId]!;
  }

  /// 设置段落重复次数
  setParagraphRepeatCount(BuildContext context, int index) {
    CompositeValueType compositeValueType = templateData().values![index] as CompositeValueType;
    String? repeatCount = compositeValueType.repeatCount?.toString();
    showEditTextDialog(context, intlanguage('app100001537', '设置重复数量'), repeatCount, 3, (value) {
      CompositeValueType compositeValueType = templateData().values![index] as CompositeValueType;
      templateData().values![index] = compositeValueType.copyWith(repeatCount: int.parse(value));
      update([compositeValueType.id!]);
      fileChanged = true;
    },
        keyboardType: TextInputType.number,
        textInputFormat: repeatCountInputFormat,
        selectAll: repeatCount?.isNotEmpty == true,
        subTitle: intlanguage('app100001538', '可输入范围1~200'),
        editTextEmptyToast: intlanguage('app100001543', '请输入重复数量'),
        barrierDismissible: false);
  }

  /// 新建段落
  createParagraph() {
    // 创建去除盲区的可打印宽高
    // 获取设置的宽高
    double width = templateData().width.toDouble();
    double height = templateData().height.toDouble();
    // 获取打印盲区
    List<num> margin = templateData().margin;
    bool autoWidth = templateData().tubeFileSetting?.autoWidth ?? false;
    Tuple2<List<TextElement>, ValueTypeProtocol> elementValuePair = EditTemplateDataTransform.createDefaultElementValue(
        width, height, margin, textFormat, autoWidth ? NetalTextAlign.start : fixLengthTextAlignHorizontal);
    if (templateData().tubeFileSetting?.autoWidth == true) {
      String paragraphId = elementValuePair.item2.id!;
      _scrollControllers[paragraphId] = ScrollController();
    }
    List<TextElement> textElements = elementValuePair.item1;
    ValueTypeProtocol value = elementValuePair.item2;
    // 添加新增段落文本格式到文件中
    if (templateData().values?.isNotEmpty == true) {
      CompositeValueType paragraph = templateData().values!.last as CompositeValueType;
      List<String> elementIds = paragraph.valueObjects!.map((e) => e.elementId!).toList();
      for (int i = 0; i < textElements.length; i++) {
        TextElement textElement = textElements[i];
        TextElement copyTextElement = templateData().elements.firstWhere((e) => e.id == elementIds[i]) as TextElement;
        textElements[i] = textElement.copyWith(
            fontSize: copyTextElement.fontSize,
            wordSpacing: copyTextElement.wordSpacing,
            letterSpacing: copyTextElement.letterSpacing,
            fontStyle: copyTextElement.fontStyle,
            typesettingMode: copyTextElement.typesettingMode,
            textAlignHorizontal: copyTextElement.textAlignHorizontal,
            textAlignVertical: copyTextElement.textAlignVertical,
            rotate: copyTextElement.rotate,
            boxStyle: copyTextElement.boxStyle);
      }
    }
    templateData().elements.addAll(textElements);
    // 添加新增段落文本数据到文件中
    templateData().values?.add(value);
    fileChanged = true;
  }

  /// 复制段落
  copyParagraph(int index) {
    List<TextElement> newElements = [];
    List<BaseElement> elements = templateData().elements;
    Tuple2<CompositeValueType, List<Tuple2<String, String>>> copyInfo =
        EditTemplateDataTransform.copyParagraph(templateData().values![index]);
    CompositeValueType compositeValueType = copyInfo.item1;
    List<Tuple2<String, String>> idInfoList = copyInfo.item2;
    idInfoList.forEach((idInfo) {
      TextElement element = elements.firstWhere((element) => element.id == idInfo.item1) as TextElement;
      newElements.add(element.copyWith(id: idInfo.item2));
    });
    if (templateData().tubeFileSetting?.autoWidth == true) {
      String paragraphId = compositeValueType.id!;
      _scrollControllers[paragraphId] = ScrollController();
    }
    templateData().elements.addAll(newElements);
    if (index + 1 == templateData().values!.length) {
      templateData().values!.add(compositeValueType);
    } else {
      templateData().values!.insert(index + 1, compositeValueType);
    }
    fileChanged = true;
  }

  /// 插入段落
  insertParagraph(int index) {
    // 创建去除盲区的可打印宽高
    // 获取设置的宽高
    double width = templateData().width.toDouble();
    double height = templateData().height.toDouble();
    // 获取打印盲区
    List<num> margin = templateData().margin;
    bool autoWidth = templateData().tubeFileSetting?.autoWidth ?? false;
    Tuple2<List<TextElement>, ValueTypeProtocol> elementValuePair = EditTemplateDataTransform.createDefaultElementValue(
        width, height, margin, textFormat, autoWidth ? NetalTextAlign.start : fixLengthTextAlignHorizontal);
    if (templateData().tubeFileSetting?.autoWidth == true) {
      String paragraphId = elementValuePair.item2.id!;
      _scrollControllers[paragraphId] = ScrollController();
    }
    List<TextElement> textElements = elementValuePair.item1;
    ValueTypeProtocol value = elementValuePair.item2;
    CompositeValueType paragraph = templateData().values![index] as CompositeValueType;
    List<String> elementIds = paragraph.valueObjects!.map((e) => e.elementId!).toList();
    for (int i = 0; i < textElements.length; i++) {
      TextElement textElement = textElements[i];
      TextElement copyTextElement = templateData().elements.firstWhere((e) => e.id == elementIds[i]) as TextElement;
      textElements[i] = textElement.copyWith(
          fontSize: copyTextElement.fontSize,
          wordSpacing: copyTextElement.wordSpacing,
          letterSpacing: copyTextElement.letterSpacing,
          fontStyle: copyTextElement.fontStyle,
          typesettingMode: copyTextElement.typesettingMode,
          textAlignHorizontal: copyTextElement.textAlignHorizontal,
          textAlignVertical: copyTextElement.textAlignVertical,
          rotate: copyTextElement.rotate,
          boxStyle: copyTextElement.boxStyle);
    }
    // 添加新增段落文本格式到文件中
    templateData().elements.addAll(textElements);
    templateData().values!.insert(index, value);
    fileChanged = true;
  }

  /// 删除段落
  deleteParagraph(BuildContext context, int index, Function refreshFun) {
    CompositeValueType value = templateData().values![index] as CompositeValueType;
    VoidCallback deleteAction = () {
      String paragraphId = value.id!;
      value.valueObjects?.forEach((value) {
        BaseElement element = templateData().elements.firstWhere((element) => element.id == value.elementId);
        templateData().elements.remove(element);
      });
      templateData().values!.removeAt(index);
      if (templateData().tubeFileSetting?.autoWidth == true) {
        if (_scrollControllers.containsKey(paragraphId)) {
          _scrollControllers[paragraphId]!.dispose();
          _scrollControllers.remove(paragraphId);
        }
      }
      refreshFun();
      fileChanged = true;
    };
    if (EditTemplateDataTransform.checkParagraphEmpty(value)) {
      deleteAction();
      return;
    }
    showCustomDialog(context, intlanguage('app100001656', '确定要删除当前段落吗？'), '',
        leftFunStr: intlanguage('app00030', '取消'),
        rightFunStr: intlanguage('app00063', '删除'),
        rightTextColor: ThemeColor.brand, rightFunCall: () {
      deleteAction();
    });
  }
}

/// 编辑文本，更新文本值或属性
extension editElement on C1EditLogic {
  /// 从值对象列表更新段落的文件结构
  updateValueObjectsFromList(
      {required List<ValueTypeProtocol> valueObjects, ValueChanged<Tuple2<bool, double>>? overFlowClosure}) {
    CompositeValueType compositeValueType = selectedParagraph()!.item1 as CompositeValueType;
    int selectColumnIndex = selectedParagraph()!.item2;
    Tuple2<NetalImageResult?, BaseElement?> resultOld =
        generateElementImage(parentValue: compositeValueType, columnIndex: selectColumnIndex, isUseImageCache: true);

    // 直接更新值对象列表
    selectedParagraphElement?.valueObjects = valueObjects;

    // 生成新的图片
    Tuple2<NetalImageResult?, BaseElement?> result =
        generateElementImage(parentValue: compositeValueType, columnIndex: selectColumnIndex, isUseImageCache: false);

    // 查看是否当前宽度溢出模版的安全宽度,区分元素的横排竖排
    bool isOverFlow = false;
    if ((result.item2 as TextElement).typesettingMode == NetalTypesettingMode.horizontal) {
      isOverFlow = (result.item2 as TextElement).value.isNotEmpty &&
          TemplateDataTransform.px2mm(result.item1?.width ?? 0) > safeWidth;
    } else if ((result.item2 as TextElement).typesettingMode == NetalTypesettingMode.vertical) {
      isOverFlow = (result.item2 as TextElement).value.isNotEmpty &&
          TemplateDataTransform.px2mm(result.item1?.height ?? 0) > safeWidth;
    }

    // 刷新元素预览显示
    update([selectedParagraph()?.item1.id ?? '']);

    // 计算宽度变化
    double dWidth = 0;
    if ((result.item2 as TextElement).typesettingMode == NetalTypesettingMode.vertical) {
      int widthOld = resultOld.item1?.height ?? 0;
      int widthNew = result.item1?.height ?? 0;
      if (widthNew != 0 && widthOld != 0) {
        dWidth = TemplateDataTransform.px2dp(widthNew - widthOld);
      }
    } else {
      int widthOld = resultOld.item1?.width ?? 0;
      int widthNew = result.item1?.width ?? 0;
      if (widthNew != 0 && widthOld != 0) {
        dWidth = TemplateDataTransform.px2dp(widthNew - widthOld);
      }
    }

    // 回调是否溢出
    overFlowClosure?.call(Tuple2(isOverFlow, dWidth));

    // 如果编辑段落已经有序号，则不允许再次插入序号
    CompositeValueType columnValue = selectedParagraphElement!;
    updateAllowInsertSerialStatus();
    fileChanged = true;
  }

  /// 更新元素值
  updateElementValue({int index = 0, required String textValue, ValueChanged<Tuple2<bool, double>>? overFlowClosure}) {
    // 获取段落以及段落下标
    CompositeValueType compositeValueType = selectedParagraph()!.item1 as CompositeValueType;
    int selectColumnIndex = selectedParagraph()!.item2;
    // 更新段落下的选中行值
    ValueTypeProtocol? updateValueType =
        _updateValue(textValue: textValue, valueType: selectedParagraphElement, index: index);
    compositeValueType.valueObjects![selectColumnIndex] = updateValueType!;
    fileChanged = true;
  }

  updateElementValueNew({required String paragraphId, ValueChanged<Tuple2<bool, double>>? overFlowClosure}) {
    if (timer != null) {
      timer?.cancel();
    }
    timer = Timer(Duration(milliseconds: 50), () {
      CompositeValueType compositeValueType = selectedParagraph()!.item1 as CompositeValueType;
      int selectColumnIndex = selectedParagraph()!.item2;
      Tuple2<NetalImageResult?, BaseElement?> resultOld =
          generateElementImage(parentValue: compositeValueType, columnIndex: selectColumnIndex, isUseImageCache: true);
      // 生成元素预览显示
      Tuple2<NetalImageResult?, BaseElement?> result =
          generateElementImage(parentValue: compositeValueType, columnIndex: selectColumnIndex, isUseImageCache: false);
      // 查看是否当前宽度溢出模版的安全宽度
      bool isOverFlow = TemplateDataTransform.px2mm(result.item1?.width ?? 0) > safeWidth;
      // 刷新元素预览显示
      update([selectedParagraph()?.item1.id ?? '']);
      double dWidth = 0;
      int widthOld = resultOld.item1?.width ?? 0;
      int widthNew = result.item1?.width ?? 0;
      if (widthNew != 0 && widthOld != 0) {
        dWidth = TemplateDataTransform.px2dp(widthNew - widthOld);
      }
      // 回调是否溢出
      overFlowClosure?.call(Tuple2(isOverFlow, dWidth));
      // 重置timer
      timer = null;
    });
    fileChanged = true;
  }

  /// 更新所有段落对应行的文本属性
  /// 刷新段落文本的图片缓存、宽高和x、y坐标需要调用图像库接口（调用图像库接口耗时20ms左右），所有段落全部刷新存在效率问题
  /// 所以只刷新可见范围段落文本的图片缓存、宽高和x、y坐标，后面保存或打印时再统一刷新
  updateAllParagraphTextAttribute(
      {FontSizeConfig? fontSize,
      double? letterSpacing,
      bool? textBold,
      bool? textUnderline,
      NetalTextAlign? textAlignHorizontal,
      NetalTypesettingMode? typesettingMode}) {
    List<CompositeValueType> allParagraphs = templateData().values!.map((e) => e as CompositeValueType).toList();
    int currentIndex = 0;
    try {
      CompositeValueType paragraph = selectedParagraph()!.item1 as CompositeValueType;
      currentIndex = allParagraphs.indexOf(paragraph);
    } catch (e) {}
    //可见范围起始段落索引
    int startIndex;
    //可见范围结束段落索引
    int endIndex;
    if (currentIndex <= 4) {
      startIndex = 0;
    } else {
      if (currentIndex + 4 <= (allParagraphs.length - 1)) {
        startIndex = currentIndex - 4;
      } else {
        startIndex = max(allParagraphs.length - 1 - 8, 0);
      }
    }
    endIndex = min(startIndex + 8, allParagraphs.length - 1);
    int columnIndex = 0;
    try {
      columnIndex = selectedParagraph()!.item2;
    } catch (e) {}
    List<BaseElement> elements = templateData().elements;
    List<int> allElementIndex = [];
    //刷新所有段落行的文本属性
    for (int i = 0; i < allParagraphs.length; i++) {
      CompositeValueType paragraph = allParagraphs[i];
      CompositeValueType paragraphColumn = paragraph.valueObjects?[columnIndex] as CompositeValueType;
      allElementIndex.add(elements.indexWhere((e) => e.id == paragraphColumn.elementId));
    }
    TextElement selectText = elements.first as TextElement;
    try {
      selectText = elements.firstWhere((e) => e.id == selectedParagraphElement!.elementId) as TextElement;
    } catch (e) {}
    List<NetalTextFontStyle> fontStyle = selectText.fontStyle;
    if (textBold != null) {
      if (textBold) {
        if (!fontStyle.contains(NetalTextFontStyle.bold)) {
          fontStyle.add(NetalTextFontStyle.bold);
        }
      } else {
        if (fontStyle.contains(NetalTextFontStyle.bold)) {
          fontStyle.remove(NetalTextFontStyle.bold);
        }
      }
    }
    if (textUnderline != null) {
      if (textUnderline) {
        if (!fontStyle.contains(NetalTextFontStyle.underline)) {
          fontStyle.add(NetalTextFontStyle.underline);
        }
      } else {
        if (fontStyle.contains(NetalTextFontStyle.underline)) {
          fontStyle.remove(NetalTextFontStyle.underline);
        }
      }
    }
    // typesettingMode不为空时&&判断当前段落的element的文本方向和参数方向是否一致，不一致则需翻转宽高以及翻转270度或者还原成0度，
    // 此处的rotate用于图像库预览时旋转由于应用层处理，后续将 rotate 处理成 0 给图像库
    num? rotate;
    NetalTextBoxStyle? boxStyle;
    NetalTextAlign? textAlignV;
    if (typesettingMode != null && selectText.typesettingMode != typesettingMode) {
      boxStyle = typesettingMode == NetalTypesettingMode.horizontal
          ? NetalTextBoxStyle.autoWidth
          : NetalTextBoxStyle.autoHeight;
      rotate = typesettingMode == NetalTypesettingMode.horizontal ? 0 : 270;
      // 单个元素预览在竖排下顶对齐，防止高度大的情况导致的截取空白问题
      textAlignV = typesettingMode == NetalTypesettingMode.horizontal ? NetalTextAlign.center : NetalTextAlign.start;
    }
    for (int i = 0; i < allElementIndex.length; i++) {
      int index = allElementIndex[i];
      elements[index] = (elements[index] as TextElement).copyWith(
          fontSize: fontSize?.mm,
          wordSpacing: letterSpacing,
          letterSpacing: letterSpacing,
          fontStyle: fontStyle,
          typesettingMode: typesettingMode,
          textAlignHorizontal: textAlignHorizontal,
          textAlignVertical: textAlignV,
          rotate: rotate,
          boxStyle: boxStyle,
          imageCache: CopyWrapper.value(null));
    }
    //刷新可见范围段落文本的图片缓存、宽高和x、y坐标
    for (int i = startIndex; i <= endIndex; i++) {
      CompositeValueType paragraph = allParagraphs[i];
      generateElementImage(parentValue: paragraph, columnIndex: columnIndex, isUseImageCache: false);
      update([paragraph.id!]);
    }
    if (fontSize != null) {
      selectTextFontSize = fontSize.mm;
    }
    if (textBold != null) {
      selectTextBold = textBold;
    }
    if (textUnderline != null) {
      selectTextUnderline = textUnderline;
    }
    if (textAlignHorizontal != null) {
      selectTextAlignH = textAlignHorizontal == NetalTextAlign.start;
    }
    if (typesettingMode != null) {
      selectTextVerticalStyle = typesettingMode == NetalTypesettingMode.vertical;
    }
    fileChanged = true;
    update([selectedParagraphElement?.id ?? '']);
  }

  /// 更新当前当落选中行的文本属性
  updateCurrentParagraphTextAttribute(
      {FontSizeConfig? fontSize,
      double? letterSpacing,
      bool? textBold,
      bool? textUnderline,
      NetalTextAlign? textAlignHorizontal,
      NetalTypesettingMode? typesettingMode}) {
    CompositeValueType paragraph = selectedParagraph()!.item1 as CompositeValueType;
    int columnIndex = selectedParagraph()!.item2;
    updateParagraphTextAttribute(paragraph, columnIndex,
        fontSize: fontSize,
        letterSpacing: letterSpacing,
        textBold: textBold,
        textUnderline: textUnderline,
        textAlignHorizontal: textAlignHorizontal,
        typesettingMode: typesettingMode);
    // 生成新的图片
    // 生成元素预览显示
    generateElementImage(parentValue: paragraph, columnIndex: columnIndex, isUseImageCache: false);
    // 刷新元素预览显示
    update([paragraph.id!]);
    fileChanged = true;
  }

  ///更新指定段落指定行的文本属性
  updateParagraphTextAttribute(CompositeValueType paragraph, int columnIndex,
      {FontSizeConfig? fontSize,
      double? letterSpacing,
      bool? textBold,
      bool? textUnderline,
      NetalTextAlign? textAlignHorizontal,
      NetalTypesettingMode? typesettingMode}) {
    CompositeValueType paragraphColumn = paragraph.valueObjects?[columnIndex] as CompositeValueType;
    // 寻找当前选中段落的element
    TextElement element = templateData()
        .elements
        .where((element) => element.id == paragraphColumn.elementId)
        .toList()
        .first as TextElement;
    // element所在index，后续替换后需回写
    int index = templateData().elements.indexOf(element);
    List<NetalTextFontStyle> fontStyle = element.fontStyle;
    if (textBold != null) {
      if (textBold) {
        if (!fontStyle.contains(NetalTextFontStyle.bold)) {
          fontStyle.add(NetalTextFontStyle.bold);
        }
      } else {
        if (fontStyle.contains(NetalTextFontStyle.bold)) {
          fontStyle.remove(NetalTextFontStyle.bold);
        }
      }
    }
    if (textUnderline != null) {
      if (textUnderline) {
        if (!fontStyle.contains(NetalTextFontStyle.underline)) {
          fontStyle.add(NetalTextFontStyle.underline);
        }
      } else {
        if (fontStyle.contains(NetalTextFontStyle.underline)) {
          fontStyle.remove(NetalTextFontStyle.underline);
        }
      }
    }
    // typesettingMode不为空时&&判断当前段落的element的文本方向和参数方向是否一致，不一致则需翻转宽高以及翻转270度或者还原成0度，
    // 此处的rotate用于图像库预览时旋转由于应用层处理，后续将 rotate 处理成 0 给图像库
    num? rotate;
    NetalTextBoxStyle? boxStyle;
    NetalTextAlign? textAlignV;
    if (typesettingMode != null && element.typesettingMode != typesettingMode) {
      boxStyle = typesettingMode == NetalTypesettingMode.horizontal
          ? NetalTextBoxStyle.autoWidth
          : NetalTextBoxStyle.autoHeight;
      rotate = typesettingMode == NetalTypesettingMode.horizontal ? 0 : 270;
      // 单个元素预览在竖排下顶对齐，防止高度大的情况导致的截取空白问题
      textAlignV = typesettingMode == NetalTypesettingMode.horizontal ? NetalTextAlign.center : NetalTextAlign.start;
    }
    // 当前只支持TextElement，直接进行类型转换, 赋予设置的属性值
    TextElement textElement = element.copyWith(
        fontSize: fontSize?.mm,
        wordSpacing: letterSpacing,
        letterSpacing: letterSpacing,
        fontStyle: fontStyle,
        typesettingMode: typesettingMode,
        textAlignHorizontal: textAlignHorizontal,
        textAlignVertical: textAlignV,
        rotate: rotate,
        boxStyle: boxStyle,
        imageCache: CopyWrapper.value(null));
    textInfo.fontSize = textElement.fontSize;
    textInfo.wordSpacing = textElement.wordSpacing;
    textInfo.letterSpacing = textElement.letterSpacing;
    textInfo.fontStyle = textElement.fontStyle;
    textInfo.typesettingMode = textElement.typesettingMode;
    textInfo.textAlignHorizontal = textElement.textAlignHorizontal;
    textInfo.textAlignVertical = textElement.textAlignVertical;
    textInfo.rotate = textElement.rotate;
    textInfo.boxStyle = textElement.boxStyle;
    // 回写新的文本属性
    templateData().elements[index] = textElement;
  }

  /// 根据类型回写值
  ValueTypeProtocol? _updateValue({required String textValue, required ValueTypeProtocol? valueType, int index = 0}) {
    ValueTypeProtocol? _valueType;
    switch (valueType?.type) {
      case TemplateValueType.text:
        _valueType = (valueType as TextValueType).copyWith(value: textValue);
        break;
      case TemplateValueType.dText:
        // TODO: Handle this case.
        break;
      case TemplateValueType.date:
        // TODO: Handle this case.
        break;
      case TemplateValueType.composite:
        CompositeValueType columnValue = CompositeValueType.fromJson(valueType!.toJson());
        columnValue.valueObjects![index] =
            (columnValue.valueObjects![index] as TextValueType).copyWith(value: textValue);
        _valueType = columnValue;
        break;
      case TemplateValueType.serial:
        // TODO: Handle this case.
        break;
      case TemplateValueType.image:
        // TODO: Handle this case.
        break;
      case TemplateValueType.barcode:
        // TODO: Handle this case.
        break;
      case TemplateValueType.qrcode:
        // TODO: Handle this case.
        break;
      default:
        break;
    }
    return _valueType;
  }
}

/// 编辑文本，更新文本值或属性
extension serial on C1EditLogic {
  ///插入序号
  Future<SerialValueType?> insertSerial(BuildContext context) async {
    // 获取当前段落对象
    CompositeValueType compositeValueType = selectedParagraphElement!;

    // 检查当前段落中的序号数量
    int serialCount = 0;
    for (var valueObj in compositeValueType.valueObjects ?? []) {
      if (valueObj is SerialValueType) {
        serialCount++;
      }
    }

    // 如果序号数量已达到3个，显示提示并返回
    if (serialCount >= 3) {
      ToastUtil.showToast(context, intlanguage('app100001969', '每个段落最多添加3组序号'));
      return null;
    }

    // 计算当前段落中所有序号组合会生成的总数量
    int totalCombinations = _calculateSerialCombinations(compositeValueType);

    // 如果添加新序号后组合总数会超过1000，显示提示并返回
    if (totalCombinations > 1000) {
      ToastUtil.showToast(context, intlanguage('app100001968', '最多创建1000段序号'));
      return null;
    }

    String id = compositeValueType.id! + "_s${serialCount + 1}";
    String elementId = compositeValueType.elementId!;
    SerialValueType serialValueType = SerialValueType(
        type: TemplateValueType.serial,
        id: id,
        elementId: elementId,
        startNumber: 1,
        fixValue: 0,
        fixLength: 2,
        incrementValue: 1);
    var value = await adjust_bottom_sheet.showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.35),
      enableDrag: false,
      isScrollControlled: true,
      builder: (_) => C1EditSerialPage(
        serialValueType: serialValueType,
        adjustKeyboard: keyBoardActionStatus() == KeyBoardActionStatus.symbol,
        checkAddEditCondition: (serialCounter, serialContext) {
          int counter = C1PrintManager.instance.getColumnSerialCount(compositeValueType) * serialCounter;
          if (counter > 1000) {
            ToastUtil.showToast(serialContext, intlanguage('app100001968', '最多创建1000段序号'));
            return false;
          }
          return true;
        },
      ),
    );
    if (value != null) {
      SerialValueType serialValueType = value["serial"] as SerialValueType;
      String format = value["format"];

      // // 添加新序号后，再次检查组合总数
      // int newTotalCombinations = _calculateSerialCombinations(compositeValueType, newSerial: serialValueType);
      // if (newTotalCombinations > 1000) {
      //   ToastUtil.showToast(context, intlanguage('app100001968', '最多创建1000段序号'));
      //   return null;
      // }

      // 发送埋点数据
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "click",
        "posCode": "129_437_427",
        "ext": {"b_name": format}
      });

      // 标记文件已更改
      fileChanged = true;

      // 返回用户配置的序列号值
      return serialValueType;
    }
    return null;
  }

  /// 计算序号组合会生成的总数量
  /// 如果提供了newSerial参数，则计算加入这个新序号后的组合总数
  int _calculateSerialCombinations(CompositeValueType compositeValueType, {SerialValueType? newSerial}) {
    // 获取当前段落中所有的序号
    List<SerialValueType> serials = [];
    bool newSerialExists = false;

    // 遍历段落中的值对象
    for (var valueObj in compositeValueType.valueObjects ?? []) {
      if (valueObj is SerialValueType) {
        // 如果提供了newSerial并且当前序号与newSerial具有相同ID
        if (newSerial != null && valueObj.id == newSerial.id) {
          // 使用newSerial替代当前序号（用于更新序号时的计算）
          serials.add(newSerial);
          newSerialExists = true;
        } else {
          // 否则添加当前序号
          serials.add(valueObj);
        }
      }
    }

    // 如果提供了新序号且它不存在于当前序号列表中，才添加它（用于新增序号时的计算）
    if (newSerial != null && !newSerialExists) {
      serials.add(newSerial);
    }

    if (serials.isEmpty) {
      return 0;
    }

    // 当只有一个序号时，计算其自身的数量
    if (serials.length == 1) {
      SerialValueType serial = serials.first;
      if (serial.endNumber != null && serial.startNumber != null && serial.incrementValue != null) {
        int count = ((serial.endNumber! - serial.startNumber!) ~/ (serial.incrementValue ?? 1)) + 1;
        return count > 0 ? count : 1;
      }
      return 1;
    }

    // 计算所有序号的排列组合总数
    // 对于多个序号，每个序号的每个值都会与其他序号的每个值组合
    // 因此总组合数是各个序号可能值数量的乘积
    int totalCombinations = 1;
    for (var serial in serials) {
      int count = 1; // 默认一个序号只有一个值
      if (serial.endNumber != null && serial.startNumber != null && serial.incrementValue != null) {
        // 计算这个序号从开始值到结束值共有多少个值
        count = ((serial.endNumber! - serial.startNumber!) ~/ (serial.incrementValue ?? 1)) + 1;
        // 确保计数不为负数
        if (count < 1) count = 1;
      }
      // 乘法原理：总组合数等于各个位置可能选择的数量相乘
      totalCombinations *= count;
    }

    return totalCombinations;
  }

  ///编辑序号
  Future<SerialValueType?> editSerial(BuildContext context, SerialValueType serialValueParam) async {
    CompositeValueType paragraphValue = selectedParagraph()!.item1 as CompositeValueType;
    int paragraphIndex = templateData().values!.indexWhere((e) => e.id == paragraphValue.id);
    int columnIndex = selectedParagraph()!.item2;
    CompositeValueType columnValue = selectedParagraphElement!;

    // Find the index of the serialValueParam in columnValue's valueObjects
    int serialIndex = columnValue.valueObjects!.indexWhere((e) => e is SerialValueType && e.id == serialValueParam.id);

    // If not found, try to find any SerialValueType
    if (serialIndex == -1) {
      serialIndex = columnValue.valueObjects!.indexWhere((e) => e is SerialValueType);
    }

    // If still not found, return null
    if (serialIndex == -1) {
      return null;
    }

    SerialValueType serialValueType = columnValue.valueObjects![serialIndex] as SerialValueType;

    var value = await adjust_bottom_sheet.showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.35),
      enableDrag: false,
      isScrollControlled: true,
      builder: (_) => C1EditSerialPage(
        serialValueType: serialValueType,
        adjustKeyboard: keyBoardActionStatus() == KeyBoardActionStatus.symbol,
        checkAddEditCondition: (serialCounter, serialContext) {
          int counter = C1PrintManager.instance.getColumnSerialCount(columnValue) ~/
              serialValueType.getSerialCount() *
              serialCounter;
          if (counter > 1000) {
            ToastUtil.showToast(serialContext, intlanguage('app100001968', '最多创建1000段序号'));
            return false;
          }
          return true;
        },
      ),
    );
    if (value != null) {
      SerialValueType editSerial = value["serial"] as SerialValueType;

      // // 当序号组合已经达到1000段时不允许添加
      // int totalCombinations = _calculateSerialCombinations(columnValue, newSerial: editSerial);
      // if (totalCombinations > 1000) {
      //   allowInsertSerial.value = false;
      //   ToastUtil.showToast(context, intlanguage('app100001968', '最多创建1000段序号'));
      //   return null;
      // }

      // Update the serial value in the columnValue
      columnValue.valueObjects![serialIndex] = editSerial;
      paragraphValue.valueObjects![columnIndex] = columnValue;
      templateData().values![paragraphIndex] = paragraphValue;

      // 生成新的图片
      // 生成元素预览显示
      generateElementImage(parentValue: paragraphValue, columnIndex: columnIndex, isUseImageCache: false);
      update([paragraphValue.id!]);

      // 更新允许插入序号的状态
      updateAllowInsertSerialStatus();

      fileChanged = true;
      return editSerial;
    }
    return null;
  }

  previewParagraphSerial(BuildContext context, int index) {
    List<TemplateData> previewTemplateList =
        PrintTemplateDataTransform.generateParagraphBatchPreviewTemplate(templateData(), index);
    adjust_bottom_sheet.showModalBottomSheet(
        context: context,
        barrierColor: Colors.black.withOpacity(0.35),
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
        clipBehavior: Clip.antiAlias,
        builder: (_) => C1PreviewSerialPage(previewTemplateList: previewTemplateList));
  }
}

extension symbol on C1EditLogic {
  /// 插入符号
  insertSymbol({required String symbolCode}) {
    try {
      // 当前编辑的子段落模型
      CompositeValueType compositeValueType = selectedParagraphElement!;
      // 插入的符号模型ID
      String id = compositeValueType.id! + "_100";
      // 插入的符号元素ID, 由于元素不可自定义文本属性值所以可复用段落元素ID
      String? elementId = compositeValueType.elementId;
      TextValueType textValueType = TextValueType(
        id: id,
        elementId: elementId,
        type: TemplateValueType.text,
        value: String.fromCharCode(int.parse(symbolCode)),
      );
      // 子段落添加符号模型
      compositeValueType.valueObjects?.add(textValueType);
      // 图像库刷新元素显示
      int selectColumnIndex = selectedParagraph()!.item2;
      generateElementImage(
          parentValue: selectedParagraph()!.item1, columnIndex: selectColumnIndex, isUseImageCache: false);
      // 刷新元素预览显示
      update([selectedParagraph()?.item1.id ?? '']);
      fileChanged = true;
    } catch (e, s) {
      print('Exception details:\n $e');
      print('Stack trace:\n $s');
    }
  }
}

/// 图片生成
extension generateImage on C1EditLogic {
  /// 生成元素预览
  /// [value] 元素值类型
  /// [isUseImageCache] 是否忽略图片缓存
  Tuple2<NetalImageResult?, BaseElement?> generateElementImage(
      {required ValueTypeProtocol? parentValue, required int columnIndex, bool isUseImageCache = true}) {
    // 获取当前columnIndex下的element以及value
    CompositeValueType? compositeValueType =
        (parentValue != null && parentValue is CompositeValueType) ? parentValue : null;
    ValueTypeProtocol? value = compositeValueType?.valueObjects?[columnIndex];
    int index = templateData().elements.indexWhere((e) => e.id == value!.elementId);
    TextElement textElement = templateData().elements[index] as TextElement;
    // 查看是否存在缓存且是否使用图片缓存
    if (textElement.imageCache != null && isUseImageCache == true) {
      return Tuple2(textElement.imageCache, textElement);
    }
    // 获取模版盲区
    List<double> margin = templateData().margin.map((e) => e.toDouble()).toList();
    // 获取渲染结果
    final res = TemplateDataTransform.generateElementImage(
        width: safeWidth, height: safeHeight, element: textElement, valueType: value, usePlaceHolder: true);
    String? textValue = res?.item1;
    NetalImageResult? result = res?.item2;
    // 元素px转换为mm宽高
    double elementWidth = TemplateDataTransform.px2mm(result?.width ?? 0);
    double elementHeight = TemplateDataTransform.px2mm(result?.height ?? 0);
    if (elementHeight != 0) {
      textInfo.height = elementHeight;
    }
    // 根据对齐方式确认x、y坐标,要加上盲区区域
    double x = textElement.textAlignHorizontal == NetalTextAlign.center
        ? (safeWidth - elementWidth) / 2.0 + margin[3]
        : textElement.typesettingMode == NetalTypesettingMode.vertical
            ? (elementHeight / 2 - elementWidth / 2) + margin[3].toDouble()
            : margin[3].toDouble();
    // 区分在自动长度下竖排增长坐标
    if (templateData().tubeFileSetting?.autoWidth == true &&
        textElement.typesettingMode == NetalTypesettingMode.vertical) {
      // 计算旋转之前的x值
      // 画布大小 = height + 左右盲区
      double canvasWidth = elementHeight + margin[1] + margin[3];
      x = (canvasWidth - elementWidth) / 2.0;
    }
    double y;
    if (textFormat == TextFormat.singleLine) {
      y = (safeHeight - elementHeight) / 2.0;
    } else {
      if (columnIndex == 0) {
        //虚线框1.5dp
        y = (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(1.5);
      } else {
        //中间虚线1.0/2（上下各占一半）+ 虚线框1.5dp
        y = templateData().height / 2.0 + (safeHeight - elementHeight) / 2.0 + TemplateDataTransform.dp2mm(2.0);
      }
    }
    // 更新内存的element，用于返回值
    textElement = textElement.copyWith(
        x: x,
        y: y,
        width: elementWidth,
        height: elementHeight,
        value: textValue ?? textElement.value,
        imageCache: CopyWrapper<NetalImageResult?>.value(result));
    templateData().elements[index] = textElement;
    return Tuple2(result, textElement);
  }
}

extension fileSetting on C1EditLogic {
  /// 打开文件设置页面
  ///
  /// 处理模板文件属性设置，包括模板名称、宽度类型设置（自动/固定）、导入Excel数据等
  /// [context] - 当前BuildContext用于弹窗
  /// [paragraphController] - 段落滚动控制器，用于在设置变更后重置滚动位置
  toFileSettingPage(BuildContext context, ScrollController paragraphController) async {
    // 启用全局手势，确保设置页面可以正常交互
    Application.isEnableGesture = true;

    // 获取当前模板基础信息
    String templateName = templateData().name;
    bool autoWidth = templateData().tubeFileSetting?.autoWidth ?? false;
    int templateWidth = autoWidth ? 30 : templateData().width.toInt();
    int importStatus = _getImportStatus();
    String? importFileName = templateData().tubeFileSetting?.lastImportedFileName;
    String? currentSpecId = templateData().tubeFileSetting?.specId;

    // 显示设置页面并等待结果
    var value = await adjust_bottom_sheet.showModalBottomSheet(
      context: context,
      barrierColor: Colors.black.withOpacity(0.35),
      isDismissible: false,
      enableDrag: false,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
      clipBehavior: Clip.antiAlias,
      builder: (_) => C1FileSettingPage(
        templateName: templateName,
        autoWidth: autoWidth,
        templateWidth: templateWidth,
        tubeFileSetting: templateData().tubeFileSetting,
        importStatus: importStatus,
        importFileName: importFileName,
        importExcelFun: (fileName, excelContent) async {
          // Excel导入处理函数，更新模板数据
          templateData.value = await EditTemplateDataTransform.createAllParagraphFromExcel(
              templateData(), textInfo, fileName, excelContent);
          return 0;
        },
        consumableCode: templateData().consumableType,
      ),
    );

    // 恢复全局手势状态
    Application.isEnableGesture = false;

    // 如果用户取消了设置，则直接返回
    if (value == null) {
      return;
    }

    // 处理设置结果，settingType=2表示批量导入Excel
    int settingType = value["settingType"];
    if (settingType == 2) {
      // 批量导入Excel成功，重置滚动位置并显示成功提示
      paragraphController.jumpTo(0);
      String msg = intlanguage('app100001940', '导入成功');
      ToastUtil.showSuccessToast(context, msg);
      fileChanged = true;
      ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "129_438_428"});
    } else {
      // 处理其他文件设置（如模板名称、宽度等）
      String templateNameNew = value["templateName"];
      bool autoWidthNew = value["autoWidth"];
      int templateWidthNew = value["templateWidth"];
      Consumable? consumable = value["consumable"];
      ConsumableSpec? consumableSpecification = value["consumableSpecification"];
      templateData.value = templateData().copyWith(name: templateNameNew);

      // 检查是否选择了新的耗材规格
      bool consumableChanged =
          consumable != null && consumableSpecification != null && (currentSpecId != consumableSpecification.id);

      // 只有在耗材规格变更时才更新耗材信息显示
      if (consumableChanged) {
        consumableInfo =
            intlanguage(consumable.parentProperty?.multilingualCode ?? '', consumable.parentProperty?.name ?? '') +
                ': ' +
                (consumableSpecification.name ?? '');
      }

      // 检查长度设置是否有变更
      bool lengthInfoChanged = _fileLengthChanged(autoWidth, autoWidthNew, templateWidth, templateWidthNew);

      // 如果长度或耗材设置有变更，需要重新计算并更新模板信息
      if (lengthInfoChanged || consumableChanged) {
        // 重置滚动位置
        paragraphController.jumpTo(0);
        await Future.delayed(const Duration(milliseconds: 20));

        // 更新文件宽度类型设置
        TubeFileSetting? tubeFileSetting;
        if (autoWidthNew) {
          tubeFileSetting = templateData().tubeFileSetting ?? TubeFileSetting();
          tubeFileSetting.autoWidth = true;
          tubeFileSetting.width = 30;
        } else {
          tubeFileSetting = templateData().tubeFileSetting;
          tubeFileSetting?.autoWidth = false;
          tubeFileSetting?.width = templateWidthNew;
        }

        // 只有在耗材规格变更时才更新耗材相关信息
        if (consumableChanged) {
          tubeFileSetting?.specId = consumableSpecification.id;
          tubeFileSetting?.specName = consumableSpecification.name;
        }

        // 固定长度改为自动长度，模板宽度默认设置为30mm
        if (!autoWidth && autoWidthNew) {
          templateWidthNew = 30;
        }

        // 更新模板数据 - 使用更优雅的方式创建参数
        templateData.value = templateData().copyWith(
          name: templateNameNew,
          width: templateWidthNew,
          // 条件参数：只在耗材变更时传入新值，否则保持原值
          height: consumableChanged ? consumableSpecification.height?.toDouble() : null,
          margin: consumableChanged ? consumableSpecification.margin : null,
          consumableType: consumableChanged ? consumable.parentProperty?.code : null,
          // 在耗材变更时更新backgroundImage
          backgroundImage: consumableChanged ? CopyWrapper.value(consumableSpecification.previewImageUrl) : null,
          tubeFileSetting: CopyWrapper.value(tubeFileSetting),
          elements: templateData().elements,
          values: templateData().values,
        );

        // 重新初始化模板数据转换工具并计算安全区域
        TemplateDataTransform.init(context);
        TemplateDataTransform.generateDesignRatio(templateData().width.toDouble(), templateData().height.toDouble());
        safeWidth = TemplateDataTransform.getTemplateEditSafeWidth(templateData());
        safeHeight = TemplateDataTransform.getTemplateEditSafeHeight(templateData());

        // 重新计算字体大小配置
        fontSizeConfigs = EditTemplateDataTransform.calcParagraphMaxFontSize(
            templateWidthNew.toDouble(), templateData().height.toDouble(), templateData().margin, textFormat);

        // 更新文本信息
        textInfo.width = safeWidth;

        // 更新长度信息显示和对齐方式
        if (autoWidthNew) {
          lengthInfo = '';
        } else {
          lengthInfo = ' ${intlanguage('app100001513', '长')}${templateData().width.toInt()}mm';
        }

        // 只有在耗材规格变更时才更新元素的高度和字体大小
        // 更新所有元素的高度、字体大小和清除图像缓存
        List<BaseElement> elements = templateData().elements;
        for (int i = 0; i < elements.length; i++) {
          TextElement element = elements[i] as TextElement;
          elements[i] = element.copyWith(
              height: consumableChanged ? safeHeight * 2 / 3 : null,
              fontSize: consumableChanged ? getFontSizeConfig(safeHeight * 1 / 2).mm : null,
              imageCache: CopyWrapper.value(null),
              textAlignHorizontal: autoWidthNew ? NetalTextAlign.start : fixLengthTextAlignHorizontal);
        }
      }
      fileChanged = true;
    }
  }

  /// 检查文件长度设置是否发生变化
  ///
  /// 比较新旧设置，判断长度类型或者固定长度值是否有变更
  /// [autoWidth] - 原始宽度类型（是否自动宽度）
  /// [autoWidthNew] - 新的宽度类型设置
  /// [templateWidth] - 原始模板宽度值
  /// [templateWidthNew] - 新的模板宽度值
  /// @return 如果长度设置有变更则返回true，否则返回false
  bool _fileLengthChanged(bool autoWidth, bool autoWidthNew, int templateWidth, int templateWidthNew) {
    // 如果宽度类型未变（均为自动宽度），则无变更
    if (autoWidth && autoWidthNew) {
      return false;
    }
    // 如果宽度类型未变（均为固定宽度），则检查宽度值是否有变化
    if (!autoWidth && !autoWidthNew) {
      if (templateWidth == templateWidthNew) {
        return false;
      }
    }
    // 其他情况（宽度类型发生变化或固定宽度值有变化）都视为有变更
    return true;
  }

  /// 获取导入Excel功能的状态码
  ///
  /// 根据模板当前状态判断Excel导入功能是否可用及显示状态
  /// @return 状态码:
  ///   0 - 可正常导入Excel
  ///   1 - 已导入过Excel
  ///   2 - 存在非空段落，导入按钮置灰
  ///   3 - 双行模板不支持导入，隐藏导入入口
  int _getImportStatus() {
    // 检查是否已导入过Excel
    String? fileName = templateData().tubeFileSetting?.lastImportedFileName;
    if (fileName?.isNotEmpty == true) {
      // 已经导入过Excel
      return 1;
    }

    // 检查是否为双行模板
    int line = templateData().tubeFileSetting?.line?.toInt() ?? 1;
    if (line > 1) {
      // 双行模板不支持导入Excel，隐藏导入入口
      return 3;
    }

    // 检查是否存在非空段落
    if (EditTemplateDataTransform.checkContainNotEmptyParagraph(templateData())) {
      // 存在非空段落，导入Excel按钮置灰
      return 2;
    }

    // 可正常导入Excel
    return 0;
  }
}

/// 保存打印
extension savePrint on C1EditLogic {
  /// 保存模板文件
  ///
  /// 触发保存操作，获取保存操作列表中的"保存"项并执行
  /// [context] - 当前BuildContext用于弹窗
  saveTemplate(BuildContext context) {
    // 获取保存操作列表（false表示不是退出场景）
    List<ExitSheetData> saveSheetDataList = _getSaveSheetDataList(context, false);
    // 直接执行保存操作（列表中的第一项）
    saveSheetDataList[0].sheetClickAction.call();
  }

  /// 处理退出页面逻辑
  ///
  /// 根据文件是否有变更决定直接退出还是提示保存
  /// [context] - 当前BuildContext用于弹窗
  handleExitPage(BuildContext context) {
    if (!fileChanged) {
      // 文件无变更，直接退出
      _doBack(context);
    } else {
      // 文件有变更，显示"保存/不保存"选项
      List<ExitSheetData> saveSheetDataList = _getSaveSheetDataList(context, true);
      showCustomListDialog(context, '', '', saveSheetDataList, (index) {
        if (index == -1) {
          // 用户取消选择，不执行任何操作
        } else {
          // 执行用户选择的操作（保存或不保存）
          saveSheetDataList[index].sheetClickAction.call();
        }
      });
    }
  }

  /// 获取保存相关操作列表
  ///
  /// 返回保存选项的列表，包括"保存"和"不保存"操作
  /// [context] - 当前BuildContext用于弹窗
  /// [exit] - 是否为退出场景，影响保存后的行为
  /// @return 保存选项列表
  List<ExitSheetData> _getSaveSheetDataList(BuildContext context, bool exit) {
    List<ExitSheetData> exitSheetDataList = [];

    // 添加"保存"选项
    ExitSheetData saveSheet = ExitSheetData(
        title: intlanguage('app00017', '保存'),
        sheetClickAction: () async {
          // 检查是否有保存内容
          TemplateData editTemplate = templateData();
          if ((editTemplate.values?.length ?? 0) == 0) {
            ToastUtil.showToast(context, intlanguage('app100001674', '当前没有保存内容'));
            return;
          }

          // 显示加载提示
          LoadingToastImpl().showLoading();

          // 检查是否为更新已有文件或创建新文件
          bool contain = await C1FileBusiness.instance.checkContainFile(editTemplate);

          // 计算段落数量并更新设置
          int totalCount = TemplateDataTransform.calcAllParagraphCount(editTemplate);
          TubeFileSetting tubeFileSetting = editTemplate.tubeFileSetting ?? TubeFileSetting();
          tubeFileSetting.paragraphCount = totalCount;
          String align;
          if (tubeFileSetting.autoWidth ?? false) {
            align = "left";
          } else {
            BaseElement? element = editTemplate.elements.firstOrNull;
            if (element != null && element is TextElement && element.textAlignHorizontal == NetalTextAlign.start) {
              align = "left";
            } else {
              align = "center";
            }
          }
          tubeFileSetting.align = align;
          String templateVersion = C1TemplateVersionManager.getTemplateVersion();

          // 构建保存用的模板数据
          TemplateData template = editTemplate.copyWith(
              id: contain ? editTemplate.id : DateTime.now().millisecondsSinceEpoch.toString(),
              localThumbnail: "",
              thumbnail: CopyWrapper<String?>.value(""),
              // 保存背景图片URL到backgroundImage
              backgroundImage: CopyWrapper<String?>.value(templateData().backgroundImage),
              tubeFileSetting: CopyWrapper<TubeFileSetting?>.value(tubeFileSetting),
              elements: editTemplate.elements,
              values: editTemplate.values,
              templateVersion: templateVersion);

          // 定义保存成功的回调
          Function(TemplateData) success = (result) {
            // 隐藏加载提示
            LoadingToastImpl().dismissLoading();

            // 显示成功提示
            String msg = intlanguage('app00349', '保存成功') + "!";
            ToastUtil.showSuccessToast(context, msg);

            // 更新当前模板数据
            _saveUpdateTemplateData(result);
            fileChanged = false;
            needRefresh = true;

            // 如果是退出场景，延迟退出
            if (exit) {
              Future.delayed(const Duration(milliseconds: 200), () {
                _doBack(context);
              });
            }
          };
          int type;
          if (C1FileBusiness.instance.checkContainMultipleSerials(template)) {
            type = 0;
          } else {
            type = 1;
          }
          // 执行保存操作（更新已有文件或创建新文件）
          if (contain) {
            C1FileBusiness.instance.modifyC1File(template, success: success);
          } else {
            C1FileBusiness.instance.createC1File(template, success: success);
          }
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "129_437_432",
            "ext": {"type": type}
          });
        });
    exitSheetDataList.add(saveSheet);

    // 添加"不保存"选项
    ExitSheetData notSaveSheet = ExitSheetData(
        title: intlanguage('app00111', '不保存'),
        saveType: SaveType.notSave,
        sheetClickAction: () {
          // 如果是退出场景，直接返回
          if (exit) {
            _doBack(context);
          }
        });
    exitSheetDataList.add(notSaveSheet);

    return exitSheetDataList;
  }

  /// 执行返回上一页操作
  ///
  /// 清除当前模板的缓存并返回上一页
  /// [context] - 当前BuildContext用于导航
  _doBack(BuildContext context) {
    // 清除模板详情缓存
    C1FileBusiness.instance.removeTemplateDetailCache(templateData().id!);
    // 返回上一页，并带上是否需要刷新的标志
    Navigator.of(context).pop(needRefresh);
  }

  /// 更新保存后的模板数据
  ///
  /// 使用保存后返回的模板数据更新当前内存中的模板数据
  /// [saveTemplate] - 保存后返回的模板数据
  _saveUpdateTemplateData(TemplateData saveTemplate) {
    // 更新创建和修改时间
    templateData.value.profile.extra.createTime = saveTemplate.profile.extra.createTime;
    templateData.value.profile.extra.updateTime = saveTemplate.profile.extra.updateTime;

    // 更新模板基本信息
    templateData.value = templateData.value.copyWith(
        id: saveTemplate.id,
        localThumbnail: "",
        thumbnail: CopyWrapper<String?>.value(""),
        local_type: saveTemplate.local_type,
        elements: templateData().elements,
        values: templateData().values);
  }

  /// 显示成功提示
  ///
  /// 在界面中间显示带有成功图标的提示信息
  /// [context] - 当前BuildContext用于显示提示
  /// [msg] - 提示信息内容
  /// [duration] - 提示显示时长，默认1秒
  void _showSuccessToast(BuildContext context, String msg, {Duration duration = const Duration(seconds: 1)}) {
    FToast().init(context).showToast(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.0),
            color: Colors.black.withOpacity(0.5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    height: 18,
                    width: 18,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  Image.asset(
                    'assets/images/vipNpsSuccess.png',
                    height: 20,
                    width: 20,
                  ),
                ],
              ),
              SizedBox(
                width: 7.0,
              ),
              Text(
                msg,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: Colors.white),
              ),
            ],
          ),
        ),
        toastDuration: duration,
        positionedToastBuilder: (context, child, gravity) {
          return Container(
            child: child,
          );
        });
  }
}

class RepeatCountInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      try {
        int num = int.parse(text);
        if (num >= 1 && num <= 200) {
          return newValue;
        }
      } catch (e) {}
      return oldValue;
    }
    return newValue;
  }
}
