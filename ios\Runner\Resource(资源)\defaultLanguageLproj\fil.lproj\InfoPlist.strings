/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Ang application na ito ay nangangailangan ng pahintulot ng camera para sa pagbabasa ng barcode, pagkilala sa teksto, at mga kakayahan sa pagkuha ng larawan. Gusto mong payagan ang access sa camera?";
NSBluetoothPeripheralUsageDescription = "Ang application na ito ay nangangailangan ng pahintulot ng Bluetooth para sa mga serbisyo ng koneksyon sa printer. Payagan ang access sa Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Ang application na ito ay nangangailangan ng pahintulot ng Bluetooth para sa mga serbisyo ng koneksyon sa printer. Payagan ang access sa Bluetooth?";
NSContactsUsageDescription = "Ang application na ito ay nangangailangan ng pahintulot sa pag-access sa mga contact upang basahin ang impormasyon sa pakikipag-ugnay. Payagan ang access sa mga contact?";
NSMicrophoneUsageDescription = "Ang application na ito ay nangangailangan ng pahintulot ng mikropono para sa mga serbisyo sa pagkilala ng boses. Payagan ang access sa mikropono?";
NSPhotoLibraryUsageDescription = "Gagamitin ang pahintulot na ito para sa pag-print ng mga larawan, pagkilala ng barcode at QR code, pagkilala ng teksto, at pag-set ng custom na avatar. Mangyaring 'Payagan ang pag-access sa lahat ng mga larawan' upang matiyak ang normal na pag-access sa album sa NIIMBOT. Kung pipiliin mo ang 'Mga Piling Larawan', ang lahat ng hindi napiling larawan at mga idadagdag pa sa hinaharap ay hindi maa-access sa NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Upang matulungan kang kumonekta sa mga kalapit na Wi-Fi network, nangangailangan ang NIIMBOT ng awtorisasyon sa lokasyon.";
NSLocationAlwaysUsageDescription = "Upang matulungan kang kumonekta sa mga kalapit na Wi-Fi network, nangangailangan ang NIIMBOT ng awtorisasyon sa lokasyon.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Upang matulungan kang kumonekta sa mga kalapit na Wi-Fi network, nangangailangan ang NIIMBOT ng awtorisasyon sa lokasyon.";
NSSpeechRecognitionUsageDescription = "Ang application na ito ay nangangailangan ng iyong pahintulot upang ma-access ang mga serbisyo sa pagkilala ng boses. Gusto mo bang payagan ang access sa voice recognition?";
NSLocalNetworkUsageDescription = "Ang app na ito ay nangangailangan ng access sa ​Local Area Network (LAN)​​ para sa mga serbisyo ng paghahanap ng LAN device at network configuration.";
"UILaunchStoryboardName" = "LaunchScreen";
