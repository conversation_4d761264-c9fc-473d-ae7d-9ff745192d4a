import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:niimbot_flutter_canvas/src/model/element/json_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_size_config.dart';

import '/src/localization/localization_public.dart';

class TypeSettingMode {
  static const int TEXT_HORIZONTAL = 1;
  static const int TEXT_VERTICAL = 2;
  static const int TEXT_CURVE = 3;
}

class LanguageName {
  static const String LANGUAGE_ZH_CN = "zh-cn";
  static const String LANGUAGE_ZH_TW = "zh-cn-t";
  static const String LANGUAGE_EN = "en";
  static const String LANGUAGE_JA = "ja";
  static const String LANGUAGE_KO = "ko";
}

class TextElement extends JsonElement {
  /// 排版模式：1.横向排版（默认） 2.纵向（垂直）排版  3.弧形文字排版
  int typesettingMode;
  List<int>? typesettingParam;
  int? textAlignHorizontal;
  int? textAlignVertical;
  int? lineMode; //2宽度固定，高度自适应  7高度固定，宽度自适应，竖排文字专用
  num wordSpacing;
  num letterSpacing;
  num lineSpacing;
  String? fontFamily;
  String? fontCode;
  List<String> fontStyle;
  num fontSize;
  List<int>? fontColor;
  bool? isTitle;
  String? contentTitle;

  /// 0.英文数字按字符换行(默认)  1.英文数字按单词换行
  int? lineBreakMode;
  double? lastWidth;
  double? lastHeight;
  List<String>? dataBind;

  /// 文本样式
  TextElementBoxStyle boxStyle;
  List<TextElementTextStyle> textStyle;

  NiimbotTextDirection textDirection;

  /// 记录上次不同模式下的文本样式
  Map<int, TextElementBoxStyle> lastBoxStyles;

  /// 记录上次非模式3下的文本框模式
  /// 用于在复选框在反选时可以回到上次操作的文本框模式
  TextElementBoxStyle lastUnlessFixWidthHeightBoxStyle;

  /// 当前文字内容固定宽高最大字号
  double fixedWidthHeightMaxFontSize;

  /// 编辑中
  bool isEditing = false;

  /// 字号变更
  bool fontSizeChanged = false;

  /// 是否需要显示字号缩小警告提示
  bool shouldShowSizeWarning = false;

  /// 上次切换的文本方向、默认为空
  int? lastTypeSettingMode;

  /// 拉伸模式：0=未拉伸，1=拉伸中
  int calcMode = 0;

  /// 行数：用于保持拉伸过程中行数不变
  int? lines;

  TextElement({
    required super.id,
    required super.x,
    required super.y,
    required super.width,
    required super.height,
    super.zIndex,
    required super.value,
    super.paperColorIndex,
    super.hasVipRes,
    super.type = ElementItemType.text,
    super.rotate,
    super.isLock,
    super.fieldName,
    super.mirrorId,
    super.isBinding,
    super.bindingColumn,
    super.isOpenMirror,
    super.mirrorType,
    super.colorReverse,
    super.colorChannel,
    super.elementColor,
    this.typesettingMode = 1,
    this.typesettingParam,
    this.textAlignHorizontal,
    this.textAlignVertical,
    required this.lineMode,
    required this.wordSpacing,
    required this.letterSpacing,
    required this.lineSpacing,
    this.fontFamily,
    this.fontCode,
    required this.fontStyle,
    required this.fontSize,
    this.fontColor,
    this.dataBind,
    this.isTitle,
    this.contentTitle,
    this.lineBreakMode,
    this.isEditing = false,
    this.calcMode = 0,
    this.lines,
    this.lastTypeSettingMode,
    TextElementBoxStyle? boxStyle,
    List<TextElementTextStyle>? textStyle,
    NiimbotTextDirection? textDirection
  })  : this.lastWidth = width.toDouble(),
        this.lastHeight = height.toDouble(),
        this.boxStyle = boxStyle ?? TextElementBO.defaultBoxStyle(),
        this.textStyle = textStyle ?? TextElementBO.defaultTextStyles(),
        this.textDirection = textDirection ?? NiimbotTextDirection.ltr,
        this.lastUnlessFixWidthHeightBoxStyle = boxStyle ?? TextElementBO.defaultBoxStyle(),
        this.lastBoxStyles = {typesettingMode: boxStyle ?? TextElementBO.defaultBoxStyle()},
        this.fixedWidthHeightMaxFontSize = allFontSizeConfigList.last.mm;

  TextElement.fromJson(Map<String, dynamic> json)
      : typesettingMode = json['typesettingMode'] ?? 1,
        typesettingParam = _parseTypesettingParam(json['typesettingParam']),
        textAlignHorizontal = _parseTextAlign(json['textAlignHorizonral']),
        textAlignVertical = _parseTextAlign(json['textAlignVertical']),
        lineMode = json['lineMode'] ?? json['lineBreakMode'],
        wordSpacing = _parseSpacing(json['wordSpacing']),
        letterSpacing = _parseSpacing(json['letterSpacing']),
        lineSpacing = _parseSpacing(json['lineSpacing']),
        fontFamily = json['fontFamily'],
        fontCode = json['fontCode'],
        fontStyle = json['fontStyle'] is String ? [] : json['fontStyle'].cast<String>(),
        fontSize = json['fontSize'],
        fontColor = _parseFontColor(json['fontColor']),
        isTitle = _parseIsTitle(json['isTitle']),
        contentTitle = json['contentTitle'],
        lineBreakMode = json['lineBreakMode'],
        lastWidth = json['width']?.toDouble(),
        lastHeight = json['height']?.toDouble(),
        dataBind = json['dataBind']?.cast<String>(),
        textStyle = _parseTextStyle(json['textStyle']),
        boxStyle = TextElementBoxStyleString.create(json["boxStyle"]),
        textDirection = _parseTextDirection(json["textDirection"]) ?? NiimbotTextDirection.ltr,
        lastUnlessFixWidthHeightBoxStyle =
            _parseLastUnlessFixWidthHeightBoxStyle(json["lastUnlessFixWidthHeightBoxStyle"]) ??
                TextElementBoxStyleString.create(json["lastUnlessFixWidthHeightBoxStyle"]),
        lastBoxStyles = {json['typesettingMode'] ?? 1: TextElementBoxStyleString.create(json["boxStyle"])},
        fixedWidthHeightMaxFontSize = allFontSizeConfigList.last.mm,
        calcMode = json['calcMode'] ?? 0,
        lines = json['lines'],
        lastTypeSettingMode = json['lastTypeSettingMode'],
        super.fromJson(json) {
    bindingColumn = getBindingColumn();
    textStyleFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data.addAll(super.toJson());

    if (escapeValue && this.runtimeType == TextElement) {
      if (!isBindingExcel() && (data['value'] ?? '').length == 0) {
        data['value'] = intlanguage('app00364', '双击文本框编辑');
      }
    }

    data['typesettingMode'] = this.typesettingMode;
    data['typesettingParam'] = this.typesettingParam == null ? [] : this.typesettingParam;
    data['textAlignHorizonral'] = this.textAlignHorizontal;
    data['textAlignVertical'] = this.textAlignVertical;
    data['lineMode'] = this.lineMode;
    data['wordSpacing'] = this.wordSpacing.digits(1);
    data['letterSpacing'] = this.letterSpacing.digits(1);
    data['lineSpacing'] = this.lineSpacing.digits(1);

    /// data['fontFamily'] = this.fontFamily;
    data['fontFamily'] = this.fontCode ?? '';
    data['fontCode'] = this.fontCode ?? '';
    data['fontStyle'] = this.fontStyle;
    data['fontSize'] = this.fontSize.digits(1);
    data['fontColor'] = this.fontColor;
    data['isTitle'] = this.isTitle;
    data['contentTitle'] = this.contentTitle ?? "";
    data['lineBreakMode'] = this.lineBreakMode ?? 0;
    data['textDirection'] = this.textDirection.value;
    data['calcMode'] = this.calcMode;
    data['lines'] = this.lines;
    data['lastTypeSettingMode'] = this.lastTypeSettingMode;
    completionFontDefault(data);

    /// 文本样式
    textStyleToJson(data);
    return data;
  }

  @override
  Map<String, dynamic> antiEscapeValueToJson() {
    Map<String, dynamic> data = toJson();
    if (isBindingExcel()) {
      // data['value'] = '\${0⊙$bindingColumn}';
      int bindingColumn = getBindingColumn();
      if(bindingColumn > -1){
        data['value'] = ExcelTransformManager().getCellPlaceHolder(getBindingColumn());
      }
    } else {
      if (this.runtimeType == TextElement && (data['value'] ?? '').length == 0) {
        data['hint'] = intlanguage('app00364', '双击文本框编辑');
      }
    }
    return data;
  }

  @override
  double getItemDisplayMinWidth(BuildContext context) {
    return max(getMinTextWidth(), 1.mm2dp().toDouble());
  }

  // @override
  // double getItemDisplayMaxWidth() {
  //   return 300.mm2dp();
  // }

  @override
  double getItemDisplayMinHeight() {
    return max(getMinTextHeight(), 1.mm2dp().toDouble());
    // return 1.mm2dp();
  }

  // @override
  // double getItemDisplayMaxHeight() {
  //   return 300.mm2dp();
  // }

  double getMinTextWidth() {
    /// 文本框最小宽度40像素（设计规范）
    return ((40.px2mm()) as num).mm2dp().toDouble();
    // if (value == null || value.isEmpty) {
    //   return 0;
    // }
    // //计算一个汉字的宽度作为最小宽度
    // Size textSize = InputUtils.boundingTextSize("精", TextStyle(fontSize: fontSize.mm2dp()), maxLines: 1);
    // return textSize.width;
  }

  double getMinTextHeight() {
    /// 文本框最小高度40像素（设计规范）
    return ((40.px2mm()) as num).mm2dp().toDouble();
    // if (value == null || value.isEmpty) {
    //   return 0;
    // }
    // Size textSize = InputUtils.boundingTextSize(value, TextStyle(fontSize: fontSize.mm2dp()), maxLines: 1);
    // return textSize.height;
  }

  bool isTextVertical() {
    return typesettingMode == 2;
  }

  void sizeSwapByTextDirectionChanged() {
    lastWidth = super.width.toDouble();
    lastHeight = super.height.toDouble();
    double tempHeight = super.height.toDouble();
    height = width;
    width = tempHeight;
    // correctTopLeft();
  }

  void correctTopLeft() {
    //保持中心点不变，重新计算x,y值
    Offset center = Offset(x.toDouble(), y.toDouble()) +
        Offset((lastWidth?.toDouble() ?? 0) / 2, (lastHeight?.toDouble() ?? 0) / 2);
    y = center.dy - height / 2;
    x = center.dx - width / 2;
  }

  void resetRotate() {
    tagForRelocation = 2;
    rotate = 0;
  }

  void change2Vertical270() {
    tagForRelocation = 1;
    rotate = 270;
  }

  void relocationForTypeSettingChange() {
    tagForRelocation = 3;
  }

  bool specialRelocation() {
    if (tagForRelocation == 1 || tagForRelocation == 2 || tagForRelocation == 3) {
      tagForRelocation = 0;
      return true;
    }
    return false;
  }

  int tagForRelocation = 0;

  ///是否支持竖排文字
  bool checkSupportVerticalText() {
    // 竖排文字仅支持应用内 简中/繁中/韩文/日文/英文
    String currentLanguage = CanvasUserCenter().languageCode;
    return currentLanguage == LanguageName.LANGUAGE_ZH_CN ||
        currentLanguage == LanguageName.LANGUAGE_ZH_TW ||
        currentLanguage == "zh" ||
        currentLanguage == LanguageName.LANGUAGE_KO ||
        currentLanguage == LanguageName.LANGUAGE_JA ||
        currentLanguage == LanguageName.LANGUAGE_EN;
  }

  ///变更文本方向时 对齐方式做下映射
  void alignChangeFun(bool isVertical) {
    if (isVertical) {
      /// 纵向没有分散对齐
      if (textAlignHorizontal == 3) {
        textAlignVertical = 0;
      } else {
        textAlignVertical = textAlignHorizontal;
      }
    } else {
      textAlignHorizontal = textAlignVertical;
    }
  }

  @override
  bool hasVipSource() {
    if ((fontCode?.isNotEmpty ?? false) && FontManager().isFontVip(fontCode!)) {
      return true;
    }
    return false;
  }

  @override
  bool isBindingExcel() {
    // return isBinding == 1 && bindingColumn != null && bindingColumn >= 0;
    return super.isBindingExcel();
  }

  bool showExcelTitle() {
    // return isTitle || contentTitle != null;
    return ExcelTransformManager().getElementModify(this)?.useTitle ?? false;
  }
}

List<int> _parseTypesettingParam(dynamic field) {
  if (field == null) return [];
  return field.cast<int>();
}

int _parseTextAlign(dynamic field) {
  if (field is String) return int.parse(field);
  return field;
}

num _parseSpacing(dynamic field) {
  if (field == null) return 0;
  if (field is String) return int.parse(field);
  return field;
}

List<int> _parseFontColor(dynamic field) {
  List<int> fontColorValue = [];
  if (field is List) {
    field.map((e) {
      if (e is String) {
        int.parse(e);
      } else {
        return e;
      }
    }).toList();
  }
  return fontColorValue;
}

bool _parseIsTitle(dynamic field) {
  if (field == null) {
    return false;
  }
  if (field is int) {
    return field == 0 ? false : true;
  } else {
    return field;
  }
}

List<TextElementTextStyle> _parseTextStyle(dynamic field) {
  /// 老版本默认使用autoHeight
  var newTextStyle = <TextElementTextStyle>[];
  if (field != null && field is List) {
    final originTextStyle = List.from(field);
    newTextStyle = originTextStyle.map((e) {
      return TextElementTextStyleString.create(e);
    }).toList();
  }
  if (newTextStyle.length == 0) {
    newTextStyle = TextElementBO.defaultTextStyles();
  }
  return newTextStyle;
}

TextElementBoxStyle? _parseLastUnlessFixWidthHeightBoxStyle(dynamic field) {
  if (field is String) {
    return TextElementBoxStyleString.create(field);
  }
  return null;
}

NiimbotTextDirection? _parseTextDirection(dynamic field) {
  final val = parseNumberFromJSON(field)?.toInt();
  if(val != null) return NiimbotTextDirection.byValue(val);
  return null;
}

num? parseNumberFromJSON(dynamic field) {
  if (field is num) {
    return field;
  }
  if (field is String) {
    return num.tryParse(field);
  }
  return null;
}

