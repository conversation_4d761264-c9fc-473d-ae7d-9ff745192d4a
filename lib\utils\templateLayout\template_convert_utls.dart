import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/escape_utils.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:text/utils/common_fun.dart';
import 'package:uuid/uuid.dart';
import 'package:niimbot_excel/models/data_source.dart';
import '../../application.dart';

class TemplateConvertUtils {
  /// 商品库字段
  static var goodsLabelCategaryId = Application.appEnv == AppEnv.production ? "270" : "1";
  static const commodityFieldsCode = [
    "app100000927",
    "app01052",
    "app01053",
    "app01055",
    "app01054",
    "app01058",
    "app01056",
    "app01057",
    "app01116",
  ];
  static final commodityFields = ['商品条码', '品名', '产地', '单位', '规格', '等级', '零售价', '促销价', '物价员'];
  static const rowLettters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  static const defaultImageMaxWH = 1000;
  static final commodityFieldNames = [
    'barcode',
    'name',
    'originPlace',
    'unit',
    'norm',
    'level',
    'retailPrice',
    'rushPrice',
    'priceOfficer'
  ];

  static Future<TemplateData> staticTemplateToGoodsDataSourceTemplate(
      Map<String, dynamic> sourceTemplate, List<dynamic> inputAreas,
      {DataSourceType dataSourceType = DataSourceType.commodity}) async {
    // 1. 复制模板数据
    TemplateData _templateData = EscapeUtils.cloneTemplateData(TemplateData.fromJson(sourceTemplate));
    _templateData.profile.extrain.userId = '-1';
    _templateData.profile.extrain.templateType = -1;

    // 2. 获取绑定字段
    Set<String> fieldNamesInInputAreas = getFieldNamesInInputAreas(inputAreas);
    // 3. 如果绑定商品字段小于3，不转化为商品模板，直接处理图像元素
    if (fieldNamesInInputAreas.length < 3 || _templateData.profile.extrain.industryId != goodsLabelCategaryId) {
      await _handleImageElements(_templateData.elements);
      return _templateData;
    }
    // 4. 处理商品元素绑定
    await _bindElements(inputAreas, _templateData, dataSourceType);

    // 5. 设置绑定信息
    _templateData.bindInfo = ExcelPageInfo(page: 0, total: 1);
    Map<String, dynamic> parms = {
      "commodity": {
        "name": "",
        "norm": "",
        "barcode": "",
        "originPlace": "",
        "retailPrice": "",
        "rushPrice": "",
        "unit": "",
        "level": "",
        "priceOfficer": "",
      }
    };
    // 6. 创建数据源
    _templateData.dataSource = [
      DataSource(
          type: dataSourceType, uri: "", name: "", headers: {}, params: parms, hash: Uuid().v4().replaceAll("-", ""))
    ];

    // 7. 设置分页信息
    if (_templateData.bindInfo != null) {
      _templateData.currentPageIndex = _templateData.bindInfo!.page == null ? 0 : _templateData.bindInfo!.page! - 1;
      _templateData.totalPage = _templateData.bindInfo!.total == null ? 1 : _templateData.bindInfo!.total!;
    }

    return _templateData;
  }

// 提取绑定字段
  static Set<String> getFieldNamesInInputAreas(List<dynamic> inputAreas) {
    Set<String> fieldNames = {};
    for (var inputArea in inputAreas) {
      if (inputArea['productField']?.isNotEmpty ?? false) {
        String productField = inputArea['productField'] == "brand" ? "name" : inputArea['productField'];
        int index = commodityFieldNames.indexOf(productField);
        if (index >= 0) {
          fieldNames.add(productField);
        }
      }
    }
    return fieldNames;
  }

// 绑定商品元素
  static Future<void> _bindElements(
      List<dynamic> inputAreas, TemplateData templateData, DataSourceType dataSourceType) async {
    List<JsonElement> elements = templateData.elements;
    for (var element in elements) {
      if (element is TextElement || element is BarCodeElement) {
        for (var inputArea in inputAreas) {
          String productField = inputArea['productField'] == "brand" ? "name" : inputArea['productField'] ?? "";
          int index = commodityFieldNames.indexOf(productField);
          if (index >= 0) {
            String rowPlaceholder = rowLettters[index];
            String? fieldName = commodityFields[index];
            String fieldTitle = intlanguage(commodityFieldsCode[index], fieldName);
            if (element is TextElement && inputArea['name'] == element.value) {
              _setTextElementProperties(element, productField, fieldName, rowPlaceholder, dataSourceType);
              if (templateData.modify == null) {
                templateData.modify = {};
              }
              if (!templateData.modify!.containsKey(element.id)) {
                templateData.modify![element.id] = {
                  "0": DataBindModify(useTitle: false, delimiter: "：", title: fieldTitle)
                };
              }
            } else if (element is BarCodeElement && inputArea['productField'] == "barcode") {
              _setBarCodeElementProperties(element, rowPlaceholder, dataSourceType);
            }
          }
        }
      } else if (element is ImageElement) {
        await _handleImageElements([element]);
      }
    }
  }

// 设置TextElement的属性
  static void _setTextElementProperties(TextElement element, String productField, String? fieldName,
      String rowPlaceholder, DataSourceType dataSourceType) {
    element.contentTitle = fieldName;
    element.fieldName = productField;
    element.value = '${rowPlaceholder}0'; // 设置value为数据绑定格式
    element.dataBind = ["", dataSourceType.getStringValue()]; // 设置dataBind为商品库类型
  }

// 设置BarCodeElement的属性
  static void _setBarCodeElementProperties(
      BarCodeElement element, String rowPlaceholder, DataSourceType dataSourceType) {
    element.value = '${rowPlaceholder}0';
    element.dataBind = ["", dataSourceType.getStringValue()];
    element.codeType = BarcodeType.EAN13;
  }

  // 处理图像元素
  static Future<void> _handleImageElements(List<JsonElement> elements) async {
    for (var element in elements) {
      if (element is ImageElement) {
        Size size = getScaledSize(element.width.toInt(), element.height.toInt(), defaultImageMaxWH, defaultImageMaxWH);
        int width = size.width.toInt();
        int height = size.height.toInt();
        await _createDefaultImage(width, height);
        element.localUrl = '${Application.localCachePath}/jc/element/${width}_${height}_defaultImage.png';
        element.imageProcessingType = 2;
        element.isDefaultImage = true;
      }
    }
  }

  static Future<void> _createDefaultImage(int canvasWidth, int canvasHeight) async {
    String imagePath = '${Application.localCachePath}/jc/element/${canvasWidth}_${canvasHeight}_defaultImage.png';
    if (File(imagePath).existsSync()) {
      return;
    }
    await _composeImageWriteToFile(canvasWidth: canvasWidth, canvasHeight: canvasHeight);
  }

  /// 加载 asset 为 ui.Image
  static Future<ui.Image> _loadImageFromAsset(String assetPath) async {
    final data = await rootBundle.load(assetPath);
    final codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
    final frame = await codec.getNextFrame();
    return frame.image;
  }

  /// 合成：400x600 画布平铺 background，然后 icon 居中，输出 PNG bytes
  static Future _composeImageWriteToFile({
    int canvasWidth = 400,
    int canvasHeight = 600,
  }) async {
    final bgTile = await _loadImageFromAsset('assets/images/defaultImageElementBack.png');
    final icon = await _loadImageFromAsset('assets/images/defaultImageElementIcon.png');
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final paint = Paint();

    // ===== 平铺背景（支持缩放）=====
    final tileW = bgTile.width.toDouble();
    final tileH = bgTile.height.toDouble();
    for (double y = 0; y < canvasHeight; y += tileH) {
      for (double x = 0; x < canvasWidth; x += tileW) {
        canvas.drawImage(bgTile, Offset(x, y), paint);
      }
    }

    // ===== 计算 icon 缩放后的位置并绘制 =====
    final iconWH = min(canvasWidth / 4, canvasHeight / 4); // 缩放尺寸
    final srcRect = Rect.fromLTWH(0, 0, icon.width.toDouble(), icon.height.toDouble());
    final dstRect = Rect.fromCenter(
      center: Offset(canvasWidth / 2, canvasHeight / 2),
      width: iconWH,
      height: iconWH,
    );

    canvas.drawImageRect(icon, srcRect, dstRect, paint);

    // ===== 输出图像并写入文件 =====
    final picture = recorder.endRecording();
    final img = await picture.toImage(canvasWidth, canvasHeight);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    final imagePath = '${Application.localCachePath}/jc/element/${canvasWidth}_${canvasHeight}_defaultImage.png';
    final file = File(imagePath);
    await file.create(recursive: true);
    await file.writeAsBytes(byteData!.buffer.asUint8List());
  }

  static getScaledSize(int originalW, int originalH, int maxW, int maxH) {
    final scaleW = maxW / originalW;
    final scaleH = maxH / originalH;
    final scale = min(scaleW, scaleH);
    return Size(originalW * scale, originalH * scale);
  }
}
