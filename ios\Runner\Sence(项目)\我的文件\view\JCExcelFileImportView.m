//
//  JCExcelImportView.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2020/11/13.
//  Copyright © 2020 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCExcelFileImportView.h"
#import "UILabel+YBAttributeTextTapAction.h"
#import "JCThirdAppTool.h"

@interface JCExcelFileImportView()<UITextViewDelegate>
@property(nonatomic,strong) UIButton *openLocalFileBtn;
@property(nonatomic,strong) UIView *openLocalFileView;
@property(nonatomic,strong) UITextView *fileSizeTipTextView;
@property(nonatomic,strong) UIScrollView *appScrollerView;
@property(nonatomic,strong) NSArray *thirdAppArr;
@property(nonatomic,assign) BOOL isAllFile;
@end


@implementation JCExcelFileImportView
- (instancetype)initWithFrame:(CGRect)frame thirdAppArr:(NSArray *)thirdAppArr isAllFile:(BOOL)isAllFile limitKb:(NSInteger)fileLimitKb {
    self = [super initWithFrame:frame];
    if(self){
        self.isAllFile = isAllFile;
        self.backgroundColor = HEX_RGB(0xF7F7F7);
        self.thirdAppArr = thirdAppArr;
        self.fileLimitKb = fileLimitKb;
        [self initUI];
    }
    return self;
}

- (void)initUI{
    [self addSubview:self.fileSizeTipTextView];
    self.fileSizeTipTextView.leading = 15;
    [self addSubview:self.openLocalFileView];
    self.openLocalFileView.leading = 15;
    [self addSubview:self.openLocalFileBtn];
    if(self.thirdAppArr.count > 0){
        [self addSubview:self.appScrollerView];
    }
    [self initAppScrollerView];
}

- (UIButton *)openLocalFileBtn{
    if(!_openLocalFileBtn){
        _openLocalFileBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_openLocalFileBtn setBackgroundColor:UIColor.clearColor];
        [_openLocalFileBtn setFrame:CGRectMake(15, self.fileSizeTipTextView.bottom + 10, self.width-30, 50)];
        [_openLocalFileBtn addTarget:self action:@selector(openLocalFileEvent) forControlEvents:UIControlEventTouchUpInside];
    }
    return _openLocalFileBtn;
}

- (UIView *)openLocalFileView{
    if(!_openLocalFileView){
        _openLocalFileView = [[UIView alloc] initWithFrame:CGRectMake(15, self.fileSizeTipTextView.bottom + 10, SCREEN_WIDTH-30, 50)];
        _openLocalFileView.backgroundColor = COLOR_NEW_THEME;
        _openLocalFileView.layer.cornerRadius = 8;
        
        
        UIView *centView = [[UIView alloc] initWithFrame:CGRectZero];
        float width = 0;
        CGSize size = [XY_LANGUAGE_TITLE_NAMED(@"app01243", @"打开本地文件") boundingRectWithSize:CGSizeMake(1000, 30) options:NSStringDrawingUsesLineFragmentOrigin attributes:@{NSFontAttributeName :MY_FONT_Regular(16)} context:nil].size;
        width = size.width + 22 + 20;
        
        UIImageView *fileImage = [[UIImageView alloc] initWithFrame:CGRectMake(5, 6, 22, 18)];
        fileImage.backgroundColor = UIColor.clearColor;
        fileImage.image = XY_IMAGE_NAMED(@"openLocal");
        [centView addSubview:fileImage];
        
        
        UILabel *openFileLabel = [[UILabel alloc] initWithFrame:CGRectMake(5 + 22 + 10, 0, size.width, 30)];
        openFileLabel.backgroundColor = UIColor.clearColor;
        openFileLabel.textColor = UIColor.whiteColor;
        openFileLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01243", @"打开本地文件");
        openFileLabel.font = MY_FONT_Regular(16);
        [centView addSubview:openFileLabel];
        centView.size = CGSizeMake(width, 30);
        centView.frame = CGRectMake((_openLocalFileView.width - width)/2, (50 - 30)/2, width, 30);
        [_openLocalFileView addSubview:centView];
        centView.leading = (_openLocalFileView.width - width)/2;
        [_openLocalFileView addSubview:self.openLocalFileBtn];
        openFileLabel.leading = 5 + 22 + 10;
        fileImage.leading = 5;
        
    }
    return _openLocalFileView;
}

- (UITextView *)fileSizeTipTextView{
    if(!_fileSizeTipTextView){
        NSString *fileLimitKb = [NSString stringWithFormat:@"%ld",(long)self.fileLimitKb];
        NSString *content = XY_LANGUAGE_TITLE_NAMED_WITH_PLACEHOLDERS(@"app100002081", @"文件限制$KB",@[fileLimitKb]);
        if(self.isAllFile){
            content = XY_LANGUAGE_TITLE_NAMED(@"", @"文件限制5MB，如有更大文件上传需求请开通会员");
        }
        UITextView *contentTextView = [[UITextView alloc] initWithFrame:CGRectMake(15, 0, 0, 0)];
        contentTextView.attributedText = [self getContentLabelAttributedText:content font:MY_FONT_Regular(13) textColor:XY_HEX_RGB(0x262626) title1:XY_LANGUAGE_TITLE_NAMED(@"", @"开通会员") textColor1:COLOR_NEW_THEME];
        contentTextView.textAlignment = self.isRTL ? NSTextAlignmentRight : NSTextAlignmentLeft;
        contentTextView.linkTextAttributes = @{NSForegroundColorAttributeName : COLOR_NEW_THEME};
        contentTextView.delegate = self;
        contentTextView.backgroundColor = COLOR_CLEAR;
        contentTextView.editable = NO;        //必须禁止输入，否则点击将弹出输入键盘
//        contentTextView.scrollEnabled = NO;
        float tipHeight = [content jk_sizeWithFont:MY_FONT_Regular(13) constrainedToWidth:SCREEN_WIDTH - 30].height + 10;
        contentTextView.size = CGSizeMake(self.width - 30, tipHeight);
        _fileSizeTipTextView = contentTextView;
        
    }
    return _fileSizeTipTextView;
}

#pragma Others
//----------------------------------
- (NSAttributedString *)getContentLabelAttributedText:(NSString *)text font:(UIFont *)labelFont textColor:(UIColor *)textColor title1:(NSString *)title1 textColor1:(UIColor *)textColor1
{
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    [paragraphStyle setLineSpacing:1.25];
    NSRange rang1 = [text rangeOfString:title1];
    NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:text attributes:@{NSFontAttributeName:labelFont,NSForegroundColorAttributeName:XY_HEX_RGB(0x262626)}];
    [attrStr addAttributes:@{NSParagraphStyleAttributeName:paragraphStyle} range:NSMakeRange(0, text.length)];
    [attrStr addAttribute:NSForegroundColorAttributeName value:textColor1 range:rang1];
    [attrStr addAttribute:NSLinkAttributeName value:@"openVIPProtocol://" range:rang1];
    return attrStr;
}


#pragma mark - UITextViewDelegate ----核心代码
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-implementations"
- (BOOL)textView:(UITextView *)textView shouldInteractWithURL:(NSURL *)URL inRange:(NSRange)characterRange {
    if ([[URL scheme] isEqualToString:@"openVIPProtocol"]) {
        //《隐私政策》
        if(self.openVipBlock){
            self.openVipBlock();
        }
        return NO;
    }
    return YES;
}
#pragma clang diagnostic pop


- (UIScrollView *)appScrollerView{
    if(!_appScrollerView){
        _appScrollerView = [[UIScrollView alloc] initWithFrame:CGRectMake(0, self.openLocalFileView.bottom + 5, SCREEN_WIDTH, 46)];
    }
    return _appScrollerView;
}

- (void)initAppScrollerView{
    if(self.thirdAppArr.count > 0){
        CGSize scrollerViewSize = CGSizeZero;
        float scrollerContentWidth = 0;
        for (NSInteger index = 0;index < self.thirdAppArr.count;index++) {
            JCThirdAppInfo *thirdAppInfo = self.thirdAppArr[index];
            UIButton *thirdAppBtn = [UIButton buttonWithType:UIButtonTypeCustom];
            thirdAppBtn.tag = index;
            [thirdAppBtn setImage:XY_IMAGE_NAMED(thirdAppInfo.imageNormal) forState:UIControlStateNormal];
            thirdAppBtn.frame = CGRectMake(15*(index+1) + 46 * index, 0, 46, 46);
            [thirdAppBtn addTarget:self action:@selector(openThirdAppEvent:) forControlEvents:UIControlEventTouchUpInside];
            scrollerContentWidth = 15*(index+2) + 46 * (index + 1);
            [self.appScrollerView addSubview:thirdAppBtn];
            thirdAppBtn.leading = 15*(index+1) + 46 * index;
        }
        scrollerViewSize = CGSizeMake(scrollerContentWidth, 46);
        self.appScrollerView.contentSize = scrollerViewSize;
    }
}

- (void)openLocalFileEvent{
    //打开本地文件事件
    if(self.openLocalFileBlock){
        self.openLocalFileBlock(@"");
    }
}

- (void)openThirdAppEvent:(UIButton *)sender{
    //打开第三方事件
    if(self.openThirdAppBlock){
        self.openThirdAppBlock(@(sender.tag));
    }
}

- (NSInteger)selectThirdAppIndex{
    return 0;
}
/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
