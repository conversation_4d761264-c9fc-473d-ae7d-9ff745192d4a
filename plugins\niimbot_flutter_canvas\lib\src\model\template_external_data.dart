import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';

class ExternalData {
  List<ExternalListModel>? externalDataList;
  String? id;
  String? fileName;

  ExternalData({
    this.externalDataList,
    required this.id,
    required this.fileName,
  });

  ExternalData.fromJson(Map<String, dynamic> json)
      : id = (json['id'] is int) ? json['id'].toString() : json['id'],
        fileName = json['fileName'],
        externalDataList = (json['list'] == null)
            ? <ExternalListModel>[]
            : (json['list'] as List).map((v) {
                return ExternalListModel.fromJson(v);
              }).toList();

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.externalDataList != null) {
      data['list'] = this.externalDataList!.map((v) => v.toJson()).toList();
    }
    data['id'] = this.id;
    data['fileName'] = this.fileName ?? "";
    return data;
  }

  ExternalData clone() {
    ExternalData externalData =
        ExternalData.fromJson(jsonDecode(jsonEncode(toJson())));
    return externalData;
  }
}

class ExternalListModel {
  String name;
  ExternalListModelData? data;

  ExternalListModel({required this.name, this.data});

  ExternalListModel.fromJson(Map<String, dynamic> json)
      : name = json['name'],
        data = json['data'] != null
            ? ExternalListModelData.fromJson(json['data'])
            : null;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

Logger _logger = Logger("ExternalListModelData", on: kDebugMode);

class ExternalListModelData {
  List<String> columnHeaders;
  List<List<String>> columns;

  ExternalListModelData({required this.columnHeaders, required this.columns});

  factory ExternalListModelData.fromJson(Map<String, dynamic> json) {
    var columnHeaders;
    var columns;
    try {
      columnHeaders = json['columnHeaders'].cast<String>();
      if (json['columns'] != null && json['columns'] is List) {
        columns = (json['columns'] as List)
            .map((e) => (e as List).map((item) => item.toString()).toList())
            .toList();
      }
    } catch (e) {
      _logger.log('ExternalListModelData.fromJson error: $e');
    }
    return ExternalListModelData(
      columnHeaders: columnHeaders,
      columns: columns,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['columnHeaders'] = this.columnHeaders;
    data['columns'] = columns;
    return data;
  }
}

class LabelNameInfo {
  String languageCode;
  String languageName;
  String name;

  LabelNameInfo(this.languageCode, this.languageName, this.name);

  LabelNameInfo.fromJson(Map<String, dynamic> json)
      : languageCode = json['languageCode'] as String,
        languageName = json['languageName'] as String,
        name = json['name'] as String;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'languageCode': languageCode,
        'languageName': languageName,
        'name': name,
      };
}

class Profile {
  ///模板条码，可用于查询
  String barcode;

  ///关键词，可用于全文检索
  String? keyword;

  ///硬件系列id
  String? hardwareSeriesId;

  ///硬件系列名称(SDK分类描述，如D11、U11都属于D11系列)
  String machineId;

  ///适配机型（B11、B3S、B50）
  String machineName;

  ///自定义扩展属性
  Extrain extrain;

  Profile({
    this.barcode = '',
    this.keyword,
    this.hardwareSeriesId,
    this.machineId = '',
    this.machineName = '',
    Extrain? extrain,
  }) : extrain = extrain ?? Extrain();

  Profile.fromJson(Map<String, dynamic> json)
      : barcode = json['barcode'] == null ? "" : json['barcode'] as String,
        keyword = json['keyword'] == null ? null : json['keyword'] as String,
        hardwareSeriesId = json['hardwareSeriesId'] == null
            ? null
            : json['hardwareSeriesId'] as String,
        machineId =
            json['machineId'] == null ? "" : json['machineId'] as String,
        machineName =
            json['machineName'] == null ? "" : json['machineName'] as String,
        extrain = json['extrain'] == null
            ? Extrain()
            : Extrain.fromJson(Map<String, dynamic>.from(json['extrain']));

  factory Profile.fromDBJson(Map<String, dynamic> json) {
    if (Platform.isIOS) {
      String machineIdStr =
          json['machineId'] == null ? "" : json['machineId'] as String;
      machineIdStr = machineIdStr.contains("*")
          ? machineIdStr.substring(1, machineIdStr.length - 1)
          : machineIdStr;
      return Profile(
          barcode: json['barcode'] == null ? "" : json['barcode'] as String,
          keyword: json['keyword'] == null ? null : json['keyword'] as String,
          hardwareSeriesId: json['hardwareSeriesId'] == null
              ? null
              : json['hardwareSeriesId'] as String,
          machineId: machineIdStr.replaceAll("*", ","),
          machineName:
              json['machineName'] == null ? "" : json['machineName'] as String,
          extrain: Extrain.fromDBJson(Map<String, dynamic>.from(json)));
    } else {
      return Profile(
          barcode: json['ONE_CODE'] == null ? "" : json['ONE_CODE'] as String,
          keyword: json['KEYWORDS'] == null ? null : json['KEYWORDS'] as String,
          hardwareSeriesId: json['HARDWARE_SERIES_ID'] == null
              ? null
              : json['HARDWARE_SERIES_ID'] as String,
          machineId:
              json['MACHINE_ID'] == null ? "" : json['MACHINE_ID'] as String,
          machineName: json['MACHINE_NAME'] == null
              ? ""
              : json['MACHINE_NAME'] as String,
          extrain: Extrain.fromDBJson(Map<String, dynamic>.from(json)));
    }
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'barcode': barcode ?? '',
        'keyword': keyword ?? '',
        'hardwareSeriesId': hardwareSeriesId ?? '',
        'machineId': machineId ?? '',
        'machineName': machineName ?? '',
        'extrain': extrain.toJson(),
      };
}

class Extrain {
  // 是否订制标签模板(固定资产)
  bool? isCustom;

  ///所属文件夹ID
  String? folderId;

  ///适配平台Code
  String? adaptPlatformCode;

  ///适配平台名
  String? adaptPlatformName;

  ///创建的用户id
  String? userId;

  ///行业分类id
  String? industryId;

  ///商品分类id
  String? commodityCategoryId;

  ///下载次数
  dynamic downloadCount;

  ///是否删除，0_未删除,1_已删除
  bool? isDelete;

  ///添加时间
  String? createTime;

  ///最后更新时间
  String? updateTime;

  ///最后打印时间
  String? lastPrintedAt;

  ///是否是热门模板，0_不是,1_是
  bool? isHot;

  ///个人模板来源
  String? sourceId;

  ///标签纸id
  String? labelId;

  ///服务端返回用于排序序号
  String? sortDependency;

  ///模板点击量、访问量,
  dynamic clickNum;

  ///模板类型，0_普通模板, 1_云模板,2_用户模板样例,4_云模板样例
  num? templateType;

  ///云模板类型：0：标签，1：模板
  num? templateClass;

  ///模板展示，0_公开, 1_私有
  bool? isPrivate;

  ///亚马逊条码（北京）
  String? amazonCodeBeijing;

  ///亚马逊条码（武汉）
  String? amazonCodeWuhan;

  ///备用条码,
  String? sparedCode;

  ///虚拟条码
  String? virtualBarCode;

  ///是否是新版路径，0_否,1_是
  bool? isNewPath;

  ///是否移动端模板：1_是:0_否（用于兼容旧模板数据适配平台的字段）
  bool? isMobileTemplete;

  ///通过扫码取模方式关联的用户手机号码
  String? phone;

  ///通过扫描打印方式创建模板的商品条码
  String? goodsCode;

  ///备用条码
  Map<String, String>? barcodeCategoryMap;

  /// 模板支持的最低版本，值类型为“5.3.0”，为空表示支持所有版本，有值表示低于此版本时此模板不可打开
  /// 主要用于后端判断，当前版本是否可以获取模板详情
  String? resourceVersion;
  //标签 ID
  String? materialModelSn;

  Extrain(
      {this.isCustom,
      this.folderId,
      this.adaptPlatformCode,
      this.adaptPlatformName,
      this.userId,
      this.industryId,
      this.commodityCategoryId,
      this.downloadCount,
      this.isDelete,
      this.createTime,
      this.updateTime,
      this.lastPrintedAt,
      this.isHot,
      this.sourceId,
      this.labelId,
      this.sortDependency,
      this.clickNum,
      this.templateType,
      this.templateClass,
      this.isPrivate,
      this.amazonCodeBeijing,
      this.amazonCodeWuhan,
      this.sparedCode,
      this.virtualBarCode,
      this.isNewPath,
      this.isMobileTemplete,
      this.phone,
      this.goodsCode,
      this.barcodeCategoryMap,
      this.resourceVersion,
      this.materialModelSn
    });

  Extrain.fromJson(Map<String, dynamic> json) {
    isCustom =
        (json['isCustom'] is bool) ? json['isCustom'] : json['isCustom'] == 1;
    folderId = json['folderId'] == null ? "0" : json['folderId'] as String;
    adaptPlatformCode = json['adaptPlatformCode'] == null
        ? ""
        : json['adaptPlatformCode'] as String;
    adaptPlatformName = json['adaptPlatformName'] == null
        ? ""
        : json['adaptPlatformName'] as String;
    userId = json['userId'] == null ? "" : json['userId'] as String;
    industryId = json['industryId'] == null ? '' : json['industryId'].toString();
    commodityCategoryId = json['commodityCategoryId'] == null ? '' : json['commodityCategoryId'].toString();
    downloadCount = json['downloadCount'] == null ? 0 : json['downloadCount'] as dynamic;
    isDelete = (json['isDelete'] is bool) ? json['isDelete'] : json['isDelete'] == 1;
    createTime = json['createTime'] as String?;
    updateTime = json['updateTime'] as String?;
    lastPrintedAt = json['lastPrintedAt'] as String?;
    isHot = (json['isHot'] is bool) ? json['isHot'] : json['isHot'] == 1;
    sourceId = json['sourceId'] == null ? "" : json['sourceId'] as String;
    labelId = json['labelId'] == null ? "" : json['labelId'] as String;
    sortDependency =
        json['sortDependency'] == null ? '' : json['sortDependency'] as String;
    clickNum = json['clickNum'] == null ? 0 : json['clickNum'] as dynamic;
    templateType = json['templateType'] == null
        ? 0
        : ((json['templateType'] is num)
        ? json['templateType']
        : num.parse(json['templateType']));
    templateClass = json['templateType'] == null
        ? 1:((json['templateClass'] is num)
        ? json['templateClass']
        : num.tryParse(json['templateClass']) ?? 0);
    isPrivate = json['isPrivate'] == null
        ? false
        : ((json['isPrivate'] is bool)
        ? json['isPrivate']
        : json['isPrivate'] == 1);
    amazonCodeBeijing = json['amazonCodeBeijing'] == null
        ? null
        : json['amazonCodeBeijing'] as String;
    amazonCodeWuhan = json['amazonCodeWuhan'] == null
        ? null
        : json['amazonCodeWuhan'] as String;
    sparedCode =
        json['sparedCode'] == null ? null : json['sparedCode'] as String;
    virtualBarCode = json['virtualBarCode'] == null
        ? null
        : json['virtualBarCode'] as String;
    isNewPath = json['isNewPath'] == null
        ? true
        : ((json['isNewPath'] is bool)
        ? json['isNewPath']   
        : json['isNewPath'] == 1);
    isMobileTemplete = json['isMobileTemplete'] == null
        ? true
        : ((json['isMobileTemplete'] is bool)
        ? json['isMobileTemplete']
        : json['isMobileTemplete'] == 1);
    phone = json['phone'] == null ? null : json['phone'] as String;
    goodsCode = json['goodsCode'] == null ? null : json['goodsCode'] as String;
    barcodeCategoryMap =  json['barcodeCategoryMap'] == null ?{}:(Map<String, String>.from(json['barcodeCategoryMap'] as Map));
    resourceVersion = json['resourceVersion'] == null ? null : json['resourceVersion'] as String;
    materialModelSn = json['materialModelSn'] == null ? null : json['materialModelSn'] as String;
  }

  Extrain.fromDBJson(Map<String, dynamic> json) {
    if (Platform.isIOS) {
      isCustom =
          (json['isCustom'] is bool) ? json['isCustom'] : json['isCustom'] == 1;
      folderId = json['folderId'] == null ? "0" : json['folderId'] as String;
      adaptPlatformCode = json['adaptPlatformCode'] == null
          ? ""
          : json['adaptPlatformCode'] as String;
      adaptPlatformName = json['adaptPlatformName'] == null
          ? ""
          : json['adaptPlatformName'] as String;
      userId = json['userId'] == null ? "" : json['userId'] as String;
      industryId =
          json['industryId'] == null ? '' : json['industryId'] as String;
      commodityCategoryId = json['commodityCategoryId'] == null
          ? ''
          : json['commodityCategoryId'] as String;
      downloadCount =
          json['downloadCount'] == null ? 0 : json['downloadCount'] as dynamic;
      isDelete =
          (json['isDelete'] is bool) ? json['isDelete'] : json['isDelete'] == 1;
      createTime = json['createTime'] as String;
      updateTime = json['updateTime'] as String;
      lastPrintedAt = json['lastPrintedAt'] as String;
      isHot = (json['isHot'] is bool) ? json['isHot'] : json['isHot'] == 1;
      sourceId = json['sourceId'] == null ? "" : json['sourceId'] as String;
      labelId = json['labelId'] == null ? "" : json['labelId'] as String;
      sortDependency = json['sortDependency'] == null
          ? ''
          : json['sortDependency'] as String;
      clickNum = json['clickNum'] == null ? 0 : json['clickNum'] as dynamic;
      templateType = (json['templateType'] is num)
          ? json['templateType']
          : num.parse(json['templateType']);
      templateClass = (json['templateClass'] is num)
          ? json['templateClass']
          : num.tryParse(json['templateClass']) ?? 0;
      isPrivate = (json['isPrivate'] is bool)
          ? json['isPrivate']
          : json['isPrivate'] == 1;
      amazonCodeBeijing = json['amazonCodeBeijing'] == null
          ? null
          : json['amazonCodeBeijing'] as String;
      amazonCodeWuhan = json['amazonCodeWuhan'] == null
          ? null
          : json['amazonCodeWuhan'] as String;
      sparedCode =
          json['sparedCode'] == null ? null : json['sparedCode'] as String;
      virtualBarCode = json['virtualBarCode'] == null
          ? null
          : json['virtualBarCode'] as String;
      isNewPath = (json['isNewPath'] is bool)
          ? json['isNewPath']
          : json['isNewPath'] == 1;
      isMobileTemplete = (json['isMobileTemplete'] is bool)
          ? json['isMobileTemplete']
          : json['isMobileTemplete'] == 1;
      phone = json['phone'] == null ? null : json['phone'] as String;
      goodsCode =
          json['goodsCode'] == null ? null : json['goodsCode'] as String;
      if (json['barcodeCategoryMap'] != null &&
          json['barcodeCategoryMap'].isNotEmpty) {
        Map<String, String> barcodeCategoryMapDB =
            (json['barcodeCategoryMap'] is Map)
                ? json['barcodeCategoryMap']
                : Map<String, String>.from(
                    (jsonDecode(json['barcodeCategoryMap'])) as Map);
        barcodeCategoryMapDB.values.forEach((element) {
          element = (element).replaceAll("*", "");
        });
        barcodeCategoryMap = barcodeCategoryMapDB;
      }

      resourceVersion = json['resourceVersion'] == null ? null : json['resourceVersion'] as String;
      materialModelSn = json['materialModelSn'] == null ? null : json['materialModelSn'] as String;
    } else {
      isCustom =
          (json['isCustom'] is bool) ? json['isCustom'] : json['isCustom'] == 1;
      folderId = json['FOLDER_ID'] == null ? "0" : json['FOLDER_ID'] as String;
      adaptPlatformCode = json['ADAPT_PLATFORM_CODE'] == null
          ? ""
          : json['ADAPT_PLATFORM_CODE'] as String;
      adaptPlatformName = json['ADAPT_PLATFORM_NAME'] == null
          ? ""
          : json['ADAPT_PLATFORM_NAME'] as String;
      userId = (json['USER_ID'] as int).toString();
      industryId = json['INDUSTRY_ID'] != ""
          ? (json['INDUSTRY_ID'] as int).toString()
          : "";
      commodityCategoryId = (json['CAT_ID'] as int).toString();
      downloadCount = json['DOWNLOAD_COUNT'] == null
          ? 0
          : json['DOWNLOAD_COUNT'] as dynamic;
      isDelete =
          (json['IS_DEL'] is bool) ? json['IS_DEL'] : json['IS_DEL'] == 1;
      createTime = json['ADD_TIME'] as String;
      updateTime = json['UPDATE_TIME'] as String;
      isHot = (json['IS_HOT'] is bool) ? json['IS_HOT'] : json['IS_HOT'] == 1;
      sourceId = json['SOURCE_ID'] == null ? "" : json['SOURCE_ID'] as String;
      labelId = json['LABEL_ID'] == null ? "" : json['LABEL_ID'] as String;
      sortDependency = (json['SORT'] as int).toString();
      clickNum = json['CLICK_NUM'] == null ? 0 : json['CLICK_NUM'] as dynamic;
      templateType = (json['TEMPLATE_TYPE'] is num)
          ? json['TEMPLATE_TYPE']
          : num.parse(json['TEMPLATE_TYPE']);
      templateClass = (json['TEMPLATE_CLASS'] is num)
          ? json['TEMPLATE_CLASS']
          : num.tryParse(json['TEMPLATE_CLASS']) ?? 0;
      isPrivate = (json['IS_PRIVATE'] is bool)
          ? json['IS_PRIVATE']
          : json['IS_PRIVATE'] == 1;
      amazonCodeBeijing =
          json['AMAZON_CODE'] == null ? null : json['AMAZON_CODE'] as String;
      amazonCodeWuhan = json['AMAZON_CODE_WUHAN'] == null
          ? null
          : json['AMAZON_CODE_WUHAN'] as String;
      sparedCode =
          json['SPARED_CODE'] == null ? null : json['SPARED_CODE'] as String;
      virtualBarCode = json['VIRTUAL_BAR_CODE'] == null
          ? null
          : json['VIRTUAL_BAR_CODE'] as String;
      isNewPath = (json['IS_NEW_PATH'] is bool)
          ? json['IS_NEW_PATH']
          : json['IS_NEW_PATH'] == 1;
      isMobileTemplete = (json['IS_MOBILE_TEMPLETE'] is bool)
          ? json['IS_MOBILE_TEMPLETE']
          : json['IS_MOBILE_TEMPLETE'] == 1;
      phone = json['PHONE'] == null ? null : json['PHONE'] as String;
      goodsCode =
          json['GOODS_CODE'] == null ? null : json['GOODS_CODE'] as String;
      if (json['BARCODE_CATEGORY_MAP'] != null &&
          json['BARCODE_CATEGORY_MAP'].isNotEmpty) {
        barcodeCategoryMap = json['BARCODE_CATEGORY_MAP'].contains("=")
            ? convertToMap(json['BARCODE_CATEGORY_MAP'])
            : Map<String, String>.from(
                (jsonDecode(json['BARCODE_CATEGORY_MAP'])) as Map);
      }

      resourceVersion = json['RESOURCE_VERSION'] == null ? null : json['RESOURCE_VERSION'] as String;
      materialModelSn = json['MATERIAL_MODEL_SN'] == null ? null : json['MATERIAL_MODEL_SN'] as String;
    }
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'isCustom': isCustom ?? false,
        'folderId': folderId ?? "0",
        'adaptPlatformCode': adaptPlatformCode ?? "",
        'adaptPlatformName': adaptPlatformName ?? "",
        'userId': userId ?? "",
        'industryId': industryId ?? "",
        'commodityCategoryId': commodityCategoryId ?? "",
        'downloadCount': downloadCount ?? 0,
        'isDelete': isDelete ?? false,
        'createTime': createTime ?? "",
        'updateTime': updateTime ?? "",
        'lastPrintedAt': lastPrintedAt ?? "",
        'isHot': isHot ?? false,
        'sourceId': sourceId ?? "",
        'labelId': labelId ?? "",
        'sortDependency': sortDependency ?? "",
        'clickNum': clickNum ?? 0,
        'templateType': templateType ?? -1,
        'templateClass': templateClass ?? 1,
        'isPrivate': isPrivate ?? false,
        'amazonCodeBeijing': amazonCodeBeijing ?? "",
        'amazonCodeWuhan': amazonCodeWuhan ?? "",
        'sparedCode': sparedCode ?? "",
        'virtualBarCode': virtualBarCode ?? "",
        'isNewPath': isNewPath ?? false,
        'isMobileTemplete': isMobileTemplete ?? true,
        'phone': phone ?? "",
        'goodsCode': goodsCode ?? "",
        'barcodeCategoryMap': barcodeCategoryMap ?? {},
        'resourceVersion': resourceVersion ?? "",
        'materialModelSn': materialModelSn ?? "",
      };

  Map<String, String> convertToMap(String jsonString) {
    RegExp pattern = RegExp(r'\{(.+?)=(.+?)\}');
    Iterable<Match> matches = pattern.allMatches(jsonString);

    Map<String, String> jsonMap = {};

    if (matches.isNotEmpty) {
      Match match = matches.first;
      String? key = match.group(1);
      String? value = match.group(2);
      if (key != null && value != null) {
        jsonMap[key] = value;
      }
    }

    return jsonMap;
  }
}
