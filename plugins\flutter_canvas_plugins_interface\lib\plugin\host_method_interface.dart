/// 调用宿主方法接口定义
abstract class HostMethodInterface {
  /// 获取当前语言类型
  String getCurrentLanguageType();

  /// 获取缓存地址
  String getCurrentDeviceCachePath();

  /// 获取翻译字段
  String getI18nString(String stringCode, String defaultStr, {List<String>? param, bool isUseDefaultValue = true});

  /// 是否线上环境
  bool isProductEnv();

  ///type=1代表图片ocr识别  type=2代表图片矫正
  Future<Map<String, dynamic>> requestOcr(String photoPath, int type);

  Future<bool> getIsShowRfid();

  /// 跳转到打印设置
  void toPrintSettingPage(Map<String, dynamic> arguments);

  // Future<int> showChangeContentExitAlert(BuildContext context,String canvasJson);
  //
  // Future<String?> updateTemplate(String canvasJson);

  requestLiveCodeList(
      Map<String, dynamic> params, Function(Map data) success, Function(int errorCode, String errorMsg) fail);

  /// 网络判断
  Future<int> customCheckConnectivity();

  Future<Map> getCapHeaderAndRowData();

  Future<bool?> jumpToTemplateFeedbackPage({withContainer = true});

  /// 检查当前语言是否支持显示"画板页高级二维码"入口
  bool isAdvancedQrEntranceSupported(String currentLanguage);

  /// 检查当前语言是否支持显示"自定义表单"入口
  bool isCustomFormEntranceSupported(String currentLanguage);

  /// rtl开关状态
  bool getRtlSwitchStatus();

  Future<Map<String, dynamic>?> getTemplateLayoutFronLabel(Map<String, dynamic> labelInfo);
}
