package com.gengcon.android.jccloudprinter


import ShopTokenBean
import WifiConnectionProxyManager
import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.core.app.NotificationManagerCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.PhoneUtils
import com.gengcon.android.jccloudprinter.flutterActivity.TransparencyPageActivity
import com.gengcon.android.jccloudprinter.flutterActivity.TransparentActionBarActivity
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterDataBuilder
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEventRegister
import com.gengcon.android.jccloudprinter.flutterLogin.PluginLangHelper
import com.gengcon.android.jccloudprinter.flutterLogin.ThirdLoginHelper
import com.gengcon.android.jccloudprinter.importExcel.ImportExcelHelper
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connect.JCConnectionManager
import com.gengcon.connect.base.NiimbotDevice
import com.gengcon.connect.base.NiimbotDeviceType
import com.gengcon.connect.bluetooth.BluetoothListener
import com.gengcon.connect.bluetooth.NiimbotBtDevice
import com.gengcon.connect.wifi.NiimbotWifiDevice
import com.gengcon.connectui.closeNfcActivityEvent
import io.flutter.plugin.common.StringCodec
import com.gengcon.connectui.helper.DeviceETagHelper
import com.gengcon.connectui.helper.eTagStatusListener
import com.gengcon.connectui.helper.screenResultListener
import com.gengcon.print.delegate.CanvasNativeDelegate
import com.gengcon.print.draw.marketRating.MarketRatingManager
import com.gengcon.print.draw.module.event.SHOP_SOURCE_MAIN_BANNER
import com.gengcon.print.draw.print.PrintManager
import com.gengcon.print.draw.module.print.DeviceSerialInfo
import com.gengcon.print.draw.module.print.PDeviceInfo
import com.gengcon.print.draw.module.print.RFIDLabelInfo
import com.gengcon.print.draw.module.qrcode.FormService
import com.gengcon.print.draw.module.qrcode.FormService.KEY_FORM_PRE
import com.gengcon.print.draw.module.qrcode.FormService.getFormSP
import com.gengcon.print.draw.module.qrcode.LiveCodeService
import com.gengcon.print.draw.module.qrcode.LiveCodeService.KEY_LIVECODE_PRE
import com.gengcon.print.draw.print.PrintTaskExecutor
import com.gengcon.print.draw.print.event.ChangePrintDataEvent
import com.gengcon.print.draw.proxy.OnPrintDialogClickListener
import com.gengcon.print.draw.proxy.PrintDialog
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.gengcon.print.draw.proxy.RfidColorProxy
import com.gengcon.print.draw.util.getCurrentDisplayMultipleForPreview
import com.gengcon.print.draw.util.initDisplayMultiple
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.idlefish.flutterboost.FlutterBoost
import com.idlefish.flutterboost.containers.FlutterBoostActivity
import com.jc.repositories.webview.library.eventbus.RefreshShopTokenEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.shop.ShopType
import com.jc.repositories.webview.library.view.webview.JSAction
import com.jc.repositories.webview.library.view.webview.JSActionEvent
import com.jc.repositories.webview.library.view.webview.WebPrintHelper
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.CONNECTED_ETAG_INFO
import com.niimbot.appframework_library.common.module.eventbus.TemplateEvent
import com.niimbot.appframework_library.common.module.eventbus.TemplateEventType
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.external.ExternalData
import com.niimbot.appframework_library.common.module.template.external.Task
import com.niimbot.appframework_library.common.module.template.item.PictureItemModule
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.util.TemplateUtils
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.services.NiimbotServiceFactory
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.ImageSdkJsonUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.ab.ABTestUtils
import com.niimbot.baselibrary.geetest.GeetestHelper
import com.niimbot.baselibrary.googlepay.GooglePayHelper
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.network.JCHttpConfig
import com.niimbot.baselibrary.riskshield.RiskShieldHelper
import com.niimbot.baselibrary.riskshield.RiskShieldHelper.requestAntiCounterfeiKey
import com.niimbot.baselibrary.templateVersion.TemplateVersionConst
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.canvas.image.NBCanvasImageApi
import com.niimbot.fastjson.JSON
import com.niimbot.graphql.GetUserInfoQuery
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.baselibrary.FlutterMethodInvokeConnectManager
import com.niimbot.templatecoordinator.core.EtagTemplateFileUtils
import com.niimbot.templatecoordinator.core.MainTemplateLocalUtils
import com.niimbot.templatecoordinator.core.SilentDownloadResources
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.utiliylibray.util.GetAndroidUniqueMark
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.SystemUtil
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.json2Any
import com.niimbot.utiliylibray.util.json2Array
import com.niimbot.utiliylibray.util.parseJson
import com.niimbot.utiliylibray.util.serializeToMap
import com.niimbot.viplibrary.VipHelper
import com.nimmbot.business.livecode.CapAppHelper
import com.nimmbot.business.livecode.service.MiniPrinterHelper
import com.nimmbot.business.livecode.CapAppHostActivity
import com.qyx.languagelibrary.utils.LanguageSPHelper
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.tencent.bugly.crashreport.CrashReport
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.BasicMessageChannel
import io.flutter.plugin.common.MessageCodec
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.sentry.Sentry
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.base.LaboratoryActivity
import melon.south.com.baselibrary.bluetooth.BluetoothStatusManager
import melon.south.com.baselibrary.dao.TemplateModuleDao
import melon.south.com.baselibrary.dao.TemplateSortModuleDao
import melon.south.com.baselibrary.eventbus.AbleToPrint
import melon.south.com.baselibrary.eventbus.FinishTemplateIndustryEvent
import melon.south.com.baselibrary.eventbus.MeBubbleChangeEvent
import melon.south.com.baselibrary.eventbus.ShopBubbleChangeEvent
import melon.south.com.baselibrary.local.module.DevicesModule
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.local.module.TemplateSortModule
import melon.south.com.baselibrary.local.util.DaoFactory
import melon.south.com.baselibrary.local.util.DevicesLocalUtils
import melon.south.com.baselibrary.local.util.DevicesSeriesLocalUtils
import melon.south.com.baselibrary.local.util.LanguageCacheUtils
import melon.south.com.baselibrary.util.CacheUtils
import melon.south.com.baselibrary.util.DataCacheUtils
import melon.south.com.baselibrary.util.OtherAppUtil
import melon.south.com.baselibrary.util.TemplateDbUtils
import melon.south.com.loginlibrary.module.UserApiManager
import melon.south.com.loginlibrary.util.LoginUtils
import melon.south.com.mainlibrary.settings.activity.AppInfoDialog
import melon.south.com.mainlibrary.settings.manager.AppCacheHelper
import melon.south.com.mainlibrary.util.NpsUtils
import melon.south.com.mainlibrary.v.MainActivity
import melon.south.com.mainlibrary.v.event.CloseNpsEvent
import melon.south.com.templatelibrary.utils.VersionCompareUtils
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.templatelibrary.utils.ShareTemplateUtils
import melon.south.com.mainlibrary.nps.NpsKeys
import melon.south.com.mainlibrary.nps.NpsManager
import org.greenrobot.eventbus.EventBus
import org.json.JSONException
import org.json.JSONObject
import java.lang.ref.WeakReference
import java.util.*
import kotlin.collections.HashMap


//import io.sentry.*
import java.util.LinkedList
interface channelCallBack {
    fun result(value: String)
}

object FlutterChannelRegister {

    private lateinit var observers: channelCallBack


    private const val NIIMBOT_METHOD_CHANNEL = "com.niimbot.printer/Method/Channel"

    private const val NIIMBOT_BASIC_MESSAGE_CHANNEL = "com.niimbot.printer/basicMessage/channel"

    /**获取原生 UA
     * 返回参数{“agent”:String}
     */
    private const val METHOD_GET_AGENT = "get_Agent"

    /**
     * 获取应用信息
     */
    private const val METHOD_GET_APP_INFO = "getAppInfo"

    /**获取翻译详情
     * 返回参数 [Map]
     */
    private const val METHOD_GET_LANGUAGE_DETAIL = "getLanguageDetail"

    /**获取原生设备系列信息
     * 返回参数 [Map]
     */
    private const val METHOD_GET_DEVICE_SERIES = "getDeviceSeriesData"

    /**向原生返回设备系列信息
     * 方法参数 [Map]
     */
    private const val METHOD_SET_DEVICE_SERIES = "set_DeviceSeriesList"

    /**向原生返回已选择设备系列信息
     * 方法参数 {…}   不带参数时  设置默认系列
     */
    private const val METHOD_SET_SELECTED_DEVICE_SERIES = "set_selectedDeviceSeries"

    /**跳转原生界面
     * 方法参数 {"vcName":”ToNiimbotHomePage"}
     */
    private const val METHOD_GOTO_NATIVE_PAGE = "goToNativePage"

    /**跳转原生界面
     * 方法参数 [Map] {"deviceList":”value"}
     */
    private const val METHOD_SET_DEVICE_LIST = "set_DeviceList"

    private const val METHOD_GET_PPI_FROM_NATIVE = "getPPIFromNative"

    private const val METHOD_GET_SCALE_FROM_NATIVE = "getScaleFromNative"

    /**
     * 获取当前语言类型
     */
    private const val METHOD_GET_APP_CURRENT_LANGUAGE_TYPE = "getAppCurrentLanguageType"

    /**
     * 调用原生埋点方法
     */
    private const val METHOD_SEND_TRACK_TO_NATIVE = "sendTrackingToNative"

    /**
     * Flutter上报登录成功
     */
    private const val METHOD_REPORT_LOGIN_SUCCESS = "reportLoginSuccess"

    /**
     * 获取支持的社交登录方式
     */
    private const val METHOD_GET_SUPPORT_SOCIAL_LIST = "getSupportSocialList"

    private const val METHOD_SIM_ONE_KEY_CONFIG = "SIMAccountOneKeyConfig"

    /**
     * 调用原生一键登录
     */
    private const val METHOD_SIM_ONE_KEY_LOGIN = "SIMAccountOneKeyLogin"

    /**
     * 调用原生一键注册
     */
    private const val METHOD_SIM_ONE_KEY_REGISTER = "SIMAccountOneKeyRegister"

    /**
     * 调用原生一键绑定
     */
    private const val METHOD_SIM_ONE_KEY_BINDING = "SIMAccountOneKeyBinding"

    /**
     * 检查一键登录状态
     */
    private const val METHOD_CHECK_SIM_INFO = "checkSIMCardInfo"

    /**
     * 回调SIM状态检查结果
     */
    private const val METHOD_NOTIFY_SIM_CHECK_RESULT = "notifyCheckSIMAccountStatusResult"

    /**
     * 请求相机和存储权限
     */
    private const val METHOD_REQUEST_SD_AND_CAMERA_PERMISSION = "authorization_Status"

    /**
     * 通知native退出登录
     */
    private const val METHOD_NOTIFY_LOGOUT = "log_Out"

    /**
     * 獲取插件語言包
     */
    private const val METHOD_GET_LANGUAGE_DETAIL_PLUGIN = "getLoginPluginLanguageDetail"

    /**
     * flutter通知native刷新用户信息
     */
    private const val METHOD_UPDATE_USER_INFO = "updateUserInfo"

    /**
     * 设置token事件
     */
    private const val METHOD_SET_TOKEN = "set_Token"

    /**
     * 获取系统国家和语言
     */
    private const val METHOD_GET_LOCALE_LANGUAGE = "getLocaleLanguage"

    /**
     * 禁止IOS侧滑(空实现)
     */
    private const val METHOD_DISALLOWED_SIDE_SLIP_FOR_IOS = "setFlutterVCCanSideslip"

    /**
     * 获取用户信息
     */
    private const val METHOD_GET_USER_INFO = "getUserInfo"

    /**
     * 取消登录
     */
    private const val METHOD_CANCEL_LOGIN = "cancelLogin"

    /**
     * 绑定主账号成功
     */
    private const val METHOD_BIND_MAIN_ACCOUNT_SUCCESS = "bindMainAccountSuccess"

    /**
     * 获取app接口环境
     * dev: 2
     * test: 1
     * production: 0
     */
    private const val METHOD_GET_APP_ENV = "getAppEnv"

    /**
     * 获取标签纸的状态
     */
    private const val METHOD_GET_PRINTER_LABEL = "getPrinterLabelData"

    /**
     * 获取打印机的连接状态
     */
    private const val METHOD_GET_PRINTER_STATE = "getPrinterConnectState"

    /**
     * 获取打印浓度
     */
    private const val METHOD_GET_DENSITY = "getDensity"


    /**
     * 获取打印设置信息
     */
    private const val METHOD_GET_PRINT_SETTING_MSG = "getPrintSettingMsg"


    /**
     * 获取打印机的连接状态
     */
    private const val METHOD_GET_DEVICE_SERIESID = "getDeviceSeries"

    /**
     * 获取本地标签纸记录缓存
     */
    private const val METHOD_GET_RECENT_LABEL = "getUseRecentLabel"

    /**
     * 电子价签自动回连
     */
    private const val METHOD_ETAG_CONNECT = "getETagConnect"

    /**
     * 商品数据模块混入
     */
    private const val METHOD_GOODS_MIX_LIST = "goodsMixList"
    private const val METHOD_GOODS_MIX_LIST_ALL = "goodsMixListAll"

    /**
     * 本轮数据写入完成
     */
    private const val METHOD_END_SEND_PICTURES = "endSendPictures"

    /**
     * 电子价签状态监听启动
     */
    private const val METHOD_ETAG_STATUS_LISTENER = "eTagStatusListener"

    /**
     * 断开价签机连接
     */
    private const val METHOD_ETAG_DISCONNECT = "etagDisConnect"

    /**
     * 重置价签机状态
     */
    private const val METHOD_RESET_ETAG_STATUS = "resetEtagStatus"


    /**
     * 价签信息同步屏幕
     */
    private const val METHOD_WRITE_SCREEN_DATA = "writeScreenData"

    /**
     * 获取模板预览视图
     */
    private const val METHOD_GET_PREVIEW_PICTURE = "getPreviewPicture"


    /**
     * 获取模板预览视图
     */
    private const val METHOD_SET_CONNECT_DEVICE = "setConnectDevice"

    /*
    * 获取非vip用户批量打印次数
    * */
    private const val METHOD_UN_VIP_BATCH_PRINT_COUNT = "unVipBatchPrintCount"

    /**
     * 获取NFC屏幕信息
     */
    private const val METHOD_GET_APP_CONFIG = "get_app_config"

    /**
     * 获取NFC屏幕信息
     */
    private const val METHOD_TO_ETAG_PAGE = "toEtagPage"

    /**
     * 关闭获取NFC信息界面
     */
    private const val METHOD_CLOSE_NFC_TOP_ACTIVITY = "closeNfcTopActivity"

    /**
     * 关闭打印机连接
     */
    private const val CLOSE_HARDWARE_CONNECTED = "closeHardWareConnected"

    /**
     * 保存模板
     */
    private const val METHOD_SAVE_TEMPLATE = "saveTemplate"

    /**
     * 是否显示vip
     */
    private const val METHOD_IF_SHOW_VIP = "ifShowVip"

    /**
     * 是否为登录状态
     */
    private const val METHOD_CHECK_LOGIN_STATUS = "checkLoginStatus"

    /**
     * 获取用户id
     */
    private const val METHOD_GET_USER_ID = "getUserId"

    /**
     * Excel导入的社交列表
     */
    private const val METHOD_GET_IMPORT_FILE_SOCIAL_LIST = "getImportFileSocialList"

    /**
     * 从本地导入Excel
     */
    private const val METHOD_IMPORT_EXCEL_FROM_LOCAL = "importFileFromLocal"

    /**
     * 从三方社交导入Excel
     */
    private const val METHOD_IMPORT_EXCEL_FROM_SOCIAL_APP = "importExcelFromSocialApp"

    /**
     * 获取模板详情
     * 设计相关图片的缓存
     */
    private const val METHOD_GET_TEMPLATE_DETAIL = "getTemplateDetail"

    /**
     * 更新标签纸最近使用
     */
    private const val METHOD_UPDATE_LABEL_USED_RECORD = "updateLabelUsedRecord"

    /**
     * 获取线缆标签纸详情
     */
    private const val METHOD_GET_CABLE_DETAIL = "getCableDetail"

    /**
     * 获取条码输入文字是否符合格式结果
     */
    private const val METHOD_CHECK_BARCODE_FORMAT = "checkBarcodeFormat"

    /**
     * 商城角标更新
     */
    private const val METHOD_UPDATE_SHOPINFO_NUMBERS = "shopInfoNotifyNumbers"

    /**
     * 从native获取用户缓存信息
     */
    private const val METHOD_GET_LOCAL_USERINFO_CACHE = "getLocalUserInfoCache"

    /**
     * 获取NFC是否正常接触
     */
    private const val METHOD_GET_NFC_CONNECT_STATE = "getNfcConnectState"

    /**
     * 获取单色热敏纸颜色或碳带颜色
     */
    private const val METHOD_GET_CURRENT_SINGLE_COLOR_INFO = "getCurrentSingleColorInfo"

    /**
     * 红黑双色打印颜色信息
     */
    private const val METHOD_GET_CURRENT_PAPER_SUPPORT_COLORS = "getCurrentPaperSupportColors"

    /**
     * 获取当前连接的打印机
     */
    private const val METHOD_GET_CONNECT_DEVICE = "getConnectDevice"

    /**
     * 更新替换rfid的标记
     */
    private const val METHOD_SYNC_REPLACE_RFID_TAG = "syncReplaceRfidTag"

    /**
     * 获取线缆字体路径
     */
    private const val METHOD_APP_FONT_PATH = "appFontPath"


    /**
     * 获取批量打印资源是否下载完成
     */
    private const val METHOD_CHECK_BATCH_SOURCES = "checkBatchPrintSources"

    /**
     * 更新线缆入口标记
     */
    private const val METHOD_SET_SP_DATA = "setSPData"


    /**
     * 同步打印偏移量
     */
    private const val METHOD_SET_DEVICE_OFFSET = "setDeviceOffset"

    /**
     * 获取rfid标签状态
     */
    private const val METHOD_GET_RFID_TAG = "getRfidTag"

    /*
     * 用户注销成功
     */
    private const val METHOD_USER_OFF_SUCCESS = "userOffSuccess"

    /**
     * 根据打印机id获取机器详情
     */
    private const val METHOD_GET_SELECT_PRINT_BY_ID = "getPrinterById"

    /**
     * DIO抓包代理
     */
    private const val METHOD_GET_PROXY_URL = "getDioProxyUrl"

    /**
     * 获取手机唯一id
     */
    private const val METHOD_GET_DEVICE_ID = "getDeviceId"

    /**
     * 是否强制LTR模式
     */
    private const val METHOD_IS_FORCE_LTR = "isForceLTR"

    /**
     * 是否已保存为我的模板（已保存为我的模板情况下，画板内点击保存显示另存为）
     */
    private const val METHOD_IS_EXIST_TEMPLATE = "isExistTemplate"

    /**
     * 获取im连接相关参数
     */
    private const val METHOD_GET_IM_PARAMS = "getImParams"

    /**
     * 是否可以打开模版
     */
    private const val METHOD_CAN_OPEN_HISTORY_TEMPLATE = "isCanOpenHistoryTemplate"

    /**
     * Flutter异常捕获
     */
    private const val METHOD_POST_CATCHED_EXCEPTION = "postCatchedException"

    /**
     * 创建活码/表单
     */
    private const val METHOD_CREATE_LIVE_CODE = "createAdvanceQRCode"

    /**
     * 预览活码/表单
     */
    private const val METHOD_PREVIEW_ADVANCE_CODE = "previewAdvanceQRCode"

    /**
     * 预览活码/表单
     */
    private const val METHOD_GET_ADVANCE_CODE = "getAdvanceQRCodeInfo"

    /**
     * 缓存活码/表单
     */
    private const val METHOD_CACHE_ADVANCE_CODE = "cacheAdvanceQRCodeInfo"

    /**
     * 通过ids获取高级二维码 包含 活码/表单本地缓存
     */
    private const val METHOD_GET_ADVANCE_CACHES = "getAdvanceQRCodeCaches"

    /**
     * 关闭nps入口
     */
    private const val METHOD_CLOSE_NPS = "closeNps"

    /**
     * 我的模板刷新事件
     */
    private const val METHOD_MY_TEMPLATE_REFRESH = "nativeTemplateNeedRefresh"

    /**
     * 我的模板数据库更新
     */
    private const val METHOD_MY_TEMPLATE_DBUPDATE = "nativeTemplateDBUpdate"

    /**
     * ionic小程序发送打印完成事件
     */
    private const val METHOD_PRINT_COMMODITY = "printCommodity"

    /**
     * 下载模版详情
     */
    private const val METHOD_DOWNLOAD_TEMPLATE_DETAIL = "downloadTemplateDetail"

    /**
     * 去掉要下载的模版详情
     */
    private const val METHOD_REMOVE_TEMPLATE_DETAIL = "removeTemplateDetail"

    /**
     * 取消要下载的模版详情
     */
    private const val METHOD_CANCEL_TEMPLATE_DETAIL = "cancelDownloadTemplateDetail"

    /**
     * 检查要下载的模版详情
     */
    private const val METHOD_CHECK_TEMPLATE_DETAIL = "checkTemplateInfoComplate"

    /**
     * 获取SP缓存
     */
    private const val METHOD_GET_SP_DATA = "getSPData"

    /**
     * 获取SP缓存int
     */
    private const val METHOD_GET_INT_SP_DATA = "getIntSPData"

    /**
     * 获取vip状态
     */
    private const val METHOD_GET_VIP_STATUS = "getVipStatus"

    private const val METHOD_SHOW_SCAN_LOGIN_SUCCESS = "showScanLoginSuccess"

    /**
     * 是否打印
     */
    private const val METHOD_ABLE_TO_PRINT = "ableToPrint"

    /**
     * 判断某个电子价签模版是否存在
     */
    private const val METHOD_IS_EXIST_ETAG_TEMPLATE = "isExistEtagTemplate"

    /**
     * 调用原生nps弹窗
     */
    private const val METHOD_TO_NPS_ALERT = "toNpsAlert"

    /**
     * 调用原生判断是否触发市场评分弹窗
     */
    private const val METHOD_IS_TRIGGER_MARKET_RATING_POP = "isTriggerMarketRatingPop"

    /**
     * 关闭所有flutter承载页
     */
    private const val METHOD_FINISH_FLUTTER_PAGES = "finishFlutterPages"

    /**
     * 获取app最大支持的版本号
     */
    private const val METHOD_MAX_SUPPORT_TEMPLATE_VERSION = "getMaxSupportTemplateVersion"

    /**
     * 请求极验验证
     */
    private const val METHOD_REQUEST_GEETEST_VERIFY = "requestGeetestVerify"

    /**
     * 设置rfid数据
     */
    private const val METHOD_SET_SDK_RFID_DATA = "setSDKRfidData"

    /**
     * 通知服务端rfid数据
     */
    private const val METHOD_REFRESH_SERVER_RFID_INFO = "refreshServerRFIDInfo"

    /**
     * 设置rfid颜色
     */
    private const val METHOD_SET_RFID_COLOR = "setRfidColor"

    /**
     * 在原生更换打印数据
     */
    private const val METHOD_CHANGE_PRINT_DATA = "changePrintData"

    private const val METHOD_GET_LABEL_SHOP_LINK = "getLabelShopLink"

    private const val METHOD_ARE_NOTIFICATION_ENABLED = "areNotificationEnabled"

    private const val METHOD_JUMP_TO_NOTIFICATION_SETTING = "jumpToNotificationSetting"

    private const val METHOD_NOTIFY_UNREAD_MESSAGE_COUNT = "notifyUnreadMessageCount"

    private const val METHOD_JUMP_TO_MESSAGE_DETAIL = "jumpToMessageDetail"

    private const val METHOD_REFRESH_TEMPLATE_LIST = "refreshNativeTemplateList"
    private const val METHOD_DOWNLOAD_TEMPLATE_FONTS = "downloadTemplateFonts"

    /**
     * 进入线号机C1首页
     */
    private const val METHOD_NOTIFY_ENTER_C1_PAGE = "notifyEnterC1Page"

    /**
     * 退出线号机C1首页
     */
    private const val METHOD_NOTIFY_BACK_FROM_C1_PAGE = "notifyBackFromC1Page"

    private const val METHOD_TO_C1_HOME_PAGE = "toC1HomePage"

    private const val METHOD_GET_NATIVE_NETWORK_STATE = "getNativeNetworkState"

    private const val METHOD_GET_ANONYMOUS_ID = "getAnonymousId"

    /**
     * 搜索到蓝牙打印机回调
     */
    private const val METHOD_FOUND_BLUETOOTH_PRINTER = "foundBluetoothPrinter"

    /**
     * 搜索到wifi打印机回调
     */
    private const val METHOD_FOUND_WIFI_PRINTER = "foundWifiPrinter"

    /**
     * 蓝牙搜索过程结束回调
     */
    private const val METHOD_DISCOVER_BLUETOOTH_PRINTER_FINISHED = "discoverBluetoothPrinterFinished"

    /**
     * wifi搜索过程结束回调
     */
    private const val METHOD_DISCOVER_WIFI_PRINTER_FINISHED = "discoverWifiPrinterFinished"

    /**
     * 打印机断开连接回调
     */
    private const val METHOD_DISCONNECT_CALLBACK = "disconnectCallback"

    /**
     * 电量回调
     */
    private const val METHOD_ELECTRICITY_CALLBACK = "electricityCallback"

    /**
     * 开合盖状态回调
     */
    private const val METHOD_COVER_STATUS_CALLBACK = "coverStatusCallback"

    /**
     * 是否安装标签纸回调
     */
    private const val METHOD_PAPER_STATUS_CALLBACK = "paperStatusCallback"

    /**
     * 是否读到标签纸rfid信息回调
     */
    private const val METHOD_PAPER_RFID_STATUS_CALLBACK = "paperRfidStatusCallback"

    /**
     * 是否安装碳带回调
     */
    private const val METHOD_RIBBON_STATUS_CALLBACK = "ribbonStatusCallback"

    /**
     * 是否读到碳带rfid信息回调
     */
    private const val METHOD_RIBBON_RFID_STATUS_CALLBACK = "ribbonRfidStatusCallback"

    private const val METHOD_TO_RISK_CHECK_DIALOG = "toRiskCheckDialog"

    /*
     * 跳转评分
    * */
    private const val METHOD_TO_MARKET_RATING = "toMarketRating"

    /**
     * 无界面打印错误弹窗处理
     */
    private const val METHOD_UN_UI_PRINT_ERROR_EVENT = "unUIPrintErrorEvent"

    /**
     * 标签记录
     */
    private const val METHOD_LABEL_RECORD = "labelRecord"

    /**
     * 无UI打印完成
     */
    private const val METHOD_UN_NEED_UI_PRINT_COMPLATE = "unNeedUIPrintComplate"

    var hasConfirmAutoRFID = false
    private const val METHOD_SYNC_OUT_OF_SERVICE = "syncOutOfService"

    /**
     * 获取商城token
     */
    private const val METHOD_GET_USER_MALL_TOKEN = "getUserMallToken"

    /**
     * 查看危废打印台账
     */
    private const val METHOD_CHECK_DANGER_RECORD = "checkDangerRecord"

    /**
     *向小程序发送生成预览图事件
     */
    private const val METHOD_SEND_CAPGENERATE_TEMPLATE_PREVIEW_EVENT = "sendCapGenerateTemplatePreviewEvent"

    private const val METHOD_NOTIFY_FLUTTER_CHANNEL = "notifyFlutterChannel"

    /**
     *flutter-native更新活码信息
     */
    private const val METHOD_UPDATE_ADVANCE_QRCODE_TO_NATIVE = "updateAdvanceQRCodeToNative"

    /**
     * 是否为Debug模式
     */
    private const val METHOD_CHECK_DEBUG_MODE = "checkDebugMode"

    /**
     *flutter-native 分享模版
     */
    private const val METHOD_SHARE_TEMPLATE = "shareTemplate"

    /**
     * 路由跳转
     */
    private const val METHOD_PUSH_TO_ROUTE = "pushToRoute"

    private const val METHOD_NOTIFY_TO_CANVAS_PAGE = "notifyToCanvasPage"

    private const val METHOD_NOTIFY_PRINT_PROCESS_STATUS = "notifyPrintProcessStatus"

    var mPrintDialog: PrintDialog? = null

    fun handleRegisterEngine(context: Context, flutterEngine: FlutterEngine) {
        FlutterEventRegister.register(flutterEngine)
        val messenger = flutterEngine.dartExecutor.binaryMessenger
        val methodChannel = MethodChannel(messenger, NIIMBOT_METHOD_CHANNEL)
        ABTestUtils.initData(methodChannel)
        FlutterMethodInvokeManager.initData(methodChannel)
        FlutterMethodInvokeConnectManager.initData(methodChannel)
        RiskShieldHelper.initData(methodChannel)
        AppCacheHelper.initData(methodChannel)
        val messageChannel = BasicMessageChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            NIIMBOT_BASIC_MESSAGE_CHANNEL,
            StringCodec.INSTANCE
        )

        messageChannel.setMessageHandler { message, reply ->
            when (JSONObject(message!!)["actionName"] as String) {
                METHOD_FOUND_BLUETOOTH_PRINTER -> {
                    val printer = JSONObject(message)["printer"] as JSONObject
                    val name = (printer["name"] as? String) ?: ""
                    val mac = (printer["mac"] as? String) ?: ""
                    val alias = (printer["alias"] as? String) ?: ""
                    val niimbotBtDevice = NiimbotBtDevice(name, mac, alias)
                    JCConnectionManager.getInstance().notifyBluetoothDeviceFound(niimbotBtDevice)
                }
                METHOD_FOUND_WIFI_PRINTER -> {
                    val printer = JSONObject(message)["printer"] as JSONObject
                    val name = (printer["name"] as? String) ?: ""
                    val ip = (printer["ip"] as? String) ?: ""
                    val port = (printer["port"] as? Int) ?: 0
                    val available = (printer["available"] as? Int) ?: 0
                    val alias = (printer["alias"] as? String) ?: ""
                    //老版本sdk返回的ip和address都是ip地址，新版本sdk没有返回address
                    val niimbotWifiDevice = NiimbotWifiDevice(name, ip, alias).apply {
                        wifiIP = ip
                        wifiPort = port
                        connNum = available
                    }
                    JCConnectionManager.getInstance().notifyWifiDeviceFound(niimbotWifiDevice)
                }
                "wifiRssiCallback" -> {
                    val rssi = (JSONObject(message)["rssi"] as? Int)
                    if(rssi != null){
                        JCConnectionManager.getInstance().notifyWifiRssi(rssi)
                    }
                }
                "printerFirmwareUpgrade" -> {
                    val gson = Gson()
                    val mapType = object : TypeToken<Map<String, Any>>() {}.type
                    val resultMap: Map<String, Any> = gson.fromJson(message, mapType)
                    if (BaseApplication.upgradeListener != null) {
                        BaseApplication.upgradeListener.onSuccess(resultMap["printerFirmwareUpgrade"])
                    }
                }
            }
        }

        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                METHOD_SHARE_TEMPLATE -> {
                    try {
                        val templateStr = call.argument<String>("template") ?: ""
                        val moduleLocal = TemplateModuleLocal.fromJson(templateStr)
                        val utils = ShareTemplateUtils(FlutterBoost.instance().currentActivity(), moduleLocal!!)
                        utils.showSharePlatformDialog()
                    }catch (e:Exception){
                        e.printStackTrace()
                    }

                }
                METHOD_UPDATE_ADVANCE_QRCODE_TO_NATIVE -> {
                    try {
                        val arguments = call.arguments
                        val livecodeJson = any2Json(arguments)
                        val liveCode = GsonUtils.fromJson(
                            livecodeJson,
                            LiveCodeService.LiveCode::class.java
                        )
                        LiveCodeService.freshLiveCodeCache(liveCode)
                    }catch (e:Exception){
                        e.printStackTrace()
                    }

                }
                METHOD_DOWNLOAD_TEMPLATE_FONTS -> {
                    val templateStr = call.argument<String>("template") ?: ""
                    val moduleLocal2 = TemplateModuleLocal.fromJson(templateStr)
                    val templateModule2 =
                        TemplateModuleTransform.templateModuleLocalToTemplateModule(moduleLocal2!!)
                    TemplateSyncLocalUtils.unDownloadList?.clear()
                    TemplateSyncLocalUtils.checkUnDownloadFonts(FlutterBoost.instance().currentActivity(), templateModule2) {
                        result.success(it)
                    }
                }
                METHOD_REFRESH_TEMPLATE_LIST -> {
                    val templateEvent = TemplateEvent()
                    templateEvent.eventType = TemplateEventType.UPDATE_PERSONAL_TEMPLATE_SYNC
                    EventBus.getDefault().post(templateEvent)
                }
                METHOD_SEND_CAPGENERATE_TEMPLATE_PREVIEW_EVENT -> {
                    val imageBase64 = call.argument<String>("imageBase64") ?: ""
                    EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                        put("action", "imageDataForIonic")
                        put("imageData", imageBase64)
                    }.toJSONString())
                }
                METHOD_CHECK_DANGER_RECORD -> {
                    //flutter通知小程序跳转到危废台账页面
                    EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                        put("action", "jumpToRecord")
                    }.toJSONString())
                }
                METHOD_GET_USER_MALL_TOKEN -> {
                    UserApiManager.getShopToken(object : JCTResponseListener<ShopTokenBean> {
                        override fun onSuccess(body: ShopTokenBean) {
                            body?.shopToken?.let {
                                HttpTokenUtils.freshShopToken(it)
                            }
                            EventBus.getDefault().post(RefreshShopTokenEvent())
                            result.success(null)
                        }

                        override fun onError(message: String) {
                            result.success(null)
                        }
                    })
                }
                METHOD_JUMP_TO_MESSAGE_DETAIL -> {
                    val linkRouteType = call.argument<Int>("linkRouteType") ?: 1
                    val linkTitle = call.argument<String>("linkTitle") ?: ""
                    val linkUrl = call.argument<String>("linkUrl") ?: ""
                    NiimbotGlobal.bannerRouter(FlutterBoost.instance().currentActivity(),linkUrl,linkRouteType,linkTitle)
                }
                METHOD_NOTIFY_UNREAD_MESSAGE_COUNT -> {
                    val unreadMessageCount = call.argument<Int>("unreadMessageCount")
                    EventBus.getDefault().post(MeBubbleChangeEvent(unreadMessageCount!!))
                }
                METHOD_ARE_NOTIFICATION_ENABLED -> {
                    val notificationEnabled = NotificationManagerCompat.from(context).areNotificationsEnabled()
                    result.success(notificationEnabled)
                }
                METHOD_JUMP_TO_NOTIFICATION_SETTING -> {
                    val intent = Intent().apply {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            // Android 8.0 及以上
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                            putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                        } else {
                            // Android 7.1 及以下
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                            data = Uri.fromParts("package", context.packageName, null)
                        }
                    }
                    context.startActivity(intent)
                }
                METHOD_CHANGE_PRINT_DATA -> {
                    val templateJson = call.argument<String>("templateJson")
                    EventBus.getDefault().post(ChangePrintDataEvent(templateJson))
                }

                METHOD_TO_MARKET_RATING -> {
                    val userPrintCount = call.argument<Int>("userPrintCount") ?: 0
                    MarketRatingManager.getInstance()
                        .MarketRatingMet(FlutterBoost.instance().currentActivity(),userPrintCount)
                }

                METHOD_UN_NEED_UI_PRINT_COMPLATE -> {
                    val status = call.argument<String>("status")
                    val uniAppId = call.argument<String>("uniAppId")
                    val taskId = call.argument<String>("taskId")
                    val message = call.argument<String>("message")
                    val params = com.niimbot.fastjson.JSONObject()
                        .apply {
                            put("taskId", taskId)
                            put("success", status == "1")
                        }
                    CapAppHelper.sendCapAppEvent(
                        eventName = "print-complete",
                        message = params.toJSONString()
                    )
                    val params1 = com.niimbot.fastjson.JSONObject()
                        .apply {
                            put("taskId", taskId)
                            put("status", status)
                            put("message", message)
                            put("uniAppId", uniAppId)
                        }
                    EventBus.getDefault().post(JSActionEvent(JSAction.JS_PRINT_COMPLETE, params1.toJSONString()))
                }

                METHOD_LABEL_RECORD -> {
                    val templateData = call.arguments
                    val printJson = com.niimbot.fastjson.JSONObject()
                    printJson["action"] = "labelRecord"
                    printJson["data"] = any2Json(templateData)
                    EventBus.getDefault().post(any2Json(printJson))
                }

                METHOD_TO_RISK_CHECK_DIALOG -> {
                    RiskShieldHelper.processRisk(call.arguments)
                }

                METHOD_SET_RFID_COLOR -> {
                    val rfidPaperColor = call.argument<String>("paperColor") ?: ""
                    val rfidRibbonColor = call.argument<String>("ribbonColor") ?: ""
                    RFIDConnectionProxyManager.postServerRFIDGet(rfidPaperColor, rfidRibbonColor)
                    CapAppHelper.sendRfidColorChangeEvent(rfidPaperColor,rfidRibbonColor)
                }

                METHOD_UN_UI_PRINT_ERROR_EVENT -> {
                    val isPaused = call.argument<Boolean>("isPaused")
                    val errorReason = call.argument<String>("errorReason")
                    val uniAppId = call.argument<String>("uniAppId")
                    val taskId = call.argument<String>("taskId")
                    LogUtils.e("startPrint unuiprint error printDialog showing=${mPrintDialog?.isShowing()} errorReason=${errorReason} uniappId=$uniAppId")
                    if (!MiniPrinterHelper.needNativeUI) {
                        val params1 = com.niimbot.fastjson.JSONObject()
                            .apply {
                                put("taskId", taskId)
                                put("status", "0")
                                put("message", errorReason)
                                put("uniAppId", uniAppId)
                            }
                        EventBus.getDefault().post(JSActionEvent(JSAction.JS_PRINT_COMPLETE, params1.toJSONString()))
                        return@setMethodCallHandler
                    }
                    if(mPrintDialog?.isShowing() == true){
                        return@setMethodCallHandler
                    }
                    mPrintDialog =
                        PrintDialog(CapAppHelper.bridge?.activity ?: FlutterBoost.instance().currentActivity(), true).apply {
                            setDialogFunClickListener(object : OnPrintDialogClickListener {
                                override fun onCancelClick() {
                                    mPrintDialog?.dismiss()
                                    if (isPaused == true) {
                                        result.success(0)
                                    }

                                }

                                override fun onCancelAndEndClick() {
                                    mPrintDialog?.dismiss()
                                    if (isPaused == true) {
                                        result.success(0)
                                    } else {
                                        val params = com.niimbot.fastjson.JSONObject()
                                            .apply {
                                                put("taskId", PrintTaskExecutor.capTaskId)
                                                put("success", false)
                                            }
                                        CapAppHelper.sendCapAppEvent(
                                            eventName = "print-complete",
                                            message = params.toJSONString()
                                        )
                                    }
                                }

                                override fun onPauseClick() {
                                }

                                override fun onResumeClick() {
                                    mPrintDialog?.dismiss()
                                    result.success(1)
                                }

                                override fun onCloseClick() {
                                    mPrintDialog?.dismiss()
                                    if (isPaused == true) {
                                        result.success(0)
                                    } else {
                                        val params = com.niimbot.fastjson.JSONObject()
                                            .apply {
                                                put("taskId", PrintTaskExecutor.capTaskId)
                                                put("success", false)
                                            }
                                        CapAppHelper.sendCapAppEvent(
                                            eventName = "print-complete",
                                            message = params.toJSONString()
                                        )
                                    }
                                }
                            })
                        }
                    if (isPaused == true) {
                        mPrintDialog?.updatePrinterState(
                            PrintDialog.printStatePause,
                            tipMsg = errorReason ?: "",
//                            errMsg = LanguageUtil.findLanguageString("app01249")
                        )
                    } else {
                        mPrintDialog?.updatePrinterState(
                            PrintDialog.printStateCancel,
                            tipMsg = errorReason ?: "",
//                            errMsg = LanguageUtil.findLanguageString("app00036")
                        )
                    }
                    mPrintDialog?.showPrintDialog()
                    mPrintDialog?.setDismissListener{
                        mPrintDialog = null
                        Log.i("wangxuhao","mprintDialog dismiss")
                    }
                }

                METHOD_FINISH_FLUTTER_PAGES -> {
                    val activities = ActivityUtils.getActivityList()
                    for (i in 0 until activities.size - 1) {
                        val activity = activities[i]
                        if (activity == ActivityUtils.getTopActivity()) {
                            continue
                        }
                        if (activity.javaClass.simpleName.contains("DrawboardIMActivity")) {
                            activity.finish()
                        }
                    }
                    NiimbotGlobal.finishCanvasPage()
                    result.success(true)
                }

                METHOD_GET_ANONYMOUS_ID -> {
                    result.success(BuriedHelper.getEventAnonymousId())
                }

                METHOD_MAX_SUPPORT_TEMPLATE_VERSION -> {
                    val maxSupportTemplateVersion =
                        TemplateVersionConst.getAppMaxSupportTemplateVersion()
                    result.success(maxSupportTemplateVersion)
                }

                METHOD_TO_NPS_ALERT -> {
                val source = call.argument<String>("source") ?: ""
                val isUniApp = call.argument<Boolean>("isUniApp") ?: false
                val uniAppId = call.argument<String>("uniAppId") ?: ""
                if (isUniApp) {
                    NpsManager.showNpsDialog(
                        FlutterBoost.instance().currentActivity(),
                        NpsKeys.NPS_CAP, source, uniAppId
                    )
                } else {
                    NpsUtils.showNpsDialog(
                        FlutterBoost.instance().currentActivity(),
                        "",
                        source
                    )
                }
            }
                METHOD_IS_TRIGGER_MARKET_RATING_POP -> {
                    val userPrintCount = call.argument<Int>("userPrintCount") ?: 0
                    val isMet = isChineseMarketRatingMet(userPrintCount)
                    result.success(isMet)
                }
//                METHOD_MY_TEMPLATE_DBUPDATE -> {
//                    (call.arguments as? Map<String, Any>)?.let {
//                        DaoFactory.templateModuleDao().queryBuilder()
//                            .where(TemplateModuleDao.Properties.Id.eq(it["id"])).unique()
//                            ?.let { localTemplate ->
//                                localTemplate.local_type = it["localType"] as Int;
//                                if (it["name"] != "") {
//                                    localTemplate.name = it["name"] as String;
//                                }
//                                if (it["folderId"] != "") {
//                                    localTemplate.folder_id = it["folderId"] as String;
//                                }
//                                if (it["updateTime"] != "") {
//                                    localTemplate.update_time = it["updateTime"] as String;
//                                }
//                                DaoFactory.templateModuleDao().insertOrReplaceInTx(localTemplate)
//                                    .let {
//                                        result.success(null)
//                                    }
//                            }
//                    }
//
//
//                }

                METHOD_MY_TEMPLATE_REFRESH -> {
                    TemplateSyncLocalUtils.sync()
                    val templateEvent = TemplateEvent()
                    templateEvent.eventType = TemplateEventType.UPDATE_HOME_TEMPLATE_SYNC
                    EventBus.getDefault().post(templateEvent)
                    result.success(null)
                }

                METHOD_GET_SP_DATA -> {
                    var value = PreferencesUtils.getString(call.arguments as String)
                    result.success(value)
                }

                METHOD_GET_INT_SP_DATA -> {
                    var value = PreferencesUtils.getInt(call.arguments as String)
                    result.success(value)
                }

                METHOD_IS_EXIST_ETAG_TEMPLATE -> {
                    val templateId = call.argument<String>("templateId")
                    val isExist = isEtagTemplateExist(templateId!!)
                    result.success(isExist)
                }

                METHOD_GET_IM_PARAMS -> {
                    result.success(getImParams())
                }

                METHOD_CAN_OPEN_HISTORY_TEMPLATE -> {
                    val templateVersion = call.argument<String>("templateVersion")
                    var openResult = "0"
                    if (templateVersion.isNullOrEmpty()) {
                        openResult = "0"
                    } else {
                        val compareResult = VersionCompareUtils.compareVersion(
                            templateVersion,
                            TemplateVersionConst.getAppMaxSupportTemplateVersion()
                        )
                        if (compareResult <= 0) {
                            openResult = "1"
                        }
                    }
                    result.success(openResult)
                }

                METHOD_GET_VIP_STATUS -> {
                    val vipResult = if (VipHelper.isCurrentUserVip()) 1 else 0
                    result.success(vipResult)
                }

                METHOD_CREATE_LIVE_CODE -> {
                    createAdvanceQRCode(call, result)
                }

                METHOD_GET_ADVANCE_CODE -> {
                    getAdvanceQRCode(call, result)
                }

                METHOD_CACHE_ADVANCE_CODE -> {
                    cacheAdvanceQRCode(call, result)
                }
                METHOD_GET_ADVANCE_CACHES -> {
                    getAdvanceQRCodeCaches(call, result)
                }

                METHOD_PREVIEW_ADVANCE_CODE -> {
                    previewAdvanceQRCode(call, result)
                }

                METHOD_GET_DEVICE_ID -> {
                    result.success(GetAndroidUniqueMark.getUniqueId(context))
                }

                METHOD_GET_AGENT -> {
                    result.success(getAgent())
                }

                METHOD_GET_APP_INFO -> {
                    result.success(getAppInfo())
                }

                METHOD_GET_LANGUAGE_DETAIL -> {
                    getLanguageDetail(result)
                }

                METHOD_GET_DEVICE_SERIES -> {
                    getDeviceSeries(result)
                }

                METHOD_SET_DEVICE_SERIES -> {
                    try {
                        val value = call.arguments as? List<Map<String, Any>>
                        setDeviceSeries(value)
                        result.success(null)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                METHOD_SET_SELECTED_DEVICE_SERIES -> {
                    (call.arguments as? Map<String, Any>)?.let {
                        setSelectDeviceSeries(DeviceSerialInfo.fromMap(it))
                    }
                    result.success(null)
                }

                METHOD_GOTO_NATIVE_PAGE -> {
                    val toConnected = call.argument<Boolean>("needConnectPrinter")
                    val sourcePage = call.argument<String>("vcName")
                    val popDirection = call.argument<String>("popDeriction")
                    initLogic(context, sourcePage, popDirection, toConnected ?: false)
                    result.success(null)
                }

                METHOD_GET_PPI_FROM_NATIVE -> {
                    val densityDpi = context.resources.displayMetrics.densityDpi
                    result.success(densityDpi.toDouble())
                }

                METHOD_GET_SCALE_FROM_NATIVE -> {
                    val scale =
                        context.resources.displayMetrics.densityDpi / context.resources.displayMetrics.density
                    result.success(scale.toDouble())

                }

                METHOD_GET_APP_CURRENT_LANGUAGE_TYPE -> {
                    result.success(getAppLanguageType())
                }

                METHOD_SEND_TRACK_TO_NATIVE -> {
                    (call.arguments as? Map<String, Any>)?.let {
                        BuriedHelper.trackEvent(
                            it["track"] as String,
                            it["posCode"] as String,
                            it["ext"] as HashMap<String, Any>?
                        )
                    }
                    result.success(true)
                }

                METHOD_REPORT_LOGIN_SUCCESS -> {
                    var resultJson = call.argument<String>("resultJson")
                    updateLoginStatus(context, resultJson)
                    result.success(null)
                }

                METHOD_GET_SUPPORT_SOCIAL_LIST -> {
                    result.success(getSupportSocialList())
                }

                METHOD_SIM_ONE_KEY_LOGIN -> {
                    callOneKeyLogin(result = result)
                }

                METHOD_SIM_ONE_KEY_REGISTER -> {
                    callOneKeyLogin(2, result = result)
                }

                METHOD_SIM_ONE_KEY_BINDING -> {
                    callOneKeyLogin(3, result = result)
                }

                METHOD_CHECK_SIM_INFO -> {
                    var available = TextHookUtil.getInstance()
                        .isSimpleChinese() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && PhoneUtils.isSimCardReady()
                    result.success(available)
                }

                METHOD_NOTIFY_SIM_CHECK_RESULT -> {
                    LogUtils.e("simCheckResult: ${any2Json(call.arguments)}")
                    val registered = call.argument<Boolean>("registered") ?: false
                    val errorToken = call.argument<Boolean>("errorToken") ?: false
                    val authResult = call.argument<Int>("authResult") ?: 0
                    handleSimCheck(registered, errorToken, authResult)
                    result.success(true)
                }

                METHOD_SIM_ONE_KEY_CONFIG -> {
                    ThirdLoginHelper.initText(call)
                    result.success(true)
                }

                METHOD_REQUEST_SD_AND_CAMERA_PERMISSION -> {
                    requestNativePermission(result)
                }

                METHOD_NOTIFY_LOGOUT -> {
                    userLogout()
                    result.success(true)
                }

                METHOD_GET_LANGUAGE_DETAIL_PLUGIN -> {
//                    getPluginLanguageDetail(result)
                }

                METHOD_UPDATE_USER_INFO -> {
                    val userJson = call.argument<String>("userInfo")
                    updateUserInfo(userJson)
                    result.success(true)
                }

                METHOD_SET_TOKEN -> {
                    result.success(true)
                }

                METHOD_GET_LOCALE_LANGUAGE -> {
                    val data = HashMap<String, String>()
                    data["languageCode"] =
                        TextHookUtil.getInstance().getSystemLocal().language.lowercase()
                    data["countryCode"] =
                        TextHookUtil.getInstance().getSystemLocal().country.lowercase()
                    LogUtils.e("checkLocaleLanguage: ${data.toString()}")
                    result.success(data)
                }

                METHOD_DISALLOWED_SIDE_SLIP_FOR_IOS -> {
                    result.success(null)
                }

                METHOD_GET_USER_INFO -> {
                    val accessToken = call.argument<String>("accessToken") ?: ""
                    val tokenType = call.argument<String>("tokenType") ?: ""
                    getUserInfoForFlutter(result, accessToken, tokenType)
                }

                METHOD_BIND_MAIN_ACCOUNT_SUCCESS -> {
                    result.success(null)
                }

                METHOD_GET_APP_ENV -> {
                    result.success(
                        /**
                         * 获取app接口环境
                         * dev: 2
                         * test: 1
                         * production: 0
                         */
                        if (JCHttpConfig.mEnvironment == JCHttpConfig.JCEnvironment.dev.name) {
                            2
                        } else if (JCHttpConfig.mEnvironment == JCHttpConfig.JCEnvironment.test.name) {
                            1
                        } else 0
                    )
                }

                METHOD_GET_PRINTER_LABEL -> {
                    if (RFIDConnectionProxyManager.rfidTemplateModuleLocal == null) {
                        result.success(HashMap<String, Any>())
                    } else {
                        RFIDConnectionProxyManager.rfidTemplateModuleLocal!!.updateLabelId()
                        val json = any2Json(RFIDConnectionProxyManager.rfidTemplateModuleLocal)
                        LogUtils.e("========getPrinterLabelData: $json")
                        result.success(parseJson(json))
                    }
                }

                METHOD_GET_PRINTER_STATE -> {
                    // TODO: 2023/9/2 Ice_Liu 没有搞懂rfidStatus这个值的定义，不知道为什么要判断模版展示，所以暂时只做此处的更改
                    val map = FlutterDataBuilder.printerData()
                    map["rfidStatus"] = if (RFIDConnectionProxyManager.flutterRFIDStatus()) 1 else 0
                    val isWifi = JCConnectionManager.getInstance().isWifiConnected()
                    map["isWifi"] = if (isWifi) 1 else 0
                    result.success(map)
                }

                METHOD_GET_DENSITY -> {
                    val consumableType = call.arguments as? String
                    if (consumableType != null) {
                        val value = RFIDConnectionProxyManager.connectedDevice?.getDensity(
                            consumableType
                        )
                        result.success(value)
                    }
                }

                METHOD_GET_PRINT_SETTING_MSG -> {
                    GlobalScope.launch(Dispatchers.Main) {
                        val gap = "  "
                        val templateSn = call.arguments as? String
                        val deviceInfos = mutableListOf<String>()

                        // 获取当前连接机型
                        val deviceInfoDic = if (JCConnectionManager.getInstance().isConnected()) {
                            RFIDConnectionProxyManager.connectedDevice
                        } else null
                        // 1. SN、B-V (序列号和电池电压)
                        if (JCConnectionManager.getInstance().isConnected()) {
                            val sn = deviceInfoDic?.deviceName ?: ""
                            val bv =  ""
                            deviceInfos.add(formatKeyValuePair("SN", sn, gap, "B-V", bv))

                            // 2. H-V、F-V (硬件版本和固件版本)
                            val hv = deviceInfoDic?.hardwareVersion ?: ""
                            val fv = deviceInfoDic?.softwareVersion ?: ""
                            deviceInfos.add(formatKeyValuePair("H-V", hv, gap, "F-V", fv))
                        }

                        // 获取RFID信息
//                        val paperRfidInfo = RFIDConnectionProxyManager.sdkRFIDLabelInfos.firstOrNull { it.isValid() && !it.isRibbon() }
//                        val ribbonRfidInfo = RFIDConnectionProxyManager.sdkRFIDLabelInfos.firstOrNull { it.isValid() && it.isRibbon() }

                        // 获取服务器RFID信息
//                        val paperSerial = paperRfidInfo?.serial_number ?: ""
//                        val ribbonSerial = ribbonRfidInfo?.serial_number ?: ""
//                        val paperActualNum = RFIDConnectionProxyManager.serverRFIDPaper[paperSerial]
//                        val ribbonActualNum = RFIDConnectionProxyManager.serverRFIDRibbon[ribbonSerial]
//                        val paperActualNum = RFIDConnectionProxyManager.getRFIDPaperActualNum()
//                        val ribbonActualNum = RFIDConnectionProxyManager.getRFIDRibbonActualNum()

                        // 3. BN、TN (批次号和碳带批次号)
//                        val bn = paperRfidInfo?.batchSn ?: ""
//                        val tn = ribbonRfidInfo?.batchSn ?: ""
//                        deviceInfos.add(formatKeyValuePair("BN", bn, gap, "TN", tn))
//
//                        // 4. R-ID、T-ID (标签RFID和碳带RFID)
//                        val rid = paperRfidInfo?.serial_number ?: ""
//                        val tid = ribbonRfidInfo?.serial_number ?: ""
//                        deviceInfos.add(formatKeyValuePair("R-ID", rid, gap, "T-ID", tid))

//                        // 5. BP、TP (标签打印数量和碳带打印数量)
//                        val bp1 = if (paperActualNum != null && paperActualNum > 0) paperActualNum.toString() else ""
//                        val bp2 = paperRfidInfo?.print_number?.toString() ?: ""
//                        val bp = if (bp1.isNotEmpty() || bp2.isNotEmpty()) "$bp1 / $bp2" else ""
//                        val tp1 = if (ribbonActualNum != null && ribbonActualNum > 0) ribbonActualNum.toString() else ""
//                        val tp2 = ribbonRfidInfo?.print_number?.toString() ?: ""
//                        val tp = if (tp1.isNotEmpty() || tp2.isNotEmpty()) "$tp1 / $tp2" else ""
//                        deviceInfos.add(formatKeyValuePair("BP", bp, gap, "TP", tp))
//
//                        // 6. BL、TL (标签长度和碳带长度)
//                        val bl = paperRfidInfo?.allow_number?.toString() ?: ""
//                        val tl = ribbonRfidInfo?.allow_number?.toString() ?: ""
//                        deviceInfos.add(formatKeyValuePair("BL", bl, gap, "TL", tl))

                        // 7. M、UID (打印里程和用户ID)
                        val mileage = "" // TODO: 打印里程，SDK暂未提供
                        val uid = LoginDataEnum.userModule?.displayUId ?: ""
                        deviceInfos.add(formatKeyValuePair("M", mileage, gap, "UID", uid))

                        // 8. A-V、BV (应用版本和构建版本)
                        val av = AppUtils.getAppVersionName()
                        val bv = AppUtils.getAppVersionCode().toString()
                        deviceInfos.add(formatKeyValuePair("A-V", av, gap, "BV", bv))

                        // 9. S-V、Model (系统版本和设备型号)
                        val sv = android.os.Build.VERSION.RELEASE
                        val modelStr = android.os.Build.MODEL
                        deviceInfos.add(formatKeyValuePair("S-V", sv, gap, "Model", modelStr))

                        // 10. D-ID (设备ID，单独一行)
                        val did = GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext)
                        deviceInfos.add("D-ID: $did")

                        result.success(deviceInfos)
                    }
                }

                METHOD_GET_RECENT_LABEL -> {
                    getLabelRecord(call, result)
                }

                METHOD_UPDATE_SHOPINFO_NUMBERS -> {
                    val number = call.arguments as? String
                    number?.let {
                        EventBus.getDefault().post(ShopBubbleChangeEvent(it.toInt()))
                    }
                }

                METHOD_CLOSE_NPS -> {
                    EventBus.getDefault().post(CloseNpsEvent())
                }

                METHOD_GET_LOCAL_USERINFO_CACHE -> {
                    val userModel = LoginDataEnum.userModule
                    userModel?.access_token = HttpTokenUtils.getToken()
                    userModel?.shopToken = HttpTokenUtils.getShopToken()
                    val userMap = userModel?.serializeToMap()
                    result.success(userMap ?: hashMapOf<String, Any>())

                }

                METHOD_GET_DEVICE_SERIESID -> {
                    result.success(getDeviceHardWareModel(call))
                }

                METHOD_ETAG_CONNECT -> {
                    JCConnectionManager.getInstance().closeConnect()
                    val deviceMsg = PreferencesUtils.getString(CONNECTED_ETAG_INFO, "")
                    JCConnectionManager.getInstance().saveLastPrinter(
                        NiimbotDevice(
                            NiimbotDeviceType.BT, "", deviceMsg
                        )
                    )

                    DeviceETagHelper.getInstance().eTagConnectAuto(object : BluetoothListener {
                        override fun onConnected(device: BluetoothDevice) {
                            super.onConnected(device)
                            AppConnectManager.getInstance()?.trackET10Event(1, 2)
                        }
                    })
                }

                METHOD_GOODS_MIX_LIST -> {
                    goodsMixTemplate(call, result)
                }

                METHOD_GOODS_MIX_LIST_ALL -> {
                    goodsMixTemplateList(call, result)
                }

                METHOD_CLOSE_NFC_TOP_ACTIVITY -> {
                    EventBus.getDefault().post(closeNfcActivityEvent())
                }

                METHOD_ETAG_STATUS_LISTENER -> {
                    val isLsitener = call.arguments as Boolean
                    DeviceETagHelper.isLsitener = isLsitener
                    result.success(null)
                }

                METHOD_ETAG_DISCONNECT -> {
                    DeviceETagHelper.getInstance().disConnect()
                }

                METHOD_RESET_ETAG_STATUS -> {
                    DeviceETagHelper.getInstance().resetCardStatus({
                        result.success(it)
                    })
                }

                METHOD_WRITE_SCREEN_DATA -> {
                    writeEtagScreen(call, result)
                }

                METHOD_GET_PREVIEW_PICTURE -> {
                    previewPicture(call, result)
                }

                METHOD_SET_CONNECT_DEVICE -> {
                    val gson = Gson()
                    val type = object : TypeToken<Map<String, Any>>() {}.type
                    val deviceMap: Map<String, Any> = gson.fromJson(call.arguments as String, type)
                    RFIDConnectionProxyManager.setConnectDevice(PDeviceInfo().mapToDataStructure(deviceMap))
                    result.success(true)
                }

                METHOD_SET_SDK_RFID_DATA -> {
                    val gson = Gson()
                    val listType = object : TypeToken<ArrayList<Map<String, Any>>>() {}.type
                    val jsonMapList: ArrayList<Map<String, Any>> =
                        gson.fromJson(call.arguments as String, listType)
                    val sdkRFIDLabelInfos = ArrayList<RFIDLabelInfo>()
                    for (jsonMap in jsonMapList) {
                        val rfidLabelInfo = RFIDLabelInfo()
                        rfidLabelInfo.one_code = jsonMap["barcode"] as? String ?: ""
                        rfidLabelInfo.serial_number = jsonMap["serialNumber"] as? String ?: ""
                        rfidLabelInfo.allow_number = (jsonMap["allPaperMeters"] as Double).toFloat()
                        rfidLabelInfo.print_number =
                            (jsonMap["usedPaperMeters"] as Double).toFloat()
                        rfidLabelInfo.paperType = (jsonMap["type"] as Double).toInt()
                        rfidLabelInfo.statusCode = (jsonMap["state"] as? Double)?.toInt().toString()


                        sdkRFIDLabelInfos.add(rfidLabelInfo)
                    }
                    RFIDConnectionProxyManager.sdkRFIDLabelInfos = sdkRFIDLabelInfos
                    RFIDConnectionProxyManager.sdkHasRFIDLabel = null != sdkRFIDLabelInfos.firstOrNull { it.isValid() }
                    LogUtils.e("============setSDKRfidData: sdkRFIDLabelInfos=>${any2Json(sdkRFIDLabelInfos)}, sdkHasRFIDLabel=>${RFIDConnectionProxyManager.sdkHasRFIDLabel}")
                    if(RFIDConnectionProxyManager.connectedDevice?.isC1() == true && DeviceC1Helper.isC1Page && DeviceC1Helper.ribbonStatus != 4) {
                        val labelInfo = sdkRFIDLabelInfos.firstOrNull { it.isValid() }
                        if (labelInfo != null) {
                            DeviceC1Helper.notifyRibbonConsumption(labelInfo.print_number, labelInfo.allow_number)
                        } else {
                            DeviceC1Helper.notifyReadRibbonFailed()
                        }
                    }
//                    RFIDConnectionProxyManager.trackRfidEvent()
                    BluetoothStatusManager.sendRfidReadState(if (RFIDConnectionProxyManager.sdkHasRFIDLabel) 1 else 0)
                    result.success(true)
                }

                METHOD_REFRESH_SERVER_RFID_INFO -> {
                    val map = call.arguments as HashMap<String, Any>
                    if(BuildConfig.DEBUG) {
                        val data = any2Json(map)
                        LogUtils.iTag("DeviceDetailsRfidInfo", "refreshServerRFIDInfo: $data")
                    }
                    RFIDConnectionProxyManager.refreshServerRFIDInfo(map)
                    MainScope().launch {
                        RFIDConnectionProxyManager.updateDeviceSettingListener?.invoke()
                    }
                }

                METHOD_GET_NFC_CONNECT_STATE -> {
                    DeviceETagHelper.getInstance().getNfcScreenInfo(object : eTagStatusListener {
                        override fun isConnect(isConnect: Boolean?) {
                            if (isConnect == true) result.success(true) else result.success(false)
                        }
                    })
                }

                CLOSE_HARDWARE_CONNECTED -> {
                    JCConnectionManager.getInstance().closeConnectAndClearAutoConnect()
                    result.success(true)
                }

                METHOD_SAVE_TEMPLATE -> {
                    saveTemplate(call, result)
                }

                METHOD_CHECK_BARCODE_FORMAT -> {
                    val barcodeContent = call.argument<String>("barcodeContent")
                    val barcodeType = call.argument<Int>("barcodeType")
                    val checkResult =
                        NBCanvasImageApi.checkBarcodeContent(barcodeContent, barcodeType!!)
                    result.success(checkResult)
                }

                METHOD_IF_SHOW_VIP -> {
                    result.success(VipHelper.ifShowVip())
                }

                METHOD_CHECK_LOGIN_STATUS -> {
                    result.success(com.niimbot.appframework_library.utils.LoginUtils.isLogin())
                }

                METHOD_GET_USER_ID -> {
                    val loginDataJson = PreferencesUtils.getString("loginData", "")
                    val jsonObject = JSON.parseObject(loginDataJson)
                    val userId = jsonObject.getInteger("id")
                    result.success(userId)
                }

                METHOD_GET_IMPORT_FILE_SOCIAL_LIST -> {
                    result.success(getImportExcelSocialList())
                }

                METHOD_IMPORT_EXCEL_FROM_LOCAL -> {
                    ImportExcelHelper.importExcelFromLocal(  call.arguments as String ,result)
                }

                METHOD_IMPORT_EXCEL_FROM_SOCIAL_APP -> {
                    val socialType = call.argument<String>("socialType")
                    val fileType=call.argument<String>("fileType")
                    if (!socialType.isNullOrEmpty()) {
                        ImportExcelHelper.importExcelFromSocial(socialType, result,fileType)
                    }
                }

                METHOD_GET_TEMPLATE_DETAIL -> {
                    val templateId = call.argument<String>("templateId") ?: ""
                    val isPersonalTemplate = call.argument<Boolean>("isPersonalTemplate") ?: false
                    val needLoadFonts = call.argument<Boolean>("needLoadFonts") ?: false
                    val isShare = call.argument<Boolean>("isShare") ?: true
                    val saveRecord = call.argument<Boolean>("saveRecord") ?: true
                    LogUtils.e("获取模板详情： ${any2Json(call)}")
                    if (templateId.isNullOrBlank()) {
                        result.success(null)
                    } else {
                        getTemplateDetailDelegate(
                            FlutterBoost.instance().currentActivity(),
                            templateId,
                            isPersonalTemplate = isPersonalTemplate,
                            needLoadFonts = needLoadFonts,
                            isShare = isShare,
                            saveRecord = saveRecord,
                            resultCall = result
                        )
                    }
                }

                com.gengcon.android.jccloudprinter.FlutterChannelRegister.METHOD_UPDATE_LABEL_USED_RECORD -> {
                    val labelId = call.argument<String>("labelId") ?: ""
                    if (labelId.isNotEmpty()) {
                        TemplateSyncLocalUtils.saveUsedRecordByLabelId(labelId)
                    }
                }

                METHOD_GET_CABLE_DETAIL -> {
                    val barCode = call.argument<String>("barCode") ?: ""
                    val labelId = call.argument<String>("labelId") ?: ""
                    if (barCode.isNullOrBlank()) {
                        result.success(null)
                    } else {
                        TemplateSyncLocalUtils.getCableTemplateByLabelId(
                            barCode,
                            labelId,
                            true
                        ) { success, templateModule, errorMsg ->
                            if (success) {
                                templateModule?.apply {
                                    val templateLocal = templateModule.toTemplateModuleLocal()
                                    result.success(any2Json(templateLocal))
                                }
                            } else {
                                if (!errorMsg.isNullOrEmpty()) {

                                    showToast(errorMsg)
                                    result.success(null)
                                }
                            }
                        }

                    }
                }

                METHOD_GET_CURRENT_SINGLE_COLOR_INFO -> {
                    MainScope().launch {
                        val map = withContext(Dispatchers.IO) {
                            RfidColorProxy.getCurrentSingleColor()
                        }
                        result.success(map)
                    }
                }

                METHOD_GET_CURRENT_PAPER_SUPPORT_COLORS -> {
                    val colors = RfidColorProxy.getCurrentPaperSupportColors()
                    result.success(colors)
                }

                METHOD_GET_CONNECT_DEVICE -> {
                    postConnectState()
                    result.success(null)
                }

                METHOD_SYNC_REPLACE_RFID_TAG -> {
                    val rfidReplaceTag = call.argument<Boolean>("hasConfirmAutoRfid") ?: false
                    if (rfidReplaceTag) {
                        hasConfirmAutoRFID = true
                        result.success(0)
                    } else {
                        result.success(if (hasConfirmAutoRFID) 1 else 0)
                    }
                }

                METHOD_APP_FONT_PATH -> {
                    result.success(FontUtils.customFontFile.absolutePath)
                }

                METHOD_CHECK_BATCH_SOURCES -> {
                    val json = call.arguments as String
                    val batchIds = json2Any<List<Map<String, Any>>>(json)!!
                    GlobalScope.launch(Dispatchers.Main) {
                        var isSuccess = 0
                        withContext(Dispatchers.IO) {
                            isSuccess = SilentDownloadResources.checkIsDownloadSuccess(batchIds)
                        }
                        result.success(isSuccess)
                    }
                }

                METHOD_SET_SP_DATA -> {
                    val entry = call.arguments as Map<String, Any>
                    PreferencesUtils.put(entry.keys.first(), entry.values.first())
                }

                METHOD_SET_DEVICE_OFFSET -> {
                    val vOffset = call.argument<Double>("offsetX")
                    val hOffset = call.argument<Double>("offsetY")
                    RFIDConnectionProxyManager.connectedDevice?.vOffset = vOffset!!.toFloat()
                    RFIDConnectionProxyManager.connectedDevice?.hOffset = hOffset!!.toFloat()
                    val templateCommandEvent = TemplateEvent()
                    templateCommandEvent.eventType = TemplateEventType.SAVE_PRINTER_OFFSET
                    EventBus.getDefault().post(templateCommandEvent)
                }

                METHOD_GET_RFID_TAG -> {
                    result.success(if (hasConfirmAutoRFID) 1 else 0)
                }

                METHOD_USER_OFF_SUCCESS -> {

                    BaseApplication.getInstance().mCurShowActivityReference?.get()
                        ?.let { activity ->
                            CacheUtils.clearCache {}
                            DataCacheUtils.clearAllCache(activity)
                            LoginDataEnum.unLogin()
                            EventBus.getDefault().post(FinishTemplateIndustryEvent())
                            setSentryUser(context, "")
                        }

                }

                METHOD_GET_SELECT_PRINT_BY_ID -> {
                    val machineId = call.argument<String>("machineId") ?: ""
                    LogUtils.e("获取选择的机器详情，id： $machineId")
                    if (machineId.isNullOrEmpty()) {
                        result.success(null)
                    } else {
                        DevicesLocalUtils.getDeviceById(machineId)?.let {
                            result.success(any2Json(DevicesModule.localToRemote(it)))
                        } ?: result.success(null)
                    }
                }

                METHOD_GET_PROXY_URL -> {
                    val flutterProxyUrl =
                        PreferencesUtils.getString(LaboratoryActivity.CUSTOM_FLUTTER_PROXY_URL, "")
                    result.success(if (com.blankj.utilcode.util.AppUtils.isAppDebug()) flutterProxyUrl else "")
                }

                METHOD_IS_FORCE_LTR -> {
                    val isForceLTR = LaboratoryActivity.isForceLTR()
                    result.success(isForceLTR)
                }

                METHOD_END_SEND_PICTURES -> {
                    DeviceETagHelper.getInstance().endSendPictures()
                }

                METHOD_UN_VIP_BATCH_PRINT_COUNT -> {
                    val count =
                        PreferencesUtils.getInt("unVipBatchPrintCount" + LoginDataEnum.id, 0)
                    result.success(count)
                }

                METHOD_GET_APP_CONFIG -> {
                    var agent = SystemUtil.getUserAgent(SuperUtils.superContext)
                    if (agent == null) {
                        agent = ""
                    }
                    val config = hashMapOf(
                        "apiBaseUrl" to JCHttpConfig.ROOT_URL + "/",
                        "userAgent" to agent,
                        "language" to TextHookUtil.getInstance()
                            .getSystemLocal().language.lowercase(),
                        "token" to HttpTokenUtils.getAccessToken(),
                        "uid" to LoginDataEnum.id.toString(),
                        "phone" to Build.BRAND,
                        "deviceId" to GetAndroidUniqueMark.getUniqueId(SuperUtils.superContext),
                        "systemVersion" to Build.VERSION.RELEASE,
                        "appVserson" to AppUtils.getAppVersionName(),
                        "os" to "Android",
                        "currentConnectPrint" to RFIDConnectionProxyManager.connectedDevice?.deviceName,
                        "isAppStore" to if (BuildConfig.DEBUG) "0" else "1",
                    )
                    result.success(config)
                }

                METHOD_TO_ETAG_PAGE -> {

                    BaseApplication.getInstance().mCurShowActivityReference?.get()
                        ?.let { activity ->
                            LoginDataEnum.loginCheck(activity) {
                                NiimbotGlobal.gotoFlutterPage(
                                    "eTag",
                                    hashMapOf("token" to HttpTokenUtils.getAccessToken())
                                )
                            }
                        }

                }

                METHOD_IS_EXIST_TEMPLATE -> {
                    val templateId = call.argument<String>("templateId") ?: ""
                    val exist = false
                    result.success(exist)
                }

                METHOD_POST_CATCHED_EXCEPTION -> {
                    var message = ""
                    var detail = ""
                    if (call.hasArgument("crash_message")) {
                        message = call.argument("crash_message") ?: ""
                    }
                    if (call.hasArgument("crash_detail")) {
                        detail = call.argument("crash_detail") ?: ""
                    }
                    postCatchedExecption(message, detail)
                    result.success(null);
                }

                METHOD_SHOW_SCAN_LOGIN_SUCCESS -> {
                    GlobalScope.launch(Dispatchers.Main) {
                        delay(200)
                        GlobalLoadingHelper.showSucceedLoadingWithText(
                            LanguageUtil.findLanguageString(
                                "app100001267"
                            )
                        )
                    }
                }

                METHOD_ABLE_TO_PRINT -> {
                    val able = call.argument<Boolean>("isAbleToPrint") ?: false
                    EventBus.getDefault().post(AbleToPrint(able))
                }

                METHOD_DOWNLOAD_TEMPLATE_DETAIL -> {
                    val templateIds = call.argument<List<String>>("templateids")
                    val ids = templateIds as List<String>
                    SilentDownloadResources.hashSet.clear()
                    SilentDownloadResources.hashMap.clear()
                    SilentDownloadResources.ids = ids
                    ids.forEach {
                        SilentDownloadResources.downloadTemplateDetails(it)
                    }
                    result.success(null);
                }

                METHOD_PRINT_COMMODITY -> {
                    val printJson = com.niimbot.fastjson.JSONObject()
                    printJson["action"] = "printCommodity"
                    printJson["taskId"] = PrintTaskExecutor.capTaskId
                    EventBus.getDefault().post(any2Json(printJson))
                }

                METHOD_REMOVE_TEMPLATE_DETAIL -> {
                    val templateIds = call.argument<List<String>>("templateids")
                    val ids = templateIds as List<String>
                    ids.forEach {
                        SilentDownloadResources.removeRunable(it)
                    }
                    result.success(null)
                }

                METHOD_CANCEL_TEMPLATE_DETAIL -> {
                    SilentDownloadResources.clearRunable()
                    result.success(null)
                }

                METHOD_CHECK_TEMPLATE_DETAIL -> {
//                    val templateIds = call.arguments as List<String>
//                    var isSuccess = 1
//                    for (templateId in templateIds) {
//                        val localTemplate = TemplateDbUtils.queryTemplate(templateId)
//                        val templateModuleLocal = localTemplate?.toTemplateModuleLocal()
//                        val fontCodes = templateModuleLocal?.checkNotDownloadFontCodes()
//                        if (localTemplate == null || !localTemplate.canEdit() || fontCodes?.isNotEmpty() == true) {
//                            isSuccess = 0
//                            break
//                        }
//                    }
//                    result.success(isSuccess);
                }

                METHOD_CANCEL_LOGIN -> {
                    NiimbotGlobal.loginCallbackInvoke(false)
                }

                METHOD_SET_DEVICE_LIST -> {

                }
                METHOD_NOTIFY_ENTER_C1_PAGE -> {
                    DeviceC1Helper.c1StatusListener = {
                        FlutterEventRegister.notifyC1Status(it)
                    }
                    DeviceC1Helper.handleToC1Page()
                }
                METHOD_NOTIFY_BACK_FROM_C1_PAGE -> {
                    DeviceC1Helper.c1StatusListener = null
                    DeviceC1Helper.handleBackFromC1Page()
                }
                METHOD_TO_C1_HOME_PAGE -> {
                    BaseApplication.getInstance().mCurShowActivityReference?.get()
                        ?.let { activity ->
                            LoginDataEnum.loginCheck(activity) {
                                NiimbotGlobal.gotoFlutterPage(
                                    "C1",
                                    hashMapOf("fontPath" to FontUtils.customFontFile.absolutePath)
                                )
                            }
                        }
                }
                METHOD_REQUEST_GEETEST_VERIFY -> {
                    val regionCode = call.argument<String>("regionCode") ?: ""
                    val phone = call.argument<String>("phone") ?: ""
                    val mustCheck = call.argument<Boolean>("mustCheck") ?: false
                    if ((regionCode.isEmpty() || phone.isEmpty() || !LiveCodeService.needGeetestVerify(
                            regionCode
                        )) && !mustCheck
                    ) {
                        val map = hashMapOf(
                            Pair("result", true),
                            Pair("response", "")
                        )
                        result.success(map)
                        return@setMethodCallHandler
                    }
                    GeetestHelper.verify { success, response ->
                        val map = hashMapOf(
                            Pair("result", success),
                            Pair("response", response)
                        )
                        result.success(map)
                    }
                }

                METHOD_GET_LABEL_SHOP_LINK -> {
                    val oneCode = call.argument<String>("oneCode")!!
                    val jumpSource = call.argument<String>("jumpSource")!!
                    val machineId = RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""
                    var url: String
                    if (ShopManager.shopType == ShopType.China) {
                        if (machineId.isNotEmpty()) {
                            url =
                                ShopManager.SHOP_PURCHASE + "/$oneCode?machine_id=$machineId&jumpSource=$jumpSource"
                        } else {
                            url = ShopManager.SHOP_PURCHASE + "/$oneCode?jumpSource=$jumpSource"
                        }
                    } else {
                        if (machineId.isNotEmpty()) {
                            url =
                                ShopManager.SHOP_PURCHASE.replace(
                                    "purchase",
                                    "detail"
                                ) + "?barCode=$oneCode&machine_id=$machineId&jumpSource=$jumpSource"
                        } else {
                            url = ShopManager.SHOP_PURCHASE.replace(
                                "purchase",
                                "detail"
                            ) + "?barCode=$oneCode&jumpSource=$jumpSource"
                        }
                    }
                    url = ShopManager.getFinalUrl(url)
                    result.success(url)
                }
                METHOD_DISCOVER_BLUETOOTH_PRINTER_FINISHED -> {
                    JCConnectionManager.getInstance().notifyBluetoothDiscoveryFinished()
                }
                METHOD_DISCOVER_WIFI_PRINTER_FINISHED -> {
                    JCConnectionManager.getInstance().notifyWifiDiscoveryFinished()
                }

                METHOD_DISCONNECT_CALLBACK -> {
                    AppConnectManager.getInstance()?.netyDisconnectCallback()
                }

                METHOD_ELECTRICITY_CALLBACK -> {
                    val value = call.arguments as Int
                    AppConnectManager.getInstance()?.netyElectricityCallback(value)
                }

                METHOD_COVER_STATUS_CALLBACK -> {
                    val status = call.arguments as Int
                    AppConnectManager.getInstance()?.netyCoverStatusCallback(status)
                }

                METHOD_PAPER_STATUS_CALLBACK -> {
                    val status = call.arguments as Int
                    AppConnectManager.getInstance()?.netyPaperStatusCallback(status)
                }

                METHOD_PAPER_RFID_STATUS_CALLBACK -> {
                    val status = call.arguments as Int
                    AppConnectManager.getInstance()?.netyPaperRfidStatusCallback(status)
                }

                METHOD_RIBBON_STATUS_CALLBACK -> {
                    val status = call.arguments as Int
                    AppConnectManager.getInstance()?.netyRibbonStatusCallback(status)
                }

                METHOD_RIBBON_RFID_STATUS_CALLBACK -> {
                    val status = call.arguments as Int
                    AppConnectManager.getInstance()?.netyRibbonRfidStatusCallback(status)
                }

                METHOD_GET_NATIVE_NETWORK_STATE -> {
                    result.success(NetworkUtils.isConnected())
                }

                METHOD_SYNC_OUT_OF_SERVICE -> {
                    val isOutOfService = call.argument<Boolean>("isOutOfService") ?: false
                    if (isOutOfService && !NetworkUtils.isOutOfService()) {
                        NetworkUtils.notifyNetWorkError(NetworkUtils.NetworkStatus.OUT_OF_SERVICE)
                    }
                    result.success(true)
                }

                METHOD_NOTIFY_FLUTTER_CHANNEL -> {
                    val appConnectManager = AppConnectManager.getInstance()
                    if(appConnectManager != null) {
                        if(com.blankj.utilcode.util.AppUtils.isAppDebug()) {
                            Log.i("RefactoringAutoConnect", "notify flutter channel: a")
                        }
                        appConnectManager.notifyFlutterChannel()
                    }
                    else{
                        if(com.blankj.utilcode.util.AppUtils.isAppDebug()) {
                            Log.i("RefactoringAutoConnect", "notify flutter channel: b")
                        }
                        AppConnectManager.flutterChannelAttached = true
                    }
                }

                METHOD_CHECK_DEBUG_MODE -> {
                    val debug = com.gengcon.android.jccloudprinter.BuildConfig.DEBUG
                    result.success(debug)
                }

                METHOD_PUSH_TO_ROUTE -> {
                    val typeCode = runCatching {
                        call.argument<String>("typeCode")?.toInt() ?: -1
                    }.getOrElse {
                        it.printStackTrace()
                        -1
                    }

                    var url = call.argument<String>("url").orEmpty()
                    if (url.isBlank()) return@setMethodCallHandler

                    val title = call.argument<String>("title").orEmpty()
                    ActivityUtils.getTopActivity()?.let {
                        NiimbotGlobal.bannerRouter(
                            it,
                            url,
                            typeCode,
                            title,
                            sourcePage = "",
                            jumpSource = "",
                            false
                        )
                    }
                    result.success(true)
                }

                METHOD_NOTIFY_TO_CANVAS_PAGE -> {
                    notifyToCanvasPage()
                }

                METHOD_NOTIFY_PRINT_PROCESS_STATUS -> {
                    NiimbotGlobal.isPrinting = call.arguments == 1
                }

                else -> {
                    result.notImplemented()
                }
            }
        }
        initLiveCodeEventCallback()
    }

    private fun previewPicture(call: MethodCall, result: MethodChannel.Result) {
        val previewImageList = arrayListOf<ByteArray>()
        GlobalScope.launch {

            val templateData: String = call.argument<String>("templateData")!!
            val pageIndex: Int = call.argument<Int>("pageIndex")!!
            val mTemplateModule = TemplateModuleLocal.fromJson(templateData)
            mTemplateModule?.let { local ->
                var localTemplate =
                    TemplateModuleTransform.templateModuleLocalToTemplateModule(local)
                if (!NetworkUtils.isConnected()) {
                    showToast("app01139")
                }
                localTemplate = TemplateSyncLocalUtils.writeModuleImg(localTemplate, false)
                templateModule = localTemplate
                val currentDisplayMultiple =
                    getCurrentDisplayMultipleForPreview(local.width, local.height)
                val reviewJson = ImageSdkJsonUtils.getPrintReviewJson(
                    VipHelper.canUseVipFunc(),
                    localTemplate.toTemplateModuleLocal(),
                    /*index*/pageIndex
                )
                val nbCanvasImage = NBCanvasImageApi.nPrintDraw(
                    0,
                    reviewJson,
                    currentDisplayMultiple,
                    local.getPaccuracy(),
                    true,
                    RFIDConnectionProxyManager.getCarbonARGBForPrint()
                )
                if (nbCanvasImage?.data != null && nbCanvasImage.data.isNotEmpty()) {
                    LogUtils.i(
                        "updateReviewImage",
                        "nbCanvasImage result width = " + nbCanvasImage.width + " ,height = " + nbCanvasImage.height + ",size = " + nbCanvasImage.data.size
                    )
                    previewImageList.add(nbCanvasImage.data)

                }
                result.success(previewImageList)

            }

        }

    }

    fun postEventResult(data: String) {
        if (observers != null) {
            observers.result(data);
        }

    }

    private fun postCatchedExecption(message: String, detail: String) {
        if (TextUtils.isEmpty(detail)) return;
        CrashReport.postException(5, "Flutter Exception", message, detail, null)
    }

    private fun getAgent(): Map<String, String> {
        val agent = SystemUtil.getUserAgent(SuperUtils.superContext)
        val map = hashMapOf(
            Pair("agent", agent),
            Pair("anonymous_id", BuriedHelper.getEventAnonymousId())
        )
        LogUtils.e("Flutter call Native--getAgent: $map")
        return map
    }

    private fun getAppInfo(): Map<String, String> {
        val infoList = AppInfoDialog.getAppInfoItems()
        val map = infoList.associate { it.key to it.value }.toMutableMap()
        val keysString = infoList.joinToString(",") { it.key }
        map["keys"] = keysString
        return map
    }


    private fun isEtagTemplateExist(templateId: String): Boolean {
        val content = EtagTemplateFileUtils.readEtagTemplate(templateId)
        if (!TextUtils.isEmpty(content)) {
            return true
        }
        return false
    }

    private fun getImParams(): Map<String, String> {
        val phoneBrand = SystemUtil.getDeviceBrand()
        val connectedDevice = RFIDConnectionProxyManager.connectedDevice
        val RFIDLabelInfos = RFIDConnectionProxyManager.getRFIDLabelInfo()

        val map = hashMapOf<String, String>()
        map["hardwareVersion"] = connectedDevice?.hardwareVersion ?: ""
        map["firmwareVersion"] = connectedDevice?.softwareVersion ?: ""
        map["hardwareSn"] = connectedDevice?.deviceName ?: ""
        map["hardwareModel"] = BluetoothUtil.getJCDeviceSerial(
            RFIDConnectionProxyManager.connectedDevice?.deviceName ?: "",
            PrintManager.getPrintType()
        )

        map["hardwareSeries"] = BluetoothUtil.getDeviceType(connectedDevice?.deviceName ?: "")

        map["rfid"] = RFIDLabelInfos?.serial_number ?: ""
        map["consumableBarcode"] = RFIDLabelInfos?.one_code ?: ""
        map["consumableName"] = RFIDConnectionProxyManager.rfidTemplateModuleLocal?.name ?: ""
        map["phoneBrand"] = phoneBrand
        LogUtils.e("Flutter call Native--getImParams: $map")
        return map
    }

    private fun getAppLanguageType(): String {
        return if (LanguageSPHelper.hasSetLanguage()) {
            LanguageSPHelper.getLastLanguageCode()
        } else {
            //根据国家和语言检索本地是否有语言缓存
            TextHookUtil.getInstance().getLanguageCode()
        }
    }

    private fun getLanguageDetail(result: MethodChannel.Result) {
        MainScope().launch {
            val langData = withContext(Dispatchers.IO) {
                val languageCode = getAppLanguageType()
                LogUtils.e("Flutter call Native--getLanguageDetail: $languageCode")
                LanguageCacheUtils.checkHasLanguageCache(languageCode)?.lang?.mapValues { entry ->
                    hashMapOf(
                        Pair("desc", entry.value.desc),
                        Pair("value", entry.value.value)
                    )
                }
            }
            result.success(langData)
        }
    }

    private fun getDeviceSeries(callback: MethodChannel.Result) {
        DevicesSeriesLocalUtils.getModule { list ->
            val result = list.map { it.toMap() }
            LogUtils.e("Flutter call Native--getDeviceSeries: ${any2Json(result)}")
            callback.success(result)
        }
    }

    private fun getLabelRecord(call: MethodCall, result: MethodChannel.Result) {
        var limit = 0
        var machineIds: List<String> = ArrayList()
        (call.arguments as? Map<String, Any>)?.let {
            limit = (it.get("limit") as Int?)!!
            if (it.get("machineIds") != null) {
                machineIds = it.get("machineIds") as List<String>
            }
        }.apply {
            loadRecordFromDB(limit, machineIds, result)
        }
    }

//    private fun loadRecordFromDB(
//        limit: Int,
//        machineIds: List<String>,
//        result: MethodChannel.Result
//    ) {
//        val list: LinkedList<HashMap<String, Any>> = LinkedList<HashMap<String, Any>>()
//        val labelIds = arrayListOf<String>()
//        DaoFactory.templateSortModuleDao()?.queryBuilder()
//            ?.where(TemplateSortModuleDao.Properties.UserId.eq(LoginDataEnum.id))
//            ?.orderDesc(TemplateSortModuleDao.Properties.UseTime)
//            ?.list()
//            ?.apply {
//                this.forEach {
//                    if (it.templateModule != null && list.size < limit && !labelIds.contains(it.templateModule.labelId)) {
//                        labelIds.add(it.templateModule.labelId)
//                        if (machineIds.isNotEmpty()) {
//                            if (it.templateModule.machine_id.intersect(machineIds).isNotEmpty()) {
//                                templatesRecord(it, list)
//                            }
//
//                        } else {
//                            templatesRecord(it, list)
//                        }
//                    }
//                }
//                result.success(list)
//            }
//    }

    private fun loadRecordFromDB(
        limit: Int,
        machineIds: List<String>,
        result: MethodChannel.Result
    ) {
        kotlinx.coroutines.GlobalScope.launch(Dispatchers.Main) {
            val list: LinkedList<HashMap<String, Any>> = LinkedList<HashMap<String, Any>>()
            val labelIds = arrayListOf<String>()
            DaoFactory.templateSortModuleDao()?.queryBuilder()
                ?.where(TemplateSortModuleDao.Properties.UserId.eq(LoginDataEnum.id))
                ?.orderDesc(TemplateSortModuleDao.Properties.UseTime)
                ?.list()
                ?.apply {
                    this.forEach {
                        val templateJsonStr = FlutterMethodInvokeManager.getTemplateDetail(it.templateId,true,true)
                        if(templateJsonStr != null){
                            val moduleLocal = TemplateModuleLocal.fromJson(templateJsonStr)
                            val moduleProfile = moduleLocal!!.profile
                            if (moduleLocal != null && list.size < limit && !labelIds.contains(moduleProfile.extrain.labelId)) {
                                labelIds.add(moduleProfile.extrain.labelId)
                                if (machineIds.isNotEmpty()) {
                                    if (moduleLocal.profile.machineId.split(",").intersect(machineIds).isNotEmpty()) {
                                        templatesRecord(it,moduleLocal, list)
                                    }

                                } else {
                                    templatesRecord(it,moduleLocal, list)
                                }
                            }
                        }

                    }
                    result.success(list)
                }

        }

    }

    private fun isChineseMarketRatingMet(userPrintCount: Int): Boolean {
        //是否触发国内评分
        var isChineseMarketRatingMet = false
        //是否在国内评过分
        val hasChineseMarketRating = PreferencesUtils.getBoolean("market_rating", false)
        //获取可以用于评分的应用市场
        var marketInfo = MarketRatingManager.getInstance().canJumpToMarketComment()
        //登录并且简体中文并且没有弹出过并且打印张数大于30
        if (LoginDataEnum.isLogin &&  userPrintCount>29 && TextHookUtil.getInstance().isSimpleChinese() && !hasChineseMarketRating && marketInfo != null) {
            isChineseMarketRatingMet = true
        }
        return isChineseMarketRatingMet
    }

    private fun templatesRecord(it: TemplateSortModule,templateModuleLocal: TemplateModuleLocal, list: LinkedList<HashMap<String, Any>>) {

        templateModuleLocal.apply {
            this.elements.clear()
            this.externalData = ExternalData()
            this.task = Task()
            this.totalPage = 1
            this.usedFonts.clear()
            this.previewImage = this.backgroundImage
            this.name =
                (this.labelNames.firstOrNull { it.languageCode == TextHookUtil.getInstance().languageName }?.name
                    ?: this.labelNames.firstOrNull { it.languageCode == TextHookUtil.LANGUAGE_ZH_CN }?.name
                    ?: this.name)
            if (!this.id.isNullOrEmpty() && this.width > 0 && this.height > 0) {
                list.add(this.serializeToMap())
            }
        }
    }

    private fun setDeviceSeries(list: List<Map<String, Any>>?) {
        LogUtils.e("Flutter call Native--setDeviceSeries: ${any2Json(list)}")
        if (!list.isNullOrEmpty()) {
            val devicesModules = list.map { DeviceSerialInfo.fromMap(it) }
            DevicesSeriesLocalUtils.setModule(devicesModules)
        }
    }

    private fun setSelectDeviceSeries(serial: DeviceSerialInfo?) {
        LogUtils.e("Flutter call Native--setSelectDeviceSeries: ${any2Json(serial)}")
        serial?.let {
            WifiConnectionProxyManager.setCurrentDeviceSeriesInfo(any2Json(it))
        }
    }


    private fun getDeviceHardWareModel(call: MethodCall): HashMap<String, Any> {
        var devicesModule = DevicesModule()
        (call.arguments as? Map<String, Any>)?.let {
            val seriesId = it.get("series").toString()
            DevicesSeriesLocalUtils.getModule {
                it.forEach { info ->
                    if (seriesId.equals(info.id)) {
                        devicesModule =
                            DevicesLocalUtils.getDeviceByName(info.machine_name?.split(",")!![0])!!;
                        return@forEach
                    }

                }
            }
        }
        return DevicesModule.localToRemote(devicesModule).serializeToMap()
    }


    var templateModule: TemplateModule? = null
    ///电子价签商品数据混入

    private fun goodsMixTemplate(call: MethodCall, result: MethodChannel.Result) {
        val previewImageList = arrayListOf<ByteArray>()
        GlobalScope.launch {
            (call.arguments as? Map<*, *>)?.let { arg ->
                val goodsMixList: String = arg.get("goodsMixList") as String
                val fontLibBeanList =
                    json2Array(goodsMixList, Map::class.java) as ArrayList<*>

                fontLibBeanList.forEachIndexed { index, map ->
                    val mTemplateModule = TemplateModuleLocal.fromJson(any2Json(map))
                    mTemplateModule?.let { local ->
                        var localTemplate =
                            TemplateModuleTransform.templateModuleLocalToTemplateModule(local)
                        if (!NetworkUtils.isConnected()) {
                            showToast("app01139")
                        }
                        localTemplate = TemplateSyncLocalUtils.writeModuleImg(localTemplate, false)
                        templateModule = localTemplate
                        TemplateSyncLocalUtils.checkUnDownloadFonts(
                            FlutterBoost.instance().currentActivity(),
                            localTemplate,
                            callbacklistener = {
                                val currentDisplayMultiple =
                                    getCurrentDisplayMultipleForPreview(local.width, local.height)
                                val reviewJson = ImageSdkJsonUtils.getPrintReviewJson(
                                    VipHelper.canUseVipFunc(),
                                    localTemplate.toTemplateModuleLocal(),
                                    /*index*/mTemplateModule.currentPage
                                )
                                val nbCanvasImage = NBCanvasImageApi.nPrintDraw(
                                    0,
                                    reviewJson,
                                    currentDisplayMultiple,
                                    local.getPaccuracy(),
                                    true,
                                    RFIDConnectionProxyManager.getCarbonARGBForPrint()
                                )
                                if (nbCanvasImage?.data != null && nbCanvasImage.data.isNotEmpty()) {
                                    LogUtils.i(
                                        "updateReviewImage",
                                        "nbCanvasImage result width = " + nbCanvasImage.width + " ,height = " + nbCanvasImage.height + ",size = " + nbCanvasImage.data.size
                                    )

                                    previewImageList.add(
                                        nbCanvasImage.data
                                    );

                                }
                                result.success(previewImageList)
                            })

                    }
                }

            }
        }
    }


    ///电子价签商品数据混入
    @DelicateCoroutinesApi
    private fun goodsMixTemplateList(call: MethodCall, result: MethodChannel.Result) {
        val previewImageList = arrayListOf<ByteArray>()
        GlobalScope.launch {
            (call.arguments as? Map<*, *>)?.let { arg ->
                val goodsMixList: String = arg.get("goodsMixList") as String
                val fontLibBeanList =
                    json2Array(goodsMixList, Map::class.java) as ArrayList<*>

                fontLibBeanList.forEachIndexed { index, map ->
                    val mTemplateModule = TemplateModuleLocal.fromJson(any2Json(map))
                    val toLocal: TemplateModuleLocal = templateModule!!.toTemplateModuleLocal()
                    val filter = mTemplateModule!!.elements.filterNot {
                        it is PictureItemModule
                    }
                    mTemplateModule.elements.clear()
                    mTemplateModule.elements.addAll(filter)
                    val filterLocal = toLocal.elements.filterNot {
                        !(it is PictureItemModule)
                    }
                    toLocal.elements.clear()
                    toLocal.elements.addAll(filterLocal)
                    toLocal.elements.addAll(mTemplateModule.elements)
                    toLocal.let { local ->
                        val localTemplate =
                            TemplateModuleTransform.templateModuleLocalToTemplateModule(local)

                        val currentDisplayMultiple =
                            getCurrentDisplayMultipleForPreview(local.width, local.height)
                        val reviewJson = ImageSdkJsonUtils.getPrintReviewJson(
                            VipHelper.canUseVipFunc(),
                            localTemplate.toTemplateModuleLocal(),
                            /*index*/mTemplateModule.currentPage
                        )
                        val nbCanvasImage = NBCanvasImageApi.nPrintDraw(
                            0,
                            reviewJson,
                            currentDisplayMultiple,
                            local.getPaccuracy(),
                            true,
                            RFIDConnectionProxyManager.getCarbonARGBForPrint()
                        )
                        if (nbCanvasImage?.data != null && nbCanvasImage.data.isNotEmpty()) {
                            LogUtils.i(
                                "updateReviewImage",
                                "nbCanvasImage result width = " + nbCanvasImage.width + " ,height = " + nbCanvasImage.height + ",size = " + nbCanvasImage.data.size
                            )

                            previewImageList.add(
                                nbCanvasImage.data
                            );

                        }

                    }
                }

            }
            result.success(previewImageList)
        }
    }

    private fun updateLoginStatus(context: Context, loginData: String?) {
        LogUtils.e("Flutter call Native--reportLoginSuccess: $loginData")

        if (LoginDataEnum.isLogin) return
        try {
            val tokensMap = HashMap<String, String>()
            val jsonObject = JSONObject(loginData!!)
            val loginDataJson = jsonObject.optJSONObject("loginData")
            val loginBusinessDataObj = jsonObject.optJSONObject("loginBusinessData")
            tokensMap["token_type"] = loginDataJson!!.optString("tokenType")
            tokensMap["access_token"] = loginDataJson.optString("accessToken")

            PreferencesUtils.put("jccloud_token_data", any2Json(tokensMap))
            if (!loginDataJson.optString("avatar").isNullOrBlank()) {
                loginBusinessDataObj!!.put("avatar", loginDataJson.optString("avatar"))
            }
            if (!loginDataJson.optString("nickname").isNullOrBlank()) {
                loginBusinessDataObj!!.put("nickname", loginDataJson.optString("nickname"))
            }

            GsonUtils.fromJson(
                loginBusinessDataObj!!.toString(),
                GetUserInfoQuery.GetUserInfo::class.java
            )?.let {
                UserApiManager.freshLogin(it)
                setSentryUser(context, it.displayUId.toString())
            }
        } catch (e: JSONException) {
            LogUtils.e("token JSONException");
        }
    }


    private fun initLogic(
        context: Context,
        sourcePage: String? = "",
        popDirection: String? = "",
        toConnectPage: Boolean
    ) {
        if (sourcePage == "ToNiimbotHomePage") {
            context.startActivity(
                Intent(
                    context,
                    MainActivity::class.java
                ).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                    .putExtra("toConnectPage", toConnectPage)
            )
        } else if (toConnectPage) {
            LeMessageManager.getInstance().dispatchMessage(
                LeMessage(
                    LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                    DeviceConnectActivityConfig(context, true)
                )
            )
        }
        BaseApplication.getInstance().mCurShowActivityReference?.get()?.finish()
    }

    private fun callOneKeyLogin(authType: Int = 1, result: MethodChannel.Result) {
        BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
            ThirdLoginHelper.handlerFlutterOneKeyAuth(it, authType, result)
        }
    }

    private fun handleSimCheck(registered: Boolean, errorToken: Boolean, oneKeyResult: Int) {
        BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
            ThirdLoginHelper.showCheckAccountSuccess(it, registered, errorToken) { authResult ->
                ThirdLoginHelper.handleFlutterAuthResult(authResult, oneKeyResult)
            }
        }
    }

    private fun requestNativePermission(result: MethodChannel.Result) {
        val checkPermissionCallback: (Boolean) -> Unit = {
            if (it) {
                result.success(2)
            } else {
                result.success(1)
            }
        }
        BaseApplication.getInstance().mCurShowActivityReference?.get()?.let { activity ->
            PermissionDialogUtils.showSelectImagePermission(activity) {
                checkPermissionCallback.invoke(it)
            }
        }
    }

    private fun userLogout() {
        BaseApplication.getInstance().mCurShowActivityReference?.get()?.let { activity ->
            NiimbotServiceFactory.getTemplateSyncService()?.stopSync()
            DataCacheUtils.clearAllCache(activity)
            LoginDataEnum.unLogin()
        }
    }


    private fun writeEtagScreen(call: MethodCall, result: MethodChannel.Result) {
        (call.arguments as? Map<String, Any>)?.let { arg ->
            val screenData: Map<String, Any> = arg.get("screenData") as Map<String, Any>

            val mTemplateModule = TemplateModuleLocal.fromJson(any2Json(screenData))
            val toLocal: TemplateModuleLocal = templateModule!!.toTemplateModuleLocal()
            val filter = mTemplateModule!!.elements.filterNot {
                it is PictureItemModule
            }
            mTemplateModule.elements.clear()
            mTemplateModule.elements.addAll(filter)
            val filterLocal = toLocal.elements.filterNot {
                !(it is PictureItemModule)
            }
            toLocal.elements.clear()
            toLocal.elements.addAll(filterLocal)
            toLocal.elements.addAll(mTemplateModule.elements)

            toLocal.let {
                DeviceETagHelper.getInstance()
                    .getScreenInfo(arg["index"] as Int, object : screenResultListener<Int> {
                        override fun onSuccess(value: Int?) {

                        }

                        override fun onScreenSuccess(p0: String) {

                            GlobalScope.launch {
                                val gson = Gson()
                                val type = object : TypeToken<Map<String, Any>>() {}.type
                                val map = gson.fromJson(p0, type) as? Map<String, Any>
                                val disPlayWidth = map!!["width"] as Double
                                initDisplayMultiple(it.width, disPlayWidth.toInt())
                                val currentDisplayMultiple =
                                    getCurrentDisplayMultipleForPreview(it.width, it.height)
                                val reviewJson = ImageSdkJsonUtils.getPrintJson(
                                    VipHelper.canUseVipFunc(),
                                    it,
                                    arg["index"] as Int,
                                    mTemplateModule.currentPage,
                                )
                                val nbCanvasImage = NBCanvasImageApi.nPrintDraw(
                                    0,
                                    reviewJson,
                                    currentDisplayMultiple,
                                    currentDisplayMultiple,
                                    false,
                                    RFIDConnectionProxyManager.getCarbonARGBForPrint()
                                )
                                if (nbCanvasImage?.data != null && nbCanvasImage.data.isNotEmpty()) {
                                    LogUtils.i(
                                        "=======updateReviewImage",
                                        "=====eTag--reviewJson: $reviewJson, nbCanvasImage result width = " + nbCanvasImage.width + " ,height = " + nbCanvasImage.height + ",size = " + nbCanvasImage.data.size
                                    )

                                    if (arg["isNfc"] as Boolean) {
                                        val preBitmap = BitmapFactory.decodeByteArray(
                                            nbCanvasImage.data,
                                            0,
                                            nbCanvasImage.data.size
                                        )
                                        DeviceETagHelper.getInstance()
                                            .writetoNfc(
                                                preBitmap,
                                                object : screenResultListener<Int> {
                                                    override fun onSuccess(value: Int?) {
                                                        result.success(true)
                                                    }

                                                    override fun onScreenSuccess(p0: String) {
                                                    }

                                                    override fun onFail(value: Int) {
                                                        result.success(false)
                                                    }
                                                })
                                    } else {
                                        DeviceETagHelper.getInstance()
                                            .writeScreenForBuffer(
                                                arg["index"] as Int,
                                                nbCanvasImage.data,
                                                nbCanvasImage.width,
                                                nbCanvasImage.height,
                                                object :
                                                    screenResultListener<Int> {
                                                    override fun onSuccess(value: Int?) {
                                                    }

                                                    override fun onScreenSuccess(p0: String) {
                                                        try{
                                                            val json = JSONObject(p0)
                                                            val map = mutableMapOf<String, Any?>()
                                                            json.keys().forEach {
                                                                map[it] = json.get(it)
                                                            }
                                                            map["screenData"] = reviewJson
                                                            val tempResult = any2Json(map)
                                                            result.success(tempResult)
                                                        } catch(e: Exception) {
                                                            e.printStackTrace()
                                                            result.success(p0)
                                                        }
                                                    }

                                                    override fun onFail(value: Int) {
                                                        result.success(value)
                                                    }
                                                })
                                    }
                                }
                            }

                        }

                        override fun onFail(value: Int) {
                            result.success(0)
                        }
                    })

            }
        }
    }


    private fun getSupportSocialList(): List<String> {
        val result = arrayListOf<String>()
        if (OtherAppUtil.isWXInstalled(SuperUtils.superContext)) result.add("weixin")
        if (OtherAppUtil.isQQInstalled(SuperUtils.superContext)) result.add("qq")
        if (GooglePayHelper.isGooglePlayServicesAvailable()) result.add("google")
        if (NiimbotGlobal.isGoogleChannel() || NiimbotGlobal.isSamsungChannel()) result.add("facebook")
        return result
    }

    private fun getImportExcelSocialList(): List<String> {
        val result = arrayListOf<String>()
        if (OtherAppUtil.isWXInstalled(SuperUtils.superContext)) result.add("wechat")
        if (OtherAppUtil.isQQInstalled(SuperUtils.superContext)) result.add("qq")
        //大于等于android11，由于权限问题无法通过钉钉导入Excel文件
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R && OtherAppUtil.isDINGInstalled(SuperUtils.superContext)) result.add(
            "dingtalk"
        )
        if (OtherAppUtil.isWhatsAppInstalled(SuperUtils.superContext)) result.add("whatsapp")
        if (OtherAppUtil.isLineInstalled(SuperUtils.superContext)) result.add("line")
        return result
    }

    private fun getPluginLanguageDetail(result: MethodChannel.Result) {
        PluginLangHelper.getLoginPluginLangPack {
            it?.let { str ->
                try {
                    com.niimbot.fastjson.JSONObject.parseObject(str)?.innerMap?.let { value ->
                        result.success(value)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
    }

    private fun updateUserInfo(userJson: String?) {
        try {
            LogUtils.e("Flutter call native to updateUserInfo: $userJson")
            val loginData = try {
                JSONObject(userJson!!)
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
            if (userJson.isNullOrEmpty()) {
                UserApiManager.freshLogin(
                    needFreshShopToken = false,
                    needLogin = false,
                    freshLogin = false
                )
            } else {
                if (null != loginData && !userJson.isNullOrEmpty()) {
                    val tokensMap = HashMap<String, String>()
                    val token = loginData.optString("accessToken")
                    if (!token.isNullOrEmpty()) {
                        tokensMap["token_type"] = loginData.optString("tokenType")
                        tokensMap["access_token"] = token
                        PreferencesUtils.put("jccloud_token_data", any2Json(tokensMap))
                        UserApiManager.freshShopToken()
                        NiimbotGlobal.loginCallbackInvoke()
                    }
                    LoginDataEnum.freshMainAccount(
                        loginData.optString("phone"),
                        loginData.optString("email")
                    )
                }
            }
        } catch (e: JSONException) {
            LogUtils.e("JSONException: $e");
        }
    }

    private fun getUserInfoForFlutter(
        result: MethodChannel.Result,
        accessToken: String,
        tokenType: String
    ) {
        if (!accessToken.isNullOrBlank() && !tokenType.isNullOrBlank()) {
            val tokensMap = HashMap<String, String>()
            tokensMap["token_type"] = tokenType
            tokensMap["access_token"] = accessToken
            PreferencesUtils.put("jccloud_token_data", any2Json(tokensMap))
        }
        LogUtils.e("getUserInfoForFlutter---->accessToken:  $accessToken, tokenType: $tokenType")
        UserApiManager.getUserInfoForFlutter {
            if (LoginDataEnum.isLogin) {
                LoginUtils.freshUserModule(infoMap = it)
            }

            result.success(it?.toMutableMap())
        }
    }

    private fun getTemplateDetailDelegate(
        context: Activity,
        templateId: String,
        isPersonalTemplate: Boolean = false,
        needLoadFonts: Boolean = false,
        isShare: Boolean = true,
        saveRecord: Boolean = true,
        resultCall: MethodChannel.Result
    ) {
        TemplateSyncLocalUtils.getTemplateDetails(
            templateId,
            isPersonalTemplate = isPersonalTemplate,
            needLoadFonts = needLoadFonts,
            isShare = isShare
        ) { result, templateModule, errorMsg ->
            if (result) {
                if (templateModule != null) {
                    val result = templateModule.toTemplateModuleLocal()
                    if (saveRecord) TemplateSyncLocalUtils.saveUsedRecord(result)
                    resultCall.success(any2Json(result))
                } else {
                    resultCall.success("")
                }
            } else {
                if (!errorMsg.isNullOrEmpty()) {
                    showToast(errorMsg)
                }
                resultCall.success("")
            }
        }
    }


    ///创建表单活码
    private fun createAdvanceQRCode(call: MethodCall, result: MethodChannel.Result) {
        // NiimbotPlugin.
        (call.arguments as? String)?.let {
            if (it.equals("liveCode")) {
                NiimbotGlobal.gotoLiveCodeApp("?from=canvas")
                NiimbotServiceFactory.getBuriedService()
                    ?.saveEventData("drawingboard_hippo_click", true, hashMapOf())
            } else {
                NiimbotGlobal.gotoFormApp("?from=canvas")
            }

            observers = object : channelCallBack {
                override fun result(value: String) {
                    var isRepeat = false
                    val event = JSON.parseObject(value)
                    val dataMap =
                        if (event["action"]!!.equals("tag_create") || event["action"]!!.equals("tag_fresh")) {
                            val liveCode = GsonUtils.fromJson(
                                event.getString("data"),
                                LiveCodeService.LiveCode::class.java
                            )
                            LiveCodeService.buildFlutterMap(liveCode)
                        } else {
                            val sheet = GsonUtils.fromJson(
                                event.getString("data"),
                                FormService.Form::class.java
                            )
                            if (event["action"]!!.equals("tag_sheet_fresh")) {
                                val form = FormService.getFormSP()
                                    .decodeString("${FormService.KEY_FORM_PRE}${sheet.id}", "")
                                isRepeat = form.isNullOrEmpty()
                            }
                            FormService.buildFlutterMap(sheet)
                        }
                    val advanceData = mutableMapOf("type" to "select", "data" to dataMap)
                    if (event["action"]!!.equals("tag_sheet_fresh") || event["action"]!!.equals("tag_fresh")) {
                        advanceData.remove("type")
                    }
                    val data =
                        mutableMapOf(
                            "action" to "advanceQRCode",
                            "advanceQRCodeInfo" to advanceData
                        )

                    if (!isRepeat) {
                        FlutterEventRegister.sendEvent(JSON.parseObject(any2Json(data)))
                    }


                    //result.success(data)
                }
            }
        }
    }

    private fun previewAdvanceQRCode(call: MethodCall, result: MethodChannel.Result) {
        (call.arguments as? Map<String, Any>)?.let {
            if (it["codeType"]!!.equals("liveCode")) {
                NiimbotGlobal.gotoLiveCodeApp("/preview/${it["codeId"]}?from=canvas")
            } else {
                NiimbotGlobal.gotoFormApp("/edit/preview/${it["codeId"]}?from=canvas")
            }
        }
        observers = object : channelCallBack {
            override fun result(value: String) {
                var event = JSON.parseObject(value)
                val dataMap = if (event["action"]!!.equals("tag_create")) {
                    var liveCode = GsonUtils.fromJson(
                        event.getString("data"),
                        LiveCodeService.LiveCode::class.java
                    )
                    LiveCodeService.buildFlutterMap(liveCode)
                } else {
                    val sheet = GsonUtils.fromJson(
                        event.getString("data"),
                        FormService.Form::class.java
                    )
                    FormService.buildFlutterMap(sheet)
                }

                val data = mutableMapOf("type" to "select", "data" to dataMap)
                result.success(data)
            }
        }
    }


    ///获取表单活码详情
    private fun getAdvanceQRCode(call: MethodCall, result: MethodChannel.Result) {
        (call.arguments as? Map<String, Any>)?.let {
            if (it["codeType"]!!.equals("liveCode")) {
                LiveCodeService.getLiveCodeDetail(it["codeId"].toString()) {
                    result.success(LiveCodeService.buildFlutterMap(it!!))
                }
            } else {
                FormService.getFormDetail(it["codeId"].toString()) {
                    result.success(FormService.buildFlutterMap(it!!))
                }
            }
        }
    }

    ///缓存表单活码详情
    private fun cacheAdvanceQRCode(call: MethodCall, result: MethodChannel.Result) {
        (call.arguments as? Map<String, Any>)?.let {
            val codeType = it["codeType"] as? String
            val dataList = it["data"] as? List<Map<String, Any>>
            if(codeType == null || dataList == null){
                result.success(null)
                return
            }
            if (codeType == "liveCode") {
                dataList.forEach { item ->
                    val id = item["id"] as? String
                    val itemData = item["item"] as? String
                    getFormSP().encode("${KEY_LIVECODE_PRE}${id}", itemData)
                }
                result.success(null)
            } else {
                dataList.forEach { item ->
                    val id = item["id"] as? String
                    val itemData = item["item"] as? String
                    getFormSP().encode("${KEY_FORM_PRE}${id}", itemData)
                }
                result.success(null)
            }
        }
    }

    ///获取表单活码缓存
    private fun getAdvanceQRCodeCaches(call: MethodCall, result: MethodChannel.Result) {

        val advanceList: MutableList<Map<String, Any>> = mutableListOf()
        (call.arguments as? Map<String, Any>)?.let {
            val livecodes = it["liveCodeIds"].toString().split(",")
            val formIds = it["formIds"].toString().split(",")
            getLiveCodeDetail(0, 0, livecodes, formIds, advanceList, result)
        }

    }


    private fun getLiveCodeDetail(
        liveCodesIndex: Int,
        formIndex: Int,
        livecodes: List<String>,
        formIds: List<String>,
        advanceList: MutableList<Map<String, Any>>,
        result: MethodChannel.Result
    ) {
        if (liveCodesIndex < livecodes.size && livecodes[liveCodesIndex].isNotEmpty()) {
            LiveCodeService.getLiveCodeDetail(livecodes[liveCodesIndex]) { s ->
                LogUtils.e("getLiveCodeDetail livecode= ${livecodes[liveCodesIndex]} ${s}")
                if(s != null){
                    advanceList.add(LiveCodeService.buildFlutterMap(s))
                }

                val index = liveCodesIndex + 1;
                getLiveCodeDetail(index, formIndex, livecodes, formIds, advanceList, result)
            }
        } else if (formIndex < formIds.size && formIds[formIndex].isNotEmpty()) {
            FormService.getFormDetail(formIds[formIndex]) { s ->
                LogUtils.e("getLiveCodeDetail form=${formIds[formIndex]} ${s}")
                if(s != null){
                    advanceList.add(FormService.buildFlutterMap(s))
                }
                val index = formIndex + 1;
                getLiveCodeDetail(liveCodesIndex, index, livecodes, formIds, advanceList, result)
            }
        } else {
            result.success(advanceList)
        }

    }

    private fun saveTemplate(call: MethodCall, result: MethodChannel.Result) {
        val templateJson = call.argument<String>("templateData")
        val isShare = call.argument<Boolean>("isShare") ?: false
        val isNeedAlert = call.argument<Boolean>("isNeedAlert") ?: false
        val isOtherSave = call.argument<Boolean>("isOtherSave") ?: false
        val isBackSave = call.argument<Boolean>("isBackSave") ?: false
        val extData = call.argument<Map<String, Any>>("extData") ?: emptyMap()
        var isEtag = false
        if (extData.isNotEmpty() && extData.containsKey("isEtag")) {
            isEtag = extData["isEtag"] as Boolean
        }


        LogUtils.e("saveTemplate---> templateJson: $templateJson")
        val templateDetail = templateJson?.let {
            TemplateModuleLocal.fromJson(it)
        }

        //新画板过来的数据统一添加VER字段
        templateDetail?.templateVersion = TemplateVersionConst.BASE_SAVE_TEMPLATE_VERSION
        templateDetail?.elements?.forEach {
            if (it is TimeItemModule) {
                templateDetail?.templateVersion =
                    TemplateVersionConst.REALTIME_SAVE_TEMPLATE_VERSION
            }
        }
        if (templateDetail?.isGoodsTemplate() == true && TemplateUtils.hasCustomGoodFields(
                templateDetail
            )
        ) {
            templateDetail?.templateVersion =
                TemplateVersionConst.GOOD_ADD_FIELD_SAVE_TEMPLATE_VERSION
        }
        if(templateDetail?.isExcelXLSTemplate() == true) {
            templateDetail?.templateVersion =
                TemplateVersionConst.EXCEL_XLS_TEMPLATE_VERSION
        }
        if (templateDetail != null) {
            val exist = if (isEtag) {
                isEtagTemplateExist(templateDetail.id)
            } else {
                false
            }
            if (templateDetail.profile.extrain.templateType == 2) {
                templateDetail.commodityTemplate = true
            }
            val topAct = com.blankj.utilcode.util.ActivityUtils.getTopActivity()
            var isFromPrintSetting = false
            if(topAct is FlutterBoostActivity && topAct.url == "printSetting"){
                isFromPrintSetting = true
            }

            TemplateSyncLocalUtils.checkHasSynced(templateDetail) { module ->
                CanvasNativeDelegate.handleTemplateSave(
                    FlutterBoost.instance().currentActivity(),
                    isNeedAlert = isNeedAlert,
                    isOtherSave = isOtherSave,
                    showOtherSave = exist,
                    isShare = isShare,
                    templateModuleLocal = module!!,
                    isEtagTemplate = isEtag,
                    isFromPrintSetting = isFromPrintSetting,
                    isBackSave = isBackSave
                ) {
                    result.success(any2Json(it))
                }
            }
        }
    }

    fun postConnectState() {
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val printMap = com.niimbot.fastjson.JSONObject()
                val series = DevicesSeriesLocalUtils.getCurrentSerialInfoBySerialName(RFIDConnectionProxyManager.connectedDevice?.name ?: "")
                val antiCounterfeiKey = withContext(Dispatchers.IO) {
                    requestAntiCounterfeiKey.invoke(
                        RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""
                    )
                }
                printMap.put("action", "printerConnectState")
                printMap.put(
                    "printerConnectState", mapOf(
                        "connected" to if (RFIDConnectionProxyManager.connectedDevice != null) 1 else 0,
                        "machineId" to RFIDConnectionProxyManager.connectedDevice?.id.toString(),
                        "machineName" to RFIDConnectionProxyManager.connectedDevice?.name,
                        "seriesId" to series?.id,
                        "seriesName" to series?.guide_name,
                        "rfidStatus" to RFIDConnectionProxyManager.rfidStatus(),
                        "hardwarVersion" to RFIDConnectionProxyManager.connectedDevice?.hardwareVersion,
                        "firmwarVersion" to RFIDConnectionProxyManager.connectedDevice?.softwareVersion,
                        "machineSecret" to antiCounterfeiKey
                    )
                )
                if (RFIDConnectionProxyManager.connectedDevice != null) {
                    val currentDevice = json2Any(
                        any2Json(RFIDConnectionProxyManager.connectedDevice),
                        DevicesModule::class.java
                    )
                    printMap.put("printer", any2Json(DevicesModule.localToRemote(currentDevice)))
                }
                EventBus.getDefault().post(any2Json(printMap))
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

    }

    private fun setSentryUser(context: Context, uid: String){
        var user = io.sentry.protocol.User()
        user.id = uid
        Sentry.setUser(user)
        Sentry.setTag("device_id", BuriedHelper.getEventAnonymousId())
    }

    /**
     * 格式化键值对，与iOS的formatKeyValuePairWithKey1方法功能一致
     * @param key1 第一个键
     * @param value1 第一个值
     * @param gap 间隔符
     * @param key2 第二个键
     * @param value2 第二个值
     * @return 格式化后的字符串
     */
    private fun formatKeyValuePair(key1: String?, value1: String?, gap: String?, key2: String?, value2: String?): String {
        return "${key1 ?: ""}: ${value1 ?: ""}${gap ?: "  "}${key2 ?: ""}: ${value2 ?: ""}"
    }

    private fun notifyToCanvasPage() {
        if(FlutterBoost.instance().currentActivity() is TransparentActionBarActivity) {
            (FlutterBoost.instance().currentActivity() as TransparentActionBarActivity).isDrawboardPage = true
        } else if (FlutterBoost.instance().currentActivity() is TransparencyPageActivity) {
            (FlutterBoost.instance().currentActivity() as TransparencyPageActivity).isDrawboardPage = true
        }
    }

    private fun initLiveCodeEventCallback() {
        observers = object : channelCallBack {
            override fun result(value: String) {
                var isRepeat = false
                val event = JSON.parseObject(value)
                val dataMap =
                    if (event["action"]!!.equals("tag_create") || event["action"]!!.equals("tag_fresh")) {
                        val liveCode = GsonUtils.fromJson(
                            event.getString("data"),
                            LiveCodeService.LiveCode::class.java
                        )
                        LiveCodeService.buildFlutterMap(liveCode)
                        getFormSP().encode("${KEY_LIVECODE_PRE}${liveCode.id}",  event.getString("data"))
                    } else {
                        val sheet = GsonUtils.fromJson(
                            event.getString("data"),
                            FormService.Form::class.java
                        )
                        if (event["action"]!!.equals("tag_sheet_fresh")) {
                            val form = FormService.getFormSP()
                                .decodeString("${FormService.KEY_FORM_PRE}${sheet.id}", "")
                            isRepeat = form.isNullOrEmpty()
                        }
                        FormService.buildFlutterMap(sheet)
                        getFormSP().encode("${KEY_FORM_PRE}${sheet.id}", event.getString("data"))
                    }
                val advanceData = mutableMapOf("type" to "select", "data" to dataMap)
                if (event["action"]!!.equals("tag_sheet_fresh") || event["action"]!!.equals("tag_fresh")) {
                    advanceData.remove("type")
                }
                val data =
                    mutableMapOf(
                        "action" to "advanceQRCode",
                        "advanceQRCodeInfo" to advanceData
                    )

                if (!isRepeat) {
                    FlutterEventRegister.sendEvent(JSON.parseObject(any2Json(data)))
                }

                //result.success(data)
            }
        }
    }
}
