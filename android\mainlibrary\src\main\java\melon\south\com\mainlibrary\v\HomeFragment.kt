package melon.south.com.mainlibrary.v

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.os.Handler
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewTreeObserver
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.Lifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager.widget.ViewPager
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.SizeUtils
import com.gengcon.android.jccloudprinter.push.PushBindUtil
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connect.JCConnectionManager
import com.gengcon.print.draw.DrawIntentParams
import com.gengcon.print.draw.module.event.GetRFIDEvent
import com.gengcon.print.draw.module.event.SHOP_SOURCE_MAIN_BANNER
import com.gengcon.print.draw.module.event.SHOP_SOURCE_MAIN_RFID
import com.gengcon.print.draw.module.logo.LogoHelper
import com.gengcon.print.draw.print.event.ServerRfidGetEvent
import com.gengcon.print.draw.proxy.ConnectionProxyManager
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.idlefish.flutterboost.FlutterBoost
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.shop.ShopType
import com.niimbot.appframework_library.CONNECTED_DEVICE_NAME
import com.niimbot.appframework_library.CURRENT_DEVICE_ELECTRICIRY
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.eventbus.FolderTemplateSyncData
import com.niimbot.appframework_library.common.module.eventbus.TemplateEvent
import com.niimbot.appframework_library.common.module.eventbus.TemplateEventType
import com.niimbot.appframework_library.common.module.eventbus.TemplateSyncData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.item.PictureItemModule
import com.niimbot.appframework_library.common.module.template.item.TextItemModule
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.util.TemplateUtils
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.expand.clipRounded
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.heightChanged
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.showImage
import com.niimbot.appframework_library.expand.showImageWithNiimbot
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.protocol.main.CommentActivityConfig
import com.niimbot.appframework_library.protocol.template.NewScanActivityConfig
import com.niimbot.appframework_library.protocol.template.TemplateDetailsActivityConfig
import com.niimbot.appframework_library.protocol.template.TemplateIndustryActivityConfig
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.ImageLoader
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.ab.ABTestHelper
import com.niimbot.baselibrary.ab.ABTestUtils
import com.niimbot.baselibrary.ab.GrayModule
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.templateVersion.TemplateVersionConst
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.bluetooth.BluetoothUtil
import com.niimbot.common.skin.SkinHelper
import com.niimbot.common.skin.SkinResource
import com.niimbot.fastjson.JSONObject
import com.niimbot.graphqllibrary.Apollo
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.templatecoordinator.core.MainTemplateLocalUtils
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.utiliylibray.util.AdManager
import com.niimbot.utiliylibray.util.AdType
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.viplibrary.VipHelper
import com.niimbot.viplibrary.event.GoVipNpsEvent
import com.niimbot.viplibrary.repository.VipRepository
import com.nimmbot.business.livecode.CapAppHelper
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.LocaleUtils
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.json2Any
import com.southcity.watermelon.util.logE
import com.southcity.watermelon.util.logI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.async
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.JCApi
import melon.south.com.baselibrary.base.BasePagerFragment
import melon.south.com.baselibrary.base.LaboratoryActivity
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.eventbus.AbleToPrint
import melon.south.com.baselibrary.local.module.BannerModule
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.local.util.AdLocalUtils
import melon.south.com.baselibrary.local.util.BannerLocalUtils
import melon.south.com.baselibrary.local.util.DevicesSeriesLocalUtils
import melon.south.com.baselibrary.module.PrintModule
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.EventBusUtils
import melon.south.com.baselibrary.util.StringUtil
import melon.south.com.baselibrary.util.TextSpannableUtil
import melon.south.com.baselibrary.util.showToast
import melon.south.com.baselibrary.view.FixedStaggeredGridLayoutManager
import melon.south.com.mainlibrary.R
import melon.south.com.mainlibrary.databinding.FragmentHomeBinding
import melon.south.com.mainlibrary.nps.NpsManager
import melon.south.com.mainlibrary.util.MachineActUtils
import melon.south.com.mainlibrary.util.NpsUtils
import melon.south.com.mainlibrary.util.NpsVipUtil
import melon.south.com.mainlibrary.util.Streamer
import melon.south.com.mainlibrary.util.StreamerBusiness
import melon.south.com.mainlibrary.v.event.CloseNpsEvent
import melon.south.com.templatelibrary.dao.TemplateShopStatusUtils
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import melon.south.com.templatelibrary.utils.VersionCompareUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File


class HomeFragment : BasePagerFragment() {
    private val binding by viewBinding(FragmentHomeBinding::bind)

    companion object {
        var showDeviceBubble = false

        var tag_icon_home_industry = "icon_home_industry"
        var tag_icon_home_scan = "icon_home_scan"
        var tag_bg_home_rfid = "bg_home_rfid"
        var tag_bg_home_list = "bg_home_list"
        var tag_bg_home_top_operate = "bg_home_top_operate"
        var showNps = true
    }

    private var mData = arrayListOf<TemplateModule>()
    private val mAdapter: MainTemplateAdapter = MainTemplateAdapter(mData)
    private var npsData: HashMap<String, Any>? = null
    private var npsUrl: String? = null
    private var bannerModuleList: List<BannerModule>? = null
    val niimbotDrawData = NiimbotDrawData()

    override fun getFragmentTag() = "HomeFragment"
    override fun getLayoutId() = R.layout.fragment_home

    override fun init(view: View) {
        EventBusUtils.register(this)
        isInit = true
        getNpsData(TextHookUtil.getInstance().languageName)
        getVipNpsData(TextHookUtil.getInstance().languageName)
        updatePrinterInfo(JCConnectionManager.getInstance().isConnected())
        mAdapter.addFoot(R.layout.adapter_main_footer)
        mAdapter.setHasStableIds(true)
        val layoutManager = FixedStaggeredGridLayoutManager(2, LinearLayoutManager.VERTICAL)
        binding.rlMyTemplate.layoutManager = layoutManager
        binding.rlMyTemplate.setHasFixedSize(true)
        binding.rlMyTemplate.adapter = mAdapter
        binding.srlRefresh.setDragRate(1f)
        binding.srlRefresh.setEnableLoadMore(false)
        //获取模板列表数据
        if (activity?.intent?.getBooleanExtra("toConnectPage", false) == true
            && !LoginDataEnum.isLogin
        ) {
            changeDateUI(mData.size > 0, "getData.fail")
        } else {
            binding.srlRefresh.autoRefresh()
        }
        initBanner()
        freshMoreText()
        LoginDataEnum.registerChange(this)
        AdLocalUtils.cacheAdData()
        getUserSystemConfig()
        showNetError(NetworkUtils.isConnected(), NetworkUtils.getErrorMsg())

        notifyUpdateSkin()
        initTopBg()

        if (LocaleUtils.isRtl(requireContext())) {
            binding.tvAppTitle.gravity = Gravity.RIGHT
            binding.tvLabelName.gravity = Gravity.RIGHT
            binding.tvTextNotice.gravity = Gravity.RIGHT
        }
        StreamerBusiness.loadRequestStreamer {
            handleStreamer(it)
        }
        binding.layoutNotice.setOnNotDoubleClickListener {
            val streamer =
                binding.layoutNotice.tag as? Streamer ?: return@setOnNotDoubleClickListener
            val redirectUrl = streamer.redirectUrl
            val typeCode = streamer.typeCode
            val title = streamer.title
            if (!redirectUrl.isNullOrEmpty()) {
                activity?.let {
                    NiimbotGlobal.bannerRouter(
                        it,
                        redirectUrl,
                        typeCode!!,
                        title = title!!,
                        jumpSource = "y_page_main_notice"
                    )
                }
                BuriedHelper.trackEvent(
                    "click",
                    "003_286",
                    hashMapOf(
                        Pair("b_name", streamer.content!!),
                        Pair("banner_id", streamer.id),
                        Pair("a_name", streamer.activityName ?: "")
                    )
                )
            }
        }
        binding.ivNoticeClose.setOnNotDoubleClickListener {
            binding.layoutNotice.visibility = View.GONE
            binding.mainLayoutMargin.visibility = View.VISIBLE
            val streamer = binding.layoutNotice.tag as? Streamer
            streamer?.recordShowTime()
        }
    }

    private fun getUserSystemConfig() {
        if (!LoginDataEnum.isLogin) {
            return
        }
        Apollo.getUserSystemConfig { userSystemConfig, errorMsg ->
            if (null != userSystemConfig) {
                val configMap = userSystemConfig.config as Map<String, Map<String, Any>>
                val status = configMap["LAUNCH_PAGE_SWITCH"]?.get("status") as? String
                if (status != null) {
                    val isOpen = status == "true"
                    PreferencesUtils.put("${LoginDataEnum.id}-launchPageSwitch", isOpen)
                }
            }
        }
    }

    private fun handleStreamer(streamer: Streamer) {
        try {
            if (binding.tvTextNotice == null || binding.layoutNotice == null) return
            if (streamer.canShow()) {
                binding.tvTextNotice.text = streamer.content
                binding.layoutNotice.tag = streamer
                binding.layoutNotice.visibility = View.VISIBLE
                binding.mainLayoutMargin.visibility = View.GONE
                BuriedHelper.trackEvent(
                    "show",
                    "003_286",
                    hashMapOf(
                        Pair("b_name", streamer.content!!),
                        Pair("banner_id", streamer.id),
                        Pair("a_name", streamer.activityName ?: ""))
                )
            } else {
                binding.layoutNotice.tag = null
                binding.layoutNotice.visibility = View.GONE
                binding.mainLayoutMargin.visibility = View.VISIBLE
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun freshNps() {
        if (!isInit) {
            return
        }
        FlutterMethodInvokeManager.isShowNps { result ->
            result?.let {
                val visible = if (result == "1") View.VISIBLE else View.GONE
                binding.rlNps.visibility = visible
            }

        }
    }

    private fun freshMoreText() {
        val showMore =
            com.niimbot.baselibrary.user.LoginDataEnum.isLogin || !NetworkUtils.isConnected()
    }

    override fun initEvent(view: View) {

        val start = System.currentTimeMillis()
        binding.ivNps.setOnNotDoubleClickListener {
            //没网提示网络异常
            if (!NetworkUtils.isConnected()) {
                showToast("app01139")
                return@setOnNotDoubleClickListener
            }
            openNpsPage(TextHookUtil.getInstance().languageName)
        }
        binding.ibNpsClose.setOnNotDoubleClickListener {
            showNps = false
            binding.rlNps.visibility = View.GONE
            binding.rlNps.invalidate()
            FlutterMethodInvokeManager.closeNps(hasSubmitNpsData = false)
            BuriedHelper.trackEvent(
                "click",
                "112_225",
                hashMapOf(
                    Pair("source", "1"),
                )
            )
        }
        //蓝牙连接
        binding.tvTitleConnect.setOnNotDoubleClickListener {
            binding.tvConnect.performClick()
        }
        binding.llConnectBtnConnect.setOnNotDoubleClickListener {
            binding.tvConnect.performClick()
        }
        binding.tvConnect.setOnNotDoubleClickListener {
            BuriedHelper.trackEvent("click", "003_003_009")
            if (JCConnectionManager.getInstance().isConnected()) {
                BuriedHelper.trackEvent("click", "003_145_149")
            }
            LeMessageManager.getInstance().dispatchMessage(
                LeMessage(
                    LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                    DeviceConnectActivityConfig(activity, true).apply {
                        this.intent.putExtra("homeManualClickConnect", true)
                    }
                )
            )

        }

        //行业模板
        binding.ivTitleIndustry.setOnNotDoubleClickListener {
            binding.tvIndustryTemplate.performClick()
        }
        binding.tvIndustryTemplate.setOnNotDoubleClickListener {
            BuriedHelper.trackEvent(
                "click", "003_004_010",
                hashMapOf(
                    Pair("type", "1"),
                )
            )

            if (LaboratoryActivity.getIndustryTemplateEntrance() == LaboratoryActivity.INDUSTRY_TEMPLATE_NEW) {
                val paramsMap = HashMap<String, Any>()
                paramsMap.put("token", HttpTokenUtils.getAccessToken())
                NiimbotGlobal.gotoFlutterPage(
                    "industryTemplate",
                    paramsMap, isTransParent = false
                )
                return@setOnNotDoubleClickListener
            }

            if (LaboratoryActivity.getIndustryTemplateEntrance() == LaboratoryActivity.INDUSTRY_TEMPLATE_OLD) {
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        TemplateIndustryActivityConfig(activity)
                    )
                )
                return@setOnNotDoubleClickListener
            }

            if (ABTestHelper.canEnterNewIndustry()) {
                val paramsMap = HashMap<String, Any>()
                paramsMap.put("token", HttpTokenUtils.getAccessToken())
                NiimbotGlobal.gotoFlutterPage(
                    "industryTemplate",
                    paramsMap, isTransParent = false
                )
            } else {
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        TemplateIndustryActivityConfig(activity)
                    )
                )
            }

        }

        //扫一扫
        binding.ivTitleTemplate.setOnNotDoubleClickListener {
            AdLocalUtils.cacheAdData()
            binding.tvScanTemplate.performClick()
        }
        binding.tvScanTemplate.setOnNotDoubleClickListener {
            BuriedHelper.trackEvent("click", "003_004_011")
            getTemplateByScanCode()
        }

        //rfid 新建内容
        binding.ivLabelBg.setOnNotDoubleClickListener {
            clickRfidTemplate()
        }


        binding.llMyTemplate.setOnNotDoubleClickListener {
            activity?.let { mActivity ->

                NiimbotGlobal.gotoFlutterPage(
                    "myTemplate",
                    hashMapOf("token" to HttpTokenUtils.getAccessToken()), isTransParent = true
                )

            }
        }
        binding.tvPrintHistory.setOnNotDoubleClickListener {
//            TemplateSyncLocalUtils.syncPrintHistory()
            LoginDataEnum.loginCheck(activity as AppCompatActivity) {
                NiimbotGlobal.gotoFlutterPage(
                    "printHistory",
                    hashMapOf("isPresent" to false, "token" to HttpTokenUtils.getAccessToken())
                )
            }
        }
//        binding.tvRfidCover.setOnNotDoubleClickListener {
//            updateRfidInfo(ConnectionProxyManager.connectState)
//        }

        binding.srlRefresh.setOnRefreshListener {
            getData()
        }
        mAdapter.onItemClickListener =
            { index ->
                if (mData.isNotEmpty() && index < mData.size) {
                    val templateId =  mData[index].id
                    BuriedHelper.trackEvent(
                        "click", "003_005_012",
                        hashMapOf(
                            Pair("temp_id", mData[index].id),
                            Pair("pos", index + 1),
                        )
                    )
                    //进入画板流程
                    GlobalLoadingHelper.showLoading(activity!!)
                    GlobalScope.launch {
                        var result: TemplateModuleLocal? =null
                        val niimbotDrawData = NiimbotDrawData()
                        val templateJson = FlutterMethodInvokeManager.getTemplateDetail(templateId,needUpdateDb = true)
                        withContext(Dispatchers.Main){
                            GlobalLoadingHelper.dismissLoading()
                        }
                        templateJson?.let {
                            result = TemplateModuleLocal.fromJson(it)
                        }
                        result?.let {
                            niimbotDrawData.niimbotTemplate= it
                            TemplateSyncLocalUtils.saveUsedRecordByLabelId(it.profile.extrain.labelId)
                            if(!NetworkUtils.isConnected() && it.isGoodTemplate()){
                                showToast("app100001136")
                                return@launch
                            }
                            if(!NetworkUtils.isConnected() && it.isExcelTemplate()){
                                val hasExcelCache = FlutterMethodInvokeManager.hasExcelFileCache(it.getExcelHash())
                                if(!hasExcelCache){
                                    showToast("app01139")
                                    return@launch
                                }
                            }
                            //断网并且模版资源不完整，则toast提示网络异常
                            if(!NetworkUtils.isConnected() && !it.canEdit(isCheckBackground = true)){
                                showToast("app01139")
                                return@launch
                            }
                            if(it.isOldPCExcelTemplate()){
                                showToast("app01393")
                                return@launch
                            }
                        }
                        withContext(Dispatchers.Main){
                            TemplateSyncLocalUtils.downloadTemplateFonts(requireActivity(),result)
                            SyncTemplatePresenter.startNiimbotDrawActivity(
                                activity = requireActivity(),
                                niimbotDrawData = niimbotDrawData,
                                isRetainActivity = true,
                                needRfidTemplate = false,
                                needDownloadFonts = false
                            )
                        }
                    }
//                    LeMessageManager.getInstance().dispatchMessage(
//                        LeMessage(
//                            LeMessageIds.MSG_ACTION_GO_ACTIVITY,
//                            TemplateDetailsActivityConfig(activity).apply {
//                                intent
//                                    .putExtra("templateId", mData[index].id)
//                                    .putExtra("isTemplateLabel", false)
//                            }
//                        )
//                    )
                }
            }
        mAdapter.onItmPrintClickListener =
            { index ->
                if (mData.isNotEmpty() && index < mData.size) {
                    val templateId =  mData[index].id
                    BuriedHelper.trackEvent(
                        "click", "003_005_440",
                        hashMapOf(
                            Pair("temp_id", mData[index].id),
                            Pair("pos", index + 1),
                        )
                    )
                    //进入打印流程
                    GlobalLoadingHelper.showLoading(activity!!)
                    GlobalScope.launch {
                        var result: TemplateModuleLocal? =null
//                        val niimbotDrawData = NiimbotDrawData()
                        val templateJson = FlutterMethodInvokeManager.getTemplateDetail(templateId,needUpdateDb = true)
                        GlobalLoadingHelper.dismissLoading()
                        templateJson?.let {
                            result = TemplateModuleLocal.fromJson(it)
                        }
                        result?.let {
                            niimbotDrawData.niimbotTemplate= it
                            //商品模版不支持离线使用
                            if(!NetworkUtils.isConnected() && it.isGoodTemplate()){
                                showToast("app100001136")
                                return@launch
                            }
                            if(!NetworkUtils.isConnected() && it.isExcelTemplate()){
                                val hasExcelCache = FlutterMethodInvokeManager.hasExcelFileCache(it.getExcelHash())
                                if(!hasExcelCache){
                                    showToast("app01139")
                                    return@launch
                                }
                            }
                            //断网并且模版资源不完整，则toast提示网络异常
                            if(!NetworkUtils.isConnected() && !it.canEdit(isCheckBackground = true)){
                                showToast("app01139")
                                return@launch
                            }
                            if(it.isOldPCExcelTemplate()){
                                showToast("app01393")
                                return@launch
                            }
                        }
                        if(result == null){
                            return@launch
                        }
                        withContext(Dispatchers.Main){
                            TemplateSyncLocalUtils.downloadTemplateFonts(requireActivity(),result)
                            //模版版本号校验
                            var templateVersion = niimbotDrawData.niimbotTemplate.templateVersion
                            var isOldTemplate = false
                            var isOpen = false
                            if (templateVersion.isNullOrEmpty()) {
                                isOldTemplate = true
                            } else {
                                var compareResult = VersionCompareUtils.compareVersion(
                                    templateVersion,
                                    TemplateVersionConst.getAppMaxSupportTemplateVersion()
                                )
                                if (compareResult <= 0) {
                                    isOpen = true
                                }
                            }

                            if (!isOldTemplate && !isOpen) {
                                showToast("app100000343")
                                return@withContext
                            }
                            //是否存在实时时间
                            var isRealTime=false;
                            result!!.elements.forEach {
                                if(it is TimeItemModule){
                                    if(it.dateIsRefresh==1){
                                        isRealTime=true
                                    }
                                }
                            }
                            if(isRealTime && !VipHelper.isCurrentUserVip()){
                                if (!com.blankj.utilcode.util.NetworkUtils.isConnected()) {
                                    showToast("app100000625")
                                } else {
                                    NiimbotGlobal.gotoFlutterPage("vipTrial",hashMapOf("vipTrialCode" to "INSTANT_TIME_PRINT"))
                                }

                            }else{
                                if (VipHelper.isCurrentUserVip() || !checkTemplateVip(result!!)) {
                                    SyncTemplatePresenter.startPrintActivity(
                                        requireActivity(),
                                        niimbotDrawData,
                                        needRfidTemplate = false
                                    )
                                } else {
                                    VipHelper.showNeedVipDialog(requireActivity() as AppCompatActivity, eventCode = "012_082_106", "012"){
                                        if(it){
                                            SyncTemplatePresenter.startPrintActivity(
                                                requireActivity(),
                                                niimbotDrawData,
                                                needRfidTemplate = false
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

        mAdapter.setOnFootItemClickListener { _, _ ->
            binding.llMyTemplate.performClick()
        }

        binding.tvLogin.setOnNotDoubleClickListener {
            NiimbotGlobal.gotoFlutterPage("login")
//            LeMessageManager.getInstance().dispatchMessage(
//                LeMessage(
//                    LeMessageIds.MSG_ACTION_GO_ACTIVITY,
//                    LoginActivityConfig(
//                        activity
//                    )
//                )
//            )
        }

        binding.tvLabelBuy.setOnNotDoubleClickListener {
            val map = binding.tvLabelBuy.tag as? HashMap<String, Any>
            var url = map!!["button_link"] as String
//            var goods_sku_id = map["goods_sku_id"] as String
            var button_type = map["button_type"] as Int
            if (url.isNullOrEmpty()) {
                url = ShopManager.shopHome + "?jumpSource=$SHOP_SOURCE_MAIN_RFID"
            }
            else {
//                if (url.contains("?")) {
//                    url += "&jumpSource=$SHOP_SOURCE_MAIN_RFID"
//                } else {
//                    url += "?jumpSource=$SHOP_SOURCE_MAIN_RFID"
//                }
            }
            activity?.let { mActivity ->
                BuriedHelper.trackEvent(
                    "click", "003_147_150", hashMapOf(
                        Pair(
                            "machine_id",
                            RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""
                        ),
                        Pair("sku_code", shopCode),
//                        Pair("goods_sku_id", goods_sku_id),
                        Pair("button_type", button_type)
                    )
                )
                LoginDataEnum.loginCheck(mActivity as AppCompatActivity) {
                    NiimbotGlobal.gotoShopWeb(
                        mActivity,
                        url
                    )
                }
            }
        }

        initHandbookListener()

        binding.tvWifiCode?.setOnNotDoubleClickListener {
            activity?.let { mActivity ->
                BuriedHelper.trackEvent(
                    "click",
                    "003_148_171",
                    hashMapOf(Pair("tag_id", AppDataUtil.rfidTemplateModule?.id ?: ""))
                )
                LoginDataEnum.loginCheck(mActivity as AppCompatActivity) {
                    var params = JSONObject()
                    params.put("isManualChangeLabel", true)
                    params.put("labelId", RFIDConnectionProxyManager.rfidTemplateModuleLocal?.id)
                    params.put(
                        "labelName",
                        RFIDConnectionProxyManager.rfidTemplateModuleLocal?.name
                    )
                    NiimbotGlobal.gotoWifiCodeApp(params.toString())
                }
            }
        }

        com.blankj.utilcode.util.LogUtils.e("initEvent耗时： ${System.currentTimeMillis() - start}")
    }

    private fun checkTemplateVip(templateLocal: TemplateModuleLocal): Boolean {
        return templateLocal.hasVIPRes || templateLocal.vip
//        return try{
//            templateLocal.needVip()
//                || templateLocal.elements.any {
//                (it is PictureItemModule && (LogoHelper.isLogoVip(it.materialId)|| LogoHelper.isBorderVip(it.materialId))) ||
//                    (it is TextItemModule && FontUtils.isFontVip(it.fontCode))
//            }
//        } catch(e: Exception) {
//            e.printStackTrace()
//            false
//        }
    }

    private var isProcessingRfidTemplateJump = false
    private fun clickRfidTemplate() {
        if(isProcessingRfidTemplateJump){
            return
        }
        isProcessingRfidTemplateJump = true
        BuriedHelper.trackEvent("click", "003_148_151")
        if (null != AppDataUtil.rfidTemplateModule) {
            AppDataUtil.rfidTemplateModule?.let { templateModule ->
                val templateId = templateModule.id
                val isLabel = templateModule.isLabel
                val printModule = PrintModule.fromJson(templateModule.json)
                if (printModule != null && activity != null) {
                   var router=""
                    var entry=  PreferencesUtils.getString("cableEntry", default = "cableCanvas")
                    if(entry.equals("cableCanvas")&& TextHookUtil.getInstance().isSimpleChinese()){
                        router = "cableCanvas"
                    }else{
                        router = "canvas"
                    }
           if(templateModule.layoutSchema.isNullOrEmpty() || templateModule.supportedEditors.firstOrNull() !="cable" || router == "canvas"){
               val paramsMap = HashMap<String, Any>()
               val data = any2Json(templateModule.toTemplateModuleLocal())
               paramsMap["jsonData"] = data
               paramsMap["shopSource"] = "print_020"
               NiimbotGlobal.gotoFlutterPage("labelDetailPage", paramsMap)
               isProcessingRfidTemplateJump = false
           }else{
                  GlobalScope.launch {
                    TemplateSyncLocalUtils.downloadTemplateResource(ActivityUtils.getTopActivity(), templateModule) {
                        fontResult,templateModule2 ->
                        if(templateModule2 == null){
                            return@downloadTemplateResource
                        }
                        GlobalScope.launch {
                            val niimbotDrawData = NiimbotDrawData()
                            niimbotDrawData.apply {
                                this.niimbotTemplate =
                                    templateModule2.toTemplateModuleLocal() ?: TemplateModuleLocal()
                                this.setNiimbotGoodsInfo()
                                this.niimbotTemplate.name = LanguageUtil.findLanguageString("app100000728")
                            }
                            if (!niimbotDrawData.niimbotTemplate.canEdit() && !NetworkUtils.isConnected()) {
                                com.niimbot.appframework_library.utils.showToast("app01139")
                                isProcessingRfidTemplateJump = false
                                return@launch
                            }
                            withContext(Dispatchers.Main) {
                                SyncTemplatePresenter.startNiimbotDrawActivity(
                                    ActivityUtils.getTopActivity(),
                                    niimbotDrawData,
                                    needRfidTemplate = false,
                                    isRetainActivity = true,
                                    fromIndustry = false,
                                    needDownloadFonts = false,
                                    isCable=true
                                )
                                isProcessingRfidTemplateJump = false
                            }
                        }

                    }
                }

           }

                }
            }
        }else{
            isProcessingRfidTemplateJump = false
        }
    }


    override fun onDestroyView() {
        super.onDestroyView()
        LoginDataEnum.unregisterChange(this)
        EventBusUtils.unregister(this)
    }

    override fun onLoginStatusChange() {
        LogUtils.e("============onLoginStatusChange: ${LoginDataEnum.isLogin}")
        mAdapter.clear()
        //先从本地数据库取数据显示-加快显示速度
        getData(fromServer = false){
            getData()
        }
//        updatePrinterInfo(JCConnectionManager.getInstance().isConnection(), true)
        freshMoreText()
        Log.i("NpsUtils", "onLoginStatusChange ${NpsUtils.url}")
        if (!TextUtils.isEmpty(NpsUtils.url)) {
            NpsUtils.webView?.reload()
        }
        getUserSystemConfig()
        if(LoginDataEnum.isLogin && isConnected()){
            PushBindUtil.bindDevice()
        }
    }


    fun onNetChange(isNet: Boolean, msg: String) {
        if (isConnected()) {
            updatePrinterInfo(JCConnectionManager.getInstance().isConnected())
            getData()
//            TemplateSyncLocalUtils.syncPrintHistory()
            StreamerBusiness.requestStreamerIfNecessary {
                if (it != null) {
                    handleStreamer(it)
                }
            }
        }
        showNetError(NetworkUtils.isConnected(), NetworkUtils.getErrorMsg())
    }

    private fun showNetError(isConnect: Boolean, msg: String = "") {
        binding.errorNotificationTv?.visible(!isConnect)
        binding.errorNotificationTv?.text = NetworkUtils.getErrorMsg()
    }

    /**
     * 语言切换，重新获取nps数据
     */
    fun languageChange(languageCode: String) {
        if (!isInit) {
            return
        }
        getNpsData(languageCode)
        if(LoginDataEnum.isLogin && isConnected()){
            PushBindUtil.bindDevice()
        }
        getVipNpsData(languageCode)
    }

    /**
     * 根据当前语言获取nps数据
     */
    fun getNpsData(languageCode: String) {
        npsUrl = NpsUtils.constructNpsUrl(languageCode)
        activity?.let { NpsUtils.reloadNpsUrl(npsUrl!!) }
    }

    fun getVipNpsData(languageCode: String) {
        var vipNpsUrl = NpsVipUtil.constructNpsUrl(languageCode)
        activity?.let { NpsVipUtil.reloadNpsUrl(vipNpsUrl!!) }
    }

    fun openNpsPage(languageCode: String) {
//        if (npsData.isNullOrEmpty()) {
//            FlutterMethodInvokeManager.getNpsDataFromFlutter(languageCode) { npsResult ->
//                npsResult?.let {
//                    npsData = npsResult
//                    NpsUtils.reloadNpsUrl(npsData!!["url"] as String)
//                    activity?.let { NpsUtils.showNpsDialog(it, npsData!!["url"] as String, "home") }
//                }
//
//            }
//        } else {
//            if(!NpsUtils.webViewLoadSuccess){
//                NpsUtils.reloadNpsUrl(npsData!!["url"] as String)
//            }
//            activity?.let { NpsUtils.showNpsDialog(it, npsData!!["url"] as String, "home") }
//        }

        if (npsUrl.isNullOrEmpty()) {
            npsUrl = NpsUtils.constructNpsUrl(languageCode)
            activity?.let {
                NpsUtils.reloadNpsUrl(npsUrl!!)
                NpsUtils.showNpsDialog(it, npsUrl!!, "home")
            }

        } else {
            if (!NpsUtils.webViewLoadSuccess) {
                NpsUtils.reloadNpsUrl(npsUrl!!)
            }
            activity?.let { NpsUtils.showNpsDialog(it, npsUrl!!, "home") }
        }
        BuriedHelper.trackEvent(
            "click",
            "112_226",
            hashMapOf(
                Pair("source", "1"),
            )
        )

    }

    /**
     * 断开蓝牙连接的时候重置ui
     */
    fun disConnectReInitUi(isConnect: Boolean) {
        changeDeviceConnectStatus(isConnect)
        initElectricity(isConnect)
        hideCardInfo()
    }

    fun updatePrinterInfo(
        isDeviceConnected: Boolean,
        fromLoginChanged: Boolean = false,
        fromLocal: Boolean = false,
        isFromRfidRead: Boolean = false
    ) {
        if (isFromRfidRead) {
            LogUtils.iTag(
                "ConnectRfidRead", "HomeFragment updatePrinterInfo, " +
                    "isDeviceConnected = $isDeviceConnected, fromLoginChanged = $fromLoginChanged, fromLocal = $fromLocal"
            )
        }
        LogUtils.e("============首页RFID updatePrinterInfo isConnect = $isDeviceConnected , ${RFIDConnectionProxyManager.isSupportCard()}")
        changeConnectState()
        if (!isDeviceConnected || !RFIDConnectionProxyManager.checkDeviceSupportRFID()
            || DeviceC1Helper.isC1Page || RFIDConnectionProxyManager.connectedDevice?.isC1() == true) {
            hideCardInfo()
            return
        }

        val series =
            DevicesSeriesLocalUtils.getCurrentSerialInfo(
                RFIDConnectionProxyManager.connectedDevice?.deviceName
                    ?: ""
            )
        if (RFIDConnectionProxyManager.isSupportCard()) {
            binding.rfidGroup?.visibility = View.GONE
            showCardInfo()
            RFIDConnectionProxyManager.initRFID {
                MainScope().launch {
                    showCardInfo()
                }
            }
            return
        }
        if (!RFIDConnectionProxyManager.isSupportCard() && AppDataUtil.coverIsOpen) {
            showCovers(AppDataUtil.coverIsOpen)
        } else {
            if (RFIDConnectionProxyManager.checkDeviceSupportRFID()) {
                showRfidInfo(true)
                updateRfidInfo(
                    isDeviceConnected,
                    fromLoginChanged,
                    fromLocal,
                    isFromRfidRead = isFromRfidRead
                )
            } else {
                AppDataUtil.rfidTemplateModule = null
                binding.rfidLayout?.visibility = View.VISIBLE
            }
        }
    }

    private fun initElectricity(isConnect: Boolean) {
        logE("initElectricity", "isConnect: $isConnect")
        if (!isInit) {
            return
        }
        if (!isConnect) {
            return
        }
        var electricity = PreferencesUtils.getInt(CURRENT_DEVICE_ELECTRICIRY, -1)
        if (electricity > 0) {
            changeElectricity(electricity)
        }
    }

    /**
     * 蓝牙连接状况
     */
    override fun changeDeviceConnectStatus(isConnect: Boolean) {
        logE("changeDeviceConnectStatus", "$isConnect")
        if (!isInit) return
//        getRfidData("03061902")
//        return
        changeConnectIcon(if (isConnect) R.drawable.ic_device_connect else R.drawable.ic_device_disconnect)
        var deviceName = if (isConnect) {
            getSerialName()
        } else {
            LanguageUtil.findLanguageString("app00190", context)
        }
        logE("changeDeviceConnectStatus", "$isConnect   $deviceName")
        if (deviceName.isNullOrEmpty()) {
            deviceName = "app00190"
            changeConnectIcon(R.drawable.ic_device_disconnect)
        }
        binding.tvConnect?.text = deviceName
        binding.tvTitleConnect?.text = deviceName

        if (isConnect) {
            changeConnectIcon(R.drawable.ic_device_connect)
            if (!RFIDConnectionProxyManager.checkDeviceSupportRFID()) binding.rfidLayout?.gone()
            if(AppDataUtil.coverIsOpen) {
                showCovers(true)
            }
        } else {
            changeConnectIcon(R.drawable.ic_device_disconnect)
            hideCardInfo()
            binding.llRfidCover.gone()
        }
    }

    fun getSerialName(): String {
        val pDeviceInfo = RFIDConnectionProxyManager.connectedDevice
        if(pDeviceInfo != null) {
            val serialName = pDeviceInfo.getSerialName()
            if(!serialName.isNullOrEmpty()) {
                return serialName
            }
        }
        var deviceName = pDeviceInfo?.deviceName ?: JCConnectionManager.getInstance().getConnectionDeviceName()
        if (deviceName.isNullOrEmpty()) {
            deviceName = PreferencesUtils.getString(CONNECTED_DEVICE_NAME)
        }
        return BluetoothUtil.getDeviceType(deviceName)
    }

    fun changeElectricity(electricity: Int) {
//        var result = Math.round(electricity.toDouble() / 10).toInt() * 10
//        if (result < 10) {
//            result = 10
//        }
//        when (result) {
//            10 -> changeConnectIcon(R.drawable.electricity_1)
//            20 -> changeConnectIcon(R.drawable.electricity_2)
//            30 -> changeConnectIcon(R.drawable.electricity_3)
//            40 -> changeConnectIcon(R.drawable.electricity_4)
//            50 -> changeConnectIcon(R.drawable.electricity_5)
//            60 -> changeConnectIcon(R.drawable.electricity_6)
//            70 -> changeConnectIcon(R.drawable.electricity_7)
//            80 -> changeConnectIcon(R.drawable.electricity_8)
//            90 -> changeConnectIcon(R.drawable.electricity_9)
//            100 -> changeConnectIcon(R.drawable.electricity_10)
//        }
    }

    private fun changeConnectIcon(icon: Int) {
        if (!isInit) return
        val drawableStart = resources.getDrawable(icon)
        if (LocaleUtils.isRtl(requireContext())) {
            drawableStart.setBounds(0, 0, AppUtils.dip2px(3), 0)
            binding.tvConnect.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                drawableStart,
                null
            )
            binding.tvTitleConnect.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                drawableStart,
                null
            )
        } else {
            drawableStart.setBounds(AppUtils.dip2px(3), 0, 0, 0)
            binding.tvConnect.setCompoundDrawablesWithIntrinsicBounds(
                drawableStart,
                null,
                null,
                null
            )
            binding.tvTitleConnect.setCompoundDrawablesWithIntrinsicBounds(
                drawableStart,
                null,
                null,
                null
            )
        }
    }

    fun showCovers(isOpen: Boolean) {
        if (isOpen) {
            AppDataUtil?.rfidTemplateModule = null
            binding.llRfidCover?.visible()
            binding.llEmptyRfid?.gone()
            binding.llRfidCard?.gone()
            binding.rfidGroup?.gone()
            binding.llConnnect?.gone()

            binding.rfidLayout?.visibility = View.VISIBLE
            binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))
        } else {
            binding.llRfidCover.visible()
        }
    }

    private var animator: ObjectAnimator? = null
    private fun showRfidAnimate(viewHeight: Int) {
        if (!checkLifecycle()) {
            binding.rfidLayout.heightChanged(viewHeight)
            return
        }
        if (null == animator) {
            val alphaProper = PropertyValuesHolder.ofFloat("alpha", 0.5f, 1f)
            val scaleXProper = PropertyValuesHolder.ofFloat("scaleX", 0.5f, 1f)
            val scaleYProper = PropertyValuesHolder.ofFloat("scaleY", 0f, 1f)
            animator = ObjectAnimator.ofPropertyValuesHolder(
                binding.rfidLayout,
                alphaProper,
                scaleXProper,
                scaleYProper
            )
            animator?.addUpdateListener { animation ->
                val value = animation.getAnimatedValue("scaleY") as Float
                if (checkLifecycle()) {
                    binding.rfidLayout.heightChanged((viewHeight * value).toInt())
                }
            }
            animator?.duration = 400
        }
        animator?.start()
    }

    private fun checkLifecycle() = viewLifecycleOwner!= null && viewLifecycleOwner.lifecycle.currentState.isAtLeast(Lifecycle.State.STARTED)

    /**
     * 蓝牙关闭，隐藏rfid view 和重置rfid 保存数据
     */
    fun showCoversNew(isCard: Boolean) {
//        AppDataUtil.rfidTemplateModule = null
        binding.llRfidCover.visibility = View.GONE
        showRfidInfo(true, isCard)
    }

    fun showCardInfo() {
        LogUtils.d("============RFID showCardInfo()===============")
        if (RFIDConnectionProxyManager.sdkHasRFIDLabel) {
            val code = RFIDConnectionProxyManager.getRFIDLabelInfo()?.one_code
            LogUtils.d("============RFID showCardInfo()===============code = $code")
            if (code.isNullOrEmpty()) {
                showRfidInfo(isEmptyRfid = true, isCard = true)
            } else {
                var showLoading = false
                val activity = FlutterBoost.instance().currentActivity()
                if (activity != null && activity is MainActivity) {
                    showLoading = true
                }
                if (showLoading) {
                    showLoading()
                }
                showCardRfidInfo()
                if (showLoading) {
                    dismissLoading()
                }
            }
        } else {
            showRfidInfo(isEmptyRfid = true, isCard = true)
        }
    }

    fun hideCardInfo() {
        AppDataUtil.rfidTemplateModule = null
        binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))
        binding.ivLabelBg?.viewPager?.removeAllViews()

        if (ConnectionProxyManager.connectState) {
            binding.rfidLayout?.gone()
        } else {
            binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))
            binding.rfidLayout?.visible()
            binding.llConnnect?.visible()
            binding.llRfidCard?.gone()
            binding.rfidGroup?.gone()
            binding.llRfidCover?.gone()
            binding.llEmptyRfid?.gone()
        }
    }

    private fun updateRfidInfo(
        isDeviceConnected: Boolean,
        fromLoginChanged: Boolean = false,
        fromLocal: Boolean = false,
        isFromRfidRead: Boolean = false
    ) {
        if (isFromRfidRead) {
            LogUtils.iTag(
                "ConnectRfidRead", "HomeFragment updateRfidInfo, " +
                    "isDeviceConnected = $isDeviceConnected, fromLoginChanged = $fromLoginChanged, fromLocal = $fromLocal"
            )
        }
        //网络连接 和 蓝牙连接
        logI(
            "updateRfidInfo",
            "${isConnected()}  $isDeviceConnected  ${RFIDConnectionProxyManager.checkDeviceSupportRFID()}"
        )
        if (isDeviceConnected && RFIDConnectionProxyManager.checkDeviceSupportRFID()) {
            logI("updateRfidInfo", "${RFIDConnectionProxyManager.sdkHasRFIDLabel}")
            if (RFIDConnectionProxyManager.sdkHasRFIDLabel) {
                val barcode = RFIDConnectionProxyManager.getRFIDLabelInfo()?.one_code
                if (barcode.isNullOrEmpty()) {
                    showRfidInfo(true)
                } else {
                    //退出登录的时候不需要调用rfid云模板查询接口
                    if (fromLoginChanged && !LoginDataEnum.isLogin && binding.rfidGroup?.visibility == View.VISIBLE) {
//                        binding.binding.tvLabelHistory?.visibility = View.GONE
                    } else {
                        getRfidData(barcode, fromLocal, isFromRfidRead = isFromRfidRead)
                    }

                }
            } else {
                showRfidInfo(true)
            }
            BuriedHelper.trackEvent("view", "003_146", hashMapOf(
                Pair("machine_id", RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""),
                Pair("type", if (RFIDConnectionProxyManager.sdkHasRFIDLabel) 1 else 2)
            ))
        } else {
            binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))
            AppDataUtil.rfidTemplateModule = null
            binding.rfidLayout?.visibility = View.GONE
        }
    }

    @Volatile
    private var loadingList = false

    /**
     * 获取我的模板
     */
    private fun getData(fromServer: Boolean = true,onComplete: (() -> Unit)? = null) {
        if (loadingList) return
        loadingList = true
        MainTemplateLocalUtils.getModule(
            fromServer && isConnected(),
            mData.isNullOrEmpty(),
            1,
            10,
            this
        ) {
            loadingList = false
            binding.srlRefresh?.finishRefresh()
            if (null != it) {
                mAdapter.updateList(it.list)
                LogUtils.e("本地模板总数：${it.total}")
            } else {
                showToast("app01160")
            }

            changeDateUI(mData.size > 0, "getData.fail")
            onComplete?.invoke()
        }
    }


    private fun getRfidData(
        code: String,
        fromLocal: Boolean = false,
        isFromRfidRead: Boolean = false
    ) {
        if (isFromRfidRead) {
            LogUtils.iTag(
                "ConnectRfidRead",
                "HomeFragment getRfidData, code = $code, fromLocal = $fromLocal"
            )
        }
        var showLoading = false
        val activity = FlutterBoost.instance().currentActivity()
        if (activity != null && activity is MainActivity) {
            showLoading = true
        }
        if (showLoading) {
            showLoading()
        }
        // 2020/11/13 Ice_Liu 将查询rfid的接口换成扫码取模的接口，以便完成将RFID识别的模板加入私人模板的需求
        val listener: (Boolean, TemplateModule?, errorMsg: String?) -> Unit = { result, it, _ ->
            try{
                LogUtils.iTag(
                    "ConnectRfidRead",
                    "HomeFragment getRfidData result callback, result = $result, it = $it"
                )
                if (showLoading) {
                    dismissLoading()
                }
                AppDataUtil.rfidTemplateModule = null
                RFIDConnectionProxyManager.rfidTemplateModuleLocal = null
                if (!result || it == null) {
                    showRfidInfo(true)
                } else {
                    if (it.one_code == "-1") {
                        showRfidInfo(true)
                        //显示自消失弹框
                        if (activity != null && !activity?.isFinishing!!) {
                            (activity as MainActivity).showAutoCloseLoadingWithText(
                                tips = LanguageUtil.findLanguageString(
                                    "app00851",
                                    activity,
                                    true
                                )
                            )
                        }
                    } else {
                        AppDataUtil.rfidTemplateModule = it
                        RFIDConnectionProxyManager.rfidTemplateModuleLocal = it.toTemplateModuleLocal()
                        EventBus.getDefault().post(ServerRfidGetEvent())
                        checkWifiLabel()
                        checkDangerLabel()
                        binding.tvLabelWarn?.visible(!RFIDConnectionProxyManager.ifPaperMatch(requireActivity()))
                        binding.tvLabelWarn?.text = "app01576"
                        binding.tvLabelName?.text =
                            it.getLabelName(TextHookUtil.getInstance().languageName)//"${it.name}(${it.width.toInt()}*${it.height.toInt()})"
                        binding.tvLabelName?.visible()
                        binding.tvLabelIllegal?.gone()
                        val thumbFile = File(it.local_thumb)
                        binding.ivLabelBg?.let { imageView ->
                            showBannerForRfidTemplate(it)
                            showRfidInfo(false)
                        }

                        EventBus.getDefault().post(GetRFIDEvent())
                        RFIDConnectionProxyManager.rfidTemplateModuleLocal?.updateLabelId()
                        val labelMap = JSONObject()
                        labelMap.put("action", "setFlutterlabelData")
                        labelMap.put(
                            "labelData",
                            any2Json(RFIDConnectionProxyManager.rfidTemplateModuleLocal)
                        )

                        EventBus.getDefault().post(
                            any2Json(
                                labelMap
                            )
                        )

                        CapAppHelper.sendLabelChangeEvent(it.id, it.name)
                        checkShopStatusNew()
                    }
                }
            } catch(e: Exception) {e.printStackTrace()}
        }
        if (isConnected()) {
            TemplateSyncLocalUtils.getCloudTemplateByScanCode(
                oneCode = code,
                getDetail = true,
                fromLocal = fromLocal,
                insertDB = true,
                listener = { res, template, errorMsg ->
                    if(!res || template == null) {
                        TemplateSyncLocalUtils.queryRfidTemplateByScanCode(oneCode = code,listener=listener)
                    }
                    else{
                        listener.invoke(true, template!!, errorMsg)
                    }
                }
            )
        } else {
            TemplateSyncLocalUtils.queryRfidTemplateByScanCode(oneCode = code,listener=listener)
        }
    }

    var shopCode = ""
    private fun checkShopStatus() {
        var t = RFIDConnectionProxyManager.rfidTemplateModuleLocal
        if (ShopManager.shopType != ShopType.None && null != t) {
            val oneCodeStr = TemplateUtils.getTemplateCode(t)
            if (!TextUtils.isEmpty(oneCodeStr)) {
                TemplateShopStatusUtils.getShopProductInfo(oneCodeStr) { shopProduct ->
                    shopProduct?.let {
                        var isShow = it.checkValid()
                        shopCode = it.oneCode!!
                        binding.tvLabelBuy?.visibility = if (isShow) {
                            View.VISIBLE
                        } else {
                            View.GONE
                        }
                        if (isShow) {
                            binding.tvShopGoodsState.text = it.getHomePageDisplayButtonName()
                            binding.tvLabelBuy.tag =  hashMapOf(
                                Pair("goods_sku_id", it.goodsSku ?: ""),
                                Pair("button_type", it.buttonType ?: 1),
                                Pair("button_link", it.buttonLink ?: "")
                            )
                            BuriedHelper.trackEvent(
                                "show", "003_147_150", hashMapOf(
                                    Pair("goods_sku_id", it.goodsSku ?: ""),
                                    Pair("button_type", it.buttonType ?: 1)
                                )
                            )
                        }
                    } ?: kotlin.run {
                        hideLabelBuyBtn()
                    }
                }
            } else {
                hideLabelBuyBtn()
            }
        } else {
            hideLabelBuyBtn()
        }
    }

    private fun checkShopStatusNew(){
        var t = RFIDConnectionProxyManager.rfidTemplateModuleLocal
        if (ShopManager.shopType == ShopType.None || null == t){
            hideLabelBuyBtn()
            return
        }
        val oneCodeStr = TemplateUtils.getTemplateCode(t)
        if(TextUtils.isEmpty(oneCodeStr)){
            hideLabelBuyBtn()
            return
        }
        val templateModule = TemplateModuleTransform.templateModuleLocalToTemplateModule(t)
        val shopProductInfo = TemplateShopStatusUtils.getShopProductInfoByTemplate(oneCodeStr, templateModule, SHOP_SOURCE_MAIN_RFID)
        shopCode = shopProductInfo.oneCode!!
        binding.tvLabelBuy?.visibility = View.VISIBLE
        binding.tvShopGoodsState.text = "app01165"
        binding.tvLabelBuy.tag =  hashMapOf(
            Pair("button_type", shopProductInfo.buttonType ?: 1),
            Pair("button_link", shopProductInfo.buttonLink ?: "")
        )
        BuriedHelper.trackEvent(
            "show", "003_147_150", hashMapOf(
                Pair("button_type", shopProductInfo.buttonType ?: 1)
            )
        )
    }

    private fun checkWifiLabel() {
        var wifiLabelId = PreferencesUtils.getString("tag_wifi_code_label", "")
        var t = RFIDConnectionProxyManager.rfidTemplateModuleLocal
        var isSimpleChinese = TextHookUtil.getInstance().isSimpleChinese()
        binding.tvWifiCode?.visible(null != t && wifiLabelId.contains(t.id) && isSimpleChinese)
    }
    private fun checkDangerLabel() {
        var t = RFIDConnectionProxyManager.rfidTemplateModuleLocal
        logI("HomeFragment","checkDangerLable supportEditor=${t?.supportedEditors} layoutschema=${t?.layoutSchema} ")
    }

    private fun hideLabelBuyBtn() {
        binding.tvLabelBuy?.visibility = View.GONE
        shopCode = ""
    }

//    private fun showLoading() {
//        if (activity != null && !activity?.isFinishing!!) {
//            (activity as MainActivity).showAutoCloseLoadingWithText(
//                30000,
//                LanguageUtil.findLanguageString(
//                    "app00872",
//                    activity,
//                    true
//                )
//            )
//        }
//    }


//    private fun dismissLoading(delay: Long = 1000L) {
//        GlobalScope.launch(Dispatchers.Main) {
//            delay(delay)
//            if (activity != null && (!activity?.isFinishing!!)) {
//                (activity as MainActivity).dismissLoading()
//            }
//        }
//    }

    /**
     * 显示rfid标签信息
     */
    private fun showRfidInfo(isEmptyRfid: Boolean, isCard: Boolean = false) {
        if (isEmptyRfid) {
            //如果rfid标签信息为空
            AppDataUtil.rfidTemplateModule = null
        }
        if (animator?.isRunning == true) return
        dismissLoading()
        binding.rfidLayout?.visibility = View.VISIBLE
        binding.llConnnect?.gone()
        binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))
        if (isEmptyRfid) {
            if (RFIDConnectionProxyManager.isShowTemplate()) {
                binding.rfidLayout?.visibility = View.GONE
            } else {
                binding.llRfidCard?.visibility = View.GONE
                binding.rfidGroup?.visibility = View.GONE
                binding.llRfidCover?.visibility = View.GONE

                binding.llEmptyRfid?.visibility = View.VISIBLE
                binding.tvEmptyRfid?.text = if (isCard) "app01244" else "app100000052"
//                BuriedHelper.trackEvent("view", "003_146", hashMapOf(
//                    Pair("machine_id", RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""),
//                    Pair("type", 2)
//                ))
            }
        } else {
            showPaperRfidInfo()
        }
    }

    /**
     * 标牌机 碳带纸 rfid信息显示
     */
    private fun showCardRfidInfo() {

//        BuriedHelper.trackEvent("view", "003_146", hashMapOf(
//            Pair("machine_id", RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""),
//            Pair("type", 1)
//        ))

        binding.rfidLayout?.visibility = View.VISIBLE
        binding.llRfidCard?.visibility = View.VISIBLE

        binding.llRfidCover?.gone()
        binding.llEmptyRfid?.gone()
        binding.rfidGroup?.gone()
        binding.llConnnect?.gone()
        binding.rfidLayout?.heightChanged(SizeUtils.dp2px(118f))

        //todo 显示碳带余量

//        var tip = LanguageUtil.findLanguageString("app00991", context)
//        binding.tvEmptyRfid.text = "$tip${RfidPrintUtil.getCardLength()}"
        val result =
            1 - (RFIDConnectionProxyManager.getRFIDLabelInfo()
                ?.let { it.print_number / it.allow_number } ?: 0f)
        val preString = LanguageUtil.findLanguageString("app100000053", context)
        binding.tvRfidCard?.apply {
            when {
                result < 0.25 -> {
//                    ivRfidCard.setImageResource(R.drawable.ic_ttr_level_d)
                    val sufString = LanguageUtil.findLanguageString("app01226", context)
                    val sourceStr = "$preString:$sufString"
                    TextSpannableUtil.setColorSpannable(
                        binding.tvRfidCard,
                        sourceStr,
                        Color.parseColor("#FF7C1E"),
                        preString.length + 1,
                        sourceStr.length
                    )
                }

                result < 0.5 -> {
//                    ivRfidCard.setImageResource(R.drawable.ic_ttr_level_c)
                    var sufString = LanguageUtil.findLanguageString("app01225", context)
                    var sourceStr = "$preString:$sufString"
                    TextSpannableUtil.setColorSpannable(
                        binding.tvRfidCard,
                        sourceStr,
                        Color.parseColor("#999999"),
                        preString.length + 1,
                        sourceStr.length
                    )
                }

                result < 0.75 -> {
//                    ivRfidCard.setImageResource(R.drawable.ic_ttr_level_b)
                    var sufString = LanguageUtil.findLanguageString("app01224", context)
                    var sourceStr = "$preString:$sufString"
                    TextSpannableUtil.setColorSpannable(
                        binding.tvRfidCard,
                        sourceStr,
                        Color.parseColor("#04BF53"),
                        preString.length + 1,
                        sourceStr.length
                    )
                }

                else -> {
//                    ivRfidCard.setImageResource(R.drawable.ic_ttr_level_a)
                    var sufString = LanguageUtil.findLanguageString("app01223", context)
                    var sourceStr = "$preString:$sufString"
                    TextSpannableUtil.setColorSpannable(
                        binding.tvRfidCard,
                        sourceStr,
                        Color.parseColor("#04BF53"),
                        preString.length + 1,
                        sourceStr.length
                    )
                }
            }
        }
    }

    private fun showBannerForRfidTemplate(t: TemplateModule) {
        binding.ivLabelBg.setAdapter { _, itemView, model, _ ->
            (itemView as ImageView).scaleType = ImageView.ScaleType.FIT_CENTER
            itemView.showImageWithNiimbot(
                model as String,
                phResId = R.drawable.bg_vip_error_logo_small,
                errorId = R.drawable.bg_vip_error_logo_small,withCache = false)
            itemView.setOnNotDoubleClickListener { clickRfidTemplate() }
        }

        val printModule = json2Any<PrintModule>(t.json) ?: return

        val path = printModule.backgrounds() as ArrayList<String>
        val localPath = printModule.localBackground
        val validLocalPath = localPath.filter { File(it).exists() }
        val useLocal = path.size == validLocalPath.size
        if (useLocal) {
            binding.ivLabelBg.setData(localPath, null)
        } else {
            binding.ivLabelBg.setData(path, null)
        }
        if (path.size > 1) {
            binding.ivLabelBg.setAutoPlayInterval(2000)
            binding.ivLabelBg.setAutoPlayAble(true)
            binding.ivLabelBg.startAutoPlay()
        } else {
            binding.ivLabelBg.setAutoPlayAble(false)
        }
    }


    /**
     * 普通标签纸 rfid信息显示
     */
    private fun showPaperRfidInfo() {
        binding.llEmptyRfid?.gone()
        binding.llRfidCard?.gone()
        binding.llConnnect?.gone()

        binding.rfidGroup?.visible()
        binding.rfidLayout?.visible()

        binding.ivLabelBg?.viewTreeObserver?.addOnPreDrawListener(object :
            ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                binding.ivLabelBg?.let {
                    binding.ivLabelBg?.viewTreeObserver?.removeOnPreDrawListener(this)
                    if (JCConnectionManager.getInstance()
                            .isConnected() && !AppDataUtil.coverIsOpen && AppDataUtil.rfidTemplateModule != null
                    ) {
                        binding.llRfidCover?.visibility = View.GONE
                        showRfidAnimate(SizeUtils.dp2px(230f))
                    }
                }
                return true
            }
        })

//        if (LoginDataEnum.isLogin) {
//            binding.binding.tvLabelHistory?.visibility = View.VISIBLE
//        } else {
//            binding.binding.tvLabelHistory?.visibility = View.GONE
//        }
    }


    private fun changeDateUI(hasData: Boolean, message: String) {
        if (hasData) {
            binding.llNoData?.visibility = View.GONE
            binding.srlRefresh?.visibility = View.VISIBLE
        } else {
            binding.llNoData?.visibility = View.VISIBLE
            //  binding.srlRefresh?.visibility = View.GONE
            if (LoginDataEnum.isLogin) {
                binding.srlRefresh?.visibility = View.VISIBLE
            } else {
                binding.srlRefresh?.visibility = View.GONE
            }
        }
        if (binding.llNoData?.visibility == View.VISIBLE) {
            if (com.niimbot.baselibrary.user.LoginDataEnum.isLogin) {
                binding.tvLogin?.visibility = View.GONE
            } else {
                binding.tvLogin?.visibility = View.VISIBLE
            }
        }
    }

    /**
     * binding.banner
     */
    private fun initBanner() {
        binding.banner?.setDelegate { _, _, model, _ ->
            if (model is BannerModule) {
                val url = model.url
                logI("initBanner", "binding.banner url: $url")
                BuriedHelper.trackEvent(
                    "click",
                    "003_081_001",
                    hashMapOf(
                        Pair("b_name", model.name),
                        Pair("b_binding.banner_key", model.id),
                        Pair("a_name", model.activityName ?: "")
                    )
                )
                if (!model.url.isNullOrEmpty()) {
                    activity?.let {
                        NiimbotGlobal.bannerRouter(
                            it,
                            model.url,
                            model.type_code,
                            model.name,
                            sourcePage = "003",
                            jumpSource = SHOP_SOURCE_MAIN_BANNER,
                            model.isCouldShare
                        )
                    }
                }
            }
        }
        binding.banner?.setAdapter { _, itemView, model, _ ->
            if (model !is BannerModule) return@setAdapter
            (itemView as ImageView).apply {
                itemView.scaleType = ImageView.ScaleType.FIT_XY
                if (!TextUtils.isEmpty(model.path)) {
//                    this.showRoundImage(url = model.path, radius = 12)
                    this.showImage(model.path)
                }
                this.clipRounded(SizeUtils.dp2px(12f))
            }
        }
        binding.banner?.setOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }

            override fun onPageSelected(position: Int) {
                try {
                    if (bannerModuleList != null && bannerModuleList!!.size <= position) return
                    if (isHidden || ActivityUtils.getTopActivity().javaClass.simpleName != "MainActivity") return
                    var bannerModule = bannerModuleList?.get(position)
                    bannerModule?.let {
                        BuriedHelper.trackEvent(
                            "show",
                            "003_081_001",
                            hashMapOf(
                                Pair("b_name", it.name),
                                Pair("b_banner_key", it.id),
                                Pair("a_name", it.activityName ?: "")
                            )
                        )
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }

            override fun onPageScrollStateChanged(state: Int) {
            }

        })
        getBannerData()
        binding.banner?.startAutoPlay()
    }

    private fun getBannerData() {
        BannerLocalUtils.getModule(true, this) {
            bannerModuleList = it
            refreshHomeBanner()
//            val filterBannerList =  bannerModuleList?.filter { banner -> AdManager.canPopupAd(AdType.HOME_BANNER,banner.id.toString(),banner.exhibitionFrequency) }
//            GlobalScope.launch(Dispatchers.Main) {
//                if (filterBannerList.isNullOrEmpty()) {
//                    binding.banner?.setAutoPlayAble(false)
//                    binding.banner?.visibility = View.GONE
//                } else {
//                    binding.banner?.setAutoPlayAble(filterBannerList.size > 1)
//                    binding.banner?.visibility = View.VISIBLE
//                    binding.banner?.setData(filterBannerList, null)
//                }
//            }
        }
    }

    private fun refreshHomeBanner() {
        if (bannerModuleList.isNullOrEmpty()) {
            binding.banner?.setAutoPlayAble(false)
            binding.banner?.visibility = View.GONE
            return
        }
        val filterBannerList = bannerModuleList?.filter { banner ->
            (banner.effectiveEndTime == 0L ||  banner.effectiveEndTime > System.currentTimeMillis()) &&
            AdManager.canPopupAd(
                AdType.HOME_BANNER,
                banner.id.toString(),
                banner.exhibitionFrequency
            )
        }
        GlobalScope.launch(Dispatchers.Main) {
            if (filterBannerList.isNullOrEmpty()) {
                binding.banner?.setAutoPlayAble(false)
                binding.banner?.visibility = View.GONE
            } else {
                binding.banner?.setAutoPlayAble(filterBannerList.size > 1)
                binding.banner?.visibility = View.VISIBLE
                binding.banner?.setData(filterBannerList, null)
                if(filterBannerList.size == 1){
                    val bannerModule = filterBannerList.first()
                    BuriedHelper.trackEvent(
                        "show",
                        "003_081_001",
                        hashMapOf(
                            Pair("b_name", bannerModule.name),
                            Pair("b_banner_key", bannerModule.id),
                            Pair("a_name", bannerModule.activityName ?: "")
                        )
                    )
                }
            }
        }
    }

    /**
     * 扫一扫
     */
    private fun getTemplateByScanCode() {
        PermissionDialogUtils.showCameraPermissionDialog(
            context,
            RequestCode.MORE,
            object :
                com.niimbot.appframework_library.common.util.permission.XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    activity?.apply {
                        LeMessageManager.getInstance().dispatchMessage(
                            LeMessage
                                (
                                LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                                NewScanActivityConfig(activity).apply {
                                    this.intent.putExtra("trackSource", 1)
                                }
                            )
                        )
                    }
                }

                override fun onPermissionDenied(
                    deniedPermissions: Array<String>,
                    alwaysDenied: Boolean
                ) {
                    com.niimbot.appframework_library.utils.showToast("app01309")
                }
            })
    }

    private fun deleteSync() {
//        val size = MainTemplateLocalUtils.getLocalSize()
        MainTemplateLocalUtils.getModule(/*(size < 10) &&*/ isConnected(), false, 1, 10, this) {
            it?.apply {
                changeDateUI(it.list.size > 0, "deleteSync")
//                mData.clear()
//                mData.addAll(it.list)
//                mAdapter.notifyDataSetChanged()
                mAdapter.updateList(it.list)
            } ?: getData()
        }
    }


    private fun initHandbookListener() {
        binding.tvHandbook?.setOnNotDoubleClickListener {
            startHandbookAction()
        }

        binding.tvTitleHandbook.setOnNotDoubleClickListener {
            startHandbookAction()
        }
    }


    private fun startHandbookAction() {
        BuriedHelper.trackEvent("click", "003_003_008")
        if (!NetworkUtils.isConnected()) {
            showToast("app01139")
            return
        }
        ABTestUtils.getGrayModuleInfo(GrayModule.HELP_CENTER) { canEnterGray ->
            var url = ""
            var showTitleBar = true
            if (canEnterGray) {
                url = JCApi.HELP_CENTER_NEW
                showTitleBar = false
            } else {
                url = JCApi.HELP_CENTER
                showTitleBar = true
            }
            activity?.let { NiimbotGlobal.routingByScheme(it, url, "", catchBackEvent = true, showTitleBar = showTitleBar) }
        }


    }

    private var isVisibleToUser: Boolean = false
    private fun isBluetoothConnected() = JCConnectionManager.getInstance().isConnected()
    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        this.isVisibleToUser = hidden
        if (isVisible && isResumed) {
            updatePrinterInfo(JCConnectionManager.getInstance().isConnected(), fromLocal = true)
        }
        if (!isHidden) {
            BuriedHelper.trackEvent("view", "003")
        }
        if (!isVisibleToUser && isResumed) {
            getBannerData()
        }
    }


    fun changeConnectState() {
        changeDeviceConnectStatus(isBluetoothConnected())
        if (isBluetoothConnected()) {
            val electricity = PreferencesUtils.getInt(CURRENT_DEVICE_ELECTRICIRY, -1)
            if (electricity > 0) {
                changeElectricity(electricity)
            } else {
                initElectricity(isBluetoothConnected())
            }
        }
    }

    override fun onResume() {
        super.onResume()
        //        changeDeviceConnectStatus(isBluetoothConnected())
        changeConnectState()
        freshNps()
        refreshHomeBanner()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTemplateEvent(event: TemplateEvent) {
        LogUtils.e("HomeFragment  onTemplateEvent--->${event.eventType}")
        when (event.eventType) {
            TemplateEventType.CREATE_PERSONAL_TEMPLATE_SYNC -> getData(false)
            TemplateEventType.DELETE_PERSONAL_TEMPLATE_SYNC -> handleTemplateSyncEvent(event)
            TemplateEventType.UPDATE_PERSONAL_TEMPLATE_SYNC -> /*handleTemplateUpdateEvent(event)*/getData(false)
            TemplateEventType.UPDATE_HOME_TEMPLATE_SYNC -> getData(false)
            TemplateEventType.DELETE_FOLDER_ALL_TEMPLATE_SYNC,
            TemplateEventType.FOLDER_BATCH_DELETE_TEMPLATE_SYNC,
            TemplateEventType.FOLDER_BATCH_MOVE_TEMPLATE_SYNC -> handleFolderTemplateEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun AbleToPrint(event: AbleToPrint) {
        if(event.isAbleToPrint){
            VipHelper.isTrial=true
            //跳转到打印设置
            SyncTemplatePresenter.startPrintActivity(
                requireActivity(),
                niimbotDrawData,
                needRfidTemplate = false
            )
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNpsEvent(event: CloseNpsEvent) {
        binding.rlNps.visibility = View.GONE
        binding.rlNps.invalidate()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onNpsVipEvent(event: GoVipNpsEvent) {
        var url = NpsVipUtil.url
        NpsVipUtil.productId = event.productId ?: ""
        if(!isConnected()){
            return
        }
        GlobalScope.launch(Dispatchers.Main){
            //请求是否要弹出用户调研的配置
            var appConfigMap = NpsVipUtil.getAppConfig()
            if(appConfigMap != null && appConfigMap["openVipNps"] == 1){
                if (url.isNullOrEmpty()) {
                    url = NpsVipUtil.constructNpsUrl(TextHookUtil.getInstance().languageName)
                    activity?.let {
                        NpsVipUtil.reloadNpsUrl(url!!)
                        NpsVipUtil.showNpsDialog(it, url!!, "vipToast")
                    }

                } else {
                    if (!NpsVipUtil.webViewLoadSuccess) {
                        NpsVipUtil.reloadNpsUrl(url!!)
                    }
                    activity?.let { NpsVipUtil.showNpsDialog(it, url!!, "vipToast") }
                }
            }

        }
    }

    private fun handleTemplateUpdateEvent(event: TemplateEvent) {
        val eventData = event.eventData ?: return
        val templateSyncData = TemplateSyncData.fromJson(eventData) ?: return
        val templateId = templateSyncData.templateId
        val templateModuleLocal = templateSyncData.niimbotTemplate ?: return
        val templateModule =
            TemplateModuleTransform.templateModuleLocalToTemplateModule(templateModuleLocal)
        mAdapter.data.withIndex().find { it.value.id == templateId }?.index?.let {
            if (mAdapter.data[it].update_time != templateModule.update_time) {
                mAdapter.data.removeAt(it)
                mAdapter.addData(templateModule, 0)
                mAdapter.notifyItemMoved(it, 0)
                binding.scrollView.smoothScrollTo(0, 0)
            } else {
                mAdapter.data[it] = templateModule
                mAdapter.notifyItemChanged(it)
            }
        }
    }

    private fun handleTemplateSyncEvent(event: TemplateEvent) {
        val eventData = event.eventData ?: return
        val templateSyncData = TemplateSyncData.fromJson(eventData) ?: return
        val templateId = templateSyncData.templateId
        LogUtils.i(TAG, "home page template sync delete, templateId = $templateId")
        mData.withIndex().find { it.value.id == templateId }?.index?.let {
            mAdapter.removeDate(it)
        }
        Handler().postDelayed({
            deleteSync()
        }, 500)
    }

    private fun handleFolderTemplateEvent(event: TemplateEvent) {
        val folderTemplateSyncData =
            json2Any(event.eventData, FolderTemplateSyncData::class.java) ?: return
        when (event.eventType) {
            TemplateEventType.DELETE_FOLDER_ALL_TEMPLATE_SYNC -> {
                val folderId = folderTemplateSyncData.newFolderId
                if (folderId.isNotEmpty()) {
                    val deleteTemplates = mData.filter { it.folder_id == folderId }
                    mAdapter.removeDate(deleteTemplates)
                    deleteSync()
                }
            }

            TemplateEventType.FOLDER_BATCH_DELETE_TEMPLATE_SYNC -> {
                val templateIds = folderTemplateSyncData.templateIds
                if (templateIds.isNotEmpty()) {
                    templateIds.forEach { id ->
                        mData.withIndex().find { it.value.id == id }?.index?.let {
                            mAdapter.removeDate(it)
                        }
                    }
                    deleteSync()
                }
            }

            TemplateEventType.FOLDER_BATCH_MOVE_TEMPLATE_SYNC -> {
                val newFolderId = folderTemplateSyncData.newFolderId
                val templateIds = folderTemplateSyncData.templateIds
                if (templateIds.isNotEmpty()) {
                    templateIds.forEach { id ->
                        mData.find { it.id == id }?.folder_id = newFolderId
                    }
                }
            }
        }
    }

    fun notifyUpdateSkin() {
        if (!SkinHelper.hasActiveSkin(TextHookUtil.getInstance().languageName)) return
        val iconIndustry =
            SkinResource.getDrawable(
                TextHookUtil.getInstance().languageName,
                tag_icon_home_industry
            )
        if (iconIndustry != null) {
            binding.tvIndustryTemplate?.setCompoundDrawablesWithIntrinsicBounds(
                null,
                iconIndustry,
                null,
                null
            )
//            binding.ivTitleIndustry?.setImageDrawable(iconIndustry)
        }
        val iconScan =
            SkinResource.getDrawable(TextHookUtil.getInstance().languageName, tag_icon_home_scan)
        if (iconScan != null) {
            binding.tvScanTemplate?.setCompoundDrawablesWithIntrinsicBounds(
                null,
                iconScan,
                null,
                null
            )
//            binding.ivTitleTemplate?.setImageDrawable(iconScan)
        }
        val bgRfid =
            SkinResource.getDrawable(TextHookUtil.getInstance().languageName, tag_bg_home_rfid)
        if (bgRfid != null) {
            binding.rfidLayout?.background = bgRfid
        }
        val bgList =
            SkinResource.getDrawable(TextHookUtil.getInstance().languageName, tag_bg_home_list)
        if (bgList != null) {
            binding.mainLayout?.background = bgList
        }
        val bgOperate =
            SkinResource.getDrawable(
                TextHookUtil.getInstance().languageName,
                tag_bg_home_top_operate
            )
        if (bgOperate != null) {
            binding.operateLayout?.background = bgOperate
        }

    }

    private fun initTopBg() {
        MainScope().launch {
            val topBgUrl = LanguageUtil.findLanguageString("app100001257")
            if (StringUtil.isUrl(topBgUrl)) {
                val bitmap =
                    withContext(Dispatchers.Default) { ImageLoader.getBitmap(activity, topBgUrl) }
                if (bitmap != null) {
                    binding.topBgLayout.background =
                        BitmapDrawable(SuperUtils.superContext.resources, bitmap)
                    try {
                        val toolbarColor = LanguageUtil.findLanguageString("app100001261")
                        if (toolbarColor.isNotEmpty() && toolbarColor.startsWith("#")) {
                            binding.homeToolbar.setBackgroundColor(
                                ColorUtils.string2Int(
                                    toolbarColor
                                )
                            )
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        binding.homeToolbar.setBackgroundColor(ColorUtils.getColor(R.color.colorPrimaryNew))
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        NpsUtils.clearWebview()
        NpsVipUtil.clearWebview()
        NpsManager.clearWebview()
    }
}
