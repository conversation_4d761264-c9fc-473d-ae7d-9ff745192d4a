import 'dart:io';

import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart' as Canvas;
import 'package:niimbot_flutter_canvas/src/model/material/material_item.dart';
import 'package:niimbot_template/niimbot_template.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/constant/template_type.dart';
import 'package:text/template/constant/template_version.dart';
import 'package:text/template/util/template_image_utils.dart';

extension TemplateDataExtension on TemplateData {
  // 创建一个静态 Map 来存储扩展字段
  static final Map<String, Map<String, dynamic>> _extraFields = {};

  static void clearExtraData() {
    _extraFields.clear();
  }

  // 获取 printPaper 字段
  int get printPaper {
    if (_extraFields[this.id] != null && _extraFields[this.id]!['printPaper'] != null) {
      return _extraFields[this.id]!['printPaper'];
    }
    return 0; // 默认值为 0
  }

  // 设置 printPaper 字段
  set printPaper(int value) {
    if (_extraFields[this.id] == null) {
      _extraFields[this.id ?? ""] = {};
    }
    _extraFields[this.id]!['printPaper'] = value;
  }

  // 获取 isSelected 字段
  bool get isSelected {
    if (_extraFields[this.id] != null && _extraFields[this.id]!['isSelected'] != null) {
      return _extraFields[this.id]!['isSelected'];
    }
    return false; // 默认值为 false
  }

  // 设置 isSelected 字段
  set isSelected(bool value) {
    if (_extraFields[this.id] == null) {
      _extraFields[this.id ?? ""] = {};
    }
    _extraFields[this.id]!['isSelected'] = value;
  }

  bool isExcelXLSTemplate() {
    if (dataSources != null && dataSources!.isNotEmpty && dataSources![0].type == TemplateDataSourceType.excel && dataSources![0].name?.endsWith('xls') == true) {
      return true;
    }
    return false;
  }

  bool isExcel() {
    if (dataSources != null && dataSources!.isNotEmpty && dataSources![0].type == TemplateDataSourceType.excel) {
      return true;
    }
    return false;
  }

  bool isCommodity() {
    if (profile.extra.templateType.toString() == TemplateType.TEMPLATE_GOOD ||
        (dataSources != null && dataSources!.isNotEmpty && dataSources![0].type == TemplateDataSourceType.commodity)) {
      return true;
    }
    return false;
  }

  bool isLocalTemplate() {
    if (local_type == TemplateLocalType.CREATE || local_type == TemplateLocalType.UPDATE) {
      return true;
    }
    return false;
  }

  /**
   * 判断模版是否完整，图片是否已经下载完毕
   */
  Future<bool> canEdit() async {
    bool isCanEdit = true;
    //判断标签纸背景图是否存在
    final filterLocalBackground = localBackground.where((e) => e.trim().isNotEmpty).toList();
    if (backgroundImage != null &&
        backgroundImage!.isNotEmpty &&
        backgroundImage!.split(',').length != filterLocalBackground.length) {
      return false;
    }
    for (var backgroundPath  in filterLocalBackground) {
      if (backgroundPath.isEmpty) {
        continue;
      }
      final isValidFile = await TemplateImageUtils.isFileValid(File(backgroundPath));
      if (!isValidFile) {
        return false;
      }
    }
    //判断image元素本地图片是否存在
    for (var element in elements) {
      if (element is ImageElement) {
        String? localImagePath = "";
        if(element.isNinePatch == true){
          localImagePath = element.ninePatchLocalUrl;
        }else{
          localImagePath = element.localImageUrl;
        }
        if (localImagePath?.isEmpty == true) {
          isCanEdit = false;
          break;
        }

        final localFile = File(localImagePath!);
        final isValidFile = await TemplateImageUtils.isFileValid(localFile);
        if (!isValidFile) {
          isCanEdit = false;
          break;
        }
      }
    }
    return isCanEdit;
  }

  bool needVipV2() {
    return hasVipElementV2() || vip;
  }

  /**
   * 判断模版是否包含vip元素
   */
  bool hasVipElementV2() {
    for (var element in elements) {
      if (element is MaterialElement) {
        bool isBorder = element.isMaterialBorder;
        MaterialCacheData? cacheData = isBorder
            ? Canvas.MaterialManager.sharedInstance().materialBoderCacheData
            : Canvas.MaterialManager.sharedInstance().materialCacheData;
        if (cacheData?.vipIdList.any((id) => id == element.materialId) ?? false) {
          return true;
        }
      } else if (element is DateElement) {
        bool hasVipFont = (element.fontCode?.isNotEmpty ?? false) && Canvas.FontManager().isFontVip(element.fontCode);
        if (element.dateIsRefresh || hasVipFont) {
          return true;
        }
      } else if (element is TextElement) {
        bool hasVipFont = (element.fontCode?.isNotEmpty ?? false) && Canvas.FontManager().isFontVip(element.fontCode);
        if (hasVipFont) {
          return true;
        }
      }else if (element is TableElement) {
        bool hasVipFont =
            element.cells.any((cell) =>
            (cell.fontCode?.isNotEmpty ?? false) &&
                Canvas.FontManager().isFontVip(cell.fontCode)
            ) ||
                element.combineCells.any((cell) =>
                (cell.fontCode?.isNotEmpty ?? false) &&
                    Canvas.FontManager().isFontVip(cell.fontCode)
                );

        if (hasVipFont) {
          return true;
        }
      }
    }
    return false;
  }

  String getLabelOneCode() {
    String? barcode = profile.barcode;
    if (barcode == null || barcode.isEmpty) {
      barcode = profile.extra.virtualBarCode;
    }
    if (barcode == null || barcode.isEmpty) {
      barcode = profile.extra.amazonCodeWuhan;
    }
    if (barcode == null || barcode.isEmpty) {
      barcode = profile.extra.amazonCodeBeijing;
    }
    if (barcode == null || barcode.isEmpty) {
      barcode = profile.extra.sparedCode;
    }
    if (barcode == null || barcode.isEmpty) {
      profile.extra.barcodeCategoryMap?.forEach((key, value) {
        if (value.isNotEmpty && (barcode == null || barcode!.isEmpty)) {
          barcode = value;
        }
      });
    }
    return barcode ?? '';
  }

  ///模板中是否包含某个关键词
  bool isContainKeyWords(String keyWords) {
    bool isContain = false;
    if (name.isCaseInsensitiveContains(keyWords)) {
      isContain = true; //包含名称
    }
    profile.extra.barcodeCategoryMap?.values.forEach((element) {
      if (element == keyWords) isContain = true; //包含条码
    });
    if (isContain) return isContain;
    if(elementsJsonStr?.isCaseInsensitiveContains(keyWords) ?? false){
      isContain = true;
    }
    // for (var element in elements) {
    //   if (element is DateElement || element.type is SerialElement || element is ImageElement) continue;
    //   if ((element is QRCodeElement && ((element.isLive) || (element.isForm)))) continue; //类型不可是高级二维码
    //   if (element is TableElement) {
    //     List<String> combineIds = [];
    //     for (var combinecellElement in element.combineCells) {
    //       if ((combinecellElement.value ?? "").isCaseInsensitiveContains(keyWords) &&
    //           (combinecellElement.dataBind == null || (combinecellElement.dataBind ?? []).isEmpty)) {
    //         isContain = true; //表格被合并的单元格包含
    //       }
    //       combineIds.add(combinecellElement.id);
    //     }
    //     if (isContain) return isContain;
    //     for (var cellElement in element.cells) {
    //       if (combineIds.contains(cellElement.combineId)) continue;
    //       if ((cellElement.value ?? "").isCaseInsensitiveContains(keyWords) &&
    //           (cellElement.dataBind == null || (cellElement.dataBind ?? []).isEmpty)) {
    //         isContain = true; //表格单元格包含
    //         break;
    //       }
    //     }
    //     if (isContain) return isContain;
    //   }
    //   if (element is TextElement && !(element.isBindingElement)) {
    //     if ((element.value ?? "").isCaseInsensitiveContains(keyWords)) {
    //       isContain = true;
    //       break;
    //     }
    //   }
    // }
    return isContain;
  }

  /**
   * 用于保存时，算出模版版本号
   */
  String getTemplateVersion() {
    String templateVersion = TemplateVersion.BASE_SAVE_TEMPLATE_VERSION;
    bool hasDateElement = elements.any((e) {
      if (e is DateElement) {
        return true;
      } else {
        return false;
      }
    });
    if (hasDateElement) {
      templateVersion = TemplateVersion.REALTIME_SAVE_TEMPLATE_VERSION;
    }
    if (isCommodity() && hasCustomGoodFields()) {
      templateVersion = TemplateVersion.GOOD_ADD_FIELD_SAVE_TEMPLATE_VERSION;
    }
    if (isExcelXLSTemplate()) {
      templateVersion = TemplateVersion.EXCEL_XLS_TEMPLATE_VERSION;
    }
    return templateVersion;
  }

  ///是否包含实时时间
  bool isContainInstantTime() {
    bool isInstantTime = elements.any((e) {
      if (e is DateElement && e.dateIsRefresh) {
        return true;
      } else {
        return false;
      }
    });
    return isInstantTime;
  }

  /**
   * 判断商品模版中是否含有自定义的商品字段
   */
  bool hasCustomGoodFields() {
    for (var element in elements) {
      if ((element is BindElement) &&
          (element is TextElement || element is BarCodeElement || element is QRCodeElement)) {
        if (element.bindingColumn >= 9) {
          return true;
        }
      }
      if (element is TableElement) {
        for (var combinecellElement in element.combineCells) {
          if (combinecellElement.isBindingElement && combinecellElement.bindingColumn >= 9) {
            return true;
          }
        }
        for (var cellElement in element.cells) {
          if (cellElement.isBindingElement && cellElement.bindingColumn >= 9) {
            return true;
          }
        }
      }
    }
    return false;
  }

  // Future<String> getTemplateLocalThumbPath() async{
  //   File localThumbFile = await TemplatePathGeneratorUtils.getThumbFile(id!,profile.extra.updateTime);
  //   bool isValidFile = await TemplateImageUtils.isFileValid(localThumbFile);
  //   if(isValidFile){
  //     return localThumbFile.absolute.path;
  //   }
  //   return "";
  // }
}
