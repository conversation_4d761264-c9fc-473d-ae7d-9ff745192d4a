import 'dart:io';
import 'dart:ui' as ui;

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart' hide MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:niim_login/login_plugin/mvp/base_page_state.dart';
import 'package:niim_login/login_plugin/router/fluro_convert_utils.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:niim_login/niim_login.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:text/macro/color.dart';
import 'package:text/pages/meProfile/me_profile_logic.dart';
import 'package:text/pages/user/profile_router.dart';
import 'package:text/routers/fluro_navigator.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/theme_widget.dart';
import 'package:text/widget/base_dialog.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import '../../application.dart';
import '../../constant.dart';
import '../../log_utils.dart';
import '../../utils/theme_color.dart';
import 'my_account_presenter.dart';

enum BindStep {
  /// 验证手机
  VERIFICATION_PHONE,

  ///验证账号密码
  VERIFICATION_PASSWORD,

  /// 绑定手机
  CONFIRMATION_PHONE,
}

class MyAccountPage extends StatefulWidget {
  String token;

  MyAccountPage(this.token);

  @override
  MyAccountPageState createState() => MyAccountPageState();
}

class MyAccountPageState extends BasePageState<MyAccountPage, MyAccountPresenter> with PageVisibilityObserver {
  String phone = Application.user?.phone ?? '';
  String areaCode = Application.user?.areaCode ?? '';
  String email = Application.user?.email ?? '';
  String profileUrl = Application.user?.profileUrl ?? '';

  bool hasPwd = true; //接口无法提供是否有密码的接口,所有都当做修改密码
  List<Map<String, dynamic>> appInstalled = [];

  /// ImageProvider
  ImageProvider? get imageDataProvider {
    try {
      MeProfileLogic logic = Get.find<MeProfileLogic>();
      if ((logic.userImageData()?.length ?? 0) != 0) {
        return MemoryImage(logic.userImageData() ?? Uint8List(0));
      }
    } catch (e) {}
    return null;
  }

  @override
  MyAccountPresenter createPresenter() => MyAccountPresenter();

  @override
  void initState() {
    super.initState();
    Application.inMyAccountPage = true;
    ToNativeMethodChannel().setFlutterVCCanSideslip(false);
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data == "refreshUser") {
        setState(() {
          phone = Application.user?.phone ?? '';
          email = Application.user?.email ?? '';
          profileUrl = Application.user?.profileUrl ?? '';
          areaCode = Application.user?.areaCode ?? '';
        });
      }
    });

    // showProgress();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      configInit().then((value) {
        //每次进入我的账号重新请求用户信息,因为存在多设备登录情况,导致不同设备信息不同步
        presenter.refreshUserInfo(callback: (sucess) {
          if (mounted) {
            setState(() {
              phone = Application.user?.phone ?? '';
              email = Application.user?.email ?? '';
              profileUrl = Application.user?.profileUrl ?? '';
              areaCode = Application.user?.areaCode ?? '';
            });
          }
          _getInstalledApps();
        });
      });
    });
  }

  @override
  void dispose() {
    closeProgress();
    NiimbotEventBus.getDefault().unregister(this);
    presenter.dispose();
    Application.inMyAccountPage = false;
    super.dispose();
  }

  _getUserInfo({bool notifyNative = false}) {
    presenter.queryUserLoginInfo(
        notifyNative: notifyNative,
        callback: (sucess) {
          if (mounted) {
            setState(() {
              phone = Application.user?.phone ?? '';
              email = Application.user?.email ?? '';
              profileUrl = Application.user?.profileUrl ?? '';
              areaCode = Application.user?.areaCode ?? '';
            });
          }
          closeProgress();
          _getInstalledApps();
        });
  }

  _getInstalledApps() async {
    appInstalled.clear();
    List socialTypes = [];
    List installedSocialTypes = await ToNativeMethodChannel.sharedInstance().getSupportSocialList();
    if (installedSocialTypes.contains("weixin")) {
      socialTypes.add("weixin");
    }
    if (installedSocialTypes.contains("qq")) {
      socialTypes.add("qq");
    }
    socialTypes.add("facebook");
    if (Platform.isIOS) {
      bool isSignInWithAppleAvailable = await LoginPluginApi.isSignInWithAppleAvailable();
      if (isSignInWithAppleAvailable) {
        socialTypes.add("apple");
      }
    } else {
      if (installedSocialTypes.contains("google")) {
        socialTypes.add("google");
      }
    }
    if (installedSocialTypes.contains("line")) {
      socialTypes.add("line");
    }
    String socialName = "";
    for (String type in socialTypes) {
      type = type.toLowerCase();
      if (type == "qq") {
        socialName = intlanguage("app00159", "QQ");
      } else if (type == "weixin") {
        socialName = intlanguage("app01015", "微信");
      } else if (type == "facebook") {
        socialName = intlanguage("app01383", "Facebook");
      } else if (type == "twitter") {
        socialName = intlanguage("app01382", "Twitter");
      } else if (type == "line") {
        socialName = intlanguage("app100000290", "Line");
      } else if (type == "google") {
        socialName = intlanguage("app100000291", "谷歌");
      } else if (type == "apple") {
        socialName = intlanguage("app100000292", "苹果");
      }
      bool flag = false;
      //已经绑定过的,就显示已绑定
      flag = Application.user?.socialNetworks
              ?.any((info) => ((info.platform == "WECHAT_OPEN" && type == "weixin") //微信需要做一个映射,后台和前端定义的不一样
                  ||
                  (info.platform?.toLowerCase() == type))) ??
          false;

      appInstalled.add({
        "type": type,
        "name": socialName,
        "binded": flag //是否绑定
      });
    }
    setState(() {});
  }

  // bool _checkAppThatInstalled(String type){
  //   if(appInstalled == null) return false;
  //   for(Map item in appInstalled){
  //     if(item["type"] == type){
  //       return true;
  //     }
  //   }
  //   return false;
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.white,
          foregroundColor: KColor.title,
          elevation: 1,
          // 去除导航栏底部阴影
          centerTitle: true,
          title: titleText(intlanguage("app100000305", "我的账号")),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios),
            onPressed: () {
              Future.delayed(Duration(microseconds: 300), () {
                closeProgress();
                ToNativeMethodChannel().setFlutterVCCanSideslip(true);
                ToNativeMethodChannel()
                    .goToNativePage({'vcName': 'sourcePage', 'needConnectPrinter': false, "popDeriction": "left"});
              });
            },
          ),
        ),
        backgroundColor: KColor.content_background,
        body: SingleChildScrollView(
          child: Column(
            children: [
              _buildMenu1Widget(),
              _buildMenu2Widget(),
              _buildMenuSocialWidget(),
              _buildMenu3Widget(),
            ],
          ),
        ));
  }

  Padding _buildMenu1Widget() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 20, 16, 0),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        child: Container(
            color: Colors.white,
            child: Column(
              children: [
                SizedBox(
                  height: 60,
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      _pickImage();
                    },
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          menuText(intlanguage("app100000306", "头像")),
                          Spacer(),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(44 / 2.0),
                            child: _validateProfileUrl()
                                ? CacheImageUtil().netCacheImage(
                                    width: 44,
                                    height: 44,
                                    fit: BoxFit.cover,
                                    imageUrl: profileUrl,
                                    imageBuilder: (context, imageProvider) => Container(
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: imageDataProvider ?? imageProvider,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    ),
                                    placeholder: ImageUtils.placeHoder(w: 10, h: 10),
                                    errorWidget: _defaultUserIcon(),
                                  )
                                : _defaultUserIcon(),
                          ),
                          SizedBox(
                            width: 3,
                          ),
                          menuRightIcon(),
                        ],
                      ),
                    ),
                  ),
                ),
                divider(),
                SizedBox(
                  height: 60,
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      ToNativeMethodChannel().sendTrackingToNative({
                        "track": "click",
                        "posCode": "132_390",
                      });
                      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                      NavigatorUtils.push(context, ProfileRouter.nicknameUpdate)
                          ?.then((value) => {ToNativeMethodChannel().setFlutterVCCanSideslip(true)});
                    },
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          menuText(intlanguage("app100000307", "昵称")),
                          SizedBox(
                            width: 24,
                          ),
                          Expanded(child: _getNickNameText()),
                          SizedBox(
                            width: 3,
                          ),
                          menuRightIcon(),
                        ],
                      ),
                    ),
                  ),
                ),
                divider(),
                _countryView()
              ],
            )),
      ),
    );
  }

  bool _validateProfileUrl() {
    if (profileUrl.isNotEmpty) {
      return Uri.parse(profileUrl).host.isNotEmpty;
    }
    return false;
  }

  Widget _defaultUserIcon() {
    if (imageDataProvider != null) {
      return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: imageDataProvider!,
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Application.user == null
          ? Image.asset(
              ImageUtils.getImgPath('header_icon'),
              width: 44,
              height: 44,
            )
          : Image.asset(
              ImageUtils.getImgPath('header_icon_logo'),
              width: 44,
              height: 44,
            );
    }
  }

  Widget _countryView() {
    //TODO: 需要根据国家地区判断是否为中国大陆, 如果是大陆用户不能修改国家,文字是灰色,不显示箭头
    // bool isForeign = Application.isForeign;
    bool isForeign = false;
    bool isCountryEmpty = true;
    String countryName = intlanguage("app100000033", "请选择");
    if (Application.user?.countryCode?.isNotEmpty == true) {
      //客户端获取所有的国家地区之后（只包含汉语和英文），保存，
      setState(() {
        countryName = Application.getUserCountryName();
      });

      isCountryEmpty = false;
    }

    return SizedBox(
        height: 60,
        child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              //非手机用户才可以选国家
              Log.d("GestureDetector ----- 非手机用户进入国家设置");
              if ((phone.isEmpty || isCountryEmpty) && DebounceUtil.checkClick()) {
                presenter.countryChangeVerify(success: (time) {
                  if (time == 0) {
                    //可以修改
                    //只有国外的用户,或者国家为空的才能选国家
                    ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                    NavigatorUtils.push(
                            context,
                            ProfileRouter.countrySelect +
                                "?isSubmitBtnShow=1" +
                                "&selectedCountryCode=${Application.user?.countryCode}")
                        ?.then((value) {
                      ToNativeMethodChannel().setFlutterVCCanSideslip(true);
                      if (value == null) return;
                      //更新用户信息
                      String countryCode = value.code;
                      var updateUserInfo = (String countryCode_) {
                        presenter.updateUserProfile(
                            regionCode: countryCode_,
                            success: () {
                              //本地更新用户的国家地区和区号
                              Application.user?.countryCode = countryCode_;
                              Application.user?.areaCode = value.dialCode;
                              if (mounted) {
                                setState(() {});
                              }
                              //刷新所有数据
                              NiimbotEventBus.getDefault().post('languageChanged');
                            });
                      };
                      if (countryCode.toLowerCase() == "cn") {
                        //切换到中国需要验证手机号
                        //并将此账号绑定到原有账号,
                        ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                        NavigatorUtils.push(context, ProfileRouter.phoneBinding + "?type=2")?.then((value) {
                          ToNativeMethodChannel().setFlutterVCCanSideslip(true);
                          if (value != null) {
                            updateUserInfo(countryCode);
                          }
                        });
                      } else {
                        updateUserInfo(countryCode);
                      }
                    });
                  } else {
                    //如果后台接口返回不能修改国家(用户已经修改过一次,并且没有满足修改设定的时间间隔),那么点击直接跳转这个页面
                    String formatString = intlanguage("app100000315", "yyyy年MM月dd日");
                    String timeStr = DateFormat(formatString).format(DateTime.fromMillisecondsSinceEpoch(time));
                    ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                    NavigatorUtils.push(context,
                            ProfileRouter.countryLimitPage + "?time=${FluroConvertUtils.fluroCnParamsEncode(timeStr)}")
                        ?.then((value) => {ToNativeMethodChannel().setFlutterVCCanSideslip(true)});
                  }
                });
              }
            },
            child: Padding(
              padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  menuText(intlanguage("app100000272", "国家地区")),
                  SizedBox(
                    width: 24,
                  ),
                  Expanded(
                    child: Text(
                      countryName,
                      textAlign: TextAlign.end,
                      style: TextStyle(
                        fontSize: 16,
                        color: isCountryEmpty ? KColor.subtitle : KColor.title,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 3,
                  ),
                  //非手机用户都可以选国家
                  (phone.isEmpty || isCountryEmpty) ? menuRightIcon() : Container(),
                ],
              ),
            )));
  }

  Widget _getNickNameText() {
    var txt = Application.user == null ? '' : (Application.user?.showName() ?? '');
    return Text(
      txt,
      textAlign: TextAlign.end,
      style: const TextStyle(fontSize: 16, color: KColor.title, fontWeight: FontWeight.w400),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  Widget _unbindText(String title, {String? prefixIcon, bool enable = true, TextStyle? style}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        prefixIcon != null ? Image.asset(ImageUtils.getImgPath(prefixIcon)) : Container(),
        Text(title,
            textAlign: TextAlign.end,
            style: style ??
                TextStyle(
                  fontSize: 16,
                  color: enable ? KColor.subtitle : KColor.disable_text,
                ))
      ],
    );
  }

  Padding _buildMenu2Widget() {
    List<Widget> columnWidgets = [];
    columnWidgets.add(SizedBox(
      height: 60,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: phone.isNotEmpty ? _changeBindPhone : _bindPhone,
        child: Padding(
          padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              menuText(intlanguage("app01397", "手机号")),
              Spacer(),
              phone.isNotEmpty ? menuSubTextLTRDirection(phone) : _unbindText(intlanguage("app100000308", "绑定")),
              SizedBox(
                width: 3,
              ),
              menuRightIcon(),
            ],
          ),
        ),
      ),
    ));

    if (columnWidgets.isNotEmpty) {
      columnWidgets.add(divider());
    }

    columnWidgets.add(SizedBox(
      height: 60,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: email.isNotEmpty
            ? null
            : () {
                //绑定邮箱
                ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                NavigatorUtils.push(context, ProfileRouter.emailBinding + "?fromNative=0")?.then((value) {
                  ToNativeMethodChannel().setFlutterVCCanSideslip(true);
                  if (value is Map && value["bindRes"]) {
                    showToast(intlanguage("app100000287", "绑定邮箱成功"));
                  }
                  if (mounted) {
                    setState(() {});
                  }
                });
              },
        child: Padding(
          padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              menuText(intlanguage("app01381", "邮箱")),
              SizedBox(
                width: 42,
              ),
              Spacer(),
              email.isNotEmpty
                  ? Expanded(
                      child: menuSubTextLTRDirection(email,
                          textAlign:
                              Directionality.of(context) == ui.TextDirection.rtl ? TextAlign.left : TextAlign.right),
                      flex: 10,
                    )
                  : _unbindText(intlanguage("app100000308", "绑定")),
              SizedBox(
                width: 3,
              ),
              if (email.isEmpty) menuRightIcon(),
            ],
          ),
        ),
      ),
    ));

    if (phone.isNotEmpty || email.isNotEmpty) {
      if (columnWidgets.isNotEmpty) {
        columnWidgets.add(divider());
      }
      columnWidgets.add(SizedBox(
        height: 60,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: (email.isEmpty && phone.isEmpty)
              ? null
              : () {
                  ToNativeMethodChannel().sendTrackingToNative({
                    "track": "click",
                    "posCode": "132_391",
                  });
                  String type, account;
                  //邮箱为空的话依然走手机号验证
                  if ((phone.isNotEmpty &&
                          Application.user?.countryCode != null &&
                          Application.user?.countryCode?.toLowerCase() != "unknown") ||
                      email.isEmpty) {
                    type = "1";
                    account = phone;
                  } else {
                    type = "2";
                    account = email;
                  }
                  ToNativeMethodChannel().setFlutterVCCanSideslip(false);
                  NavigatorUtils.push(
                          context,
                          ProfileRouter.setPassword +
                              "?accountType=$type" +
                              "&account=$account" +
                              "&iddCode=${Application.user?.countryCode ?? "" + "&hasPwd=${hasPwd ? 1 : 0}"}")
                      ?.then((value) {
                    ToNativeMethodChannel().setFlutterVCCanSideslip(true);
                    if (value == 0) {
                      showToast(intlanguage("app100000316", "修改成功"));
                    }
                  });
                },
          child: Padding(
            padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                menuText(intlanguage("app100000278", "密码")),
                Spacer(),
                menuRightIcon(),
              ],
            ),
          ),
        ),
      ));
    }
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 20, 16, 0),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        child: Container(
            color: Colors.white,
            child: Column(
              children: columnWidgets,
            )),
      ),
    );
  }

/*检查该社交账号是否已经绑定,暂时没用*/
  bool checkSocialBindStatus(String type) {
    if (Application.user?.socialNetworks == null) return false;
    return Application.user?.socialNetworks
            ?.any((info) => ((info.platform == "WECHAT_OPEN" && type == "weixin") //微信需要做一个映射,后台和前端定义的不一样
                ||
                (info.platform?.toLowerCase() == type))) ??
        false;
  }

/*社交登录*/
  Widget _buildMenuSocialWidget() {
    // List<Widget> columnWidgets = [];
    //找出已经安装的app
    if (appInstalled.length == 0) return Container();

    double count = appInstalled.length.truncateToDouble();
    num bindedCount = 0;
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 20, 16, 0),
      child: ClipRRect(
        borderRadius: BorderRadius.all(Radius.circular(12)),
        child: Container(
          color: KColor.WHITE,
          height: 60.0 * count,
          child: ListView.separated(
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              String type, socialName;
              bool binded = false;
              Map<dynamic, dynamic> item = appInstalled[index];
              type = item["type"] as String;
              socialName = item["name"] as String;
              binded = item["binded"];

              if (binded) {
                bindedCount++;
              }

              return Container(
                height: 60,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "132_392",
                    });

                    Map<dynamic, dynamic> item = appInstalled[index];
                    binded = item["binded"];
                    type = item["type"] as String;
                    if (binded) {
                      //解绑
                      // _unbindSocial(type);
                      if (bindedCount > 1 || Application.isExistMainAccount()) {
                        showUnBindConfirmDialog(type);
                      } else {
                        showToast(intlanguage("app100000296", "不可解绑"));
                      }
                    } else {
                      //去绑定
                      if (type == "qq") {
                        _bindQQ();
                      } else if (type == "weixin") {
                        _bindWeixin();
                      } else if (type == "facebook") {
                        _bindFacebook();
                      } else if (type == "twitter") {
                      } else if (type == "line") {
                        _bindLine();
                      } else if (type == "google") {
                        _bindGoogle();
                      } else if (type == "apple") {
                        _bindApple();
                      }
                    }
                    // showUnBindConfirmDialog(1);
                  },
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(20, 0, 12, 0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        menuText(socialName),
                        Spacer(),
                        //TODO: 此处需要添加已绑定和绑定的图标
                        (binded)
                            ? Container(
                                height: 30,
                                decoration: BoxDecoration(
                                    color: KColor.content_background,
                                    borderRadius: BorderRadius.all(Radius.circular(30))),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 12,
                                    ),
                                    Image(
                                      image: ImageUtils.getAssetImage("profile/binded_icon"),
                                      width: 12,
                                      height: 12,
                                    ),
                                    SizedBox(
                                      width: 6,
                                    ),
                                    menuSubText(intlanguage("app100000281", "已绑定")),
                                    SizedBox(
                                      width: 12,
                                    ),
                                  ],
                                ),
                              )
                            : Container(
                                height: 30,
                                decoration: BoxDecoration(
                                    color: KColor.content_background,
                                    borderRadius: BorderRadius.all(Radius.circular(30))),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 12,
                                    ),
                                    Image(
                                      image: ImageUtils.getAssetImage("profile/add_binding"),
                                      width: 12,
                                      height: 12,
                                    ),
                                    SizedBox(
                                      width: 6,
                                    ),
                                    _unbindText(intlanguage("app100000308", "绑定"),
                                        style: TextStyle(color: KColor.RED, fontSize: 16)),
                                    SizedBox(
                                      width: 12,
                                    ),
                                  ],
                                ),
                              ),
                        SizedBox(
                          width: 3,
                        ),
                        // menuRightIcon(),
                      ],
                    ),
                  ),
                ),
              );
            },
            itemCount: appInstalled.length,
            separatorBuilder: (context, index) {
              return divider();
            },
          ),
        ),
      ),
    );
  }

  _unbindSocial(String provider) {
    if (provider == "weixin") {
      provider = "WECHAT_OPEN";
    }
    presenter.unbindSocial(provider, () {
      //本地修改application中的社交信息, 刷新界面
      if (Application.user?.socialNetworks != null) {
        Application.user?.socialNetworks
            ?.removeWhere((element) => element.platform?.toLowerCase() == provider.toLowerCase());
        _getUserInfo();
      }
    });
  }

  //有手机号更换绑定手机号
  void _changeBindPhone() async {
    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "132_388"});

    // NiimbotDialogController().show(
    //     title: intlanguage("app100001826", "更换已绑定的手机号？"),
    //     dialogType: DialogType.normal,
    //     child: Column(
    //       children: [
    //         const SizedBox(
    //           height: 6,
    //         ),
    //         Text(
    //           intlanguage("app100001827", "当前绑定的手机号为") + "$phone",
    //           textAlign: TextAlign.center,
    //           style: TextStyle(
    //             color: ThemeColor.mainTitle,
    //             fontSize: 14,
    //             fontWeight: FontWeight.w400,
    //           ),
    //         ), // 描述
    //         const SizedBox(
    //           height: 16,
    //         ),
    //         Text(
    //           intlanguage(
    //               "app100001828", "更换后，你在精臣其他App或服务下绑定该手机号的账号将都换绑为新的手机号。(精臣旗下App或服务包括但不限于“精臣云打印”、“臣小印”、“精臣商城”)"),
    //           style: TextStyle(
    //             color: ThemeColor.mainTitle,
    //             fontSize: 14,
    //             fontWeight: FontWeight.w400,
    //           ),
    //         ),
    //         const SizedBox(
    //           height: 19,
    //         ),
    //       ],
    //     ),
    //     confirmText: intlanguage("app100001327", "更换"),
    //     cancelText: intlanguage("app00030", "取消"),
    //     onCancel: (done) {
    //       done();
    //     },
    //     onConfirm: (done) {
    //       done();
    //       ToNativeMethodChannel().setFlutterVCCanSideslip(false);
    //       NavigatorUtils.push(context, ProfileRouter.changePhoneBinding + "?bindStep=0&phone=$phone&iddCode=$areaCode")
    //           ?.then((Value) {
    //         ToNativeMethodChannel().setFlutterVCCanSideslip(true);
    //       });
    //     });
    ToNativeMethodChannel().setFlutterVCCanSideslip(false);
    bool? result = await BaseDialog.showChangeBindPhoneeDialog(context,
        title: intlanguage("app100001826", "更换已绑定的手机号？"),
        subTitle: intlanguage("app100001827", "当前绑定的手机号为") + "$phone",
        content:
            intlanguage("app100001828", "更换后，你在精臣其他App或服务下绑定该手机号的账号将都换绑为新的手机号。(精臣旗下App或服务包括但不限于“精臣云打印”、“臣小印”、“精臣商城”)"),
        cancelDes: intlanguage("app00030", "取消"),
        confirmDes: intlanguage("app100001327", "更换"));
    if (result != null) {
      if (result) {
        NavigatorUtils.push(context, ProfileRouter.changePhoneBinding + "?bindStep=0&phone=$phone&iddCode=$areaCode")
            ?.then((value) => {ToNativeMethodChannel().setFlutterVCCanSideslip(true)});
      }
    }
  }

  //没有手机号绑定
  _bindPhone() async {
    String secret = "";
    // if(Platform.isIOS) {
    //   secret = Constant.iosOneKey;
    // }else if(Platform.isAndroid) {
    //   secret = Constant.androidOneKey;
    // }

    Map res = await ToNativeMethodChannel.sharedInstance().SIMAccountOneKeyBinding(secret);
    if (res["action"] == "SIMBinding") {
      //TODO: 直接调用一键绑定接口
      presenter.bindSimAccount(res["ispToken"], success: () {
        if (mounted) {
          setState(() {});
        }
        //刷新所有数据
        NiimbotEventBus.getDefault().post('languageChanged');
      });
    } else if (res["action"] == "switchPhoneBinding") {
      //绑定手机
      ToNativeMethodChannel().setFlutterVCCanSideslip(false);
      NavigatorUtils.push(context, ProfileRouter.phoneBinding + "?type=1")?.then((value) {
        ToNativeMethodChannel().setFlutterVCCanSideslip(true);
        if (value is Map && value["bindRes"]) {
          showToast(intlanguage("app100000285", "绑定手机号成功"));
        }
        if (mounted) {
          setState(() {});
        }
      });
    }
  }

  _bindQQ() {
    LoginPluginApi.bindqq((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("qq", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  _bindWeixin() {
    LoginPluginApi.bindWeixin((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("WECHAT_OPEN", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  _bindLine() {
    LoginPluginApi.bindLine((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("line", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  _bindGoogle() {
    LoginPluginApi.bindGoogle((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("google", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  _bindApple() {
    LoginPluginApi.bindApple((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("apple", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  _bindFacebook() {
    LoginPluginApi.bindFacebook((p0) {
      if (p0["authResult"] != null && p0["authResult"]) {
        presenter.bindSocial("facebook", p0["socialCode"], success: () {
          _getUserInfo();
        });
      }
    });
  }

  void showUnBindConfirmDialog(String type) {
    //如果只有一种社交就不能解绑
    if (email.isEmpty && phone.isEmpty && Application.user?.socialNetworks?.length == 1) {
      showToast(intlanguage("app100000296", "不可解绑"));
      return;
    }
    String socialName = "";
    //去绑定
    if (type == "qq") {
      socialName = intlanguage("app00159", "QQ");
    } else if (type == "weixin") {
      socialName = intlanguage("app01015", "微信");
    } else if (type == "facebook") {
      socialName = intlanguage("app01383", "Facebook");
    } else if (type == "twitter") {
      socialName = socialName = intlanguage("app01382", "Twitter");
    } else if (type == "line") {
      socialName = intlanguage("app100000290", "Line");
    } else if (type == "google") {
      socialName = intlanguage("app100000291", "谷歌");
    } else if (type == "apple") {
      socialName = intlanguage("app100000292", "苹果");
    }
    showCustomDialog(context, intlanguage("app00749", "解绑") + " " + socialName,
        intlanguage("app100000293", "确定要解绑当前 %s 号？", param: [socialName]),
        leftFunStr: intlanguage("app00030", "取消"), rightFunStr: intlanguage("app00749", "解绑"), rightFunCall: () {
      _unbindSocial(type);
    });
  }

  Padding _buildMenu3Widget() {
    return Padding(
        padding: EdgeInsets.fromLTRB(16, 20, 16, 20),
        child: GestureDetector(
          onTap: () {
            _logout(context);
          },
          child: ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(12)),
            child: Container(
              height: 60.0,
              color: Colors.white,
              child: Center(
                child: Text(
                  intlanguage("app00207", "退出登录"),
                  style: TextStyle(fontSize: 16, color: KColor.RED, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
        ));
  }

  void _logout(BuildContext context) {
    showCustomDialog(context, intlanguage("app00032", "提示"), intlanguage("app00418", "是否确认退出登录"), rightFunCall: () {
      showProgress(msg: intlanguage("app100000309", "正在退出登录..."));
      presenter.logout(callback: (result) {
        closeProgress();
        NiimbotLogTool.writeLogToFile({"接收登录信息": '退出成功'});
        Application.clearUserInfo().then((value) {
          NiimbotLogTool.writeLogToFile({"接收登录信息": '清空Application userInfo ${Application.user?.toJson()}'});
          ToNativeMethodChannel().logout(1);
          LoginPluginApi.loginOutClearCache();
          //退出登录
          ToNativeMethodChannel().setFlutterVCCanSideslip(true);
          ToNativeMethodChannel.sharedInstance()
              .goToNativePage({'vcName': 'sourcePage', 'needConnectPrinter': false, "popDeriction": "left"});
        });
      });
    });
  }

  Future<void> _pickImage() async {
    // int state = await ToNativeMethodChannel.sharedInstance().authorizationStatus();
    // if (state == 1) {
    //   return;
    // }

    getImage() async {
      showModalBottomSheet(
        context: context,
        barrierColor: Color(0x00000000).withOpacity(0.35),
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        isDismissible: false,
        enableDrag: false,
        isScrollControlled: true,
        builder: (context) => TakePhotosCameraPureWidget(),
      ).then((image) async {
        if (image != null && image is XFile && image.path.isNotEmpty) {
          Uint8List? imageData = await image.readAsBytes();
          String imgName = "userImage_" + DateTime.now().millisecondsSinceEpoch.toString();
          MultipartFile file = MultipartFile.fromBytes(
            imageData as List<int>,
            filename: imgName,
          );
          if (file.length > Constant.oss_image_upload_max_bytes) {
            showToast(intlanguage("login0113", "图片上传失败，仅支持上传小于2M的图片"));
          } else {
            if (Application.networkConnected != true) {
              showToast(intlanguage("app100000354", "网络异常"));
              return;
            }
            showProgress(msg: intlanguage("app100000313", "正在保存..."));
            presenter.uploadImage(imgName, file, 0, success: (int index, String imageUrl) async {
              if (Application.user?.profileUrl != imageUrl) {
                presenter.updateUserProfile(
                    avatar: imageUrl,
                    success: () {
                      closeProgress();
                      Application.user?.profileUrl = imageUrl;
                      Application.saveUser();
                      // showToast(loginIntl("alert_upgrade_sucess"));
                      showToast(intlanguage("app100000316", "修改成功"));
                    });
              } else {
                closeProgress();
                Application.user?.profileUrl = imageUrl;
                // 用户头像缓存Key
                try {
                  MeProfileLogic logic = Get.find<MeProfileLogic>();
                  logic.userImageData.value = imageData;
                } catch (e) {}
                // 清除缓存
                await CacheImageUtil().evictFromCache(profileUrl);
                Application.saveUser();
                // showToast(loginIntl("alert_upgrade_sucess"));
                showToast(intlanguage("app100000316", "修改成功"));
              }
            }, error: (int index, String errorMsg) {
              closeProgress();
              if (errorMsg.isNotEmpty) {
                showToast(errorMsg);
              } else {
                showToast(loginIntl("login0114"));
              }
            });
          }
        }
      });
    }

    if (!mounted) return;
    late bool psState;
    // 首次会获取摄像头权限，判断摄像头权限
    if (Platform.isAndroid) {
      psState = await PermissionDialog.requestCamera(context);
      if (psState) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "132_389",
        });
        getImage();
      }
    } else {
      psState = await PermissionUtils.checkCameraGranted();
      if (psState) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "132_389",
        });
        getImage();
      } else {
        // 展示alert
        showCupertinoAlert(context,
            title: intlanguage("app100000865", '相机权限未开启'),
            content: intlanguage("app100000864", '无法使用相机，请前往“设置>精臣云打印>相机”中打开相机权限。'),
            cancelDes: intlanguage("app100000866", '暂不'),
            confirmDes: intlanguage("app01308", '去设置'),
            cancelAction: () {}, confirmAction: () async {
          // 跳转设置页
          await PhotoManager.openSetting();
        });
    }

    }
  }

  Future<void> showCupertinoAlert(
    BuildContext context, {
    required String title,
    required String content,
    String cancelDes = '',
    String confirmDes = '',
    VoidCallback? cancelAction,
    VoidCallback? confirmAction,
  }) {
    return showCupertinoDialog(
        useRootNavigator: false,
        context: context,
        builder: (BuildContext context) {
          return WillPopScope(
            onWillPop: () async => true,
            child: Container(
              color: ThemeColor.mainTitle.withOpacity(0.35),
              child: Center(
                child: Container(
                  width: 270,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0), color: ThemeColor.background),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 标题
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 20, 16, 6),
                        child: Text(
                          title,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: ThemeColor.mainTitle,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.none),
                        ),
                      ),
                      // 描述
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 19),
                        child: Text(
                          content,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: ThemeColor.mainTitle,
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              decoration: TextDecoration.none),
                        ),
                      ),
                      Container(
                        color: ThemeColor.COLOR_D9D9D9,
                        height: 0.5,
                      ),
                      SizedBox(
                        height: 50,
                        child: DefaultTextStyle(
                          style: TextStyle(color: ThemeColor.COLOR_595959, fontSize: 17, fontWeight: FontWeight.w400),
                          child: Row(
                            children: [
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop(true);
                                    if (cancelAction != null) {
                                      cancelAction();
                                    }
                                  },
                                  behavior: HitTestBehavior.opaque,
                                  child: Text(
                                    cancelDes.isEmpty ? intlanguage("app00030", "取消") : cancelDes,
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Container(
                                color: ThemeColor.COLOR_D9D9D9,
                                width: 0.5,
                              ),
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).pop(true);
                                    if (confirmAction != null) {
                                      confirmAction();
                                    }
                                  },
                                  behavior: HitTestBehavior.opaque,
                                  child: Text(
                                    confirmDes.isEmpty ? intlanguage("app00048", "确定") : confirmDes,
                                    style: TextStyle(color: ThemeColor.COLOR_537FB7),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        });
  }
}
