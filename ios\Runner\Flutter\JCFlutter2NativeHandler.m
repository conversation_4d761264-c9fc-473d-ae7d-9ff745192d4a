//
//  JCFlutter2NativeHandler.m
//  Runner
//  处理 Flutter Boost 插件中 Flutter 插件跳转到原生页面的场景
//
//  Created by 杨瑞 on 2023/2/2.
//

#import "JCFlutter2NativeHandler.h"
#import <flutter_boost/FlutterBoost.h>
#import "FlutterBoostUtility.h"
#import "JCPrintHistoryHelper.h"
#import "JCTemplateImageManager.h"
#import "JCExcelListViewController.h"
#import "JCVIPDetailViewController.h"
#import "JCMyTemplateDetailViewController.h"
#import "FlutterBoostUtility.h"
#import "JCAboutUsViewController.h"
#import "JCSettingViewController.h"
#import "JCQAViewController.h"
#import "JCShopNormalVC.h"
#import "JCExcelDetailModel.h"
#import "JCFontManager.h"
#import "UINavigationController+NavigationBar.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "XYTabBarController.h"
#import "JCShopNormalVC.h"
#import "JCGrayManager.h"
#import "JCExcelTransUtil.h"
#import "JCRFIDSourceManager.h"
#import "JCAppMethodChannel.h"
#import "JCC1PrintInfo.h"
#import "JCLabelAppModel.h"
// 扫码相关
#import "Global.h"
#import "StyleDIY.h"
#import "QQLBXScanViewController.h"
#import "NMFAWebViewController.h"
#import "JCBatchPrintSourceManager.h"
#import "JCTemplateSaveManager.h"
// WKWebView
#import "NMWebViewController.h"
#import "JCExcelTransUtil.h"
#import "JCNPSWebviewAlert.h"
#import "JCLabelApplicationViewController.h"


//我的界面相关
static NSString *_toBluetoothConnectEvent = @"toBluetoothConnectEvent";  //连接蓝牙或进入设备详情
static NSString *_toMyTemplatesEvent = @"toMyTemplatesEvent";           //我的模板
static NSString *_toMyPrintHistoryEvent = @"toMyPrintHistoryEvent";     //打印记录
static NSString *_toNewUserGuideEvent = @"toNewUserGuideEvent";                 //新手引导
static NSString *_toHelpCenterEvent = @"toHelpCenterEvent";                     //帮助中心
static NSString *_toSystemDockingServiceEvent = @"toSystemDockingServiceEvent"; //系统对接
static NSString *_toInvestmentAgentEvent = @"toInvestmentAgentEvent";          //招商代理
static NSString *_toFadebackEvent = @"toFadebackEvent";                         //一键反馈
static NSString *_toAboutUSEvent = @"toAboutUSEvent";                           //关于我们
static NSString *_toLoginOrUserCenterEvent = @"toLoginOrUserCenterEvent";       //登录或打开个人中心
static NSString *_toAppSettingrEvent = @"toAppSettingrEvent";                   //app设置
static NSString *_toMessageCenter = @"toMessageCenter";                   //app消息中心
static NSString *_toRenewVipEvent = @"toRenewVipEvent";                         //开始vip续费
static NSString *_toOpenVipEvent = @"toOpenVipEvent";                           //进入VIP详情页
static NSString *_toMergeVipRightEvent = @"toMergeVipRightEvent";               //vip权益绑定
static NSString *_toObligationsEvent = @"toObligationsEvent";               //待付款
static NSString *_toBeReceivedEvent = @"toBeReceivedEvent";                 //待收货
static NSString *_toReturnAfterSaleEvent = @"toReturnAfterSaleEvent";       //退换
static NSString *_toShopOrdersEvent = @"toShopOrdersEvent";                 //用户全部订单
static NSString *_toBuyLabelsEvent = @"toBuyLabelsEvent";                   //去购买
static NSString *_toShopPointsEvent = @"toShopPointsEvent";                 //商城积分
static NSString *_toShopCouponEvent = @"toShopCouponEvent";                 //商城优惠券呢
static NSString *_toShopUserAddressEvent = @"toShopUserAddressEvent";       //商城购买收货地址
static NSString *_toNPSGradeEvent = @"toNPSGradeEvent";                     //nps评分
static NSString *_toAnotherOrderEvent = @"toBuyLabelsEvent";                //再来一单
static NSString *_toSpecBenefitsEvent = @"toSpecBenefitsEvent";             //精臣专享福利
static NSString *_toMyDocument = @"toMyExcel";                              //跳转到我的文件
static NSString *_toImPageEvent = @"toImPageEvent";                              //跳转到我的文件
static NSString *_toMeDevicesPage = @"toMeDevicesPage";                              //跳转到我的设备

static NSString *_toETagConnectPage = @"DeviceConnectTagBasePage";          //跳转至电子价签连接

static NSString *_toC1ConnectPage = @"DeviceConnectC1HomePage";          // 跳转至C1连接

static NSString *_toC1PrintPage = @"C1PrintPage";          // 跳转至C1打印页

static NSString *_toEtagPage = @"toEtagPage";

static NSString * _toNpsAlert = @"toNpsAlert";

static NSString * _toMyAppEvent = @"toMyAppEvent";


@interface JCFlutter2NativeHandler()
@property (nonatomic, strong) NSMutableArray *fontSourceResoloveArr;
@property (nonatomic, strong) MBProgressHUD *progressHUD;
@end

//extern BOOL isCanCreate;

@implementation JCFlutter2NativeHandler

DEF_SINGLETON(JCFlutter2NativeHandler)

extern BOOL isCanCreate;

- (id)init {
  self = [super init];
  if (self) {
    self.fontSourceResoloveArr = [NSMutableArray array];
  }
  return self;
}

- (void)constructionFlutterController:(UIViewController *)flutterViewController {
  flutterViewController.lsl_prefersNavigationBarHidden = YES;
}

- (void) pushFlutterRoute:(FBFlutterViewContainer *)flutterViewContainer nav:(UINavigationController *)navVC isAnimated:(BOOL)isAnimated{
  if([flutterViewContainer.name isEqualToString:@"cableCanvas"]){
    NSMutableDictionary *parms = flutterViewContainer.params.mutableCopy;
    NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
    parms[@"fontPath"] = fontPath;
    flutterViewContainer.params = parms;
    NSMutableArray *vcArray = nil;
    BOOL isNeedRemove = [navVC.viewControllers.lastObject isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer*)navVC.viewControllers.lastObject).name isEqualToString:@"canvas"];
    if(isNeedRemove){
      NSString *templateStr = parms[@"jsonData"];
      if(!STR_IS_NIL(templateStr)){
        JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:templateStr error:nil];
        templateData.idStr = templateData.profile.extrain.labelId;
        templateData.profile.extrain.templateType = @"1";
        templateData.profile.extrain.templateClass = @"0";
        parms[@"jsonData"] = templateData.toJSONString;
        flutterViewContainer.params = parms;
      }
      vcArray = [NSMutableArray arrayWithArray:
                 [navVC.viewControllers safeSubarrayWithRange:NSMakeRange(0, navVC.viewControllers.count - 1)]];
      [vcArray addObject:flutterViewContainer];
    }
    [navVC pushViewController:flutterViewContainer animated:isAnimated];
    if(isNeedRemove) navVC.viewControllers = vcArray;
  }else if([flutterViewContainer.name isEqualToString:@"canvas"]){
    BOOL isNeedRemove = [navVC.viewControllers.lastObject isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer*)navVC.viewControllers.lastObject).name isEqualToString:@"cableCanvas"];
    NSMutableArray *vcArray = nil;
    if(isNeedRemove){
      vcArray = [NSMutableArray arrayWithArray:
                 [navVC.viewControllers safeSubarrayWithRange:NSMakeRange(0, navVC.viewControllers.count - 1)]];
      [vcArray addObject:flutterViewContainer];
    }
    [navVC pushViewController:flutterViewContainer animated:isAnimated];
    if(isNeedRemove) navVC.viewControllers = vcArray;
  }else{
    [navVC pushViewController:flutterViewContainer animated:isAnimated];
  }
}

- (void)flutterToNativeVipPagearguments:(NSDictionary *)arguments {
  UIViewController *currentVC = [XYTool getCurrentVC];
  XYNavigationController *nav = (XYNavigationController *)currentVC.navigationController;
  NSString *content = arguments[@"vipType"];
  XYNormalBlock iapBlock = ^(){
    [JCIAPHelper openViewWithAlert:currentVC
                       needOpenTip:NO
                    isUseVipSource:NO
                           success:^{
      // 回调 Flutter 层
      if(![content isEqualToString:@"cable"]){
        if(xy_isLogin)
          [[FlutterBoost instance] sendResultToFlutterWithPageName:@"ToVipPage"
                                                         arguments:@{@"result": @1}];
      }
    } failure:^(NSString *msg, id model) {
      //            result({@"result": @0});
    } sourceInfo:@{@"isPrintHistory":@(1)}];
  };
  if([content isEqualToString:@"cable"]){
    if(![XYCenter sharedInstance].isDeviceVip){
      [[JCIAPHelper sharedInstance] setVipIAPSence:CableVip];
    }
    if(![XYCenter sharedInstance].isSupportAnonymityBuyVip){
      iapBlock();
    }else{
      [[JCLoginManager sharedInstance] checkLogin:^{

      } viewController:currentVC loginSuccessBlock:^{
        UserModel *user = m_userModel;
        BOOL isCableVip = NO;
        //判断是否属于线缆VIP false 继续执行购买
        for (NSDictionary *vipInfo in user.products) {
          NSNumber *cableVipValid = vipInfo[@"valid"];
          NSString *productCode = vipInfo[@"productCode"];
          if(cableVipValid.integerValue == 1 && [productCode isEqualToString:@"CABLE_VIP"]){
            [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
            isCableVip = YES;
            break;
          }
        }
        if(!isCableVip){
          [[JCIAPHelper sharedInstance] setVipIAPSence:CableVip];
          iapBlock();
        }
      }];
    }
  }
}

- (BOOL)pushNativeRoute:(NSString *)pageName
              arguments:(NSDictionary *)arguments {

  UIViewController *currentVC = [XYTool getCurrentVC];
  XYNavigationController *nav = (XYNavigationController *)currentVC.navigationController;
  if ([pageName isEqualToString:@"ToVipPage"]) {
    // 进入 VIP 支付页并返回结果
    // 支付成功返回 1, 失败返回 0
    NSString *content = arguments[@"vipType"];
    XYNormalBlock iapBlock = ^(){
      [JCIAPHelper openViewWithAlert:currentVC
                         needOpenTip:NO
                      isUseVipSource:NO
                             success:^{
        // 回调 Flutter 层
        if(![content isEqualToString:@"cable"]){
          if(xy_isLogin)
            [[FlutterBoost instance] sendResultToFlutterWithPageName:pageName
                                                           arguments:@{@"result": @1}];
        }
      } failure:^(NSString *msg, id model) {
        //            result({@"result": @0});
      } sourceInfo:@{@"isPrintHistory":@(1)}];
    };
    if([content isEqualToString:@"cable"]){
      if(![XYCenter sharedInstance].isDeviceVip){
        [[JCIAPHelper sharedInstance] setVipIAPSence:CableVip];
      }
      if([XYCenter sharedInstance].isSupportAnonymityBuyVip){
        iapBlock();
      }else{
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:currentVC loginSuccessBlock:^{
          UserModel *user = m_userModel;
          BOOL isCableVip = NO;
          //判断是否属于线缆VIP false 继续执行购买
          for (NSDictionary *vipInfo in user.products) {
            NSNumber *cableVipValid = vipInfo[@"valid"];
            NSString *productCode = vipInfo[@"productCode"];
            if(cableVipValid.integerValue == 1 && [productCode isEqualToString:@"CABLE_VIP"]){
              [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
              isCableVip = YES;
              break;
            }
          }
          if(!isCableVip){
            [[JCIAPHelper sharedInstance] setVipIAPSence:CableVip];
            iapBlock();
          }
        }];
      }
    }else{
      [[JCIAPHelper sharedInstance] setVipIAPSence:NormalVip];
      iapBlock();
    }


  } else if ([pageName isEqualToString:@"ToTemplatePage"]) {
#pragma mark 这里的 进入画板界面--这里目前只有 打印记录 用到
    XYWeakSelf
    if(!isAbleToCanvs) return YES;
    NSString *content = arguments[@"content"];
    NSString *linkedData = arguments[@"linkedData"];
    NSString *excelId = @"";
    NSString * fromSource = arguments[@"source"];
    NSString * printChannelCode = arguments[@"printChannelCode"];
    NSString * uniqueId = arguments[@"uniqueId"];
    if(!STR_IS_NIL(linkedData)){
      NSDictionary *linkInfo = [linkedData xy_toDictionary];
      excelId = linkInfo[@"excelId"];
    }

    BOOL isNewVersion = false;
    if (arguments[@"isNewVersion"] != nil && [arguments[@"isNewVersion"] boolValue] == true) {
      isNewVersion = true;
    }
    JSONModelError *error;
    JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:content
                                                                    error:&error];
    if(!STR_IS_NIL(templateData.profile.extrain.labelId)){
      uploadMostRecentlyUsedTemplate(templateData.profile.extrain.labelId);
    }
    TemplateSource source = TemplateSource_Mine;
    if(arguments[@"source"] != nil && [arguments[@"source"] isEqualToString:@"etag"]){
      source = TemplateSource_ETag;
    }else if([printChannelCode isEqualToString:@"printRecord"]){
      NSString *printHistoryCache = [[NSUserDefaults standardUserDefaults] stringForKey:[NSString stringWithFormat:@"print_history_%@",uniqueId]];
      if(!STR_IS_NIL(printHistoryCache)){
        templateData = [[JCTemplateData alloc] initWithString:printHistoryCache error:&error];
      }else{
        templateData.idStr = [XYTool randomElementId];
      }
      source = TemplateSource_History;
      templateData.isPrintHistory = YES;
    }
    isAbleToCanvs = NO;
    BOOL isFromMyTemplate = NO;
    UIViewController *viewController = [XYTool getCurrentVC];
    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && ([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"myTemplate"])){
      isFromMyTemplate = YES;
      printChannelCode = @"myTemplate";
    }
    if(isFromMyTemplate){
      [JCTemplateFunctionHelper checkFontDownload:templateData complate:^(){
        isAbleToCanvs = YES;
        if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:templateData.templateVersion] == false){
          [JCTemplateFunctionHelper showTemplteNeedUpgrade:templateData];
          return;
        }
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          if(!STR_IS_NIL(excelId) && !([templateData.dataSource isKindOfClass:[NSArray class]] && templateData.dataSource.count > 0)){
            [self addExcelDataWith:excelId complate:^(NSDictionary *excelData) {
              templateData.externalData = excelData;
              [JCFlutter2NativeHandler toFlutterCanvasWith:templateData type:TemplateSource_Mine channel:printChannelCode];
            }];
          }else{
            [JCFlutter2NativeHandler toFlutterCanvasWith:templateData type:TemplateSource_Mine channel:printChannelCode];
          }
        });

      }];
      return YES;
    }
    XYBlock toCanvasBlock = ^(JCTemplateData *currentTemplate){
      if (currentTemplate != nil && error == nil) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          if(!STR_IS_NIL(excelId) && !([currentTemplate.dataSource isKindOfClass:[NSArray class]] && currentTemplate.dataSource.count > 0)){
            [self addExcelDataWith:excelId complate:^(NSDictionary *excelData) {
              currentTemplate.externalData = excelData;
              [weakSelf toCanvasEditWithTemplateData:currentTemplate source:source channel:printChannelCode uniqueId:UN_NIL(uniqueId)];
            }];
          }else{
            [weakSelf toCanvasEditWithTemplateData:currentTemplate source:source channel:printChannelCode uniqueId:UN_NIL(uniqueId)];
          }
        });
      }else{
        isAbleToCanvs = YES;
      }
    };
    toCanvasBlock(templateData);
  }else if([pageName isEqualToString:@"TemplateDetailsPage"]){
    NSString *templateIdValue = arguments[@"templateId"];
    NSNumber *isFolderShare = arguments[@"isFolderShare"];
    JCMyTemplateDetailViewController *vc = [[JCMyTemplateDetailViewController alloc] initWithTemplateId:templateIdValue sourceType:isFolderShare.boolValue?@"3": @"2"];
    vc.refreshShareBlock = ^(NSInteger refreshType){
      [[FlutterBoost instance] sendResultToFlutterWithPageName:pageName
                                                     arguments:@{@"refreshType":@(refreshType)}];
    };
    UIViewController * rootController = [XYTool getKeyWindow].rootViewController;
    if([rootController isKindOfClass:[XYTabBarController class]]){
      UIViewController *top = ((XYTabBarController *)rootController).selectedViewController;
      if ([top isKindOfClass: [UINavigationController class]]) {
        [((UINavigationController *)top) pushViewController:vc animated:true];
      } else {
        [top presentViewController:vc animated:true completion:nil];
      }
    }

  }
  else if([pageName isEqualToString:@"toBatchPrintSettingPage"]){
    NSArray *printInfos = arguments[@"printInfos"];
    NSMutableArray *templateIds = [NSMutableArray array];
    for (NSDictionary *printInfo in printInfos) {
      NSString *templateId = printInfo[@"id"];
      [templateIds addObject:templateId];
    }
    [[JCBatchPrintSourceManager sharedManager] isNeedReDownload:templateIds completion:^(BOOL needReDownload) {
      if(needReDownload){
        [[JCBatchPrintSourceManager sharedManager] downloadBatchPrintSource:templateIds];
      }
      [[JCBatchPrintSourceManager sharedManager] getBatchPrintTemplates:printInfos completion:^(NSArray *templates) {
        [self toPrintWithTemplateData:templates
                          printCopies:@"1"
                            historyId:nil
                         isFromCanvas:NO
                     isFromBatchPrint:YES
                        isFolderShare:NO];
      }];
    }];
  }
  else if ([pageName isEqualToString:@"ToPrintSettingPage"]) {
    // 打印设置页
    if(isReadyToPrintPage){
      return YES;
    }
    isReadyToPrintPage = YES;
    XYWeakSelf
    NSString *content = arguments[@"content"];
    NSString *uniqueId = arguments[@"uniqueId"];
    NSString *linkedData = [arguments[@"linkedData"] isEqualToString:@"{}"] ? @"" : arguments[@"linkedData"];
    NSString *excelId = @"";
    // 从画板跳入无需从网络下载资源且默认可编辑
    NSString *isFromCanvas = arguments[@"isFromCanvas"];
    BOOL isFolderShare = ((NSNumber *)arguments[@"isFolderShare"]).boolValue;
    NSArray *pdfBindInfos = arguments[@"pdfBindInfo"];
    if([pdfBindInfos isKindOfClass:[NSArray class]] && pdfBindInfos.count > 0){
      NSMutableArray *pdfBindInfoModels = [NSMutableArray array];
      for (NSDictionary *bindInfo in pdfBindInfos) {
        JCPDFBindInfo *model = [[JCPDFBindInfo alloc] initWithDictionary:bindInfo error:nil];
        [pdfBindInfoModels addObject:model];
      }
      [JCPDFPrintSourceManager sharedInstance].pdfBindInfos = pdfBindInfoModels;
    }else{
      [JCPDFPrintSourceManager sharedInstance].pdfBindInfos = nil;
    }
    //        [JCPrintManager sharedInstance].printChannelCode = arguments[@"printChannelCode"];
    // RFID信息保存
    BOOL isShowRfid = ((NSNumber *)arguments[@"showRfid"]).boolValue;
    [JCRFIDSourceManager sharedInstance].isShowRFID = isShowRfid;
    // RFID绑定信息
    NSString *rfidInfo = arguments[@"rfidInfo"];
    if(!STR_IS_NIL(rfidInfo)) {
      NSError *error;
      JCRFIDDataModel *rfidModel = [[JCRFIDDataModel alloc] initWithString:rfidInfo error:&error];
      [JCRFIDSourceManager sharedInstance].sourceModel = rfidModel;
    } else {
      // 防止脏数据污染
      [JCRFIDSourceManager sharedInstance].sourceModel = nil;
    }
    if(!STR_IS_NIL(linkedData)){
      NSDictionary *linkInfo = [linkedData xy_toDictionary];
      excelId = linkInfo[@"excelId"];
    }
    // 默认打印一份
    NSNumber *copies = @1;
    if (arguments[@"copies"] != nil && ![arguments[@"copies"] isEqual: @0]) {
      copies = arguments[@"copies"];
    }
    BOOL isNewVersion = false;
    if (arguments[@"isNewVersion"] != nil && [arguments[@"isNewVersion"] boolValue] == true) {
      isNewVersion = true;
    }

    JSONModelError *error;
    JCTemplateData *templateData;
    if(!STR_IS_NIL(uniqueId)){
      NSString *printHistoryCache = [[NSUserDefaults standardUserDefaults] stringForKey:[NSString stringWithFormat:@"print_history_%@",uniqueId]];
      if(!STR_IS_NIL(printHistoryCache)){
        templateData = [[JCTemplateData alloc] initWithString:printHistoryCache error:&error];
      }else{
        templateData = [[JCTemplateData alloc] initWithString:content error:&error];
        templateData.idStr = [XYTool randomElementId];
      }
      templateData.isPrintHistory = YES;
    }else{
      templateData = [[JCTemplateData alloc] initWithString:content error:&error];
    }
    BOOL isFromMyTemplate = NO;
    UIViewController *viewController = [XYTool getCurrentVC];
    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && ([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"myTemplate"])){
      isFromMyTemplate = YES;
    }
    //打印历史记录进打印界面的时候需要维持原有的currentPage
    if(!isFromCanvas && !isNewVersion && !isFromMyTemplate){
      templateData.currentPage = 1;
    }
    if (templateData != nil && error == nil) {
      //新版本由于flutter段解析规则和服务端解析externalData规则不一致，以客户端为准
      XYBlock toPrintTemplateHistory = ^(JCTemplateData *template){
        isReadyToPrintPage = NO;
        if((!STR_IS_NIL(excelId) && [excelId integerValue] > 0) && !([template.dataSource isKindOfClass:[NSArray class]] && template.dataSource.count > 0)){
          [self addExcelDataWith:excelId complate:^(NSDictionary *excelData) {
            templateData.externalData = excelData;
            [weakSelf toPrintWithTemplateData:@[template]
                                  printCopies:StringFromInt(copies.integerValue)
                                    historyId:uniqueId
                                 isFromCanvas:isFromCanvas
                             isFromBatchPrint:NO
                                isFolderShare:isFolderShare
            ];
          }];
        }else{
          [self toPrintWithTemplateData:@[template]
                            printCopies:StringFromInt(copies.integerValue)
                              historyId:uniqueId
                           isFromCanvas:isFromCanvas
                       isFromBatchPrint:NO
                          isFolderShare:isFolderShare
          ];
        }
        if(arguments[@"isFolderShare"] != nil){

          [JCPrintManager sharedInstance].isFolderSharePrint = isFolderShare;
        }
      };
      if(templateData.isPrintHistory){
        [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
          if([resultData checkTemplateComplate]){
            NSString *printHistoryCacheKey = [NSString stringWithFormat:@"print_history_%@",uniqueId];
            NSString *templateJsonStr = resultData.toJSONString;
            [[NSUserDefaults standardUserDefaults] setObject:templateJsonStr forKey:printHistoryCacheKey];
          }
          toPrintTemplateHistory(resultData);
        }];
      }else{
        toPrintTemplateHistory(templateData);
      }

    }
  } else if ([pageName isEqualToString:@"ScanBarcode"]) {
    // 扫码页
    void (^scanBlock)() = ^{
      // 获取参数
      NSString *scanType = arguments[@"scanType"];
      QQLBXScanViewController *vc = [[QQLBXScanViewController alloc] init];
      vc.libraryType = [Global sharedManager].libraryType;
      vc.scanCodeType = [Global sharedManager].scanCodeType;
      vc.scanCreateType = STR_IS_NIL(scanType) ? 6 : scanType.integerValue;
      vc.style = [StyleDIY qqStyle];
      __weak QQLBXScanViewController *weakVC = vc;
      vc.scanResultBlock = ^(LBXScanResult *result) {
        NSString *code = result.strScanned;
        // 返回结果到 Flutter
        NSString *codeTypeStr = result.strBarCodeType;
        NSString *codeType = @""; // 1 条码 2 二维码
        NSString *resultSuccess = @"0"; // 1 识别成功 0 失败
        NSString *commodityBarcode = @""; // 是否是商品数据
        if(!STR_IS_NIL(code)){
          if([codeTypeStr hasSuffix:@"PDF417"] || [codeTypeStr hasSuffix:@"DataMatrix"] || [codeTypeStr hasSuffix:@"Aztec"] || [codeTypeStr hasSuffix:@"QRCode"]){
            codeType = @"2";
          }else{
            codeType = @"1";
          }
          if([codeTypeStr hasSuffix:@"EAN-13"]){
            commodityBarcode = code;
          }
        }
        NSDictionary *etDic = @{@"source":@"3",@"type":codeType,@"s_type":@"2",@"commodity_barcode":commodityBarcode};
        NSMutableDictionary *scanInfoDic = [NSMutableDictionary dictionaryWithDictionary:etDic];
        [scanInfoDic setValue:code ?code: @"" forKey:@"value"];
        [[FlutterBoost instance] sendResultToFlutterWithPageName:pageName
                                                       arguments:scanInfoDic];
        [weakVC dismissViewControllerAnimated:YES
                                   completion:nil];
      };
      // 镜头拉远拉近功能
      vc.isVideoZoom = YES;
      XYNavigationController *navVC = [[XYNavigationController alloc] initWithRootViewController:vc];
      [currentVC presentViewController:navVC animated:YES
                            completion:nil];
    };

    // 检查摄像头权限
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
      [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{

      } sureBlock:^{
        NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if([[UIApplication sharedApplication] canOpenURL:url]) {
          NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
          [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
        }
      }];
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
      [AVCaptureDevice requestAccessForMediaType:mediaType completionHandler:^(BOOL granted) {
        if(granted){
          dispatch_async(dispatch_get_main_queue(), ^{
            scanBlock();
          });
        }
      }];
    }else{
      scanBlock();
    }

  } else if ([pageName isEqualToString: @"CanvasPage"]) {
#pragma mark 这里的 进入画板界面--这里目前只有 行业模版 用到
    bool isCustomLabel = ((NSNumber *)arguments[@"isCustomLabel"]).boolValue;
    NSString *jsonStr = ((NSDictionary *)arguments[@"templateData"]).xy_toJsonString;
    __block JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:arguments[@"templateData"] error:nil];
    bool isCreate = !templateData.elements.isNotEmpty;
    __block JCTemplateData *labelData = [[JCTemplateData alloc] initWithDictionary:arguments[@"labelData"] error:nil];
    if (isCustomLabel) {
      // 如果是自定义的标签纸需要把模版的elements带入到LabelData上面, 无需上传历史记录
      labelData.elements = templateData.elements;
      labelData.vip = templateData.vip;
      labelData.hasVipRes = templateData.hasVipRes;
      labelData.idStr = [XYTool randomElementId];
      labelData.profile.extrain.templateType = [templateData isBusinessTemplate]?@"2":@"0";//
      if(templateData.canvasRotate != 0){
        if((templateData.canvasRotate == 90 || templateData.canvasRotate == 270)){
          float width = labelData.width;
          labelData.width = labelData.height;
          labelData.height = width;
        }
        labelData.canvasRotate = templateData.canvasRotate;
        NSInteger rotate = labelData.rotate + templateData.canvasRotate/90 * 270;
        labelData.rotate = rotate%360;
      }
    }
    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    if (labelData != nil) {
      // 登陆后上传使用记录
      if(xy_isLogin){
        uploadMostRecentlyUsedTemplate(labelData.idStr);
      }
      if (isCustomLabel) {
        // 自定义标签纸直接跳转，无须混入
        if(NETWORK_STATE_ERROR){
          labelData.idStr = [XYTool randomElementId];//由云模版创建个人模板 建立临时id
          [self jumpToTemplateEditControllerWith:labelData isFromCreate:isCreate isIndustryTemplate:true];
          [self.progressHUD hideAnimated:NO];
        }else{
          [self jumpToCanvasPage:labelData isFromCreate:isCreate isIndustryTemplate:true]; //3
        }
        return YES;
      }
      templateData.idStr = [XYTool randomElementId];//由云模版创建个人模板 建立临时id
      // 下载模版数据
      [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultTemplateData) {
        [JCTemplateImageManager downLoadImagesForData:labelData options:DownAll complete:^(JCTemplateData *resultLabelData) {
          JCTemplateData *newData = [JCTMDataBindGoodsInfoManager templateOldData:resultTemplateData replaceWithLabelData:resultLabelData];
          [self.progressHUD hideAnimated:NO];
          [self jumpToCanvasPage: newData isFromCreate:isCreate isIndustryTemplate:true]; //1
        }];
      }];
    } else {
      // 上传使用记录
      if(!STR_IS_NIL(templateData.profile.extrain.labelId)){
        uploadMostRecentlyUsedTemplate(templateData.profile.extrain.labelId);
      }
      XYBlock sourceLoadBlock = ^(JCTemplateData *templateModel){
        templateModel = [JCTMDataBindGoodsInfoManager templateDataWith:templateModel goodInfo:nil];
        //              templateModel.usedFonts = resultData.getNeedFonts;
        [self jumpToCanvasPage:templateModel isFromCreate:isCreate isIndustryTemplate:true]; //2
      };
      [JCTemplateFunctionHelper getTemplateDetailRequest:templateData complate:^(JCTemplateData *requestData) {
        if(requestData != nil){
          if(requestData.elements.count == 0 && templateData.elements.count > 0){
            requestData.elements = templateData.elements;
          }
          sourceLoadBlock(requestData);
        }else{
          sourceLoadBlock(templateData);
        }
      }];
    }

  } else if ([pageName isEqualToString:@"DeviceConnectPage"]) {
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)currentVC.navigationController  isFromHome:NO];

  } else if ([pageName isEqualToString:@"TemplateSourceNew"]) {
#pragma mark 进入画板界面--目前只有行业模版&&新建&&PDF外部导入
    isCanCreate = NO;
    NSLog(@"卡屏调试:准备进入画板，不可创建");
    bool isCustomLabel = ((NSNumber *)arguments[@"isCustomLabel"]).boolValue;
    
    // 解析文件信息
    NSDictionary *fileInfo = arguments[@"fileInfo"];
    
    JCTemplateData *labelData;
    //labelData为空则取当前打印机连接内的标签纸信息
    if (((NSDictionary *)arguments[@"labelData"]).isNotEmpty) {
      labelData = [[JCTemplateData alloc] initWithDictionary:arguments[@"labelData"] error:nil];
      if(STR_IS_NIL(labelData.idStr)){
        labelData.idStr = [XYTool randomElementId];
        [self jumpToEditControllerWith:labelData isFromCreate:isCustomLabel fileInfo:fileInfo];
      }else{
        [JCTemplateFunctionHelper getTemplateDetailRequest:labelData complate:^(id x) {
          //              labelData.idStr = [XYTool randomElementId];
          [self jumpToEditControllerWith:labelData isFromCreate:isCustomLabel fileInfo:fileInfo];
        }];
      }
    } else {
      labelData = [JCPrintManager sharedInstance].rfidTemplateData;
      [self jumpToEditControllerWith:labelData isFromCreate:isCustomLabel fileInfo:fileInfo];
    }
  } else if ([pageName isEqualToString:@"toCanvasPage"]) { //新版版式入口
#pragma mark 进入画板界面--目前只有行业模版&&新建&&PDF外部导入
    isCanCreate = NO;
    NSLog(@"卡屏调试:准备进入画板，不可创建");
    bool isCustomLabel = ((NSNumber *)arguments[@"isCustomLabel"]).boolValue;
    
    // 解析文件信息
    NSDictionary *fileInfo = arguments[@"fileInfo"];
    
    JCTemplateData *labelData;
    //labelData为空则取当前打印机连接内的标签纸信息
    if (((NSDictionary *)arguments[@"labelData"]).isNotEmpty) {
      labelData = [[JCTemplateData alloc] initWithDictionary:arguments[@"labelData"] error:nil];
      if(STR_IS_NIL(labelData.idStr)){
        labelData.idStr = [XYTool randomElementId];
        [self jumpToEditControllerWith:labelData isFromCreate:isCustomLabel fileInfo:fileInfo];
      }else{
        saveRecentlyUsedRecord(UN_NIL(labelData.profile.extrain.labelId));
        [JCTemplateImageManager downLoadImagesForData:labelData options:DownAll complete:^(JCTemplateData *resultData)  {
          isCanCreate = YES;
          //根据用户线缆画板切换偏好 跳转
          NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
          if(!resultData.isCableLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [canvasType isEqualToString:@"normalCanvas"]){
            // 是否是危废标签纸
            if (!resultData.isDangerLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [canvasType isEqualToString:@"normalCanvas"]) {
              [JCFlutter2NativeHandler toFlutterCanvasWith:resultData type:TemplateSource_New fileInfo:fileInfo];
            } else {
              if (NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return;
              }
              [[JCLoginManager sharedInstance] checkLogin:^{
              } viewController:nil loginSuccessBlock:^{
                [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:UniAppTrackID_dangerCap needKeepLive:YES parms:nil receiveUniappData:^(id x) {

                }];
              }];
            }
          }else{
            NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
            NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
            if(STR_IS_NIL(canvasType) || [canvasType isEqualToString:@"cableCanvas"]){
              dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:@{@"jsonData": resultData.toJSONString,@"isPresent": @NO,@"fontPath": fontPath,
                                                                                @"isEnablePopGesture": @NO} onPageFinished:^(NSDictionary *dic) {

                }];
              });

            }else{
              [JCFlutter2NativeHandler toFlutterCanvasWith:resultData type:TemplateSource_New fileInfo:fileInfo];
            }
          }
        }];
      }
    } else {
      labelData = [JCPrintManager sharedInstance].rfidTemplateData;
      [self jumpToEditControllerWith:labelData isFromCreate:isCustomLabel fileInfo:fileInfo];
    }
  }  else if ([pageName isEqualToString:@"toAssetVipDetail"]) {
    NMFAWebViewController *webViewController = [[NMFAWebViewController alloc] init];
    webViewController.isSupportShare = NO;
    [webViewController loadUrl:@"https://ydygdzc.niimbot.com/h5/third-page/check-vip/index"];
    UIViewController *topVc = [XYTool getCurrentVC];
    [topVc.navigationController pushViewController:webViewController animated:YES];
  }
  if([pageName isEqualToString:_toBluetoothConnectEvent]){
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:nav  isFromHome:NO];
  }
  if([pageName isEqualToString:_toMyTemplatesEvent]){
    [self pushMyTemplete:currentVC];
  }
  if([pageName isEqualToString:_toMyPrintHistoryEvent]){
    [self pushPrintHistory:currentVC];
  }if([pageName isEqualToString:_toMessageCenter]){
    [self pushMessageCenter:currentVC];
  }
  if([pageName isEqualToString:_toNewUserGuideEvent]){
    [self pushToFlutterNewUserGuide:currentVC];
  }
  if([pageName isEqualToString:_toHelpCenterEvent]){
    [self pushHelp:currentVC];
  }
  if([pageName isEqualToString:_toSystemDockingServiceEvent]){
    [self pushSystemDockingService:currentVC];
  }
  if([pageName isEqualToString:_toInvestmentAgentEvent]){
    [self pushInvestmentAgent:currentVC];
  }
  if([pageName isEqualToString:_toFadebackEvent]){
    [self pushToQAVC:currentVC];
  }
  if([pageName isEqualToString:_toAboutUSEvent]){
    [self pushAboutUS:currentVC];
  }
  if([pageName isEqualToString:_toLoginOrUserCenterEvent]){
    [self toLoginOrUserCenter:currentVC];
  }
  if([pageName isEqualToString:_toAppSettingrEvent]){
    [self pushSetting:currentVC];
  }
  if([pageName isEqualToString:_toRenewVipEvent]){
    [self renewVip];
  }
  if([pageName isEqualToString:_toOpenVipEvent]){
    [self openNiimbotVip:currentVC];
  }
  if([pageName isEqualToString:_toMergeVipRightEvent]){
    [self moveNiimbotVipRight:currentVC];
  }
  if([pageName isEqualToString:_toObligationsEvent]){
    [self pushToObligations:currentVC];
  }
  if([pageName isEqualToString:_toBeReceivedEvent]){
    [self pushToBeReceived:currentVC];
  }
  if([pageName isEqualToString:_toReturnAfterSaleEvent]){
    [self pushToReturnAfterSale:currentVC];
  }
  if([pageName isEqualToString:_toShopOrdersEvent]){
    [self pushMyOrders:currentVC];
  }
  if([pageName isEqualToString:_toBuyLabelsEvent]){
    //TODO 购买标签
  }
  if([pageName isEqualToString:_toShopPointsEvent]){
    //
    [JCFlutter2NativeHandler pushToShopPoints:currentVC];
  }
  if([pageName isEqualToString:_toShopCouponEvent]){
    [self pushToShopCoupons:currentVC];
  }
  if([pageName isEqualToString:_toShopUserAddressEvent]){
    [self pushUserAddress:currentVC];
  }
  if([pageName isEqualToString:_toNPSGradeEvent]){
    [self toNPSGrade:currentVC];
  }
  if([pageName isEqualToString:_toAnotherOrderEvent]){
    //页面跳转 至 "再来一单"
    [self toAnotherOrderWebPageWith:arguments navVC:nav];

  }
  if([pageName isEqualToString:_toMeDevicesPage]){
    [self pushToMyDevicePage:currentVC];
  }
  if([pageName isEqualToString:_toEtagPage]){
    [self pushToEtagPage:currentVC];
  }
  if([pageName isEqualToString:_toSpecBenefitsEvent]){
    //页面跳转 至 "精臣专属福利"
  }
  if([pageName isEqualToString:_toMyDocument]){
    [self toDocument:currentVC];
  }
  if([pageName isEqualToString:_toImPageEvent]){
    NSString *imLink = arguments[@"imLink"];
    NMWebViewController *webViewController = [[NMWebViewController alloc] init];
    webViewController.isSupportShare = NO;
    [webViewController loadUrl:imLink];
    [currentVC.navigationController pushViewController:webViewController animated:YES];
  }

  if([pageName isEqualToString:_toETagConnectPage]){
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:nav  isFromHome:NO];
  }
  if([pageName isEqualToString:@"ToBuyLabelPage"]){
    NSString *link = arguments[@"link"];
    [self jumpShopNormalVCWith:link];
  }
  if([pageName isEqualToString:@"toOtherApp"]){
    NSString *packageName = arguments[@"packageName"];
    NSArray *appArr = [XYCenter sharedInstance].appArr;
    JCLabelAppModel *currentAppModel = nil;
    for (JCLabelAppModel *appModel in appArr) {
      if ([appModel.packageName isEqualToString:packageName]) {
        currentAppModel = appModel;
        break;
      }
    }
    if (currentAppModel == nil) {
      if([packageName isEqualToString:@"com.niimbot.pc"]){
        JCLabelAppModel *currentAppModel = [[JCLabelAppModel alloc] init];
        currentAppModel.packageName = @"com.niimbot.pc";
        currentAppModel.dialogTitle = @"app100001759";/*  @"【精臣云打印】电脑端";*/
        currentAppModel.dialogMessage = @"app100001760";/*@"精臣云打印电脑端支持精臣全系列商用智能标签打印机，目前仅支持Windows系统安装使用。";*/
        currentAppModel.icon = @"https://oss-print.niimbot.com/public_resources/material/dcdde35f15dc61e0bf2b6ba24040056c.png";/*@"https://oss-print.niimbot.com/public_resources/material/dcdde35f15dc61e0bf2b6ba24040056c.png";*/
        [JCToNativeRouteHelp jumpToOtherAppWithAppModel:currentAppModel];
      }else{
        [MBProgressHUD showToastWithMessageDarkColor:@""];
      }
    }else{
      [JCToNativeRouteHelp jumpToOtherAppWithAppModel:currentAppModel];
    }
  }
  if([pageName isEqualToString:_toC1ConnectPage]){
    [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:nav isFromHome:NO];
  }
  if ([pageName isEqualToString:_toC1PrintPage]) {
    // C1打印页
    NSError *error;
    JCC1PrintInfo *info = [[JCC1PrintInfo alloc] initWithDictionary:arguments error:&error];
    [self toC1PrintWithPrintInfo:info];
  }
  if([pageName isEqualToString:_toMyAppEvent]){
    [self pushMyApplication:currentVC];
  }
  return YES;
}
static BOOL isSavingTemplate = NO;
static BOOL isReadyToPrintPage = NO;
static BOOL isAbleToCanvs = YES;
//跳转商城
-(void)jumpShopNormalVCWith:(NSString *)link{
  UIViewController *topVc = [XYTool getCurrentVC];
  //    if(!xy_isLogin){
  //        XYWeakSelf
  //        [[JCLoginManager sharedInstance] checkLogin:^{
  //        } viewController:topVc loginSuccessBlock:^{
  //            [weakSelf jumpShopNormalVCWith:link];
  //        }];
  //        return;
  //    }
  //    if(NETWORK_STATE_ERROR){
  //        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
  //        return;
  //    }
  //
  //    JCShopNormalVC * shopVc = [[JCShopNormalVC alloc]initWithShopAppointUrl:link];
  //    shopVc.entrance_type_id = @"3";
  //    if(topVc.navigationController == nil){
  //        XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:shopVc];
  //        [topVc presentViewController:nav animated:YES completion:^{
  //
  //        }];
  //    }else{
  //        [topVc.navigationController pushViewController:shopVc animated:YES];
  //    }

  float delayTime = 0;
  if(!xy_isLogin) {
    delayTime = 1;
  }
  [[JCLoginManager sharedInstance] checkLogin:^{

  } viewController:[XYTool getCurrentVC] loginSuccessBlock:^{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      JCShopNormalVC * shopVc = [[JCShopNormalVC alloc]initWithShopAppointUrl:link];
      shopVc.entrance_type_id = @"3";
      if(topVc.navigationController == nil){
        XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:shopVc];
        [topVc presentViewController:nav animated:YES completion:^{

        }];
      }else{
        [topVc.navigationController pushViewController:shopVc animated:YES];
      }
    });
  }];

}

//续费
- (void)renewVip{
  XYWeakSelf
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  if(xy_isLogin && STR_IS_NIL(m_userModel.phone) && STR_IS_NIL(m_userModel.email)){
    [[JCLoginManager sharedInstance] checkBindWithviewController:[XYTool getCurrentVC] withBindType:1 withResponse:^{} withComplete:^(id x) {

    }];
    return;
  }
  [JCIAPHelper renewUserVIPWith:[XYTool getCurrentVC]
                    showAllInfo:NO
                        success:^{
  }
                        failure:^(NSString *msg, id model) {
    [MBProgressHUD showToastWithMessageDarkColor:msg];
  }
                     sourceInfo:@{@"sourcePage":UN_NIL(@""),@"act_name":UN_NIL(@"")}];
}

- (void)toAnotherOrderWebPageWith:(NSDictionary *)params navVC:(UINavigationController *)nav{
  int orderType = [((NSNumber *)params[@"order_type"]) intValue]; //1. 普通(平台)订单; 2. 定制订单

  NSString *subPath = @"";
  NSDictionary *mParams = nil;
  if(orderType == 2){
    NSString *orderId = [NSString stringWithFormat:@"%@",params[@"purchase_id"]];
    NSString *goodsCount = [NSString stringWithFormat:@"%@",params[@"goodsCount"]];
    subPath = @"buyCustomPaper";
    mParams = @{@"purchase_id":orderId,@"goodsCount": goodsCount};
  }else{
    subPath = @"shoppingCart";
  }
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:subPath options:mParams];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"y_page_user_order_repurchase";
  [vc setIsNeedBackApp:YES];
  [nav pushViewController:vc animated:YES];
}

/**
 至模版编辑
 */
- (void)toCanvasEditWithTemplateData:(JCTemplateData *)data source:(TemplateSource)fromSource channel:(NSString *)printChannelCode uniqueId:(NSString *)uniqueId{
  UIViewController *currentVC = [XYTool getCurrentVC];
  [self downLoadImageImageAndFontsForTemplateData:data isForPrint:NO isEtag:fromSource == TemplateSource_ETag complete:^(JCTemplateData *templateData){
    isAbleToCanvs = YES;
    JCTemplateData *currentTemplate = [[JCTemplateData alloc] initWithString:[templateData toJSONString] error:nil];
    TemplateSource source = TemplateSource_History;
    if ([currentTemplate.profile.extrain.templateType isEqualToString:@"2"] && [currentTemplate.profile.extrain.userId isEqual:m_userModel.userid]) {
      source = TemplateSource_Mine;
    }
    if(fromSource == TemplateSource_ETag){
      source = TemplateSource_ETag;
    }else if(fromSource == TemplateSource_History){
      if([templateData checkTemplateComplate]){
        NSString *printHistoryCacheKey = [NSString stringWithFormat:@"print_history_%@",uniqueId];
        NSString *templateJsonStr = templateData.toJSONString;
        [[NSUserDefaults standardUserDefaults] setObject:templateJsonStr forKey:printHistoryCacheKey];
      }
    }
    if(![currentTemplate.profile.extrain.userId isEqualToString:m_userModel.userid]){
      currentTemplate.originTemplateId = data.idStr;
      //            data.sourceOfIndustryTemplateId = data.idStr;
    }
    //        [currentTemplate resetUnDownloadVipFontResource];
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:currentTemplate.templateVersion] == false){
      [JCTemplateFunctionHelper showTemplteNeedUpgrade:currentTemplate];
      return;
    }
    //        BOOL is_ableEdit_Template = [currentTemplate checkTemplateDetailByBackImage:YES containFont:NO];
    //        if(!is_ableEdit_Template){
    //          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000354", @"网络异常")];
    //          return;
    //        }
    if([currentTemplate isNewCommodityTemplate] && NETWORK_STATE_ERROR){
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    }
    [JCFlutter2NativeHandler toFlutterCanvasWith:currentTemplate type:source channel:printChannelCode];
  }];
}

static int doubleClickDelay = 0;
static NSString *defaultELementType = @"";
static BOOL isRouterLoadCanvas = NO;

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType isPresent:(BOOL)isPresent {
  [self toFlutterCanvasWith:model type:modelType channel:@"" isPresent:isPresent];
}

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType {
  [self toFlutterCanvasWith:model type:modelType isPresent:NO];
}

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType channel:(NSString *)printChannelCode {
  [self toFlutterCanvasWith:model type:modelType channel:printChannelCode isPresent:NO];
}

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType channel:(NSString *)printChannelCode isPresent:(BOOL)isPresent {
  [self toFlutterCanvasWith:model type:modelType channel:printChannelCode isPresent:isPresent fileInfo:nil];
}

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType fileInfo:(NSDictionary *)fileInfo {
  [self toFlutterCanvasWith:model type:modelType channel:@"" isPresent:NO fileInfo:fileInfo];
}

+ (void)toFlutterCanvasWith:(JCTemplateData *)model type:(NSInteger)modelType channel:(NSString *)printChannelCode isPresent:(BOOL)isPresent fileInfo:(NSDictionary *)fileInfo {
  if(doubleClickDelay >= 1){
    return;
  }
  doubleClickDelay = 1;
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(doubleClickDelay * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    doubleClickDelay = 0;
  });
  NSDictionary *arguments = @{};
  if(model != nil){
    NSString *jsonString = [model toJSONString];
    NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
    
    NSMutableDictionary *mutableArguments = [@{
      @"isPresent": [NSNumber numberWithBool:isPresent],
      @"isEnablePopGesture": @NO,
      @"fontPath": fontPath,
      @"jsonData": jsonString,
      @"token": m_userModel.token == nil ? @"" : m_userModel.token,
      @"needRfidTemplate":modelType == TemplateSource_New?@1:@0,
      @"language": XY_JC_LANGUAGE,
      @"need_download_fonts":@NO,
      @"source":modelType == TemplateSource_ETag ? @"etag" : @"",
      @"printChannelCode": printChannelCode,
    } mutableCopy];
    
    // 添加文件信息到arguments中
    if (fileInfo && [fileInfo isKindOfClass:[NSDictionary class]] && fileInfo.count > 0) {
      mutableArguments[@"fileInfo"] = fileInfo;
      NSLog(@"向画板传递文件信息: %@", fileInfo);
    }
    
    arguments = [mutableArguments copy];
  }else{
    NSMutableDictionary *mutableArguments = [@{
      @"isPresent": [NSNumber numberWithBool:isPresent],
      @"isEnablePopGesture": @NO,
      @"token": m_userModel.token == nil ? @"" : m_userModel.token,
      @"language": XY_JC_LANGUAGE,
      @"printChannelCode": printChannelCode,
    } mutableCopy];
    
    // 添加文件信息到arguments中
    if (fileInfo && [fileInfo isKindOfClass:[NSDictionary class]] && fileInfo.count > 0) {
      mutableArguments[@"fileInfo"] = fileInfo;
      NSLog(@"向画板传递文件信息: %@", fileInfo);
    }
    
    arguments = [mutableArguments copy];
  }
  NSMutableDictionary *canvasArguments = [NSMutableDictionary dictionaryWithDictionary:arguments];
  if(isRouterLoadCanvas){
    [canvasArguments setValue:defaultELementType forKey:@"defaultSelect"];
    [canvasArguments setValue:@YES forKey:@"need_download_fonts"];
  }
  [FlutterBoostUtility gotoFlutterPage:@"canvas"
                             arguments:canvasArguments
                        onPageFinished:^(NSDictionary *dic) {
    // 页面结束回传数据
    // B32R机型的RFID数据重置，存在非B32R机型的无用重置
    [JCRFIDSourceManager sharedInstance].sourceModel = nil;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [JCTemplateSaveManager checkAllOffLineData];
    });
  }];
}

+ (void)toFlutterCanvasNeedDownloadFontsWith:(JCTemplateData *)model type:(NSInteger)modelType{
  NSDictionary *arguments = @{};
  [model resetTemplateLocalPath];
  NSString *jsonString = [self canvasJsonDataWith:model];
  if(model != nil){
    NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
    arguments = @{
      @"isPresent": @NO,
      @"isEnablePopGesture": @NO,
      @"fontPath": fontPath,
      @"jsonData": jsonString,
      @"token": m_userModel.token == nil ? @"" : m_userModel.token,
      @"needRfidTemplate":modelType == TemplateSource_New?@1:@0,
      @"language": XY_JC_LANGUAGE,
      @"need_download_fonts":@YES,
      @"source": @"industryTemplate"
    };
  }else{
    arguments = @{
      @"isPresent": @NO,
      @"isEnablePopGesture": @NO,
      @"token": m_userModel.token == nil ? @"" : m_userModel.token,
      @"language": XY_JC_LANGUAGE,
    };
  }
  [FlutterBoostUtility gotoFlutterPage:@"canvas"
                             arguments:arguments
                        onPageFinished:^(NSDictionary *dic) {
    // 页面结束回传数据
    // B32R机型的RFID数据重置，存在非B32R机型的无用重置
    [JCRFIDSourceManager sharedInstance].sourceModel = nil;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [JCTemplateSaveManager checkAllOffLineData];
    });
  }];
}

+ (NSString *)canvasJsonDataWith:(JCTemplateData *)model{
  NSString *jsonString = @"";
  if(model != nil){
    JCGoodDetailInfo *goodsInfo = [model.goodsList safeObjectAtIndex:0];
    for (JCElementModel *elementModel in model.elements) {
      if([elementModel.type isEqualToString:@"image"]){
        // 点九图使用元素ID、普通的按照边框ID，没有则使用元素ID
        if ([[NSFileManager defaultManager] fileExistsAtPath:elementModel.localUrl]) {
        }else{
          // TODO: image Migration
          //                NSString *localUrlId = elementModel.isNinePatch ? elementModel.elementId : (!STR_IS_NIL(elementModel.materialId) ? elementModel.materialId : elementModel.elementId);
          //                elementModel.localUrl = ElementLocalPath(localUrlId, NO);
        }
        elementModel.imageData = @"";
      }else{
        //                elementModel.localUrl = ElementLocalPath(!STR_IS_NIL(elementModel.materialId) ? elementModel.materialId : elementModel.elementId, NO);
        //                // 离线的情况下，localUrl并没有保存，所以存在localUrl路径不存在，此时不可清除imageData，否则离线打开造成文件丢失
        //                if ([[NSFileManager defaultManager] fileExistsAtPath:elementModel.localUrl]) {
        //                    elementModel.imageData = @"";
        //                }
      }
      if(goodsInfo != nil && !STR_IS_NIL(elementModel.fieldName)){
        elementModel.value = [goodsInfo valueForKey:elementModel.fieldName];
      }
    }
    model.goodsList = @[];
    jsonString = [model toJSONString];
  }
  return jsonString;
}
/**
 至打印设置
 */

- (void)toPrintWithTemplateData:(NSArray *)currentPrintDatas
                    printCopies:(NSString *)printCopies
                      historyId:(NSString *)historyId
                   isFromCanvas:(BOOL)isFromCanvas
               isFromBatchPrint:(BOOL)isFromBatchPrint
                  isFolderShare:(BOOL)isFolderShare
{
  JCTemplateData *templateData = currentPrintDatas.firstObject;
  UIViewController *currentVC = [XYTool getCurrentVC];
  void (^completeBlock)(JCTemplateData*) = ^(JCTemplateData *templateData){
    /*if([currentData shouldCurveTextUpdrage]){
     [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
     [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {
     //  回调
     }];
     } alertType:3];
     return;
     }*/
    BOOL isFromMyTemplate = NO;
    UIViewController *viewController = [XYTool getCurrentVC];
    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && ([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"myTemplate"])){
      isFromMyTemplate = YES;
    }
    if(!isFromBatchPrint && !isFromMyTemplate){
      BOOL isNeedOpenVip = NO;
      if(isFromCanvas){
        isNeedOpenVip = ![templateData checkHasVipTraiTime] && [templateData shouldShowVip];
      }else{
        isNeedOpenVip = [templateData shouldShowVip];
      }
      if(isNeedOpenVip){
        jc_is_support_vip = YES;
        JC_TrackWithparms(@"click",@"025_083_107",(@{}));
        [JCIAPHelper openViewWithAlert:currentVC needOpenTip:YES isUseVipSource:YES success:^{

        } failure:^(NSString *msg, id model) {
          [MBProgressHUD showToastWithMessageDarkColor:msg];
        } sourceInfo:@{@"sourcePage":@"025"}];
        return;
      }
    }
    //        BOOL is_ableEdit_Template = [templateData checkTemplateDetailByBackImage:YES containFont:NO];
    //        if(!is_ableEdit_Template){
    //          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000354", @"网络异常")];
    //          return;
    //        }
    [DrawBoardInfo updateInfoWith:templateData];
    JCPrintScene printScene = JCPrintSceneHistory;
    if(isFromBatchPrint){
      printScene = JCPrintSceneBatchPrint;
      [[JCPrintManager sharedInstance] doPrintWithTemplateModel:currentPrintDatas uniAppId:@"" printScene:printScene historyId:@"" printComplate:^(id x) {

      }];
    }else{
      if(isFromCanvas){
        printScene = JCPrintSceneCanvas;
      }else if(isFolderShare){
        printScene = JCPrintSceneTemplateShare;
      }else if(!STR_IS_NIL(historyId)){
        printScene = JCPrintSceneHistory;
      }else{
        printScene = JCPrintSceneMyTemplate;
      }
      [[JCPrintManager sharedInstance] doPrintWithTemplateModel:@[templateData]
                                                       uniAppId:@""
                                                     printScene:printScene
                                                      historyId:historyId
                                                  printComplate:^(id x) {

      }];
    }
  };
  if (isFromCanvas || isFromBatchPrint) {
    completeBlock(templateData);
  } else {//
    [self downLoadImageImageAndFontsForTemplateData:templateData isForPrint:YES isEtag:false complete: completeBlock];
  }
}

- (void)toC1PrintWithPrintInfo:(JCC1PrintInfo *)printInfo {
  // 终止页
  //    [JCPrintManager sharedInstance].printNum = printInfo.printCount.stringValue;
  //    [JCPrintManager sharedInstance].minPrintPageNum = @"1";
  //    [JCPrintManager sharedInstance].maxPrintPageNum = StringFromInt(printInfo.batchIds.count / printInfo.printCount.integerValue);
  //    // 设置打印偏移
  //    [JCPrintManager sharedInstance].printOffsetX = printInfo.hOffset.stringValue;
  //    [JCPrintManager sharedInstance].printOffsetY = printInfo.vOffset.stringValue;
  //    [[JCPrintManager sharedInstance] doC1PrintWithWithPrintInfo:printInfo
  //                                                 printComplate:^(id x) {
  //
  //    }];

}

/**
 下载模版相关资源缩略图、背景、元素、字体
 */
- (void)downLoadImageImageAndFontsForTemplateData:(JCTemplateData *)data isForPrint:(BOOL)isForPrint isEtag:(BOOL)isEtag complete:(void(^)(JCTemplateData *resultData))completeBlock{
  JCDownImage option = isEtag ? EtagDownAll : DownAll;
  __block JCTemplateData *_data = data;
  [JCTemplateImageManager downLoadImagesForData:data options:option complete:^(JCTemplateData *resultData) {
    // Update the data with the downloaded resultData
    if (resultData != data) {
      _data = resultData;
    }
    [self.progressHUD hideAnimated:NO];
    BOOL isFromMyTemplatePrint = NO;
    UIViewController *viewController = [XYTool getCurrentVC];
    if([viewController isKindOfClass:[FBFlutterViewContainer class]] && ([viewController isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)viewController).name isEqualToString:@"myTemplate"]) && isForPrint){
      isFromMyTemplatePrint = YES;
    }
    if(isFromMyTemplatePrint){
      if (completeBlock) completeBlock(resultData);
      return;
    }
    [JCTemplateFunctionHelper checkFontDownload:resultData complate:^{
      if (completeBlock) completeBlock(resultData);
    }];
  }];
}

/**
 获取excel数据
 */
- (void)addExcelDataWith:(NSString *)excelId complate:(XYBlock)complate{
  NSFileManager *fm = [NSFileManager defaultManager];
  NSString *excelPath = RESOURCE_EXCEL_PATH;
  if(![fm fileExistsAtPath:excelPath]){
    [fm createDirectoryAtPath:excelPath withIntermediateDirectories:YES attributes:nil error:nil];
  }
  NSString *excelDataFilePath = [NSString stringWithFormat:@"%@/%@.txt",excelPath,excelId];
  if([fm fileExistsAtPath:excelDataFilePath]){
    NSString *dataString = [NSString stringWithContentsOfFile:excelDataFilePath encoding:NSUTF8StringEncoding error:nil];
    JCExcelDetailModel *detailModel = [[JCExcelDetailModel alloc] initWithString:dataString error:nil];
    NSArray *columnValueArr = detailModel.list.mutableCopy;
    NSDictionary *externalData = [self assemblingExcelDataWithId:excelId valueArr:columnValueArr];
    complate(externalData);
  }else{
    [@{@"idList":UN_NIL(excelId)} xy_postWithModelType:[JCExcelDetailModel class] Path:J_file_data hud:@"" tag:0 timeoutInterval:20 Success:^(__kindof YTKBaseRequest *request, NSArray *modelArr) {
      if(modelArr.count > 0){
        JCExcelDetailModel *detailModel = modelArr[0];
        NSArray *columnValueArr = detailModel.list.mutableCopy;
        NSDictionary *externalData = [self assemblingExcelDataWithId:excelId valueArr:columnValueArr];
        complate(externalData);
        NSString *modelArrString = detailModel.toJSONString;
        [modelArrString writeToFile:excelDataFilePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
      }else{
        complate(@{});
      }
    } failure:^(NSString *msg, id model) {
      complate(@{});
    }];
  }
}

/**
 组装模Excel数据
 */

- (NSDictionary *)assemblingExcelDataWithId:(NSString *)excelId valueArr:(NSArray *)columnValueArr{
  JCExcelForElement *object = [JCExcelForElement new];
  NSArray *columnHeaders = columnValueArr.firstObject;
  NSMutableArray *list = [NSMutableArray arrayWithCapacity:columnValueArr.count];
  for (NSInteger col = 0; col < columnHeaders.count; col++) {
    NSMutableArray *arr = [NSMutableArray arrayWithCapacity:columnValueArr.count-1];
    for (NSInteger i = 1; i < columnValueArr.count; i++) {
      NSArray *eachRow = [columnValueArr safeObjectAtIndex:i];
      NSString *value = [eachRow safeObjectAtIndex:col];
      [arr addObject:UN_NIL(value)];
    }
    [list addObject:arr];
  }
  object.excelFileName = UN_NIL(@"");
  object.columnArr = list;
  object.excelId = excelId;
  object.columnHeaders = columnHeaders;
  return [object convert2ExternalData];
}

// MeProfile 事件汇总
-(void)presentMyDevice:(UIViewController *)currentVC{
  //    if(!JC_IS_CONNECTED_PRINTER){
  //        [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)currentVC.navigationController isFromHome:NO];
  //    }else{
  //        kPreventRepeatClickTime(1);
  //        JCDeviceDetailViewController *vc = [[JCDeviceDetailViewController alloc] init];
  //        [currentVC.navigationController pushViewController:vc animated:YES];
  //    }
}
- (void)pushPrintHistory:(UIViewController *)currentVC{
  if(!xy_isLogin){
    [[JCLoginManager sharedInstance] checkLogin:^{
    } viewController:currentVC loginSuccessBlock:^{
      [self pushPrintHistory:currentVC];
    }];
  }else{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [FlutterBoostUtility gotoFlutterPage:@"printHistory"
                                 arguments:@{@"isPresent": @NO,@"token": m_userModel.token}
                            onPageFinished:^(NSDictionary *_) {
        // 页面结束回传数据
      }];
    });
  }
}


- (void)pushMessageCenter:(UIViewController *)currentVC{
  if(!xy_isLogin){
    [[JCLoginManager sharedInstance] checkLogin:^{
    } viewController:currentVC loginSuccessBlock:^{
      [self pushMessageCenter:currentVC];
    }];
  }else{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [FlutterBoostUtility gotoFlutterPage:@"messageCenter"
                                 arguments:nil
                            onPageFinished:^(NSDictionary *_) {
        // 页面结束回传数据
      }];
    });

  }
}

-(void)pushMyTemplete:(UIViewController *)currentVC{
  NSDictionary *arguments = @{@"token": m_userModel.token, @"isPresent": @NO,@"isEnablePopGesture": @NO};
  [FlutterBoostUtility gotoFlutterPage:@"myTemplate"
                             arguments:arguments
                        onPageFinished:^(NSDictionary *_) {
    // 页面结束回传数据
  }];
}

-(void)pushUserAddress:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushUserAddress:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCRecordTool recordWithAction:click_my_orders];
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"addressList" options:nil];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_017";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushMyOrders:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushMyOrders:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCRecordTool recordWithAction:click_my_orders];
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"orderList" options:@{@"orderActiveTab":@"0"}];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_016";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushToObligations:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushToObligations:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCRecordTool recordWithAction:click_my_orders];
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"orderList" options:@{@"orderActiveTab":@"1"}];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_016";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushToBeReceived:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushToBeReceived:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCRecordTool recordWithAction:click_my_orders];
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"orderList" options:@{@"orderActiveTab":@"2"}];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_016";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushToReturnAfterSale:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushToReturnAfterSale:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCRecordTool recordWithAction:click_my_orders];
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"orderList" options:@{@"orderActiveTab":@"4"}];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_016";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushHelp:(UIViewController *)currentVC{
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [JCGrayJumpHelper jumpHelpCenterWithGray];
}

-(void)pushToQAVC:(UIViewController *)currentVC{
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  JCQAViewController *vc = [[JCQAViewController alloc] init];
  [currentVC.navigationController pushViewController:vc animated:YES];
}

-(void)pushSetting:(UIViewController *)currentVC{
  JCSettingViewController *vc = [[JCSettingViewController alloc] init];
  [currentVC.navigationController pushViewController:vc animated:YES];
}

- (void)openNiimbotVip:(UIViewController *)currentVC{
  JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
  vc.sourcePage = @"006";
  [currentVC.navigationController pushViewController:vc animated:YES];

}

- (void)moveNiimbotVipRight:(UIViewController *)currentVC{
  [JCIAPHelper moveVIPRight:currentVC];
}

- (void)toDocument:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf toExcelListVcWithCurrentVc:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  [self toExcelListVcWithCurrentVc:currentVC];
}

-(void)toExcelListVcWithCurrentVc:(UIViewController *)currentVC
{
  JCExcelListViewController *vc = [[JCExcelListViewController alloc] init];
  vc.improtFrom = 4;
  [currentVC.navigationController pushViewController:vc animated:YES];
}


- (void)pushToFlutterNewUserGuide:(UIViewController *)currentVC{
  NSDictionary *arguments = @{
    @"isPresent": @NO,
    @"isFromAppStart": @0
  };
  [FlutterBoostUtility gotoFlutterPage:@"selectDeviceSeries"
                             arguments:arguments
                        onPageFinished:^(NSDictionary *_) {
    // 页面结束回传数据
  }];
}

- (void)pushToMyDevicePage:(UIViewController *)currentVC{
  NSDictionary *arguments = @{
    @"isPresent": @NO,
  };
  [FlutterBoostUtility gotoFlutterPage:@"meDevicesPage"
                             arguments:arguments
                        onPageFinished:^(NSDictionary *_) {
    // 页面结束回传数据
  }];
}

- (void)pushToEtagPage:(UIViewController *)currentVC{
  float delayTime = 0;
  if(!xy_isLogin) {
    delayTime = 1;
  }
  [[JCLoginManager sharedInstance] checkLogin:^{

  } viewController:currentVC loginSuccessBlock:^{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [JCToNativeRouteHelp toNativePageWith:JC_ETAG fromType:JC_AppCenter eventTitle:@""];
    });
  }];
}

- (void)pushSystemDockingService:(UIViewController *)currentVC{
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return ;
  }
  //app100000687
  //https://n.niimbot.com/#/abutment
  XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:XY_LANGUAGE_TITLE_NAMED(@"app100000687", JCSystemHelpServiceURL)];
  c.title = XY_LANGUAGE_TITLE_NAMED(@"app100000123", @"系统对接服务");
  //c.showWebTitle = YES;
  [currentVC.navigationController pushViewController:c animated:YES];
}

- (void)pushInvestmentAgent:(UIViewController *)currentVC{
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return ;
  }
  XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:[NSString stringWithFormat:@"%@#/cooperation",potatoURL]];
  c.title = XY_LANGUAGE_TITLE_NAMED(@"app100000124", @"招商代理");
  //    c.showWebTitle = YES;
  [currentVC.navigationController pushViewController:c animated:YES];
}

- (void)pushAboutUS:(UIViewController *)currentVC{
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  JCAboutUsViewController *c =[[JCAboutUsViewController alloc] init];
  [currentVC.navigationController pushViewController:c animated:YES];
}

-  (void)toLoginOrUserCenter:(UIViewController *)currentVC{
  if(xy_isLogin){
    [[JCLoginManager sharedInstance] touUserManagerWith:currentVC];
  }else{
    [[JCLoginManager sharedInstance] presentLoginViewController:^{

    } viewController:currentVC loginSuccessBlock:^{

    }];
  }
}

+ (void)pushToShopPoints:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushToShopPoints:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"points" options:nil];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_014";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

- (void)pushToShopCoupons:(UIViewController *)currentVC{
  if(!xy_isLogin){
    XYWeakSelf
    [[JCLoginManager sharedInstance] checkLogin:^{

    } viewController:currentVC loginSuccessBlock:^{
      [weakSelf pushToShopCoupons:currentVC];
    }];
    return;
  }
  if(NETWORK_STATE_ERROR){
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    return;
  }
  JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"myCoupon" options:nil];
  vc.entrance_type_id = @"3";
  vc.jumpSource = @"print_015";
  [currentVC.navigationController pushViewController:vc animated:YES];
}

- (void)toNPSGrade:(UIViewController *)currentVC{
  currentVC.tabBarController.tabBar.hidden = YES;
}

-(void)showBottomBar:(UIViewController *)currentVC{
  currentVC.tabBarController.tabBar.hidden = false;
}

- (void)jumpToCanvasPage:(JCTemplateData *)requestModel isFromCreate:(bool)isCreate isIndustryTemplate:(bool)isIndustryTemplate{
  if (requestModel && [requestModel isKindOfClass:[JCTemplateData class]]) {
    [JCTemplateDBManager db_queryTemplateDataById:requestModel.idStr success:^(JCTemplateData *tagDetailModel) {
      if(tagDetailModel != nil){
        [JCTemplateImageManager downLoadImagesForData:requestModel options:DownAll complete:^(JCTemplateData *resultData) {
          JCTemplateData *labelToEditModel = nil;
          [self.progressHUD hideAnimated:NO];
          [JCTemplateDBManager db_queryTemplateDataById:requestModel.idStr success:^(JCTemplateData *labelToEditModel) {
            if(labelToEditModel != nil){
              [self jumpToTemplateEditControllerWith:labelToEditModel isFromCreate:isCreate isIndustryTemplate:isIndustryTemplate];
            }
          } failed:^(id x) {
            [self.progressHUD hideAnimated:NO];
          }];
        }];
      }else{
        JCTemplateData *labelToEditModel = requestModel;
        labelToEditModel.temporaryId = requestModel.temporaryId;
        [self jumpToTemplateEditControllerWith:labelToEditModel isFromCreate:isCreate isIndustryTemplate:isIndustryTemplate];
        [self.progressHUD hideAnimated:NO];
      }
    } failed:^(id x) {
      JCTemplateData *labelToEditModel = requestModel;
      labelToEditModel.temporaryId = requestModel.temporaryId;
      [self jumpToTemplateEditControllerWith:labelToEditModel isFromCreate:isCreate isIndustryTemplate:isIndustryTemplate];
      [self.progressHUD hideAnimated:NO];
    }];
  } else {
    dispatch_async(dispatch_get_main_queue(), ^{
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00321", @"请使用精臣标签。")];
    });
  }
}

- (void)jumpToTemplateEditControllerWith:(JCTemplateData *)model isFromCreate:(bool)isCreate isIndustryTemplate:(bool)isIndustryTemplate{
  [self jumpToTemplateEidtCtlWith:model isFromCreate:isCreate source:isIndustryTemplate ? TemplateSource_Cloud : TemplateSource_New];
}

- (void)jumpToTemplateEidtCtlWith:(JCTemplateData *)model isFromCreate:(bool)isCreate source:(TemplateSource)source{
  /**
   *注: JCTemplateEditController *edit = [[JCTemplateEditController alloc] initWithModel:model source:TemplateSource_New]; 该句 中source参数只有在我的模版入口才能使用TemplateFrom_Mine源
   */
  [JCPrintManager sharedInstance].mm_w = model.width;
  NSString *jsonString = [model toJSONString];
  NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
  NSLog(@"to Flutter canvas fontPath: %@", fontPath);
  NSLog(@"to Flutter canvas jsonString: %@", jsonString);
  if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
    [JCTemplateFunctionHelper showTemplteNeedUpgrade:model];
    return;
  }
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    [JCFlutter2NativeHandler toFlutterCanvasNeedDownloadFontsWith:model type:source];
  });
}

- (void)jumpToEditControllerWith:(JCTemplateData *)label isFromCreate:(bool)isCreate fileInfo:(NSDictionary *)fileInfo {
  
  if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:label.templateVersion] == false){
    [JCTemplateFunctionHelper showTemplteNeedUpgrade:label];
    return;
  }
  // 如果是新建的标签纸，不需要保存使用记录，直接跳入画板
  if (isCreate) {
    label.idStr = [XYTool randomElementId];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      isCanCreate = YES;
    });
    NSLog(@"卡屏调试:已进入画板，可创建");
    [FlutterBoost.instance sendResultToFlutterWithPageName:@"TemplateSourceNew" arguments:@{}];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [JCFlutter2NativeHandler toFlutterCanvasWith:label type:TemplateSource_New fileInfo:fileInfo];
    });
  } else {
    // 保存使用记录
    saveRecentlyUsedRecord(label.idStr);
    XYBlock downloadSource = ^(JCTemplateData *label){
      // 直接跳入原生画板
      label.profile.extrain.userId = @"";//清除此标签纸用户id
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        isCanCreate = YES;
      });
      XYNormalBlock canvasBlock = ^(){
        JCTemplateData *data = [JCTMDataBindGoodsInfoManager templateDataWith:label goodInfo:nil];
        // 画板灰度
        data.name = XY_LANGUAGE_TITLE_NAMED(@"app100000728", @"未命名模板");
        data.dataSource = @[];
        data.dataSources = @[];
        data.bindInfo = @{};
        NSLog(@"卡屏调试:已进入画板，可创建");
        // 异步获取RTL状态后创建默认文本元素
        [JCAppMethodChannel getRtlSwitchStatus:XY_JC_LANGUAGE_REAL callback:^(BOOL status) {
            [data createDefaultTextModelWithTextDirection:status ? 1 : 0];
            [FlutterBoost.instance sendResultToFlutterWithPageName:@"TemplateSourceNew" arguments:@{}];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [JCFlutter2NativeHandler toFlutterCanvasWith:data type:TemplateSource_New fileInfo:fileInfo];
            });
        }];
      };
      //根据用户线缆画板切换偏好 跳转
      NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
      if(!label.isCableLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [canvasType isEqualToString:@"normalCanvas"]){
        // 是否是危废标签纸
        if (!label.isDangerLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [canvasType isEqualToString:@"normalCanvas"]) {
          canvasBlock();
        } else {
          if (NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
          }
          [[JCLoginManager sharedInstance] checkLogin:^{
          } viewController:nil loginSuccessBlock:^{
            [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:UniAppTrackID_dangerCap needKeepLive:YES parms:nil receiveUniappData:^(id x) {

            }];
          }];
        }
      }else{
        NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
        NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
        if(STR_IS_NIL(canvasType) || [canvasType isEqualToString:@"cableCanvas"]){
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:@{@"jsonData": label.toJSONString,@"isPresent": @NO,@"fontPath": fontPath,
                                                                            @"isEnablePopGesture": @NO} onPageFinished:^(NSDictionary *dic) {

            }];
          });

        }else{
          canvasBlock();
        }
      }
    };
    [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:label success:^(JCTemplateData *requestModel) {
      downloadSource(requestModel);
    } field:^(id msg) {
      // 还原成默认值
      isCanCreate = YES;
      [self.progressHUD hideAnimated:NO];
      if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
      }else{
        [MBProgressHUD showToastWithMessageDarkColor:msg];
      }
    }];
  }
}

//根据路由加载画板
- (void)loadCanvasWithAppRouter:(NSDictionary *)routeInfo{
  NSString *templateId = routeInfo[@"templateId"];
  NSString *defaultSelect = routeInfo[@"defaultSelect"];
  __block MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:[XYTool getCurrentVC].view animated:NO contentColor:hudContentColor backColor:hudBackColor];
  progressHUD.label.text = @"";
  //    [self getTemplateDetailRequestWithTemplateId:templateId complate:^(JCTemplateData *templateData) {
  //        if(templateData != nil){
  //            //下载图片及其他素材资源
  //            [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
  //                //静默下载字体资源
  //                [progressHUD hideAnimated:NO];
  //                JCTemplateData *data = [JCTMDataBindGoodsInfoManager templateDataWith:templateData goodInfo:nil];
  //                defaultELementType = defaultSelect;
  //                isRouterLoadCanvas = YES;
  //                [JCFlutter2NativeHandler toFlutterCanvasWith:data type:TemplateSource_New];
  //                defaultELementType = @"";
  //                isRouterLoadCanvas = NO;
  //            }];
  //        }else{
  //            [progressHUD hideAnimated:NO];
  //            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000346", @"数据不存在")];
  //        }
  //    }];
  [JCTemplateDBManager db_queryTemplateDataById:templateId success:^(JCTemplateData *templateData) {
    if(templateData != nil){
      [progressHUD hideAnimated:NO];
      JCTemplateData *data = [JCTMDataBindGoodsInfoManager templateDataWith:templateData goodInfo:nil];
      defaultELementType = defaultSelect;
      isRouterLoadCanvas = YES;
      [JCFlutter2NativeHandler toFlutterCanvasWith:data type:TemplateSource_New];
      defaultELementType = @"";
      isRouterLoadCanvas = NO;
    }else{
      [progressHUD hideAnimated:NO];
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000346", @"数据不存在")];
    }
  } failed:^(id msg) {
    [progressHUD hideAnimated:NO];
    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000346", @"数据不存在")];
  }];
}

-(void)pushMyApplication:(UIViewController *)currentVC {
  UIViewController *topVc = [XYTool getCurrentVC];
  JCLabelApplicationViewController *vc = [[JCLabelApplicationViewController alloc] init];
  vc.isNeedHidePaileTie = YES;
  [topVc.navigationController pushViewController:vc animated:YES];
}

@end

