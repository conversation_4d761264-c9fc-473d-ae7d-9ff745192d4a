import 'package:niimbot_template/models/template_data.dart';
import 'package:text/template/util/template_misc_utils.dart';

class C1TemplateVersionManager {
  static const String BASE_TEMPLATE_VERSION = "1.7.0.1";

  static String getMaxSupportTemplateVersion() {
    return "1.7.0.1";
  }

  static String getTemplateVersion() {
    return BASE_TEMPLATE_VERSION;
  }

  static bool checkTemplateVersion(TemplateData templateData) {
    String? templateVersion = templateData.templateVersion;
    if (templateVersion == null || templateVersion.isEmpty) {
      return true;
    }
    String maxSupportTemplateVersion = getMaxSupportTemplateVersion();
    int result = TemplateMiscUtils.compareTemplateVersion(templateVersion, maxSupportTemplateVersion);
    return result <= 0;
  }
}
