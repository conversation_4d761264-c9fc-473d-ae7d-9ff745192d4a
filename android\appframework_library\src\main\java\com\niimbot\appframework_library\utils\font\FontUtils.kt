package com.niimbot.appframework_library.utils.font

import android.annotation.SuppressLint
import android.content.Context
import android.content.res.ColorStateList
import android.util.Log
import android.util.SparseArray
import android.view.ViewGroup
import android.widget.ProgressBar
import android.widget.TextView
import com.blankj.utilcode.util.FileIOUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ResourceUtils
import com.niimbot.appframework_library.R
import com.niimbot.appframework_library.common.module.font.FontLibBean
import com.niimbot.appframework_library.dialog.SuperFactory
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.ToastInstance
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.canvas.image.NBCanvasImageApi
import com.niimbot.fastjson.JSON
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.niimbot.okgolibrary.okgo.callback.JsonCallback
import com.niimbot.okgolibrary.okgo.model.Response
import com.niimbot.utiliylibray.util.*
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import kotlinx.coroutines.*
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 *
 *
 * <AUTHOR>
 * @since 2020/7/10
 */
object FontUtils {

    private const val TAG = "FontUtils"

    //自定义字体文件存储根目录
    private var customFontFilePath = "${SuperUtils.superContext.filesDir.absolutePath}/font_manager/custom_and_vip/"

    var customFontFile = File(customFontFilePath)
    var defaultFontFile = File(customFontFilePath, "ZT001.ttf")
    var defaultFontFile_arial = File(customFontFilePath, "ZT063.ttf")
    var defaultFontFile_japanese = File(customFontFilePath, "ZT825.ttf")

    //获取字体库列表
    private val fontLibListHttpRequestInfo = FontLibListHttpRequestInfo()
    //字体文件后缀名
    private const val FONT_FILE_SUFFIX = ".ttf"
    //默认字体
    private const val DEFAULT_FONT_CODE = "ZT001"
    private const val DEFAULT_FONT_CODE_ARIAL = "ZT063"
    private const val DEFAULT_FONT_CODE_JAPANESE = "ZT825"

    private const val DEFAULT_FONT_KEY = "fontDefault"
    public const val ZT001_FILE = "ZT001.ttf"
    private const val ZT063_FILE = "ZT063.ttf"
//    private const val Arabic_FILE = "Arabic.ttf"
    private const val ZT825_FILE = "ZT825.ttf"
    private const val Korean_FILE = "Korean.ttf"
    private const val emoji_FILE = "emoji.ttf"
    private const val Thai_FILE = "Thai.ttf"
    private const val Fallback_FILE = "Fallback"
    private const val FontConfig_File = "fontConfig.json" // 字体配置

    var fontBeanList: ArrayList<FontLibBean>? = null
    var fontVipList: ArrayList<FontLibBean> = arrayListOf()

    var allFontList: ArrayList<FontLibBean>? = null
    var allFontMap: HashMap<String, FontLibBean> = HashMap()
    var fontClassifyList: ArrayList<FontLibBean.ClassifyBean> = arrayListOf()
    var fontClassifyMap: HashMap<FontLibBean.ClassifyBean, ArrayList<FontLibBean>> = HashMap()

    private const val TAG_FONT_ALL = "all"
    private const val TAG_FONT_VIP = "vip"
    private const val TAG_FONT_CLASSIFY = "font_classify"

    var onInitCompleteListener: (()->Unit)? = null

    fun initFontInfo(context: Context, fontLibListUrl: String, fontClassifyUrl: String, fontCDNURL: String, fontLibListIsPostRequest: Boolean){
        fontLibListHttpRequestInfo.fontLibListUrl = fontLibListUrl
        fontLibListHttpRequestInfo.fontClassifyUrl = fontClassifyUrl
        fontLibListHttpRequestInfo.isPostMethod = fontLibListIsPostRequest
        fontLibListHttpRequestInfo.cdnUrl = fontCDNURL

        val customFontPath = customFontFilePath.removeSuffix("/")
        LogUtils.e("JNILOG", "initFontInfo: customFontPath = $customFontPath")
        GlobalScope.launch(Dispatchers.IO) {
            //复制默认字体到目标目录
            copyDefaultFromAsset()
            try{
                NBCanvasImageApi.initFontPath(customFontPath);
                LogUtils.e("字体库初始化结果：")
            } catch(e: Exception) {e.printStackTrace()}
            LogUtils.e( "JNILOG", "initImageProcessingDefault: customFontPath = $customFontPath")
            getAllFontFromNet()
            getFontClassify(){
                onInitCompleteListener?.invoke()
            }
        }
    }


    fun initFontInfoForUniapp() {
        try{
            NBCanvasImageApi.initFontPath(customFontFilePath.removeSuffix("/"));
            LogUtils.e("字体库初始化结果：")
        } catch(e: Exception) {e.printStackTrace()}
    }

    private fun copyDefaultFromAsset() {
        val emojiFile = File(customFontFilePath, emoji_FILE)
        if(emojiFile.exists()){
            emojiFile.delete()
        }
        arrayListOf(ZT001_FILE, ZT063_FILE/*, Arabic_FILE, Korean_FILE*/, ZT825_FILE, emoji_FILE, Thai_FILE, Fallback_FILE, FontConfig_File).forEach {
//        arrayListOf(ZT001_FILE, ZT063_FILE).forEach {
            val file = File(customFontFilePath, it)
            if (!file.exists()) {
                ResourceUtils.copyFileFromAssets(it, File(customFontFilePath, it).path)
            }
        }

        if (isArial()) {
            LogUtils.e("默认字体是否存在: ${defaultFontFile_arial.exists()}, 路径：${defaultFontFile_arial.absolutePath}")
        } else if(isJapanese()) {
            LogUtils.e("默认字体是否存在: ${defaultFontFile_japanese.exists()}, 路径：${defaultFontFile_japanese.absolutePath}")
        } else {
            LogUtils.e("默认字体是否存在: ${defaultFontFile.exists()}, 路径：${defaultFontFile.absolutePath}")
        }
    }

    fun deleteDefault() {
        try{
            if (defaultFontFile.exists()) {
                defaultFontFile.delete()
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    fun getDefaultFontName(): String = LanguageUtil.findLanguageString(if (isArial()) "app01417" else "app01533", SuperUtils.superContext)

    fun getDefaultFontCode(): String {
        if (isArial()) {
            return DEFAULT_FONT_CODE_ARIAL
        }
        if (isJapanese()) {
            return DEFAULT_FONT_CODE_JAPANESE
        }
        return DEFAULT_FONT_CODE
    }

    fun getDefaultFont(): FontLibBean = FontLibBean().apply {
        code = getDefaultFontCode()
        name = getDefaultFontName()
    }

    fun getDefaultArialFont(): FontLibBean = FontLibBean().apply {
        id = 1000
        code = DEFAULT_FONT_CODE_ARIAL
        name = LanguageUtil.findLanguageString("app01417")
    }

    fun getDefaultJapaneseFont(): FontLibBean = FontLibBean().apply {
        id = 1008
        code = DEFAULT_FONT_CODE_JAPANESE
        name = LanguageUtil.findLanguageString("app100001929")
    }

    fun getDefaultAppFont(): FontLibBean = FontLibBean().apply {
        code = DEFAULT_FONT_CODE
        name = LanguageUtil.findLanguageString("app01533")
    }

    fun getDefaultFontFileName(): String {
        if (isArial()) {
            return ZT063_FILE
        }
        if (isJapanese()) {
            return ZT825_FILE
        }
        return ZT001_FILE
    }
    fun getDefaultFontKey() = DEFAULT_FONT_KEY

    fun isFontDownload(fontCode: String): Boolean{
        val file = File(customFontFile, getFontFileName(fontCode))
        return file.exists()
    }

    fun deleteFont(fontCode: String) {
        val file = File(customFontFile, getFontFileName(fontCode))
        if (file.exists()) {
            file.delete()
        }
    }

    fun isFontDownloading(fontLibId: Int) = downloadStatusArray[fontLibId] != null

    fun isFontVip(fontCode: String): Boolean {
        if (fontCode == DEFAULT_FONT_CODE || fontCode == DEFAULT_FONT_CODE_ARIAL || fontCode == DEFAULT_FONT_CODE_JAPANESE) {
            return false
        }
        return if (fontVipList.isNotEmpty()) {
            fontVipList?.firstOrNull { it.code.equals(fontCode, true) }?.isVip == true
        } else {
            getFontInfoFromLocal("vip").firstOrNull { it.code.equals(fontCode, true) }?.isVip == true
        }
    }

    fun getFontFileName(fontCode: String): String{
        return "$fontCode$FONT_FILE_SUFFIX"
    }

    fun getDisplayFontName(fontLibBean: FontLibBean): String{
        return if(isDefaultFont(fontLibBean.code)){
            getDefaultFontName()
        } else{
            fontLibBean.name
        }
    }

    /**
     * 判断是否默认字体
     * 手机系统语言为阿拉伯语言时，默认字体为Arabic-ZT063
     * 手机系统语言为日语时，默认字体为Japanese-ZT825
     * 其他，默认字体为思源黑体-ZT001
     * @param fontCode String
     * @return Boolean
     */
    fun isDefaultFont(fontCode: String) : Boolean {
        return fontCode == DEFAULT_FONT_CODE_ARIAL || fontCode == DEFAULT_FONT_CODE_JAPANESE || fontCode == DEFAULT_FONT_CODE
    }

    /**是否阿拉伯环境*/
    private fun isArial() = TextHookUtil.getInstance().isArialCountry()

    private fun isJapanese() = TextHookUtil.getInstance().isJapaneseCountry()

    fun getFontInfoFromLocal(langCode: String = TextHookUtil.getInstance().languageName): ArrayList<FontLibBean>{
        val fontLibBeans = arrayListOf<FontLibBean>()
        val fontLibJson = readJson(langCode)
        if(fontLibJson.isNotEmpty()){
            try{
                val fontLibBeanList = json2Array(fontLibJson, FontLibBean::class.java) as ArrayList<FontLibBean>
                if(fontLibBeanList.isNotEmpty()){
                    fontLibBeans.addAll(fontLibBeanList)
                }
            } catch(e: Exception) {e.printStackTrace()}
        }
        fontLibBeans.forEach {
            val file = File(customFontFile, getFontFileName(it.code))
            if (!file.exists()) {
                it.downloadTime = ""
            }
        }
        return fontLibBeans
    }

    fun getFontInfoFromNet(needVip: Boolean = true, ignoreLanguage: Boolean = false, listener: ((result: Boolean, fontInfo: ArrayList<FontLibBean>, errorMsg: String) -> Unit) ? = null){
        getFontList(ignoreLanguage = ignoreLanguage, canUseCache = true, jcResponseListener = object :
            JCTResponseListener<ArrayList<FontLibBean>> {
            override fun onSuccess(body: ArrayList<FontLibBean>) {
                val fontInfos = getFontInfoFromLocal()
                if(body.isNotEmpty() && fontInfos.isNotEmpty()){
                    body.forEach { newFont ->
                        newFont.downloadTime = fontInfos.firstOrNull { it.code == newFont.code}?.downloadTime ?: ""
                    }
                }
                body.forEach {
                    val file = File(customFontFile, getFontFileName(it.code))
                    if (!file.exists()) {
                        it.downloadTime = ""
                    }
                }
                val fontLibJson = any2Json(body)
                if (fontLibJson.isNotEmpty()) {
                    writeJson(fontLibJson)
                }
                fontBeanList = body
                listener?.invoke(true, body, "")
            }

            override fun onError(message: String) {
                listener?.invoke(false, arrayListOf(), message)
            }
        })
    }

    fun getAllFontFromNet(listener: ((result: Boolean, fontInfo: ArrayList<FontLibBean>, errorMsg: String) -> Unit) ? = null){
        getFontList(ignoreLanguage = true, jcResponseListener = object :
            JCTResponseListener<ArrayList<FontLibBean>> {
            override fun onSuccess(body: ArrayList<FontLibBean>) {
                if (!body.isNullOrEmpty()) {
                    filterAllFonts(body)
                    writeJson(any2Json(fontVipList), TAG_FONT_VIP)
                    writeJson(any2Json(allFontList), TAG_FONT_ALL)
                }
                listener?.invoke(true, fontVipList, "")
            }

            override fun onError(message: String) {
                filterAllFonts(getFontInfoFromLocal(TAG_FONT_ALL))
                listener?.invoke(false, arrayListOf(), message)
            }
        })
    }

    fun getFontClassify(listener: ((result: List<FontLibBean.ClassifyBean>) -> Unit)? = null) {
        if (!fontClassifyList.isNullOrEmpty()) {
            listener?.invoke(fontClassifyList)
            return
        }

        FlutterMethodInvokeManager.getFontClassify(
            isFromLocal = false,
            success = { fontClassifyJson ->
                try {
                    val fontLibBeanList = json2Array(fontClassifyJson, FontLibBean.ClassifyBean::class.java) as ArrayList<FontLibBean.ClassifyBean>
                    if (fontLibBeanList.isNotEmpty()) {
                        fontClassifyList.clear()
                        fontClassifyList.addAll(fontLibBeanList)
                        writeJson(any2Json(fontClassifyList), TAG_FONT_CLASSIFY)
                    }
                    listener?.invoke(fontClassifyList)
                } catch (e: Exception) {
                    e.printStackTrace()
                    // 如果解析失败，尝试从本地缓存读取
                    loadFontClassifyFromLocal(listener)
                }
            },
            fail = { errorMessage ->
                Log.e(TAG, "获取字体分类失败: $errorMessage")
                // 失败时从本地缓存读取
                loadFontClassifyFromLocal(listener)
            }
        )
    }

    private fun loadFontClassifyFromLocal(listener: ((result: List<FontLibBean.ClassifyBean>) -> Unit)?) {
        val fontLibJson = readJson(TAG_FONT_CLASSIFY)
        if (fontLibJson.isNotEmpty()) {
            try {
                val fontLibBeanList = json2Array(fontLibJson, FontLibBean.ClassifyBean::class.java) as ArrayList<FontLibBean.ClassifyBean>
                if (fontLibBeanList.isNotEmpty()) {
                    fontClassifyList.addAll(fontLibBeanList)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        listener?.invoke(fontClassifyList)
    }

    private fun filterAllFonts(datas: ArrayList<FontLibBean>) {
        if (!datas.isNullOrEmpty()) {
            datas.sortWith { o1, o2 -> o1.priority - o2.priority}
            allFontList = datas
            fontVipList.clear()
            fontClassifyMap.clear()
            downloadedFontList.clear()
            datas.forEach {
                if(it.isVip) fontVipList.add(it)
                val file = File(customFontFile, getFontFileName(it.code))
                if (file.exists() && !downloadedFontList.contains(it)) downloadedFontList.add(it)
                if (!it.classifyId.isNullOrEmpty() && !it.hasOffShelf()) {
                    var valueList = fontClassifyMap.getOrPut(it.getClassify()) { arrayListOf() }
                    if (!valueList.contains(it)) valueList.add(it)
                }
                allFontMap[it.code] = it
            }
        }
        downloadedFontList.add(0, getDefaultFont())
    }


    private val  jobs= arrayListOf<Job>()
    fun down(fontLibBean: FontLibBean) {
        FileUtils.resetFontDownloadStatus()
        if (isFontDownload(fontLibBean.code)) {
            downloadStatusArray[fontLibBean.id]?.onDownloadFinished()
            return
        }
        if(started.size >= 3){
            downloadStatusArray[fontLibBean.id]?.onStartDownloadFailed("app100000539")
            return
        }
        downloadStatusArray[fontLibBean.id]?.onDownloadProgress(0)
        val file = File(
            customFontFile,
            getFontFileName(fontLibBean.code)+ "_tmp"
        )
        var job: Job? = null
        jobs.add(GlobalScope.launch(start = CoroutineStart.LAZY){
            FileUtils.writeFile2Disk(fontLibBean.url, File(file.absolutePath), object:
                FileDownloadListener {
                override fun onException(e: Exception) {
                    e.printStackTrace()
                    if (file.exists()) {
                        file.delete()
                    }
                    GlobalScope.launch (Dispatchers.Main){
                        try{
                            downloadStatusArray[fontLibBean.id]?.onDownloadFailed(e.message ?: "下载字体失败")
                            downloadStatusArray.remove(fontLibBean.id)
                        } catch(e: Exception) {e.printStackTrace()}
                    }
                    started.remove(job)
                    start(jobs)
                }

                override fun onStart(disposable: FileDisposable) {
                    val fontInfos = getFontInfoFromLocal()
                    if(fontInfos.isNotEmpty()){
                        fontInfos.firstOrNull { it.code == fontLibBean.code}?.downloadTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())
                        val fontInfoJson = any2Json(fontInfos)
                        if(fontInfoJson.isNotEmpty()){
                            writeJson(fontInfoJson)
                        }
                    }
                }

                override fun onProgress(progress: Long) {
                    GlobalScope.launch (Dispatchers.Main) {
                        downloadStatusArray[fontLibBean.id]?.onDownloadProgress(progress.toInt())
                    }
                }

                override fun onEnd() {
                    com.blankj.utilcode.util.FileUtils.rename(file, getFontFileName(fontLibBean.code))
                    freshDownloadList(arrayListOf(fontLibBean))
                    GlobalScope.launch (Dispatchers.Main){
                        try{
                            downloadStatusArray[fontLibBean.id]?.onDownloadFinished()
                            downloadStatusArray.remove(fontLibBean.id)
                        } catch(e: Exception) {e.printStackTrace()}
                    }
                    started.remove(job)
                    start(jobs)
                }

            }) }.apply { job = this })
        start(jobs)
    }

    fun recycle(){
        jobs.clear()
        started.clear()
        downloadStatusArray.clear()
        FileUtils.resetFontDownloadStatus()
    }

    private  val  started= arrayListOf<Job>()
    @Synchronized private fun  start(jobs: ArrayList<Job>){
        try{
            if(jobs.size > 0){
                jobs[0].start()
                started.add(jobs[0])
                jobs.removeAt(0)
            }
        } catch(e: Exception) {e.printStackTrace()}
    }



    private val downloadSuffix = "-down"
    /**
     * listener参数说明：
     *  needDownloadFonts： 待下载的字体集合
     *  index：             任务index
     *  status：            下载状态： -2：取消下载；-1：下载失败； 0：下载成功；1：正在下载
     *  progress：          下载进度
     */
     fun downloadFontsOrder(
        fontCodeNeedDownload: List<FontLibBean>, index: Int = 0,
        listener: ((needDownloadFonts: List<FontLibBean>?, index: Int, status: Int, progress: Int) -> Unit)?
    ) {
        if (index >= fontCodeNeedDownload.size) return
        var fontLibBean = fontCodeNeedDownload.getOrNull(index)
        fontLibBean?.let {
            var file = File(customFontFile, "${getFontFileName(fontLibBean.code)}$downloadSuffix")
            LogUtils.e("========", "正在下载--->${fontLibBean.name}, index:$index")
            FileUtils.writeFile2Disk(fontLibBean.url, file, object :
                FileDownloadListener {
                override fun onException(e: Exception) {
                    e.printStackTrace()
                    if (file.exists()) {
                        file.delete()
                    }
                    GlobalScope.launch(Dispatchers.Main) {
                        listener?.invoke(
                            fontCodeNeedDownload,
                            index,
                            if (e is InterruptedException) -2 else -1,
                            0
                        )
                    }
                }

                override fun onStart(disposable: FileDisposable) {
                    val fontInfos = getFontInfoFromLocal()
                    if(fontInfos.isNotEmpty()){
                        fontInfos.firstOrNull { it.code == fontLibBean.code}?.downloadTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())
                        val fontInfoJson = any2Json(fontInfos)
                        if(fontInfoJson.isNotEmpty()){
                            writeJson(fontInfoJson)
                        }
                    }
                }

                override fun onProgress(progress: Long) {
                    GlobalScope.launch(Dispatchers.Main) {
                        listener?.invoke(fontCodeNeedDownload, index, 1, progress.toInt())
                    }
                }

                override fun onEnd() {
                    LogUtils.e("========", "下载成功--->${fontLibBean.name}, index:$index")
                    GlobalScope.launch(Dispatchers.Main) {
                        file.renameTo(File(customFontFile, "${getFontFileName(fontLibBean.code)}"))
                        listener?.invoke(fontCodeNeedDownload, index, 0, 100)
                        downloadFontsOrder(fontCodeNeedDownload, index + 1, listener)
                        freshDownloadList(arrayListOf(fontLibBean))
                    }
                }

            })
        }
    }


    private fun readJson(langCode: String = TextHookUtil.getInstance().languageName): String {
        val file = File("${customFontFile}/fontlib_${langCode}.json")
        return if (file.exists()) FileIOUtils.readFile2String(file, "UTF-8") else ""
    }

    private fun writeJson(json: String, langCode: String = TextHookUtil.getInstance().languageName){
        FileIOUtils.writeFileFromString("${customFontFile}/fontlib_${langCode}.json", json)
    }

    fun clearFontCache() {
//        fontBeanList?.clear()
//        fontVipList.clear()
//        allFontList?.clear()
//        fontClassifyList.clear()
//        fontClassifyMap.clear()
//        val fileAll = File("${customFontFile}/fontlib_${TAG_FONT_ALL}.json")
//        if (fileAll.exists()) fileAll.delete()
//        val fileVip = File("${customFontFile}/fontlib_${TAG_FONT_VIP}.json")
//        if (fileVip.exists()) fileVip.delete()
    }

    val downloadedFontList = CopyOnWriteArrayList<FontLibBean>()
    private fun freshDownloadList(newDownloadedList: List<FontLibBean>) {
        GlobalScope.launch {
            newDownloadedList.forEach {
                if (!downloadedFontList.contains(it)) downloadedFontList.add(it)
            }
        }
    }

    /**
     * 获取字体列表
     * @param ignoreLanguage Boolean 是否忽略语言，获取所有字体
     * @param jcResponseListener JCTResponseListener<ArrayList<FontLibBean>>
     */
    private fun getFontList(
        ignoreLanguage: Boolean = false,
        canUseCache: Boolean = true,
        jcResponseListener: JCTResponseListener<ArrayList<FontLibBean>>,
    ){
        check(fontLibListHttpRequestInfo.cdnUrl.isNotEmpty()) { "请求字体库url未配置" }
        if (canUseCache && ignoreLanguage && !allFontList.isNullOrEmpty()) {
            jcResponseListener.onSuccess(allFontList!!)
            return
        }
        GlobalScope.launch(Dispatchers.IO) {
            try {
                val targetFile = File(SuperUtils.superContext.cacheDir, "font_list.json")
                val deviceFile =
                    FileUtils.writeFile2Disk(fontLibListHttpRequestInfo.cdnUrl, targetFile, needCache = false)
                if (deviceFile.isSuccessFull) {
                    deviceFile.file?.let {
                        val fontListStr = FileIOUtils.readFile2String(it)
                        val fontList: List<FontLibBean> =
                            JSON.parseArray(fontListStr, FontLibBean::class.java)
                        if (fontList.isNotEmpty()) {
                            jcResponseListener.onSuccess(fontList as ArrayList<FontLibBean>)
                        } else {
                            jcResponseListener.onError("获取字体信息失败")
                        }
                    } ?: kotlin.run { jcResponseListener.onError("获取字体信息失败") }
                } else {
                    jcResponseListener.onError("获取字体信息失败")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                jcResponseListener.onError("获取字体信息失败")
            }
        }
    }

    var downloadStatusArray = SparseArray<DownloadStatusListener>()
    fun replaceFontDownloadListener(fontLibId: Int, listener: DownloadStatusListener) {
        downloadStatusArray.put(fontLibId, listener)
    }


    @SuppressLint("SetTextI18n")
    fun showDownloadingDialog(
        context: Context,
        fontCodes: ArrayList<FontLibBean>,
        themeColorResId: Int = R.color.red_fb4b42,
        btnBgResId: Int = R.drawable.btn_second_bg,
        listener: ((result: Boolean) -> Unit)? = null,
        fontDownloadedCallback: ((FontLibBean) -> Unit)? = null
    ) {
        var downloadCount = 0
        var downloadMsg = StringBuffer()
        val width = (AppUtils.getScreenWidth(context) * 0.8).toInt()
        val height = ViewGroup.LayoutParams.WRAP_CONTENT
        val holder = SuperFactory
            .createDialog(context)
            .setLayout(R.layout.dialog_font_download_progress)
            .setWidth(width)
            .setHeight(height)
            .setDimAmount(0.3f)
            .setCancelable(false)
            .build()
        holder.show()
        holder.findViewById<TextView>(R.id.tv_message).text =  fontCodes[0].name
        holder.findViewById<TextView>(R.id.tv_count).text = "1"
//        holder.findViewById<TextView>(R.id.tv_count).setTextColor(context.resources.getColor(themeColorResId))
        holder.findViewById<TextView>(R.id.tv_count_total).text = "/${fontCodes.size}"
//        holder.findViewById<TextView>(R.id.tv_cancel).setBackgroundResource(btnBgResId)
        holder.findViewById<TextView>(R.id.tv_cancel).setOnClickListener {
            FileUtils.cancelFontDownload()
            holder.dismiss()
          //  listener?.invoke(false)
        }
//        holder.findViewById<ProgressBar>(R.id.pb_progress).backgroundTintMode = PorterDuff.Mode.CLEAR
        holder.findViewById<ProgressBar>(R.id.pb_progress).progressTintList = ColorStateList.valueOf(context.resources.getColor(themeColorResId))
        holder.findViewById<ProgressBar>(R.id.pb_progress).max = 100*fontCodes.size
        FileUtils.resetFontDownloadStatus()
        downloadFontsOrder(fontCodes) { list: List<FontLibBean>?, index: Int, status: Int, progress: Int ->
            if (progress == 1 || progress == 100) {
                LogUtils.e("============", "indxe:$index, status:$status, progress:$progress, ${list?.get(index)?.name}")
            }
            if (status < 0) {
                LogUtils.e("============", "${list?.get(index)?.name}下载异常")
                //判断下载状态(已下载和未下载个数)，显示响应的toast
                if (downloadMsg.isNotEmpty()) {
                    showToast("$downloadMsg\n" + LanguageUtil.findLanguageString("app01216", context))
                } else {
                    if (status == -1) { //下载异常
                        if(!NetworkUtils.isConnected()) {
                            showToast("app01139")
                        }
                    }
                }
                holder.dismiss()
                listener?.invoke(false)
                return@downloadFontsOrder
            }
            if (null != list && index >= 0 && index < list.size) {
                holder.findViewById<TextView>(R.id.tv_message).text =  (list?.get(index)?.name
                    ?: "")
                holder.findViewById<TextView>(R.id.tv_count).text = "${index + 1}"
                holder.findViewById<TextView>(R.id.tv_count_total).text = "/${list?.size}"
                if (progress > 0) {
                    holder.findViewById<ProgressBar>(R.id.pb_progress).progress = progress + index*100
                }

                if (status == 0 && progress == 100) {
                    var fontName = list?.get(index)?.name
                    if (downloadMsg.isNotEmpty()) downloadMsg.append(",").append(fontName) else downloadMsg.append(fontName)
                    downloadCount++
                    list?.get(index)?.let { fontDownloadedCallback?.invoke(it) }
                    if (downloadCount == list.size) {
                        //全部下载成功
                        ToastInstance.INSTANCE.showSystemToast(context, "app01216")
                        holder.dismiss()
                        listener?.invoke(true)
                    }
                }
            }
        }

    }

    fun checkShowSystemName(code: String):Boolean {
        return allFontMap[code]?.showSystemName ?: false
    }

    suspend fun downloadFonts(fontCodes: List<String>, needControl: Boolean = false): Boolean{
        check(fontLibListHttpRequestInfo.fontLibListUrl.isNotEmpty()) { "请求字体库url未配置" }
        val request = if(fontLibListHttpRequestInfo.isPostMethod)
            DokitOkGo.post<ArrayList<FontLibBean>>(fontLibListHttpRequestInfo.fontLibListUrl)
        else
            DokitOkGo.get<ArrayList<FontLibBean>>(fontLibListHttpRequestInfo.fontLibListUrl)
        if (fontLibListHttpRequestInfo.platformCodes.isNotEmpty()) {
            request.params("platformCodes", fontLibListHttpRequestInfo.platformCodes)
        }
        request.params("status", "2") //入参：status：0：表示下架； 1或者不传该字段：上架 ;现在要支持查询上架 + 下架, 就传2
        request.params("queryVersion", "2")

        var fontLibBeans: List<FontLibBean>? = allFontList
        try {
//            val responseBody = request.execute().body?.string()
//            if (!responseBody.isNullOrEmpty()) {
//                val body = json2Any(responseBody, ResponseBody::class.java)
//                fontLibBeans = body?.data
                if (!fontLibBeans.isNullOrEmpty()) {
                    val localFontLibBeans = getFontInfoFromLocal()
                    if (localFontLibBeans.isNotEmpty()) {
                        fontLibBeans.forEach { newFont ->
                            newFont.downloadTime =
                                localFontLibBeans.firstOrNull { it.code == newFont.code }?.downloadTime
                                    ?: ""
                        }
                    }
                    val fontLibJson = any2Json(fontLibBeans)
                    if (fontLibJson.isNotEmpty()) {
                        writeJson(fontLibJson)
                    }
                }
//            }
        } catch (e: Exception) {
            logE(TAG, "downloadFonts: getFontInfo failed")
            e.printStackTrace()
        }
        if(fontLibBeans.isNullOrEmpty()){
            fontLibBeans = getFontInfoFromLocal()
        }
        if(fontLibBeans.isNullOrEmpty()){
            return false
        }
        val fontCodeUrlMap = mutableMapOf<String, String>()
        fontCodes.forEach { code ->
            if(!isFontDownload(code)){
                val url = fontLibBeans.firstOrNull { it.code == code }?.url
                if(!url.isNullOrEmpty()){
                    fontCodeUrlMap[code] = url
                }
            }
        }
        if(fontCodeUrlMap.isEmpty()){
            return true
        }
        else{
            return withContext(Dispatchers.Default) {
                var downloadFailed = false
                val downloadSuccessFonts = arrayListOf<FontLibBean>()
                val downloadFontTaskMap = mutableMapOf<String, Deferred<FileDisposable>>()
                var interval = 1
                for((code, url) in fontCodeUrlMap){
                    val task = async(Dispatchers.IO){
                        FileUtils.downloadFile2Disk(url, File(customFontFile, getFontFileName(code)))
                    }
                    downloadFontTaskMap[code] = task
                    LogUtils.e("======静默下载字体======code: $code, url: $url")
                    if (needControl) {
                        if (interval%3 == 0) {
                            delay(20000)
                        }
                        interval++
                        if (interval > 30) {
                            PreferencesUtils.put("tag_need_download_fonts_default", false)
                            break
                        }
                    }
                }
                for((code, task) in downloadFontTaskMap) {
                    val result = task.await().isSuccessFull
                    if(!result){
                        downloadFailed = true
                    } else{
                        val downloadSuccessFont = fontLibBeans.firstOrNull { it.code == code }
                        if(downloadSuccessFont != null){
                            downloadSuccessFont.downloadTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(Date())
                            downloadSuccessFonts.add(downloadSuccessFont)
                        }
                    }
                }
                val fontInfos = getFontInfoFromLocal()
                if(downloadSuccessFonts.isNotEmpty() && fontInfos.isNotEmpty()){
                    downloadSuccessFonts.forEach { downloadSuccessFont ->
                        fontInfos.firstOrNull { it.code == downloadSuccessFont.code}?.downloadTime = downloadSuccessFont.downloadTime
                    }
                    val fontInfoJson = any2Json(fontInfos)
                    if(fontInfoJson.isNotEmpty()){
                        writeJson(fontInfoJson)
                    }
                    freshDownloadList(downloadSuccessFonts)
                }
                !downloadFailed
            }
        }
    }

    interface DownloadStatusListener{
        fun onStartDownloadFailed(errorMsg: String)
        fun onDownloadFailed(errorMsg: String)
        fun onDownloadProgress(progress: Int)
        fun onDownloadFinished()
    }
}
