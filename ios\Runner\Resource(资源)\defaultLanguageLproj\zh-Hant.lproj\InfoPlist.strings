/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "精臣雲打印";
CFBundleDisplayName = "精臣雲打印";
NSCameraUsageDescription = "此應用程式會於掃描條碼、文字辨識、拍照功能時存取您的相機權限，是否允許開啟相機？";
NSBluetoothPeripheralUsageDescription = "此應用程式會於連接印表機服務時存取您的藍牙權限，是否允許開啟藍牙？";
NSBluetoothAlwaysUsageDescription = "此應用程式會於連接印表機服務時存取您的藍牙權限，是否允許開啟藍牙？";
NSContactsUsageDescription = "此應用程式會於讀取聯絡人服務時存取您的通訊錄權限，是否允許開啟通訊錄？";
NSMicrophoneUsageDescription = "此應用程式會於語音辨識服務時存取您的麥克風權限，是否允許開啟麥克風？";
NSPhotoLibraryUsageDescription = "該權限將用於列印圖片素材、條碼辨識、QR Code 辨識、文字辨識、自訂頭像等功能。請「允許存取所有照片」，以確保精臣雲列印能正常使用相簿。如使用「選擇照片...」，則未被選取的照片與未來新增的照片都將無法在精臣雲列印中存取。";
NSLocationAlwaysUsageDescription = "為了方便您使用附近的 Wi-Fi 網路，精臣雲列印需要申請定位權限";
NSLocationWhenInUseUsageDescription = "為了方便您使用附近的 Wi-Fi 網路，精臣雲列印需要申請定位權限";
NSLocationAlwaysAndWhenInUseUsageDescription = "為了方便您使用附近的 Wi-Fi 網路，精臣雲列印需要申請定位權限";
NSSpeechRecognitionUsageDescription = "此應用程式需經您同意，才能使用語音辨識，是否允許開啟語音辨識？";
NSLocalNetworkUsageDescription = "此應用在搜尋區域網路設備、配網的服務中需要存取本地區域網路 (LAN)​。";
"UILaunchStoryboardName" = "LaunchScreen";
