//
//  JCTrackingServiceHelp.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2022/3/8.
//  Copyright © 2022 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCTrackingServiceHelp.h"
#import <HoundAnalyticsSDK/SensorsAnalyticsSDK.h>
#import "JCSSLTokenCacheHelper.h"
@interface JCTrackingServiceHelp () <SLSTokenEmitter>
@property(nonatomic,strong) SLSTokenData *tokenData;
@end

@implementation JCTrackingServiceHelp

DEF_SINGLETON( JCTrackingServiceHelp )

- (instancetype)initTrackingConfigWithOptions:(NSDictionary *)launchOptions{
    // 数据上报到数据服务，数据服务相关信息
    self = [super init];
    if(self){
        NBServerInfo *serverInfo = [[NBServerInfo alloc] init];
        [serverInfo setBizName:@"NIIMBOT"];
        [serverInfo setAppName:@"ios_print"];
        #ifdef  Pro
        [serverInfo setLogProject:@"niimbot-pro"];
        //测试环境
        #elif   JCTest
        [serverInfo setLogProject:@"niimbot-fat"];
        //appStore版本
        #elif   ReleaseAppStore
        [serverInfo setLogProject:@"niimbot-pro"];
        #elif   Dev
        [serverInfo setLogProject:@"niimbot-fat"];
        #endif
        [serverInfo setLogStore:@"print-buried-point"];
        [serverInfo setLanguage:[self getCurrentLanguage]];
        // 设置阿里云日志服务的 ak/sk 接口，实现 SLSTokenEmitter 协议
        [serverInfo setEmitter:self];

        SAConfigOptions *options = [[SAConfigOptions alloc] initWithServerInfo:serverInfo launchOptions:launchOptions];

        // 启用 session 标记
        options.enableSession = YES;

        options.flushNetworkPolicy = SensorsAnalyticsNetworkTypeALL;
        options.enableTrackAppCrash = NO;
        options.enableHeatMap = NO;
        options.enableVisualizedAutoTrack = NO;
        options.enableJavaScriptBridge = NO;
        options.maxCacheSize = 20000;
        options.enableLog = NO;
        [SensorsAnalyticsSDK startWithConfigOptions:options];
        if(xy_isLogin){
            [[SensorsAnalyticsSDK sharedInstance] login:m_userModel.userid];
        }
    }
    return self;
}

- (NSString *)getCurrentLanguage{
    NSString *ret = XY_CURRENT_APP_LANGUAGE;
    if([ret isEqualToString:@""] || ret == nil){
        ret = @"zh-cn";
    }else{
        ret = [XYTool getNationalLanguageNameFromSystemLanguage:ret];

        if ([ret isEqualToString:@"zh-Hans"]) {
            ret = @"zh-cn";
        }
    }
    return ret;
}

- (SLSTokenData *)emit {
    //yc to do 接收不到失败的回调
    NSString * path = J_get_sts_track;
    __block NSDictionary *dataDic = nil;
    JCSSLTokenCacheHelper * cacheHelper = [JCSSLTokenCacheHelper shareInstance];
    NSDictionary * cacheData = [cacheHelper getCacheDataWithKey:path];
    if(cacheData != nil){
        dataDic = cacheData;
    }else{
        dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
        [@{@"needDesRequest":@"1"} java_postWithModelType:nil Path:path hud:nil Success:^(__kindof YTKBaseRequest *request, id model) {
            dataDic = model;
            [cacheHelper addCacheDataWithKey:path data:dataDic];
            dispatch_semaphore_signal(semaphore);
        } failure:^(NSString *msg, id model) {
            NSLog(@"该重新获取token啦");
            [cacheHelper clearCacheKey:path];
            dispatch_semaphore_signal(semaphore);
        }];
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    }


    SLSTokenData *errorData = [[SLSTokenData alloc] init];
    errorData.accessKeyId = @"errorTest";
    errorData.accessKeySecret = @"errorTest";
    errorData.securityToken = @"errorTest";
    errorData.endpoint = @"errorTest";
    errorData.tokenExpireTime = 10;
    if (dataDic == nil) {
        return errorData;
    }
    if (![dataDic isKindOfClass:NSDictionary.class]) {
        return errorData;
    }
//    NSDictionary *dataDic = dict[@"data"];
    if(dataDic.count == 0){
        return errorData;
    }
    SLSTokenData *tokenData = [[SLSTokenData alloc] init];
    tokenData.accessKeyId = dataDic[@"accessKeyId"];
    tokenData.accessKeySecret = dataDic[@"accessKeySecret"];
    tokenData.securityToken = dataDic[@"securityToken"];
    tokenData.endpoint = dataDic[@"endpoint"];
    NSNumber *tokenExpireTime = dataDic[@"tokenExpireTime"];
    tokenData.tokenExpireTime = tokenExpireTime.integerValue;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(tokenExpireTime.integerValue * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLog(@"该重新获取token啦");
    });
    return tokenData;
}

- (NSDictionary *)requestHeaderFieldValueDictionary {
    NSString *token = m_userModel.token;
    NSString *shopToken = m_userModel.shopToken;
    if(token == nil) return nil;
    if (token.length > 0 ) {
        token = [NSString stringWithFormat:@"bearer %@",token];
        NSLog(@"请求token：%@",token);
    }
    NSMutableDictionary *header = [NSMutableDictionary dictionaryWithCapacity:3];
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    if (!userAgent) {
        userAgent = [[XYCenter sharedInstance] getUA];
    }
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    if([config.baseUrl isEqualToString:ShopAPIRequestURL]){
        [header setObject:UN_NIL(shopToken) forKey:@"Authorization"];
    }else{
        [header setObject:UN_NIL(token) forKey:@"Authorization"];
    }
    [header setObject:UN_NIL(userAgent) forKey:@"niimbot-user-agent"];
    /** 语言code */
    if (!STR_IS_NIL(XY_JC_LANGUAGE_REAL)) {
        [header setObject:XY_JC_LANGUAGE_REAL forKey:@"languageCode"];
    }
    return header;
}

- (void)setTrackingWithTrack:(NSString *)trackName posCode:(NSString *)posCode withProperties:(NSDictionary *)properties{
    if(!STR_IS_NIL(m_userModel.userid)){
        [[SensorsAnalyticsSDK sharedInstance] login:m_userModel.userid];
    }else{
        [[SensorsAnalyticsSDK sharedInstance] logout];
    }
    [[SensorsAnalyticsSDK sharedInstance] track:trackName
                                        posCode:posCode
                                 withProperties:properties];
#ifdef DEBUG
    NSLog(@"[track_log] event:%@  pos_code:%@  ext:%@",trackName,posCode,properties.xy_toJsonString);
#endif
}

- (void)bluetoothTarck:(BOOL)isConnect btName:(NSString *)deviceName macAddress:(NSString *)macAddress regionCode:(NSString *)regionCode{
    if(isConnect){
        [[SensorsAnalyticsSDK sharedInstance] connect:macAddress btName:deviceName regionCode:regionCode];
    }else{
        [[SensorsAnalyticsSDK sharedInstance] disconnect];
    }
}
@end
