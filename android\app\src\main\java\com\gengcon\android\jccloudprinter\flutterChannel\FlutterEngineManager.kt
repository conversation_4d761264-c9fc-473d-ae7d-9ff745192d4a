package com.gengcon.android.jccloudprinter.flutterChannel

import JCApiManager
import android.app.Activity
import android.app.Application
import android.content.Intent
import android.text.TextUtils
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.android.jccloudprinter.FlutterChannelRegister
import com.gengcon.android.jccloudprinter.flutterActivity.TransparencyPageActivity
import com.gengcon.android.jccloudprinter.flutterActivity.TransparentActionBarActivity
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connectui.helper.DeviceETagHelper
import com.gengcon.print.draw.DrawIntentParams
import com.gengcon.print.draw.activity.C1PrintActivity
import com.gengcon.print.draw.print.PrintTaskExecutor
import com.google.gson.reflect.TypeToken
import com.idlefish.flutterboost.FlutterBoost
import com.idlefish.flutterboost.FlutterBoostDelegate
import com.idlefish.flutterboost.FlutterBoostRouteOptions
import com.idlefish.flutterboost.containers.FlutterBoostActivity
import com.jc.repositories.webview.library.view.DrawboardIMActivity
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.TemplateModuleR
import com.niimbot.appframework_library.common.module.template.external.ExternalData
import com.niimbot.appframework_library.common.module.template.external.Sheet
import com.niimbot.appframework_library.common.module.template.external.Task
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.util.TemplateUtils
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.protocol.webview.LiveCodeWebActivityConfig
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.ToastInstance
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.ab.ABTestUtils
import com.niimbot.baselibrary.dialog.ThirdAppHintDialog
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.templateVersion.TemplateVersionConst
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.storelibrary.bean.ExcelDetailsModule
import com.niimbot.templatecoordinator.core.PrintHistoryFileUtils
import com.niimbot.templatecoordinator.core.SilentDownloadResources
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.module.TemplateRequest
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.viplibrary.VipDialog
import com.niimbot.viplibrary.VipDialogActivity
import com.niimbot.viplibrary.VipType
import com.qyx.languagelibrary.utils.LanguageSPHelper
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.any2Json
import com.southcity.watermelon.util.json2Any
import com.southcity.watermelon.util.json2Array
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.eventbus.FinishTemplateIndustryEvent
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.local.util.ExcelLocalManager
import melon.south.com.baselibrary.local.util.TemplateUsedRecordUtils
import melon.south.com.baselibrary.util.TemplateDbUtils
import melon.south.com.mainlibrary.util.UserCenterHelper
import melon.south.com.mainlibrary.v.InnerAppFragment
import melon.south.com.templatelibrary.activity.TemplateDetailsActivity
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import org.greenrobot.eventbus.EventBus
import java.util.*
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine


/**
 * @ClassName: FlutterEnginManager
 * @Author: Liuxiaowen
 * @Date: 2023/2/10 10:00
 * @Description:
 */
object FlutterEngineManager {
    lateinit var context: Application
    fun init(context: Application) {
        this.context = context
        FlutterBoost.instance().setup(context, object : FlutterBoostDelegate {
            override fun pushNativeRoute(options: FlutterBoostRouteOptions) {

                LogUtils.e("进入原生界面=${options.pageName()}")
                toNativePage(options)
            }

            override fun pushFlutterRoute(options: FlutterBoostRouteOptions) {
                toFlutterPage(options)
            }
        }) { engine: FlutterEngine? ->
            engine?.dartExecutor?.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
            engine?.let { FlutterChannelRegister.handleRegisterEngine(context, it) }
        }
    }

    private suspend fun addExcelDataWithExcelId(excelId: String): ExternalData? {
        try {
            val listBean = ExcelLocalManager.getListByID(excelId)
            //数据库数据为空
            if (listBean != null && !TextUtils.isEmpty(listBean.excelPath)) {
                //文件为空
                val stringBuffer = ExcelLocalManager.readExcel(listBean.excelPath)
                if (stringBuffer == null) {
                    return getExcel(excelId)
                } else {
                    val excelDetailsModule =
                        json2Array(stringBuffer.toString(), ExcelDetailsModule::class.java)?.get(0)
                    return excelDetailsModule?.let { convertExcelDetailToExternalData(it) }
                }
            } else {
                return getExcel(excelId)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    /**
     * 获取excel数据
     */
    private suspend fun getExcel(id: String): ExternalData? {
        return suspendCoroutine {
            JCApiManager.getCloudFileDetail(id, object : JCTResponseListener<String> {
                override fun onSuccess(body: String) {
                    if (body.isNotEmpty()) {
                        ExcelLocalManager.UpdateListDetailsByID(id, body)
                        val excelDetailsModule =
                            json2Array(body, ExcelDetailsModule::class.java).get(0)
                        val externalData = convertExcelDetailToExternalData(excelDetailsModule)
                        it.resume(externalData)
                    } else {
                        it.resume(null)
                    }
                }

                override fun onError(message: String) {
                    it.resume(null)
                }
            })
        }

    }

    private fun convertExcelDetailToExternalData(excelDetailsModule: ExcelDetailsModule): ExternalData {
        val externalData = ExternalData()
        externalData.id = excelDetailsModule.id.toString()
        val sheet = Sheet()
        val sheetData = excelDetailsModule.list
        if (sheetData != null && sheetData.size > 1) {
            sheet.data.columnHeaders.addAll(sheetData[0])
            sheetData.remove(sheetData[0])
            val columnSize = sheet.data.columnHeaders.size
            for (i in 0 until columnSize) {
                val columnData = arrayListOf<String>()
                sheetData.forEach {
                    columnData.add(it.getOrElse(i) { "" })
                }
                sheet.data.columns.add(columnData)
            }
        }
        externalData.list.add(sheet)
        externalData.fileName = ""
        return externalData


    }


    /**
     * flutter跳转原生页面
     */
    fun toNativePage(options: FlutterBoostRouteOptions) {
        when (options.pageName()) {
            "CanvasPage" -> {
                GlobalScope.launch {
                    withContext(Dispatchers.Main) {
                        GlobalLoadingHelper.showLoading(getSafeActivityContext())
                    }
                    labelRecorder(options)
                    withContext(Dispatchers.Main) {
                        GlobalLoadingHelper.dismissLoading()
                    }
                }
            }

            "TemplateSourceNew" -> {
                GlobalScope.launch {
                    labelCreate(options)
                }
            }

            "ScanBarcode" -> {
                if (FlutterBoost.instance()
                        .currentActivity() is TransparencyPageActivity
                ) {
                    (FlutterBoost.instance()
                        .currentActivity() as TransparencyPageActivity).setPermissionListener(
                        "ScanBarcode",
                        options
                    )
                } else {
                    (FlutterBoost.instance()
                        .currentActivity() as TransparentActionBarActivity).setPermissionListener(
                        "ScanBarcode",
                        options
                    )
                }
                var justCallbackScanCode = false
                if (options.arguments().containsKey("justCallbackScanCode")) {
                    justCallbackScanCode =
                        options.arguments().get("justCallbackScanCode") as Boolean
                }
                SyncTemplatePresenter.startScanActivity(
                    getSafeActivityContext(),
                    options.requestCode(),
                    justCallbackScanCode
                )
            }

            "DeviceConnectPage" -> {
                val fromCanvasSelectLabel =
                    options.arguments().get("fromCanvasSelectLabel") as? Boolean ?: false
                var fromUnimp = false
                if (options.arguments().containsKey("fromUnimp")) {
                    fromUnimp = options.arguments().get("fromUnimp") as Boolean
                }
                var uniappId = ""
                if (options.arguments().containsKey("uniappId")) {
                    uniappId = options.arguments().get("uniappId") as String
                }
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        DeviceConnectActivityConfig(
                            getSafeActivityContext(),
                            true
                        ).apply {
                            this.intent.putExtra("fromUnimp", fromUnimp)
                            this.intent.putExtra("uniappId", uniappId)
                            this.intent.putExtra("fromCanvasSelectLabel", fromCanvasSelectLabel)
                        }
                    )
                )
            }

            "ToPrintSettingPage" -> {
                GlobalScope.launch {
                    var jsonStr = options.arguments().get("content") as? String
                    val isFromSelectData =
                        options.arguments().get("isFromSelectData") as? Boolean ?: false
                    val isFolderShare =
                        options.arguments().get("isFolderShare") as? Boolean ?: false
                    val needDownloadRes =
                        options.arguments().get("needDownloadRes") as? Boolean ?: true
                    val printChannelCode = options.arguments().get("printChannelCode") as? String

                    var pdfBindInfo =
                        options.arguments().get("pdfBindInfo") as? List<Map<String, Any>>

                    PrintTaskExecutor.printChannelCode = printChannelCode
                    val uniqueId = options.arguments().get("uniqueId") as? String
                    if (!TextUtils.isEmpty(uniqueId)) {
                        val printHistroyContent = PrintHistoryFileUtils.readPrintHistory(uniqueId)
                        if (!TextUtils.isEmpty(printHistroyContent)) {
                            jsonStr = printHistroyContent
                        }
                    }
                    //打印份数
                    var copies = options.arguments().get("copies") as? Int ?: 0
                    if (copies == 0) {
                        copies = 1
                    }
                    //标记从打印记录过来的
                    val fromPrintHistory =
                        options.arguments().get("fromPrintHistory") as? Boolean ?: false
                    val linkedData = options.arguments().get("linkedData") as? String
                    val needRfidTemplate =
                        options.arguments().get("needRfidTemplate") as? Boolean ?: false
                    val showRfid = (options.arguments().get("showRfid") as? Boolean) ?: false
                    val isFromCanvas =
                        (options.arguments().get("isFromCanvas") as? Boolean) ?: false
                    val rfidInfo = (options.arguments().get("rfidInfo") as? String) ?: ""
                    var templateDetail = jsonStr?.let {
                        TemplateModuleLocal.fromJson(it as String)
                    }

//                    templateDetail?.hasVIPRes =
//                        templateDetail?.hasVipElement() ?: false || templateDetail?.vip ?: false || templateDetail?.hasVIPRes ?: false
                    if (templateDetail?.isGoodsTemplate() == true && fromPrintHistory) {
                        ABTestUtils.transformPcNewExcelTemplateJson(jsonStr!!) { templateJson ->
                            GlobalScope.launch {
                                templateDetail = TemplateModuleLocal.fromJson(templateJson)
                                preJumpToPrintSettingPage(
                                    fromPrintHistory,
                                    linkedData,
                                    uniqueId,
                                    copies,
                                    templateDetail!!,
                                    needRfidTemplate,
                                    showRfid,
                                    rfidInfo,
                                    isFromCanvas,
                                    isFromSelectData,
                                    isFolderShare,
                                    needDownloadRes = needDownloadRes,
                                    pdfBindInfo = pdfBindInfo ?: mutableListOf()
                                )
                            }
                        }
                    } else {
                        preJumpToPrintSettingPage(
                            fromPrintHistory,
                            linkedData,
                            uniqueId,
                            copies,
                            templateDetail!!,
                            needRfidTemplate,
                            showRfid,
                            rfidInfo,
                            isFromCanvas,
                            isFromSelectData,
                            isFolderShare,
                            needDownloadRes = needDownloadRes,
                            pdfBindInfo = pdfBindInfo ?: mutableListOf()
                        )
                    }

                }

            }

            "ToTemplatePage" -> {
                GlobalScope.launch {
                    //这里的 进入画板界面--这里目前只有 打印记录 用到
                    //下载所有图片 缩略图 前景图 元素图 字体  表单信息 活吗信息等---然后进入画板
                    var jsonStr = options.arguments().get("content")
                    val uniqueId = options.arguments().get("uniqueId") as? String
                    val source = options.arguments().get("source") as? String
                    val templateId = options.arguments().get("templateId") as? String
                    val printChannelCode = options.arguments().get("printChannelCode") as? String
                    if (!TextUtils.isEmpty(uniqueId)) {
                        val printHistroyContent = PrintHistoryFileUtils.readPrintHistory(uniqueId)
                        if (!TextUtils.isEmpty(printHistroyContent)) {
                            jsonStr = printHistroyContent
                        }
                    }
                    val linkedData = options.arguments().get("linkedData") as? String
                    var excelId: String? = ""
                    val templateDetail = jsonStr?.let {
                        TemplateModuleLocal.fromJson(it as String)
                    }
                    templateDetail?.let {
                        TemplateSyncLocalUtils.saveUsedRecordByLabelId(it.profile.extrain.labelId)
                    }

                    if (!TextUtils.isEmpty(uniqueId)) {
                        //打印记录重置模版id
                        templateDetail?.id = System.currentTimeMillis().toString()
                        //  templateDetail?.profile?.extrain?.userId = "-1";
                    }
                    templateDetail?.profile?.extrain?.templateType = -1

                    jumpToCanvasEditPage(uniqueId, templateDetail!!, source, printChannelCode)
                    //重建exterData数据
//                    if (!TextUtils.isEmpty(linkedData)) {
//                        val type = object :
//                            TypeToken<Map<String, String>>() {}.type
//                        val linkInfo =
//                            GsonUtils.fromJson<Map<String, String>>(
//                                linkedData,
//                                type
//                            )
//                        excelId = linkInfo["excelId"]
//                    }
//                    if (!TextUtils.isEmpty(excelId) && !excelId.equals("0") && templateDetail?.dataSource?.isNotEmpty() == true && templateDetail?.externalData?.list.isNullOrEmpty()) {
//                        val externalData = addExcelDataWithExcelId(excelId!!)
//                        externalData?.let {
//                            templateDetail?.externalData = it
//                        }
//                        jumpToCanvasEditPage(uniqueId, templateDetail!!, source, printChannelCode)
//                    } else {
//                        jumpToCanvasEditPage(uniqueId, templateDetail!!, source, printChannelCode)
//                    }
                }

            }

            "ToBuyLabelPage" -> {
                var context = FlutterBoost.instance().currentActivity()
                LoginDataEnum.loginCheck(context) {
                    val link = options.arguments().get("link") as String
                    NiimbotGlobal.gotoShopWeb(context, link)
                }
            }

            "ToVipPage" -> {
                //vip购买
                //标记是否要同时弹出登录框
                val isPopLogin = options.arguments().get("isPopLogin") as? Boolean ?: false
                val vipType = options.arguments().get("vipType") as? String ?: ""
                if (vipType == "cable") {
                    if (!LoginDataEnum.isLogin) {
                        LoginDataEnum.loginCheck(getSafeActivityContext()) {
                        }
                        return
                    }
                }


                if (vipType == "cable") {
                    VipDialog.showVipRenewDialog(
                        getSafeActivityContext(),
                        sourcePage = "006",
                        vipType = VipType.CABLE,
                        anchorSubscribeId = "niimbot_ydy_cable_yearly",
                        listener = {
                        })
                } else {
                    val intent = Intent(
                        getSafeActivityContext(),
                        VipDialogActivity::class.java
                    ).apply { putExtra("isPopLogin", isPopLogin) }.apply {
                        putExtra("vipType", vipType)
                    }
                    getSafeActivityContext().startActivityForResult(intent, options.requestCode())
                }

            }

            "DeviceConnectTagBasePage" -> {
                val uniappId = options.arguments().get("uniappId") as? String ?: ""
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        DeviceConnectActivityConfig(
                            getSafeActivityContext(),
                            true
                        ).apply {
                            this.intent.putExtra("fromUnimp", true)
                            this.intent.putExtra("uniappId", uniappId)
                        }
                    )
                )
            }

            "DeviceConnectC1HomePage" -> {
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        DeviceConnectActivityConfig(
                            getSafeActivityContext(),
                            true
                        ).apply {
                            this.intent.putExtra("fromUnimp", true)
                            this.intent.putExtra("uniappId", DeviceC1Helper.uniappId)
                        }
                    )
                )
            }

            "TemplateDetailsPage" -> {
                val templateId = options.arguments().get("templateId").toString()
                val isFolderShare = options.arguments().get("isFolderShare") as? Boolean ?: false
                val intent =
                    Intent(
                        getSafeActivityContext(),
                        TemplateDetailsActivity::class.java
                    )
//                    intent.putExtra("needTemplateIdCallback", true)
                intent.putExtra("templateId", templateId)
                    .putExtra("isTemplateLabel", false)
                    .putExtra("isFolderShare", isFolderShare)
                FlutterBoost.instance().currentActivity()
                    .startActivityForResult(intent, options.requestCode())

//                LeMessageManager.getInstance().dispatchMessage(
//                    LeMessage(
//                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
//                        TemplateDetailsActivityConfig(
//                            FlutterBoost.instance().currentActivity()
//                        ).apply {
//                            intent
//                                .putExtra("templateId", templateId)
//                                .putExtra("isTemplateLabel", false)
//                                .putExtra("isFolderShare", isFolderShare)
//                        }
//                    )
//                )
            }

            "toPrintTask" -> {
//                val printTemplate = options.arguments().get("printTemplate") as String
//                val printCount = options.arguments().get("printCount") as Int
//                val printDensity = options.arguments().get("printDensity") as Int
//                val hOffset = (options.arguments().get("hOffset") as Double).toFloat()
//                val vOffset = (options.arguments().get("vOffset") as Double).toFloat()
//
//                val activity = getSafeActivityContext()
//                val intent = Intent(activity, PrintTaskActivity::class.java)
//                intent.putExtra("printTemplate", printTemplate)
//                intent.putExtra("printCount", printCount)
//                intent.putExtra("printDensity", printDensity)
//                intent.putExtra("hOffset", hOffset)
//                intent.putExtra("vOffset", vOffset)
//
//                FlutterBoost.instance().currentActivity()
//                    .startActivityForResult(intent, options.requestCode())

            }

            "toWebPage" -> {
                val url = options.arguments().get("url").toString()
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        LiveCodeWebActivityConfig(context).apply {
                            intent.putExtra("url", url)
//                            intent.putExtra("code_id", selectId)
                            intent.putExtra("showTitleBar", true)
                            intent.putExtra("dialogMode", true)
//                            intent.putExtra("pageFrom", pageFrom)
                        }
                    )
                )
            }

            "toBatchPrintSettingPage" -> {
                val jsonStr = options.arguments().get("printInfos") as List<Map<String, Int>>
                val showRfid = (options.arguments().get("showRfid") as? Boolean) ?: false
                val rfidInfo = (options.arguments().get("rfidInfo") as? String) ?: ""
                val templateId = jsonStr[0].get("id").toString()
                FlutterMethodInvokeManager.queryTemplateById(templateId) { isSuccess, templateJsonStr, errMsg ->
                    GlobalScope.launch(Dispatchers.Main) {
                        var localTemplate: TemplateModule? = null
                        withContext(Dispatchers.IO) {
                            if (!templateJsonStr.isNullOrEmpty()) {
                                var resultTemplateModuleLocal =
                                    TemplateModuleLocal.fromJson(templateJsonStr)
                                localTemplate =
                                    TemplateModuleTransform.templateModuleLocalToTemplateModule(
                                        resultTemplateModuleLocal!!
                                    )
                            }
                        }
                        var isActiveCount = true

                        if (SilentDownloadResources.getExecutor().queue.size == 0 && SilentDownloadResources.getExecutor().activeCount == 0) {
                            isActiveCount = false
                        }
                        SyncTemplatePresenter.startPrintActivity(
                            getSafeActivityContext(),
                            niimbotDrawData = NiimbotDrawData().apply {
                                if (localTemplate != null) {
                                    niimbotTemplate = localTemplate!!.toTemplateModuleLocal()
                                }
                            },
                            isShare = false,
                            fromFlutter = true,
                            showRfid = showRfid,
                            rfidInfo = rfidInfo,
                            fromBatchPrint = true,
                            batchtIds = jsonStr,
                            activeCount = isActiveCount
                        )

                    }

                }
//                val localTemplate = TemplateDbUtils.queryTemplate(jsonStr[0].get("id").toString())
//                var isActiveCount = true
//
//                if (SilentDownloadResources.getExecutor().queue.size == 0 && SilentDownloadResources.getExecutor().activeCount == 0) {
//                    isActiveCount = false
//                }
//                SyncTemplatePresenter.startPrintActivity(
//                    getSafeActivityContext(),
//                    niimbotDrawData = NiimbotDrawData().apply {
//                        if (localTemplate != null) {
//                            niimbotTemplate = localTemplate.toTemplateModuleLocal()
//                        }
//                    },
//                    isShare = false,
//                    fromFlutter = true,
//                    showRfid = showRfid,
//                    rfidInfo = rfidInfo,
//                    fromBatchPrint = true,
//                    batchtIds = jsonStr,
//                    activeCount = isActiveCount
//                )
            }

            "DrawboardIMPage" -> {
                val imLink = options.arguments().get("imLink") as String
                val activity = getSafeActivityContext()
                val intent = Intent(activity, DrawboardIMActivity::class.java)
                intent.putExtra("imLink", imLink)
                activity.startActivity(intent)
            }

            "C1PrintPage" -> {
                val batchIds = options.arguments().get("batchIds") as List<Map<String, Int>>
                val printCount = options.arguments().get("printCount") as Int
                val printDensity = options.arguments().get("printDensity") as Int
                val hOffset = (options.arguments().get("hOffset") as Double).toFloat()
                val vOffset = (options.arguments().get("vOffset") as Double).toFloat()
                val halfCut = options.arguments().get("halfCut") as Boolean
                val cutDepth = options.arguments().get("cutDepth") as Int
                val printDivider = options.arguments().get("printDivider") as Boolean
                val printTemplate = options.arguments().get("printTemplate") as String
                val tubeType = options.arguments().get("tubeType") as Int
                val tubeSpecs = (options.arguments().get("tubeSpecs") as Double).toFloat()
                C1PrintActivity.s_batchIds = batchIds
                C1PrintActivity.s_printCount = printCount
                C1PrintActivity.s_printDensity = printDensity
                C1PrintActivity.s_hOffset = hOffset
                C1PrintActivity.s_vOffset = vOffset
                C1PrintActivity.s_halfCut = halfCut
                C1PrintActivity.s_cutDepth = cutDepth
                C1PrintActivity.s_printDivider = printDivider
                C1PrintActivity.s_printTemplate = printTemplate
                C1PrintActivity.s_tubeType = tubeType
                C1PrintActivity.s_tubeSpecs = tubeSpecs
                val activity = getSafeActivityContext()
                val intent = Intent(activity, C1PrintActivity::class.java)
//                activity.startActivity(intent)
            }

            "toOtherApp" -> {
                val packageName = options.arguments().get("packageName") as String
                val innerApp = InnerAppFragment.staticAppList.flatMap { it.appList }
                    .firstOrNull { it.packageName == packageName }
                var title = innerApp?.dialogTitle ?: "app00030"
                var message = innerApp?.dialogMessage ?: ""
                var iconUrl = innerApp?.icon
                    ?: "https://oss-print.niimbot.com/public_resources/material/dcdde35f15dc61e0bf2b6ba24040056c.png"


                var appName = ""
                if (packageName == "com.niimbot.pc") {
                    appName = "云打印电脑端"
                    title = innerApp?.dialogTitle ?: "app100001759"
                    message = innerApp?.dialogMessage ?: "app100001760"
                }


                BuriedHelper.trackEvent(
                    "show",
                    "004_308",
                    hashMapOf(
                        Pair("b_name", appName),
                    )
                )
                ThirdAppHintDialog.show(
                    context = getSafeActivityContext(),
                    title = title,
                    msg = message,
                    iconUrl = iconUrl,
                    downloadUrl = if (packageName == "com.niimbot.pc") "app100001762" else null
                ) {
                    BuriedHelper.trackEvent(
                        "click",
                        "004_308_334",
                        hashMapOf(
                            Pair("b_name", appName),
                        )
                    )
                    if (packageName == "com.niimbot.pc") {
                        NiimbotGlobal.putTextToClip(
                            getSafeActivityContext(),
                            LanguageUtil.findLanguageString("app100001762")
                        )
                        ToastInstance.INSTANCE.showSuccessToast(
                            getSafeActivityContext(),
                            "app100001765"
                        )
                    } else {
                        NiimbotGlobal.checkAndOpenThirdApp(
                            getSafeActivityContext(),
                            packageName,
                            innerApp?.router ?: "",
                        ) { hasInstalled ->
                            if (!hasInstalled) {
                                NiimbotGlobal.gotoMarket(getSafeActivityContext(), packageName)
                            }
                        }
                    }
                }
            }
            "toCanvasPage" -> {
                var isCustomLabel: Boolean = options.arguments().get("isCustomLabel") as Boolean
                val data = options.arguments().get("labelData")
                val labelId = options.arguments().get("labelId")
                LogUtils.e("call toCanvasPage， data ==> : $data")
                data.let {
                    if (labelId != null) {
                        TemplateUsedRecordUtils.saveUsedRecord(
                            labelId.toString(),
                            LoginDataEnum.isLogin
                        )
                    }
                    val result = com.niimbot.fastjson.JSONObject()
                    result.put("template", data)
                    result.put("labelId", labelId)
                    EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                        put("action", "jumpToCanvas")
                        put("data", result)
                    }.toJSONString())
                }
            }
        }

        //处理我的页面相关跳转
        UserCenterHelper.handleUserCenterPageJump(
            getSafeActivityContext(),
            options
        )

    }

    /**
     * 根据id跳转画板
     * templateId：模板id
     * defaultSelect: 默认选中类型
     */
    fun jumpToCanvasEditPage(templateId: String, defaultSelect: String) {
        if (templateId.isEmpty()) return
        GlobalLoadingHelper.showLoading(getSafeActivityContext())
        TemplateSyncLocalUtils.getTemplateDetails(
            templateId,
            false,
            true,
            false,
            isFolderShare = false
        ) { result, templateModule, errorMsg ->
            if (templateModule != null) {
                val paramsMap = mutableMapOf<String, Any>()
                paramsMap["isCustomLabel"] = false
                paramsMap["needRecordLabel"] = false
                paramsMap["defaultSelect"] = defaultSelect
                paramsMap["templateData"] =
                    TemplateModuleLocal().localToRemote(templateModule.toTemplateModuleLocal())
                val options = FlutterBoostRouteOptions.Builder()
                    .pageName("CanvasPage")
                    .arguments(paramsMap)
                    .build()
                toNativePage(options)
            } else {
                GlobalLoadingHelper.dismissLoading()
                if (!errorMsg.isNullOrEmpty()) {
                    melon.south.com.baselibrary.util.showToast(errorMsg)
                } else {
                    melon.south.com.baselibrary.util.showToast("app01160")
                }
            }
        }
    }

    private suspend fun jumpToCanvasEditPage(
        uniqueId: String?,
        templateDetail: TemplateModuleLocal,
        source: String?,
        printChannelCode: String?,
    ) {
        var drawData = TemplateModuleTransform.templateModuleLocalToTemplateModule(templateDetail!!)
        TemplateSyncLocalUtils.downloadTemplateResource(
            getSafeActivityContext(),
            drawData
        ) { fontResult, templateModule2 ->
            if (templateModule2 == null) {
                return@downloadTemplateResource
            }

            GlobalScope.launch {
                val niimbotDrawData = NiimbotDrawData()
                niimbotDrawData.apply {
                    this.niimbotTemplate =
                        templateModule2.toTemplateModuleLocal() ?: TemplateModuleLocal()
                    this.setNiimbotGoodsInfo()
                }
                uniqueId?.let {
                    val content = any2Json(niimbotDrawData.niimbotTemplate)
                    PrintHistoryFileUtils.savePrintHistory(content, it)
                }
                if (!niimbotDrawData.niimbotTemplate.canEdit() && !NetworkUtils.isConnected()) {
                    showToast("app01139")
                    return@launch
                }
                withContext(Dispatchers.Main) {
                    SyncTemplatePresenter.startNiimbotDrawActivity(
                        getSafeActivityContext(),
                        niimbotDrawData,
                        needRfidTemplate = false,
                        isRetainActivity = true,
                        fromIndustry = false,
                        needDownloadFonts = false,
                        source = source,
                        printChannelCode = printChannelCode
                    )
                }
            }

        }
    }

    private suspend fun preJumpToPrintSettingPage(
        fromPrintHistory: Boolean,
        linkedData: String?,
        uniqueId: String?,
        copies: Int,
        templateDetail: TemplateModuleLocal,
        needRfidTemplate: Boolean,
        showRfid: Boolean,
        rfidInfo: String,
        isFromCanvas: Boolean,
        isFromSelectData: Boolean,
        isFolderShare: Boolean,
        needDownloadRes: Boolean = true,
        pdfBindInfo: List<Map<String, Any>> = mutableListOf()
    ) {
        templateDetail?.templateVersion = TemplateVersionConst.BASE_SAVE_TEMPLATE_VERSION
        templateDetail?.elements?.forEach {
            if (it is TimeItemModule) {
                templateDetail?.templateVersion =
                    TemplateVersionConst.REALTIME_SAVE_TEMPLATE_VERSION
            }
        }
        if (templateDetail?.isGoodsTemplate() == true && TemplateUtils.hasCustomGoodFields(
                templateDetail
            )
        ) {
            templateDetail?.templateVersion =
                TemplateVersionConst.GOOD_ADD_FIELD_SAVE_TEMPLATE_VERSION
        }
        if (templateDetail?.isExcelXLSTemplate() == true) {
            templateDetail?.templateVersion =
                TemplateVersionConst.EXCEL_XLS_TEMPLATE_VERSION
        }

        var excelId: String? = ""
        //重建exterData数据
//        if (!TextUtils.isEmpty(linkedData)) {
//            val type = object :
//                TypeToken<Map<String, String>>() {}.type
//            val linkInfo =
//                GsonUtils.fromJson<Map<String, String>>(
//                    linkedData,
//                    type
//                )
//            excelId = linkInfo["excelId"]
//        }
//        if (fromPrintHistory && !TextUtils.isEmpty(excelId) && !excelId.equals("0") && templateDetail?.dataSource?.isNotEmpty() == true && templateDetail?.externalData?.list.isNullOrEmpty()) {
//            val externalData = addExcelDataWithExcelId(excelId!!)
//            externalData?.let {
//                templateDetail?.externalData = it
//            }
//            jumpToPrintSettingPage(
//                uniqueId,
//                copies,
//                templateDetail!!,
//                needRfidTemplate,
//                showRfid,
//                rfidInfo,
//                isFromCanvas,
//                isFromSelectData,
//                isFolderShare,
//                needDownloadRes,
//                pdfBindInfo
//            )
//        } else {
//            jumpToPrintSettingPage(
//                uniqueId,
//                copies,
//                templateDetail!!,
//                needRfidTemplate,
//                showRfid,
//                rfidInfo,
//                isFromCanvas,
//                isFromSelectData,
//                isFolderShare,
//                needDownloadRes,
//                pdfBindInfo
//            )
//        }
        jumpToPrintSettingPage(
            uniqueId,
            copies,
            templateDetail!!,
            needRfidTemplate,
            showRfid,
            rfidInfo,
            isFromCanvas,
            isFromSelectData,
            isFolderShare,
            needDownloadRes,
            pdfBindInfo
        )
    }

    private suspend fun jumpToPrintSettingPage(
        uniqueId: String?,
        copies: Int,
        templateDetail: TemplateModuleLocal,
        needRfidTemplate: Boolean,
        showRfid: Boolean,
        rfidInfo: String,
        isFromCanvas: Boolean,
        isFromSelectData: Boolean,
        isFolderShare: Boolean,
        needDownloadRes: Boolean,
        pdfBindInfo: List<Map<String, Any>> = mutableListOf(),
    ) {
        var drawData =
            TemplateModuleTransform.templateModuleLocalToTemplateModule(
                templateDetail!!
            )

        suspend fun handlePrint(templateModule: TemplateModule) {
            val niimbotDrawData = NiimbotDrawData().apply {
                templateModule.toTemplateModuleLocal()?.let {
                    niimbotTemplate = it
                }
            }

            uniqueId?.let {
                val content = any2Json(niimbotDrawData.niimbotTemplate)
                PrintHistoryFileUtils.savePrintHistory(content, it)
            }

            val printScene = when {
                !TextUtils.isEmpty(uniqueId) -> DrawIntentParams.PrintScene.PRINT_SCENE_HISTORY
                isFromSelectData -> DrawIntentParams.PrintScene.PRINT_SCENE_SELECT_DATA
                isFromCanvas -> DrawIntentParams.PrintScene.PRINT_SCENE_CANVAS
                else -> DrawIntentParams.PrintScene.PRINT_SCENE_MY_TEMPLATE
            }

            withContext(Dispatchers.Main) {
                try {
                    SyncTemplatePresenter.startPrintActivity(
                        getSafeActivityContext(),
                        niimbotDrawData = niimbotDrawData,
                        isShare = false,
                        needRfidTemplate = needRfidTemplate,
                        fromFlutter = true,
                        showRfid = showRfid,
                        rfidInfo = rfidInfo,
                        printScene = printScene,
                        copies = copies,
                        uniqueId = uniqueId,
                        isFromCanvas = isFromCanvas,
                        isFolderShare = isFolderShare,
                        pdfBindInfo = pdfBindInfo
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }

        if (needDownloadRes) {
            TemplateSyncLocalUtils.downloadTemplateResource(
                getSafeActivityContext(), drawData
            ) { _, templateModule2 ->
                if (templateModule2 == null) return@downloadTemplateResource
                GlobalScope.launch {
                    handlePrint(templateModule2)
                }
            }
        } else {
            GlobalScope.launch {
                handlePrint(drawData)
            }
        }
//        if(needDownloadRes){
//            TemplateSyncLocalUtils.downloadTemplateResource(
//                getSafeActivityContext(), drawData
//            ) {
//                    fontResult,templateModule2 ->
//                if(templateModule2 == null){
//                    return@downloadTemplateResource
//                }
//                GlobalScope.launch {
//                    val niimbotDrawData = NiimbotDrawData()
//                    niimbotDrawData.apply {
//                        val moduleLocal = templateModule2.toTemplateModuleLocal()
//                        if (moduleLocal != null) {
//                            niimbotTemplate = moduleLocal
//                        }
//                    }
//                    uniqueId?.let {
//                        val content = any2Json(niimbotDrawData.niimbotTemplate)
//                        PrintHistoryFileUtils.savePrintHistory(content, it)
//                    }
//                    var printScene = ""
//                    if (!TextUtils.isEmpty(uniqueId)) {
//                        printScene = DrawIntentParams.PrintScene.PRINT_SCENE_HISTORY
//                    } else if (isFromSelectData) {
//                        printScene = DrawIntentParams.PrintScene.PRINT_SCENE_SELECT_DATA
//                    } else {
//                        printScene = DrawIntentParams.PrintScene.PRINT_SCENE_CANVAS
//                    }
//                    withContext(Dispatchers.Main) {
//                        try {
//                            SyncTemplatePresenter.startPrintActivity(
//                                getSafeActivityContext(),
//                                niimbotDrawData = niimbotDrawData,
//                                isShare = false,
//                                needRfidTemplate = needRfidTemplate,
//                                fromFlutter = true,
//                                showRfid = showRfid,
//                                rfidInfo = rfidInfo,
//                                printScene = printScene,
//                                copies = copies,
//                                uniqueId = uniqueId,
//                                isFromCanvas = isFromCanvas,
//                                isFolderShare = isFolderShare,
//                                pdfBindInfo = pdfBindInfo
//                            )
//                        } catch (e: Exception) {
//                            e.printStackTrace()
//                        }
//                    }
//                }
//            }
//        }

    }

    /**
     * 原生跳转到flutter页面
     */
    fun toFlutterPage(options: FlutterBoostRouteOptions) {

        val arguments = options.arguments()
        val isTransParent = arguments?.get("transParent") ?: false
        if (options.pageName().contains("eTag")) {
            TemplateSyncLocalUtils.unDownloadList.clear()
        }
        val activityClass =
            if (isTransParent as Boolean) TransparencyPageActivity::class.java else TransparentActionBarActivity::class.java
        val intent = FlutterBoostActivity.CachedEngineIntentBuilder(
            activityClass
        ).destroyEngineWithActivity(false)
            .uniqueId(options.uniqueId())
            .url(options.pageName())
            .urlParams(options.arguments())
            .build(getSafeActivityContext())
        getSafeActivityContext().startActivityForResult(intent, 1)
    }


    /**
     * 处理跳转参数及url
     */
    fun toFlutter(router: String, params: String?) {

        LogUtils.e("call toFlutter: $router")
        if (router.equals("eTag")) {
            DeviceETagHelper.isEtagPage = true
        } else if (router.equals("C1")) {
            DeviceC1Helper.isC1Page = true
        }
        var paramsMap = mapOf<String, Any>()
        ///flutter 与原生路由跳转时会发生路由栈混乱得情况，此处加50毫秒做延时处理
        GlobalScope.launch {
            delay(50)
            BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                params?.let {
                    paramsMap = json2Any<Map<String, Any>>(params)!!
                }
                val options: FlutterBoostRouteOptions =
                    FlutterBoostRouteOptions
                        .Builder()
                        .pageName(router)
                        .arguments(paramsMap)
                        .uniqueId(UUID.randomUUID().toString())
                        .build()
                FlutterBoost.instance().open(options)
            }

        }

    }

    fun onDestroy() {
        FlutterEngineCache.getInstance().clear()
    }


}


suspend fun labelRecorder(options: FlutterBoostRouteOptions) {
    try {
        var template: TemplateModuleLocal? = null
        var label: TemplateModuleLocal? = null
        var mixinData: TemplateModuleLocal?
        var isCustomLabel: Boolean = options.arguments().get("isCustomLabel") as Boolean
        var data = options.arguments().get("labelData")
        var needRecordLabel = options.arguments().get("needRecordLabel") as? Boolean ?: true
        if (isCustomLabel) {
            data?.let {
                val languageCode = TextHookUtil.getInstance().languageName
                val rtlStatus = FlutterMethodInvokeManager.getRtlSwitchStatusSync(languageCode)
                val labelData = any2Json(it)
                label = TemplateModuleLocal.fromJson(labelData)
                label = TemplateSyncLocalUtils.moduleToLocal(label, true)
                label.apply {
                    this?.isEdited = 0
                    this?.local_type = TemplateModule.CREATE
                    this?.id = System.currentTimeMillis().toString()
                    this?.updateLabelId()
                }
                label?.addDefaultTextItem(rtlStatus)
            }
        } else {
            options.arguments().get("labelData")?.let {
                val labelData = any2Json(it)
                label = TemplateModuleLocal.fromJson(labelData)
                label = TemplateSyncLocalUtils.moduleToLocal(label, true)
                TemplateUsedRecordUtils.saveUsedRecord(
                    label!!.id,
                    LoginDataEnum.isLogin
                )
            }
        }
        options.arguments().get("templateData")?.let {
            val templateData = any2Json(it)
            val templateR = TemplateModuleR.fromJson(templateData)
            ///灰度处理
            if (templateR?.dataSources!!.isNotEmpty() && (templateR.commodityTemplate || templateR.profile.extrain.templateType == 2)) {
                template = TemplateModuleR().remoteToLocal(templateR)
            } else {
                template = TemplateModuleLocal.fromJson(templateData)
            }
        }


        //   template = TemplateSyncLocalUtils.moduleToLocal(template, false, false)
        if (options.arguments().get("labelData") == null && needRecordLabel) {
            template?.let { TemplateRequest.saveLabelData(it.profile.extrain.labelId) }
        }

        if (template != null && label != null) {
            label!!.elements = template!!.elements
            label!!.vip = template!!.vip
            label!!.freshCanvasRotate(template!!)
            label!!.hasVIPRes = template!!.hasVIPRes
            label!!.originTemplateId = template!!.id
            if (template!!.isFromIndustry()) {
                label!!.cloudTemplateId = template!!.id
            }
            label!!.id = System.currentTimeMillis().toString()
            label!!.usedFonts = template!!.usedFonts
            label!!.dataSource = template!!.dataSource
            label!!.externalData = template!!.externalData.copy()!!
            label!!.task = template!!.task.copy()!!
            label!!.bindInfo = template!!.bindInfo?.copy()
            label!!.totalPage = template!!.totalPage
            label!!.currentPage = template!!.currentPage
            label!!.profile.extrain.templateClass = 1
            label!!.templateVersion = template!!.templateVersion
            label!!.modify = template!!.modify
            label!!.name = template!!.name
            mixinData = label
        } else if (template != null) {
            template!!.originTemplateId = template!!.id
            if (template!!.isFromIndustry()) {
                template?.updateCloudTemplateId(template!!.id)
            }
            template!!.id = System.currentTimeMillis().toString()
            mixinData = template
        } else {
            mixinData = label
        }

        mixinData?.profile?.extrain?.templateType = -1
        mixinData?.profile?.extrain?.userId = "-1"
//            mixinData?.name = LanguageUtil.findLanguageString("app100000728")

        var drawData = TemplateModuleTransform.templateModuleLocalToTemplateModule(mixinData!!)
        drawData = TemplateSyncLocalUtils.writeModuleImg(drawData, false)
        mixinData = drawData.toTemplateModuleLocal()
        val niimbotDrawData = NiimbotDrawData()
        niimbotDrawData.apply {
            this.niimbotTemplate =
                mixinData ?: TemplateModuleLocal()
            this.setNiimbotGoodsInfo()
        }

        if (!niimbotDrawData.niimbotTemplate.canEdit() && !NetworkUtils.isConnected()) {
            showToast("app01139")
            return
        }
        var defaultSelect = options.arguments().get("defaultSelect") as? String
        withContext(Dispatchers.Main) {
            SyncTemplatePresenter.startNiimbotDrawActivity(
                getSafeActivityContext(),
                niimbotDrawData,
                needRfidTemplate = false,
                isRetainActivity = true,
                fromIndustry = true,
                defaultSelect = defaultSelect
            )
        }

    } catch (e: Exception) {
        e.printStackTrace()
    }
}


suspend fun labelCreate(options: FlutterBoostRouteOptions) {
    try {
        val fileInfo: HashMap<String, Any>? =
            if (options.arguments().containsKey("fileInfo")) options.arguments()
                .get("fileInfo") as HashMap<String, Any> else null
        var label: TemplateModuleLocal?
        var isCustomLabel: Boolean = options.arguments().get("isCustomLabel") as Boolean
        var data = options.arguments().get("labelData")
        if (isCustomLabel) {
            data.let {
                val labelData = any2Json(it)
                label = TemplateModuleLocal.fromJson(labelData)
                if (label?.id?.isNotEmpty() == true) {
                    label = TemplateSyncLocalUtils.moduleToLocal(label, true)
                }
                label.apply {
                    this?.isEdited = 0
                    this?.local_type = TemplateModule.CREATE
                    this?.id = System.currentTimeMillis().toString()
                    this?.updateLabelId()
                }
//                    label?.addDefaultTextItem()

            }
        } else {
            data.let {
                val languageCode = TextHookUtil.getInstance().languageName
                val rtlStatus = FlutterMethodInvokeManager.getRtlSwitchStatusSync(languageCode)
                val labelData = any2Json(it)
                label = TemplateModuleLocal.fromJson(labelData)
//                label = TemplateSyncLocalUtils.moduleToLocal(label, true)
//                label?.addDefaultTextItem(rtlStatus)
                val elements = label?.elements
                label = TemplateSyncLocalUtils.moduleToLocal(label, true, false)
                if (elements != null) {
                    label?.elements = elements
                }
//                label?.addDefaultTextItem()
                label?.vip = false
                label?.hasVIPRes = false
                TemplateUsedRecordUtils.saveUsedRecord(
                    label!!.id,
                    LoginDataEnum.isLogin
                )

                label?.id = System.currentTimeMillis().toString()
                label?.name = LanguageUtil.findLanguageString("app100000728")
            }
        }

        label?.profile?.extrain?.templateType = -1
        label?.dataSource?.clear()
        label?.externalData = ExternalData()
        label?.task = Task()
        label?.bindInfo = null
        label?.totalPage = 1
        label?.currentPage = 1
        if (label?.name.isNullOrEmpty()) {
            label?.name = LanguageUtil.findLanguageString("app100000728")
        }
        val drawData = NiimbotDrawData()
        drawData.apply {
            this.niimbotTemplate =
                label ?: TemplateModuleLocal()
            this.setNiimbotGoodsInfo()
            this.niimbotTemplate.profile.extrain.userId = "-1"
        }

        if (!drawData.niimbotTemplate.canEdit() && !NetworkUtils.isConnected()) {
            showToast("app01139")
            return
        }
        withContext(Dispatchers.Main) {
            SyncTemplatePresenter.startNiimbotDrawActivity(
                activity = getSafeActivityContext(),
                niimbotDrawData = drawData,
                isFromScanPrint = false,
                isRetainActivity = true,
                isCable = true,
                fileInfo = fileInfo
            )
        }

        if (!isCustomLabel) {
            TemplateUsedRecordUtils.saveUsedRecord(
                drawData.niimbotTemplate.id,
                LoginDataEnum.isLogin
            )
        }

        val isSearchTemplate: Boolean = options.arguments().get("isSearchTemplate") == null
        if (isSearchTemplate) {
            EventBus.getDefault().post(FinishTemplateIndustryEvent())
        }

    } catch (e: Exception) {
        e.printStackTrace()
    }
}

private fun getSafeActivityContext(): Activity {
    var activity = FlutterBoost.instance().currentActivity()
    if (activity == null) {
        activity = ActivityUtils.getTopActivity()
    }
    if (activity == null) {
        activity = BaseApplication.instance.mCurShowActivityReference.get()
    }
    return activity
}


