import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import '/src/widgets/components/add_subtract_text_button.dart';

///数值步进控件
class NumStepWidget<T extends num> extends StatefulWidget {
  /// 最小值
  final T min;

  /// 最大值
  final T max;

  /// 当前值
  final T value;

  /// 步进值
  final T stepValue;
  final bool canInput;
  final RegExp? allowInputRegExp;
  final ValueChanged<T>? valueChanged;
  final ThrottleAddCallback? throttleAddCallback;
  final ThrottleSubtractCallback? throttleSubtractCallback;
  final bool isEnable;
  /// 是否撑满父布局
  final bool isExpand;

  const NumStepWidget(
      {super.key,
      required this.min,
      required this.max,
      required this.stepValue,
      required this.value,
      this.valueChanged,
      this.canInput = false,
      this.isEnable = true,
      this.allowInputRegExp = null,
      this.throttleAddCallback = null,
      this.throttleSubtractCallback = null,
      this.isExpand = false});

  @override
  State<NumStepWidget> createState() => _NumStepWidgetState<T>();
}

class _NumStepWidgetState<E extends num> extends State<NumStepWidget<E>> {
  late E _value;
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _value = widget.value;
  }

  @override
  void setState(VoidCallback fn) {
    // TODO: implement setState
    if (mounted) {
      super.setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardVisibilityBuilder(
      builder: (_, bool isKeyboardVisible) {
        return AddSubtractTextButton(
          isSubtractEnable:
              double.parse(_value.toStringAsFixed(1)) > widget.min && widget.isEnable,
          isAddEnable: double.parse(_value.toStringAsFixed(1)) < widget.max && widget.isEnable,
          canInput: widget.canInput,
          allowInputRegExp: widget.allowInputRegExp,
          onSubtractClicked: () {
            if (!widget.isEnable) {
              return;
            }
            num v = _value;
            v -= widget.stepValue;
            _onValueChanged(v);
          },
          onAddClicked: () {
            if (!widget.isEnable) {
              return;
            }
            num v = _value;
            v += widget.stepValue;

            _onValueChanged(v);
          },
          textValue: formattedValue(),
          inputChange: (String inputValue) {
            if (!widget.isEnable) {
              return;
            }
            if (inputValue.isNotEmpty) {
              num temp = num.tryParse(inputValue) ?? 0;
              _onValueChanged(temp);
            }
          },
          focusNode: focusNode,
          throttleAddCallback: widget.throttleAddCallback,
          throttleSubtractCallback: widget.throttleSubtractCallback,
          isExpand: widget.isExpand,
        );
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    focusNode.dispose();
  }

  void _onValueChanged(v) {
    if (v < widget.min) {
      v = widget.min;
    } else if (v > widget.max) {
      v = widget.max;
    }
    E? newValue;
    if (E.toString() == 'double') {
      newValue = v as E;
    } else if (E.toString() == 'int') {
      newValue = v.toInt() as E;
    }

    if (_value == newValue) {
      return;
    }
    setState(() {
      _value = newValue!;
    });
    widget.valueChanged?.call(newValue!);
  }

  String formattedValue() {
    String value = E.toString() == 'double'
        ? (_value as double).toStringAsFixed(1)
        : _value.toString();
    if (value == "-0.0") {
      value = "0.0";
    }
    return value;
  }

  @override
  void didUpdateWidget(covariant NumStepWidget<E> oldWidget) {
    super.didUpdateWidget(oldWidget);
    _value = widget.value;
  }
}
