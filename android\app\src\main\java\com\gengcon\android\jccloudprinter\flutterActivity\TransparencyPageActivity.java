package com.gengcon.android.jccloudprinter.flutterActivity;

import android.content.Intent;
import android.graphics.Color;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ColorUtils;
import com.blankj.utilcode.util.KeyboardUtils;
import com.gengcon.android.jccloudprinter.R;
import com.idlefish.flutterboost.FlutterBoostRouteOptions;
import com.idlefish.flutterboost.containers.FlutterBoostActivity;
import com.niimbot.appframework_library.BaseApplication;
import com.niimbot.baselibrary.geetest.GeetestHelper;
import com.niimbot.baselibrary.loading.GlobalLoadingHelper;

import io.flutter.embedding.android.FlutterActivityLaunchConfigs.BackgroundMode;
import melon.south.com.baselibrary.util.StatusBarUtil;
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter;

public class TransparencyPageActivity extends FlutterBoostActivity {

    String listener;
    FlutterBoostRouteOptions options;

    public boolean isDrawboardPage = false;

    @Override
    protected BackgroundMode getBackgroundMode() {
        return super.getBackgroundMode();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
      android.util.Log.e("============relaunchApp=========", "savedInstanceState : $savedInstanceState, appInit: ${BaseApplication.appInit}");
      if (savedInstanceState != null && !BaseApplication.appInit) {
        AppUtils.relaunchApp();
      }
        /**
         * 全透状态栏
         */
        //21表示5.0
        super.onCreate(savedInstanceState);

        // 传统状态栏透明配置
//        Window window = getWindow();
//        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
//        window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
//                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
//        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
//        window.setStatusBarColor(Color.TRANSPARENT);

        // Android 15 及以上版本：确保内容不侵入系统栏
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM && !getUrl().equals("createLabelHome")) {
          if(getUrl().equals("printSettingDialog")  || getUrl().equals("printNullUiShowProgress")){
            applyInsetsV2(getWindow().getDecorView(),false,true);
          }else{
            applyInsets(getWindow().getDecorView());
          }
        }else{
          Window window = getWindow();
          window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
          window.getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE);
          window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
          window.setStatusBarColor(Color.TRANSPARENT);

        }

        GeetestHelper.INSTANCE.init(this);
    }

  /**
   * Android 15 及以上版本：应用系统窗口的 Insets
   * 确保内容不侵入状态栏和导航栏，并保持视觉协调
   */
  protected void applyInsets(View view) {
    ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
      int topInset = 0;
      int bottomInset = 0;

      // 获取系统栏的 insets
      android.view.WindowInsets windowInsets = (android.view.WindowInsets) insets.toWindowInsets();
      if (windowInsets != null) {
        topInset = windowInsets.getInsets(android.view.WindowInsets.Type.statusBars()).top;
        bottomInset = windowInsets.getInsets(android.view.WindowInsets.Type.navigationBars()).bottom;
      }
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        v.setBackgroundColor(getResources().getColor(R.color.white));
      }

      // 设置适当的 padding 以避免内容被系统栏遮挡
      v.setPadding(
        v.getPaddingLeft(),  // 保持原有的左右 padding
        v.getPaddingTop(),  // 为状态栏预留空间
        v.getPaddingRight(),
        bottomInset  // 为导航栏预留空间
      );

      return insets;
    });
  }

  protected void applyInsetsV2(View view,Boolean needTopPadding,Boolean needBottomPadding) {
    ViewCompat.setOnApplyWindowInsetsListener(view, (v, insets) -> {
      int topInset = 0;
      int bottomInset = 0;

      // 获取系统栏的 insets
      android.view.WindowInsets windowInsets = (android.view.WindowInsets) insets.toWindowInsets();
      if (windowInsets != null) {
        topInset = windowInsets.getInsets(android.view.WindowInsets.Type.statusBars()).top;
        bottomInset = windowInsets.getInsets(android.view.WindowInsets.Type.navigationBars()).bottom;
      }
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
        v.setBackgroundColor(getResources().getColor(R.color.white));
      }

      // 设置适当的 padding 以避免内容被系统栏遮挡
      v.setPadding(
        v.getPaddingLeft(),  // 保持原有的左右 padding
        needTopPadding ? topInset : 0,
        v.getPaddingRight(),
        needBottomPadding ? bottomInset : 0  // 为导航栏预留空间
      );

      return insets;
    });
  }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        KeyboardUtils.hideSoftInput(this);
        GeetestHelper.INSTANCE.cancel();
    }

    @Override
    protected void onStop() {
        super.onStop();
        GlobalLoadingHelper.INSTANCE.dismissLoading();
    }

    @Override
    public void onPostResume() {
        super.onPostResume();
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE && !getUrl().equals("createLabelHome")) {
//        com.niimbot.appframework_library.utils.AppUtils.INSTANCE.setStatusBarColor(this, getResources().getColor(melon.south.com.baselibrary.R.color.white));
        com.niimbot.appframework_library.utils.AppUtils.INSTANCE.setStatusBarLightColor(this, ColorUtils.getColor(R.color.transparent),true);
      }else{
        StatusBarUtil.setStatusBarTranslucent(this, true);
      }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }


    @Override
    protected void onUpdateSystemUiOverlays() {

    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if(listener == null){
            return;
        }

      if (listener.equals("ScanBarcode")) {
        if (grantResults[0] == 0) {
          boolean justCallbackScanCode = false;
          if(options.arguments().containsKey("justCallbackScanCode")){
            justCallbackScanCode = (Boolean)options.arguments().get("justCallbackScanCode");
          }
          SyncTemplatePresenter.INSTANCE.startScanActivity(
            this,
            options.requestCode(),
            justCallbackScanCode
          );
        }
      }
    }

    public void setPermissionListener(String listener, FlutterBoostRouteOptions options) {
        this.listener = listener;
        this.options = options;
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }
}
