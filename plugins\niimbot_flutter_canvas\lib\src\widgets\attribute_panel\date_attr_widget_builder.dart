import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:intl/intl.dart' as Intl;
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/date_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/floating_bar_visible_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/associated_selector_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/date_selector_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/deviation_selector_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/time_selector_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/clickable_item.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/src/localization/localization_public.dart';
import '../../utils/theme_color.dart';
import '../dialog/custom_menu_dialog.dart';
import 'attr_widget_builder.dart';
import 'use_instant_time_tip.dart';

class DateAttrWidgetBuilder extends AttrWidgetBuilder {
  static Function? updateFun;
  var contentHeight = 0.0;
  var isTimeRefresh = false;
  var _associatedKey = GlobalKey();
  final List<String> prefix = [
    intlanguage('app100001405', '无前缀'),
    intlanguage('app100001406', '生产日期'),
    intlanguage('app100001407', '上架日期'),
    intlanguage('app100001459', '制作日期'),
    intlanguage('app100001887', '开封日期'),
  ];
  final List<String> associatedPrefix = [
    intlanguage('app100001405', '无前缀'),
    intlanguage('app100001408', '保质期至'),
    intlanguage('app100001457', '有效期至'),
    intlanguage('app100001458', '到期时间'),
  ];
  ScrollController controller = ScrollController();
  DateAttrWidgetBuilder(AttrPanelState attrPanelState, double height, Function(Function, {bool isPopLogin}) guideBuyVip)
      : super(attrPanelState, height, guideBuyVip: guideBuyVip);

  @override
  List<MenuTag> getMenus(List<CanvasElement> canvasElements) {
    int index = 0;
    if ((canvasElements ?? []).length == 1) {
      menus = [
        MenuTag(index: index++, text: intlanguage('app00010', '时间'), isSelected: true, trackName: '时间'),
        MenuTag(index: index++, text: intlanguage('app01006', '样式'), isSelected: false, trackName: '样式'),
        MenuTag(index: index++, text: intlanguage('app01005', '字体'), isSelected: false, trackName: '字体'),
        MenuTag(index: index++, text: intlanguage("app100000760", "对齐/镜像"), isSelected: false, trackName: '对齐/镜像'),
      ];
    } else {
      menus = [
        MenuTag(index: index++, text: intlanguage("app100000760", "对齐/镜像"), isSelected: true, trackName: '对齐/镜像'),
        MenuTag(index: index++, text: intlanguage('app01006', '样式'), isSelected: false, trackName: '样式'),
        MenuTag(index: index++, text: intlanguage('app01005', '字体'), isSelected: false, trackName: '字体'),
      ];
    }

    return menus;
  }

  @override
  init(BuildContext context, List<CanvasElement> canvasElements) {
    DateElementHelper.setTimeNewOpen(false);
    DateElementHelper.isShowTimeElement = true;
    isTimeRefresh = true;
    return super.init(context, canvasElements);
  }

  @override
  dispose() {
    DateElementHelper.isShowTimeElement = false;
    return super.dispose();
  }

  @override
  Widget buildAttrWidget(
      MenuTag menuTag, List<CanvasElement> canvasElements, BuildContext context, VoidCallback refresh) {
    if (canvasElements.length == 1) {
      if (menuTag.index == 0) {
        return basicStyle(canvasElements, context, refresh);
      } else if (menuTag.index == 1) {
        return textStylePanel(canvasElements, context, refresh);
      } else if (menuTag.index == 2) {
        return textFontPanel(canvasElements, context, refresh, guideBuyVip);
      } else if (menuTag.index == 3) {
        return layoutPanel(canvasElements, context, refresh);
      }
    } else {
      if (menuTag.index == 0) {
        return layoutPanel(canvasElements, context, refresh);
      } else if (menuTag.index == 1) {
        return textStylePanel(canvasElements, context, refresh);
      } else if (menuTag.index == 2) {
        return textFontPanel(canvasElements, context, refresh, guideBuyVip);
      }
    }

    return Container(
      color: Colors.transparent,
      width: double.infinity,
      height: 100,
    );
  }

  /// 设置元素编辑状态
  void setElementEditingStatus(List<CanvasElement> canvasElements) {
    canvasElements.forEach((element) {
      if (element.data is TextElement) {
        (element.data as TextElement).isEditing = true;
      }
    });
  }

  /// 样式
  Widget basicStyle(List<CanvasElement> canvasElements, BuildContext context, VoidCallback refresh) {
    // 初始化关联状态为false
    var associated = false;
    // // 设置元素的编辑状态
    // setElementEditingStatus(canvasElements);
    // 获取画布元素列表中的第一个元素
    DateElement firstElement = canvasElements.first.data as DateElement;
    // 获取画布配置实现接口
    var canvasConfigIml = CanvasPluginManager().canvasConfigImpl;

    // 遍历画布元素列表，检查是否有元素已关联或有关联ID
    canvasElements.forEach((canvasElement) {
      DateElement element = canvasElement.data as DateElement;
      if (element.associated || element.associateId.isNotEmpty) {
        associated = true; // 如果发现有元素已关联，则将关联状态设置为true
      }
    });
    return AnimatedContainer(
      duration: Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      height: height + contentHeight,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: Colors.white,
      child: SingleChildScrollView(
        controller: controller,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeColor.divider, // 设置边框颜色
                  width: 0.5, // 设置边框宽度
                ),
                color: ThemeColor.COLOR_FFFCF5,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Container(
                height: 44,
                child: ClickableItem(
                  color: ThemeColor.COLOR_FFFCF5,
                  child: Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(18, 5, 5, 5),
                    child: Row(
                      //  crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          canvasConfigIml!.getDateShowText(),
                          style: TextStyle(
                            fontSize: 13,
                            color: ThemeColor.COLOR_78430E,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(
                          width: 6,
                        ),
                        SvgIcon(
                          'assets/common/vip_corner.svg',
                          width: 23,
                          height: 15,
                          useDefaultColor: false,
                        ),
                        GestureDetector(
                          child: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                            child: SvgIcon(
                              'assets/element/attribute/question.svg',
                              width: 20,
                              height: 20,
                              useDefaultColor: false,
                            ),
                          ),
                          onTap: () {
                            FloatingBarVisibleNotifier().forceDismiss();
                            showUseInstantTimeTip(context);
                          },
                        ),
                        Spacer(),
                        Selector<CanvasUserCenter, bool>(
                          builder: (context, isVip, child) {
                            Future.delayed(Duration(milliseconds: 50), () {
                              updateFun?.call();
                            });

                            return Transform.scale(
                              scale: 0.9,
                              child: CupertinoSwitch(
                                  value: firstElement.isRefreshDateEnable(),
                                  activeColor: CanvasTheme.of(context).highlightColor,
                                  onChanged: (bool on) async {
                                    FloatingBarHelper().dismissFloatingBar();
                                    showUseInstantTimeTip(context, isNeedCheckFirst: true);
                                    CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                                      "track": "click",
                                      "posCode": "108_140",
                                      "ext": {"state": on ? 1 : 0}
                                    });

                                    canvasElements.forEach((canvasElement) {
                                      DateElement element = canvasElement.data as DateElement;
                                      element.dateIsRefresh = on ? 1 : 0;
                                      if (element.dateIsRefresh == 1) {
                                        String timeFormat = DateElementHelper.getTimeFormat();
                                        if (DateElementHelper.sp
                                                ?.getInt(DateElementHelper.Recent_Instant_Time_Switch_Key) ==
                                            null) {
                                          timeFormat = "HH:mm:ss";
                                          DateElementHelper.recordTimeFormat(timeFormat);
                                          DateElementHelper.setTimeOpen(true);
                                        }
                                        element.timeFormat = timeFormat;
                                      }
                                    });
                                    refresh?.call();
                                    DateElementHelper.sp
                                        ?.setInt(DateElementHelper.Recent_Instant_Time_Switch_Key, on ? 1 : 0);
                                    Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                        .setDataChangedElements(canvasElements);
                                  }),
                            );
                          },
                          selector: (context, canvasCenter) => canvasCenter.isVip,
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 8,
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: ThemeColor.divider, // 设置边框颜色
                  width: 0.5, // 设置边框宽度
                ),
                color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Column(
                children: [
                  _dateAndTimeWidget(context, firstElement, canvasElements, refresh),
                  _deviationTimeWidget(context, firstElement, canvasElements, refresh)
                ],
              ),
            ),
            SizedBox(
              height: 8,
            ),
            _associatedWidget(associated, context, canvasElements, refresh),
          ],
        ),
      ),
    );
  }

  showUseInstantTimeTip(BuildContext context, {bool isNeedCheckFirst = false}) {
    if (isNeedCheckFirst) {
      SharedPreferences.getInstance().then((value) {
        bool hasfirstTip = value.getBool("has_first_use_instant_time") ?? false;
        if (!hasfirstTip) {
          showUseInstantTimeTip(context);
          value.setBool("has_first_use_instant_time", true);
        }
      });
    } else {
      FloatingBarHelper().dismissFloatingBar();
      showModalBottomSheet(
          barrierColor: const Color.fromARGB(90, 0, 0, 0),
          context: context,
          isScrollControlled: true,
          backgroundColor: const Color(0xFFFFFFFF),
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(18.0), topRight: Radius.circular(18.0))),
          builder: (BuildContext context) {
            return UseInstantTimeTip(
                currentTime: Intl.DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
                formart: 'yyyy-MM-dd HH:mm:ss');
          });
    }
  }

  _dateAndTimeWidget(
      BuildContext context, DateElement firstElement, List<CanvasElement> canvasElements, VoidCallback refresh) {
    DateElement dateElement = canvasElements.first.data as DateElement;
    DateElement? firstDateElement;
    CanvasElement? firstElement;
    if (dateElement.associateId.isEmpty && !dateElement.associated) {
      firstElement = canvasElements.first;
      firstDateElement = firstElement.data as DateElement;
    } else if (dateElement.associateId.isNotEmpty && dateElement.associated) {
      firstElement = canvasElements.first;
      firstDateElement = firstElement.data as DateElement;
    } else if (dateElement.associateId.isNotEmpty && !dateElement.associated) {
      firstElement = canvasKey.currentState?.getAssociateElement(canvasElements.first);
      firstDateElement = firstElement?.data as DateElement;
    }
    List<String> selectList = [];
    selectList.addAll(prefix);
    selectList.add(
        DateElementHelper.sp?.getString(DateElementHelper.Recent_Prefix_Key) ?? intlanguage('app100000966', '自定义'));
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        _prefixButton(
            context, firstDateElement!, canvasElements, DateElementHelper.Recent_Prefix_Key, selectList, refresh,
            (value) {
          firstDateElement?.contentTitle = value;
          if (value == intlanguage('app100001405', '无前缀')) {
            firstDateElement?.contentTitle = null;
            DateElementHelper.setContentTitle("");
          } else {
            DateElementHelper.setContentTitle(value);
            firstDateElement?.contentTitle = value;
          }
          firstElement?.data = firstDateElement as DateElement;
          refresh.call();
          Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([firstElement!]);
        }, (status, value) {
          if (status == NativeTrack.open) {
            CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
              "track": "click",
              "posCode": "108_295",
            });
          } else if (status == NativeTrack.click) {
            CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
              "track": "click",
              "posCode": "108_296_269",
              "ext": {"b_name": value}
            });
          } else if (status == NativeTrack.reName) {
            CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
              "track": "click",
              "posCode": "108_296_270",
              "ext": {"user_content": value}
            });
          }
        }),
        Container(
          child: Text(":", style: CanvasTheme.of(context).attrContentBodyTextStyle),
        ),
        Expanded(flex: 3, child: _dateWidget(context, firstDateElement, canvasElements, refresh)),
        Expanded(flex: 2, child: _timeWidget(context, firstDateElement, canvasElements, refresh))
      ],
    );
  }

  _prefixButton(BuildContext context, DateElement firstElement, List<CanvasElement> canvasElements, String spKey,
      List<String> menuList, VoidCallback refresh, Function function, Function onOpen) {
    return CustomMenuDialog(
      menuList,
      Container(
        margin: EdgeInsetsDirectional.fromSTEB(9, 8, 3, 7),
        child: Row(
          children: [
            Container(
              padding: EdgeInsetsDirectional.fromSTEB(10, 8, 7, 8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(8)),
                border: Border.all(
                  color: CanvasTheme.of(context).attributeDividerColor ?? const Color(0xFFEBEBEB), // 边框线颜色
                  width: 0.5, // 边框线宽度
                ),
              ),
              child: Row(
                children: [
                  Container(
                    constraints: BoxConstraints(
                      maxWidth: 80.0, // 设置最大宽度为200像素
                    ),
                    child: Text(
                      firstElement.contentTitle == null || firstElement.contentTitle?.isEmpty == true
                          ? intlanguage('app100001405', '无前缀')
                          : firstElement.contentTitle ?? '',
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(fontSize: 13.0, color: Color(0xFF161616), fontWeight: FontWeight.w400),
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  SvgIcon(
                    'assets/common/unfold.svg',
                    color: ThemeColor.COLOR_BLACK_30,
                  )
                ],
              ),
            )
          ],
        ),
      ),
      (value) {
        function.call(value);
      },
      title: firstElement.contentTitle == null || firstElement.contentTitle?.isEmpty == true
          ? intlanguage('app100001405', '无前缀')
          : firstElement.contentTitle ?? '',
      spKey: spKey,
      onOpen: (status, value) {
        FloatingBarHelper().dismissFloatingBar();
        onOpen.call(status, value);
      },
    );
  }

  _dateWidget(
      BuildContext context, DateElement firstElement, List<CanvasElement> canvasElements, VoidCallback refresh) {
    return Container(
      margin: EdgeInsetsDirectional.fromSTEB(9, 7, 0, 7),
      child: DateSelectorWidget(
        time: firstElement.time,
        dateFormat: firstElement.dateFormat!,
        viewMode: DateSelectorViewMode.Date,
        disable: firstElement.dateIsRefresh == 1,
        dateOpen: firstElement.isDateOpen(),
        disableToastFun: () {},
        enableToastFun: (isEnable) {
          contentHeight = isEnable ? 100 : 0;
          refresh.call();
        },
        completed: (int time, String dateFormat, bool dateOpen) {
          // 遍历canvasElements列表，更新每个元素的日期格式和时间
          canvasElements.forEach((canvasElement) {
            DateElement element = canvasElement.data as DateElement;
            // 如果当前元素的日期格式或时间不匹配，则更新日期格式
            if (element.dateFormat != dateFormat || element.time != time) {
              element.dateFormat = DateElementHelper.getDateFormat();
            }
            // 记录当前使用的日期格式
            DateElementHelper.recordDateFormat(dateFormat);
            // 设置日期为开启状态
            DateElementHelper.setDateOpen(true);
            // 更新元素的日期格式和时间
            element.dateFormat = dateFormat;
            element.time = time;
          });

          // 通知监听器，数据元素已变更
          Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
        },
        isShowDate: (dateOpen) {
          canvasElements.forEach((canvasElement) {
            DateElement element = canvasElement.data as DateElement;
            if (dateOpen) {
              element.dateFormat = DateElementHelper.getDateFormat();
              DateElementHelper.setDateOpen(true);
            } else {
              element.dateFormat = "";
              DateElementHelper.setDateOpen(false);
            }
            if (!dateOpen && !element.isTimeOpen()) {
              DateElementHelper.setTimeOpen(true);
              element.timeFormat = DateElementHelper.getTimeFormat();
            }
          });

          Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
          refresh?.call();
        },
      ),
    );
  }

  _timeWidget(
      BuildContext context, DateElement firstElement, List<CanvasElement> canvasElements, VoidCallback refresh) {
    return Container(
      margin: EdgeInsetsDirectional.fromSTEB(5, 7, 7, 7),
      child: TimeSelectorWidget(
        time: firstElement.time,
        format: firstElement.timeFormat!,
        viewMode: TimeSelectorViewMode.Time,
        disable: firstElement.dateIsRefresh == 1,
        timeUnit: firstElement.timeUnit,
        timeOpen: firstElement.isTimeOpen(),
        disableToastFun: () {
          LoadingMix.showToast(intlanguage('app01399', '请关闭实时时间开关'));
        },
        enableToastFun: (isEnable) {
          contentHeight = isEnable ? 100 : 0;
          refresh.call();
        },
        completed: (int time, String timeFormat, String timeUnit) {
          // 遍历canvasElements列表，更新每个元素的时间格式、时间和时间单位
          canvasElements.forEach((canvasElement) {
            DateElement element = canvasElement.data as DateElement;
            // 如果当前元素的时间格式、时间或时间单位不匹配，则更新时间格式为默认值
            if (element.timeFormat != timeFormat || element.time != time || element.timeUnit != timeUnit) {
              element.timeFormat = DateElementHelper.getTimeFormat();
            }
            // 设置时间格式、时间和时间单位
            element.timeFormat = timeFormat;
            element.time = time;
            DateElementHelper.setTimeOpen(true); // 标记时间选择为开启状态
            DateElementHelper.recordTimeFormat(timeFormat); // 记录选择的时间格式
            element.timeUnit = timeUnit;
          });

          // 通知监听器，数据元素已变更
          Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
        },
        isShowTime: (timeOpen) {
          canvasElements.forEach((canvasElement) {
            DateElement element = canvasElement.data as DateElement;
            if (timeOpen) {
              DateElementHelper.setTimeOpen(true);
              element.timeFormat = DateElementHelper.getTimeFormat();
            } else {
              DateElementHelper.setTimeOpen(false);
              element.timeFormat = "";
            }

            if (!timeOpen && !element.isDateOpen()) {
              DateElementHelper.setDateOpen(true);
              element.dateFormat = DateElementHelper.getDateFormat();
            }
          });
          Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(canvasElements);
          refresh?.call();
        },
      ),
    );
  }

  _deviationTimeWidget(
      BuildContext context, DateElement firstElement, List<CanvasElement> canvasElements, VoidCallback refresh) {
    return Container(
      padding: EdgeInsetsDirectional.fromSTEB(18, 0, 0, 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
              child: Text(intlanguage('app100001421', '时间偏移'),
                  style: TextStyle(
                    fontSize: 13,
                    color: ThemeColor.COLOR_161616,
                    fontWeight: FontWeight.w400,
                  ))),
          Expanded(
            child: Container(),
          ),
          Container(
            margin: EdgeInsetsDirectional.fromSTEB(5, 7, 7, 7),
            child: Container(
              decoration: BoxDecoration(
                color: ThemeColor.background,
                borderRadius: BorderRadius.all(Radius.circular(8)),
                border: Border.all(
                  color: CanvasTheme.of(context).attributeDividerColor ?? const Color(0xFFEBEBEB), // 边框线颜色
                  width: 0.5, // 边框线宽度
                ),
              ),
              child: DeviationSelectorWidget(
                  timeOffset: firstElement.timeOffset,
                  completed: (int timeOffset) {
                    canvasElements.forEach((canvasElement) {
                      DateElement element = canvasElement.data as DateElement;
                      element.timeOffset = timeOffset;
                      if (timeOffset != 0) {
                        if (timeOffset % (24 * 60) != 0) {
                          element.timeFormat = DateElementHelper.getTimeFormat();
                          DateElementHelper.setTimeOpen(true);
                        }
                        if ((timeOffset > 24 * 60 || timeOffset < -(24 * 60)) && !DateElementHelper.getDateOpen()) {
                          element.dateFormat = DateElementHelper.getDateFormat();
                          DateElementHelper.setDateOpen(true);
                        }
                      }
                    });

                    Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                        .setDataChangedElements(canvasElements);
                  }),
            ),
          )
        ],
      ),
    );
  }

  _associatedWidget(bool associated, BuildContext context, List<CanvasElement> canvasElements, VoidCallback refresh) {
    // 首先从画布元素中获取第一个元素的数据
    List<AssociatedModel> commonlyUsedTimes = DateElementHelper.getCommonlyUsedTimes();
    DateElement dateElement = canvasElements.first.data as DateElement;
    DateElement? firstDateElement;
    CanvasElement? firstElement;
    int associateType = 0;
    // 检查是否存在与第一个元素关联的元素
    if (canvasKey.currentState?.getAssociateElement(canvasElements.first) != null) {
      // 如果存在关联，并且关联ID不为空
      if (dateElement.associateId.isNotEmpty && dateElement.associated) {
        // 获取关联的元素及其数据
        firstElement = canvasKey.currentState?.getAssociateElement(canvasElements.first);
        firstDateElement = firstElement?.data as DateElement?;
      } else {
        // 如果没有关联ID或未设置关联，直接使用第一个元素及其数据
        firstElement = canvasElements.first;
        firstDateElement = firstElement.data as DateElement?;
      }
      associateType = firstDateElement?.associateType ?? 0;
    } else {
      // 如果没有找到关联元素，设置关联标志为false
      associated = false;
    }

    List<String> selectList = [];
    selectList.addAll(associatedPrefix);
    selectList.add(
        DateElementHelper.sp?.getString(DateElementHelper.Recent_Associated_Key) ?? intlanguage('app100000966', '自定义'));
    return !associated
        ? GestureDetector(
            onTap: () {
              if (dateElement.isLock == 1) return;
              CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                "track": "click",
                "posCode": "108_298",
              });
              canvasKey.currentState?.createAssociateElement(context, canvasElements.first);
              while (controller.hasClients) {
                double bottomValue = controller.position.maxScrollExtent;
                controller.animateTo(
                  bottomValue,
                  duration: Duration(milliseconds: 250),
                  curve: Curves.easeOut,
                );
                break;
              }
            },
            child: Container(
                height: 44,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: ThemeColor.divider, // 设置边框颜色
                    width: 0.5, // 设置边框宽度
                  ),
                  color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgIcon(
                      "assets/element/attribute/value_add.svg",
                      color: dateElement.isLock == 0 ? ThemeColor.COLOR_595959 : ThemeColor.disable_text,
                      fit: BoxFit.cover,
                    ),
                    Container(
                      key: _associatedKey,
                      padding: EdgeInsetsDirectional.only(start: 5),
                      child: Text(intlanguage('app100001431', '添加关联时间'),
                          style: TextStyle(
                            fontSize: 14,
                            color: dateElement.isLock == 0 ? ThemeColor.COLOR_595959 : ThemeColor.disable_text,
                            fontWeight: FontWeight.w400,
                          )),
                    ),
                  ],
                )),
          )
        : Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: ThemeColor.divider, // 设置边框颜色
                width: 0.5, // 设置边框宽度
              ),
              color: CanvasTheme.of(context).attributeGroupBackgroundColor,
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _prefixButton(context, firstDateElement!, canvasElements, DateElementHelper.Recent_Associated_Key,
                        selectList, refresh, (value) {
                      // 设置firstDateElement的内容标题为value
                      firstDateElement?.contentTitle = value;
                      // 如果value等于'无前缀'（根据国际化资源intlanguage获取），则将内容标题设为null，否则保持为value
                      if (value == intlanguage('app100001405', '无前缀')) {
                        firstDateElement?.contentTitle = null;
                      } else {
                        firstDateElement?.contentTitle = value;
                      }
                      // 将更新后的firstDateElement赋值给firstElement的数据属性
                      firstElement?.data = firstDateElement as DateElement;
                      // 调用refresh方法来刷新界面
                      refresh.call();
                      // 使用Provider更新ElementsDataChangedNotifier中的数据，并通知UI刷新
                      Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                          .setDataChangedElements([firstElement!]);
                    }, (status, value) {
                      if (status == NativeTrack.open) {
                        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                          "track": "click",
                          "posCode": "108_299",
                        });
                      } else if (status == NativeTrack.click) {
                        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                          "track": "click",
                          "posCode": "108_300_269",
                          "ext": {"b_name": value}
                        });
                      } else if (status == NativeTrack.reName) {
                        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                          "track": "click",
                          "posCode": "108_300_270",
                          "ext": {"user_content": value}
                        });
                      }
                    }),
                    const Spacer(),
                    Container(
                      height: 32,
                      child: TabSwitcher(firstDateElement!.associateType, (value) {
                        firstDateElement?.associateType = value;
                        // 将更新后的firstDateElement赋值给firstElement的数据属性
                        firstElement?.data = firstDateElement as DateElement;
                        // 调用refresh方法来刷新界面
                        refresh.call();
                        // 使用Provider更新ElementsDataChangedNotifier中的数据，并通知UI刷新
                        Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                            .setDataChangedElements([firstElement!]);
                        if (value == 0) {
                          Future.delayed(Duration(milliseconds: 100), () {
                            double bottomValue = controller.position.maxScrollExtent;
                            controller.animateTo(
                              bottomValue,
                              duration: Duration(milliseconds: 300),
                              curve: Curves.easeOut,
                            );
                          });
                        }
                      }),
                    ),
                    const SizedBox(
                      width: 8,
                    ),
                  ],
                ),
                SizedBox(height: 3),
                associateType == 1
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              child: Text(intlanguage('app100001966', '时长'),
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: ThemeColor.COLOR_161616,
                                    fontWeight: FontWeight.w400,
                                  ))),
                          Expanded(
                            child: Container(),
                          ),
                          Container(
                            margin: EdgeInsetsDirectional.fromSTEB(5, 0, 7, 0),
                            child: Container(
                              decoration: BoxDecoration(
                                color: ThemeColor.background,
                                borderRadius: BorderRadius.all(Radius.circular(8)),
                                border: Border.all(
                                  color:
                                      CanvasTheme.of(context).attributeDividerColor ?? const Color(0xFFEBEBEB), // 边框线颜色
                                  width: 0.5, // 边框线宽度
                                ),
                              ),
                              child: AssociatedSelectorWidget(
                                  validityPeriodNew: firstDateElement.validityPeriodNew,
                                  validityPeriodUnit: firstDateElement.validityPeriodUnit,
                                  completed: (int timeOffset, String key) {
                                    firstDateElement?.validityPeriodUnit = key; // 设置有效期单位
                                    // 更新第一个日期元素的有效期和单位，并根据有效期单位的不同，进行不同的数据处理和界面刷新。
                                    firstDateElement?.validityPeriodNew = timeOffset; // 设置有效期时长
                                    if (firstDateElement?.validityPeriodUnit == DateElementHelper.Associated_Hour) {
                                      // 如果有效期单位是小时，进行如下处理
                                      firstDateElement?.timeFormat = DateElementHelper.getTimeFormat(); // 设置时间格式
                                      var associatedElement =
                                          canvasKey.currentState?.getAssociateElement(firstElement!); // 获取关联元素
                                      var associatedElementData = associatedElement?.data as DateElement; // 获取关联元素的数据
                                      associatedElementData.timeFormat =
                                          DateElementHelper.getTimeFormat(); // 设置关联元素的时间格式
                                      associatedElement?.data = associatedElementData; // 更新关联元素的数据
                                      firstElement?.data = firstDateElement as DateElement; // 更新第一个元素的数据
                                      refresh.call(); // 调用刷新方法
                                      // 通知数据发生变化，更新界面
                                      Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                          .setDataChangedElements([firstElement!, associatedElement!]);
                                    } else {
                                      // 如果有效期单位不是小时，进行如下处理
                                      firstElement?.data = firstDateElement as DateElement; // 更新第一个元素的数据
                                      refresh.call(); // 调用刷新方法
                                      // 通知数据发生变化，更新界面
                                      Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                          .setDataChangedElements([firstElement!]);
                                    }
                                  }),
                            ),
                          ),
                        ],
                      )
                    :
                    // 选项按钮区域
                    Container(
                        child: Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: commonlyUsedTimes.map((e) {
                            final isSelected = DateElementHelper.isSelectedByCommonlyUsedTime(
                                [e], firstDateElement!.validityPeriodUnit, firstDateElement.validityPeriodNew);
                            return GestureDetector(
                              onTap: () {
                                firstDateElement?.validityPeriodUnit = e.key; // 设置有效期单位
                                firstDateElement?.validityPeriodNew = e.realyValue!; // 设置有效期时长
                                if (firstDateElement?.validityPeriodUnit == DateElementHelper.Associated_Hour) {
                                  // 如果有效期单位是小时，进行如下处理
                                  firstDateElement?.timeFormat = DateElementHelper.getTimeFormat(); // 设置时间格式
                                  var associatedElement =
                                      canvasKey.currentState?.getAssociateElement(firstElement!); // 获取关联元素
                                  var associatedElementData = associatedElement?.data as DateElement; // 获取关联元素的数据
                                  associatedElementData.timeFormat = DateElementHelper.getTimeFormat(); // 设置关联元素的时间格式
                                  associatedElement?.data = associatedElementData; // 更新关联元素的数据
                                  firstElement?.data = firstDateElement as DateElement; // 更新第一个元素的数据
                                  refresh.call(); // 调用刷新方法
                                  // 通知数据发生变化，更新界面
                                  Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                      .setDataChangedElements([firstElement!, associatedElement!]);
                                } else {
                                  // 如果有效期单位不是小时，进行如下处理
                                  firstElement?.data = firstDateElement as DateElement; // 更新第一个元素的数据
                                  refresh.call(); // 调用刷新方法
                                  // 通知数据发生变化，更新界面
                                  Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                                      .setDataChangedElements([firstElement!]);
                                }
                              },
                              child: Container(
                                width: (MediaQuery.of(context).size.width - 32 - 40) / 4,
                                height: 29,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  border: Border.all(color: Color(0xFF3C3C43).withOpacity(0.09), width: 0.5),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  e.getDateTimeDescrptime(),
                                  style: TextStyle(
                                    fontSize: 12,
                                    overflow: TextOverflow.ellipsis,
                                    color: isSelected ? ThemeColor.brand : Color(0xFF3C3C43).withOpacity(0.6),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                const SizedBox(height: 8)
              ],
            ),
          );
  }
}

class TabSwitcher extends StatefulWidget {
  int selectedIndex = 0;
  Function(int)? selectCallback;
  TabSwitcher(this.selectedIndex, this.selectCallback);
  @override
  _TabSwitcherState createState() => _TabSwitcherState();
}

class _TabSwitcherState extends State<TabSwitcher> {
  int selectedIndex = 0;
  List tabs = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    selectedIndex = widget.selectedIndex;
    String value1 = intlanguage('app100001964', '常用');
    String value2 = intlanguage('app100001965', '自定义');
    tabs = [value1, value2];
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    // 计算 tab 的最大宽度
    double maxWidth = 0;
    final tabWidgets = tabs.map((tab) {
      final text = Text(
        tab,
        style: TextStyle(fontSize: 15),
      );
      final textWidth = _getTextWidth(text);
      maxWidth = maxWidth > textWidth ? maxWidth : textWidth;
      return text;
    }).toList();
    maxWidth = maxWidth < 76 ? 76 : maxWidth;
    return Container(
      width: maxWidth * tabs.length + 8,
      height: 32,
      padding: EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Color(0xFF747480).withOpacity(0.08),
        borderRadius: BorderRadius.circular(9),
      ),
      child: Stack(
        children: [
          // 选中项背景动画
          AnimatedAlign(
            alignment: selectedIndex == 0 ? Alignment.centerLeft : Alignment.centerRight,
            duration: Duration(milliseconds: 0),
            child: Container(
              width: maxWidth,
              height: 28,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(7),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
          // 按钮内容
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: List.generate(tabs.length, (index) {
              final selected = selectedIndex == index;
              return Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      selectedIndex = index;
                      widget.selectCallback?.call(selectedIndex);
                    });
                  },
                  child: Container(
                    height: 28,
                    width: maxWidth,
                    color: Colors.transparent,
                    alignment: Alignment.center,
                    child: Text(
                      tabs[index],
                      style: TextStyle(
                        overflow: TextOverflow.ellipsis,
                        fontSize: 12,
                        color: Colors.black,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              );
            }),
          )
        ],
      ),
    );
  }

  double _getTextWidth(Text text) {
    final textPainter = TextPainter(
      text: TextSpan(text: text.data, style: text.style),
      maxLines: 1,
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    return textPainter.size.width;
  }
}
