import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/canvas_industry_template_logic.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/canvas_industry_template_state.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/model/industry_template_category_list_model.dart';
import 'package:niimbot_flutter_canvas/src/utils/toast_util.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CanvasSearchTemplateController extends GetxController {
  /// 模版搜索结果
  var templatesResult = [].obs;
  var scrollOffset = 0.0;

  /// 展示搜索页还是历史记录
  var fade = CrossFadeState.showFirst.obs;
  ScrollController scrollController = ScrollController();

  /// 存储实例
  SharedPreferences? sp;

  ///搜索状态
  var searchState = CanvasIndustryHomeState.loading;

  /// 搜索历史
  var searchList = Rxn<List<String>>([]);

  /// 传输流
  final _streamController = StreamController();

  CanvasIndustryTemplateLogic? logic;

  var isSearching = false.obs;
  var currentSearchKey = "";

  //是否反馈过
  bool notifyState = false;

  CanvasSearchTemplateController(CanvasIndustryTemplateLogic? logic) {
    this.logic = logic;
  }

  /// 是否是标签条码
  bool get isLabelBarcode {
    if (templatesResult.length == 1 && templatesResult.first.profile?.extrain?.templateClass == 0) {
      return true;
    } else {
      return false;
    }
  }

  @override
  void onInit() async {
    //getHistroyList();
    super.onInit();
  }

  @override
  void onClose() {
    _streamController.close();
    scrollController.dispose();
    if (logic?.getState().canvasIndustryTemplateInterface?.isLogin() == true) {
      searchList.value = [];
      sp?.setStringList('search_history', searchList.value ?? []);
    }
    super.onClose();
  }

  getFocusNode() {
    return logic?.getState().node;
  }

  /// 获取搜索结果
  getSearchResult(
      {int page = 1,
      int limit = 10,
      num width = 0,
      num height = 0,
      String searchKey = '',
      bool isMoreData = false,
      String sort = '',
      String labelId = '',
      Function(IndustryTemplateCategoryListModel?)? resultClosure}) {
    searchState = CanvasIndustryHomeState.showContainer;
    currentSearchKey = searchKey;
    // width height 传入当前尺寸 -wy
    Map<String, dynamic> parameters = {
      'sort': 'drawboard',
      'labelId': logic?.getState().labelId,
      'page': page,
      'limit': limit,
      'searchText': searchKey,
      'width': logic?.getState().sizeList[0].width,
      'height': logic?.getState().sizeList[0].height,
      'isCustomSize': false,
    };
    // 画板搜索使用字段   "sort": "drawboard"，labelId 当前的标签纸id
    if (sort.isNotEmpty) {
      parameters["sort"] = sort;
    }
    if (labelId.isNotEmpty) {
      parameters["labelId"] = labelId;
    }
    isSearching.value = true;

    logic?.getState().canvasIndustryTemplateInterface?.getSearchResult(parameters, (data) {
      isSearching.value = false;
      var value = IndustryTemplateCategoryListModel.fromJson(data);
      if (isMoreData) {
        if (value.list != null) {
          templatesResult.addAll((value.list!));
        }
      } else {
        scrollController.animateTo(
          0.0,
          duration: Duration(milliseconds: 10),
          curve: Curves.easeInOut,
        );
        if (value.list != null) {
          templatesResult.value = (value.list!);
        }
      }
      notifyState = false;
      searchState = CanvasIndustryHomeState.showContainer;
      resultClosure?.call(value);
    }, (code, message) {
      isSearching.value = false;
      searchState = CanvasIndustryHomeState.error;
      resultClosure?.call(null);
    });
  }

  ///获取用户搜索历史
  updateHistroyList(state) async {
    sp = await SharedPreferences.getInstance();
    var histroyList = sp?.getStringList('search_history') ?? [];
    if (histroyList.isNotEmpty) {
      uplpadHistroy(state, histroyList, isFirstUpload: true);
    } else {
      getHistroyList(state);
    }
  }

  ///获取搜索记录
  getHistroyList(state) {
    logic?.getState().canvasIndustryTemplateInterface?.getHistroyList((data) {
      List arguments = data["queryIndustryTemplateSearchHistories"]["content"] as List;
      if (arguments.isNotEmpty) {
        searchList.value = [];
        sp?.setStringList('search_history', searchList.value ?? []);

        arguments.forEach((element) {
          searchList.value?.add(element["name"]);
        });
      }
    }, (p0, p1) {});
  }

  ///上传搜索记录
  uplpadHistroy(state, List<String> value, {isFirstUpload = false}) {
    logic?.getState().canvasIndustryTemplateInterface?.uplpadHistroy({
      "input": {"content": value}
    }, (hasException) {
      if (!hasException) {
        if (isFirstUpload) {
          getHistroyList(state);
        }
      } else {
        if (isFirstUpload) {
          searchList.value = sp?.getStringList('search_history') ?? [];
        }
      }
    }, (p0, p1) {});
  }

  ///清空搜索记录
  clearHistroyList() {
    if (!(logic?.getState().canvasIndustryTemplateInterface?.isLogin() == true)) {
      searchList.value = [];
      sp?.setStringList('search_history', searchList.value ?? []);
    } else {
      logic?.getState().canvasIndustryTemplateInterface?.clearHistroyList((hasException) {
        if (!hasException) {
          searchList.value = [];
          sp?.setStringList('search_history', searchList.value ?? []);
        } else {
          var error = intlanguage('app01139', '网络异常');
          showToast(msg: error);
        }
      }, (p0, p1) {});
    }
  }
}

extension StreamEvent on CanvasSearchTemplateController {
  addEventListen(Function(dynamic event) onData) {
    _streamController.stream.listen(onData);
  }

  sendEvent(String text) {
    _streamController.sink.add(text);
  }
}

extension SaveAndDelete on CanvasSearchTemplateController {
  /// 存储搜索的key
  saveSearchText(state, String text) {
    /// 先进行去重
    searchList.value?.removeWhere((element) => element == text);

    /// 插入到最前面
    searchList.value?.insert(0, text);
    if (searchList.value?.length == 11) {
      searchList.value?.removeLast();
    }

    sp?.setStringList('search_history', searchList.value ?? []);
    uplpadHistroy(state, [text]);
  }

  /// 清除所有历史记录
  deleteAllHistory() {
    clearHistroyList();
  }

  insertSearchTextToFront(int index) {
    searchList.value?.removeAt(index);
  }

  reportSceneInfo(Function success) {
    logic?.reportSceneInfo(() {
      success.call();
    });
  }
}
