/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "본 앱에서 바코드 스캔, 글자 인식, 촬영 기능 사용 시 카메라 사용 권한이 필요합니다. 카메라를 켜시겠습니까?";
NSBluetoothPeripheralUsageDescription = "본 앱에서 프린터 연결 서비스 이용 시 블루투스 사용 권한이 필요합니다. 블루투스를 켜시겠습니까?";
NSBluetoothAlwaysUsageDescription = "본 앱에서 프린터 연결 서비스 이용 시 블루투스 사용 권한이 필요합니다. 블루투스를 켜시겠습니까?";
NSContactsUsageDescription = "본 앱에서 연락인 인식 서버스 이용 시 주소록 권한이 필요합니다. 주소록을 켜시겠습니까?";
NSMicrophoneUsageDescription = "본 앱에서 음성 인식 서비스 이용 시 마이크 권한이 필요합니다. 마이크를 켜시겠습니까?";
NSPhotoLibraryUsageDescription = "해당 권한은 이미지 소재 프린트, 바코드 인식, QR코드 인식, 글자 인식, 프로필 사진 설정 시 필요합니다. NIIMBOT에서 정상적으로 사진첩을 방문할 수 있도록 \"모든 이미지 방문 허용\"을 설정해 주세요. 만일 \"이미지 선택...\" 사용 시 선택하지 않은 이미지와 추후 추가된 이미지는 NIIMBOT에서 방문할 수 없습니다.";
NSLocationWhenInUseUsageDescription = "근처 Wi-Fi 네트워크를 사용할 수 있도록 NIIMBOT에서 위치 권한을 요청합니다.";
NSLocationAlwaysUsageDescription = "근처 Wi-Fi 네트워크를 사용할 수 있도록 NIIMBOT에서 위치 권한을 요청합니다.";
NSLocationAlwaysAndWhenInUseUsageDescription = "근처 Wi-Fi 네트워크를 사용할 수 있도록 NIIMBOT에서 위치 권한을 요청합니다.";
NSSpeechRecognitionUsageDescription = "본 앱은 사용자의 동의를 받아야 음성 식별을 사용할 수 있습니다. 음성 식별을 켜시겠습니까?";
NSLocalNetworkUsageDescription = "이 앱은 LAN 장치 검색 및 네트워크 구성 서비스를 위해 ​로컬 에어리어 네트워크 (LAN)​에 액세스해야 합니다.";
"UILaunchStoryboardName" = "LaunchScreen";
