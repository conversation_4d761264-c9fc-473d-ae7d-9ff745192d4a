package com.nimmbot.business.livecode

import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.webkit.WebView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.airbnb.lottie.LottieAnimationView
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.SizeUtils
import com.getcapacitor.Bridge
import com.getcapacitor.BridgeActivity
import com.getcapacitor.CapConfig
import com.getcapacitor.ServerPath
import com.getcapacitor.WebViewListener
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.nimmbot.business.livecode.bean.CapAppBean
import com.nimmbot.business.livecode.client.NiimbotIonicSender
import com.southcity.watermelon.util.LogUtils
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * @ClassName: IonicHostActivity
 * @Author: Liuxiaowen
 * @Date: 2022/12/29 13:50
 * @Description: ionic宿主页面
 */
class CapAppHostActivity: BridgeActivity() {

    private var webView: WebView? = null
    var capAppBean: CapAppBean? = null
    private val handler = Handler(Looper.getMainLooper())
    private lateinit var dismissRunnable: Runnable
//    private lateinit var netChangeListener: NetworkUtils.OnNetChangeListener

    /**
     * 销毁WebView释放摄像头资源
     */
    private fun destroyWebView() {
        try {
            webView?.let { webView ->
                // 停止加载
                webView.stopLoading()
                // 清理缓存和历史
                webView.clearCache(true)
                webView.clearHistory()
                // 销毁WebView
                webView.destroy()
                // 设置为null
                this.webView = null
            }
        } catch (e: Exception) {
            LogUtils.e("Error destroying WebView: ${e.message}")
            e.printStackTrace()
        }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        android.util.Log.e("============relaunchApp=========", "savedInstanceState : $savedInstanceState, appInit: ${BaseApplication.appInit}")
        if (savedInstanceState != null && !BaseApplication.appInit) {
            AppUtils.relaunchApp()
        }
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
//            window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
//            window.statusBarColor = getColor(R.color.white)
//        }

        initPlugin()
        NiimbotIonicSender.connectSender(this)
        webView = findViewById(com.getcapacitor.android.R.id.webview)

        showLoading()
        dismissRunnable = Runnable { dismissLoading() }
        handler.postDelayed(dismissRunnable, 3000)
//        netChangeListener = object : NetworkUtils.OnNetChangeListener {
//            override fun onNetChange(isNet: Boolean, msg: String) {
//                if(isNet){
//                    webView?.reload()
//                }
//
//            }
//        }
//        NetworkUtils.registerNetWorkListener(netChangeListener,this)
    }

    private fun initPlugin() {
        CapAppHelper.getCapPlugin().forEach {
            registerPlugin(it)
        }
    }

    private fun extraIntent() {
        try{
            val capStr = intent.getStringExtra("capApp")
            capAppBean = GsonUtils.fromJson(capStr, CapAppBean::class.java)
            if (null == getBridge() || getBridge().config.startPath != capAppBean?.subPath) {
                val configBuilder = CapConfig.Builder(this)
                    .setAndroidScheme(Bridge.CAPACITOR_HTTPS_SCHEME)
                    .setHostname(CapConstants.capHost)
                    .setAllowMixedContent(true)
                    .setLoggingEnabled(true)
                    .setWebContentsDebuggingEnabled(true)

                hookConfigBuilder(configBuilder)

                if (!capAppBean?.subPath.isNullOrEmpty()) {
                    configBuilder.setStartPath(capAppBean?.subPath)
                }
                this.config = configBuilder.create()
                bridgeBuilder.setConfig(config)
                bridgeBuilder.setServerPath(ServerPath(ServerPath.PathType.BASE_PATH, capAppBean?.localPath))

                capAppBean?.appId?.let { CapEntryHelper.addEntry(it, this) }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        Log.e("==============", "onNewIntent---> ${intent?.getStringExtra("capApp").toString()}")
        Log.e("==============", "onNewIntent---> currentPage: ${bridge?.webView?.url}")
        try{
            val capStr = intent?.getStringExtra("capApp")
            val capAppBean = GsonUtils.fromJson(capStr, CapAppBean::class.java)
            Log.e("==============", " getBridge().serverBasePath-->${ getBridge().serverBasePath}, capAppBean.localPath-->${capAppBean.localPath}")
            if (null != getBridge() && (getBridge().config.startPath != capAppBean.subPath || getBridge().serverBasePath != capAppBean.localPath)) {
                // 销毁WebView释放摄像头资源
                destroyWebView()
                this.intent = intent
                this.recreate()
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    override fun onStart() {
        Log.e("==============", "onStart---> ${intent.getStringExtra("capApp").toString()}")
        extraIntent()
        super.onStart()
        loadAnimate()
        CapAppHelper.bridge = bridge
        CapAppHelper.currentCapView = this
        CapAppHelper.bridge?.addWebViewListener(object : WebViewListener(){
            override fun onPageLoaded(webView: WebView?) {
                super.onPageLoaded(webView)
                dismissLoading()
            }

        })

        if(capAppBean?.fullScreen == true){
            com.niimbot.appframework_library.utils.AppUtils.setStatusBarLightColor(this, ColorUtils.getColor(
                melon.south.com.baselibrary.R.color.transparent))
            val webViewParent = webView?.parent as? ViewGroup
            webViewParent?.let {
                it.fitsSystemWindows = false
            }
        }else{
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                window.statusBarColor = getColor(com.niimbot.appframework_library.R.color.white)
            }
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.VANILLA_ICE_CREAM) {
            applyInsets(window.decorView,capAppBean?.fullScreen ?: false)
            Log.e("wangxuhao","cap appid=${capAppBean?.appId}")
            //烟草-医药-危废-高级二维码兼容
            if(capAppBean?.appId == "__CAP__DE09E3D" ||capAppBean?.appId == "__CAP__7863BFB" || capAppBean?.appId == "__CAP__SPR666G" || capAppBean?.appId == "__CAP__9DCE028"){
                KeyboardUtils.fixAndroidBug5497(this)
                window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
            }
        }
    }

    /**
     * 餐饮效期小程序发送剪切板内容
     */
    fun sendClipBoardEvent(content: String) {
        if(capAppBean?.appId != "__CAP__SPR889G") {
            return
        }
        CapAppHelper.sendClipBoardEvent(content)
    }

    var isInit = false
    override fun onResume() {
        super.onResume()
        CapAppHelper.checkClipboard(!isInit)
        isInit = true
    }
    /**
     * 设置ionic本地调试环境
     * @param builder Builder
     */
    private fun hookConfigBuilder(builder: CapConfig.Builder) {
        val ionicWebUrl = PreferencesUtils.getString("custom_ionic_web_url", "")
        if (AppUtils.isAppDebug() && !ionicWebUrl.isNullOrBlank()) {
            builder.setServerUrl(ionicWebUrl)
        }
    }

    private var animationView: LottieAnimationView? = null
    private fun loadAnimate() {
        if (null == animationView) {
            animationView = LottieAnimationView(this)
            animationView?.setAnimation(R.raw.animate_cap_app_loading)
            animationView?.repeatCount = Int.MAX_VALUE
        }
    }
    private fun showLoading() {
        try{
            if (null == animationView) {
                loadAnimate()
            }
            val layoutParams = CoordinatorLayout.LayoutParams(SizeUtils.dp2px(154f), ViewGroup.LayoutParams.MATCH_PARENT)
            layoutParams.gravity = Gravity.CENTER
            (webView?.parent as? ViewGroup)?.addView(
                animationView, layoutParams)
            animationView?.playAnimation()
        } catch(e: Exception) {e.printStackTrace()}
    }

    fun dismissLoading() {
        try{
            runOnUiThread {
                animationView?.let {
                    it.cancelLongPress()
                    (webView?.parent as? ViewGroup)?.removeView(it)
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    override fun onBackPressed() {
        CapAppHelper.sendSystemBackEvent("system")
    }

    override fun moveTaskToBack(nonRoot: Boolean): Boolean {
        val result = super.moveTaskToBack(nonRoot)
        overridePendingTransition(0, com.niimbot.appframework_library.R.anim.slide_out_down)
        return result
    }
    override fun finishAndRemoveTask() {
        super.finishAndRemoveTask()
        overridePendingTransition(0, com.niimbot.appframework_library.R.anim.slide_out_down)
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(0, com.niimbot.appframework_library.R.anim.slide_out_down)
    }

    override fun onDestroy() {
        super.onDestroy()

        // 销毁WebView释放摄像头资源
        try {
            destroyWebView()
        } catch (e: Exception) {
            LogUtils.e("Error destroying WebView in onDestroy: ${e.message}")
            e.printStackTrace()
        }

//        NetworkUtils.unRegisterNetWorkListener(netChangeListener)
        handler.removeCallbacks(dismissRunnable)
        NiimbotIonicSender.disConnectSender(this)
        overridePendingTransition(0, com.niimbot.appframework_library.R.anim.slide_out_down)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        XPermissionUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

}
