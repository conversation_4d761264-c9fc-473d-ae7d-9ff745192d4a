package melon.south.com.templatelibrary.mvp.presenter

import WifiConnectionProxyManager
import android.app.Activity
import android.content.Intent
import android.util.Log
import com.andsync.xpermission.XPermissionUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.connect.JCConnectionManager
import com.gengcon.print.draw.DrawIntentParams
import com.gengcon.print.draw.api.NiimbotDrawConfig
import com.gengcon.print.draw.module.event.LargeTemplateSendEvent
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.external.ExternalData
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.expand.toJson
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.protocol.template.NewScanActivityConfig
import com.niimbot.appframework_library.utils.LoginUtils
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.templateVersion.TemplateVersionConst
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSONObject
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import melon.south.com.templatelibrary.utils.VersionCompareUtils
import org.greenrobot.eventbus.EventBus


/**
 * <AUTHOR>
 * @date 2019/11/26.
 * email：<EMAIL>
 * description：
 */
object SyncTemplatePresenter {

    fun setListener() {
        RFIDConnectionProxyManager.printSettingApplyRFIDTemplate = { activity, template ->
            val niimbotDrawData = NiimbotDrawData()
            niimbotDrawData.niimbotTemplate = template
            startNiimbotDrawActivity(
                activity = activity,
                niimbotDrawData = niimbotDrawData,
                needRfidTemplate = false,
                needDownloadFonts = false
            )
        }
    }

    /**
     * 画板页
     */
    fun startNiimbotDrawActivity(
        activity: Activity,
        niimbotDrawData: NiimbotDrawData,
        isFromScanPrint: Boolean = false,
        isRetainActivity: Boolean = false,
        isShare: Boolean = false,
        needRfidTemplate: Boolean = true,
        needDownloadFonts: Boolean = true,
        fromIndustry: Boolean = false,
        fromGoodsIndustry: Boolean = false,
        isCable: Boolean = false,
        isFromDangerAPPJump: Boolean = false,//危废小程序直接跳入通用画板
        source: String? = "",
        defaultSelect: String? = "",
        printChannelCode: String? = "",
        fileInfo: HashMap<String, Any>? = null
    ) {
        var templateVersion = niimbotDrawData.niimbotTemplate.templateVersion
        var isOldTemplate = false
        var isOpen = false
        if (templateVersion.isNullOrEmpty()) {
            isOldTemplate = true
        } else {
            var compareResult = VersionCompareUtils.compareVersion(
                templateVersion,
                TemplateVersionConst.getAppMaxSupportTemplateVersion()
            )
            if (compareResult <= 0) {
                isOpen = true
            }
        }

        if (!isOldTemplate && !isOpen) {
//            showToast("当前版本不支持此模板，请升级版本")
            showToast(TemplateVersionConst.getUnsupportedTips(templateVersion))
            return
        }

        LogUtils.eTag("toDraw", "start in startNiimbotDrawActivity")
        niimbotDrawData.niimbotTemplate.apply {
            paccuracyName = getCloudTmpInitAccuracy(niimbotDrawData)
            completionUsedFonts()
        }
        NiimbotDrawConfig.isFromScanPrint = isFromScanPrint
        NiimbotDrawConfig.isFinishActivity = !isRetainActivity


        if (fromIndustry
            && niimbotDrawData.niimbotTemplate.cloudTemplateId.isNullOrEmpty()
            && !niimbotDrawData.niimbotTemplate.isLabel()
            && niimbotDrawData.niimbotTemplate.isFromIndustry()
        ) {
            niimbotDrawData.niimbotTemplate.cloudTemplateId = niimbotDrawData.niimbotTemplate.id
            niimbotDrawData.niimbotTemplate.id = System.currentTimeMillis().toString()
        }
        //如果是标签，或者模板名称为空则 置为默认
        if (niimbotDrawData.niimbotTemplate.name.isNullOrEmpty()) {
            niimbotDrawData.niimbotTemplate.name = LanguageUtil.findLanguageString("app100000728")
        }
        val niimbotDrawDataJson = any2Json(niimbotDrawData)
        LogUtils.e("niimbotDrawDataJson.length: ${niimbotDrawDataJson.length / 1024}KB")
        //数据超过一定大小(某些手机250K，某些500K..)时序列化后，超过了binder的限制
        //if (niimbotDrawDataJson.length > 100 * 1024) { //数据超过一定大小(某些手机250K，某些500K..)时序列化后，超过了binder的限制
        val enterNewDrawboard = {
            //组装flutter画板数据
            val paramsMap = HashMap<String, Any>()
            if(fileInfo != null) {
                paramsMap["fileInfo"] = fileInfo
            }
            paramsMap[DrawIntentParams.NIIMBOT_IS_SHARE] = isShare
            paramsMap["needRfidTemplate"] = if (needRfidTemplate) 1 else 0
            paramsMap[DrawIntentParams.NIIMBOT_NEED_DOWNLOAD_FONTS] = needDownloadFonts
            paramsMap[DrawIntentParams.NIIMBOT_FROM_INDUSTRY] = fromIndustry
            paramsMap[DrawIntentParams.NIIMBOT_FROM_GOODS_INDUSTRY] = fromGoodsIndustry
            //存在dataSource的情况下进入新画板 不再需要externalData数据
            if (niimbotDrawData.niimbotTemplate.dataSource != null && niimbotDrawData.niimbotTemplate.dataSource!!.isNotEmpty()) {
                niimbotDrawData.niimbotTemplate.externalData = ExternalData()
            }
            paramsMap["fontPath"] = FontUtils.customFontFile.absolutePath
            paramsMap["fontDefaultFile"] = FontUtils.ZT001_FILE
            if (fromIndustry) {
                paramsMap["source"] = "industryTemplate"
            }
            if (source?.isNotEmpty() == true) {
                paramsMap["source"] = source
            }
            if (LoginUtils.isLogin()) {
                paramsMap["token"] = HttpTokenUtils.getToken()
            }
            paramsMap["defaultSelect"] = defaultSelect ?: ""
            paramsMap["printChannelCode"] = printChannelCode ?: ""
            logMsg("wang", "canvas param json===${paramsMap.toJson()}")


            var router=""
            var barCode = niimbotDrawData.niimbotTemplate.profile.barcode
            if (barCode.isEmpty()) {
                barCode = niimbotDrawData.niimbotTemplate.profile.extrain.sparedCode
            } else
            if (barCode.isEmpty()) {
                barCode = niimbotDrawData.niimbotTemplate.profile.extrain.virtualBarCode
            } else
            if (barCode.isEmpty()) {
                barCode = niimbotDrawData.niimbotTemplate.profile.extrain.amazonCodeBeijing
            } else
            if (barCode.isEmpty()) {
                barCode = niimbotDrawData.niimbotTemplate.profile.extrain.amazonCodeWuhan
            } else
            if (barCode.isEmpty()) {
                niimbotDrawData.niimbotTemplate.profile.extrain.barcodeCategoryMap.forEach { key, value ->
                    if(!value.isNullOrEmpty()){
                        barCode=value
                        return@forEach
                    }
                }
            }
            val data = any2Json(niimbotDrawData.niimbotTemplate)
            paramsMap["jsonData"] = data
            paramsMap["fontPath"] =
                FontUtils.customFontFile.absolutePath
            logI("SyncTemplatePresenter","getCableTemplateByLabelId supportEditor=${niimbotDrawData.niimbotTemplate?.supportedEditors} layoutschema=${niimbotDrawData.niimbotTemplate?.layoutSchema} ")
            var isJumpTpDangerCap = (niimbotDrawData.niimbotTemplate?.layoutSchema?.isNotEmpty() == true&&niimbotDrawData.niimbotTemplate.supportedEditors.firstOrNull()=="hazardous" && TextHookUtil.getInstance().isSimpleChinese())
            if(isFromDangerAPPJump){
                NiimbotGlobal.gotoFlutterPage("canvas", paramsMap)
                if (!isRetainActivity) {
                    activity.finish()
                }
            }else if(isJumpTpDangerCap){
                LoginDataEnum.loginCheck(activity) {
                    NiimbotGlobal.gotoDangerCapApp()
                    if (!isRetainActivity) {
                        activity.finish()
                    }
                }
//                if(!isCable){
//                    NiimbotGlobal.gotoFlutterPage("canvas", paramsMap)
//                    if (!isRetainActivity) {
//                        activity.finish()
//                    }
//                }else{
//                    LoginDataEnum.loginCheck(activity) {
//                        NiimbotGlobal.gotoDangerCapApp()
//                        if (!isRetainActivity) {
//                            activity.finish()
//                        }
//                    }
//                }


            }else{
                val fromLocal = niimbotDrawData.niimbotTemplate?.layoutSchema?.isNotEmpty() == true
                TemplateSyncLocalUtils.getCableTemplateByLabelId(oneCode = barCode,niimbotDrawData.niimbotTemplate.profile.extrain.labelId, getDetail = true, fromLocal = fromLocal) { success, templateModule, errorMsg ->
                    niimbotDrawData.niimbotTemplate.layoutSchema= templateModule?.layoutSchema?:""
                    logI("SyncTemplatePresenter","getCableTemplateByLabelId supportEditor=${templateModule?.supportedEditors} layoutschema=${templateModule?.layoutSchema} ")
                    niimbotDrawData.niimbotTemplate.supportedEditors.clear()
                    templateModule?.supportedEditors?.forEach {
                        niimbotDrawData.niimbotTemplate.supportedEditors.add(it)
                    }
                    val data = any2Json(niimbotDrawData.niimbotTemplate)
                    paramsMap["jsonData"] = data
                    isJumpTpDangerCap = (niimbotDrawData.niimbotTemplate?.layoutSchema?.isNotEmpty() == true&&niimbotDrawData.niimbotTemplate.supportedEditors.firstOrNull()=="hazardous" && TextHookUtil.getInstance().isSimpleChinese())
                    if(isJumpTpDangerCap){
                        LoginDataEnum.loginCheck(activity) {
                            NiimbotGlobal.gotoDangerCapApp()
                            if (!isRetainActivity) {
                                activity.finish()
                            }
                        }
//                        if(!isCable){
//                            NiimbotGlobal.gotoFlutterPage("canvas", paramsMap)
//                            if (!isRetainActivity) {
//                                activity.finish()
//                            }
//                        }else{
//                            LoginDataEnum.loginCheck(activity) {
//                                NiimbotGlobal.gotoDangerCapApp()
//                                if (!isRetainActivity) {
//                                    activity.finish()
//                                }
//                            }
//                        }

                    } else if (isCable){
                        if(templateModule?.layoutSchema?.isNotEmpty() == true&&templateModule.supportedEditors.firstOrNull()=="cable"){
                            var entry=  PreferencesUtils.getString("cableEntry", default = "cableCanvas")
                            if(entry.equals("cableCanvas")&& TextHookUtil.getInstance().isSimpleChinese()){
                                router = "cableCanvas"
                            }else{
                                router = "canvas"
                            }
                        }else{
                            router = "canvas"
                        }
                        NiimbotGlobal.gotoFlutterPage(router, paramsMap)
                        if (!isRetainActivity) {
                            activity.finish()
                        }
                    }else{
                        NiimbotGlobal.gotoFlutterPage("canvas", paramsMap)
                        if (!isRetainActivity) {
                            activity.finish()
                        }
                    }


                }
            }



        }
        enterNewDrawboard.invoke()
    }

    fun logMsg(tag: String, msg: String) {  //信息太长,分段打印
        //因为String的length是字符数量不是字节数量所以为了防止中文字符过多，
        //  把4*1024的MAX字节打印长度改为2001字符数
        var msg = msg
        val max_str_length = 2001 - tag.length
        //大于4000时
        while (msg.length > max_str_length) {
            Log.i(tag, msg.substring(0, max_str_length))
            msg = msg.substring(max_str_length)
        }
        //剩余部分
        Log.i(tag, msg)
    }


    /**
     * 相机扫码页
     */
    fun startScanActivity(activity: Activity, requestCode: Int, justCallbackScanCode: Boolean) {
        PermissionDialogUtils.showCameraPermissionDialog(
            activity,
            RequestCode.MORE,
            object : XPermissionUtils.OnPermissionListener,
                com.niimbot.appframework_library.common.util.permission.XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    val intent =
                        Intent(
                            activity,
                            BaseApplication.getInstance().activityMap.get(NewScanActivityConfig::class.java)
                        )
//                    intent.putExtra("needTemplateIdCallback", true)
                    intent.putExtra("flutterCallback", true)
                    intent.putExtra("justCallbackScanCode",  justCallbackScanCode)
                    intent.putExtra("trackSource", 3)
                    activity
                        .startActivityForResult(intent, requestCode)
                }

                override fun onPermissionDenied(
                    deniedPermissions: Array<String>,
                    alwaysDenied: Boolean
                ) {
                    com.niimbot.appframework_library.utils.showToast("app01309")
                }
            })

    }


    /**
     * 获取打印机列表页
     */
    fun startDeviceConnectActivity(activity: Activity, fromCanvasSelectLabel: Boolean) {
        if (JCConnectionManager.getInstance().isConnected()) {
            BuriedHelper.trackEvent("click", "003_145_149")
        }
        LeMessageManager.getInstance().dispatchMessage(
            LeMessage(
                LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                DeviceConnectActivityConfig(activity, true).apply {
                    this.intent.putExtra("fromCanvasSelectLabel", fromCanvasSelectLabel)
                }
            )
        )
    }


    fun startPrintActivity(
        activity: Activity,
        niimbotDrawData: NiimbotDrawData,
        isShare: Boolean = false,
        needRfidTemplate: Boolean = true,
        fromFlutter: Boolean = false,
        showRfid: Boolean = false,
        rfidInfo: String = "",
        fromBatchPrint: Boolean = false,
        batchtIds: List<Map<String, Int>> = mutableListOf(),
        activeCount: Boolean = true,
        fromSelectGoodsPrint: Boolean = false,
        printScene: String = "",
        copies: Int = 1,
        uniqueId: String? = "",
        isFolderShare: Boolean = false,
        isFromCanvas: Boolean = false,
        pdfBindInfo: List<Map<String, Any>> = mutableListOf(),
    ) {
        if (fromFlutter) {
            gotoPrintSettingActivity(
                activity,
                niimbotDrawData,
                isShare,
                needRfidTemplate,
                fromFlutter,
                showRfid,
                rfidInfo,
                fromSelectGoodsPrint,
                fromBatchPrint,
                batchtIds,
                activeCount, printScene, copies, uniqueId,isFolderShare=isFolderShare,
                isFromCanvas = isFromCanvas,
                pdfBindInfo = pdfBindInfo
            )
        } else {
            FlutterMethodInvokeManager.isShowRfid {
                gotoPrintSettingActivity(
                    activity,
                    niimbotDrawData,
                    isShare,
                    needRfidTemplate,
                    fromFlutter,
                    it,
                    rfidInfo,
                    fromSelectGoodsPrint,
                    fromBatchPrint,
                    batchtIds,
                    activeCount, printScene, copies, uniqueId, isFolderShare = isFolderShare
                )
            }
        }
    }

    private fun gotoPrintSettingActivity(
        activity: Activity,
        niimbotDrawData: NiimbotDrawData,
        isShare: Boolean = false,
        needRfidTemplate: Boolean = true,
        fromFlutter: Boolean = false,
        showRfid: Boolean = false,
        rfidInfo: String = "",
        fromSelectGoodsPrint: Boolean = false,
        fromBatchPrint: Boolean = false,
        batchtIds: List<Map<String, Int>> = mutableListOf(),
        activeCount: Boolean = true,
        printScene: String = "",
        copies: Int,
        uniqueId: String?,
        isFolderShare: Boolean = false,
        isFromCanvas: Boolean = false,
        pdfBindInfo: List<Map<String, Any>> = mutableListOf(),

    ) {
        niimbotDrawData.niimbotTemplate.apply {
            paccuracyName = getCloudTmpInitAccuracy(niimbotDrawData)
//            hasVIPRes = hasVipElement() || vip || hasVIPRes
        }
        var templateVersion = niimbotDrawData.niimbotTemplate.templateVersion
        var isOldTemplate = false
        var isOpen = false
        if (templateVersion.isNullOrEmpty()) {
            isOldTemplate = true
        } else {
            var compareResult = VersionCompareUtils.compareVersion(
                templateVersion,
                TemplateVersionConst.getAppMaxSupportTemplateVersion()
            )
            if (compareResult <= 0) {
                isOpen = true
            }
        }

        if (!isOldTemplate && !isOpen) {
            showToast(TemplateVersionConst.getUnsupportedTips(templateVersion))
            return
        }
        //存在excel dataSource的情况下进入打印设置 不再需要externalData数据
        if (niimbotDrawData.niimbotTemplate.dataSource != null && niimbotDrawData.niimbotTemplate.dataSource!!.isNotEmpty() && niimbotDrawData.niimbotTemplate.dataSource!![0].type == "excel") {
//            niimbotDrawData.niimbotTemplate.externalData = ExternalData()
            niimbotDrawData.niimbotTemplate.externalData.list = arrayListOf()
        }
        var niimbotDrawDataJson = any2Json(niimbotDrawData)
       val historyId = uniqueId?:""
        var scene=printScene
        if(fromBatchPrint){
            scene= DrawIntentParams.PrintScene.PRINT_BATCH
        }
        if(isFolderShare){
            scene= DrawIntentParams.PrintScene.FOLDER_SHARE
        }
        NiimbotGlobal.gotoFlutterPage(
            "printSetting",
            hashMapOf(
                "resetDisplayMultiple" to true,
                "printScene" to scene,
                "printCount" to copies,
                "printHistoryId" to historyId,
                "showRfid" to showRfid,
                "rfidInfo" to rfidInfo,
                "niimbotTemplate" to niimbotDrawDataJson,
                "batchtIds" to  batchtIds.toJson(),
                "pdfBindInfo" to pdfBindInfo
            ), isTransParent = false
        )

        LogUtils.e("niimbotDrawDataJson.length: ${niimbotDrawDataJson.length / 1024}KB")
        //数据超过一定大小(某些手机250K，某些500K..)时序列化后，超过了binder的限制
        //if (niimbotDrawDataJson.length > 100 * 1024) { //数据超过一定大小(某些手机250K，某些500K..)时序列化后，超过了binder的限制
    /*    EventBus.getDefault().postSticky(
            LargeTemplateSendEvent(
                DrawIntentParams.NIIMBOT_DRAW_DATA,
                niimbotDrawDataJson
            )
        )
        LeMessageManager.getInstance().dispatchMessage(
            LeMessage(
                LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                JCPrintSettingActivityConfig(activity, niimbotDrawDataJson).apply {
                    this.intent.putExtra(DrawIntentParams.NIIMBOT_RESET_DISPLAYMULTIPLE, true)
                    this.intent.putExtra(DrawIntentParams.NIIMBOT_IS_SHARE, isShare)
                    this.intent.putExtra(
                        DrawIntentParams.NIIMBOT_NEED_RFID_TEMPLATE,
                        needRfidTemplate
                    )
                    this.intent.putExtra(
                        DrawIntentParams.NIIMBOT_FROM_SELECT_GOODS_PRINT,
                        fromSelectGoodsPrint
                    )
                    this.intent.putExtra(
                        DrawIntentParams.NIIMBOT_PRINT_SCENE,
                        printScene
                    )
                    this.intent.putExtra(
                        DrawIntentParams.NIIMBOT_PRINT_COPIES,
                        copies
                    )
                    this.intent.putExtra(
                        DrawIntentParams.NIIMBOT_PRINT_UNIQUEID,
                        uniqueId
                    )
                    this.intent.putExtra("fromFlutter", fromFlutter)
                    this.intent.putExtra("showRfid", showRfid)
                    this.intent.putExtra("rfidInfo", rfidInfo)
                    this.intent.putExtra(
                        "fromBatchPrint",
                        fromBatchPrint
                    )
                    this.intent.putExtra("activeCount", activeCount)
                    this.intent.putExtra("batchtIds", batchtIds.toJson())
                    this.intent.putExtra("isFolderShare", isFolderShare)
                    this.intent.putExtra("isFromCanvas", isFromCanvas)
                    this.intent.putExtra("pdfBindInfo", pdfBindInfo.toJson())
                })
        )*/
    }


    /**
     * 初始化云模板精度
     */
    private fun getCloudTmpInitAccuracy(niimbotDrawData: NiimbotDrawData): Int {
        return niimbotDrawData.niimbotTemplate.run {
            if (isCloudTemplate()) {
                this.paccuracyName =
                    try {
                        if (null != RFIDConnectionProxyManager.connectedDevice) {
                            RFIDConnectionProxyManager.connectedDevice?.paccuracyName?.toInt()
                                ?: 203
                        } else {
                            //根据当前选中类型确定精度
                            WifiConnectionProxyManager.getSelectDevice().paccuracyName.toInt()
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        203
                    }
                LogUtils.e("设置云模板初始精度: ${this.paccuracyName}")
                paccuracyName
            } else {
                this.paccuracyName
            }
        }
    }

    /**
     * 事件传递后，从event事件中获取模板
     * @return TemplateModuleLocal
     */
    fun getTemplateFromEvent(): TemplateModuleLocal {
        var drawTemplate = try {
            var data =
                EventBus.getDefault().removeStickyEvent(LargeTemplateSendEvent::class.java)?.run {
                    if (target == DrawIntentParams.NIIMBOT_DRAW_DATA) {
                        templateJson
                    } else {
                        null
                    }
                }
            val niimbotDrawDataJsonObject = JSONObject.parseObject(data)
            val niimbotTemplateJson =
                niimbotDrawDataJsonObject?.getString(DrawIntentParams.NIIMBOT_TEMPLATE) ?: ""
            TemplateModuleLocal.fromJson(niimbotTemplateJson) ?: TemplateModuleLocal().apply {
                this.width = 50.0f
                this.height = 30.0f
            }
        } catch (e: Exception) {
            e.printStackTrace()
            TemplateModuleLocal().apply {
                this.width = 50.0f
                this.height = 30.0f
            }
        }
        return drawTemplate
    }
}
