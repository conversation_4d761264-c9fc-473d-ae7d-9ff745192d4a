import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/models/netal_element_base.dart';
import 'package:netal_plugin/models/netal_enum.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/date_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/graph_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/line_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/serial_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';
import 'package:uuid/uuid.dart';

import '../excel/excel_transform_manager.dart';
import 'constraint_element.dart';

/// 转义绑定列(excel/商品库)的元素 value 值
typedef EscapeBindingMethod = String? Function(JsonElement element);

/// 组件基类
/// 基础属性实现
/// 元素定义 https://whjc.yuque.com/fq7i4s/wdsc38/xqol530iwtbruayu#R84tV
class JsonElement extends ConstraintElement {
  String id;
  String itemId;
  String itemType;
  num x;
  num y;
  num width;
  num height;
  int zIndex;
  String? value;
  String type;
  int rotate;
  int isLock;

  ///设置元素反白: 0 不反白  1 反白
  List<int>? elementColor;

  ///设置元素颜色索引
  int paperColorIndex;

  ///设置元素反白: 0 不反白  1 反白
  int colorReverse;

  /// 设置元素颜色打印档位
  int colorChannel;

  /// 商品标签模板绑定字段
  String fieldName;

  /// 商品标签模板绑定字段

  /// 镜像属性
  int isOpenMirror = 0;
  String mirrorId = '';

  /// 0: 画板中心点镜像, 1: 画板中心 y 轴镜像, 2: 画板中心 x 轴镜像
  int mirrorType = 0;

  /// excel 列绑定信息
  int isBinding;
  int bindingColumn;

  /// 第一个参数为excel唯一表示hash值，第二个参数为excel中第一个sheet的名字 eg:"dataBind": ["d455be4adf7c208fccd5a3c17016d036d4821e20", "sheet1"] // 数据源标识,sheet名
  /// dataBind有值的话代表绑定过excel
  List<String>? dataBind;

  /// 是否包含vip元素
  bool hasVipRes;

  /// 日期、流水号、excel/商品库 绑定场景下
  /// toJson 时是否转义 value 字段
  /// 默认不转义，用于图像库生成时需转义
  bool escapeValue = false;
  EscapeBindingMethod? escapeBindingMethod;
  bool compactOldTemplate = false;
  int checkResult = 0;
  String group = '';

  get contentTitle => contentTitle;

  /// 生成 id
  static String generateId() {
    return Uuid().v4().replaceAll("-", "");
  }

  /// 固定的镜像 id 值
  /// 保持固定便于撤销恢复时数据刷新
  String generateFixedMirrorId() {
    return (id.isEmpty) ? "" : id.split('').reversed.join();
  }

  static String elementIdFromJson(Map json) {
    String elementId = "";
    if (json['id'] == null) {
      if (json['elementId'] != null) {
        elementId = json['elementId'];
      } else {
        elementId = generateId();
      }
    } else if (json['id'] is int) {
      elementId = json['id'].toString();
    } else {
      elementId = json['id'];
      if (elementId.isEmpty) elementId = generateId();
    }
    return elementId;
  }

  int getBindingColumn() {
    if (isBindingElement()) {
      if (value == null || (value?.isEmpty ?? true) || (value?.contains("⊙") ?? true)) {
        return 0;
      }
      RegExp exp = RegExp(r'^[A-Z0-9]+$');
      if (!exp.hasMatch(value!)) {
        return 0;
      }
      CellAddress cellAddress = NiimbotExcelUtils.decodeCellIndex(value!);
      return cellAddress.c;
    }
    return -1;
  }

  /// 构建实体类
  static JsonElement build(Map<String, dynamic> json) {
    /// 字段兼容处理
    /// 元素是否锁定
    if (json['isLock'] is bool) {
      json['isLock'] = json['isLock'] == true ? 1 : 0;
    }

    /// 是否开启镜像
    if (json['isOpenMirror'] != null && json['isOpenMirror'] is bool) {
      json['isOpenMirror'] = json['isOpenMirror'] == true ? 1 : 0;
    }

    /// excel 绑定方式修改
    // if (json['value'] is String) {
    //   String value = json['value'];
    //   if (value.startsWith('\${0⊙') && value.endsWith('}')) {
    //     String index = value.replaceAll('\${0⊙', '').replaceAll('}', '');
    //     json['bindingColumn'] = int.tryParse(index);
    //     json['isBinding'] = 1;
    //     json['value'] = '';
    //   }
    // }

    /// 日期是否刷新
    if (json['dateIsRefresh'] != null && json['dateIsRefresh'] is bool) {
      json['dateIsRefresh'] = json['dateIsRefresh'] == true ? 1 : 0;
    }

    if (json['hasVipRes'] != null && json['hasVipRes'] is int) {
      json['hasVipRes'] = json['hasVipRes'] == 1 ? true : false;
    }
    String type = json['type'] ?? "";
    if (json['itemType'] != null && json['itemType'] != "widget") {
      type = json['itemType'];
      json['type'] = json['itemType'] ?? "";
    }
    if (json['itemId'] != null) {
      json['id'] = json['itemId'] ?? "";
    }
    if (json['id'] != null) json['itemId'] = json['id'];
    if (type == ElementItemType.text) {
      TextElement element = TextElement.fromJson(json);
      return element;
    } else if (type == ElementItemType.qrcode) {
      return QrCodeElement.fromJson(json);
    } else if (type == ElementItemType.barcode) {
      return BarCodeElement.fromJson(json);
    } else if (type == ElementItemType.image) {
      return ImageElement.fromJson(json);
    } else if (type == ElementItemType.table) {
      return TableElement.fromJson(json);
    } else if (type == ElementItemType.line) {
      return LineElement.fromJson(json);
    } else if (type == ElementItemType.graph) {
      return GraphElement.fromJson(json);
    } else if (type == ElementItemType.serial) {
      return SerialElement.fromJson(json);
    } else if (type == ElementItemType.date) {
      return DateElement.fromJson(json);
    }
    throw TypeError();
  }

  /// 是否为文本类元素
  bool isTextElement() {
    return (type == ElementItemType.text || type == ElementItemType.date || type == ElementItemType.serial);
  }

  /// 文本元素的图片缩放模式
  BoxFit getTextElementBoxFit() {
    if (isTextElement() &&
        ((this as TextElement).getTextMode() == TextMode.Horizontal ||
            (this as TextElement).getTextMode() == TextMode.Arc)) {
      return BoxFit.contain;
    }
    return BoxFit.none;
  }

  /// 是否可以双击编辑文本
  bool canDoubleClickToEditValue() {
    /// 文本 一维码 二维码 可双击
    /// 镜像体不可以双击
    // return (type == ElementItemType.text || type == ElementItemType.barcode || type == ElementItemType.qrcode) &&
    //     !(isOpenMirror == 0 && (mirrorId ?? '').length > 0);
    bool isBindGoods = false;
    if (isBindingCommodity()) {
      isBindGoods = true;
    }
    return (type == ElementItemType.text || type == ElementItemType.barcode || type == ElementItemType.qrcode);
  }

  bool isBindingElement() {
    List<String>? dataBind = this.dataBind;
    return dataBind != null && dataBind.isNotEmpty;
  }

  bool isAssociateWith(JsonElement element) {
    if (this is! DateElement || element is! DateElement) return false;
    DateElement dateElement1 = this as DateElement;
    DateElement dateElement2 = element as DateElement;
    if (dateElement1.associateId.isNotEmpty && dateElement2.associateId == dateElement2.associateId) {
      return true;
    } else {
      return false;
    }
  }

  //是否是边框类型的元素
  bool isMaterialBoder() {
    bool isBoder = false;
    if (this.type == ElementItemType.image) {
      ImageElement imageElement = this as ImageElement;
      if (imageElement.materialType == "2") {
        isBoder = true;
      }
    }
    return isBoder;
  }

  //是否素材或边框
  bool isMaterial() {
    bool isMaterial = false;
    if (this.type == ElementItemType.image) {
      ImageElement imageElement = this as ImageElement;
      if (imageElement.materialId?.isNotEmpty == true) {
        isMaterial = true;
      }
    }
    return isMaterial;
  }

  //是否默认图
  bool isImageElementDefault() {
    bool isDefault = false;
    if (this.type == ElementItemType.image) {
      ImageElement imageElement = this as ImageElement;
      if (imageElement.localUrl?.endsWith('_defaultImage.png') == true) {
        isDefault = true;
      }
    }
    return isDefault;
  }

  bool isBindingCommodity() {
    bool isBindGoods = false;
    if ((dataBind?.isNotEmpty ?? false) && (dataBind?.firstOrNull?.isEmpty ?? true)) {
      isBindGoods = true;
    }
    return isBindGoods;
  }

  bool isAdvanceQRCode() {
    bool isAdvanceQRCode = false;
    if (this is QrCodeElement &&
        (((this as QrCodeElement).isForm ?? false) || ((this as QrCodeElement).isLive ?? false))) {
      isAdvanceQRCode = true;
    }
    return isAdvanceQRCode;
  }

  bool hasContentTitle() {
    return contentTitle != null && (contentTitle as String).isNotEmpty;
  }

  bool hasLock() {
    /// 当前元素被锁定 or 当前为镜像但主体被锁定
    return this.isLock == 1;
  }

  CanvasElement toCanvasElement() {
    return CanvasElement(this);
  }

  JsonElement(
      {required this.id,
      required this.x,
      required this.y,
      required this.width,
      required this.height,
      this.zIndex = 0,
      required this.value,
      required this.type,
      this.rotate = 0,
      this.isLock = 0,
      this.fieldName = '',
      this.mirrorId = '',
      this.isOpenMirror = 0,
      this.mirrorType = 0,
      this.isBinding = 0,
      this.bindingColumn = -1,
      this.paperColorIndex = 0,
      this.colorChannel = 0,
      this.colorReverse = 0,
      this.hasVipRes = false,
      this.elementColor = const [255, 0, 0, 0],
      this.dataBind,
      this.itemId = '',
      this.itemType = '',
      this.group = ''});

  JsonElement.fromJson(Map<String, dynamic> json)
      : id = elementIdFromJson(json),
        itemId = _parseIntToString(json['id']),
        x = json['x'] ?? 0,
        y = json['y'] ?? 0,
        width = json['width'] ?? 0,
        height = json['height'] ?? 0,
        zIndex = _parseZIndex(json['zIndex']),
        value = _parseIntToString(json['value']),
        type = json['type'] ?? "",
        itemType = json['type'] ?? "",
        rotate = json['rotate'] ?? 0,
        isLock = _parseBoolField(json['isLock']),
        fieldName = json['fieldName'] ?? '',
        mirrorId = json['mirrorId'] ?? '',
        isOpenMirror = _parseBoolField(json['isOpenMirror']),
        mirrorType = json['mirrorType'] ?? 0,
        isBinding = json['isBinding'] ?? 0,
        bindingColumn = json['bindingColumn'] ?? -1,
        paperColorIndex = json['paperColorIndex'] ?? 0,
        colorReverse = json['colorReverse'] == null
            ? 0
            : (json['colorReverse'] is int)
                ? json['colorReverse']
                : (json['colorReverse'])
                    ? 1
                    : 0,
        colorChannel = json['colorChannel'] ?? 0,
        elementColor = _parseElementColor(json['elementColor']),
        hasVipRes = json['hasVipRes'] ?? false,
        dataBind = json['dataBind']?.cast<String>(),
        group = json['group'] == null ? '' : json['group'];

  ///反转义优先级高于转义优先级
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['itemId'] = this.id;
    data['id'] = this.id;
    data['elementId'] = this.id;
    data['x'] = this.x.digits(6);
    data['y'] = this.y.digits(6);
    data['width'] = this.width.digits(6);
    data['height'] = this.height.digits(6);
    data['zIndex'] = this.zIndex ?? 0;
    data['type'] = this.type;
    data['itemType'] = this.type;
    data['rotate'] = this.rotate;
    data['isLock'] = this.isLock;
    data['fieldName'] = this.fieldName ?? '';
    data['mirrorId'] = this.mirrorId ?? '';
    data['isOpenMirror'] = this.isOpenMirror ?? 0;
    data['mirrorType'] = this.mirrorType ?? 0;
    data['isBinding'] = this.isBinding ?? 0;
    data['bindingColumn'] = this.bindingColumn ?? -1;
    data['paperColorIndex'] = this.paperColorIndex ?? 0;
    data['colorReverse'] = this.colorReverse ?? 0;
    data['colorChannel'] = this.colorChannel ?? 0;
    data['elementColor'] = this.elementColor ?? [255, 0, 0, 0];
    if (this.escapeValue != false && this.escapeBindingMethod != null && isElementNeedEscape()) {
      data['value'] = escapeBindingMethod!.call(this);
    } else {
      data['value'] = this.value;
    }
    data['hasVipRes'] = hasVipSource();
    data['dataBind'] = dataBind;
    data['group'] = group;
    return data;
  }

  Map<String, dynamic> antiEscapeValueToJson() {
    return toJson();
  }

  bool isElementNeedEscape() {
    /// 1.绑定 excel 列
    /// 2.绑定商品字段
    /// 3.流水号
    // return (this.isBinding == 1 && this.bindingColumn != null && this.bindingColumn >= 0) ||
    //     (this.fieldName ?? '').isNotEmpty ||
    //     type == ElementItemType.serial;
    return (this.dataBind != null && this.dataBind!.isNotEmpty == true) ||
        (this.fieldName ?? '').isNotEmpty ||
        type == ElementItemType.serial;
  }

  /// 复制一份，默认重新生成 id
  JsonElement clone({
    bool isTableCell = false,
    bool keepId = false,
    bool isMirror = false,
    TemplateData? templateData,
  }) {
    /// table cell 特殊处理
    if (isTableCell == true) {
      TableCellElement element = TableCellElement.fromJson(jsonDecode(jsonEncode(toJson())));
      element.tableElement = (this as TableCellElement).tableElement;
      return element;
    }

    JsonElement jsonElement = JsonElement.build(jsonDecode(jsonEncode(toJson())));
    if (keepId == false) {
      jsonElement.id = generateId();
      cloneTableElementCells(jsonElement, templateData);
    }
    return jsonElement;
  }

  ///表格复制特殊处理下，单元格id和合并单元格id也要生成新的,并对应
  cloneTableElementCells(JsonElement jsonElement, TemplateData? templateData) {
    if (jsonElement is TableElement) {
      TableElement tableElement = jsonElement;
      tableElement.cells.forEach((cell) {
        String newId = generateId();
        templateData?.cloneElementSyncExcelTask(cell.id, newId);
        cell.id = newId;
      });
      tableElement.combineCells.forEach((combineCell) {
        String id = combineCell.id;
        String newCombineId = generateId();
        tableElement.cells.forEach((cell) {
          if (cell.combineId == id) {
            cell.combineId = newCombineId;
          }
        });
        templateData?.cloneElementSyncExcelTask(id, newCombineId);
        combineCell.id = newCombineId;
      });
    }
  }

  bool isSameELement(JsonElement canvasElement) {
    bool isSameElementInfo = true;

    ///镜像体进入画板会位置调整矫正，此处不判断镜像体 只判断原始元素
    if (canvasElement.mirrorId.isNotEmpty && canvasElement.isOpenMirror == 0) return isSameElementInfo;
    List dataBindOld = dataBind ?? [];
    List dataBindNew = canvasElement.dataBind ?? [];
    if ((x - canvasElement.x).abs() > 0.2 ||
        (y - canvasElement.y).abs() > 0.2 ||
        (width - canvasElement.width).abs() > 0.5 ||
        (height - canvasElement.height).abs() > 0.5 ||
        rotate != canvasElement.rotate ||
        (value != canvasElement.value && canvasElement.type != "date" && canvasElement.type != "serial") ||
        (dataBindOld.join(",") != dataBindNew.join(",")) ||
        type != canvasElement.type ||
        isLock != (canvasElement.isLock ?? 0) ||
        fieldName != canvasElement.fieldName ||
        paperColorIndex != canvasElement.paperColorIndex ||
        colorReverse != canvasElement.colorReverse) {
      isSameElementInfo = false;
    }
    if (this is TextElement) {
      TextElement oldTextElement = this as TextElement;
      TextElement canvasTextElement = canvasElement as TextElement;
      final oldDataBindModify = ExcelTransformManager().getElementModify(oldTextElement);
      final newDataBindModify = ExcelTransformManager().getElementModify(canvasTextElement);

      if ((oldTextElement.fontSize - canvasTextElement.fontSize).abs() > 0.1 ||
          oldTextElement.fontCode != canvasTextElement.fontCode ||
          !isSameFontStyles(oldTextElement.fontStyle, canvasTextElement.fontStyle) ||
          // oldTextElement.contentTitle != canvasTextElement.contentTitle ||
          oldDataBindModify?.useTitle != newDataBindModify?.useTitle ||
          oldDataBindModify?.title != newDataBindModify?.title ||
          oldTextElement.lineBreakMode != canvasTextElement.lineBreakMode ||
          oldTextElement.textAlignHorizontal != canvasTextElement.textAlignHorizontal ||
          oldTextElement.textAlignVertical != canvasTextElement.textAlignVertical ||
          (oldTextElement.wordSpacing - canvasTextElement.wordSpacing).abs() > 0.05 ||
          (oldTextElement.letterSpacing - canvasTextElement.letterSpacing).abs() > 0.05 ||
          (oldTextElement.lineSpacing - canvasTextElement.lineSpacing).abs() > 0.05) {
        isSameElementInfo = false;
      }
      if (this is DateElement) {
        DateElement oldDateElement = this as DateElement;

        DateElement canvasDateElement = canvasElement as DateElement;
        var isTimeRefresh = false;
        if (canvasDateElement.dateIsRefresh != 1) {
          isTimeRefresh = oldDateElement.time != canvasDateElement.time;
        }
        if (oldDateElement.dateFormat != canvasDateElement.dateFormat ||
            oldDateElement.timeFormat != canvasDateElement.timeFormat ||
            oldDateElement.timeOffset != canvasDateElement.timeOffset ||
            oldDateElement.associated != canvasDateElement.associated ||
            isTimeRefresh ||
            oldDateElement.associateId != canvasDateElement.associateId ||
            oldDateElement.validityPeriod != canvasDateElement.validityPeriod ||
            oldDateElement.validityPeriodNew != canvasDateElement.validityPeriodNew ||
            oldDateElement.contentTitle != canvasDateElement.contentTitle ||
            oldDateElement.validityPeriodUnit != canvasDateElement.validityPeriodUnit ||
            oldDateElement.timeUnit != canvasDateElement.timeUnit ||
            oldDateElement.dateIsRefresh != canvasDateElement.dateIsRefresh) {
          isSameElementInfo = false;
        }
      }
      if (this is SerialElement) {
        SerialElement oldSerialElement = this as SerialElement;
        SerialElement canvasSerialElement = canvasElement as SerialElement;
        if (oldSerialElement.prefix != canvasSerialElement.prefix ||
            oldSerialElement.startNumber != canvasSerialElement.startNumber ||
            oldSerialElement.suffix != canvasSerialElement.suffix ||
            oldSerialElement.incrementValue != canvasSerialElement.incrementValue) {
          isSameElementInfo = false;
        }
      }
    }
    if (this is BarCodeElement) {
      BarCodeElement oldBarcodeElement = this as BarCodeElement;
      BarCodeElement canvasBarcodeElement = canvasElement as BarCodeElement;
      if (oldBarcodeElement.codeType != canvasBarcodeElement.codeType ||
          (oldBarcodeElement.textHeight - canvasBarcodeElement.textHeight).abs() > 0.1 ||
          oldBarcodeElement.textPosition != canvasBarcodeElement.textPosition ||
          (oldBarcodeElement.fontSize - canvasBarcodeElement.fontSize).abs() > 0.1) {
        isSameElementInfo = false;
      }
    }
    if (this is QrCodeElement) {
      QrCodeElement oldQrcodeElement = this as QrCodeElement;
      QrCodeElement canvasQrcodeElement = canvasElement as QrCodeElement;
      if (oldQrcodeElement.codeType != canvasQrcodeElement.codeType ||
          oldQrcodeElement.correctLevel != canvasQrcodeElement.correctLevel) {
        isSameElementInfo = false;
      }
    }
    if (this is LineElement) {
      LineElement oldLineElement = this as LineElement;
      LineElement canvasLineElement = canvasElement as LineElement;
      if (oldLineElement.lineType != canvasLineElement.lineType ||
          (oldLineElement.lineWidth - canvasLineElement.lineWidth).abs() >= 0.05) {
        isSameElementInfo = false;
      }
    }
    if (this is ImageElement) {
      ImageElement oldImageElement = this as ImageElement;
      ImageElement canvasImageElement = canvasElement as ImageElement;
      int imageProcessingTypeCanvas = canvasImageElement.imageProcessingType;
      if (canvasImageElement.imageProcessingType == 4) {
        imageProcessingTypeCanvas = 1;
      }
      int imageProcessingTypeOld = oldImageElement.imageProcessingType;
      if (oldImageElement.imageProcessingType == 4) {
        imageProcessingTypeOld = 1;
      }
      bool conditions1 = (imageProcessingTypeOld != imageProcessingTypeCanvas && oldImageElement.localUrl.isNotEmpty);
      bool conditions2 = oldImageElement.allowFreeZoom != canvasImageElement.allowFreeZoom;
      bool conditions3 = oldImageElement.materialId != canvasImageElement.materialId;
      bool conditions4 = (oldImageElement.imageProcessingValue.isNotEmpty &&
          canvasImageElement.imageProcessingValue.isNotEmpty &&
          oldImageElement.imageProcessingValue.first != canvasImageElement.imageProcessingValue.first &&
          oldImageElement.localUrl.isNotEmpty);
      bool newConditions = oldImageElement.localUrl != canvasImageElement.localUrl;
      if (PdfBindInfoManager.instance.elementIsBindPdf(oldImageElement.id)) {
        newConditions = false;
      }

      if (conditions1 || conditions2 || conditions3 || conditions4 || newConditions) {
        isSameElementInfo = false;
      }
      //原始判断逻辑，新增图片本地地址判断，为了替换之后，画板退出能够提示保存
      // if ((oldImageElement.imageProcessingType != canvasImageElement.imageProcessingType &&
      //         oldImageElement.localUrl.isNotEmpty) ||
      //     oldImageElement.allowFreeZoom != canvasImageElement.allowFreeZoom ||
      //     oldImageElement.materialId != canvasImageElement.materialId ||
      //     (oldImageElement.imageProcessingValue.isNotEmpty &&
      //         canvasImageElement.imageProcessingValue.isNotEmpty &&
      //         oldImageElement.imageProcessingValue.first != canvasImageElement.imageProcessingValue.first &&
      //         oldImageElement.localUrl.isNotEmpty)) {
      //   isSameElementInfo = false;
      // }
    }
    if (this is GraphElement) {
      GraphElement oldGraphElement = this as GraphElement;
      GraphElement canvasGraphElement = canvasElement as GraphElement;
      if (oldGraphElement.lineType != canvasGraphElement.lineType ||
          (oldGraphElement.lineWidth - canvasGraphElement.lineWidth).abs() >= 0.05 ||
          oldGraphElement.graphType != canvasGraphElement.graphType) {
        isSameElementInfo = false;
      }
    }
    /*
     int row;
  int column;
  List<double> rowHeight;
  List<double> columnWidth;
  List<TableCellElement> cells;
  List<TableCellElement> combineCells;
  bool allowFreeZoom;
  int lineColorChannel = 0;
  int contentColorChannel = 0;
  List<int> lineColor = [255, 0, 0, 0];
  List<int> contentColor = [255, 0, 0, 0];
  int lineMode;
    */
    if (this is TableElement) {
      TableElement oldTableElement = this as TableElement;
      TableElement canvasTableElement = canvasElement as TableElement;
      if (oldTableElement.column != canvasTableElement.column ||
          oldTableElement.row != canvasTableElement.row ||
          oldTableElement.lineType != canvasTableElement.lineType ||
          (oldTableElement.lineWidth - canvasTableElement.lineWidth).abs() >= 0.05 ||
          oldTableElement.lineColorChannel != canvasTableElement.lineColorChannel ||
          oldTableElement.contentColorChannel != canvasTableElement.contentColorChannel ||
          oldTableElement.lineMode != canvasTableElement.lineMode ||
          !isSameListDouble(oldTableElement.rowHeight, canvasTableElement.rowHeight) ||
          !isSameListDouble(oldTableElement.columnWidth, canvasTableElement.columnWidth) ||
          !isSameTableCells(oldTableElement.cells, canvasTableElement.cells) ||
          !isSameTableCells(oldTableElement.combineCells, canvasTableElement.combineCells)) {
        isSameElementInfo = false;
      }
    }
    // print("是否相同元素$isSameElementInfo + 当前元素:${this.toJson()} + 对比元素${canvasElement.toJson()}");
    return isSameElementInfo;
  }

  bool isSameFontStyles(List<String> oldStyles, List<String> styles) {
    bool isSameFontStyles = true;
    if (oldStyles.length != styles.length) {
      return false;
    }
    for (var element in oldStyles) {
      if (!styles.contains(element)) {
        return false;
      }
    }
    return isSameFontStyles;
  }

  bool isSameListDouble(List<double>? oldCell, List<double>? newCell, {double abs = 0.05}) {
    if (oldCell == null) {
      return newCell == null;
    }
    if (newCell == null || oldCell.length != newCell.length) {
      return false;
    }
    if (identical(oldCell, newCell)) {
      return true;
    }
    for (int index = 0; index < oldCell.length; index += 1) {
      if ((oldCell[index] - newCell[index]).abs() > abs) {
        return false;
      }
    }
    return true;
  }

  bool isSameTableCells(List<TableCellElement> oldCells, List<TableCellElement> canvasCells) {
    bool isSameElementInfo = true;
    if (oldCells.length != canvasCells.length) {
      return false;
    }
    for (var element in oldCells) {
      bool hasCell = false;
      for (var canvasElement in canvasCells) {
        if (element.id == canvasElement.id) {
          hasCell = true;
          isSameElementInfo = isSameTableCell(element, canvasElement);
          if (!isSameElementInfo) {
            return false;
          }
        }
      }
      if (!hasCell) {
        return false;
      }
    }
    return isSameElementInfo;
  }

  bool isSameTableCell(TableCellElement oldTextElement, TableCellElement canvasTextElement) {
    bool isSameElementInfo = true;
    if ((oldTextElement.fontSize - canvasTextElement.fontSize).abs() > 0.1 ||
        oldTextElement.fontCode != canvasTextElement.fontCode ||
        oldTextElement.value != canvasTextElement.value ||
        !isSameFontStyles(oldTextElement.fontStyle, canvasTextElement.fontStyle) ||
        // oldTextElement.contentTitle != canvasTextElement.contentTitle ||
        oldTextElement.lineBreakMode != canvasTextElement.lineBreakMode ||
        oldTextElement.textAlignHorizontal != canvasTextElement.textAlignHorizontal ||
        oldTextElement.textAlignVertical != canvasTextElement.textAlignVertical ||
        (oldTextElement.wordSpacing - canvasTextElement.wordSpacing).abs() > 0.05 ||
        (oldTextElement.letterSpacing - canvasTextElement.letterSpacing).abs() > 0.05 ||
        (oldTextElement.lineSpacing - canvasTextElement.lineSpacing).abs() > 0.05) {
      isSameElementInfo = false;
    }
    return isSameElementInfo;
  }

  bool hasVipSource() {
    return false;
  }

  bool isBindingExcel() {
    return isBindingElement();
    // return false;
  }

  bool isMirrorElement() {
    if (mirrorId.isEmpty) {
      return false;
    }
    return isOpenMirror != 1;
  }

  bool isExcelModifyIdMatch(String modifyId) {
    return id == modifyId;
  }

  completionFontDefault(Map<String, dynamic> data) {
    final fontCode = data['fontCode'] as String?;
    if (fontCode != null && fontCode.isEmpty) {
      final languageType = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
      if (languageType == "ar") {
        data['fontCode'] = FontManager.DEFAULT_FONT_CODE_ARIAL;
        data['fontFamily'] = FontManager.DEFAULT_FONT_CODE_ARIAL;
      } else if (languageType == "ja") {
        data['fontCode'] = FontManager.DEFAULT_FONT_CODE_JAPANESE;
        data['fontFamily'] = FontManager.DEFAULT_FONT_CODE_JAPANESE;
      } else {
        data['fontCode'] = FontManager.DEFAULT_FONT_CODE;
        data['fontFamily'] = FontManager.DEFAULT_FONT_CODE;
      }
    }
  }

  // 转换成图像库Element结构，主要在Niimbot_template中
  NetalElementBase toNetal() {
    return NetalElementBase(
      x: x,
      y: y,
      rotate: rotate,
      width: width,
      height: height,
      type: NetalElementType.values.byName(type),
    );
  }
}

List<int> _parseElementColor(dynamic field) {
  return field == null ? [255, 0, 0, 0] : List<int>.from(field);
}

String _parseIntToString(dynamic field) {
  return (field == null || field == '') ? '' : ((field is int) ? field.toString() : field);
}

int _parseZIndex(dynamic field) {
  return field == null ? 0 : ((field is String) ? int.parse(field) : field);
}

int _parseBoolField(dynamic field) {
  if (field is bool) {
    return field == true ? 1 : 0;
  }
  return field ?? 0;
}
