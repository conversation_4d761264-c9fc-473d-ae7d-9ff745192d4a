import 'dart:io';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:custom_pop_up_menu/custom_pop_up_menu.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:get/get.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/mix/touch_board_listener.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/graph_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/provider/smart_tips_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/input_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/printarea/print_area_builder.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/clickable_item.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_interactive_viewer.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/dotted_line.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/stack2.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/src/widgets/controller/canvas/canvas_controller.dart';
import 'package:niimbot_flutter_canvas/src/widgets/elements/interactive_element.dart';
import 'package:niimbot_flutter_canvas/src/widgets/rfid_bind/rfid_info_manager.dart';

import '/src/localization/localization_public.dart';
import '/src/provider/floating_bar_visible_notifier.dart';
import '/src/utils/canvas_image_util.dart';
import '/src/utils/theme_color.dart';
import '/src/widgets/box_frame/canvas_ruler.dart';
import 'auxiliary/auxiliary_lines_builder.dart';
import 'floatbar/floating_bar_action.dart';

final Logger _logger = Logger("NbCanvas", on: kDebugMode);

enum SmartTipsAction {
  /// 全选
  selectAll,

  /// 选择所有文本
  selectAllTextElements,

  /// 选择所有线条
  selectAllLineElements,

  /// 清空画板
  clearCanvas
}

/// 画板绘制层
class CanvasCore extends StatefulWidget {
  CanvasCore({
    super.key,
    required this.canvasElements,
    required this.focusedElements,
    required this.itemBuilder,
    // this.multiSelect = false,
    required this.isMultiSelect,
    this.onTranslated,
    this.onTranslateEnded,
    this.onScaleEndWithFontSize,
    this.onBoxEdit,
    this.onBoxReplace,
    this.onBoxDeleted,
    this.onBoxCopied,
    this.onBoxRotate,
    this.onBoxLock,
    this.onBoxAlign,
    this.onCellAction,
    this.onMergeCells,
    this.onSplitCell,
    required this.idBuilder,
    required this.isItemFocused,
    required this.isLock,
    required this.isMirror,
    required this.isResizeEnable,
    required this.isAssociateElement,
    required this.multiElementsRect,
    this.fetchBatchInfo,
    this.startOffset,
    this.defaultScale = 0,
    this.readOnly = false,
    this.onBoxTap,
    this.itemControlsBuilder,
    required this.templateWidth,
    required this.templateHeight,
    this.onInteractionUpdate,
    this.onItemDoubleTap,
    this.onMultiSelectChanged,
    this.onSelectAll,
    this.onSelectAllText,
    this.onSelectAllLine,
    this.onBatchPreviewTap,
    required this.backgroundImage,
    required this.multipleBackIndex,
    required this.localBackgrounds,
    this.onBackgroundImageChange,
    required this.canvasRotate,
    this.onSmartTipsTap,
    required this.labelInfo,
    this.onLabelChange,
    required this.isShowHandleBtn,
    this.onElementFocusState,
    this.rfidBind,
    this.checkSelectAll,
    this.checkSelectAllText,
    this.checkSelectAllLine,
    required this.materialModelSn,
    this.onClickLabelDetail,
  });

  // final bool multiSelect;
  final bool Function() isMultiSelect;
  final List<CanvasElement> canvasElements;
  final List<CanvasElement> focusedElements;

  final Widget Function(CanvasElement data) itemBuilder;
  final Widget Function(JsonElement data)? itemControlsBuilder;
  final String Function(CanvasElement) idBuilder;
  final bool Function(CanvasElement) isItemFocused;
  final bool Function(CanvasElement) isLock;
  final bool Function(CanvasElement) isMirror;
  final bool Function(CanvasElement) isAssociateElement;
  final bool Function(CanvasElement) isResizeEnable;
  final Rect? Function() multiElementsRect;
  final List<int>? Function()? fetchBatchInfo;
  final Offset? startOffset;
  double? defaultScale;

  final void Function(CanvasElement?, DragUpdate dragUpdate, bool ignoreElement)? onTranslated;
  final void Function(CanvasElement?, bool ignoreElement)? onTranslateEnded;

  /// 等比缩放结束时，设置字号
  final ValueSetter<(CanvasElement canvasElement, double newFontSize, ControlPointType controlPointType)>?
      onScaleEndWithFontSize;
  final void Function(CanvasElement?)? onBoxTap;
  final void Function(List<CanvasElement>)? onBoxEdit;
  final void Function(List<CanvasElement>)? onBoxReplace;
  final void Function(List<CanvasElement>)? onBoxDeleted;
  final void Function(List<CanvasElement>)? onBoxCopied;
  final void Function(List<CanvasElement>)? onBoxRotate;
  final void Function(List<CanvasElement>)? onBoxLock;
  final void Function(int action, List<CanvasElement>)? onBoxAlign;
  final void Function(int action, List<CanvasElement>)? onCellAction;
  final void Function(List<CanvasElement>)? onMergeCells;
  final void Function(List<CanvasElement>)? onSplitCell;
  final void Function(Offset offset, double scale)? onInteractionUpdate;
  final void Function(CanvasElement)? onItemDoubleTap;

  // 元素状态
  final ElementFocusState Function()? onElementFocusState;

  /// 多选
  final ValueChanged<bool>? onMultiSelectChanged;

  final void Function()? onSelectAll;

  final void Function()? onSelectAllText;

  final void Function()? onSelectAllLine;

  /// 批量预览
  final void Function()? onBatchPreviewTap;

  //便签纸点击
  final void Function()? onClickLabelDetail;

  /// 智能多选
  final void Function(SmartTipsAction type)? onSmartTipsTap;

  /// 切换背景
  final void Function(int index)? onBackgroundImageChange;

  /// 更改标签纸
  final void Function()? onLabelChange;

  /// rfid绑定
  final void Function()? rfidBind;

  final bool Function()? checkSelectAll;
  final bool Function()? checkSelectAllText;
  final bool Function()? checkSelectAllLine;

  final bool readOnly;
  final double templateHeight;
  final double templateWidth;
  final String backgroundImage;
  final int multipleBackIndex;
  final List<String> localBackgrounds;
  final double canvasRotate;
  final String labelInfo;
  String materialModelSn;

  //是否显示调节杆
  final bool isShowHandleBtn;

  @override
  CanvasCoreState createState() => CanvasCoreState();
}

class CanvasCoreState extends State<CanvasCore> with TouchBoardListenerMixin, TickerProviderStateMixin {
  final GlobalKey _canvasKey = GlobalKey();

  /// 画板状态控制器
  CanvasController? _controller;

  /// Internal
  ValueNotifier<bool> _isCardHovered = ValueNotifier(false);
  TransformationController2 _transformController = TransformationController2();

  /// 画布元素列表
  List<CanvasElement> get _canvasElements => widget.canvasElements;

  /// 多选
  bool get _multiSelect => widget.isMultiSelect.call() == true;

  @override
  bool get enableKeyListener => true;

  /// 画布缩放比例
  double get _canvasScale => _transformController.value.getMaxScaleOnAxis();

  /// 画布偏移量
  Offset get _canvasOffset =>
      Offset(_transformController.value.getTranslation().x, _transformController.value.getTranslation().y);

  /// 当前智能提醒模式
  get smartTipsMode => SmartTipsNotifier().smartTipsMode;

  /// 背景key
  final GlobalKey _backGroundKey = GlobalKey(debugLabel: 'backGround');
  //批量预览信息高度
  double batchInfoHeight = 30;
  //rfid绑定高度
  double rfidHeight = 22;
  //rfid碳带颜色高度
  double ribbonColorHeight = 17;

  //画板顶部显示字段默认垂直间隔
  double verticalSpace = 8;
  //画板最顶部间隔
  double firstLineVercalSpace = 16;
  double canvasAreaHeight = 0;
  bool firstBuild = true;

  @override
  void initState() {
    super.initState();

    _controllerReset = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1),
    );

    /// 智能提醒数据监听
    SmartTipsNotifier().addListener(_smartTipsNotifierHandler);
    FloatingBarVisibleNotifier().addListener(_floatingBarVisibleNotifierHandler);
    try {
      _controller = Get.find<CanvasController>();
    } catch (e, s) {
      debugPrint('异常信息:\n $e');
    }
    _setFirstLineSpace();
  }

  @override
  void dispose() {
    SmartTipsNotifier().removeListener(_smartTipsNotifierHandler);
    FloatingBarVisibleNotifier().removeListener(_floatingBarVisibleNotifierHandler);
    super.dispose();
  }

  /// 智能提示变动监听
  _smartTipsNotifierHandler() {
    if (smartTipsMode != SmartTipsMode.none || _canvasScale != 1) setState(() {});
    FloatingBarHelper().checkHasCovered();
  }

  _floatingBarVisibleNotifierHandler() {
    _logger.log("悬浮条状态变化： ${FloatingBarVisibleNotifier().floatingBarVisible}");
    if (!FloatingBarVisibleNotifier().floatingBarVisible) {
      FloatingBarHelper().dismissFloatingBar();
    } else {
      // FloatingBarHelper().dismissFloatingBar();
      _freshFloatingBar();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (firstBuild) {
      firstBuild = false;

      ///内容高度-顶部toolbar42-组件面板高度（滑块+组件tab+组件元素面板+底部bottombar48+底部边距15）-安全区域
      canvasAreaHeight = DisplayUtil.deviceSize.height -
          42 -
          (6 + 10 + 49 + 220 + 48 + 15) -
          DisplayUtil.padding.bottom -
          DisplayUtil.padding.top;
      double realCanvasHeiht = widget.templateHeight + _getVerticalOffset();
      if ((realCanvasHeiht > canvasAreaHeight)) {
        double canvasScale = (canvasAreaHeight - _getVerticalOffset()) / widget.templateHeight;
        if (canvasScale != 1) {
          _transformController.value = Matrix4.identity();
          _transformController.value.scale(canvasScale, canvasScale, canvasScale);
          //x轴采用中心点 Y轴采用顶点进行缩放
          _transformController.value.translate(
              (widget.templateWidth / 2 - widget.templateWidth * canvasScale / 2) * 1 / canvasScale,
              // (widget.templateHeight / 2 - widget.templateHeight * canvasScale / 2) * 1 / canvasScale,
              0,
              100.0);
        }
      }
    }
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    _logger.log("=============canvas_core组件的build--canvaScale: $_canvasScale");
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    int consumablesType = CanvasObjectSharedWidget.consumablesTypeOf(context);
    bool isDragging =
        _multiSelectDragging || _canvasElements.where((element) => element.isDragging).toList().length > 0;
    Container container = Container(
      // color: ThemeColor.COLOR_F5F5F5,
      child: Stack(
        children: [
          ValueListenableBuilder(
            valueListenable: _isCardHovered,
            builder: (BuildContext context, bool value, Widget? cachedChild) {
              return GestureDetector(
                  key: _canvasKey,
                  onTap: handleBgPressed,
                  child: InteractiveViewer2(
                      transformationController: _transformController,
                      boundaryMargin: EdgeInsets.all(double.infinity),
                      minScale: 0.3,
                      maxScale: 6,
                      constrained: false,
                      scaleEnabled: !value,
                      panEnabled: !value,
                      startOffset: Offset(
                          //etag模式下隐藏切换标签纸的条目
                          DisplayUtil.marginBounds,
                          // DisplayUtil.designMarginBounds  + (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
                          //     ? 0
                          //     : DisplayUtil.designLableInfoHeight)+
                          _getVerticalOffset()),
                      onInteractionStart: (ScaleStartDetails scaleStartDetails) {
                        // if (!_multiSelect) {
                        //   FloatingBarHelper().dismissFloatingBar();
                        // }
                      },
                      onInteractionEnd: (ScaleEndDetails scaleEndDetails) {
                        if (_canvasScale != 1) setState(() {});
                      },
                      onInteractionUpdate: (ScaleUpdateDetails scaleUpdateDetails) {
                        widget.onInteractionUpdate?.call(scaleUpdateDetails.focalPoint, _canvasScale);
                      },
                      isSupportRecognizerGesture:
                          (Offset focalPoint, GestureType? gestureType, int pointerCount, Matrix4 transform) {
                        // // 非None状态不处理手势
                        // if (widget.onElementFocusState() == ElementFocusState.None) return true;
                        // if ((gestureType == GestureType.pan || gestureType == null) && pointerCount == 1) {
                        //   RenderBox backGroundBox = _backGroundKey.currentContext.findRenderObject();
                        //   Offset position = backGroundBox.localToGlobal(Offset.zero);
                        //   double scale = transform.getMaxScaleOnAxis();
                        //   Rect rect = Rect.fromLTWH(
                        //       position.dx, position.dy, backGroundBox.size.width * scale, backGroundBox.size.height * scale);
                        //   _logger.log('rect-----$rect------point--------${focalPoint}');
                        //   if (rect.contains(focalPoint)) {
                        //     return false;
                        //   }
                        // }
                        return true;
                      },
                      child: cachedChild!));
            },
            child: Stack2(
              fit: StackFit.loose,
              clipBehavior: Clip.none,
              children: [
                /// 画板背景
                (widget.backgroundImage).isNotEmpty
                    ? _buildSelectedBackgroundWidget(context)
                    : Positioned(
                        key: _backGroundKey,
                        left: 0,
                        top: 0,
                        child: Container(
                          width: widget.templateWidth,
                          height: widget.templateHeight,
                          decoration:
                              BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(10))),
                        ),
                      ),
                // if (printColor != null && printColor.isNotEmpty && consumablesType == 2)
                //   Positioned(
                //     left: 0,
                //     top: widget.templateHeight + 15,
                //     child: _printColrTipWidget(printColor),
                //   ),

                /// 多选元素外框
                if (widget.multiElementsRect.call() != null) buildMultiElementsRect(),

                /// 形状 和边框 处于选中优先级最底层
                ..._canvasElements
                    .where(
                        (element) => (element.elementType == ElementItemType.graph) || element.data.isMaterialBoder())
                    .map((canvasElement) {
                  return createInteractiveElement(canvasElement, widget.isItemFocused(canvasElement),
                      isShowHandleBtn: widget.isShowHandleBtn);
                }).toList(),

                /// 图片/素材/表格 处于选中优先级第二级 (需要剔除边框素材)
                ..._canvasElements
                    .where((element) =>
                        element.elementType == ElementItemType.table ||
                        (element.elementType == ElementItemType.image && element.data.isMaterialBoder() == false))
                    .map((canvasElement) {
                  return createInteractiveElement(canvasElement, widget.isItemFocused(canvasElement),
                      isShowHandleBtn: widget.isShowHandleBtn);
                }).toList(),

                /// 其他元素 处于选中优先级第一级
                ///未选中
                ..._canvasElements
                    .where((element) =>
                        !widget.isItemFocused(element) &&
                        element.elementType != ElementItemType.table &&
                        element.elementType != ElementItemType.graph &&
                        element.elementType != ElementItemType.image)
                    .map((canvasElement) {
                  return createInteractiveElement(canvasElement, widget.isItemFocused(canvasElement),
                      isShowHandleBtn: widget.isShowHandleBtn);
                }).toList(),

                ///选中
                ..._canvasElements
                    .where((element) =>
                        widget.isItemFocused(element) &&
                        element.elementType != ElementItemType.table &&
                        element.elementType != ElementItemType.graph &&
                        element.elementType != ElementItemType.image)
                    .map((canvasElement) {
                  return createInteractiveElement(canvasElement, widget.isItemFocused(canvasElement),
                      isShowHandleBtn: widget.isShowHandleBtn);
                }).toList(),

                /// 计算并构建辅助线
                ...buildAuxiliaryLines(_canvasElements),

                /// 构建可打印区域
                ...buildPrintArea(_canvasElements),

                /// 多选元素外框
                if (widget.multiElementsRect() != null) buildMultiElementsRectLine(),
              ],
            ),
          ),

          /// 横向标尺
          if (isDragging) _buildHoriRulerWidget(),

          /// 纵向标尺
          if (isDragging) _buildVerRulerWidget(),

          /// 标签纸信息栏
          if (canvasConfigIml?.isShowLabelButton() ?? false) _buildLabelInfoWidget(),

          /// 批量数据分页
          if (widget.fetchBatchInfo?.call() != null) buildBatchInfoWidget(context),

          /// RFID显示
          buildRFIDWidget(context),

          /// 双色碳带
          buildRibbonDoubleColorWidget(context),

          ///多背景切换入口
          if (widget.backgroundImage.split(",").length > 1) _buildBackgroundChangeEntranceWidget(context),

          /// 2023/5/23 Ice_Liu 隐藏智能提醒相关
          // /// 智能提醒栏
          // if (smartTipsMode != SmartTipsMode.none) buildSmartTipsWidget(context),

          /// 画板复位开关
          if (_canvasScale != 1) _buildCanvasScaleWidget(context),
        ],
      ),
    );

    return container;
  }

  Widget _printColrTipWidget(String color) {
    List<int> colors = color
        .split(".")
        .map(
          (e) => int.parse(e),
        )
        .toList();
    return Container(
      width: widget.templateWidth,
      alignment: Alignment.center,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(intlanguage('app100000069', '当前碳带颜色为')),
          Container(height: 12, width: 18, color: Color.fromRGBO(colors[0], colors[1], colors[2], 1))
        ],
      ),
    );
  }

  /// 构建横向标尺
  Widget _buildHoriRulerWidget() {
    Offset _hRulerOffset = Offset(DisplayUtil.marginBounds, 0);
    _logger.log("========_hRulerOffset: ${_hRulerOffset.toString()}");
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    double topMargin =
        (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag ? 0 : DisplayUtil.designLableInfoHeight);
    return Positioned(
        left: 0,
        top: topMargin,
        child: CanvasRuler(
          _hRulerOffset,
          Offset(_canvasOffset.dx, 0),
          _canvasScale,
          widget.templateWidth?.dp2mm().toDouble() ?? 0,
          widget.templateHeight.dp2mm().toDouble(),
        ));
  }

  /// 构建纵向标尺
  Widget _buildVerRulerWidget() {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    // Offset _vRulerOffset = Offset(0, DisplayUtil.designMarginBounds +(canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
    //     ? 0
    //     : DisplayUtil.designLableInfoHeight) - 34 + _getVerticalOffset());
    Offset _vRulerOffset = Offset(0, -34 + _getVerticalOffset());
    return Positioned(
        left: 0,
        top: 34,
        child: CanvasRuler(
          _vRulerOffset,
          Offset(0, _canvasOffset.dy),
          _canvasScale,
          widget.templateWidth?.dp2mm().toDouble() ?? 0,
          widget.templateHeight.dp2mm().toDouble(),
          axis: Axis.vertical,
        ));
  }

  double arrowImageWidth = 15;
  double padding = 14;
  double alertIDIconWidth = 18;

  ///标签纸信息栏
  Widget _buildLabelInfoWidget() {
    String labelInfo = widget.labelInfo;
    return Positioned(
        left: 0,
        top: 0,
        child: Container(
          height: DisplayUtil.designLableInfoHeight,
          width: MediaQuery.sizeOf(context).width,
          padding: EdgeInsets.only(right: padding, left: padding),
          decoration: BoxDecoration(color: Colors.white),
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  FloatingBarHelper().dismissFloatingBar();
                  widget.onLabelChange?.call();
                },
                child: Container(
                  width: getMaxLength() + arrowImageWidth,
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(labelInfo,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            softWrap: true,
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'PingFangSC-Regular',
                              color: ThemeColor.COLOR_595959,
                            )),
                      ),
                      Container(
                        width: arrowImageWidth,
                        height: arrowImageWidth,
                        child: SvgIcon(
                          'assets/common/arrow_down_gray.svg',
                          useDefaultColor: false,
                          width: arrowImageWidth,
                          height: arrowImageWidth,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              //),
              // Flexible(
              //   flex: 1,
              Expanded(
                child: Container(
                  child: Offstage(
                    offstage: !((widget.materialModelSn ?? "").length > 0),
                    child: GestureDetector(
                      onTap: () {
                        jumpLabelDetail();
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SvgIcon(
                            'assets/element/attribute/icon_font_info.svg',
                            useDefaultColor: false,
                            color: ThemeColor.COLOR_595959,
                            width: alertIDIconWidth,
                            height: alertIDIconWidth,
                          ),
                          Text(" ID:" + widget.materialModelSn ?? "",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              softWrap: true,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                                color: ThemeColor.COLOR_595959,
                              )),
                        ],
                      ),
                      // ),
                    ),
                  ),
                ),
              )
            ],
          ),
        ));
  }

  static Size boundingTextSize(BuildContext context, String text, {int maxLines = 2 ^ 31, double? maxWidth}) {
    if (text.isEmpty) {
      return Size.zero;
    }
    double m = maxWidth == null ? MediaQuery.sizeOf(context).width : maxWidth;
    TextStyle sty = const TextStyle(
      fontSize: 13,
      fontWeight: FontWeight.w400,
      color: ThemeColor.COLOR_595959,
    );
    final TextPainter textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        locale: Localizations.localeOf(context),
        text: TextSpan(text: text, style: sty),
        maxLines: 1)
      ..layout(maxWidth: m);
    return textPainter.size;
  }

  double getMaterialIDTextLength(String text) {
    return (text.isNotEmpty) ? boundingTextSize(context, " ID:" + text).width : 0;
  }

  double getLabelLength(String text) {
    double materialLength = getMaterialIDTextLength(text);
    return MediaQuery.sizeOf(context).width -
        padding -
        padding -
        (materialLength > 0 ? (materialLength + alertIDIconWidth + 2 + 30) : 0) -
        arrowImageWidth;
  }

  double getMaxLength() {
    double width =
        boundingTextSize(context, widget.labelInfo, maxWidth: getLabelLength(widget.materialModelSn ?? "")).width;
    return width;
  }

  jumpLabelDetail() {
    widget.onClickLabelDetail?.call();
  }

  /// 批量预览
  Widget buildBatchInfoWidget(BuildContext context) {
    final batchInfo = widget.fetchBatchInfo?.call();
    if (batchInfo?.length != 2) {
      return Container();
    }
    String formatExcelPageInfo;
    if (Directionality.of(context) == TextDirection.rtl) {
      formatExcelPageInfo = '${batchInfo!.last}/${batchInfo.first + 1}';
    } else {
      formatExcelPageInfo = '${batchInfo!.first + 1}/${batchInfo.last}';
    }
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return Positioned.directional(
        start: 12,
        top: (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
            ? firstLineVercalSpace
            : DisplayUtil.designLableInfoHeight + firstLineVercalSpace),
        //etag模式下减去切换标签纸的高度
        textDirection: Directionality.of(context),
        child: InkWell(
          onTap: () {
            widget.onBatchPreviewTap?.call();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(32),
              border: Border.all(color: ThemeColor.COLOR_EBEBEB, width: 0.5),
              boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.08), offset: Offset(0, 6), blurRadius: 18)],
            ),
            child: Center(
              child: Row(
                children: [
                  Text(
                    '${intlanguage("app01102", "预览")}: ${formatExcelPageInfo}',
                    style: TextStyle(color: ThemeColor.brand, fontSize: 12, fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(
                    width: 2,
                  ),
                  SvgIcon(
                    'assets/common/arrow_down_black.svg',
                    color: ThemeColor.brand,
                    useDefaultColor: false,
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  // RFID显示
  Widget buildRFIDWidget(BuildContext context) {
    // rfidContent生成
    List<Widget> Function({RFIDShowType type, required String rfidValue}) rfidContent =
        ({RFIDShowType? type, required String rfidValue}) {
      List<Widget> rfidContent = [SizedBox.shrink()];
      switch (type) {
        case RFIDShowType.NotMatch:
          rfidContent = [
            const SizedBox(
              width: 8,
            ),
            const SvgIcon(
              'assets/excel/rfid_warn.svg',
              useDefaultColor: false,
            ),
            const SizedBox(
              width: 4,
            ),
            Flexible(
              child: Text(
                rfidValue,
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
              ),
            ),
          ];
          break;
        case RFIDShowType.NotBinding:
          rfidContent = [
            const SizedBox(
              width: 8,
            ),
            const SvgIcon(
              'assets/excel/rfid_warn.svg',
              useDefaultColor: false,
            ),
            const SizedBox(
              width: 4,
            ),
            Text(
              intlanguage('app00735', '未关联'),
              style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_F8473E),
            ),
          ];
          break;
        case RFIDShowType.Normal:
          rfidContent = [
            const SizedBox(
              width: 6,
            ),
            Flexible(
              child: Text(
                rfidValue,
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
              ),
            ),
          ];
          break;
        case null:
          // TODO: Handle this case.
          break;
      }
      return rfidContent;
    };

    // RFID标签显示
    // isShowNoMatch是否展示符合规范Text
    Widget Function({RFIDShowType type}) rfidMark = ({RFIDShowType? type}) {
      return Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(6.0), color: Colors.black.withOpacity(0.06)),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 5.0, vertical: 3.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SvgIcon(
              'assets/excel/rfid_mark_with_text.svg',
              color: ThemeColor.title,
            ),
            const SizedBox(
              width: 2,
            ),
            const SvgIcon(
              'assets/excel/rfid_right_arrow.svg',
              color: ThemeColor.title,
            ),
          ],
        ),
      );
    };

    RfidShowItem? rfidShowItem =
        RfidRepository().getRfidShowItem(CanvasObjectSharedWidget.canvasDataOf(context)?.currentPageIndex ?? 0);
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return Obx(() {
      return _controller != null &&
              _controller?.state.isShowRFID() == true &&
              canvasConfigIml?.supportBindRfid() == true
          ? PositionedDirectional(
              start: 20,
              // top: widget.fetchBatchInfo?.call() != null ? 80 : 46,
              top: widget.fetchBatchInfo?.call() != null
                  ? DisplayUtil.designLableInfoHeight + firstLineVercalSpace + batchInfoHeight + verticalSpace
                  : DisplayUtil.designLableInfoHeight + firstLineVercalSpace,
              end: 20,
              child: Align(
                alignment: AlignmentDirectional.centerStart,
                child: GestureDetector(
                  onTap: widget.rfidBind,
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      rfidMark(type: rfidShowItem.rfidShowType),
                      ...rfidContent(type: rfidShowItem.rfidShowType, rfidValue: rfidShowItem.rfidContent),
                    ],
                  ),
                ),
              ),
            )
          : SizedBox.shrink();
    });
  }

  Widget buildRibbonDoubleColorWidget(BuildContext context) {
    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return Obx(() {
      String? ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
      // TODO: 移除掉下一句，目前insert到Obx中作为刷新使用
      String? _ = _controller?.state.ribbonDoubleColor();
      if (ribbonDoubleColor.isNotEmpty) {
        //     String? ribbonDoubleColor = _controller?.state.ribbonDoubleColor();
        // CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
        // return Obx(() {
        //   String? ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
        //   // TODO: 移除掉下一句，目前insert到Obx中作为刷新使用
        //   String? _ = _controller?.state.ribbonDoubleColor();
        //   if (ribbonDoubleColor.isNotEmpty && _getRibbonDoubleColor(ribbonDoubleColor).length > 1) {

        List<Color> colorArray = _getRibbonDoubleColor(ribbonDoubleColor);
        return PositionedDirectional(
          start: 20,
          // top: widget.fetchBatchInfo?.call() != null ? (_showRFIDSource() ? 100 : 80) : (_showRFIDSource() ? 70 : 46),
          top: widget.fetchBatchInfo?.call() != null
              ? (_showRFIDSource()
                  ? DisplayUtil.designLableInfoHeight +
                      firstLineVercalSpace +
                      batchInfoHeight +
                      verticalSpace +
                      rfidHeight +
                      verticalSpace
                  : DisplayUtil.designLableInfoHeight + firstLineVercalSpace + batchInfoHeight + verticalSpace)
              : (_showRFIDSource()
                  ? DisplayUtil.designLableInfoHeight + firstLineVercalSpace + rfidHeight + verticalSpace
                  : DisplayUtil.designLableInfoHeight + firstLineVercalSpace),
          end: 20,
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ...[
                  Text(
                    intlanguage('app100000069', '当前碳带颜色为'),
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                  ),
                  SizedBox(width: 2),
                ],
                ...colorArray.map((color) => Container(height: 8, width: 8, color: color)).toList()
              ],
            ),
          ),
        );
      } else {
        return SizedBox.shrink();
      }
    });
  }

  bool _showRFIDSource() {
    bool showRFIDSource = _controller != null &&
        _controller?.state.isShowRFID() == true &&
        CanvasPluginManager().canvasConfigImpl?.supportBindRfid() == true;
    return showRFIDSource;
  }

  List<Color> _getRibbonDoubleColor(String ribbonColorInfo) {
    List<String> splitArray = ribbonColorInfo.split(',');
    // 拆分碳带颜色
    if (splitArray.length == 2 && splitArray[0].isNotEmpty && splitArray[1].isNotEmpty) {
      // 双色
      List<String> colorInfo1 = splitArray[0].split('.');
      List<String> colorInfo2 = splitArray[1].split('.');
      return [
        Color.fromRGBO(int.parse(colorInfo1[0]), int.parse(colorInfo1[1]), int.parse(colorInfo1[2]), 1),
        Color.fromRGBO(int.parse(colorInfo2[0]), int.parse(colorInfo2[1]), int.parse(colorInfo2[2]), 1)
      ];
    } else if (splitArray.length == 1 && splitArray[0].isNotEmpty) {
      // 单色
      List<String> colorInfo1 = splitArray[0].split('.');
      return [Color.fromRGBO(int.parse(colorInfo1[0]), int.parse(colorInfo1[1]), int.parse(colorInfo1[2]), 1)];
    }
    return [];
  }

  _setFirstLineSpace() {
    //是否显示批量预览
    bool isShowBatchInfo = widget.fetchBatchInfo?.call() != null;
    //是否显示rfid
    bool isShowRfid = _showRFIDSource();

    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    String ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
    //是否显示rfid碳带颜色
    bool isShowRibbonColor = ribbonDoubleColor.isNotEmpty;
    if (isShowBatchInfo || isShowRfid || isShowRibbonColor) {
      firstLineVercalSpace = 10;
    } else {
      firstLineVercalSpace = 16;
    }
  }

  double _getVerticalOffset() {
    //是否显示批量预览
    bool isShowBatchInfo = widget.fetchBatchInfo?.call() != null;
    //是否显示rfid
    bool isShowRfid = _showRFIDSource();

    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    String ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
    //是否显示rfid碳带颜色
    bool isShowRibbonColor = ribbonDoubleColor.isNotEmpty;
    // 累加垂直偏移量
    double totalOffset = 0;
    //标签纸显示区域
    if (canvasConfigIml?.currentConfigMode() != CanvasCurrentConfigMode.etag) {
      totalOffset += DisplayUtil.designLableInfoHeight;
    }
    totalOffset += firstLineVercalSpace;

    // 批次信息优先显示
    if (isShowBatchInfo) {
      totalOffset += batchInfoHeight;
      totalOffset += verticalSpace;
    }

    // RFID和色带颜色并行显示
    if (isShowRfid) {
      totalOffset += rfidHeight;
      totalOffset += verticalSpace;
    }
    if (isShowRibbonColor) {
      totalOffset += ribbonColorHeight;
      totalOffset += verticalSpace;
    }

    return totalOffset;
    // return max(_getRfidVerticalOffset(), _getRibbonDoubleColorVerticalOffset());
  }

  double _getRfidVerticalOffset() {
    try {
      if (_controller != null && _controller?.state.isShowRFID() == true && widget.fetchBatchInfo?.call() != null) {
        CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
        String? ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
        if (ribbonDoubleColor.isNotEmpty) {
          return (100 - 46 + 8).toDouble();
        } else {
          return (80 - 46 + 8).toDouble();
        }
      }
    } catch (e, s) {
      debugPrint('调用栈信息:\n $s');
    }
    return 0;
  }

  double _getRibbonDoubleColorVerticalOffset() {
    try {
      CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
      String? ribbonDoubleColor = canvasConfigIml?.getRfidColor() ?? "";
      bool isShowRFID = _controller != null && _controller?.state.isShowRFID() == true;
      if (ribbonDoubleColor.isNotEmpty && isShowRFID) {
        return (70 - 46 + 8).toDouble();
      } else if (ribbonDoubleColor.isNotEmpty && widget.fetchBatchInfo?.call() != null) {
        return (80 - 46 + 8).toDouble();
      }
    } catch (e, s) {
      debugPrint('调用栈信息:\n $s');
    }
    return 0;
  }

  Map<String, Uint8List> backgroundDataMap = {};

  ///模板背景图显示
  Widget _buildSelectedBackgroundWidget(BuildContext context) {
    List<String> backgroudList = widget.backgroundImage.split(",");
    String iamgeUrl = backgroudList.length > widget.multipleBackIndex && widget.multipleBackIndex >= 0
        ? backgroudList[widget.multipleBackIndex]
        : "";
    String selectedLocalPath =
        widget.localBackgrounds.length > widget.multipleBackIndex && widget.multipleBackIndex >= 0
            ? widget.localBackgrounds[widget.multipleBackIndex]
            : "";
    File localImage = File(selectedLocalPath);
    if (backgroundDataMap[selectedLocalPath] == null && localImage.existsSync()) {
      backgroundDataMap.clear();
      backgroundDataMap[selectedLocalPath] = localImage.readAsBytesSync();
    }
    return Positioned(
        key: _backGroundKey,
        left: ((widget.canvasRotate ?? 0) / 90 == 1 || (widget.canvasRotate ?? 0) / 90 == 3)
            ? -(widget.templateHeight - (widget.templateWidth ?? 0)) / 2
            : 0,
        top: ((widget.canvasRotate ?? 0) / 90 == 1 || (widget.canvasRotate ?? 0) / 90 == 3)
            ? (widget.templateHeight - (widget.templateWidth ?? 0)) / 2
            : 0,
        child: Transform.rotate(
          angle: (widget.canvasRotate ?? 0) / 90 * (pi / 2),
          child: localImage.existsSync()
              ? Image.memory(
                  backgroundDataMap[selectedLocalPath]!,
                  gaplessPlayback: true,
                  width: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateWidth : widget.templateHeight,
                  height: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateHeight : widget.templateWidth,
                  fit: BoxFit.fill,
                  errorBuilder: (_, __, ___) => CachedNetworkImage(
                    width: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateWidth : widget.templateHeight,
                    height: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateHeight : widget.templateWidth,
                    fit: BoxFit.contain,
                    imageUrl: iamgeUrl,
                    errorWidget: (_, __, ___) =>
                        CanvasImageUtils.errorHolder(width: widget.templateWidth ?? 0, height: widget.templateHeight),
                  ),
                )
              : CachedNetworkImage(
                  width: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateWidth : widget.templateHeight,
                  height: ((widget.canvasRotate ?? 0) / 90) % 2 == 0 ? widget.templateHeight : widget.templateWidth,
                  fit: BoxFit.fill,
                  imageUrl: iamgeUrl,
                  errorWidget: (_, __, ___) =>
                      CanvasImageUtils.errorHolder(width: widget.templateWidth ?? 0, height: widget.templateHeight),
                ),
        ));
  }

  CustomPopupMenuController _popController = CustomPopupMenuController();

  /// 多背景选择
  Widget _buildBackgroundChangeEntranceWidget(BuildContext context) {
    List<String> backgroudList = widget.backgroundImage.split(",");
    String iamgeUrl = backgroudList.length > widget.multipleBackIndex && widget.multipleBackIndex >= 0
        ? backgroudList[widget.multipleBackIndex]
        : "";
    String selectedLocalPath =
        widget.localBackgrounds.length > widget.multipleBackIndex && widget.multipleBackIndex >= 0
            ? widget.localBackgrounds[widget.multipleBackIndex]
            : "";
    if (backgroudList.length < 2) {
      return Container();
    }
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    return Positioned.directional(
        end: 20,
        top: (canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag
            ? firstLineVercalSpace
            : DisplayUtil.designLableInfoHeight + firstLineVercalSpace),
        textDirection: Directionality.of(context),
        child: CustomPopupMenu(
          child: Container(
            padding: EdgeInsets.fromLTRB(5, 6, 5, 6),
            decoration: BoxDecoration(color: Colors.black12, borderRadius: BorderRadius.circular(6)),
            child: Center(
              child: Row(
                children: [
                  CanvasImageUtils.imageWidget(20, 20, iamgeUrl, localPath: selectedLocalPath),
                  const SizedBox(
                    width: 3,
                  ),
                  const SvgIcon(
                    'assets/common/template_bg_change.svg',
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
          menuBuilder: _buildBackgroundImageMenu,
          barrierColor: Colors.transparent,
          pressType: PressType.singleClick,
          showArrow: false,
          enablePassEvent: false,
          controller: _popController,
          verticalMargin: -34,
        ));
  }

  Widget _buildBackgroundImageMenu() {
    double ratio = widget.canvasRotate == 90 || widget.canvasRotate == 270
        ? widget.templateHeight / (widget.templateWidth ?? 0)
        : (widget.templateWidth ?? 0) / widget.templateHeight;
    double itemWidth = ratio > 1 ? 80 : 80 * ratio;
    return ClipRRect(
      borderRadius: BorderRadius.circular(24),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        constraints: const BoxConstraints(maxWidth: 270, maxHeight: 270),
        width: itemWidth * 2 + 50,
        color: Colors.black54,
        child: GridView.count(
          padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
          crossAxisCount: 2,
          crossAxisSpacing: ratio > 1 ? 10 : 20,
          mainAxisSpacing: ratio > 1 ? 10 : 20,
          childAspectRatio: ratio,
          shrinkWrap: true,
          children: _generateBackgroundItems(),
        ),
      ),
    );
  }

  List<Widget> _generateBackgroundItems() {
    List<String> backgroudList = widget.backgroundImage.split(",");
    String iamgeUrl = backgroudList.length > widget.multipleBackIndex && widget.multipleBackIndex >= 0
        ? backgroudList[widget.multipleBackIndex]
        : "";
    List<Widget> result = [];
    // if (null != widget.localBackgrounds && widget.localBackgrounds.isNotEmpty) {
    //   widget.localBackgrounds.forEach((element) {
    //     result.add(GestureDetector(
    //         onTap: () {
    //           widget.onBackgroundImageChange.call(widget.localBackgrounds.indexOf(element));
    //           _popController.hideMenu();
    //         },
    //         child: Container(
    //             constraints: const BoxConstraints(maxWidth: 80, maxHeight: 80),
    //             child: Image.file(
    //               File(element),
    //               // fit: widget.templateWidth > widget.templateHeight ? BoxFit.fitWidth : BoxFit.fitHeight,
    //             ))));
    //   });
    // } else {
    backgroudList.forEach((element) {
      int index = backgroudList.indexOf(element);
      result.add(GestureDetector(
          onTap: () {
            widget.onBackgroundImageChange?.call(index);
            _popController.hideMenu();
          },
          child: Container(
            constraints: const BoxConstraints(maxWidth: 80, maxHeight: 80),
            child: CanvasImageUtils.imageWidget(80, 80, backgroudList.length > index ? backgroudList[index] : "",
                localPath: widget.localBackgrounds.length > index ? widget.localBackgrounds[index] : ""),
          )));
    });
    // }
    return result;
  }

  /// 画布复位开关
  Positioned _buildCanvasScaleWidget(BuildContext context) {
    _logger.log("画布复位开关高度：${SmartTipsNotifier().bottomOffset}");
    return Positioned.directional(
        start: 16,
        bottom: 320.0 + 56,
        textDirection: Directionality.of(context),
        child: GestureDetector(
          onTap: () {
            animateResetInitialize();
            // handleBgPressed();
            if (!_multiSelect) {
              FloatingBarHelper().dismissFloatingBar();
            }
          },
          child: SvgIcon(
            _canvasScale > 1 ? 'assets/canvas_shrink.svg' : 'assets/canvas_expand.svg',
            // color: Colors.white,
            width: 36,
            height: 36,
            useDefaultColor: false,
          ),
        ));
  }

  Positioned buildSmartTipsWidget(BuildContext context) {
    return Positioned.directional(
        end: 10,
        bottom: SmartTipsNotifier().bottomOffset,
        textDirection: Directionality.of(context),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (smartTipsMode == SmartTipsMode.selectAllTextElements ||
                smartTipsMode == SmartTipsMode.selectAllLineElements)
              Padding(
                padding: const EdgeInsets.only(right: 6.0),
                child: ClickableItem(
                  color: CanvasTheme.of(context).backgroundLightColor,
                  onTap: () {
                    if (smartTipsMode == SmartTipsMode.selectAllTextElements) {
                      widget.onSmartTipsTap?.call(SmartTipsAction.selectAllTextElements);
                    } else {
                      widget.onSmartTipsTap?.call(SmartTipsAction.selectAllLineElements);
                    }
                  },
                  borderRadius: BorderRadius.circular(36 / 2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10.0,
                    )
                  ],
                  child: Padding(
                    padding: EdgeInsets.fromLTRB(16, 7, 16, 7),
                    child: Text(
                      smartTipsMode == SmartTipsMode.selectAllTextElements
                          ? intlanguage('app100000792', '选择所有文本')
                          : intlanguage('app100000793', '选择所有线条'),
                      style: CanvasTheme.of(context).floatingButtonTextStyle,
                    ),
                  ),
                ),
              ),
            PopupMenuButton<int>(
                position: PopupMenuPosition.over,
                offset: Offset(0, -220),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                elevation: 3.0,
                itemBuilder: (BuildContext context) => [
                      PopupMenuItem<int>(
                          value: 0,
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            intlanguage('app00506', '全选'),
                            style: CanvasTheme.of(context).popMenuItemTextStyle,
                          )),
                      PopupMenuDivider(
                        height: 0.5,
                      ),
                      PopupMenuItem<int>(
                          value: 1,
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            intlanguage('app100000792', '选择所有文本'),
                            style: CanvasTheme.of(context).popMenuItemTextStyle,
                          )),
                      PopupMenuDivider(
                        height: 0.5,
                      ),
                      PopupMenuItem<int>(
                          value: 2,
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            intlanguage('app100000793', '选择所有线条'),
                            style: CanvasTheme.of(context).popMenuItemTextStyle,
                          )),
                      PopupMenuItem<int>(
                          height: 4,
                          padding: EdgeInsets.zero,
                          child: Container(
                            height: 4,
                            color: CanvasTheme.of(context).backgroundColor,
                          )),
                      PopupMenuItem<int>(
                          value: 3,
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            intlanguage('app100000794', '清空画布'),
                            style: CanvasTheme.of(context).popMenuItemWarnTextStyle,
                          )),
                    ],
                onSelected: (int index) {
                  if (index == 0) {
                    widget.onSmartTipsTap?.call(SmartTipsAction.selectAll);
                  } else if (index == 1) {
                    widget.onSmartTipsTap?.call(SmartTipsAction.selectAllTextElements);
                  } else if (index == 2) {
                    widget.onSmartTipsTap?.call(SmartTipsAction.selectAllLineElements);
                  } else if (index == 3) {
                    widget.onSmartTipsTap?.call(SmartTipsAction.clearCanvas);
                  }
                },
                child: Container(
                    padding: EdgeInsets.fromLTRB(6, 6, 6, 6),
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 10.0,
                        )
                      ],
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(36 / 2),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (smartTipsMode == SmartTipsMode.smartMultiSelect) ...[
                          SizedBox(
                            width: 9,
                          ),
                          Text(
                            intlanguage('app100000791', '智能多选'),
                            style: CanvasTheme.of(context).floatingButtonTextStyle,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                        ],
                        Icon(Icons.keyboard_arrow_up_outlined)
                      ],
                    ))),
          ],
        ));
  }

  /// 计算并构建辅助线
  /// 多选虚线框大于单选
  List<Positioned> buildAuxiliaryLines(List<CanvasElement> canvasElements) {
    final draggingElemet = widget.focusedElements.firstWhereOrNull((element) => element.isDragging);
    if (draggingElemet == null) return [];
    double top = double.maxFinite;
    double left = double.maxFinite;
    double bottom = -double.maxFinite;
    double right = -double.maxFinite;
    double rectWidth = 1.0 / 2;
    if (widget.multiElementsRect() != null) {
      rectWidth = 2.0;
    }
    for (var element in widget.focusedElements) {
      final rect = element.rect;
      top = min(top, rect.top);
      left = min(left, rect.left);
      bottom = max(bottom, rect.bottom);
      right = max(right, rect.right);
    }
    final length = 2000;
    return [
      Positioned(
        left: (widget.templateWidth ?? 0) / 2 - length / 2,
        top: top - rectWidth,
        child: DottedLine(width: 1, color: Colors.blue, axis: Axis.horizontal, length: length),
      ),
      Positioned(
        left: (widget.templateWidth ?? 0) / 2 - length / 2,
        top: bottom + rectWidth,
        child: DottedLine(width: 1, color: Colors.blue, axis: Axis.horizontal, length: length),
      ),
      Positioned(
        left: left - rectWidth,
        top: widget.templateHeight / 2 - length / 2,
        child: DottedLine(width: 1, color: Colors.blue, axis: Axis.vertical, length: length),
      ),
      Positioned(
        left: right + rectWidth,
        top: widget.templateHeight / 2 - length / 2,
        child: DottedLine(width: 1, color: Colors.blue, axis: Axis.vertical, length: length),
      )
    ];
  }

  /// 计算并构建辅助线
  List<Positioned> __buildAuxiliaryLines(List<CanvasElement> canvasElements) {
    return AuxiliaryLinesBuilder.build(canvasElements, context, _canvasScale);
  }

  /// 构建可打印区域
  List<Positioned> buildPrintArea(List<CanvasElement> canvasElements) {
    return PrintAreaBuilder.build(canvasElements, context);
  }

  /// 多选元素外框
  Positioned buildMultiElementsRect() {
    Rect? multiElementsRect = widget.multiElementsRect();
    return Positioned(
      left: (multiElementsRect?.left ?? 0) - 2,
      top: (multiElementsRect?.top ?? 0) - 2,
      child: DraggablePoint(
        onDrag: (d) {
          _handleCornerDragged(
              null,
              DragUpdate(
                dragType: DragType.position,
                position: d / _canvasScale,
                size: Size(1, 1),
                constraints: Size(1, 1),
                rotate: 0,
              ),
              ignoreElement: true);
        },
        onDragEnd: () => _handleDragComplete(null, ignoreElement: true),
        // child: DottedBorder(
        //     borderType: BorderType.Rect,
        //     dashPattern: [4, 4],
        //     color: Colors.blue,
        //     strokeWidth: 2,
        //     child: Container(
        //       width: multiElementsRect.width,
        //       height: multiElementsRect.height,
        //     )),
        child: Container(
          width: multiElementsRect?.width,
          height: multiElementsRect?.height,
        ),
      ),
    );
  }

  /// 多选元素外框虚线
  Positioned buildMultiElementsRectLine() {
    Rect? multiElementsRect = widget.multiElementsRect();
    return Positioned(
      left: (multiElementsRect?.left ?? 0) - 2,
      top: (multiElementsRect?.top ?? 0) - 2,
      child: IgnorePointer(
        child: DottedBorder(
            borderType: BorderType.Rect,
            dashPattern: [4, 4],
            color: Colors.blue,
            strokeWidth: 2,
            child: Container(
              width: multiElementsRect?.width,
              height: multiElementsRect?.height,
            )),
      ),
    );
  }

  /// 创建单个元素
  InteractiveElement createInteractiveElement(CanvasElement canvasElement, bool isItemFocused,
      {bool isShowHandleBtn = false}) {
    String boxId = widget.idBuilder(canvasElement);
    bool isLock = widget.isLock(canvasElement);
    bool isMirror = widget.isMirror(canvasElement);
    bool isAssociateElement = widget.isAssociateElement(canvasElement);
    bool resizeEnable = widget.isResizeEnable(canvasElement);
    dynamic widgetFromLibrary = widget.itemBuilder.call(canvasElement);
    bool tableCellFocused = false;
    bool isBindingElement = (canvasElement.data.isBindingElement() ||
        PdfBindInfoManager.instance.elementIsBindPdf(canvasElement.elementId));

    /// 对角线
    bool showCornerDragBtn = true;

    /// 中线拖动
    bool showCenterDragBtn = false;

    /// 底部拖动
    bool showBottomDragBtn = false;

    /// 文字方向
    Axis textDirection = Axis.horizontal;

    /// 控制点类型
    ControlPointType controlPointType = ControlPointType.fixedRatio;

    /// 表格选中单元格时表格则不在框选状态
    /// 这里从 ui 层篡改而不是从数据层 showControls 修改
    /// 是因为表格仍需保持选中的特性: 置顶, 悬浮工具条展示
    if (isItemFocused && canvasElement.data.type == ElementItemType.table) {
      final tableElement = canvasElement.data as TableElement;
      isItemFocused = tableElement.getFocusedCells().length == 0;
    }

    bool isDefaultElement = false;
    if (canvasElement.data is ImageElement) {
      ImageElement imageElement = canvasElement.data as ImageElement;
      isDefaultElement = imageElement.isDefaultImage ?? false;
    }

    /// 表格单元格被选中
    if (canvasElement.data.type == ElementItemType.table) {
      TableElement tableElement = canvasElement.data as TableElement;
      tableCellFocused = tableElement.getFocusedCells().length > 0;
    }
    int isWidthHeightChanged = 0;
    if (canvasElement.data is TextElement) {
      /// 文本 && 时间元素支持特性
      TextElement textElement = canvasElement.data as TextElement;
      if ((canvasElement.data as TextElement).isSupportBoxStyle()) {
        if (textElement.getTextMode() == TextMode.Horizontal) {
          // 根据新设计，文本元素在横排下始终显示三个控制点
          showCornerDragBtn = true; // 右下角等比缩放
          showCenterDragBtn = true; // 右侧宽度调整
          showBottomDragBtn = true; // 下方高度调整
          textDirection = Axis.horizontal;
          // 横排文本使用缩放类型的控制点
          controlPointType = ControlPointType.scale;
        } else if (textElement.getTextMode() == TextMode.Vertical) {
          showCornerDragBtn = false; // 右下角等比缩放
          showCenterDragBtn = true; // 右侧宽度调整
          // 竖排文本使用缩放类型的控制点
          textDirection = Axis.vertical;
        } else if (textElement.getTextMode() == TextMode.Horizontal_90) {
          showCornerDragBtn = false; // 右下角等比缩放
          showCenterDragBtn = true; // 右侧宽度调整
          // 竖排文本使用缩放类型的控制点
          textDirection = Axis.vertical;
        } else if (textElement.getTextMode() == TextMode.Arc) {
          showCornerDragBtn = true; // 右下角等比缩放
          // 横排文本使用缩放类型的控制点
          controlPointType = ControlPointType.scale;
        }
      }
      if (textElement.typesettingMode == 2) {
        isWidthHeightChanged = 1;
      }
    }
    double rotate = canvasElement.rotate.toDouble();
    return InteractiveElement(
      key: Key('stickerPage_${boxId}_draggableResizable_asset'),
      canTransform: isItemFocused,
      isWidthHeightChanged: isWidthHeightChanged,
      rotate: rotate,
      isDefaultElement: isDefaultElement,
      tableCellFocused: tableCellFocused,
      isLock: isLock,
      isMirror: isMirror,
      isAssociateElement: isAssociateElement,
      resizeEnable: resizeEnable,
      scale: _canvasScale,
      onUpdate: (update) => _handleCornerDragged(canvasElement, update),
      onDelete: () => _handleDeleteTap([canvasElement]),
      onCopy: () => _handleCopyTap([canvasElement]),
      onTap: () => _handleBoxTap(canvasElement),
      onDoubleTap: () => _handleBoxDoubleTap(canvasElement),
      size: canvasElement.size,
      child: widgetFromLibrary,
      startOffset: canvasElement.offset,
      minChildSize: Size(canvasElement.data.getItemDisplayMinWidth(context) ?? 5 * DisplayUtil.dpRatio,
          canvasElement.data.getItemDisplayMinHeight() ?? 5 * DisplayUtil.dpRatio),
      onScaleEndWithFontSize: (newFontSize) =>
          widget.onScaleEndWithFontSize?.call((canvasElement, newFontSize, controlPointType)),
      onDragEnd: () => _handleDragComplete(canvasElement),
      equalRatio: isEqualRatio(canvasElement),
      dragHorizontalOnly: isDragHorizontalOnly(canvasElement),
      constraints: canvasElement.data.getBoxConstrains(context),
      isVipElement: isVipElement(canvasElement),
      isBindingElement: isBindingElement,
      isShowHandleBtn: isShowHandleBtn,
      showCenterDragBtn: showCenterDragBtn,
      showCornerDragBtn: showCornerDragBtn,
      showBottomDragBtn: showBottomDragBtn,
      textDirection: textDirection,
      currentFontSize:
          canvasElement.data is TextElement ? (canvasElement.data as TextElement).fontSize.toDouble() : null,
      controlPointType: controlPointType,
    );
  }

  bool isVipElement(CanvasElement element) {
    // if (element.data is DateElement) {
    //   DateElement textElement = element.data;
    //   if(textElement.hasVipSource()){
    //     return true;
    //   }
    // }else if (element.data is TextElement) {
    //   TextElement textElement = element.data;
    //   if(textElement.hasVipSource()){
    //     return true;
    //   }
    // } else if (element.data is TableElement) {
    //   TableElement tableElement = element.data;
    //   if(tableElement.hasVipSource()){
    //     return true;
    //   }
    // } else if (element.data is ImageElement) {
    //   ImageElement imageElement = element.data;
    //   if (imageElement.materialId != null &&
    //       imageElement.materialId.isNotEmpty &&
    //       MaterialManager.sharedInstance().vipIdList.any((id) => id.toString() == imageElement.materialId)) {
    //     return true;
    //   }
    // }
    // return false;
    return element.data.hasVipSource();
  }

  /// 仅支持横向手势
  bool isDragHorizontalOnly(CanvasElement canvasElement) {
    // return canvasElement.data.isTextElement() || canvasElement.data.type == ElementItemType.line;
    return canvasElement.data.type == ElementItemType.line;
  }

  bool isEqualRatio(CanvasElement canvasElement) {
    if (canvasElement.elementType == ElementItemType.image) {
      ImageElement imageElement = canvasElement.data as ImageElement;
      return (imageElement.allowFreeZoom != true);
      /** 图片允许自由拉伸 */
    } else if (canvasElement.elementType == ElementItemType.graph) {
      GraphElement graphElement = canvasElement.data as GraphElement;
      return (graphElement.graphType == 1 /** 圆 */);
    }
    return (canvasElement.elementType == ElementItemType.qrcode);
  }

  @override
  void handleKeyDown(RawKeyDownEvent value) {
    if (value.logicalKey == LogicalKeyboardKey.space) {}
  }

  @override
  void handleKeyUp(RawKeyUpEvent value) async {
    if (value.logicalKey == LogicalKeyboardKey.space) {}
  }

  ///是否多选拖拽
  bool _multiSelectDragging = false;

  void _handleCornerDragged(CanvasElement? boxData, DragUpdate dragUpdate, {bool ignoreElement = false}) {
    // _logger.log(
    //     "_handleCornerDragged -> dragUpdate = ${dragUpdate.toString()}, scale: $_canvasScale");
    widget.onTranslated?.call(boxData, dragUpdate, ignoreElement);

    _multiSelectDragging = ignoreElement && widget.multiElementsRect() != null;
  }

  void _handleDragComplete(CanvasElement? boxData, {bool ignoreElement = false}) {
    widget.onTranslateEnded?.call(boxData, ignoreElement);
    // _logger.log("_handleDragComplete");
    _multiSelectDragging = false;
  }

  void _handleBoxDoubleTap(CanvasElement boxData) {
    widget.onItemDoubleTap?.call(boxData);
  }

  void _handleBoxTap(CanvasElement boxData) {
    widget.onBoxTap?.call(boxData);
  }

  void _handleMouseOverChanged(bool value) {
    _isCardHovered.value = value;
  }

  void handleBgPressed() {
    FloatingBarHelper().dismissFloatingBar();
    InputUtils.unFocus();
    setState(() {
      widget.onBoxTap?.call(null);
    });
  }

  void _handleReplaceTap(List<CanvasElement> data) {
    widget.onBoxReplace?.call(data);
  }

  void _handleEditTap(List<CanvasElement> data) {
    widget.onBoxEdit?.call(data);
  }

  void _handleDeleteTap(List<CanvasElement> data) {
    widget.onBoxDeleted?.call(data);
    // FloatingBarHelper().dismissFloatingBar();
  }

  void _handleCopyTap(List<CanvasElement> data) {
    widget.onBoxCopied?.call(data);
  }

  void _handleLockTap(List<CanvasElement> data) {
    widget.onBoxLock?.call(data);
    _freshFloatingBar();
  }

  void _handleRotateTap(List<CanvasElement> data) {
    widget.onBoxRotate?.call(data);
  }

  void _handleAlignTap(int action, List<CanvasElement> data) {
    widget.onBoxAlign?.call(action, data);
  }

  void _handleMergeCells(List<CanvasElement> data) {
    widget.onMergeCells?.call(data);
  }

  void _handleTableCellAction(int action, List<CanvasElement> data) {
    widget.onCellAction?.call(action, data);
  }

  void _handleSplitCells(List<CanvasElement> data) {
    widget.onSplitCell?.call(data);
  }

  void _handleMultiSelect() {
    widget.onMultiSelectChanged?.call(!_multiSelect);
    // _freshFloatingBar();
  }

  void _handleSelectAll() {
    widget.onSelectAll?.call();
  }

  void _handleSelectAllText() {
    widget.onSelectAllText?.call();
  }

  void _handleSelectAllLine() {
    widget.onSelectAllLine?.call();
  }

  void _freshFloatingBar() {
    List<CanvasElement> selectedCanvasElements =
        widget.focusedElements.where((element) => widget.isItemFocused(element) && !widget.isMirror(element)).toList();
    _logger.log("悬浮条入口-->选中是否为空: ${selectedCanvasElements.isEmpty}");
    if (selectedCanvasElements.isNotEmpty && (FloatingBarVisibleNotifier().floatingBarVisible || _multiSelect)) {
      ///悬浮条顶部距离
      double topMargin = 0.0;
      RenderBox? canvasRender = _canvasKey.currentContext?.findRenderObject() as RenderBox?;
      Offset? canvasOffsetGlobal = canvasRender?.localToGlobal(Offset.zero) ?? null;
      _logger.log(
          "画板位置Global-->left: ${canvasOffsetGlobal?.dx}, top: ${canvasOffsetGlobal?.dy}, Global.size: ${canvasRender?.size.toString()}");
      topMargin = canvasOffsetGlobal?.dy ?? 0.0;

      CanvasElement firstCanvasElement = selectedCanvasElements.first;
      debugPrint("焦点定位：工具栏首个元素ID: ${firstCanvasElement.elementId}");
      JsonElement firstJsonElement = firstCanvasElement.data;
      Size anchorSize = firstCanvasElement.rotate == 90 || firstCanvasElement.rotate == 270
          ? firstCanvasElement.size.flipped
          : firstCanvasElement.size;

      Offset cellOffset = Offset(0, 0);

      ///判断是否选中的为表格的cell
      if (firstJsonElement.type == ElementItemType.table &&
          (firstJsonElement as TableElement).getFocusedCells().length > 0) {
        TableCellElement cell = (firstJsonElement).getFocusedCells().first;
        anchorSize = Size(cell.width.mm2dp().toDouble(), cell.height.mm2dp().toDouble());
        // print("选中的tableCell信息：${cell.toJson()}");
        if (cell.isCombine()) cell = cell.firstCellOfCombine!;
        Offset offset = (firstJsonElement).getCellOffset(cell.columnIndex, cell.rowIndex);
        cellOffset = cellOffset + offset;
        _logger.log("选中的tableCell偏移信息：${cellOffset.toString()}");
        _logger.log("选中的tableCell宽高信息：${anchorSize.toString()}");
      }

      double fixTop = (canvasOffsetGlobal?.dy ?? 0.0) + 34;
      _logger.log(
          "元素位置-->left: ${firstCanvasElement.rect.left * _canvasScale}, top: ${firstCanvasElement.rect.top * _canvasScale}, Element.size: ${firstCanvasElement.rect.toString()}");
      if (!_multiSelect) {
        /// 画板Top + 选中元素Top - 悬浮窗高度 + 画板本身的偏移 + 画板移动的偏移 + 表格元素的偏移
        topMargin = topMargin +
            firstCanvasElement.rect.top * _canvasScale -
            49 +
            // DisplayUtil.designMarginBounds +DisplayUtil.designLableInfoHeight +
            _canvasOffset.dy +
            cellOffset.dy * _canvasScale +
            _getVerticalOffset();
      } else {
        /// 画板Top + 画板移动的偏移
        topMargin = topMargin + _canvasOffset.dy;
      }

      _logger.log("画板偏移-->top: ${_canvasOffset.dy}, left: ${_canvasOffset.dx}");
      _logger.log("悬浮窗位置-->top: $topMargin, 最大宽度: ${DisplayUtil.deviceSize.width}");

      CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
      //顶部标签纸栏不显示的情况下 画板整体会往上移动标签纸条目的高度 工具条的top需要减去标签纸栏的高度
      if (canvasConfigIml?.isShowLabelButton() == false) {
        topMargin = topMargin - DisplayUtil.designLableInfoHeight;
      }

      /// 元素相对画板的偏移 + 画板整体偏移 + 画板边距 + tabcell偏移
      Offset anchorOffset = Offset(
          (firstCanvasElement.rect.left ?? 0) * _canvasScale +
              _canvasOffset.dx +
              DisplayUtil.marginBounds +
              cellOffset.dx * _canvasScale,
          topMargin);
      _logger.log("锚点信息-->大小: ${anchorSize.toString()}, 偏移: ${anchorOffset.toString()}");

      ///悬浮窗中箭头的显示位置
      double arrowMargin =
          (firstCanvasElement.rect.center.dx + DisplayUtil.marginBounds) / canvasRender!.size.width ?? 0.0;

      if (selectedCanvasElements.length == 0) {
        FloatingBarHelper().dismissFloatingBar();
        return;
      }

      /// 非镜像元素的个数
      var sizeExpectMirror = _canvasElements
          .where((element) => !(element.data.isOpenMirror == 0 && (element.data.mirrorId ?? '').length > 0))
          .toList()
          .length;

      FloatingBarHelper().showFloatingBar(context, fixTop, arrowMargin, anchorSize * _canvasScale, anchorOffset,
          _canvasElements, selectedCanvasElements, _canvasScale, _multiSelect, sizeExpectMirror > 1, (type, elements) {
        switch (type) {
          case FloatingShortcutType.edit:
            _handleEditTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.replace:
            _handleReplaceTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.remove:
            _handleDeleteTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.copy:
            _handleCopyTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.lock:
            _handleLockTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.rotate:
            _handleRotateTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.h_start:
            _handleAlignTap(0, selectedCanvasElements);
            break;
          case FloatingShortcutType.h_center:
            _handleAlignTap(1, selectedCanvasElements);
            break;
          case FloatingShortcutType.h_end:
            _handleAlignTap(2, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_top:
            _handleAlignTap(3, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_center:
            _handleAlignTap(4, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_bottom:
            _handleAlignTap(5, selectedCanvasElements);
            break;
          case FloatingShortcutType.h_distribute:
            _handleAlignTap(6, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_distribute:
            _handleAlignTap(7, selectedCanvasElements);
            break;
          case FloatingShortcutType.h_distribute_shrink:
            _handleAlignTap(8, selectedCanvasElements);
            break;
          case FloatingShortcutType.h_distribute_expand:
            _handleAlignTap(9, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_distribute_shrink:
            _handleAlignTap(10, selectedCanvasElements);
            break;
          case FloatingShortcutType.v_distribute_expand:
            _handleAlignTap(11, selectedCanvasElements);
            break;
          case FloatingShortcutType.tab_delete_row:
            _handleTableCellAction(0, selectedCanvasElements);
            break;
          case FloatingShortcutType.tab_insert_row:
            _handleTableCellAction(1, selectedCanvasElements);
            break;
          case FloatingShortcutType.tab_delete_column:
            _handleTableCellAction(2, selectedCanvasElements);
            break;
          case FloatingShortcutType.tab_insert_column:
            _handleTableCellAction(3, selectedCanvasElements);
            break;
          case FloatingShortcutType.tab_cell_clear:
            _handleTableCellAction(4, selectedCanvasElements);
            break;
          case FloatingShortcutType.splitCells:
            _handleSplitCells(selectedCanvasElements);
            break;
          case FloatingShortcutType.mergeCells:
            _handleMergeCells(selectedCanvasElements);
            break;
          case FloatingShortcutType.multiSelect:
            _handleMultiSelect();
            break;
          case FloatingShortcutType.edit:
            _handleEditTap(selectedCanvasElements);
            break;
          case FloatingShortcutType.select_all:
            _handleSelectAll();
            break;
          case FloatingShortcutType.select_all_text:
            _handleSelectAllText();
            break;
          case FloatingShortcutType.select_all_line:
            _handleSelectAllLine();
            break;
          case FloatingShortcutType.previous:
            break;
          case FloatingShortcutType.next:
            break;
          case FloatingShortcutType.align:
            break;
          case FloatingShortcutType.distributed:
            break;
          case FloatingShortcutType.back:
            break;
          case FloatingShortcutType.tab_cell_combine:
            break;
          case FloatingShortcutType.tab_cell_split:
            break;
          case FloatingShortcutType.tab_cell_delete:
            break;
          case FloatingShortcutType.tab_cell_insert:
            break;
        }
      }, widget.checkSelectAll, widget.checkSelectAllText, widget.checkSelectAllLine);
    } else {
      FloatingBarHelper().dismissFloatingBar();
    }
  }

  Animation<Matrix4>? _animationReset;
  late AnimationController _controllerReset;

  void animateResetInitialize() {
    _controllerReset.reset();
    _animationReset = Matrix4Tween(
      begin: _transformController.value,
      end: Matrix4.identity(),
    ).animate(_controllerReset);
    _animationReset?.addListener(_onAnimateReset);
    _controllerReset.forward();
  }

  void _onAnimateReset() {
    _transformController.value = _animationReset!.value;
    if (!(_controllerReset.isAnimating)) {
      _animationReset?.removeListener(_onAnimateReset);
      _animationReset = null;
      _controllerReset.reset();
      setState(() {});
    }
  }
}
