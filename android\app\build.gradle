
buildscript {
  repositories {
    mavenCentral()
  }
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'org.greenrobot.greendao'
apply from: 'product-flavors.gradle'
apply plugin: 'kotlin-kapt'
apply plugin: 'dagger.hilt.android.plugin'
// 应用 com.sensorsdata.analytics.android 插件
//apply plugin: 'com.sensorsdata.analytics.android'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"




def packageTime() {
    return new Date().format("MM-dd", TimeZone.getTimeZone("UTC"))
}

def getGitCommitDescription() {
    def cmd = "git log -1 --pretty=format:%cd --date=iso"
    def proc = cmd.execute()
    String cmdResult = proc.text.trim()
    String[] resultArray = cmdResult.replaceAll(":", "").split()
    String apkName = (resultArray[0] + "-" + resultArray[1])
    return apkName
}



def dartEntryPoint = "lib/main.dart"

android {
    namespace = "com.gengcon.android.jccloudprinter"
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

//    ndkVersion "25.1.8937393"
    ndkVersion "27.0.12077973"

    compileSdkVersion rootProject.ext.android["compileSdkVersion"]
    buildToolsVersion rootProject.ext.android["buildToolsVersion"]
    defaultConfig {
        applicationId APP_ID
        versionCode Integer.parseInt(VERSION_CODE)
        versionName VERSION_NAME
        minSdkVersion rootProject.ext.android["minSdkVersion"]
        targetSdkVersion rootProject.ext.android["targetSdkVersion"]

        buildConfigField "String", "App_SHA1", "\"$project.SHA1_APP\""
        buildConfigField "String", "VERSION_NAME", "\"$project.VERSION_NAME\""
        buildConfigField "int", "VERSION_CODE", project.VERSION_CODE

        multiDexEnabled true
        flavorDimensions "versionCode"
        buildConfigField "String", "HOTFIXTAGS", "\"${HOTFIXTAGS}\""
//        resConfigs "zh", "en"
        manifestPlaceholders = [
                BAIDU_APPKEY_VALUE: CN_BD_LBS_AK,
                FB_APP_ID         : CN_FB_ID,
                FB_APP_SCHEME     : CN_FB_SCHEME,
                qqappid           : CN_QQ_ID,
                TENCENT_APP_ID    : CN_QQ_ID,
                UM_KEY            : CN_UM_KEY,
                "apk.applicationId" : "com.gengcon.android.jccloudprinter",

                JPUSH_PKGNAME: APP_ID,
                JPUSH_APPKEY : "f3725414ce59593f7ca894e1",//值来自开发者平台取得的AppKey
                JPUSH_CHANNEL: "default_developer",
                MEIZU_APPKEY : "MZ-3b20c9b6666948048b9e0b311db8f7f2",
                MEIZU_APPID : "MZ-154619",
                OPPO_APPKEY : "OP-6efc955ae1974be7bfc641bd9a7509fc",
                OPPO_APPID : "OP-3712998",
                OPPO_APPSECRET : "OP-4c2c06d10c374fbd92beae2ba798b061",
                VIVO_APPKEY : "9fc055ee67de64853cceeff3cd65fa0c",
                VIVO_APPID : "100237347",
                HONOR_APPID : "900807024",
                XIAOMI_APPID:"MI-2882303761517881266",
                XIAOMI_APPKEY:"MI-5881788179266",
        ]

        ndk {
            //设置支持的SO库架构，根据需要 自行选择添加的对应cpu类型的.so库。还可以添加  'x86_64', 'mips', 'mips64'
            abiFilters "armeabi-v7a", "arm64-v8a"
        }
    }
  dexOptions {
    preDexLibraries false
  }

    splits{
        abi{
            def isReleaseBuild = false

            gradle.startParameter.taskNames.find {
                // Enable split for release builds in different build flavors
                // (assemblePaidRelease, assembleFreeRelease, etc.)
                if (it ==~ /:app:assemble.*Release/) {
                    isReleaseBuild = true
                    return true // break
                }

                return false // continue
            }

            // Enables building multiple APKs per ABI.
            enable isReleaseBuild
            reset()
            include "armeabi-v7a", "arm64-v8a"
            universalApk true
        }
    }

    //此处配置必须添加 否则无法正确运行

  signingConfigs {
        debug {
            storeFile file('debug.jks')
            storePassword "whsf2018"
            keyAlias "key0"
            keyPassword "whsf2018"
        }
        release {
            storeFile file('JC_sign.jks')
            storePassword "whsf2018"
            keyAlias "key0"
            keyPassword "whsf2018"
        }
    }

    buildTypes {
        debug {
            minifyEnabled false //混淆
            zipAlignEnabled false
            shrinkResources false
            ext.enableCrashlytics = false
            signingConfig signingConfigs.debug
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'proguard-third.pro'
        }

        release {
            minifyEnabled true //混淆
            zipAlignEnabled true
            shrinkResources true
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'proguard-third.pro'
        }

    }

    android.applicationVariants.all { variant ->
        variant.outputs.all { output ->
            String abi = ""
            if (output.getFilters() != null && output.getFilters().size() > 0) {
                abi = "_" + output.getFilters().get(0).getIdentifier()
            }
            if (variant.buildType.name == "release") {
                if ("true" == IS_JENKINS) {
                    outputFileName = "Niimbot_v${project.VERSION_NAME}_${getGitCommitDescription()}_${ENVIRONMENT}_${VERSION_CODE}_${variant.productFlavors[0].name}${abi}.apk"
                } else {
                    outputFileName = "Niimbot_v${project.VERSION_NAME}_${VERSION_CODE}_${packageTime()}_${ENVIRONMENT}_${variant.productFlavors[0].name}${abi}.apk"
                }
            } else {
                outputFileName = "debug_v${project.VERSION_NAME}_${VERSION_CODE}_${ENVIRONMENT}_${variant.productFlavors[0].name}${abi}.apk"
            }
        }
    }


  packagingOptions {
    //google firebase相关
    jniLibs {
            useLegacyPackaging = true
      pickFirsts += ['lib/arm64-v8a/libc++_shared.so', 'lib/armeabi-v7a/libc++_shared.so']
    }
    resources {
      excludes += ['META-INF/rxjava.properties', 'project.properties', 'META-INF/INDEX.LIST', 'META-INF/DEPENDENCIES', 'META-INF/LICENSE', 'META-INF/LICENSE.txt', 'META-INF/license.txt', 'META-INF/NOTICE', 'META-INF/NOTICE.txt', 'META-INF/notice.txt', 'META-INF/ASL2.0']
      pickFirsts += [
        'assets/libwbsafeedit_x86_64',
        'assets/libwbsafeedit_64',
        'assets/libwbsafeedit_x86',
        'assets/libwbsafeedit',
        'assets/h5_qr_back.png',
        'assets/com.tencent.open.config.json'

      ]
    }
  }


    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }
    bundle {
        language {
            enableSplit = false
        }
        density {
            enableSplit = true
        }
        abi {
            enableSplit = true
        }
    }

    //额外添加的，在build之前检查一下githook脚本是否存在
    // afterEvaluate {
    //     tasks.matching {
    //         it.name != "runGitHook"
    //     }.each {
    //         it.dependsOn(runGitHook)
    //     }
    // }

  repositories {
    flatDir {
      dirs 'libs'
    }
  }
  androidResources {
    ignoreAssetsPattern '!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~'
    additionalParameters '--auto-add-overlay'
  }
  lint {
    abortOnError false
    checkReleaseBuilds false
  }
}

flutter {
    source '../..'
    target dartEntryPoint
}

//project.afterEvaluate{
//  getTasksByName("compileDebugKotlin",false).each {
//    it.mustRunAfter("greendao")
////        it.dependsOn("greendao")
//  }
//}



dependencies {
    implementation fileTree(include: ['*.jar','*.aar'], dir: 'libs')
    implementation UtilLib.eventbus
    implementation 'com.bigkoo:quicksidebar:1.0.3'
    implementation 'com.github.AItsuki:SwipeMenuRecyclerView:2.1.5'
    implementation 'com.orhanobut:dialogplus:1.11@aar'
    implementation files('libs/nineoldandroids-2.4.0.jar')
    implementation "org.aspectj:aspectjrt:1.9.5"
    implementation project(path: ':fluwx')
    implementation ServiceLib.paymentLibrary
    implementation project(':baselibrary')
    implementation project(':niimbotBaselibrary')
    if (isMainLibrary.toBoolean()) {
        implementation project(':mainlibrary')
    }
    if (isTemplateLibrary.toBoolean()) {
        implementation project(':templatelibrary')
    }
    if (isLoginLibrary.toBoolean()) {
        implementation project(':login_module')
    }
    implementation project(':printlibrary')

    if (isConnectUiLibrary.toBoolean()) {
        implementation project(':connectUiLibrary')
    }
    if (isBluetoothlibrary.toBoolean()) {
        implementation project(':bluetoothlibrary')
    }
    if (isStoreLibrary.toBoolean()) {
        implementation project(':storelibrary')
    }
    if (isX5.toBoolean()) {
        implementation project(path: ':webviewlibrary_x5')
    } else {
        implementation project(path: ':webviewlibrary')
    }
    implementation project(':templatecoordinator')
    implementation project(':viplibrary')
    implementation project(':goods_module')
    implementation HiltLib.hiltAndroid
    implementation project(path: ':capacitor-android')
    kapt HiltLib.hiltCompiler
//    implementation project(path: ':common_repository')
    implementation project(':business_livecode')
    implementation project(':common_skin')

    implementation project(':common_graphql')
    guanwangImplementation project(':common_speech')
    yingyongbaoImplementation project(':common_speech')
  samsungImplementation project(':common_speech')

  // Import the Firebase BoM
    googleImplementation platform('com.google.firebase:firebase-bom:31.2.0')
    // Add the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    googleImplementation 'com.google.firebase:firebase-crashlytics-ktx'
    googleImplementation 'com.google.firebase:firebase-analytics-ktx'
    implementation(group: 'com.niimbot.skia', name: 'niimbot_canvas_image', version: rootProject.ext.android["skiaVersionName"]) {
        artifact {
            classifier = "so"
        }
        changing = true
    }

  //以下为OPPO 3.0.0 aar需要依赖----start---
  implementation 'com.google.code.gson:gson:2.8.5'
  implementation 'commons-codec:commons-codec:1.6'
  implementation 'androidx.annotation:annotation:1.1.0'
//  谷歌请按照厂商文档配置主 gradle 谷歌镜像依赖和添加 google-services.json 后再打开此依赖
  implementation 'com.google.firebase:firebase-messaging:23.2.0'
//  华为请按照厂商文档配置主 gradle 华为镜像依赖和添加 agconnect-services.json 后再打开此依赖
  implementation 'com.huawei.hms:push:6.12.0.300'

  implementation 'io.sentry:sentry-android-ndk:7.2.0'
}
kapt {
  correctErrorTypes true
}

//谷歌请按照厂商文档配置根 gradle 谷歌镜像依赖和添加 google-services.json 后再打开此插件依赖
apply plugin: 'com.google.gms.google-services'
//华为请按照厂商文档配置根 gradle 华为镜像依赖和添加 agconnect-services.json 后再打开此插件依赖
apply plugin: 'com.huawei.agconnect'

//2023-03-27 注销guanwang渠道的 firebase mapping文件上传任务
gradle.taskGraph.whenReady {
    tasks.each{ task ->
        if (task.name.contains("uploadCrashlyticsMappingFileGuanwangRelease")
        || task.name.contains("uploadCrashlyticsMappingFileSamsungRelease")) {
            task.enabled = false
        }
    }
}


configurations.all {
    resolutionStrategy {
      force "com.jingchen.printsdk:api:$jcsdk_version"
      //循环每个依赖库
      eachDependency { org.gradle.api.artifacts.DependencyResolveDetails details ->
        //获取当前循环到的依赖库
        def requested = details.requested
        //如果这个依赖库群组的名字是com.squareup.okhttp3
        if (requested.group == 'androidx.annotation' && requested.name == "annotation") {
          details.useVersion '1.4.0'
        } else if (requested.group == 'androidx.appcompat' && requested.name == "appcompat") {
          details.useVersion '1.4.1'
        }
      }
    }
}

