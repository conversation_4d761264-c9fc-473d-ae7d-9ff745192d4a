import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/config/canvas_theme_data.dart';
import 'package:flutter_canvas_plugins_interface/config/template_config.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/shared/canvas_font_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/localization_config.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:get/get.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/tinykit_canvas_theme_widget.dart';
import 'package:niimbot_tiny_canvaskit/canvaskit/utils/input_utils.dart';
import 'package:text/pages/cable/cable_canvas_state.dart';
import 'package:text/pages/industry_template/select_label/label_details_page.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/svg_icon.dart';

import '../../utils/common_fun.dart';
import '../../utils/theme_color.dart';
import '../../widget/get_bind_widget.dart';
import '../industry_template/select_label/label_category_list_model.dart';
import 'cable_canvas_logic.dart';

class CableCanvasPage extends StatefulWidget {
  String? templateJsonData;
  String? fontPath;

  CableCanvasPage({Key? key, this.templateJsonData, this.fontPath}) : super(key: key);

  @override
  _CableCanvasPageState createState() => _CableCanvasPageState();
}

class _CableCanvasPageState extends State<CableCanvasPage> {
  late final CableCanvasLogic logic;
  late final CableCanvasState state;

  @override
  void initState() {
    logic = CableCanvasLogic();
    state = logic.state;
    state.appDocDir = widget.fontPath ?? "";
    logic.init(context, widget.templateJsonData);
    Get.put(logic);
    super.initState();
    CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track": 'view', "posCode": "125"});
  }

  @override
  void dispose() {
    super.dispose();
    logic.unLabelListener();
    Get.delete<CableCanvasLogic>();
  }

  @override
  Widget build(BuildContext context) {
    // Widget vipTipWidget = vipTipContainer();
    Widget vipTipWidget = Container();
    debugPrintBuildScope = true;
    return GetBindWidget(
      binds: [logic],
      child: GetBuilder<CableCanvasLogic>(builder: (logic) {
        return CanvasTheme(
          themeData: CanvasThemeData.canvasThemeDataDefault(),
          language: 'en',
          child: GestureDetector(
            onTap: InputUtils.unFocus,
            child: Scaffold(
              resizeToAvoidBottomInset: false,
              body: state.jsonData != null && state.templateData != null
                  ? TinyKitCanvasThemeWidget(
                      // key: state.canvasAddItemBoxKey,
                      jsonData: state.jsonData,
                      language: 'en',

                      fontConfig:
                          CanvasFontConfig(fontCategories: [], fontPath: state.appDocDir, fontDefaultFile: 'ZT001.ttf'),
                      onPrint: (String? canvasJson, int? currentPage, TemplateConfig? config,
                          BuildContext canvasContext, Widget? extraWidget) {
                        logic.onPrintData(canvasJson, currentPage, config, context, extraWidget);
                      },
                      onSave: logic.onSavedData,
                      onLabelInit: logic.onLabelData,
                      onConfig: logic.onConfig,
                      localizationConfig: LocalizationConfig(Locale('zh'),
                          customLocalizationResource: {
                            // LocalStringKey.element: '自定义标题'
                          },
                          direction: TextDirection.ltr),
                      toolkitButtons: [],
                      needDownloadFonts: true,
                      defaultSelectType: '',
                      canvasInitData: logic.onCanvasInitData,
                      onPrintSettings: () {
                        /// 数据埋点
                        ToNativeMethodChannel().sendTrackingToNative({
                          "track": "click",
                          "posCode": "125_327_300",
                          "ext": {"source": 2}
                        });
                        logic.onPrintSettings(context);
                      },
                      onSendTrackEvent: (trackParms) {
                        /// 数据埋点
                        ToNativeMethodChannel().sendTrackingToNative(trackParms);
                      },
                      vipTipWidget: vipTipWidget,
                      paperErrorTipWidget: paperErrorTipContainer(),
                      frameInfo: state.frameInfo,
                    )
                  : Container(),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildLabelInfoWidget() {
    String materialModelSn = state.templateData?['profile']?['extrain']?['materialModelSn'] ?? "";
    var labelInfo = logic.getSimpleInfo();
    return Container(
      height: DisplayUtil.designLableInfoHeight,
      padding: EdgeInsets.symmetric(vertical: 4.5, horizontal: 16),
      decoration: BoxDecoration(color: Colors.white),
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              if (state.isShowCableLabel) {
                logic.changeLabel(context);

                /// 数据埋点
                ToNativeMethodChannel().sendTrackingToNative({
                  "track": "click",
                  "posCode": "108_211",
                  "ext": {"source": 2}
                });
              }
            },
            child: Container(
              child: Row(
                children: [
                  Text(labelInfo,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w400,
                        color: ThemeColor.COLOR_595959,
                      )),
                  state.isShowCableLabel
                      ? Image.asset(
                          "assets/images/arrow_down_gray.png",
                          height: 17,
                          width: 17,
                          matchTextDirection: true,
                          fit: BoxFit.contain,
                        )
                      : Container(),
                ],
              ),
            ),
          ),
          Expanded(
            child: Container(
              child: Offstage(
                offstage: materialModelSn.isEmpty,
                child: GestureDetector(
                  onTap: () {
                    jumpLabelDetailPage(labelInfo);

                    /// 数据埋点
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "108_074_236",
                      "ext": {"source": 2}
                    });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SvgIcon(
                        'assets/images/icon_font_info.svg',
                        width: 15,
                        height: 15,
                        fit: BoxFit.cover,
                      ),
                      Text(" ID:" + materialModelSn ?? "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          softWrap: true,
                          style: const TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w400,
                            color: ThemeColor.COLOR_595959,
                          )),
                    ],
                  ),
                  // ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget vipTipContainer() {
    return GetBuilder<CableCanvasLogic>(builder: (logic) {
      return state.frameInfo.isVip
          ? Container()
          : GestureDetector(
              onTap: () {
                logic.toVipCheck(context);

                /// 数据埋点
                ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "125_328_304", "ext": {}});
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12.0),
                    topRight: Radius.circular(12.0),
                  ),
                  image: DecorationImage(
                    image: AssetImage('assets/images/cable_vip_bg.png'), // 设置背景图片
                    fit: BoxFit.cover, // 图片填充方式
                  ),
                ),
                child: Row(
                  children: [
                    Image.asset(
                      "assets/images/cable_icon.png",
                      width: 20,
                      height: 20,
                    ),
                    SizedBox(
                      width: 6,
                    ),
                    Text(intlanguage('app100001654', '您正在试用线缆小程序'),
                        style: TextStyle(color: ThemeColor.COLOR_072650, fontSize: 13, fontWeight: FontWeight.w400)),
                    Expanded(child: Container()),
                    Text(intlanguage('app01511', '立即开通'),
                        style: TextStyle(color: ThemeColor.COLOR_072650, fontSize: 13, fontWeight: FontWeight.w400)),
                    SizedBox(
                      width: 3,
                    ),
                    const SvgIcon(
                      'assets/images/tiny_go_gcanvas.svg',
                      width: 7,
                      height: 12,
                      color: ThemeColor.COLOR_072650,
                      fit: BoxFit.cover,
                    )
                  ],
                ),
              ));
    });
  }

  Widget paperErrorTipContainer() {
    return Column(
      children: [
        _buildLabelInfoWidget(),
        state.cableLabelToast.isNotEmpty
            ? Container(
                padding: EdgeInsets.fromLTRB(16, 4, 16, 4),
                width: double.infinity,
                height: 26,
                color: ThemeColor.COLOR_FFF5ED,
                child: Row(
                  children: [
                    const SvgIcon(
                      'assets/images/toast.svg',
                      width: 14,
                      height: 14,
                      fit: BoxFit.cover,
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    Text(
                      state.cableLabelToast,
                      style: TextStyle(color: ThemeColor.COLOR_FF9900, fontSize: 13, fontWeight: FontWeight.w400),
                    )
                  ],
                ),
              )
            : Container()
      ],
    );
  }

  jumpLabelDetailPage(
    String labelInfo,
  ) {
    if (state.templateData == null) {
      return;
    }
    Item item = Item.fromJson(state.templateData!);
    item.rawJson = state.templateData!;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelDetailsPage(item,source: 2);
      },
    ).then((value) {});
  }
}
