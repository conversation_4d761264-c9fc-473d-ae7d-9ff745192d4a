package melon.south.com.templatelibrary.activity

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Rect
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.lifecycle.ViewModelProvider
import com.blankj.utilcode.util.LogUtils
import com.gengcon.print.draw.util.PrintLibraryUtils
import com.google.zxing.BarcodeFormat
import com.google.zxing.Result
import com.google.zxing.client.android.DecodeFormatManager
import com.google.zxing.client.android.Intents
import com.huawei.hms.hmsscankit.RemoteView
import com.huawei.hms.ml.scan.HmsScan
import com.journeyapps.barcodescanner.ScanOptions
import com.luck.picture.lib.basic.PictureSelector
import com.luck.picture.lib.config.PictureConfig
import com.luck.picture.lib.config.SelectMimeType
import com.luck.picture.lib.config.SelectModeConfig
import com.niimbot.appframework_library.BaseActivity
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.visible
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.cloudprint.scan.launchScanResult
import com.niimbot.cloudprint.scan.utils.CustomImageEngine
import com.niimbot.cloudprint.scan.utils.ImageScanUtil
import com.niimbot.cloudprint.scan.utils.ScanImageFileCropEngine
import com.niimbot.fastjson.JSONObject
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.utiliylibray.util.any2Json
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.util.StringUtil
import melon.south.com.baselibrary.util.showToast
import melon.south.com.templatelibrary.R
import melon.south.com.templatelibrary.activity.viewmodel.ActionType
import melon.south.com.templatelibrary.activity.viewmodel.ErrorData
import melon.south.com.templatelibrary.activity.viewmodel.ScanResultData
import melon.south.com.templatelibrary.activity.viewmodel.ScanViewModel
import melon.south.com.templatelibrary.databinding.ActivityNewScanPageBinding
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import melon.south.com.templatelibrary.widgets.ToggleView
import org.greenrobot.eventbus.EventBus
import java.util.*

/**
 * @ClassName: NewScanActivity
 * @Author: Liuxiaowen
 * @Date: 2021/3/9 16:15
 * @Description:
 */
class NewScanActivity : BaseActivity() {
    private val binding by viewBinding(ActivityNewScanPageBinding::inflate)


    protected lateinit var selectedView: ToggleView

    private lateinit var scanViewModel: ScanViewModel

    private var fromChangeTemplate = false
    private var fromGoodRepo = false
    private var fromUnimp = false
    private var unimpScanType = 0
    private var unimpTips: String? = ""
    private var trackSource = 1
    private var scanType = 1
    var handleLogin = false
    var hasHandleOcrResult = false

    var formats = arrayListOf(
        ScanOptions.UPC_A,
        ScanOptions.UPC_E,
        ScanOptions.EAN_8,
        ScanOptions.EAN_13,
        ScanOptions.RSS_14,
        ScanOptions.CODE_39,
        ScanOptions.CODE_93,
        ScanOptions.CODE_128,
        "CODABAR",
        ScanOptions.ITF,
        ScanOptions.RSS_14,
        ScanOptions.RSS_EXPANDED,
        ScanOptions.QR_CODE,
        ScanOptions.DATA_MATRIX,
        ScanOptions.PDF_417,
        "AZTEC"
    )

    var qrFormats = arrayListOf(
        BarcodeFormat.QR_CODE,
        BarcodeFormat.DATA_MATRIX,
        BarcodeFormat.PDF_417,
        BarcodeFormat.AZTEC,
    )


    override fun onCreate(savedInstanceState: Bundle?) {
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        overridePendingTransition(
            com.niimbot.appframework_library.R.anim.slide_in_up,
            com.niimbot.appframework_library.R.anim.anim_page_stay
        )
        super.onCreate(savedInstanceState)
        initScanKit(savedInstanceState)
    }

    override fun bindLayout(): Int {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        return R.layout.activity_new_scan_page
    }

    override fun initView(savedInstanceState: Bundle?, contentView: View?) {
        initAll()
        scanViewModel = ViewModelProvider(this).get(ScanViewModel::class.java)
        scanViewModel.dataSource.observe(this) {
//            remoteView?.resumeContinuouslyScan()
            it.data?.apply {
                when (it.actionType) {
                    ActionType.SHOW_ERROR      -> showErrorMsg(this as ErrorData)
                    ActionType.GET_SCAN_RESULT -> handleQueryResult(this as ScanResultData)
                    ActionType.GET_OCR_RESULT  -> handleOcrResult(this as TemplateModule)
                    ActionType.SCAN_LOGIN_RESULT -> handleScanLoginConfirm(this as String)
                }
            }

        }
        scanViewModel.fromUniapp = fromUnimp
    }

    override fun doBusiness() {
    }

    var remoteView: RemoteView? = null
    private fun initScanKit(savedInstanceState:Bundle?) {
        // 绑定相机预览布局
        // 设置扫码识别区域，您可以按照需求调整参数
        val dm = getResources().getDisplayMetrics()
        val density = dm.density
        val mScreenWidth = getResources().getDisplayMetrics().widthPixels
        val mScreenHeight = getResources().getDisplayMetrics().heightPixels
        // 当前Demo扫码框的宽高是300dp
        val SCAN_FRAME_SIZE = 300
        val scanFrameSize = (SCAN_FRAME_SIZE * density).toInt()
        val rect = Rect()
        rect.left = (mScreenWidth / 2 - scanFrameSize / 2).toInt()
        rect.right = (mScreenWidth / 2 + scanFrameSize / 2).toInt()
        rect.top = (mScreenHeight / 2 - scanFrameSize / 2).toInt()
        rect.bottom = (mScreenHeight / 2 + scanFrameSize / 2).toInt()
        // 初始化RemoteView，并通过如下方法设置参数:setContext()（必选）传入context、setBoundingBox()设置扫描区域、setFormat()设置识别码制式，设置完毕调用build()方法完成创建。通过setContinuouslyScan（可选）方法设置非连续扫码
        remoteView =  RemoteView.Builder()
            .setContext(this)
            .setBoundingBox(rect)
            .setContinuouslyScan(true)
            .setFormat(HmsScan.ALL_SCAN_TYPE).build()
        // 将自定义view加载到activity的frameLayout中
        remoteView?.onCreate(savedInstanceState)
        val params = FrameLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT)
        binding.scanRoot.addView(remoteView, 0, params)

        remoteView?.setOnResultCallback {hmsScans ->
            if (hmsScans != null && hmsScans.isNotEmpty()) {
                //展示扫码结果
                Log.e("========","华为扫描结果： ${any2Json(hmsScans)}")
                val result = hmsScans?.getOrNull(0)?.let {
                    Result(it.originalValue, it.getOriginValueByte(), null, melon.south.com.baselibrary.util.ImageScanUtil.getScanTypeFromHMSScan(it.getScanType()))
                }
                processScanResult(result)
            }
        }
    }

    private fun initAll() {
        fromChangeTemplate = intent.getBooleanExtra("from_change_template", false)
        fromGoodRepo = intent.getBooleanExtra("from_goods_repo", false)
        fromUnimp = intent.getBooleanExtra("fromUnimp", false)
        unimpScanType = intent.getIntExtra("scanType", 0)
        unimpTips = intent.getStringExtra("customTips")
        trackSource = intent.getIntExtra("trackSource", 1)
        setListener()
        checkLanguage(!fromGoodRepo && !fromChangeTemplate)
        checkPermission()
    }

    private fun checkPermission() {
//        if (checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
//            requestPermission(Manifest.permission.CAMERA){
//                if (it) barcodeScannerView?.resume()
//            }
//        }
//        if (PermissionDialogUtils.showPermissionSetting(this, Manifest.permission.CAMERA)) {
            PermissionDialogUtils.showCameraPermissionDialog(
                this,
                RequestCode.MORE,
                object : XPermissionUtils.OnPermissionListener {
                    override fun onPermissionGranted() {
                        remoteView?.onStart()
                        remoteView?.onResume()
                    }

                    override fun onPermissionDenied(
                        deniedPermissions: Array<String>,
                        alwaysDenied: Boolean
                    ) {
                        showToast("app01309")
                    }
                })
//        }
    }

    private fun checkLanguage(showTabs: Boolean) {

        var toFlutter = intent.getBooleanExtra("flutterCallback", false)
        if(toFlutter){
            selectedView = binding.tvAuto
            modeSelect(binding.tvOcr, if (fromGoodRepo) "app01288" else "app01330")
            binding.modeGroup.visible(false)
            return
        }

        if (fromUnimp) {
            if (unimpScanType == 0) {
                selectedView = binding.tvScan
                val tips = if (unimpTips.isNullOrBlank()) "请扫描卷烟条码进行打印" else unimpTips!!
                modeSelect(binding.tvScan, tips)
            } else {
                selectedView = binding.tvOcr
                val tips = if (unimpTips.isNullOrBlank()) "app01330" else unimpTips!!
                modeSelect(binding.tvOcr, tips)
            }
        } else if (showTabs) {
            selectedView = binding.tvOcr
            modeSelect(binding.tvAuto, "app01288")
        } else {
            selectedView = binding.tvAuto
            modeSelect(binding.tvOcr, if (fromGoodRepo) "app01288" else "app01330")
        }

        binding.modeGroup.visible(showTabs && !fromUnimp)
    }


    private fun setListener() {

        binding.ibClose.setOnNotDoubleClickListener {
                onBackPressed()
        }

        binding.tvAuto.setOnNotDoubleClickListener {
            modeSelect(binding.tvAuto, "app01288")
            BuriedHelper.trackEvent("click", "045_116", hashMapOf(Pair("b_name", "自动识别")))
        }
        binding.tvOcr.setOnNotDoubleClickListener {
            modeSelect(binding.tvOcr, "app01330")
            BuriedHelper.trackEvent("click", "045_116", hashMapOf(Pair("b_name", "识标签")))
        }
        binding.tvScan.setOnNotDoubleClickListener {
//            //扫描商品，未登陆时提示登陆
//            if (!com.niimbot.baselibrary.user.LoginDataEnum.isLogin) {
//                barcodeScannerView?.pause()
//                showLoginDialog()
//            } else {
//                modeSelect(binding.tvScan, "app01100")
//            }
            modeSelect(binding.tvScan, "app01100")
            BuriedHelper.trackEvent("click", "045_116", hashMapOf(Pair("b_name", "扫商品")))
        }

        binding.ivCameraScanLight.setOnNotDoubleClickListener {
            if (remoteView?.lightStatus == true) {
                remoteView?.switchLight()
            } else {
                remoteView?.switchLight()
            }
            binding.ivCameraScanLight?.setImageResource(if(remoteView?.lightStatus == true) R.drawable.ic_flash_on else R.drawable.ic_flash_off)
        }

        binding.ivGallery.setOnNotDoubleClickListener {
            selectPicFromLocal()
        }
    }


    private fun modeSelect(view: ToggleView, tips: String) {
        selectedView.setCheck(false)
        view.setCheck(true)
        selectedView = view
        binding.tvText.text = tips
    }


    override fun onStart() {
        super.onStart()
        if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            remoteView?.onStart()
        }
    }

    override fun onResume() {
        super.onResume()
        if(handleLogin){
            handleLogin = false
            MainScope().launch {
                delay(500)
                if(!LoginDataEnum.isLogin && checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED){
                    remoteView?.onResume()
                    remoteView?.resumeContinuouslyScan()
                }
            }
            return
        }
        if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            remoteView?.onResume()
//            remoteView?.resumeContinuouslyScan()
        }

        binding.ivCameraScanLight?.setImageResource(if(remoteView?.lightStatus == true) R.drawable.ic_flash_on else R.drawable.ic_flash_off)
    }


    override fun onPause() {
        super.onPause()
        if (checkSelfPermission(Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
            remoteView?.onPause()
        }
      //  dismissLoading()
    }


    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        return super.onKeyDown(keyCode, event)
    }

    private fun handleQueryResult(scanResultData: ScanResultData) {
        dismissLoading()
        if (fromUnimp) {
            //ionic小程序
            if (trackSource == 5) {
                kotlinx.coroutines.MainScope().launch {
                    track(1, result = if(scanResultData.getGoods)  1 else 0, commodityBarCode = scanResultData.applyGoodsInfo.one_code)
                    onBackPressed()
                    kotlinx.coroutines.delay(200)
                    val result = JSONObject()
                    result.put("detail", JSONObject().apply {
                        put("barcode", scanResultData.applyGoodsInfo.one_code)
                        put("priceOfficer", scanResultData.applyGoodsInfo.price_officer)
                        put("name", scanResultData.applyGoodsInfo.goods_name)
                        put("retailPrice", scanResultData.applyGoodsInfo.retail_price)
                        put("norm", scanResultData.applyGoodsInfo.norm)
                        put("originPlace", scanResultData.applyGoodsInfo.origin)
                        put("level", scanResultData.applyGoodsInfo.level)
                        put("rushPrice", scanResultData.applyGoodsInfo.rush_price)
                        put("unit", scanResultData.applyGoodsInfo.unit)
                    })
                    result.put("type", "commodity")
                    EventBus.getDefault().post(JSONObject().apply {
                        put("action", "scanForIonic")
                        put("data", result)
                    }.toJSONString())
                }
            }
        } else {
            if (intent.getBooleanExtra("flutterCallback", false)) {
                val intent = Intent()
                intent.putExtra("value", scanResultData.applyGoodsInfo.one_code)
                intent.putExtra("source", 3)
                intent.putExtra("type", scanType)
                intent.putExtra("s_type", 2)
                setResult(Activity.RESULT_OK, intent)
                super.finish()
            } else {
                if (null != scanResultData.niimbotDrawData) {
                    if(hasHandleOcrResult){
                        return
                    }
                    hasHandleOcrResult = true
                    GlobalScope.launch {
                        scanResultData.niimbotDrawData.niimbotTemplate = TemplateSyncLocalUtils.writeModuleImgV2(scanResultData.niimbotDrawData.niimbotTemplate)
                        withContext(Dispatchers.Main){
                            SyncTemplatePresenter.startNiimbotDrawActivity(
                                this@NewScanActivity,
                                scanResultData.niimbotDrawData,
                                true
                            )
                        }

                    }


                } else {
                    TemplateScanResultActivity.startActivity(
                        this,
                        scanResultData.goodsCode,
                        scanResultData.applyGoodsInfo
                    )
                    onBackPressed()
                }
                track(1, result = if(scanResultData.getGoods)  1 else 0, commodityBarCode = scanResultData.goodsCode)
            }


        }
    }

    private fun handleOcrResult(ocrResultData: TemplateModule) {
        if(hasHandleOcrResult){
            return
        }
        hasHandleOcrResult = true
        dismissLoading()
        ocrResultData.let {
            if (fromUnimp) {
                track(2, result = 1)
                if (trackSource == 5) {
                    val result = JSONObject()
                    result.put("detail", com.niimbot.utiliylibray.util.any2Json(it.toTemplateModuleLocal()))
                    result.put("type", "label")
                    EventBus.getDefault().post(JSONObject().apply {
                        put("action", "scanForIonic")
                        put("data", result)
                    }.toJSONString())
                }
            } else if (intent.getBooleanExtra("flutterCallback", false)) {
                val intent = Intent()
                intent.putExtra("value",ocrResultData.one_code)
                intent.putExtra("source", 3)
                intent.putExtra("type", scanType)
                intent.putExtra("s_type", 2)
                setResult(Activity.RESULT_OK, intent)
                finish()
            } else {
                if (intent.getBooleanExtra("needTemplateIdCallback", false)) {
                    intent.putExtra("ocrTemplateId", it.id)
                    intent.putExtra("type", scanType)
                    setResult(0x1111, intent)
                } else {
                    track(2, result = 1)
//                    LeMessageManager.getInstance().dispatchMessage(
//                        LeMessage(
//                            LeMessageIds.MSG_ACTION_GO_ACTIVITY,
//                            TemplateDetailsForRecommActivityConfig(this).apply {
//                                this.intent
//                                    .putExtra("templateId", it.id)
//                                    .putExtra("isTemplateLabel", it.isLabel)
//                                    .putExtra("pageSource", "首页")
//                            }
//                        )
//                    )
//                    EventBus.getDefault().postSticky(
//                        LargeTemplateSendEvent(
//                            TAG_TEMPLATE_DETAIL_RECOMM_DATA,
//                            Gson().toJson(it)
//                        )
//                    )
                    val paramsMap = HashMap<String, Any>()
                    val data = any2Json(it.toTemplateModuleLocal())
                    paramsMap["jsonData"] = data
                    paramsMap["isFromScan"] = true
                    NiimbotGlobal.gotoFlutterPage("labelDetailPage", paramsMap)
                }
            }
        }
        onBackPressed()
    }

    /**
     * authResult:格式为 “client&&code”
     */
    private fun handleScanLoginConfirm(authResult: String){
        MainScope().launch {
            val paramsMap = HashMap<String, Any>()
            val authList = authResult.split("&&")
            if (authList.size != 2) return@launch
            paramsMap["client"] = authList[0]
            paramsMap["scanCode"] = authList[1]
            paramsMap["scanCodeLoginPage"] = true
            NiimbotGlobal.gotoFlutterPage("scanCodeLogin", paramsMap)
            //马上finish左上角会闪过一个loading加载框，所以延时100ms
            delay(100)
            finish()
        }
    }

    private fun showErrorMsg(error: ErrorData) {
        dismissLoading()
        if(error.code==5){
            remoteView?.onResume()
            remoteView?.resumeContinuouslyScan()
        }else{
            showToast(error.msg)
            when(error.code) {
                1 -> track(2, 0)
                2 -> track(1, 0)
            }
            MainScope().launch {
                delay(1500)
                onBackPressed()
            }
        }

    }

    private fun track(searchType: Int, result: Int, commodityBarCode: String? = "") {
        val map = hashMapOf<String, Any>()
        map["source"] = trackSource
        map["type"] = scanType
        map["s_type"] = searchType
        map["result"] = result
        if (!commodityBarCode.isNullOrBlank()) {
            map["commodity_barcode"] = commodityBarCode
        }
        BuriedHelper.trackEvent("show", "002_003_178", map)
    }


    /**
     * 从图库获取图片
     */
    private fun selectPicFromLocal() {
        PermissionDialogUtils.showGalleryPermissionDialog(this, RequestCode.MORE,
            object : XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    PictureSelector.create(this@NewScanActivity)
                        .openGallery(SelectMimeType.ofImage())
                        .setMaxSelectNum(1)
                        .setSelectionMode(SelectModeConfig.SINGLE)
                        .setImageEngine(CustomImageEngine.createGlideEngine())
                        .setCropEngine(ScanImageFileCropEngine())
                        .setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED)
                        .setLanguage(TextHookUtil.getInstance().getPictureSelectorLanguage())
                        .forResult(PictureConfig.CHOOSE_REQUEST)
                }

                override fun onPermissionDenied(
                    deniedPermissions: Array<out String>?,
                    alwaysDenied: Boolean
                ) {
                    com.niimbot.appframework_library.utils.showToast("app01370")
                }

            })
    }

    private fun handleParseImage(data: Intent) {
        val localMedia = PictureSelector.obtainSelectorList(data).getOrNull(0)
        localMedia?.let {
            val compressImage = PrintLibraryUtils.getImageFromChoose(
                media = it,
                needCompress = false, toGray = false)
            //设置显示照片
            scanBitmap(compressImage?.bitmap)
        }
    }

    private fun scanBitmap(sourceBitmap: Bitmap?) {
        sourceBitmap?.let { bitmap ->
            GlobalScope.launch {
                MainScope().launch { showLoading() }
                LogUtils.e("图片解码start to decode")
                var result = ImageScanUtil.handleQrcodeFromBitmap(
                    this@NewScanActivity,
                    bitmap,
                    DecodeFormatManager.parseDecodeFormats(Intent().apply {
                        putExtra(Intents.Scan.FORMATS, formats.joinToString(","))
                    })
                )
                LogUtils.e("图片解码end decode")
                LogUtils.e("图片解码结果： ${any2Json(result)}")
                processScanResult(result)
            }
        }
    }

    private fun processScanResult(result: Result?) {
        if (result == null || result.text == null) {
            MainScope().launch {
                dismissLoading()
                remoteView?.resumeContinuouslyScan()
            }
            showToast("app100000049")
            return
        }
        if(unimpScanType == 2){
            //小程序得到原始扫码结果
            val jsonResult = JSONObject()
            jsonResult["detail"] = result?.text
            jsonResult["type"] = "scanResult"
            EventBus.getDefault().post(JSONObject().apply {
                put("action", "scanForIonic")
                put("data", jsonResult)
            }.toJSONString())
            onBackPressed()
            return
        }
        remoteView?.pauseContinuouslyScan()
        MainScope().launch { dismissLoading() }
        result.let {
            if (intent.getBooleanExtra("flutterCallback", false)) {
                if(intent.getBooleanExtra("justCallbackScanCode", false)) {
                    val intent = Intent()
                    intent.putExtra("value", it.text)
                    intent.putExtra("source", 3)
                    intent.putExtra("type", scanType)
                    intent.putExtra("s_type", 2)
                    if (result.barcodeFormat == BarcodeFormat.EAN_13) {
                        intent.putExtra("commodity_barcode", it.text)
                    }
                    setResult(Activity.RESULT_OK, intent)
                    super.finish()
                    return@let
                }
            }
            val loginCode = getLoginCode(it.text)
            if(!loginCode.isNullOrEmpty()){
                //扫描PC端二维码，认证后PC端快捷登录
                scanViewModel.processQrCodeLogin(client = loginCode[0], loginCode = loginCode[1], activity = this@NewScanActivity)
                return@let
            }
            val guziInfo = getGuziInfo(it.text)
            if(guziInfo.isNotEmpty()){
                //识别到固资相关信息，打开固资页面
                super.finish()
                val openGuziEvent = JSONObject()
                openGuziEvent.put("action", "openGuziPage")
                openGuziEvent.put("params", guziInfo)
                openGuziEvent.put("url", NiimbotGlobal.guziBaseUrl)
                EventBus.getDefault().post(openGuziEvent.toJSONString())
                return@let
            }
            if (qrFormats.contains(result.barcodeFormat)) scanType = 2
            when {
                StringUtil.isUrl(it.text) -> {
                    track(3, result = 1)
                    NiimbotGlobal.routingByScheme(this@NewScanActivity, it.text)
                }

                StringUtil.isNumer(it.text) -> {
                    if (fromGoodRepo) {
                        MainScope().launch { dismissLoading() }
                        intent.putExtra("barCode", it.text)
                        intent.putExtra("type", scanType)
                        intent.putExtra("s_type", 1)
                        setResult(0x1111, intent)
                        onBackPressed()
                    } else {
                        scanViewModel.checkScanResult(selectedView.id, it,this@NewScanActivity)
                    }
                }

                else -> {
                    if (intent.getBooleanExtra("flutterCallback", false)) {
                        val intent = Intent()
                        intent.putExtra("value",it.text)
                        intent.putExtra("source", 3)
                        intent.putExtra("type", scanType)
                        intent.putExtra("s_type", 2)
                        if (result.barcodeFormat == BarcodeFormat.EAN_13) {
                            intent.putExtra("commodity_barcode", it.text)
                        }
                        setResult(Activity.RESULT_OK, intent)
                        super.finish()
                    }else{
                        launchScanResult(this@NewScanActivity, it.text, it.barcodeFormat.ordinal)
                    }
                }
            }
        }
    }

    private fun getLoginCode(content: String): List<String>?{
        //云打印PC扫码登录
        if((content.startsWith("http://") || content.startsWith("https://")) && content.contains("niimbotScan?code=")){
            val splitArray = content.split("niimbotScan?code=")
            if(splitArray.size != 2){
                return null
            }
            return splitArray.mapIndexed { index, s ->  if(index == 0) "pc" else s }
        }
        //固资扫码登录
        if (content.startsWith("client=") && content.contains("authCode=")) {
            val splitArray = content.replace("client=", "").replace("authCode=", "").trim().split("&&")
            if(splitArray.size != 2){
                return null
            }
            return splitArray
        }
        return null
    }

    private fun getGuziInfo(content: String): Map<String, String> {
        //固资扫码登录
        if (content.startsWith("gz-asset:assetId=") || content.startsWith("gz-invite:company=")) {
            return parseAsset(content);
        }
        return emptyMap()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == PictureConfig.CHOOSE_REQUEST
            && resultCode == Activity.RESULT_OK
            && data != null
        ) {
            remoteView?.pauseContinuouslyScan()
            handleParseImage(data)
        }
    }

    override fun onBackPressed() {
        finishAfterTransition()
        overridePendingTransition(-1, com.niimbot.appframework_library.R.anim.slide_out_down)
    }

    override fun onRequestPermissionsResult(
        requestCode: kotlin.Int,
        permissions: kotlin.Array<out kotlin.String>,
        grantResults: kotlin.IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        permissions.forEach {
            PermissionDialogUtils.confirmHasRequestPermission(it)
        }
        if (!XPermissionUtils.hasGrantedPermission(Manifest.permission.CAMERA)) {
            showToast("app01309")
        }
    }
    override fun onDestroy() {
        GlobalLoadingHelper.dismissLoading()
        super.onDestroy()
        remoteView?.onDestroy()
    }

    override fun onStop() {
        super.onStop()
        remoteView?.onStop()
    }

    fun parseAsset(input: String): Map<String, String> {
        val regex = "([a-zA-Z0-9-]+):([a-zA-Z0-9]+)=([a-zA-Z0-9]+)(?:&([a-zA-Z0-9]+)=([a-zA-Z0-9]+))?".toRegex()
        val matchResult = regex.find(input)
        return if (matchResult != null) {
            val groups = matchResult.groupValues
            val map = mutableMapOf("type" to groups[1], groups[2] to groups[3])
            if (groups.size > 4 && groups[4].isNotEmpty()) {
                map[groups[4]] = groups[5]
            }
            map
        } else {
            emptyMap()
        }
    }
}
