////
////  XYTabBarController.m
////  Runner
////
////  Created by <PERSON><PERSON><PERSON><PERSON> on 16/4/14.
////  Copyright © 2016年 xiaoyao. All rights reserved.
////
//
#import "XYTabBarController.h"
#import <SDWebImage/UIButton+WebCache.h>
#import "JCNewHomeViewController.h"
#import "JCShopMallsViewController.h"
#import "JCLabelCustomizeViewController.h"
#import "JCSettingViewController.h"
#import "JCLabelApplicationViewController.h"
#import "JCFlutter2NativeHandler.h"
#import <flutter_boost/FlutterBoost.h>
#import <SSZipArchive.h>
#import "JCADShopModel.h"
#import "JCShareTool.h"
#import "JCTabBar+Badge.h"
#import "FlutterBoostUtility.h"
#import "JCTemplateImageManager.h"
#import "JCTemplateDBManager.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JCGrayManager.h"
#import "JCAppMethodChannel.h"

#pragma mark -
#pragma mark - Life Cycle
@interface XYTabBarController ()<UITabBarControllerDelegate,JCTabBarControllerDelegate>
@property(nonatomic,strong) UIColor *itemTextColorNormal;
@property(nonatomic,strong) UIColor *itemTextColorSelect;
@property(nonatomic,strong) UIColor *itemBadgeColor;
@property(nonatomic,strong) UIColor *itemBadgeTextColor;
@property(nonatomic,strong) NSArray *itemImageArr;
@property(nonatomic, copy) NSString *tabShowNum;
@end

@implementation XYTabBarController

BOOL isCanCreate = YES;

- (void)viewDidLoad {
    [super viewDidLoad];
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    appDelegate.mainVC = self;
    //self.tabBar.shadowImage = [[UIImage alloc] init];
    self.tabBar.backgroundImage = [UIImage imageWithColor:HEX_RGB(0xFAFAFA)];
    if (@available(iOS 15.0, *)) {
       self.tabBar.backgroundColor = HEX_RGB(0xFAFAFA);
    }
    //透明设置为NO，显示白色，view的高度到tabbar顶部截止，YES的话到底部
    self.jcTabbar.translucent = NO;
    self.jcTabbar.centerOffsetY = 5;
    if(self.itemImageArr.count > 0){
        NSString *filePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
        NSString *normalImagePath = [NSString stringWithFormat:@"%@/%@",filePath,self.itemImageArr[0][2]];
        UIImage *normalImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:normalImagePath]];
        NSString *selectfilePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
        NSString *selectImagePath = [NSString stringWithFormat:@"%@/%@",selectfilePath,self.itemImageArr[1][2]];
        UIImage *selectImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:selectImagePath]];
        [self.jcTabbar.centerBtn setImage:normalImage forState:UIControlStateNormal];
        [self.jcTabbar.centerBtn setImage:selectImage forState:UIControlStateSelected];
    }else{
        [self.jcTabbar.centerBtn setImage:XY_IMAGE_NAMED(@"首页tabbar新建") forState:UIControlStateNormal];
        [self.jcTabbar.centerBtn setImage:XY_IMAGE_NAMED(@"首页tabbar新建") forState:UIControlStateSelected];
    }
    JCNCAddOb(self, @selector(changeLanguageRefresh), JCNOTICATION_ChANGELANGUAGE, nil);
    self.jcDelegate = self;
    [self initThemeInfo];
    [self addChildViewControllers];
    [self refreshTabbarTheme];
    [self addObserver:self forKeyPath:@"self.tabBar.hidden" options:NSKeyValueObservingOptionNew context:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(firmUpdateNotification:) name:JCFirmUpdateNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appNewNotification:) name:JCAppNewNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED_SHOP object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(shopMallNewMsgNotification:) name:JCNOTICATION_SHOP_MALL_MSG_SHOW object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshTabbarTheme) name:JCNOTICATION_REFRESH_APP_THEME object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshMindTabbarBadge:) name:JCNOTICATION_MIND_TABBAR_BADGE object:nil];
    [self configBottomBarBg];
    [self addCenterBtnShadow];
}

-(void)addCenterBtnShadow{
    self.jcTabbar.centerBtn.layer.shadowColor = HEX_RGB(0xFAFAFA).CGColor; // 阴影颜色
    //self.jcTabbar.centerBtn.layer.shadowOffset = CGSizeMake(0, 2); // 阴影偏移量
    self.jcTabbar.centerBtn.layer.shadowOpacity = 0.2; // 阴影透明度
    self.jcTabbar.centerBtn.layer.shadowRadius = 34; //
}

-(void)configBottomBarBg{

     if (@available(iOS 13.0, *)) {
         [self hideTopLine];
         if([self getTabBarBackImageUrl].length > 0){
             dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                 NSData * data = [[NSData alloc]initWithContentsOfURL:[NSURL URLWithString:[self getTabBarBackImageUrl]]];
                 UIImage *image = [[UIImage alloc]initWithData:data];
                 if (data != nil) {
                     [self refreshTabarAppearanceWithImage:image isNetworkImage:true];
                 }
             });
         }else{
             [self refreshTabarAppearanceWithImage:[UIImage imageWithColor:[UIColor whiteColor]] isNetworkImage:false];
         }
     }else{
         self.jcTabbar.backgroundColor = HEX_RGB(0xFAFAFA);
     }
}

-(void)refreshTabarAppearanceWithImage:(UIImage *)image isNetworkImage:(BOOL)isNetworkImage{
    if (@available(iOS 13.0, *)) {

        dispatch_async(dispatch_get_main_queue(), ^{
            UITabBarAppearance *appearance = isNetworkImage ? [[UITabBarAppearance alloc]init] : self.tabBarController.tabBar.standardAppearance;
            [appearance setBackgroundImage:image];
            appearance.shadowImage = [UIImage imageWithColor:[UIColor clearColor]];
            appearance.backgroundImageContentMode = UIViewContentModeScaleToFill;
            appearance.stackedLayoutAppearance.normal.titlePositionAdjustment = UIOffsetMake(0, -4);
            self.tabBar.standardAppearance = appearance;
            if (@available(iOS 15.0, *)) {
                self.jcTabbar.scrollEdgeAppearance = appearance;
            }
        });
    }
}

-(void)hideTopLine{
    [self.jcTabbar.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj isKindOfClass:NSClassFromString(@"_UIBarBackground")]) {
            [obj.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj2, NSUInteger idx, BOOL * _Nonnull stop) {
                       //[array addObject:obj2];
                if ([obj2 isKindOfClass:[UIVisualEffectView class]]) {
                    [obj2.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj3, NSUInteger idx, BOOL * _Nonnull stop) {
                        if ([obj3 isKindOfClass:NSClassFromString(@"_UIBarBackgroundShadowContentImageView")]) {
                            obj3.hidden = YES;
                        }
                    }];
                }
            }];
        }
    }];
}

-(NSString *)getTabBarBackImageUrl
{
    NSString * imageUrl = XY_LANGUAGE_TITLE_NAMED(@"app100001262", @"");
    BOOL isURL = [imageUrl hasPrefix:@"http://"] || [imageUrl hasPrefix:@"https://"];
    return isURL ? imageUrl : @"";
}

- (NSDictionary *)getThemeInfoWiththemeInfoDic:(NSDictionary *)themeInfo{
    NSString *filePath = [NSString stringWithFormat:@"%@/%@/source/theme_config.json",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
    NSString *jsonString = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:nil];
    return [jsonString xy_toDictionary];
}

- (void)initThemeInfo{
    NSString *filePath = [NSString stringWithFormat:@"%@/%@/source/theme_config.json",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
    NSString *jsonString = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:nil];
    NSDictionary *themeConfig = [jsonString xy_toDictionary];
    if(themeConfig.count > 0){
        self.itemTextColorNormal = [UIColor jk_colorWithHexString:themeConfig[@"color_main_tab_normal"]];
        self.itemTextColorSelect = [UIColor jk_colorWithHexString:themeConfig[@"color_main_tab_select"]];
//        self.itemBadgeColor = [UIColor jk_colorWithHexString:themeConfig[@"color_main_tab_bubble"]];
//        self.itemBadgeTextColor = [UIColor jk_colorWithHexString:themeConfig[@"color_main_tab_bubble_text"]];
        self.itemBadgeColor = COLOR_NEW_THEME;
        self.itemBadgeTextColor = COLOR_WHITE;
        NSMutableArray *normalImages = [NSMutableArray array];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_rb_home_normal"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_rb_innerapp_normal"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_rb_create_normal"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_rb_shop_normal"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_rb_usercenter_normal"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_home_industry"])];
        [normalImages addObject:UN_NIL(themeConfig[@"icon_home_scan"])];
        [normalImages addObject:UN_NIL(themeConfig[@"bg_home_rfid"])];
        [normalImages addObject:UN_NIL(themeConfig[@"bg_home_list"])];
        [normalImages addObject:UN_NIL(themeConfig[@"bg_home_top_operate"])];
        NSMutableArray *selectImages = [NSMutableArray array];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_rb_home_select"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_rb_innerapp_select"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_rb_create_select"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_rb_shop_select"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_rb_usercenter_select"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_home_industry"])];
        [selectImages addObject:UN_NIL(themeConfig[@"icon_home_scan"])];
        [selectImages addObject:UN_NIL(themeConfig[@"bg_home_rfid"])];
        [selectImages addObject:UN_NIL(themeConfig[@"bg_home_list"])];
        [selectImages addObject:UN_NIL(themeConfig[@"bg_home_top_operate"])];
        self.itemImageArr = @[normalImages,selectImages];
    }
}

//处理文字选中色为空的默认处理
- (void)setItemTextColorNormal:(UIColor *)itemTextColorNormal{
    if(itemTextColorNormal == nil){
        _itemTextColorNormal = HEX_RGB(0x595959);
    }else{
        _itemTextColorNormal = itemTextColorNormal;
    }
}

//处理文字普通色为空的默认处理
- (void)setItemTextColorSelect:(UIColor *)itemTextColorSelect{
    if(itemTextColorSelect == nil){
        _itemTextColorSelect = COLOR_NEW_THEME;
    }else{
        _itemTextColorSelect = itemTextColorSelect;
    }
}
- (void)downloadThemeSourceComplateBlock:(XYBlock)complateBlock themeInfoDic:(NSDictionary *)themeInfo{
    NSFileManager* fm=[NSFileManager defaultManager];
    NSString *appThemeSourcePath = [NSString stringWithFormat:@"%@/%@",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
    if(![fm fileExistsAtPath:appThemeSourcePath]){
        [fm createDirectoryAtPath:appThemeSourcePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    NSString *downloadPath = themeInfo[@"fileUrl"];
    NSString *fileName = downloadPath.lastPathComponent;
    NSString *saveUrlString = [NSString stringWithFormat:@"%@/%@",appThemeSourcePath,fileName];
    NSString *sourcePath = [NSString stringWithFormat:@"%@/source",appThemeSourcePath];
    if(![fm fileExistsAtPath:sourcePath]){
        [fm createDirectoryAtPath:sourcePath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    [@{} xy_downLoadFileWithUrlString:downloadPath savePath:saveUrlString tag:@"0" Success:^(__kindof YTKBaseRequest *request, id model) {
        BOOL success = [SSZipArchive unzipFileAtPath:saveUrlString toDestination:sourcePath progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {

        } completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
            complateBlock(@"1");
        }];
    } failure:^(NSString *msg, id model) {
        complateBlock(@"0");
    } downloadBlock:nil];
}

-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context{
    if([keyPath isEqualToString:@"self.tabBar.hidden"]){
        if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
            if(self.tabBar.hidden == YES && self.selectedIndex != 1){
                UINavigationController *naviVC = [self.viewControllers safeObjectAtIndex:self.selectedIndex];
                if([naviVC.visibleViewController isKindOfClass:[JCNewHomeViewController class]]){
                    self.tabBar.hidden = NO;
                }
            }
        }
    }
}

-(void)dealloc{
    [self removeObserver:self forKeyPath:@"self.tabBar.hidden"];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)dropShadowWithOffset:(CGSize)offset
                      radius:(CGFloat)radius
                       color:(UIColor *)color
                     opacity:(CGFloat)opacity {
    CGMutablePathRef path = CGPathCreateMutable();
    CGPathAddRect(path, NULL, self.tabBarController.tabBar.bounds);
    self.tabBarController.tabBar.layer.shadowPath = path;
    CGPathCloseSubpath(path);
    CGPathRelease(path);

    self.tabBar.layer.shadowColor = color.CGColor;
    self.tabBar.layer.shadowOffset = offset;
    self.tabBar.layer.shadowRadius = radius;
    self.tabBar.layer.shadowOpacity = opacity;

    self.tabBar.clipsToBounds = NO;
}

- (void)firmUpdateNotification:(NSNotification *)notification{
    id tabShowNum = notification.object;
    int index = 0;
    NSInteger itemCount = 0;
    if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
        index = 4;
        itemCount = 5;
    }else {
        if([[XYCenter sharedInstance] isShowShop]) {
            index = 3;
            itemCount = 4;
        }else {
            index = 2;
            itemCount = 3;
        }
    }
    if([JCBluetoothManager sharedInstance].needUpdateFirm){
        if ([JCAppEventChannel shareInstance].eventSink) {
            [JCAppEventChannel shareInstance].eventSink(@{@"printUpdateNotify":@"1"});
        }
        if ([self.tabShowNum intValue] == 0) {
            if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
                [self.jcTabbar showBadgeOnItemIndex:index itemCount:itemCount];
            }else {
                [self.jcTabbar showOtherBadgeOnItemIndex:index itemCount:itemCount language:XY_JC_LANGUAGE_REAL];
            }
        }

    }else{
        if ([JCAppEventChannel shareInstance].eventSink) {
            [JCAppEventChannel shareInstance].eventSink(@{@"printUpdateNotify":@"0"});
        }
        if ([self.tabShowNum intValue] == 0) {
            if([XY_JC_LANGUAGE_REAL isEqualToString:@"ar"]) {
                [self.jcTabbar removeArBadgeOnItem:index itemCount:itemCount];
            }else {
                [self.jcTabbar removeBadgeOnItem:index itemCount:itemCount];
            }

        }

    }
    [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
}

- (void)appNewNotification:(NSNotification *)notification{
    if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
        if(self.selectedIndex != 1 && [[XYCenter sharedInstance] shouldShowAppCenterBadge]){
            [self.jcTabbar showBadgeOnItemIndex:1 itemCount:5];
            [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
        }
    }
}

- (void)shopMallNewMsgNotification:(NSNotification *)notification{
    if([[XYCenter sharedInstance] isShowShop]){
        NSString *tabShowNum = notification.object;
        int index = [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? 3 : 2;
        NSInteger itemCount = [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? 5 : 4;
        if(tabShowNum.integerValue > 0){
            [self.jcTabbar showBadgeOnItemIndex:index itemCount:itemCount number:tabShowNum];
        }else{
            [self.jcTabbar removeBadgeOnItem:index itemCount:itemCount];
        }
        [XYCenter sharedInstance].userShopInfo.tab_show_num = tabShowNum;
        [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
    }
}


- (void)refreshMindTabbarBadge:(NSNotification *)notification{
    id tabShowNum = notification.object;
    int index = 0;
    NSInteger itemCount = 0;
    if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
        index = 4;
        itemCount = 5;
    }else {
        if([[XYCenter sharedInstance] isShowShop]) {
            index = 3;
            itemCount = 4;
        }else {
            index = 2;
            itemCount = 3;
        }
    }
    self.tabShowNum = tabShowNum;
    if([tabShowNum integerValue] > 0){
        //如果存在更新设备则将之前小红点移除
        if([JCBluetoothManager sharedInstance].needUpdateFirm) {
            if([XY_JC_LANGUAGE_REAL isEqualToString:@"ar"]) {
                [self.jcTabbar removeArBadgeOnItem:index itemCount:itemCount];
            }
        }
        if([XY_JC_LANGUAGE_REAL isEqualToString:@"ar"]) {
            [self.jcTabbar showBadgeWithArOnItemIndex:index itemCount:itemCount number:[tabShowNum stringValue]];
        }else if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
            [self.jcTabbar showBadgeOnItemIndex:index itemCount:itemCount number:[tabShowNum stringValue]];
        }else {
            [self.jcTabbar showBadgeWithOtherOnItemIndex:index itemCount:itemCount number:[tabShowNum stringValue]];
        }
    }else{
        [self.jcTabbar removeBadgeOnItem:index itemCount:itemCount];
        if([JCBluetoothManager sharedInstance].needUpdateFirm) {
            if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
                [self.jcTabbar showBadgeOnItemIndex:index itemCount:itemCount];
            }else {
                [self.jcTabbar showOtherBadgeOnItemIndex:index itemCount:itemCount language:XY_JC_LANGUAGE_REAL];
            }
        }

    }
   [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
}

- (void)refreshTabbarTheme{
    XYWeakSelf
    NSArray *appThemeInfos = [[NSUserDefaults standardUserDefaults] objectForKey:@"themeInfo"];
    if(appThemeInfos.count > 0){
        NSString *currentLangThemeVersion = [[NSUserDefaults standardUserDefaults] objectForKey:[NSString stringWithFormat:@"oldThemeVersion_%@",XY_JC_LANGUAGE_REAL]];
        BOOL isSupportCurrentLanguage = NO;
        for (NSDictionary *themeInfo in appThemeInfos) {
            if([themeInfo[@"languageCode"] isEqualToString:XY_JC_LANGUAGE_REAL]){
                BOOL themeEndValid = YES;
                BOOL themeStartValid = YES;
                NSString *serverVersion = themeInfo[@"version"];
                NSNumber *themeEndTime = themeInfo[@"endTimestamp"];
                NSNumber *themeStartTime = themeInfo[@"startTimestamp"];
                NSString *currentTime = [XYTool getCurrentTimesWithoutTSZ];
                NSDate *nowTime = [NSDate dateWithString:currentTime format:@"yyyy-MM-dd HH:mm:ss"];
                NSDate *endTime = [XYTool dateFromMsTimeStamp:themeEndTime.integerValue];
                NSDate *startTime = [XYTool dateFromMsTimeStamp:themeStartTime.integerValue];
                if([nowTime compare:endTime] == NSOrderedAscending){
                    themeEndValid = YES;
                }else{
                    themeEndValid = NO;
                    break;
                }
                if([nowTime compare:startTime] == NSOrderedAscending){
                    themeEndValid = NO;
                    break;
                }else{
                    themeEndValid = YES;
                }
                if(!STR_IS_NIL(currentLangThemeVersion) && [themeInfo[@"version"] isEqualToString:currentLangThemeVersion]){
                    [weakSelf initThemeInfo];
                    [weakSelf refreshTabbarItem];
                }else{
                    [self downloadThemeSourceComplateBlock:^(NSString *downloadResult){
                        if(downloadResult.integerValue == 1){
                            [[NSUserDefaults standardUserDefaults] setObject:serverVersion forKey:[NSString stringWithFormat:@"oldThemeVersion_%@",XY_JC_LANGUAGE_REAL]];
                        }
                        [weakSelf initThemeInfo];
                        [weakSelf refreshTabbarItem];
                        [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_REFRESH_THEME object:nil];
                    } themeInfoDic:themeInfo];
                }
                isSupportCurrentLanguage = YES;
                break;
            }
        }
        if(!isSupportCurrentLanguage){
            self.itemTextColorNormal = HEX_RGB(0x595959);
            self.itemTextColorSelect = COLOR_NEW_THEME;

            self.itemImageArr = @[];
            [self refreshTabbarItem];
        }
    }else{
        self.itemTextColorNormal = HEX_RGB(0x595959);
        self.itemTextColorSelect = COLOR_NEW_THEME;

        self.itemImageArr = @[];
        [self refreshTabbarItem];
    }
    self.itemBadgeColor = COLOR_NEW_THEME;
    self.itemBadgeTextColor = COLOR_WHITE;
}

- (NSArray *)getThemeImageArr{
    return self.itemImageArr;
}

- (void)changeLanguageRefresh{
    [self refreshTabbarTheme];
}

- (void)refreshTabbarItem{
    NSArray *currentViewControllers = self.viewControllers;
    NSMutableArray *arr1 = [NSMutableArray arrayWithArray:currentViewControllers];
    [self.jcTabbar removeAllBadge];
    // 根据个数判断当前展示的ViewControllers（非最佳方式，持有所有的子ViewController会比较方便，但是导致引用+1）
    if (currentViewControllers.count == 3) {
        // 补全所有的VC,后续根据业务进行移除
        // 插入应用
        [arr1 insertObject:self.myStoreVC atIndex:1];
        // 插入商城
        [arr1 insertObject:self.shopVC atIndex:3];
        UINavigationController *vc0 = arr1[0];
        vc0.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00296", @"首页");
        UINavigationController *vc2 = arr1[2];
        vc2.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建");
        self.shopVC.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00938", @"商城");
        UINavigationController *vc4 = arr1[4];
        vc4.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的");

    } else if (currentViewControllers.count == 4) {
        // 补全所有的VC,后续根据业务进行移除
        // 插入应用
        [arr1 insertObject:self.myStoreVC atIndex:1];
        UINavigationController *vc0 = arr1[0];
        vc0.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00296", @"首页");
        UINavigationController *vc2 = arr1[2];
        vc2.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建");
        self.shopVC.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00938", @"商城");
        UINavigationController *vc4 = arr1[4];
        vc4.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的");
    }
    if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
        // 移除应用
        [arr1 removeObjectAtIndex:1];
        if (![[XYCenter sharedInstance] isShowShop]) {
            // 移除商城
            [arr1 removeObjectAtIndex:2];
            self.shopVC.tabBarItem.title = @"";
            self.shopVC.tabBarItem.image = [UIImage new];
        } else {
            self.shopVC.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00938", @"商城");
        }
        UINavigationController *vc0 = arr1[0];
        vc0.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00296", @"首页");
        UINavigationController *vc1 = arr1[1];
        vc1.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建");
        // 展示商城则位于第3个否则第2个
        UINavigationController *vc3 = arr1[[[XYCenter sharedInstance] isShowShop] ? 3 : 2];
        vc3.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的");
    }else{
        if([[XYCenter sharedInstance] shouldShowAppCenterBadge]){
            [self.jcTabbar showBadgeOnItemIndex:1 itemCount:5];
        }else{
            [self.jcTabbar removeBadgeOnItem:1 itemCount:5];
        }
        if([XYCenter sharedInstance].userShopInfo.tab_show_num.integerValue > 0){
            [self.jcTabbar showBadgeOnItemIndex:3 itemCount:5 number:[XYCenter sharedInstance].userShopInfo.tab_show_num];
        }else{
            [self.jcTabbar removeBadgeOnItem:3 itemCount:5];
        }
        [self refreshTabbar:[XYCenter sharedInstance].shop_TabbarModel serviceType:[XYCenter sharedInstance].isServiceType];
    }
    if([JCBluetoothManager sharedInstance].needUpdateFirm){
        if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
            [self.jcTabbar showBadgeOnItemIndex:3 itemCount:4];
        }else{
            [self.jcTabbar showBadgeOnItemIndex:4 itemCount:5];
        }
    }else{
        if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
            [self.jcTabbar removeBadgeOnItem:2 itemCount:3];
        }else{
            [self.jcTabbar removeBadgeOnItem:4 itemCount:5];
        }
    }
    if(self.itemImageArr.count > 0){
        [self.jcTabbar.centerBackView setHidden:NO]; //切换语言会导致centerBackView隐藏，这里解决切换到简中新建按钮无法显示的问题
        for (NSInteger index = 0;index < arr1.count;index++) {
            UINavigationController *currentNav = arr1[index];
            UIViewController *vc = nil;
            if([currentNav isKindOfClass:[UINavigationController class]]){
                vc = [currentNav jk_rootViewController];
            }else{
                vc = currentNav;
            }
            NSInteger imageIndex = index;
            if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
                if(index == 1){
                    imageIndex = 2;
                }
                if(index == 2){
                    imageIndex = 4;
                }
            }
            NSString *filePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
            NSString *normalImagePath = [NSString stringWithFormat:@"%@/%@",filePath,self.itemImageArr[0][imageIndex]];
            UIImage *normalImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:normalImagePath]];
            NSString *selectImagePath = [NSString stringWithFormat:@"%@/%@",filePath,self.itemImageArr[1][imageIndex]];
            UIImage *selectImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:selectImagePath]];
            if([vc.tabBarItem.title isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建")]){
                selectImage = nil;
                normalImage = nil;
            }
            selectImage = [selectImage jk_imageScaledToSize:CGSizeMake(28, 28)];
            normalImage = [normalImage jk_imageScaledToSize:CGSizeMake(28, 28)];
            vc.tabBarItem.selectedImage = (selectImage == nil?[UIImage new]:[selectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
            vc.tabBarItem.image = (normalImage == nil?[UIImage new]:[normalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
            [self setTabbarItemFont:vc fontColor:self.itemTextColorSelect fontNormalColor:self.itemTextColorNormal fontSize:[self getFontSize]];
        }
        NSString *filePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
        NSString *normalImagePath = [NSString stringWithFormat:@"%@/%@",filePath,self.itemImageArr[0][2]];
        UIImage *normalImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:normalImagePath]];
        NSString *selectfilePath = [NSString stringWithFormat:@"%@/%@/source",RESOURCE_APP_THEME_SOURCE_PATH,XY_JC_LANGUAGE_REAL];
        NSString *selectImagePath = [NSString stringWithFormat:@"%@/%@",selectfilePath,self.itemImageArr[1][2]];
        UIImage *selectImage = [UIImage imageWithData:[NSData dataWithContentsOfFile:selectImagePath]];
        [self.jcTabbar.centerBtn setImage:normalImage forState:UIControlStateNormal];
        [self.jcTabbar.centerBtn setImage:selectImage forState:UIControlStateSelected];
        [self.jcTabbar refreshThemeLayerOut:YES];
        [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
    }else{
        NSArray *normalImages, *selectImages;
        // 是否展示海外新建
        BOOL isShowAbroadNew = NO;
        // 根据aar1的count数判断当前的展示tab
        if (arr1.count == 3) {
            // 首页、新建、我的
            normalImages = @[@"首页normal",@"",@"我的normal"];
            selectImages = @[@"首页selected",@"",@"我的selected"];
        } else if (arr1.count == 4) {
            // 首页、新建、商城、我的
            normalImages = @[@"首页normal",@"abroad_new",@"商城normal",@"我的normal"];
            selectImages = @[@"首页selected",@"abroad_new",@"商城selected",@"我的selected"];
            isShowAbroadNew = YES;
        } else {
            // 首页、应用、新建、商城、我的
            normalImages = @[@"首页normal",@"ic_inner_app_normal",@"",@"商城normal",@"我的normal"];
            selectImages = @[@"首页selected",@"ic_inner_app_selected",@"",@"商城selected",@"我的selected"];
        }

        for (NSInteger index = 0;index < arr1.count;index++) {
            UINavigationController *currentNav = arr1[index];
            UIViewController *vc = nil;
            if([currentNav isKindOfClass:[UINavigationController class]]){
                vc = [currentNav jk_rootViewController];
            }else{
                vc = currentNav;
            }
            UIImage *normalImage = XY_IMAGE_NAMED(normalImages[index]);;
            UIImage *selectImage = XY_IMAGE_NAMED(selectImages[index]);;
            vc.tabBarItem.selectedImage = (selectImage == nil? [UIImage new] : [selectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
            vc.tabBarItem.image = (normalImage == nil ? [UIImage new] : [normalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
            [self setTabbarItemFont:vc fontColor:self.itemTextColorSelect fontNormalColor:self.itemTextColorNormal fontSize:[self getFontSize]];
        }
        if (isShowAbroadNew) {
            // 新建隐藏
            [self.jcTabbar.centerBackView setHidden:YES];
        } else {
            [self.jcTabbar.centerBackView setHidden:NO];
            [self.jcTabbar.centerBtn setImage:XY_IMAGE_NAMED(@"首页tabbar新建") forState:UIControlStateNormal];
            [self.jcTabbar.centerBtn setImage:XY_IMAGE_NAMED(@"首页tabbar新建") forState:UIControlStateSelected];
            [self.jcTabbar refreshThemeLayerOut:NO];
            [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
        }
    }
    self.viewControllers = arr1;

    if ([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
        [[JCAppEventChannel shareInstance] eventData:@{
            @"shopState": @1,
            @"shopSiteCode": @"SC001",
        }];
    } else {
        [[JCAppEventChannel shareInstance] eventData:@{
            @"shopState": @([[XYCenter sharedInstance] isShowShop] ? 2 : 0),
            @"shopSiteCode": [[XYCenter sharedInstance] siteCode],
        }];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];

}

- (void)setItemImageArr:(NSArray *)itemImageArr{
    _itemImageArr = itemImageArr;
//    _itemImageArr = @[];
}
- (void)refreshTabbar:(JCADShop_TabbarModel *)tabbarModel serviceType:(BOOL)isServiceType{
    if(self.itemImageArr.count == 0){
        UIImage *normalImage = [UIImage imageNamed:@"商城normal"];
        self.shopVC.tabBarItem.image = (normalImage == nil?[UIImage new]:[normalImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
        UIImage *selectImage = [UIImage imageNamed:@"商城selected"];
        self.shopVC.tabBarItem.selectedImage =  (selectImage == nil?[UIImage new]:[selectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
        self.shopVC.tabBarItem.title = XY_LANGUAGE_TITLE_NAMED(@"app00938", @"商城");;
    }
}



//添加子控制器
- (void)addChildViewControllers{
    //图片大小建议32*32
    XYViewController *firstViewController = [[JCNewHomeViewController alloc] init];
    XYViewController *secondViewController = [[JCLabelApplicationViewController alloc] init];
    XYViewController *thirdViewController = [[XYViewController alloc] init];
    JCShopMallsViewController *forthViewController = [[JCShopMallsViewController alloc] init];
    forthViewController.jumpSource = @"y_page_main";
//    JCMeController *fifthViewController = [[JCMeController alloc] init];
    FBFlutterViewContainer *fifthViewController = [[FBFlutterViewContainer alloc] init];
           [fifthViewController setName:@"meProfile"
                                 uniqueId:[NSString jk_UUID]
                                   params:nil
                                   opaque:YES];
           [[JCFlutter2NativeHandler sharedInstance] constructionFlutterController:fifthViewController];
    if(self.itemImageArr.count > 0){
        [self addChildrenViewController:firstViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00296", @"首页") andImageName:self.itemImageArr[0][0] selectImage:self.itemImageArr[1][0]];
        [self addChildrenViewController:secondViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"应用") andImageName:self.itemImageArr[0][1] selectImage:self.itemImageArr[1][1]];
        [self addChildrenViewController:thirdViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建") andImageName:@"" selectImage:@""];
        [self addChildrenViewController:forthViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"商城") andImageName:self.itemImageArr[0][2] selectImage:self.itemImageArr[1][2]];
        [self addChildrenViewController:fifthViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的") andImageName:self.itemImageArr[0][3] selectImage:self.itemImageArr[1][3]];
    }else{
        [self addChildrenViewController:firstViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00296", @"首页") andImageName:@"首页normal" selectImage:@"首页selected"];
        [self addChildrenViewController:secondViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"应用") andImageName:@"ic_inner_app_normal" selectImage:@"ic_inner_app_selected"];
        [self addChildrenViewController:thirdViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建") andImageName:@"" selectImage:@""];
        [self addChildrenViewController:forthViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"", @"商城") andImageName:@"商城normal" selectImage:@"商城selected"];
        [self addChildrenViewController:fifthViewController andTitle:XY_LANGUAGE_TITLE_NAMED(@"app00838", @"我的") andImageName:@"我的normal" selectImage:@"我的selected"];
    }
    // 商城预加载
    [forthViewController preLoadWebView];
}

- (void)addChildrenViewController:(UIViewController *)childVC andTitle:(NSString *)title andImageName:(NSString *)imageName selectImage:(NSString *)selectImageName{
    UIImage *image = [UIImage imageNamed:imageName];
    childVC.tabBarItem.image = (image == nil?[UIImage new]:[image imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
    UIImage *selectImage = [UIImage imageNamed:selectImageName];
    childVC.tabBarItem.selectedImage =  (selectImage == nil?[UIImage new]:[selectImage imageWithRenderingMode:UIImageRenderingModeAlwaysOriginal]);
    childVC.tabBarItem.title = title;
    if (@available(ios 13.0,*))
    {
        childVC.tabBarItem.titlePositionAdjustment = UIOffsetMake(0, -4);
    }
    [self setTabbarItemFont:childVC fontColor:self.itemTextColorSelect fontNormalColor:self.itemTextColorNormal fontSize:[self getFontSize]];
    if(![title isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app00270", @"新建")]){
        XYNavigationController *baseNav = [[XYNavigationController alloc] initWithRootViewController:childVC];
        if([title isEqualToString:@"商城"]){
            self.shopVC = baseNav;
        }
        if([title isEqualToString:@"应用"]){
            self.myStoreVC = baseNav;
        }
        [self addChildViewController:baseNav];
    }else{
        [self addChildViewController:childVC];
    }
}

- (void)setTabbarItemFont:(UIViewController *)vc fontColor:(UIColor *)colorSelected fontNormalColor:(UIColor *)colorNormal fontSize:(NSUInteger)fontSize{
    if(colorNormal == nil) colorNormal = [UIColor blackColor];
    if(colorSelected == nil) colorSelected = COLOR_NEW_THEME;
    if (@available(iOS 13.0, *)) {
        UITabBarAppearance *appearance = [[UITabBarAppearance alloc]init];
        // 设置未被选中的颜色
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = @{NSForegroundColorAttributeName: colorNormal,NSFontAttributeName:MY_FONT_Regular(fontSize)};
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = @{NSForegroundColorAttributeName: colorSelected,NSFontAttributeName:MY_FONT_Regular(fontSize)};
        appearance.stackedLayoutAppearance.normal.titlePositionAdjustment = UIOffsetMake(0, -4);
        vc.tabBarItem.standardAppearance = appearance;
    }else{
        NSMutableDictionary *textAttrs = [NSMutableDictionary dictionary];
        textAttrs[NSForegroundColorAttributeName]= colorNormal;
        textAttrs[NSFontAttributeName]=MY_FONT_Regular(fontSize);
        [vc.tabBarItem setTitleTextAttributes:textAttrs forState:UIControlStateNormal];

        NSMutableDictionary *textAttrs1 = [NSMutableDictionary dictionary];
        textAttrs1[NSForegroundColorAttributeName]= colorSelected;
        textAttrs1[NSFontAttributeName]=MY_FONT_Regular(fontSize);
        [vc.tabBarItem setTitleTextAttributes:textAttrs1 forState:UIControlStateSelected];
    }
}

// 使用JCTabBarController 自定义的 选中代理
- (void)jcTabBarController:(UITabBarController *)tabBarController didSelectViewController:(UIViewController *)viewController{
    if([tabBarController.viewControllers indexOfObject:viewController] == 0){
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    }else{
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
    }
    if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
        self.isShopVc = NO;
    }else{
        if (tabBarController.selectedIndex == 3){
            self.isShopVc = YES;
        }else {
            self.isShopVc = NO;
        }
        if(tabBarController.selectedIndex != 1){
            if([[XYCenter sharedInstance] shouldShowAppCenterBadge]){
                [self.jcTabbar showBadgeOnItemIndex:1 itemCount:5];
            }else{
                [self.jcTabbar removeBadgeOnItem:1 itemCount:5];
            }
        }else{
            [[XYCenter sharedInstance] recordUpdateNewApp];
            [self.jcTabbar removeBadgeOnItem:1 itemCount:5];
        }
        [self.jcTabbar refreshBadgeColorWith:self.itemBadgeColor textColor:self.itemBadgeTextColor];
    }
}

-(BOOL)tabBarController:(UITabBarController *)tabBarController shouldSelectViewController:(UIViewController *)viewController{
    XYWeakSelf
    if([viewController isKindOfClass:[XYNavigationController class]]){
        XYNavigationController *naviVC = (XYNavigationController *)viewController;
        UIViewController *vc = naviVC.topViewController;
        if([vc isKindOfClass:[JCShopMallsViewController class]]){
            JC_TrackWithparms(@"click",@"002_001_004",(@{}));
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return NO;
            }
            if(!weakSelf.isShopVc){
                [JCRecordTool recordWithAction: click_ad_shop];
                JCShopMallsViewController *shopVC = (JCShopMallsViewController *)vc;
                [shopVC callH5Mothord];
            }
        }else if ([vc isKindOfClass:[JCLabelApplicationViewController class]]){
            JC_TrackWithparms(@"click",@"002_001_002",(@{}));
            [JCRecordTool recordWithAction:tabbar_silkbag_click withContent:@"tabbar_silkbag_click" isClickEvent:YES parmeters:@{}];
        }
        else if([vc isKindOfClass:[JCNewHomeViewController class]]){
            self.isShopVc = NO;
            JC_TrackWithparms(@"click",@"002_001_001",(@{}));
        }else if([vc isKindOfClass:[FBFlutterViewContainer class]]){
            self.isShopVc = NO;
            JC_TrackWithparms(@"click",@"002_001_005",(@{}));
        }

    }else{
        [self creatNewTemplate];
        return NO;
    }
    return YES;
}

- (void)checkLoginBeforeToShop{

}
- (void)creatNewTemplate {
    // 获取当前连接的标签纸信息
    __block JCTemplateData *model = [JCPrintManager sharedInstance].rfidTemplateData;
    if (model) {
        // 保存使用记录
        saveRecentlyUsedRecord(model.idStr);
        // 下载模版背景数据
      // 直接跳入原生画板
      JCTemplateData *labelToEditModel = model;
      labelToEditModel.profile.extrain.userId = @"";
      XYNormalBlock canvasBlock = ^(){
          // 画板灰度
          JCTemplateData *data = [JCTMDataBindGoodsInfoManager templateDataWith:labelToEditModel goodInfo:nil];
          data.name = XY_LANGUAGE_TITLE_NAMED(@"app100000728", @"未命名模板");
          // 异步获取RTL状态后创建默认文本元素
        [JCTemplateFunctionHelper getLayoutTemplate:data callback:^(JCTemplateData * layoutTemplate) {
          [JCFlutter2NativeHandler toFlutterCanvasWith:layoutTemplate type:TemplateSource_New];
        }];
      };
      if(!labelToEditModel.isCableLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
        // 是否是危废标签纸
        if (!labelToEditModel.isDangerLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
            canvasBlock();
         } else {
           [[JCLoginManager sharedInstance] checkLogin:^{
           } viewController:nil loginSuccessBlock:^{
             [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:UniAppTrackID_dangerCap needKeepLive:YES parms:nil receiveUniappData:^(id x) {

             }];
           }];
         }
      }else{
          NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
          NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
          if(STR_IS_NIL(canvasType) || [canvasType isEqualToString:@"cableCanvas"]){
              [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:@{@"jsonData": labelToEditModel.toJSONString,@"isPresent": @NO,@"fontPath": fontPath,
                                                                              @"isEnablePopGesture": @NO} onPageFinished:^(NSDictionary *dic) {

              }];
          }else{
              canvasBlock();
          }
      }
      JC_TrackWithparms(@"click",@"002_001_003",(@{@"type": @1}));
    } else {
        // 跳转到Flutter页选择
        if(!isCanCreate) {
            NSLog(@"卡屏调试:不可创建");
            return;
        }else {
            NSLog(@"卡屏调试:重新可创建");
        }
      // 判断当前选中的tab的导航栏里的rootVc是否为Flutter VC
      UINavigationController *currentNavVC =
          [self.viewControllers safeObjectAtIndex:self.selectedIndex];
      UIViewController *rootVC = [currentNavVC jk_rootViewController];
      BOOL isFlutterVC = [rootVC isKindOfClass:[FBFlutterViewContainer class]];

      if (isFlutterVC) {
        // 如果是Flutter VC，延迟0.3秒跳转
        dispatch_after(
            dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)),
            dispatch_get_main_queue(), ^{
              [FlutterBoostUtility gotoTransparentFlutterPage:@"createLabelHome"
                                                    arguments:@{
                                                      @"isPresent" : @YES,
                                                      @"isAnimated" : @NO,
                                                    }
                                               onPageFinished:^(NSDictionary *_){
                                                   // 页面结束回传数据
                                               }];
            });
      } else {
        // 其他情况直接跳转
        [FlutterBoostUtility gotoTransparentFlutterPage:@"createLabelHome"
                                              arguments:@{
                                                @"isPresent" : @YES,
                                                @"isAnimated" : @NO,
                                              }
                                         onPageFinished:^(NSDictionary *_){
                                             // 页面结束回传数据
                                         }];
      }

        JC_TrackWithparms(@"click",@"002_001_003",(@{@"type": @0}));
    }

    [JCRecordTool recordWithAction:ClickIndexCreateTemplate];
}

- (void)loginChanged:(NSNotification *)notification{
    [[XYCenter sharedInstance] getUserShopInfoSuccess:^(id x) {

    } failure:^(id x) {

    }];
}

- (void)resetStoreNavRootVC{

}

//旋转动画
- (void)rotationAnimation{
    if ([@"key" isEqualToString:[self.jcTabbar.centerBtn.layer animationKeys].firstObject]){
        return;
    }
    CABasicAnimation *rotationAnimation = [CABasicAnimation animationWithKeyPath:@"transform.rotation.z"];
    rotationAnimation.toValue = [NSNumber numberWithFloat:M_PI*2.0];
    rotationAnimation.duration = 3.0;
    rotationAnimation.repeatCount = HUGE;
    [self.jcTabbar.centerBtn.layer addAnimation:rotationAnimation forKey:@"key"];
}

//处理测试机上13.6tabbar字体显示不下，显示为...的问题
- (NSInteger)getFontSize {
    if (@available(iOS 13.0, *)) {
        if ([[UIDevice currentDevice].systemVersion floatValue] < 14.0) {
            // 当前系统版本在 iOS 13.0 和 iOS 14.0 之间
            return 9;
        }
    }
    // 当前系统版本不是在 iOS 13.0 和 iOS 14.0 之间，返回 11
    return 11;
}

@end
