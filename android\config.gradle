ext {
    config = [
            // 很多app都会有自己的打包开关，来区分线上包和测试包，可以通过给applyPlugin赋值来区分测试包和线上包
            MavenPublishURL :"http://10.10.13.12:8082/artifactory",
    ]
    android = [compileSdkVersion         : 35,
               buildToolsVersion         : "34.0.0",

               applicationId             : "com.gengcon.android.jccloudprinter",
               minSdkVersion             : 24,
               targetSdkVersion          : 35,
               versionCode               : 100,
               pluginVersionName         : "1.0.0",
               //jcenter 版本号 打包上传时版本名字
               jcenterArchivesVersionName: "1.0.0",
               //内部仓库版本号
               didiArchivesVersionName   : "1000.0.22",
               versionName               : "1.0.0",
               // 图像库版本号
               skiaVersionName           : "1.9.3-dev.7"
    ]


    //***************************************加密接口 start***********************************************//
    //  DokitOkGo接口配置加密的方式：
    //      --请求header增加参数即可：.headers("need_encrypt", "1")
    //  目前涉及到加密的接口有：
    //  1.获取打印策略：rfid/getRfidPrintStrategy
    //  2.获取RFID信息：rfid/getRfid
    //  3.获取用户信息：user/logged
    //  4.账号密码登陆：user/account/login
    //  5.获取VIP功能试用过期时间：vip/probation/end/time/v1
    //  6.获取字体列表：content/fontlib/list
    //  7.获取vip素材id集合：materialLib/get/vip-ids
    //  8.查询用户ocr使用次数: graphql
    //  9.素材列表：materialLib/page
    //  10.获取打印策略: graphql
    //***************************************加密接口 end*************************************************//
}
