import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:text/utils/image_utils.dart';

class LabelWebPage extends StatefulWidget {
  final String title;
  final String url;
  final String rightCloseText;
  final bool isFullScreen;
  const LabelWebPage(
      {Key? key,
      this.title = "",
      this.url = "",
      this.rightCloseText = '',
      this.isFullScreen = false})
      : super(key: key);

  @override
  _LabelWebPageState createState() => _LabelWebPageState();
}

class _LabelWebPageState extends State<LabelWebPage> {
  double? progress = 0.0;
  String webTitle = '';
  final Completer<WebViewController> _controller =
      Completer<WebViewController>();
  @override
  Widget build(BuildContext context) {
    if (widget.isFullScreen) {
      return Scaffold(
        appBar: _topWidget(),
        body: getContentWidget(),
      );
    } else {
      return getContentWidget();
    }
  }

  _topWidget() {
    return AppBar(
      centerTitle: true,
      backgroundColor: ThemeColor.background,
      elevation: 0,
      leading: InkWell(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 15),
            child: Image(
              image: ImageUtils.getAssetImage('pageback'),
              matchTextDirection: true,
            ),
          ),
          onTap: () {
            Navigator.of(context).pop();
          }),
      title: Text(
        widget.title.length > 0 ? widget.title : webTitle,
        style: TextStyle(
          fontSize: 17,
          color: ThemeColor.title,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget getContentWidget() {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.COLOR_F5F5F5,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      padding:
          EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(
        children: [
          Offstage(
            offstage: widget.isFullScreen,
            child: _titleBarWidget(),
          ),
          progress! >= 1
              ? Container()
              : LinearProgressIndicator(
                  value: progress,
                  minHeight: 2,
                  backgroundColor: ThemeColor.border,
                  valueColor: AlwaysStoppedAnimation(ThemeColor.brand),
                ),
          // Expanded(
          //   child: InAppWebView(
          //     initialUrlRequest: URLRequest(url: Uri.parse(widget.url)),
          //     onScrollChanged: (InAppWebViewController controller, int x, int y) {},
          //     onProgressChanged: (InAppWebViewController controller, int value) {
          //       progress = value / 100;
          //       setState(() {});
          //     },
          //   ),
          // ),
          Expanded(
            child: WebView(
              initialUrl: widget.url,
              javascriptMode: JavascriptMode.unrestricted,
              backgroundColor: Colors.white,
              onWebViewCreated: (WebViewController webViewController) {
                _controller.complete(webViewController);
              },
              // },
              onPageStarted: (String url) {
                // isLoading = true;
              },
              onPageFinished: (String url) {
                _getTitle();
                setState(() {
                  // isLoading = false; // 页面加载完成，更新状态
                });
              },
              onWebResourceError: (error) {
                setState(() {
                  // isLoading = false;
                  // isError = true;
                });
              },
              onProgress: (int value) {
                progress = value / 100;
                setState(() {
                  // isLoading = false; // 页面加载完成，更新状态
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  _getTitle() {
    _controller.future.then((value) {
      value.getTitle().then((title) {
        webTitle = title ?? "";
        setState(() {});
      });
    });
  }

  ///标题
  _titleBarWidget() {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      padding: EdgeInsets.symmetric(
          horizontal: widget.rightCloseText.length > 0 ? 17 : 12, vertical: 7),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                  maxWidth: MediaQuery.sizeOf(context).width - 200),
              child: Text(
                widget.title.length > 0 ? widget.title : webTitle,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
                style: const TextStyle(
                    color: ThemeColor.title,
                    fontSize: 17,
                    fontWeight: FontWeight.w600),
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.centerEnd,
            child: widget.rightCloseText.length > 0
                ? GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Text(
                      widget.rightCloseText,
                      style: const TextStyle(
                          color: ThemeColor.title,
                          fontSize: 16,
                          fontWeight: FontWeight.w600),
                    ),
                  )
                : PlaneButton(
                    width: 30,
                    height: 30,
                    child: const SvgIcon(
                        'assets/images/industry_template/replace_label/close_line.svg'),
                    onTap: () => Navigator.of(context).pop(),
                  ),
          ),
        ],
      ),
    );
  }
}
