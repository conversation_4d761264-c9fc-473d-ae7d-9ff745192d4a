/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "This app will access your camera for barcode scanning, text recognition, and photo capture. Allow camera access?";
NSBluetoothPeripheralUsageDescription = "This app will access your Bluetooth for connecting to the printer. Allow Bluetooth access?";
NSBluetoothAlwaysUsageDescription = "This app will access your Bluetooth for connecting to the printer. Allow Bluetooth access?";
NSContactsUsageDescription = "This app will access your contacts for printer connection services. Allow contacts access?";
NSMicrophoneUsageDescription = "This app will access your microphone for voice recognition. Allow microphone access?";
NSPhotoLibraryUsageDescription = "This permission is used for printing image materials, barcode recognition, QR code scanning, text recognition, setting custom avatars, and more. Please \"Allow Access to All Photos\" to ensure proper access to your photo album in NIIMBOT. If you choose \"Select Photos...\", any unselected and future photos will not be accessible in NIIMBOT.";
NSLocationWhenInUseUsageDescription = "To help you connect to nearby Wi-Fi, NIIMBOT requests location access.";
NSLocationAlwaysUsageDescription = "To help you connect to nearby Wi-Fi, NIIMBOT requests location access.";
NSLocationAlwaysAndWhenInUseUsageDescription = "To help you connect to nearby Wi-Fi, NIIMBOT requests location access.";
NSSpeechRecognitionUsageDescription = "This app requires your permission to access voice recognition. Allow it?";
NSLocalNetworkUsageDescription = "This app needs to access the ​Local Area Network (LAN)​​ for services that search for LAN devices and configure networks.";
"UILaunchStoryboardName" = "LaunchScreen";
