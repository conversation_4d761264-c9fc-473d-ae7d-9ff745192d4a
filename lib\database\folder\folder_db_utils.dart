import 'dart:convert';

import 'package:isar/isar.dart';
import 'package:text/application.dart';
import 'package:text/database/folder/folder_model.dart' as db_folder;
import 'package:text/database/folder/folder_model.g.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/pages/my_template/model/owner_Profile_model.dart';

class FolderDbUtils {
  static Future<bool> batchInsertFolders(List<FolderModel> folders) async {
    List<db_folder.FolderModel> isarFolders = folders.map((folder) => folderModelToIsarModel(folder)).toList();
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await isar.folderModels.putAll(isarFolders);
    });
    return true;
  }

  static Future<bool> batchDeleteFoldersByIds(List<num> folderIds) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      await Future.forEach(folderIds, (folderId) async {
        await isar.folderModels.filter().idEqualTo(folderId.toInt()).deleteAll();
      });
    });
    return true;
  }

  static Future<int> batchDeleteFoldersByIdAndType({String? id, int type = 0}) async {
    int userId = Application.user!.userId;
    int? folderId = id != null ? int.tryParse(id) : null;
    List<db_folder.FolderModel> isarModels;
    final isar = DBManagerUtil.instance.isar;
    if (type == 0 || type == 1) {
      if (folderId != null) {
        isarModels = await isar.folderModels.filter().idEqualTo(folderId).userIdEqualTo(userId).findAll();
      } else {
        isarModels = await isar.folderModels.filter().userIdEqualTo(userId).findAll();
      }
    } else {
      if (folderId != null) {
        isarModels = await isar.folderModels
            .filter()
            .idEqualTo(folderId)
            .sharedUserIdsElementContains(userId.toString())
            .findAll();
      } else {
        isarModels = await isar.folderModels.filter().sharedUserIdsElementContains(userId.toString()).findAll();
      }
    }
    if (isarModels.isNotEmpty) {
      await isar.writeTxn(() async {
        await isar.folderModels.deleteAll(isarModels.map((e) => e.isarId).toList());
      });
    }
    return isarModels.length;
  }

  static Future<List<FolderModel>> queryFoldersByIdAndType({String? id, int type = 0}) async {
    int? userId = Application.user?.userId;
    if (userId == null) {
      return [];
    }
    final isar = DBManagerUtil.instance.isar;

    // 修改排序逻辑，按照 gmtModified 时间降序排序（最新修改的在前）
    List<db_folder.FolderModel> isarFolderModels = await isar.folderModels
        .filter()
        .userIdEqualTo(userId)
        .sortByGmtModifiedDesc() // 按 gmtModified 降序排序
        .findAll();

    if (id != null) {
      isarFolderModels = isarFolderModels.where((e) => e.id?.toString() == id).toList();
    }
    if (type != 0) {
      isarFolderModels = isarFolderModels.where((e) => e.type == type).toList();
    }
    List<FolderModel> folderModels = isarFolderModels.map((e) => isarModelToFolderModel(e)).toList();
    return folderModels;
  }

  static Future<List<FolderModel>> shareMeQuery({String? id, int pageIndex = 1, int type = 0, String? folderId}) async {
    String userId = Application.user?.userId.toString() ?? "";
    int? folderId = id != null ? int.tryParse(id) : null;
    final isar = DBManagerUtil.instance.isar;
    List<db_folder.FolderModel> isarFolderModels;
    if (folderId != null) {
      isarFolderModels = await isar.folderModels
          .filter()
          .sharedUserIdsElementContains(userId)
          .idEqualTo(folderId)
          .typeEqualTo(type)
          .sortByGmtModifiedDesc() // 按 gmtModified 降序排序
          .offset((pageIndex - 1) * 100)
          .limit(100)
          .findAll();
    } else {
      isarFolderModels = await isar.folderModels
          .filter()
          .sharedUserIdsElementContains(userId)
          .typeEqualTo(type)
          .sortByGmtModifiedDesc() // 按 gmtModified 降序排序
          .offset((pageIndex - 1) * 100)
          .limit(100)
          .findAll();
    }
    List<FolderModel> folderModels = isarFolderModels.map((e) => isarModelToFolderModel(e)).toList();
    return folderModels;
  }

  ///通用model转数据库model
  static db_folder.FolderModel folderModelToIsarModel(FolderModel folderModel) {
    db_folder.FolderModel isarFolderModel = db_folder.FolderModel();
    isarFolderModel.name = folderModel.name;
    isarFolderModel.id = folderModel.id?.toInt();
    isarFolderModel.gmtCreated = folderModel.gmtCreated;
    isarFolderModel.gmtModified = folderModel.gmtModified;
    isarFolderModel.userId = folderModel.userId?.toInt();
    isarFolderModel.isAdd = folderModel.isAdd;
    isarFolderModel.isSelected = folderModel.isSelected;
    isarFolderModel.isEnableEdit = folderModel.isEnableEdit;
    isarFolderModel.isDefault = folderModel.isDefault;
    isarFolderModel.templateSize = folderModel.templateSize;
    isarFolderModel.sharedAt = folderModel.sharedAt;
    isarFolderModel.templateCount = folderModel.templateCount?.toInt();
    isarFolderModel.type = folderModel.type;
    isarFolderModel.vip = folderModel.vip;
    isarFolderModel.ownerProfileJson =
        folderModel.ownerProfile != null ? jsonEncode(folderModel.ownerProfile!.toJson()) : null;
    isarFolderModel.sharedUserIds = folderModel.sharedUserIds;
    isarFolderModel.sharedJson = jsonEncode(folderModel.shared);
    return isarFolderModel;
  }

  ///数据库model转通用model
  static FolderModel isarModelToFolderModel(db_folder.FolderModel isarFolderModel) {
    FolderModel folderModel = FolderModel(
        name: isarFolderModel.name,
        id: isarFolderModel.id,
        gmtCreated: isarFolderModel.gmtCreated,
        gmtModified: isarFolderModel.gmtModified,
        userId: isarFolderModel.userId,
        isAdd: isarFolderModel.isAdd,
        isSelected: isarFolderModel.isSelected,
        isEnableEdit: isarFolderModel.isEnableEdit,
        isDefault: isarFolderModel.isDefault,
        templateSize: isarFolderModel.templateSize,
        sharedAt: isarFolderModel.sharedAt,
        templateCount: isarFolderModel.templateCount,
        type: isarFolderModel.type,
        vip: isarFolderModel.vip,
        ownerProfile: isarFolderModel.ownerProfileJson?.isNotEmpty == true
            ? OwnerProfileModel.fromJson(jsonDecode(isarFolderModel.ownerProfileJson!))
            : null,
        sharedUserIds: isarFolderModel.sharedUserIds,
        shared: isarFolderModel.sharedJson?.isNotEmpty == true ? jsonDecode(isarFolderModel.sharedJson!) : null);
    return folderModel;
  }
}
