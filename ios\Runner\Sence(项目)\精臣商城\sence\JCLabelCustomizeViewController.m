//
//  JCLabelCustomizeViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2021/1/25.
//  Copyright © 2021 Jingchen Technology Co.Ltd  . All rights reserved.
//

#import "JCLabelCustomizeViewController.h"
#import "QQLBXScanViewController.h"
#import <AlipaySDK/AlipaySDK.h>
#import "Global.h"
#import "StyleDIY.h"
#import "ONImagePickerController.h"


@interface JCLabelCustomizeViewController ()
@property (nonatomic,assign) BOOL isLoginRefresh;
@end

@implementation JCLabelCustomizeViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.entrance_type_id = @"1";
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        self.statusBarStyle = statusBarStyle;
        self.statusBackColor = 0xFFFFFF;
        self.isNeedReload = YES;
        self.isHomePageShop = YES;
        
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 脚本消息处理器已在基类中注册，无需重复注册
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadShopHomeView) name:LOGIN_CHANGED_SHOP object:nil];
    if(xy_isLogin){
        [self loadUrl:self.shopLabelCustomizeUrl];
    }
    [self shopDetailStateView];
    self.view.backgroundColor = COLOR_WHITE;
    [self setupWebViewFrame];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
    NSLog(@"%@",self.webView);
    if(STR_IS_NIL(userShopToken)){
        self.isNeedReload = YES;
    }
    self.webView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
}

- (NSString *)shopLabelCustomizeUrl{
    NSString *url = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/templateNewHome" : @"#/templateNewHome"];
    NSString *labelCustromizeString = STR_IS_NIL([XYCenter sharedInstance].shopLabelCustomizeUrl) ? url : [XYCenter sharedInstance].shopLabelCustomizeUrl;
    return labelCustromizeString;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if(self.isNeedReload || !self.loadSuccess || self.isLoginRefresh){
        [self loadUrl:self.shopLabelCustomizeUrl];
        self.isNeedReload = NO;
        self.isLoginRefresh = NO;
    }else{
        
    }
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    self.detailStateView.backgroundColor = HEX_RGB(self.statusBackColor);
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}

//支付完成刷新webview
-(void)refreshWeb{
    UIViewController *rootVC = [UIApplication sharedApplication].keyWindow.rootViewController;
    if([rootVC isKindOfClass:[UITabBarController class]]){
        UITabBarController *root = (UITabBarController*)rootVC;
        UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
        if(![naviVC.visibleViewController isKindOfClass:[self class]]){
            self.isNeedReload = YES;
        }else{
            NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
            NSURL *url = [NSURL URLWithString:myStr];
            NSURLRequest *req = [NSURLRequest requestWithURL:url ];
            [self.webView loadRequest:req];
        }
    }
    
}

- (void)refreshWebViewWith:(NSString *)myUrlStr{
    [self loadUrl:myUrlStr];
}

// 商城埋点
- (void)callH5Mothord{
    NSString *jsStr = @"appUserClickEvent()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//刷新商城首页
- (void)reloadShopHomeView{
    if(!xy_isLogin){
        [self.webView evaluateJavaScript:@"document.body.innerHTML='';" completionHandler:^(id _Nullable response, NSError * _Nullable error) {
        }];
    }else{
        self.loadSuccess = NO;
        if(STR_IS_NIL(userShopToken)){
            self.isNeedReload = YES;
        }else{
            self.isNeedReload = NO;
            [self loadUrl:self.shopLabelCustomizeUrl];
        }
    }
}

//H5交互
#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    // 子类特有的处理逻辑
    self.isNeedReload = NO;
    self.progressView.alpha = 0;
    
    // 调用基类的消息处理方法
    [super userContentController:userContentController didReceiveScriptMessage:message];
}

// 扫码方法已在基类中实现，无需重复定义

@end
