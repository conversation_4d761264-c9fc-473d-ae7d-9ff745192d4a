/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Aplikasi ini memerlukan akses kamera untuk memindai barcode, mengenali teks, dan mengambil foto. Izinkan akses kamera?";
NSBluetoothPeripheralUsageDescription = "Aplikasi ini memerlukan akses Bluetooth untuk menghubungkan ke printer. Izinkan akses Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Aplikasi ini memerlukan akses Bluetooth untuk menghubungkan ke printer. Izinkan akses Bluetooth?";
NSContactsUsageDescription = "Aplikasi ini memerlukan akses ke kontak Anda. Izinkan akses ke kontak?";
NSMicrophoneUsageDescription = "Aplikasi ini memerlukan akses mikrofon untuk pengenalan suara. Izinkan akses mikrofon?";
NSPhotoLibraryUsageDescription = "Izin ini digunakan untuk mencetak gambar, mengenali barcode/QR code, mengenali teks, dan mengatur avatar khusus. Izinkan akses ke semua foto agar aplikasi NIIMBOT dapat mengakses album dengan normal. Jika kamu menggunakan “Pilih Foto...”, maka foto yang tidak dipilih dan foto baru di masa depan tidak bisa diakses oleh NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Untuk memudahkan penggunaan jaringan Wi-Fi di sekitar Anda, NIIMBOT memerlukan izin lokasi.";
NSLocationAlwaysUsageDescription = "Untuk memudahkan penggunaan jaringan Wi-Fi di sekitar Anda, NIIMBOT memerlukan izin lokasi.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Untuk memudahkan penggunaan jaringan Wi-Fi di sekitar Anda, NIIMBOT memerlukan izin lokasi.";
NSSpeechRecognitionUsageDescription = "Aplikasi ini memerlukan izin Anda untuk mengakses pengenalan suara. Izinkan akses?";
NSLocalNetworkUsageDescription = "Aplikasi ini perlu mengakses ​Jaringan area lokal (LAN)​​ untuk layanan pencarian perangkat LAN dan konfigurasi jaringan.";
"UILaunchStoryboardName" = "LaunchScreen";
