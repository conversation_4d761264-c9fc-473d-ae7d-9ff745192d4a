# NewScanActivity 业务流程分析文档

## 概述

NewScanActivity 是一个扫码识别页面，支持多种扫码模式和场景，主要用于扫描二维码、条形码、标签识别等功能。配合 ScanViewModel 处理各种扫码业务逻辑。

## 核心组件

- **NewScanActivity**: 主界面，负责相机扫码、图片选择、UI交互
- **ScanViewModel**: 业务逻辑处理，负责扫码结果分析、网络请求、数据处理

## 业务场景分析

### 场景1：相机扫码识别

#### 业务流程图

```mermaid
graph TD
    A[启动扫码页面] --> B[检查相机权限]
    B --> C{权限是否获取}
    C -->|是| D[初始化华为扫码SDK]
    C -->|否| E[显示权限申请对话框]
    E --> F{用户是否授权}
    F -->|是| D
    F -->|否| G[显示权限被拒绝提示]
    D --> H[开始扫码]
    H --> I[获取扫码结果]
    I --> J[处理扫码结果]
    J --> K[根据模式分发处理]
```

#### 关键代码

**初始化扫码SDK**
```kotlin
private fun initScanKit(savedInstanceState:Bundle?) {
    val dm = getResources().getDisplayMetrics()
    val density = dm.density
    val mScreenWidth = getResources().getDisplayMetrics().widthPixels
    val mScreenHeight = getResources().getDisplayMetrics().heightPixels
    val SCAN_FRAME_SIZE = 300
    val scanFrameSize = (SCAN_FRAME_SIZE * density).toInt()
    val rect = Rect()
    rect.left = (mScreenWidth / 2 - scanFrameSize / 2).toInt()
    rect.right = (mScreenWidth / 2 + scanFrameSize / 2).toInt()
    rect.top = (mScreenHeight / 2 - scanFrameSize / 2).toInt()
    rect.bottom = (mScreenHeight / 2 + scanFrameSize / 2).toInt()
    
    remoteView = RemoteView.Builder()
        .setContext(this)
        .setBoundingBox(rect)
        .setContinuouslyScan(true)
        .setFormat(HmsScan.ALL_SCAN_TYPE).build()
    
    remoteView?.onCreate(savedInstanceState)
    val params = FrameLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.MATCH_PARENT)
    binding.scanRoot.addView(remoteView, 0, params)
    
    remoteView?.setOnResultCallback {hmsScans ->
        if (hmsScans != null && hmsScans.isNotEmpty()) {
            val result = hmsScans?.getOrNull(0)?.let {
                Result(it.originalValue, it.getOriginValueByte(), null, 
                    melon.south.com.baselibrary.util.ImageScanUtil.getScanTypeFromHMSScan(it.getScanType()))
            }
            processScanResult(result)
        }
    }
}
```

**权限检查**
```kotlin
private fun checkPermission() {
    PermissionDialogUtils.showCameraPermissionDialog(
        this,
        RequestCode.MORE,
        object : XPermissionUtils.OnPermissionListener {
            override fun onPermissionGranted() {
                remoteView?.onStart()
                remoteView?.onResume()
            }

            override fun onPermissionDenied(
                deniedPermissions: Array<String>,
                alwaysDenied: Boolean
            ) {
                showToast("app01309")
            }
        })
}
```

### 场景2：图片扫码识别

#### 业务流程图

```mermaid
graph TD
    A[点击相册按钮] --> B[检查存储权限]
    B --> C{权限是否获取}
    C -->|是| D[打开图片选择器]
    C -->|否| E[显示权限申请对话框]
    E --> F{用户是否授权}
    F -->|是| D
    F -->|否| G[显示权限被拒绝提示]
    D --> H[用户选择图片]
    H --> I[图片裁剪处理]
    I --> J[解码图片中的二维码/条形码]
    J --> K{是否识别成功}
    K -->|是| L[处理扫码结果]
    K -->|否| M[显示识别失败提示]
```

#### 关键代码

**图片选择**
```kotlin
private fun selectPicFromLocal() {
    PermissionDialogUtils.showGalleryPermissionDialog(this, RequestCode.MORE,
        object : XPermissionUtils.OnPermissionListener {
            override fun onPermissionGranted() {
                PictureSelector.create(this@NewScanActivity)
                    .openGallery(SelectMimeType.ofImage())
                    .setMaxSelectNum(1)
                    .setSelectionMode(SelectModeConfig.SINGLE)
                    .setImageEngine(CustomImageEngine.createGlideEngine())
                    .setCropEngine(ScanImageFileCropEngine())
                    .setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED)
                    .setLanguage(TextHookUtil.getInstance().getPictureSelectorLanguage())
                    .forResult(PictureConfig.CHOOSE_REQUEST)
            }

            override fun onPermissionDenied(
                deniedPermissions: Array<out String>?,
                alwaysDenied: Boolean
            ) {
                com.niimbot.appframework_library.utils.showToast("app01370")
            }
        })
}
```

**图片解码**
```kotlin
private fun scanBitmap(sourceBitmap: Bitmap?) {
    sourceBitmap?.let { bitmap ->
        GlobalScope.launch {
            MainScope().launch { showLoading() }
            LogUtils.e("图片解码start to decode")
            var result = ImageScanUtil.handleQrcodeFromBitmap(
                this@NewScanActivity,
                bitmap,
                DecodeFormatManager.parseDecodeFormats(Intent().apply {
                    putExtra(Intents.Scan.FORMATS, formats.joinToString(","))
                })
            )
            LogUtils.e("图片解码end decode")
            LogUtils.e("图片解码结果： ${any2Json(result)}")
            processScanResult(result)
        }
    }
}
```

### 场景3：扫码模式选择与处理

#### 业务流程图

```mermaid
graph TD
    A[获取扫码结果] --> B[判断扫码内容类型]
    B --> C{是否为登录二维码}
    C -->|是| D[处理登录逻辑]
    B --> E{是否为固资二维码}
    E -->|是| F[跳转固资页面]
    B --> G{是否为URL}
    G -->|是| H[通过Scheme路由跳转]
    B --> I{是否为数字}
    I -->|是| J[根据选择模式处理]
    J --> K[自动识别模式]
    J --> L[扫商品模式]
    J --> M[识标签模式]
    B --> N[其他格式处理]
```

#### 关键代码

**扫码结果处理主流程**
```kotlin
private fun processScanResult(result: Result?) {
    if (result == null || result.text == null) {
        MainScope().launch {
            dismissLoading()
            remoteView?.resumeContinuouslyScan()
        }
        showToast("app100000049")
        return
    }
    
    // 小程序原始扫码结果处理
    if(unimpScanType == 2){
        val jsonResult = JSONObject()
        jsonResult["detail"] = result?.text
        jsonResult["type"] = "scanResult"
        EventBus.getDefault().post(JSONObject().apply {
            put("action", "scanForIonic")
            put("data", jsonResult)
        }.toJSONString())
        onBackPressed()
        return
    }
    
    remoteView?.pauseContinuouslyScan()
    MainScope().launch { dismissLoading() }
    result.let {
        // Flutter回调处理
        if (intent.getBooleanExtra("flutterCallback", false)) {
            if(intent.getBooleanExtra("justCallbackScanCode", false)) {
                val intent = Intent()
                intent.putExtra("value", it.text)
                intent.putExtra("source", 3)
                intent.putExtra("type", scanType)
                intent.putExtra("s_type", 2)
                if (result.barcodeFormat == BarcodeFormat.EAN_13) {
                    intent.putExtra("commodity_barcode", it.text)
                }
                setResult(Activity.RESULT_OK, intent)
                super.finish()
                return@let
            }
        }
        
        // 登录二维码处理
        val loginCode = getLoginCode(it.text)
        if(!loginCode.isNullOrEmpty()){
            scanViewModel.processQrCodeLogin(client = loginCode[0], loginCode = loginCode[1], activity = this@NewScanActivity)
            return@let
        }
        
        // 固资二维码处理
        val guziInfo = getGuziInfo(it.text)
        if(guziInfo.isNotEmpty()){
            super.finish()
            val openGuziEvent = JSONObject()
            openGuziEvent.put("action", "openGuziPage")
            openGuziEvent.put("params", guziInfo)
            openGuziEvent.put("url", NiimbotGlobal.guziBaseUrl)
            EventBus.getDefault().post(openGuziEvent.toJSONString())
            return@let
        }
        
        if (qrFormats.contains(result.barcodeFormat)) scanType = 2
        when {
            StringUtil.isUrl(it.text) -> {
                track(3, result = 1)
                NiimbotGlobal.routingByScheme(this@NewScanActivity, it.text)
            }

            StringUtil.isNumer(it.text) -> {
                if (fromGoodRepo) {
                    MainScope().launch { dismissLoading() }
                    intent.putExtra("barCode", it.text)
                    intent.putExtra("type", scanType)
                    intent.putExtra("s_type", 1)
                    setResult(0x1111, intent)
                    onBackPressed()
                } else {
                    scanViewModel.checkScanResult(selectedView.id, it,this@NewScanActivity)
                }
            }

            else -> {
                if (intent.getBooleanExtra("flutterCallback", false)) {
                    val intent = Intent()
                    intent.putExtra("value",it.text)
                    intent.putExtra("source", 3)
                    intent.putExtra("type", scanType)
                    intent.putExtra("s_type", 2)
                    if (result.barcodeFormat == BarcodeFormat.EAN_13) {
                        intent.putExtra("commodity_barcode", it.text)
                    }
                    setResult(Activity.RESULT_OK, intent)
                    super.finish()
                }else{
                    launchScanResult(this@NewScanActivity, it.text, it.barcodeFormat.ordinal)
                }
            }
        }
    }
}
```

### 场景4：三种识别模式处理

#### 4.1 自动识别模式

**业务流程图**
```mermaid
graph TD
    A[自动识别模式] --> B[调用云端模板识别]
    B --> C{是否识别到模板}
    C -->|是| D[返回OCR结果]
    C -->|否| E[降级为商品扫描]
    E --> F[查询商品信息]
    F --> G[返回商品扫描结果]
```

**关键代码**
```kotlin
private fun processAutoMode(rawResult: Result, activity: NewScanActivity) {
    val oneCode = rawResult.text
    MainScope().launch { showLoading(activity) }
    TemplateSyncLocalUtils.getCloudTemplateByScanCode(
        oneCode,
        true
    ) { result, templateModule, errorMessage ->
        MainScope().launch { dismissLoading() }
        if (result) {
            ocrResultData.value = templateModule
        } else {
            queryScanResult(rawResult, activity)
        }
    }
}
```

#### 4.2 识标签模式

**业务流程图**
```mermaid
graph TD
    A[识标签模式] --> B[调用云端模板识别]
    B --> C{是否识别到模板}
    C -->|是| D[返回OCR结果]
    C -->|否| E[显示识别失败]
```

**关键代码**
```kotlin
private fun processOCRMode(rawResult: Result, activity: NewScanActivity) {
    val oneCode = rawResult.text
    MainScope().launch { showLoading(activity) }
    TemplateSyncLocalUtils.getCloudTemplateByScanCode(
        oneCode,
        true
    ) { result, templateModule, errorMessage ->
        dismissLoading()
        if (result) {
            ocrResultData.value = templateModule
        } else {
            if (!errorMessage.isNullOrEmpty()) {
                errorMsg.value = ErrorData(1, errorMessage)
            }
        }
    }
}
```

#### 4.3 扫商品模式

**业务流程图**
```mermaid
graph TD
    A[扫商品模式] --> B[检查登录状态]
    B --> C{是否已登录}
    C -->|否| D[显示登录对话框]
    D --> E{用户是否登录}
    E -->|是| F[查询商品信息]
    E -->|否| G[取消操作]
    C -->|是| F
    F --> H{是否查询成功}
    H -->|是| I[处理商品数据]
    H -->|否| J[显示查询失败]
    I --> K{是否有模板}
    K -->|是| L[生成绘图数据]
    K -->|否| M[使用默认RFID模板]
    L --> N[返回扫描结果]
    M --> N
```

**关键代码**
```kotlin
private fun queryScanResult(rawResult: Result, activity: NewScanActivity) {
    val goodsCode = rawResult.text
    if (!NetworkUtils.isConnected()) {
        errorMsg.value = ErrorData(-1, "app01139")
        return
    }

    var getGoods = {
        MainScope().launch {
            if (activity.isDestroyed || activity.isFinishing) return@launch
            try {
                showLoading(activity)
            }catch (e: Exception){

            }
        }
        TemplateSyncLocalUtils.getGoodsInfoByScanCode(
            goodsCode,
            needOldURL = fromUniapp
        ) { result, templateExampleModuleNew, _ ->
            MainScope().launch { dismissLoading() }
            if (!result) {
                errorMsg.value = ErrorData(2, "app01160")
                return@getGoodsInfoByScanCode
            }
            val applyTemplateExampleModuleNew =
                templateExampleModuleNew ?: TemplateExampleModuleNew()
            val applyTemplateModule = applyTemplateExampleModuleNew.template_info
            val applyGoodsInfo = (applyTemplateExampleModuleNew.goods_info
                ?: TemplateExampleModule()).apply { this.one_code = goodsCode }
            var tmp: NiimbotDrawData? = null

            if (applyTemplateModule != null) {
                tmp = NiimbotDrawData()
                tmp.niimbotTemplate = applyTemplateModule.toTemplateModuleLocal()
                tmp.hasGoodsTemplate = true
                tmp.niimbotGoodsInfo = applyGoodsInfo.getTextMap()
                
                scanResultData.value = ScanResultData(
                    goodsCode,
                    tmp,
                    applyGoodsInfo,
                    null != applyTemplateExampleModuleNew.goods_info
                )
            } else if (AppDataUtil.rfidTemplateModule != null) {
                // 使用默认RFID模板处理
                tmp = NiimbotDrawData()
                tmp.niimbotTemplate = AppDataUtil.rfidTemplateModule!!.toTemplateModuleLocal()
                // ... 构建商品数据源和元素
                scanResultData.value = ScanResultData(
                    goodsCode,
                    tmp,
                    applyGoodsInfo,
                    null != applyTemplateExampleModuleNew.goods_info
                )
            } else {
                scanResultData.value = ScanResultData(
                    goodsCode,
                    tmp,
                    applyGoodsInfo,
                    null != applyTemplateExampleModuleNew.goods_info
                )
            }
        }
    }

    NiimbotGlobal.loginCancelCallback =  {
        errorMsg.value = ErrorData(5, "app01160")
    }
    LoginDataEnum.loginCheckConfirm(activity, "app100001162", { it ->
        if (!it) {
            errorMsg.value = ErrorData(5, "app01160")
            return@loginCheckConfirm
        }
    }, {
        if (LoginDataEnum.isLogin) {
            getGoods.invoke()
        }
    })
}
```

### 场景5：扫码登录处理

#### 业务流程图

```mermaid
graph TD
    A[识别到登录二维码] --> B[解析登录信息]
    B --> C[检查登录状态]
    C --> D{是否已登录}
    D -->|否| E[标记需要登录]
    D -->|是| F[调用扫码登录接口]
    E --> G[用户登录后继续]
    G --> F
    F --> H{登录是否成功}
    H -->|是| I[跳转到扫码登录页面]
    H -->|否| J[显示登录失败]
```

#### 关键代码

**登录二维码识别**
```kotlin
private fun getLoginCode(content: String): List<String>?{
    //云打印PC扫码登录
    if((content.startsWith("http://") || content.startsWith("https://")) && content.contains("niimbotScan?code=")){
        val splitArray = content.split("niimbotScan?code=")
        if(splitArray.size != 2){
            return null
        }
        return splitArray.mapIndexed { index, s ->  if(index == 0) "pc" else s }
    }
    //固资扫码登录
    if (content.startsWith("client=") && content.contains("authCode=")) {
        val splitArray = content.replace("client=", "").replace("authCode=", "").trim().split("&&")
        if(splitArray.size != 2){
            return null
        }
        return splitArray
    }
    return null
}
```

**扫码登录处理**
```kotlin
fun processQrCodeLogin(client: String = "PC", loginCode: String, activity: NewScanActivity){
    if (!NetworkUtils.isConnected()) {
        errorMsg.value = ErrorData(-1, "app01139")
        return
    }
    if(!LoginDataEnum.isLogin){
        activity.handleLogin = true
    }
    if(activity.hasHandleOcrResult){
        return
    }
    activity.hasHandleOcrResult = true
    LoginDataEnum.directLoginCheck{
        MainScope().launch { showLoading(activity) }
        FlutterMethodInvokeManager.loginScanCode(client, loginCode){result, error ->
            MainScope().launch { dismissLoading() }
            if(result){
                scanLoginResultData.value = "$client&&$loginCode"
            }
            else{
                errorMsg.value = ErrorData(6, error)
                activity.hasHandleOcrResult = false
            }
        }
    }
}
```

## 数据流转

### ViewModel 数据观察

```kotlin
scanViewModel.dataSource.observe(this) {
    it.data?.apply {
        when (it.actionType) {
            ActionType.SHOW_ERROR      -> showErrorMsg(this as ErrorData)
            ActionType.GET_SCAN_RESULT -> handleQueryResult(this as ScanResultData)
            ActionType.GET_OCR_RESULT  -> handleOcrResult(this as TemplateModule)
            ActionType.SCAN_LOGIN_RESULT -> handleScanLoginConfirm(this as String)
        }
    }
}
```

### 数据模型

```kotlin
enum class ActionType { SHOW_ERROR, GET_SCAN_RESULT, GET_OCR_RESULT, SCAN_LOGIN_RESULT }

data class ScanResultData(
    val goodsCode: String,
    val niimbotDrawData: NiimbotDrawData?,
    val applyGoodsInfo: TemplateExampleModule,
    val getGoods: Boolean
)

data class PageData(val actionType: ActionType, val data: Any?)
data class ErrorData(val code: Int, val msg: String)
```

## 关键配置参数

### 启动参数

- `from_change_template`: 是否来自更换模板
- `from_goods_repo`: 是否来自商品库
- `fromUnimp`: 是否来自小程序
- `scanType`: 扫码类型
- `customTips`: 自定义提示
- `trackSource`: 追踪来源
- `flutterCallback`: 是否需要Flutter回调
- `needTemplateIdCallback`: 是否需要模板ID回调
- `justCallbackScanCode`: 是否只回调扫码结果

### 支持的扫码格式

```kotlin
var formats = arrayListOf(
    ScanOptions.UPC_A, ScanOptions.UPC_E, ScanOptions.EAN_8, ScanOptions.EAN_13,
    ScanOptions.RSS_14, ScanOptions.CODE_39, ScanOptions.CODE_93, ScanOptions.CODE_128,
    "CODABAR", ScanOptions.ITF, ScanOptions.RSS_14, ScanOptions.RSS_EXPANDED,
    ScanOptions.QR_CODE, ScanOptions.DATA_MATRIX, ScanOptions.PDF_417, "AZTEC"
)

var qrFormats = arrayListOf(
    BarcodeFormat.QR_CODE, BarcodeFormat.DATA_MATRIX,
    BarcodeFormat.PDF_417, BarcodeFormat.AZTEC,
)
```

## 注意事项

1. **权限管理**: 需要相机权限和存储权限
2. **生命周期管理**: 正确处理扫码SDK的生命周期
3. **内存管理**: 及时释放Bitmap资源
4. **网络状态**: 检查网络连接状态
5. **登录状态**: 某些功能需要登录验证
6. **错误处理**: 完善的错误提示和重试机制
7. **埋点统计**: 记录用户行为数据

## 维护建议

1. **代码优化**: 可以考虑将长方法拆分为更小的方法
2. **异常处理**: 增加更多的异常捕获和处理
3. **测试覆盖**: 增加单元测试和集成测试
4. **文档更新**: 及时更新业务逻辑变更的文档
5. **性能监控**: 监控扫码识别的成功率和响应时间
