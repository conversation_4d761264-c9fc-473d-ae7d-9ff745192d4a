import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/image_element.dart';
import 'package:niimbot_flutter_canvas/src/provider/floating_bar_visible_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/smart_tips_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';

import '/src/localization/localization_public.dart';
import '/src/model/canvas_element.dart';
import '/src/model/element/table_element.dart';
import 'floating_bar_action.dart';
import 'floating_shortcut_bar.dart';

class FloatingBarHelper {
  static Logger _logger = Logger("FloatingBarHelper", on: kDebugMode);

  FloatingBarHelper._internal();

  static FloatingBarHelper? _instance;

  factory FloatingBarHelper() {
    _instance ??= FloatingBarHelper._internal();
    return _instance!;
  }

  double heightCache = 0;
  OverlayEntry? _overlayEntry;
  Size? anchorSize;
  Offset? anchorOffset;
  double leftMargin = 0.0;
  double topMargin = 0.0;
  double fixTop = 0.0;
  double arrowLeftMargin = 0.0;
  bool moreOnCanvas = false;
  bool multiSelect = false;
  bool multiSelectSubStatus = false;
  bool showSelectAllLine = false;
  TextDirection? currentDirection;

  Function(FloatingShortcutType type, List<CanvasElement> selectedCanvasElements)? callback;
  bool Function()? checkSelectAll;
  bool Function()? checkSelectAllText;
  bool Function()? checkSelectAllLine;
  List<CanvasElement>? selectedCanvasElements;
  List<CanvasElement>? canvasElements;
  List<FloatingShortcutAction>? currentActions;
  Map<String, double> floatWidthCache = {};

  List<FloatingShortcutAction> get distributeSelectItems => [
        FloatingShortcutAction(FloatingShortcutType.back, '',
            icon: 'assets/floating_shortcut/floating_shortcut_back.svg'),
        FloatingShortcutAction(FloatingShortcutType.v_distribute_shrink, intlanguage('app100000856', '更紧凑'),
            trackName: '更紧凑', actionType: ActionType.distribute),
        FloatingShortcutAction(FloatingShortcutType.v_distribute_expand, intlanguage('app100000857', '更疏松'),
            trackName: '更疏松', actionType: ActionType.distribute),
        FloatingShortcutAction(FloatingShortcutType.v_distribute, intlanguage('app100000854', '垂直等距'),
            trackName: '垂直等距', actionType: ActionType.distribute),
        FloatingShortcutAction(FloatingShortcutType.h_distribute, intlanguage('app100000855', '水平等距'),
            trackName: '水平等距', actionType: ActionType.distribute),
      ];

  final GlobalKey<FloatingShortcutBarState> _floatBarKey = GlobalKey();

  void showFloatingBar(
      BuildContext context,
      double fixTop,
      double arrowMarginRatio,
      Size anchorSize,
      Offset anchorOffset,
      List<CanvasElement> canvasElements,
      List<CanvasElement> selectedCanvasElements,
      double canvasScale,
      bool multiSelect,
      bool moreOnCanvas,
      Function(FloatingShortcutType type, List<CanvasElement> selectedCanvasElements) callback,
      bool Function()? checkSelectAll,
      bool Function()? checkSelectAllText,
      bool Function()? checkSelectAllLine) {
    if(selectedCanvasElements.any((element) => element.data is ImageElement && 
    ((element.data as ImageElement).isDefaultImage ?? false))){
      return;
    }
    this.currentDirection = Directionality.of(context);
    this.canvasElements = canvasElements;
    this.selectedCanvasElements = selectedCanvasElements;
    this.callback = callback;
    this.checkSelectAll = checkSelectAll;
    this.checkSelectAllText = checkSelectAllText;
    this.checkSelectAllLine = checkSelectAllLine;
    this.fixTop = fixTop;
    this.anchorSize = anchorSize;
    this.anchorOffset = anchorOffset;
    this.moreOnCanvas = moreOnCanvas;
    this.multiSelect = multiSelect;
    if (leftMargin != anchorOffset.dx) {
      lastWidth = 0;
    }
    leftMargin = anchorOffset.dx + anchorSize.width / 2 - 120;
    topMargin = anchorOffset.dy;
    if (topMargin < fixTop) topMargin = fixTop;

    if (checkHasCovered()) {
      return;
    }

    if (_overlayEntry != null) {
      parentAction = null;
      _justFresh();
      return;
    }

    _overlayEntry = OverlayEntry(builder: (context) {
      currentActions = _getActions(context);
      double cacheWidth = _getCacheWidth();
      if (cacheWidth != 0) {
        updateFloatingBar(cacheWidth, init: true);
      }

      Widget floatbar = Positioned(
          left: leftMargin,
          top: topMargin,
          child: Material(
              color: Colors.transparent,
              child: Container(
                child: FloatingShortcutBar(
                  key: _floatBarKey,
                  arrowLeftMargin: arrowLeftMargin,
                  actions: currentActions,
                  floatingShortcutClicked: _actionHandle,
                ),
              )));
      _logger.log("===========overLayout return floatbar, leftMargin: $leftMargin, topMargin: $topMargin");
      return floatbar;
    });

    Overlay.of(context).insert(_overlayEntry!);
  }

  ///检查进度条是否被遮挡
  bool checkHasCovered() {
    if (heightCache <= 0) {
      heightCache = DisplayUtil.deviceSize.height;
    }
    double panelHeight = SmartTipsNotifier().bottomOffset;
    _logger.log("==============检查悬浮条被遮盖，topMargin： $topMargin, 屏幕height：$heightCache, panel高度：$panelHeight");
    bool hasCovered = topMargin > heightCache - panelHeight;
    if (hasCovered) dismissFloatingBar();
    return hasCovered;
  }

  void dismissFloatingBar() {
    //多选情况下不允许移除工具条
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      _resetFloatbar();
      FloatingBarVisibleNotifier().forceDismiss(needNotify: false);
    }
  }

  void selectSizeChanged(List<CanvasElement> selectedCanvasElements, bool multiSelect) {
    this.selectedCanvasElements?.clear();
    this.selectedCanvasElements?.addAll(selectedCanvasElements);
    this.multiSelect = multiSelect;
    if (selectedCanvasElements.isEmpty) {
      dismissFloatingBar();
    } else {
      if (multiSelect) {
        if (this.multiSelectSubStatus) {
          _resetSelectSubStatus();
          updateFloatingBar(0);
        } else {
          // 间距是否显示刷新，子元素可能已更新数量
          if (parentAction?.type == FloatingShortcutType.distributed && selectedCanvasElements.length < 3) {
            parentAction = null;
            itemDepth--;
          }
          _justFresh();
        }
      } else {
        _resetSelectSubStatus();
        _justFresh();
      }
    }
  }

  void graphElementTypeRefreshFloatingBarIfNecessary() {
    if (this.multiSelectSubStatus) {
      bool hasLine = canvasElements
              ?.any((element) => !element.data.isMirrorElement() && element.data.type == ElementItemType.line) ??
          false;
      if (hasLine) {
        if (!showSelectAllLine) {
          updateFloatingBar(0);
        }
      } else {
        if (showSelectAllLine) {
          updateFloatingBar(0);
        }
      }
    }
  }

  _resetFloatbar() {
    parentAction = null;
    itemDepth = 1;
    lastWidth = 0.0;
    _resetSelectSubStatus();
  }

  _resetSelectSubStatus() {
    this.multiSelectSubStatus = false;
    this.showSelectAllLine = false;
  }

  double lastWidth = 0.0;

  void updateFloatingBar(double floatBarWidth, {bool needFixTop = false, bool init = false}) {
    if (lastWidth == floatBarWidth) return;
    var canvasWidth = DisplayUtil.deviceSize.width;
    if (_overlayEntry != null) {
      lastWidth = floatBarWidth;
      double anchorCenterX = anchorSize!.width / 2 + anchorOffset!.dx;
      leftMargin = anchorCenterX - floatBarWidth / 2;
      double rightMargin = canvasWidth - anchorCenterX - floatBarWidth / 2;
      if (leftMargin > 0 && rightMargin > 0) {
        arrowLeftMargin = floatBarWidth / 2 - 9;
      } else if (leftMargin < 0) {
        arrowLeftMargin = floatBarWidth / 2 + leftMargin - 12;
        if (arrowLeftMargin < 8) arrowLeftMargin = 8;
        leftMargin = 8;
      } else if (rightMargin < 0) {
        arrowLeftMargin = floatBarWidth / 2 - rightMargin + 6;
        if (arrowLeftMargin > floatBarWidth - 17 - 8) arrowLeftMargin = floatBarWidth - 17 - 8;
        leftMargin = canvasWidth - floatBarWidth - 8;
      }
    }

    ///箭头适配rtl的显示
    if (currentDirection == TextDirection.rtl) {
      arrowLeftMargin = floatBarWidth - arrowLeftMargin;
      if (arrowLeftMargin < 8) arrowLeftMargin = 8;
      if (arrowLeftMargin > floatBarWidth - 17 - 8) arrowLeftMargin = floatBarWidth - 17 - 8;
    }

    if (multiSelect) {
      topMargin = fixTop;
      leftMargin = (canvasWidth - floatBarWidth) / 2;
      arrowLeftMargin = floatBarWidth / 2;
    } else {
      topMargin = anchorOffset!.dy;

      ///产品需要元素在最上面时，悬浮条不遮挡元素
      if (topMargin < fixTop - 32) {
        topMargin = fixTop - 32;
      }
    }

    if (currentActions?.isNotEmpty ?? false) {
      String key = currentActions!.map((e) => e.type.index).join(',');
      floatWidthCache[key] = floatBarWidth;
      _logger.log("===============记录floatWidthCache， cache长度：${floatWidthCache.toString()}");
    }
    if (!init && _overlayEntry != null) {
      _overlayEntry!.markNeedsBuild();
    }
  }

  FloatingShortcutAction? parentAction;
  int itemDepth = 1;

  ///是否显示’上一个‘箭头
  var showStartArrow = false;

  ///是否显示‘下一个’箭头
  var showEndArrow = true;

  List<FloatingShortcutAction> _getActions(BuildContext context) {
    CanvasElement firstCanvasElement = selectedCanvasElements!.first;
    JsonElement firstJsonElement = firstCanvasElement.data;

    var actions;

    if (parentAction != null) {
      if (parentAction?.type == FloatingShortcutType.align) {
        if (Directionality.of(context) == TextDirection.rtl) {
          actions = alignSelectItemsRTL;
        } else {
          actions = alignSelectItems;
        }
      } else if (parentAction?.type == FloatingShortcutType.distributed) {
        actions = distributeSelectItems;
      } else if (parentAction?.type == FloatingShortcutType.back) {
        if (itemDepth == 2) {
          actions = distributeSelectItems;
        }
      } else if (parentAction!.type == FloatingShortcutType.tab_cell_delete) {
        TableElement tableElement = firstJsonElement as TableElement;
        actions = [
          FloatingShortcutAction(FloatingShortcutType.back, '',
              icon: 'assets/floating_shortcut/floating_shortcut_back.svg'),
          if (tableElement.deleteRowEnable() > 0)
            FloatingShortcutAction(
                FloatingShortcutType.tab_delete_row,
                tableElement.deleteRowEnable() > 1
                    ? intlanguage('app100000851', '删除该行', param: [tableElement.deleteRowEnable().toString()])
                    : intlanguage('app100000849', '删除该行')),
          if (tableElement.deleteColumnEnable() > 0)
            FloatingShortcutAction(
                FloatingShortcutType.tab_delete_column,
                tableElement.deleteColumnEnable() > 1
                    ? intlanguage('app100000852', '删除该列', param: [tableElement.deleteColumnEnable().toString()])
                    : intlanguage('app100000850', '删除该列')),
        ];
      } else if (parentAction!.type == FloatingShortcutType.tab_cell_insert) {
        actions = tableInsertActions;
      } else {
        actions = _buildActionItems();
      }
    } else if (selectedCanvasElements!.length == 1 &&
        firstJsonElement.type == ElementItemType.table &&
        (firstJsonElement as TableElement).getFocusedCells().length > 0) {
      /// 表格选中且单元格具备焦点的情况下
      TableElement tableElement = firstJsonElement;
      itemDepth = 1;
      actions = [
        if ((firstJsonElement).getFocusedCells().length == 1)
          FloatingShortcutAction(FloatingShortcutType.edit, intlanguage('app00336', '编辑'),
              icon: 'assets/floating_shortcut/floating_shortcut_input.svg', trackName: '编辑'),

        /// 合并单元格
        if (tableElement.mergeCellsEnable())
          FloatingShortcutAction(FloatingShortcutType.mergeCells, intlanguage('app100000860', '合并')),

        /// 拆分单元格
        if (tableElement.splitCellsEnable())
          FloatingShortcutAction(FloatingShortcutType.splitCells, intlanguage('app100000861', '拆分')),

        /// 删除行/列
        if ((tableElement.deleteColumnEnable() > 0 || tableElement.deleteRowEnable() > 0) && tableElement.isLock != 1)
          FloatingShortcutAction(FloatingShortcutType.tab_cell_delete, intlanguage('app100000848', '删除行/列')),

        if (tableElement.isLock != 1)
        FloatingShortcutAction(FloatingShortcutType.tab_cell_insert, intlanguage('app100000847', '插入行/列')),

        if (tableElement.clearContentEnable())
          FloatingShortcutAction(FloatingShortcutType.tab_cell_clear, intlanguage('app00209', '清除')),
      ];
    } else {
      if (multiSelectSubStatus) {
        actions = _buildMultiSelectSubActionItems();
      } else {
        actions = _buildActionItems();
      }
    }
    return actions;
  }

  ///多选次级子菜单
  ///不同场景根据需要显示“全选”、“全选文本”、“全选线条”
  List<FloatingShortcutAction> _buildMultiSelectSubActionItems() {
    bool hasText = canvasElements?.any((element) => element.data.isTextElement()) ?? false;
    bool hasLine = canvasElements?.any((element) => element.data.type == ElementItemType.line) ?? false;
    showSelectAllLine = hasLine;
    List<FloatingShortcutAction> actions = [
      FloatingShortcutAction(FloatingShortcutType.select_all, intlanguage('app00506', '全选'),
          trackName: '全选', actionType: ActionType.selectAll),
      if (hasText)
        FloatingShortcutAction(FloatingShortcutType.select_all_text, intlanguage('app100001241', '全选文本'),
            trackName: '全选文本', actionType: ActionType.selectAll),
      if (hasLine)
        FloatingShortcutAction(FloatingShortcutType.select_all_line, intlanguage('app100001242', '全选线条'),
            trackName: '全选线条', actionType: ActionType.selectAll),
    ];
    return actions;
  }

  List<FloatingShortcutAction> _buildActionItems() {
    CanvasElement firstCanvasElement = selectedCanvasElements!.first;
    JsonElement firstJsonElement = firstCanvasElement.data;

    bool hasTable = selectedCanvasElements!.any((element) => element.elementType == ElementItemType.table);
    List<FloatingShortcutAction> actions = [
      //图片编辑替换
      if (!multiSelect &&
          firstJsonElement.type == ElementItemType.image &&
          !firstJsonElement.isMaterial() &&
          !((firstJsonElement as ImageElement).isDefaultImage?? false) &&
          !PdfBindInfoManager.instance.elementIsBindPdf(firstCanvasElement.elementId))
        FloatingShortcutAction(FloatingShortcutType.edit, intlanguage('app00336', '编辑'), trackName: '编辑'),

      //图片编辑替换

      if (!multiSelect &&
          firstJsonElement.type == ElementItemType.image &&
          !firstJsonElement.isMaterial() &&
          !PdfBindInfoManager.instance.elementIsBindPdf(firstCanvasElement.elementId))
        FloatingShortcutAction(FloatingShortcutType.replace, intlanguage('app01429', '替换'), trackName: '替换'),

      if (!multiSelect && firstJsonElement.canDoubleClickToEditValue())

        /// 输入
        FloatingShortcutAction(FloatingShortcutType.edit, intlanguage('app00336', '编辑'),
            icon: 'assets/floating_shortcut/floating_shortcut_input.svg', trackName: '编辑'),

      /// 移除
      FloatingShortcutAction(FloatingShortcutType.remove, intlanguage('app00063', '删除'),
          icon: 'assets/floating_shortcut/floating_shortcut_remove.svg', trackName: '删除'),

      /// 复制
      if (firstJsonElement.isLock != 1)
        FloatingShortcutAction(FloatingShortcutType.copy, intlanguage('app00361', '复制'),
            icon: 'assets/floating_shortcut/floating_shortcut_copy.svg', trackName: '复制'),

      /// 旋转
      if (firstJsonElement.isLock != 1 && !hasTable)
        FloatingShortcutAction(FloatingShortcutType.rotate, intlanguage('app00358', '旋转'),
            icon: 'assets/floating_shortcut/floating_shortcut_rotate.svg', trackName: '旋转'),

      /// 多选
      if (moreOnCanvas && !multiSelect && firstJsonElement.isLock != 1)
        FloatingShortcutAction(FloatingShortcutType.multiSelect, intlanguage('app01411', '多选'),
            isShowStar: true, trackName: '多选'),

      /// 对齐
      if (firstJsonElement.isLock != 1)
        FloatingShortcutAction(FloatingShortcutType.align, intlanguage('app00168', '对齐'), trackName: '对齐'),

      /// 分布
      if (multiSelect && selectedCanvasElements!.length > 2 && firstJsonElement.isLock != 1)
        FloatingShortcutAction(FloatingShortcutType.distributed, intlanguage('app01007', '间距'),
            isShowStar: true, trackName: '间距'),

      /// 锁定
      firstJsonElement.isLock == 1
          ? FloatingShortcutAction(FloatingShortcutType.lock, intlanguage('app01063', '解锁'),
              icon: 'assets/floating_shortcut/floating_shortcut_unlock.svg', trackName: '解锁')
          : FloatingShortcutAction(FloatingShortcutType.lock, intlanguage('app00362', '锁定'),
              icon: 'assets/floating_shortcut/floating_shortcut_lock.svg', trackName: '锁定'),
    ];
    return actions;
  }

  double _getCacheWidth() {
    double cacheWidth = 0;
    String key = _getCurrentActionKey();
    _logger.log("===========actionKey: $key, 是否多选： $multiSelect");
    if (key.isNotEmpty && floatWidthCache.containsKey(key)) {
      cacheWidth = floatWidthCache[key]!;
      _logger.log("===========检查宽度缓存: $cacheWidth");
    }
    return cacheWidth;
  }

  String _getCurrentActionKey() {
    String key = "";
    if (currentActions?.isNotEmpty ?? false) {
      key = currentActions!.map((e) => e.type.index).join(",");
    }
    return key;
  }

  _actionHandle(FloatingShortcutAction action) {
    _logger.log("=======ActionType: ${action.type}");
    bool needDispatch = true;
    parentAction = action;
    switch (action.type) {
      case FloatingShortcutType.back:
        if (itemDepth == 2) parentAction = null;
        itemDepth--;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.align:
        itemDepth++;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.distributed:
        itemDepth++;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.tab_cell_delete:
        itemDepth++;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.tab_cell_insert:
        itemDepth++;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.multiSelect:
        parentAction = null;
        multiSelect = !multiSelect;
        multiSelectSubStatus = true;
        updateFloatingBar(0);
        break;
      case FloatingShortcutType.select_all:
        if (checkSelectAll?.call() == true) {
          _resetSelectSubStatus();
          updateFloatingBar(0);
        } else {
          needDispatch = false;
        }
        break;
      case FloatingShortcutType.select_all_text:
        if (checkSelectAllText?.call() == true) {
          _resetSelectSubStatus();
          updateFloatingBar(0);
        } else {
          needDispatch = false;
        }
        break;
      case FloatingShortcutType.select_all_line:
        if (checkSelectAllLine?.call() == true) {
          _resetSelectSubStatus();
          updateFloatingBar(0);
        } else {
          needDispatch = false;
        }
        break;
      case FloatingShortcutType.next:

        ///滚动到下一个
        showEndArrow = false;
        showStartArrow = true;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.previous:

        ///滚动到上一个
        showEndArrow = true;
        showStartArrow = false;
        _justFresh();
        needDispatch = false;
        break;
      case FloatingShortcutType.rotate:
        // if (multiSelect == false) {
        //   dismissFloatingBar();
        // }
        break;
      default:
        break;
    }
    if (needDispatch) {
      this.callback?.call(action.type, selectedCanvasElements!);
    }
    if (action.trackName?.isNotEmpty ?? false) {
      if (action.type == FloatingShortcutType.replace) {
        //上层已经添加了埋点了
        return;
      }
      String posCode;
      if (action.actionType == ActionType.distribute) {
        posCode = "108_069_233";
      } else if (action.actionType == ActionType.selectAll) {
        posCode = "108_069_238";
      } else {
        if (selectedCanvasElements?.any((element) {
              return element.data is ImageElement;
            }) ??
            false) {
          return;
        }
        posCode = "108_069_098"; //
      }
      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
      //   "track": "click",
      //   "posCode": posCode,
      //   "ext": {
      //     "b_name": action.trackName,
      //   }
      // });
      TrackUtils.sendTrackingWrapTemplateId({
        "track": "click",
        "posCode": posCode,
        "ext": {
          "b_name": action.trackName,
        }
      });
    }
  }

  _justFresh() {
    if (null != _overlayEntry) {
      _overlayEntry!.markNeedsBuild();
    }
  }
}
