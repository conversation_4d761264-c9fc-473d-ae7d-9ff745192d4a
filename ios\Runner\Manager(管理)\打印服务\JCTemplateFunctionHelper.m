//
//  JCPrintPortalsHelper.m
//  Runner
//
//  Created by aiden on 2025/4/22.
//

#import "JCTemplateFunctionHelper.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "FlutterBoostUtility.h"
#import "JCFontManager.h"

@implementation JCTemplateFunctionHelper

+ (void)getLayoutTemplate:(JCTemplateData *)templateDetail callback:(void(^)(JCTemplateData *))callback {
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"layoutTemplate" arguments:templateDetail.toJSONString result:^(id value) {
    if([value isKindOfClass:[NSString class]]){
      if(STR_IS_NIL(value)){
        callback(templateDetail);
      }else{
        JCTemplateData *layoutTemplate = [[JCTemplateData alloc] initWithString:value error:nil];
        callback(layoutTemplate);
      }
    }else{
      callback(templateDetail);
    }
  }];
}

+ (void)toEdit:(JCTemplateData *)templateDetail{
  XYBlock readyToEditBlock = ^(JCTemplateData *templateData){
    UIViewController *currentVC = [XYTool getCurrentVC];
    BOOL is_ableEdit_Template = [templateData checkTemplateComplate];
    if(is_ableEdit_Template){
        TemplateSource source = TemplateSource_New;
        JCTemplateData *data = [[JCTemplateData alloc] initWithString:templateData.toJSONString error:nil];
        if ([self isTemplateSelf:data]) {
            source = TemplateSource_Mine;
        }else{
            data = [JCTMDataBindGoodsInfoManager templateDataWith:data goodInfo:nil];
        }
        if(![self isTemplateSelf:data] && data.idStr.length < 12){
            data.originTemplateId = data.idStr;
        }
        // 去掉重置操作 画板那边有重置操作 -wy
//        [data resetUnDownloadVipFontResource];

        if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:data.templateVersion] == false){
            [self showTemplteNeedUpgrade:data];
            return;
        }
        if([data isNewOrOldCommodityTemplate] && ((NETWORK_STATE_ERROR ) || m_currentServerState == ServerState_Stop)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001136", @"网络该模板不支持离线使用，请连接网络！")];
            return;
        }
        NSMutableArray *navPushControllers = [NSMutableArray array];
        for (UIViewController *viewController in currentVC.navigationController.viewControllers) {
            if(![viewController isKindOfClass:[FBFlutterViewContainer class]] || ([viewController isKindOfClass:[FBFlutterViewContainer class]] && ![((FBFlutterViewContainer *)viewController).name isEqualToString:@"canvas"])){
                if([viewController isKindOfClass:[self class]] && ![viewController isEqual:self]){
                    continue;
                }else{
                    [navPushControllers addObject:viewController];
                }
            }else if([viewController isEqual:currentVC.navigationController.jk_rootViewController]){
                [navPushControllers addObject:viewController];
            }
        }
        currentVC.navigationController.viewControllers = navPushControllers;
        [JCFlutter2NativeHandler toFlutterCanvasWith:data type:source];
        // 模型为模版且当前内部包含标签纸(模版中存在的标签纸数据)
        if (!STR_IS_NIL(data.profile.extrain.labelId)) {
            // 5.8.2新建版本更改为无需登录，保存使用记录, 拆分出模版内的标签纸数据
            uploadMostRecentlyUsedTemplate(data.profile.extrain.labelId);
//            [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:data success:^(JCTemplateData *labelData) {
//              if (labelData) {
//                  labelData.recentUserId = m_userModel.userid;
//                  [JCTemplateDBManager db_insertOrUpdateTemplateData:labelData];
//              }
//            } field:^(id x) {
//              
//            }];
        }
    }else{
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        }else{
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000661", @"加载失败")];
        }
      });
      return;
    }
  };
  [self getGrayBlockDetailModel:templateDetail back:^(bool isFromPC) {
    if(isFromPC){
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01393", @"仅支持PC端编辑此模板")];
    }else{
      [self getTemplateDetailRequest:templateDetail complate:^(JCTemplateData *requestData) {
          if(requestData != nil){
            [self checkFontDownload:requestData complate:^{
              readyToEditBlock(requestData);
            }];
          }else{
            [self checkFontDownload:requestData complate:^{
              readyToEditBlock(templateDetail);
            }];
          }
      }];
    }
  }];
  uploadLogInfoFlutter([templateDetail checkTemplateDetailUnComplateReason], @"templateDetail", ^(id x,id y){},J_get_sls_Log_Token);
}

+(void)getGrayBlockDetailModel:(JCTemplateData*)data back:(void(^)(bool))backBlock{
    __block BOOL fromPc = false;
    NSArray * list = [data.externalData objectForKey:@"list"];
    if(list != nil && [list isKindOfClass:[NSArray class]] && list.count > 0 && [data.platformCode isEqualToString:@"CP001PC"]){
        fromPc = true;
    }
    [JCGrayManager gotoGrayModulePage:GrayModuleDrawingBoard routeBlock:^(JCGrayModuleModel * grayModel) {
        if(grayModel.branchType == GrayBranchA){
            //新版pc端保存的模板，在旧画板+新版的版本应该不能打开
            if([data isFromPcExcel] && data.profile.extrain.templateType.integerValue !=2){
                fromPc = true;
            }
        }
        backBlock(fromPc);
    }];
}

+ (void)toPrint:(JCTemplateData *)templateDetail{
    XYBlock readyToPrintBlock = ^(JCTemplateData *toPrintData){
        UIViewController *currentVC = [XYTool getCurrentVC];
        if([toPrintData isNewOrOldCommodityTemplate] && ((NETWORK_STATE_ERROR ) || m_currentServerState == ServerState_Stop)){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001136", @"网络该模板不支持离线使用，请连接网络！")];
            return;
        }
        BOOL is_ableEdit_Template = [toPrintData checkTemplateComplate];
        if(!is_ableEdit_Template){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000354", @"网络异常")];
          return;
        }
        JCTemplateData *currentData = toPrintData;
        if([currentData shouldCurveTextUpdrage]){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01439", @"提醒") message:XY_LANGUAGE_TITLE_NAMED(@"app100000082", @"限时免费体验已结束，为保证您的正常使用，请更新APP到最新版本。") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app100000081", @"不用了") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00487", @"更新") cancelBlock:nil sureBlock:^{
                [[UIApplication sharedApplication] openURL:XY_URLWithString(app_download_Url) options:@{} completionHandler:^(BOOL success) {}];
            } alertType:3];
            return;
        }
        XYNormalBlock toPrintBlock = ^(){
            [JCPDFPrintSourceManager sharedInstance].pdfBindInfos = nil;
            [DrawBoardInfo updateInfoWith:currentData];
            NSMutableArray *navPushControllers = [NSMutableArray array];
            for (UIViewController *viewController in currentVC.navigationController.viewControllers) {
                if(![viewController isKindOfClass:[FBFlutterViewContainer class]] || ([viewController isKindOfClass:[FBFlutterViewContainer class]] && ![((FBFlutterViewContainer *)viewController).name isEqualToString:@"printSetting"] && ![((FBFlutterViewContainer *)viewController).name isEqualToString:@"printSettingDialog"])){
                    if([viewController isKindOfClass:[self class]] && ![viewController isEqual:self]){
                        continue;
                    }else{
                        [navPushControllers addObject:viewController];
                    }
                }else if([viewController isEqual:currentVC.navigationController.jk_rootViewController]){
                    [navPushControllers addObject:viewController];
                }
            }
            currentVC.navigationController.viewControllers = navPushControllers;
            [[JCPrintManager sharedInstance] doPrintWithTemplateModel:@[currentData]
                                                             uniAppId:@""
                                                           printScene:JCPrintSceneMyTemplate
                                                            historyId:nil
                                                        printComplate:^(id x) {

            }];
        };
        if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:currentData.templateVersion] == false){
            [self showTemplteNeedUpgrade:currentData];
            return;
        }
        //检查是否存在实时时间
        if([currentData checkHasVipTraiTime] && !m_user_vip){
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return;
            }
            [FlutterBoostUtility gotoTransparentFlutterPage:@"vipTrial" arguments:@{@"isPresent": @YES,@"isAnimated": @NO,@"vipTrialCode": @"INSTANT_TIME_PRINT"} onPageFinished:^(NSDictionary *dic) {
                NSNumber *isAbleToPrint = dic[@"isAbleToPrint"];
                UIViewController *vc = [XYTool getCurrentVC];
                if([vc isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)vc).name isEqualToString:@"vipTrial"]){
                  [vc dismissViewControllerAnimated:NO completion:^{
                      UIViewController *vc = [XYTool getCurrentVC];
                      if(isAbleToPrint.boolValue){
                          toPrintBlock();
                      }
                  }];
                }else{
                    toPrintBlock();
                }
            }];
            return;
        }
        //检查是否存在其他VIP资源场景弹窗
        if([currentData shouldShowVip]){
            jc_is_support_vip = YES;
            JC_TrackWithparms(@"click",@"012_082_106",(@{}));
            [JCIAPHelper openViewWithAlert:currentVC needOpenTip:YES isUseVipSource:YES success:^{
              toPrintBlock();
            } failure:^(NSString *msg, id model) {
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            } sourceInfo:@{@"sourcePage":@"012"}];
            return;
        }
        toPrintBlock();
    };
  [self getGrayBlockDetailModel:templateDetail back:^(bool isFromPC) {
    if(isFromPC){
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01393", @"仅支持PC端编辑此模板")];
    }else{
      [self getTemplateDetailRequest:templateDetail complate:^(JCTemplateData *requestData) {
        if(requestData != nil){
          [self checkFontDownload:requestData complate:^{
              readyToPrintBlock(requestData);
          }];
        }else{
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000661", @"获取失败")];
        }
      }];
    }
  }];
}

+ (BOOL)isTemplateSelf:(JCTemplateData *)templateData{
    BOOL isMyTemplate = YES;
    NSString *templateUserId = templateData.profile.extrain.userId;
    if((templateData.isCloudTemplate ||
       ![templateUserId isEqualToString:m_userModel.userId]) && templateData.localType != JCLocalType_OffLineCreate){
        isMyTemplate = NO;
    }
    return isMyTemplate;
}

+ (void)getTemplateDetailRequest:(JCTemplateData *)templateDetail complate:(XYBlock)complateCallback{
  __block MBProgressHUD *progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
  progressHUD.label.text = @"";
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetTemplateDetail" arguments:@{
    @"templateId":templateDetail.idStr,@"needUpdateDb":@YES
  } result:^(NSString *value) {
    [progressHUD hideAnimated:YES];
    if ([value isKindOfClass:[NSString class]] && !STR_IS_NIL(value)) {
      NSError *error;
      JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:value error:&error];
      if(templateData == nil){
        complateCallback(nil);
        return;
      }
      if(STR_IS_NIL(templateData.name)){
        templateData.name = templateDetail.name;
      }
      NSString * excelHash = [templateDetail getExcelHash];
      if(!STR_IS_NIL(excelHash) && (NETWORK_STATE_ERROR)){
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"hasExcelCache" arguments:@{@"hash":excelHash} result:^(NSNumber * isHaveCache) {
            if(NETWORK_STATE_ERROR && [isHaveCache boolValue] == false){
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                  [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000354", @"网络异常")];
                });
                return;
            }else{
              complateCallback(templateData);
            }
        }];
      }else{
        complateCallback(templateData);
      }
    } else {
      complateCallback(nil);
    }
  }];
}

+ (void)getTemplateDetailRequestById:(NSString *)templateId complate:(XYBlock)complateCallback needLoading:(BOOL)isNeedLoading{
  __block MBProgressHUD *progressHUD = nil;
  if(isNeedLoading){
    progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    progressHUD.label.text = @"";
  }
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetTemplateDetail" arguments:@{
    @"templateId":templateId,@"needUpdateDb":@YES
  } result:^(NSString *value) {
    [progressHUD hideAnimated:YES];
    if ([value isKindOfClass:[NSString class]] && !STR_IS_NIL(value)) {
      NSError *error;
      JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:value error:&error];
      if(templateData == nil){
        complateCallback(nil);
        return;
      }
      NSString * excelHash = [templateData getExcelHash];
      if(!STR_IS_NIL(excelHash) && (NETWORK_STATE_ERROR)){
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"hasExcelCache" arguments:@{@"hash":excelHash} result:^(NSNumber * isHaveCache) {
            if(NETWORK_STATE_ERROR && [isHaveCache boolValue] == false){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000354", @"网络异常")];
                return;
            }else{
              complateCallback(templateData);
            }
        }];
      }else{
        complateCallback(templateData);
      }
    } else {
      complateCallback(nil);
    }
  }];
}


+ (void)checkFontDownload:(JCTemplateData *)templateData complate:(XYNormalBlock)complate{
  NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:templateData].count;
  NSInteger needChangLangCount = [[JCFontManager sharedManager] needChangLangDownloadFont:templateData].count;
  if([[JCFontManager sharedManager] isNeedDownloadFonts:templateData]){
      if(noFontCount != 0){
          if(needChangLangCount == 0){
              NSInteger noFontCount = [[JCFontManager sharedManager] needDownloadFont:templateData].count;
              NSString *descrpString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01217",@"字体缺失，是否需要下载")];
              [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                if (complate) complate();
              } sureBlock:^{
                [[JCFontManager sharedManager] downloadAllFonts:templateData downloadComplateBlock:^{
                    if (complate) complate();
                }];
              } alertType:3];
          }else{
              NSString *fontDownloadString = [NSString stringWithFormat:@"%ld %@",noFontCount,XY_LANGUAGE_TITLE_NAMED(@"app01246",@"字体需下载")];
              NSString *needChangeLangString = [NSString stringWithFormat:@"%ld %@",needChangLangCount,XY_LANGUAGE_TITLE_NAMED(@"app01247",@"字体需切换语言后下载")];
              NSString *descrpString = [NSString stringWithFormat:@"%@\n%@",fontDownloadString,needChangeLangString];
              [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01220",@"下载") cancelBlock:^{
                if (complate) complate();
              } sureBlock:^{
                [[JCFontManager sharedManager] downloadAllFonts:templateData downloadComplateBlock:^{
                    if (complate) complate();
                }];
              } alertType:3];
          }
      }else{
          if(needChangLangCount != 0){
              NSString *descrpString = [NSString stringWithFormat:@"%@",XY_LANGUAGE_TITLE_NAMED(@"app01248",@"请切换语言下载字体")];
              [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01218",@"字体缺失") message:descrpString cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00707",@"我知道了") cancelBlock:^{
                complate();
              } sureBlock:^{
                complate();
              }];
          }else{
            complate();
          }
      }
  }else{
    complate();
  }
}

+ (void)showTemplteNeedUpgrade:(JCTemplateData *)templateData{
  NSString *updateTip = XY_LANGUAGE_TITLE_NAMED(@"app100000343", @"您的软件版本过低，请升级");
  if([templateData.templateVersion isEqualToString:PCMULTIJSONVersion] && [templateData.platformCode isEqualToString:@"CP001PC"]){
    updateTip = XY_LANGUAGE_TITLE_NAMED(@"app100002075", @"请使用电脑端最新软件打开");
  }
  [MBProgressHUD showToastWithMessageDarkColor:updateTip];
}
@end
