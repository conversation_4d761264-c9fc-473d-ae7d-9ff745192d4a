//
//  JCShopProductVC.m
//  Runner
//
//  Created by xy on 2018/10/17.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopProductVC.h"
#import <AlipaySDK/AlipaySDK.h>
#import "QQLBXScanViewController.h"
#import "StyleDIY.h"
#import "Global.h"
#import "ONImagePickerController.h"

@interface JCShopProductVC ()
@property (nonatomic, copy) NSString *templateType;
@property (nonatomic, copy) NSString *productOneCode;
@end

@implementation JCShopProductVC


- (id)initWithTemplateType:(NSString *)templateType oneCode:(NSString *)productOneCode sourceType:(NSInteger)sourceType{
    self = [super init];
    if(self){
        self.templateType = templateType;
        self.productOneCode = productOneCode;
        if(sourceType == 1){
            self.entrance_type_id = @"2";
        }else if(sourceType == 2){
            if(STR_IS_NIL(productOneCode)){
                self.entrance_type_id = @"4";
            }else{
                self.entrance_type_id = @"5";
            }
        }else if(sourceType == 3){
            if(STR_IS_NIL(productOneCode)){
                self.entrance_type_id = @"6";
            }else{
                self.entrance_type_id = @"7";
            }
        }
        self.isNewCreate = YES;
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }
        self.statusBarStyle = statusBarStyle;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    self.detailStateView.backgroundColor = HEX_RGB(self.statusBackColor);
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    if (self.viewDidDisappearBlock) {
        self.viewDidDisappearBlock();
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self setupWebViewFrame];
    NSString *loadUrl = @"";
    // 存在商城链接，优先加载商城链接
    if (!STR_IS_NIL(self.mallLink)) {
        loadUrl = self.mallLink;
    } else {
        if(STR_IS_NIL(self.productOneCode)){
            loadUrl = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/?" : @"#/home?"];
            if(!STR_IS_NIL([XYCenter sharedInstance].shopHomeHost)){
                if([[XYCenter sharedInstance].shopHomeHost rangeOfString:@"#/"].location != NSNotFound){
                    if([[XYCenter sharedInstance].shopHomeHost rangeOfString:@"?"].location != NSNotFound){
                        loadUrl = [NSString stringWithFormat:@"%@",[XYCenter sharedInstance].shopHomeHost];
                    }else{
                        loadUrl = [NSString stringWithFormat:@"%@?",[XYCenter sharedInstance].shopHomeHost];
                    }
                }else{
                    loadUrl = [NSString stringWithFormat:@"%@#/?",[XYCenter sharedInstance].shopHomeHost];
                }
            }else{
                loadUrl = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/?" : @"#/home?"];
            }
        }else{
            if(!STR_IS_NIL([XYCenter sharedInstance].shopHomeHost)) {
                loadUrl = [[XYCenter sharedInstance].shopHomeHost stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/purchase/:%@?machine_id=%@&type=%@" : @"#/detail?barCode=%@&machine_id=%@&type=%@", self.productOneCode, JC_CURRENT_CONNECTED_PRINTER, self.templateType];
            } else {
                loadUrl = [ShopURL stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/purchase/:%@?machine_id=%@&type=%@" : @"#/detail?barCode=%@&machine_id=%@&type=%@", self.productOneCode, JC_CURRENT_CONNECTED_PRINTER, self.templateType];
            }
        }
    }
    [self loadUrl:loadUrl];
    // 脚本消息处理器已在基类中注册，无需重复注册
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
}

-(void)refreshWeb{
    NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
    NSURL *url = [NSURL URLWithString:myStr];
    NSURLRequest *req = [NSURLRequest requestWithURL:url ];
    [self.webView loadRequest:req];
}

#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    // 处理特殊的返回逻辑
    if ([message.name isEqualToString:@"backHome"]) {
        if(self.navigationController == nil || self.navigationController.viewControllers.count == 1){
            [self dismissViewControllerAnimated:YES completion:^{
                            
            }];
        }else{
            [self.navigationController popViewControllerAnimated:YES];
        }
        if([m_userModel.is_FirstShop isEqualToString:@"1"]){
            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
                
            }];
        }
        return;
    }
    
    // 调用基类的消息处理方法处理其他消息
    [super userContentController:userContentController didReceiveScriptMessage:message];
}

// 扫码方法、图片选择方法等已在基类中实现，无需重复定义

@end
