import 'dart:math';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';

import '/src/model/template_data.dart';
import '/src/widgets/canvas/canvas_theme_widget.dart';
import '/src/model/canvas_element.dart';
import '/src/model/element/text_element.dart';
import '/src/provider/elements_data_changed_notifier.dart';
import '/src/widgets/attribute_panel/comm/font_size_config.dart';
import '/src/widgets/box_frame/canvas_box_frame.dart';
import '/src/widgets/components/group_button/group_button.dart';
import 'package:provider/provider.dart';

class TextAttrPanelMiniWidget extends StatefulWidget {
  List<CanvasElement> canvasElements;
  TextElement firstTextElement;

  VoidCallback refresh;

  TextAttrPanelMiniWidget(
      {super.key, required this.canvasElements, required this.refresh})
      : firstTextElement = canvasElements.first.data as TextElement;

  @override
  State<TextAttrPanelMiniWidget> createState() =>
      _TextAttrPanelMiniWidgetState();
}

class _TextAttrPanelMiniWidgetState extends State<TextAttrPanelMiniWidget> {
  late TemplateData canvasData;
  late String dataPathInnerApp;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  List<String> alignmentIcons = [
    'assets/element/attribute/text_align_left.svg',
    'assets/element/attribute/text_align_center.svg',
    'assets/element/attribute/text_align_right.svg'
  ];

  List<String> styleIcons = [
    'assets/element/attribute/text_style_bold.svg',
    'assets/element/attribute/text_style_underline.svg',
    'assets/element/attribute/text_style_italic.svg',
  ];

  List<String> modeIcons = [
    'assets/element/attribute/text_mode_horizontal.svg',
    // 'assets/element/attribute/text_mode_vertical.svg',
    'assets/element/attribute/text_mode_arc.svg',
  ];

  List<String> fontSizeAdjustIcons = [
    'assets/element/attribute/text_font_size_subtract.svg',
    'assets/element/attribute/text_font_size_add.svg',
  ];

  @override
  Widget build(BuildContext context) {
    double buttonWidth = MediaQuery.sizeOf(context).width / 10;
    List<FontSizeConfig>? fontSizeConfigList = calcMaxFontSize(
        CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble(),
        CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble());
    return Container(
      margin: EdgeInsets.only(top: 8),
      height: 44,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 5.0),
        child: Row(
          children: [
            Expanded(
                flex: 2,
                child: Center(
                  child: GroupButton(
                    /// 取消选中高亮色
                    key: Key('${Random().nextInt(99999999)}'),
                    spacing: 6,
                    onSelected: (index, isSelected) {
                      int configIndex = fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(widget.firstTextElement.fontSize.toDouble());
                      if (index > 0) {
                        configIndex++;
                      } else {
                        configIndex--;
                      }
                      if (configIndex > -1 &&
                          configIndex < fontSizeConfigList.length) {
                        widget.canvasElements.forEach((canvasElement) {
                          TextElement element =
                              canvasElement.data as TextElement;
                          element.fontSize =
                              fontSizeConfigList[configIndex].mm;
                        });
                        _notifyCanvasElementsDataChanged();
                      }
                      setState(() {});
                    },
                    isIconButtons: true,
                    buttonWidth: buttonWidth,
                    buttonHeight: 34,
                    unselectedColor: Colors.transparent,
                    selectedColor: Colors.transparent,
                    borderRadius: BorderRadius.all(Radius.circular(6)),
                    selectedButtons: [],
                    buttons: fontSizeAdjustIcons,
                  ),
                )),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 3),
              width: 1,
              height: 24,
              color: CanvasTheme.of(context).attributeDividerColor,
            ),
            Expanded(
                flex: 3,
                child: Center(
                  child: GroupButton(
                    key: UniqueKey(),
                    isRadio: true,
                    spacing: 6,
                    onSelected: (index, isSelected) {
                      widget.canvasElements.forEach((canvasElement) {
                        TextElement element = canvasElement.data as TextElement;
                        element.textAlignHorizontal = index;
                      });
                      _notifyCanvasElementsDataChanged();
                    },
                    isIconButtons: true,
                    buttonWidth: buttonWidth,
                    buttonHeight: 34,
                    unselectedColor: Colors.transparent,
                    selectedColor: Colors.transparent,
                    borderRadius: BorderRadius.all(Radius.circular(6)),
                    selectedButtons: [
                      alignmentIcons[
                          widget.firstTextElement.textAlignHorizontal!]
                    ],
                    buttons: alignmentIcons,
                  ),
                )),
            Container(
              margin: EdgeInsets.symmetric(horizontal: 3),
              width: 1,
              height: 24,
              color: CanvasTheme.of(context).attributeDividerColor,
            ),
            Expanded(
                flex: 3,
                child: Center(
                  child: GroupButton(
                    key: UniqueKey(),
                    isRadio: false,
                    spacing: 6,
                    onSelected: (index, isSelected) {
                      List<String> fontStyles = ['bold', 'underline', 'italic'];
                      widget.canvasElements.forEach((canvasElement) {
                        TextElement element = canvasElement.data as TextElement;
                        if (isSelected) {
                          if (!element.fontStyle.contains(fontStyles[index])) {
                            element.fontStyle.add(fontStyles[index]);
                          }
                        } else {
                          element.fontStyle.removeWhere(
                              (element) => element == fontStyles[index]);
                        }
                      });

                      _notifyCanvasElementsDataChanged();
                    },
                    isIconButtons: true,
                    buttonWidth: buttonWidth,
                    buttonHeight: 34,
                    unselectedColor: Colors.transparent,
                    selectedColor: Colors.transparent,
                    borderRadius: BorderRadius.all(Radius.circular(6)),
                    selectedButtons: [
                      if (widget.firstTextElement.fontStyle.contains('bold'))
                        styleIcons[0],
                      if (widget.firstTextElement.fontStyle
                          .contains('underline'))
                        styleIcons[1],
                      if (widget.firstTextElement.fontStyle.contains('italic'))
                        styleIcons[2],
                    ],
                    buttons: styleIcons,
                  ),
                )),
          ],
        ),
      ),
    );
  }

  void _notifyCanvasElementsDataChanged() {
    Provider.of<ElementsDataChangedNotifier>(context, listen: false)
        .setDataChangedElements(widget.canvasElements);
  }
}
