import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/pages/meProfile/me_profile_presenter.dart';
// import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/pages/my_template/abstract/getx_controller_abstract.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/pages/my_template/my_template_state.dart';
import 'package:text/pages/my_template/page/personal_template/controller/personal_template_logic.dart';
import 'package:text/pages/my_template/page/public_template/controller/public_template_logic.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/input_field.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/text_utils.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/custom_popup_menu.dart';

import '../../../template/constant/template_version.dart';
import '../../../utils/cachedImageUtil.dart';
import '../controller/search_template_manager.dart';

enum TemplateEventType { select, increaseCount, reduceCount }

class MyTemplateItemWidget extends StatefulWidget {
  final TemplateData model;
  final Function(TemplateEventType eventType) function;
  final Function(bool moreOpeResult)? morefunction;
  final TemplateOperateState operateType;
  final bool isCanEdit;
  final String seachKey;
  final int index;
  final GetxControllerAbstract logic;
  final bool isPreview;
  final bool isSearch;

  MyTemplateItemWidget(this.logic, this.model, this.function,
      {this.isCanEdit = true,
      this.operateType = TemplateOperateState.normal,
      this.seachKey = "",
      this.index = 0,
      this.isPreview = false,
      this.isSearch = false,
        this.morefunction,
      Key? key})
      : super(key: key);

  @override
  _MyTemplateItemWidgetState createState() => _MyTemplateItemWidgetState();
}

class _MyTemplateItemWidgetState extends State<MyTemplateItemWidget> with WidgetsBindingObserver {
  final FocusNode printFocusNode = FocusNode();
  final FocusNode Node = FocusNode();
  final key = GlobalKey();
  double keyboardHeight = 0.0;
  double bottomHeight = 0.0;
  bool isRefresh = true;
  bool isDataSourceTemplate = false;
  bool isTemplateNeedUpgrade = false;
  var keyboardVisibilityController = KeyboardVisibilityController();

  KeyboardActionsConfig _buildConfig() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          focusNode: printFocusNode,
          onTapAction: () {},
        )
      ],
      defaultDoneWidget: Container(
        child: Text(
          intlanguage('app01031', '完成'),
          style: const TextStyle(
            color: ThemeColor.COLOR_4676EE,
            fontSize: 16.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (widget.logic is PublicTemplateLogic) {
      return;
    }
    var logics = widget.logic as PersonalTemplateLogic;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (logics.state.operateType != TemplateOperateState.batchPrint || !mounted) return;
      if (logics.personalTemplateState.selectIndex == widget.index) {
        ScrollController templateScrollController;
        List<TemplateData> templateList;
        if (widget.isPreview) {
          templateList = widget.logic.state.myTemplateSelectList;
          templateScrollController = widget.logic.state.previewTemplateScrollController;
        } else if (widget.isSearch && widget.logic.state.operateType == TemplateOperateState.batchPrint) {
          final templateSearchResult = Get.find<SearchTemplateManager>().templatesResult;
          templateList = templateSearchResult.value as List<TemplateData>;
          templateScrollController = widget.logic.state.searchTemplateScrollController;
        } else {
          templateList = widget.logic.state.myTemplateList;
          templateScrollController = widget.logic.state.templateScrollController;
        }
        if (widget.index == templateList.length - 1) {
          if (keyboardHeight > MediaQuery.viewInsetsOf(context).bottom) {
            bottomHeight = 0;
            ScrollPosition _position = templateScrollController.position;
            // 重置 ScrollPosition 的滚动范围和位置
            if (_position.activity?.isScrolling == true) {
              _position.setPixels(0); // 将滚动距离设置为0，以像素为单位
              _position.restoreScrollOffset(); // 还原滚动位置
            }
          } else {
            bottomHeight = 300;
          }
        }
        scrollToTextField(key, context);
      }
      keyboardHeight = MediaQuery.viewInsetsOf(context).bottom;
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // 在适当时刻调用该函数
  void scrollToTextField(GlobalKey<State<StatefulWidget>> key, BuildContext context) {
    // 添加简单的安全检查
    if (key.currentContext == null) {
      return;
    }

    final RenderBox? componentBox = key.currentContext!.findRenderObject() as RenderBox?;
    if (componentBox == null) {
      return;
    }

    final componentHeight = MediaQuery.sizeOf(context).height - componentBox.localToGlobal(Offset.zero).dy;
    final heightDifference = keyboardHeight + 100 - componentHeight;
    ScrollController templateScrollController;
    if (widget.isPreview) {
      templateScrollController = widget.logic.state.previewTemplateScrollController;
    } else if (widget.isSearch && widget.logic.state.operateType == TemplateOperateState.batchPrint) {
      templateScrollController = widget.logic.state.searchTemplateScrollController;
    } else {
      templateScrollController = widget.logic.state.templateScrollController;
    }

    // 添加简单的安全检查
    if (!templateScrollController.hasClients) {
      return;
    }

    if (heightDifference > 0) {
      var scrollHeight = templateScrollController.offset + heightDifference;
      templateScrollController.animateTo(scrollHeight, duration: Duration(milliseconds: 300), curve: Curves.ease);
    }
  }

  @override
  Widget build(BuildContext context) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 45) / 2 - 30;
    var itemHeight = itemWidth / widget.model.width! * widget.model.height!;
    var printIds = 0;
    TemplateData? template;
    widget.logic.state.myTemplateSelectList.forEach((element) {
      if (element.id == widget.model.id) {
        template = element;
      }
    });
    List<TemplateData> templateList;
    if (widget.isPreview) {
      templateList = widget.logic.state.myTemplateSelectList;
    } else if (widget.isSearch && widget.logic.state.operateType == TemplateOperateState.batchPrint) {
      templateList = widget.logic.state.myTemplateSelectList;
    } else {
      templateList = widget.logic.state.myTemplateList;
    }
    if (template != null) {
      printIds = template!.printPaper;
    }
    var widgetWidth = widget.isPreview
        ? (MediaQuery.sizeOf(context).width - 45) / 2
        : (widget.isSearch
            ? ((MediaQuery.sizeOf(context).width - 45) / 2 - 30)
            : (MediaQuery.sizeOf(context).width - 162 - 20 - 31));
    if (widget.logic.state.operateType == TemplateOperateState.batchPrint) {
      var personalTemplateLogic = widget.logic as PersonalTemplateLogic;
      isDataSourceTemplate = personalTemplateLogic.checkTemplateDataSource(widget.model);
      isTemplateNeedUpgrade = personalTemplateLogic.isTemplateNeedUpgrade(widget.model);
    } else {
      isDataSourceTemplate = false;
    }
    if (widget.operateType == TemplateOperateState.shareMe) {
      isDataSourceTemplate = widget.model.commodityTemplate;
    }
    return Container(
        width: widgetWidth,
        child: Stack(
          children: [
            GestureDetector(
              onTap: () {
                if (widget.operateType == TemplateOperateState.managerState) {
                  widget.function.call(TemplateEventType.select);
                } else if (widget.operateType == TemplateOperateState.normal ||
                    widget.operateType == TemplateOperateState.shareOut) {
                  bool canClick = DebounceUtil.checkClick(needTime: 1);
                  if(!canClick){
                    return;
                  }
                  widget.logic.toCanvasPage(context, widget.model);
                } else if (widget.operateType == TemplateOperateState.batchPrint) {
                  FocusScope.of(context).unfocus();
                } else if (widget.operateType == TemplateOperateState.shareMe) {
                  if (isDataSourceTemplate) {
                    showCenterToast(context, intlanguage('app100001380', '商品模板，暂不支持打印'));
                    ;
                    return;
                  }
                  bool canClick = DebounceUtil.checkClick(needTime: 1);
                  if(!canClick){
                    return;
                  }
                  widget.logic.toCanvasPage(context, widget.model, isFolderShare: true);
                }
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: Directionality.of(context) == TextDirection.rtl
                        ? EdgeInsets.only(bottom: 0, left: widget.isPreview ? 15 : 0, top: 10)
                        : EdgeInsets.only(bottom: 0, right: widget.isPreview ? 15 : 0, top: 10),
                    decoration: BoxDecoration(
                        border: Border.all(color: ThemeColor.border, width: 0.5),
                        borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          decoration: const BoxDecoration(
                              color: ThemeColor.imageBackground,
                              borderRadius:
                                  BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                          child: Stack(
                            children: [
                              Center(
                                child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        vertical: widget.model.vip! ||
                                                widget.model.hasVipRes! ||
                                                widget.model.commodityTemplate!
                                            ? 20
                                            : 20,
                                        horizontal: 14),
                                    child: Container(
                                      constraints: const BoxConstraints(maxHeight: 242),
                                      height: itemHeight + 4,
                                      child: Stack(
                                        alignment: AlignmentDirectional.center,
                                        children: [
                                          ((widget.model.local_type == 1 ||
                                                      widget.model.local_type == 2 ||
                                                      (Platform.isIOS &&
                                                          (widget.model.local_type == 4 ||
                                                              widget.model.local_type == -1)) ||
                                                      (Platform.isAndroid && widget.model.local_type == -1)) &&
                                                  File(widget.model.localThumbnail).existsSync())
                                              ? localThumbnail()
                                              : networkThumbnalImage(),
                                        ],
                                      ),
                                    )),
                              ),
                              _isVipTemplate(widget.model),
                              _isEditTemplate(widget.model),
                              // (widget.model.local_type == 1 || widget.model.local_type == 2)
                              //     ? Positioned(
                              //         bottom: 0,
                              //         right: 0,
                              //         child: Container(
                              //           padding: EdgeInsets.symmetric(horizontal: 7, vertical: 2),
                              //           child: SvgIcon(
                              //             'assets/images/my_template/native.svg',
                              //           ),
                              //         ),
                              //       )
                              //     : Container()
                            ],
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(bottom: 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 0),
                                child: Text.rich(TextSpan(
                                  children: [
                                    ..._buildTemplateFlagWidgetV2(widget.model),
                                    widget.isSearch
                                        ? _richText(
                                            widget.model.name ?? "",
                                            (isDataSourceTemplate || isTemplateNeedUpgrade)
                                                ? ThemeColor.hint
                                                : Color(0xFF262626),
                                            FontWeight.w600,
                                            13,
                                            widget.seachKey)
                                        : TextSpan(
                                            text: widget.model.name ?? '',
                                            style: const TextStyle(
                                                fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
                                  ],
                                )),
                              ),
                              Stack(
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.max,
                                    // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 0, 0),
                                        child: Container(
                                          width: (widgetWidth -
                                              10 -
                                              (widget.operateType != TemplateOperateState.normal
                                                  ? (widget.operateType != TemplateOperateState.batchPrint ? 30 : 30)
                                                  : 0)),
                                          child: Text(
                                            key: key,
                                            '${JCTextUtils.getRealitySize(widget.model.width?.toStringAsFixed(2) ?? "")}x${JCTextUtils.getRealitySize(widget.model.height?.toStringAsFixed(2) ?? "")}mm',
                                            style: TextStyle(
                                                fontSize: 13,
                                                fontWeight: FontWeight.w400,
                                                color: (isDataSourceTemplate || isTemplateNeedUpgrade)
                                                    ? ThemeColor.hint
                                                    : Color(0xFF999999)),
                                          ),
                                        ),
                                      ),
                                      // _selectStauts(printIds)
                                    ],
                                  ),
                                  SizedBox(
                                    height: (widget.operateType == TemplateOperateState.batchPrint && printIds>0)? 0: 14+25+4,
                                  ),
                                  PositionedDirectional(
                                    bottom: 0,
                                    end: 0,
                                    child: _buildPrintBottomWidget(printIds, context),
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        widget.logic is PersonalTemplateLogic ? _batchPrintBtn(widget.model) : Container()
                      ],
                    ),
                  ),
                  Container(
                    height: widget.index == templateList.length - 1 ? bottomHeight : 0,
                  )
                ],
              ),
            ),
            widget.isPreview
                ? Positioned(
                    right: Directionality.of(context) == TextDirection.rtl ? widgetWidth - 5 - 10 : 5,
                    top: -3,
                    child: GestureDetector(
                      onTap: () {
                        (widget.logic as PersonalTemplateLogic).deleteBatchTemplate(template!.id!);
                      },
                      child: const SvgIcon(
                        'assets/images/goods_lib/icon_good_cancel_select.svg',
                        width: 24,
                        height: 24,
                      ),
                    ))
                : Container(),
          ],
        ));
  }

  Image localThumbnail() {
    // Image thumbnailImage = Image.file(
    //   File(model.localThumbnail),
    //   fit: BoxFit.fitWidth,
    // );

    Image thumbnailImage = Image(
      image: FileImageEx(File(widget.model.localThumbnail)),
      fit: BoxFit.fitWidth,
    );
    return thumbnailImage;
  }

  Container networkThumbnalImage() {
    return Container(
      child: CacheImageUtil().netCacheImage(
          fit: BoxFit.fitWidth,
          imageUrl: widget.model.thumbnail == null
              ? ""
              : widget.model.thumbnail + "?x-oss-process=image/resize,w_500/quality,q_10",
          filterQuality: FilterQuality.high,
          useOldImageOnUrlChange: false,
          cacheKey: widget.model.thumbnail + (widget.model.profile.extra.updateTime ?? ""),
          errorWidget: File(widget.model.localThumbnail).existsSync()
              ? localThumbnail()
              : const SvgIcon(
                  'assets/images/industry_template/home/<USER>',
                )),
    );
  }

  _selectStauts(int printIds) {
    switch (widget.operateType) {
      case TemplateOperateState.managerState:
        return Container(
          padding: EdgeInsets.all(10),
          child: widget.logic.templateSelectState(widget.model)
              ? SvgIcon(
                  'assets/images/my_template/templateSelect.svg',
                  width: 20,
                  height: 20,
                )
              : SvgIcon(
                  'assets/images/my_template/templateUnSelect.svg',
                  width: 20,
                  height: 20,
                ),
        );
      case TemplateOperateState.batchPrint:
        return printIds > 0
            ? Container()
            : GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  var logics = widget.logic as PersonalTemplateLogic;
                  if (isTemplateNeedUpgrade) {
                    if(widget.model.templateVersion == TemplateVersion.PC_MULTI_IMAGE_VERSION) {
                      showCenterToast(context, intlanguage('app100002075', '请使用电脑端最新版本打开'));
                    } else {
                      showCenterToast(context, intlanguage('app100000343', '您的软件版本过低，请升级'));
                    }
                    return;
                  }
                  if (isDataSourceTemplate) {
                    showCenterToast(context, intlanguage('app100001246', '包含数据源和序列号的模板 暂不支持快捷打印'));
                    return;
                  }
                  logics.addTemplateCopies(widget.model);
                  logics.personalTemplateState.selectIndex = widget.index;
                  setState(() {});
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(20, 10, 10, 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.white,
                  ),
                  child: SvgIcon(
                    'assets/images/my_template/add.svg',
                    width: 20,
                    height: 20,
                    color: (isDataSourceTemplate || isTemplateNeedUpgrade)
                        ? ThemeColor.brand.withOpacity(0.5)
                        : ThemeColor.brand,
                  ),
                ),
              );
      case TemplateOperateState.shareMe:
        return CustomPopupMenuButton(
          itemBuilder: (BuildContext context) {
            return [
              _PopupMenuItemForSingle(
                'assets/images/my_template/share.svg',
                intlanguage('app00335', '分享'),
                TemplateEventStatus.share,
              ),

            ];
          },
          child: Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.transparent,
            ),
            child: Icon(
              Icons.more_horiz,
              size: 20,
            ),
          ),
          offset: Offset(Directionality.of(context) == TextDirection.ltr ? -5 : 10, 30),
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          onSelected: (value) {
            widget.logic.templateItemEventBefore(context, value as TemplateEventStatus, widget.model,widget.operateType,resultCallBack: widget.morefunction);
          },
          onOpened: () {
            widget.logic.templateItemMoreClick(widget.model,widget.operateType);
          },
        );
      case TemplateOperateState.normal:
      case TemplateOperateState.shareOut:
        String oneCode = widget.model.getLabelOneCode();
        String labelId = widget.model.profile.extra.labelId ?? "";
        bool showLabelItem = false;
        if (oneCode.isNotEmpty || labelId.isNotEmpty) {
          showLabelItem = true;
        }
        bool showBuyItem = false;
        if(oneCode.isNotEmpty && MeProfilePresenter.shopState != 0) {
          showBuyItem = true;
        }
        return widget.isCanEdit
            ? CustomPopupMenuButton(
                itemBuilder: (BuildContext context) {
                  return [
                    _PopupMenuItem(
                      'assets/images/my_template/re_name.svg',
                      intlanguage('app01321', '重命名'),
                      TemplateEventStatus.reName,
                      isFirst: true,
                    ),
                    _PopupMenuItem(
                      'assets/images/my_template/move.svg',
                      intlanguage('app100001186', '移动到'),
                      TemplateEventStatus.move,
                    ),
                    _PopupMenuItem(
                      'assets/images/my_template/copy.svg',
                      intlanguage('app00361', '复制'),
                      TemplateEventStatus.copy,
                    ),
                    _PopupMenuItem(
                      'assets/images/my_template/share.svg',
                      intlanguage('app00335', '分享'),
                      TemplateEventStatus.share,
                    ),
                    if (showBuyItem)
                      _PopupMenuItem('assets/images/my_template/shopcar.svg', intlanguage('app100001972', '购买标签纸'),
                          TemplateEventStatus.buyLabel,
                          isSectionStart: true),
                    if (showLabelItem)
                      _PopupMenuItem(
                        'assets/images/my_template/info.svg',
                        intlanguage('app100001973', '标签纸信息'),
                        TemplateEventStatus.labelInfo,
                      ),
                    _PopupMenuItem('assets/images/my_template/delete.svg', intlanguage('app00063', '删除'),
                        TemplateEventStatus.delete,
                        isDelete: true)
                  ];
                },
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: Colors.transparent,
                  ),
                  child: Icon(
                    Icons.more_horiz,
                    size: 20,
                  ),
                ),
                offset: Offset(Directionality.of(context) == TextDirection.ltr ? -5 : 10, 30),
                elevation: 3,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                onSelected: (value) {
                  widget.logic.templateItemEventBefore(context, value as TemplateEventStatus, widget.model,widget.operateType,resultCallBack: widget.morefunction);
                },
                onOpened: () {
                  widget.logic.templateItemMoreClick(widget.model,widget.operateType);
                },
              )
            : Container();
      // case TemplateOperateState.shareOut:
      //   return widget.isCanEdit
      //       ? CustomPopupMenuButton(
      //           itemBuilder: (BuildContext context) {
      //             return [
      //               _PopupMenuItem(
      //                 'assets/images/my_template/re_name.svg',
      //                 intlanguage('app01321', '重命名'),
      //                 TemplateEventStatus.reName,
      //                 isFirst: true,
      //               ),
      //               _PopupMenuItem(
      //                 'assets/images/my_template/move.svg',
      //                 intlanguage('app100001186', '移动到'),
      //                 TemplateEventStatus.move,
      //               ),
      //               _PopupMenuItem('assets/images/my_template/delete.svg', intlanguage('app00063', '删除'),
      //                   TemplateEventStatus.delete,
      //                   isDelete: true)
      //             ];
      //           },
      //           child: Container(
      //             padding: const EdgeInsetsDirectional.fromSTEB(20, 0, 10, 0),
      //             decoration: BoxDecoration(
      //               borderRadius: BorderRadius.circular(12),
      //               color: Colors.white,
      //             ),
      //             child: Icon(
      //               Icons.more_horiz,
      //               size: 20,
      //             ),
      //           ),
      //           offset: Offset(Directionality.of(context) == TextDirection.ltr ? -5 : 10, 20),
      //           elevation: 3,
      //           shape: RoundedRectangleBorder(
      //             borderRadius: BorderRadius.circular(12),
      //           ),
      //           onSelected: (value) {
      //             widget.logic.templateItemEventBefore(context, value as TemplateEventStatus, widget.model);
      //           },
      //           onOpened: () {
      //             widget.logic.templateItemMoreClick(widget.model);
      //           },
      //         )
      //       : Container();
      default:
        return Container();
    }
  }

  //右下角布局
  Widget _buildPrintBottomWidget(int printIds, BuildContext context) {
    Widget printBtn = Container();
    switch (widget.operateType) {
      case TemplateOperateState.normal:
      case TemplateOperateState.shareOut:
      case TemplateOperateState.shareMe:
        printBtn = GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            //进入打印界面
            if (widget.operateType == TemplateOperateState.normal ||
                widget.operateType == TemplateOperateState.shareOut) {
              bool canClick = DebounceUtil.checkClick(needTime: 1);
              if(!canClick){
                return;
              }
              widget.logic.toPrintSettingPage(context, widget.model,widget.operateType);
            } else if (widget.operateType == TemplateOperateState.shareMe) {
              if (isDataSourceTemplate) {
                showCenterToast(context, intlanguage('app100001380', '商品模板，暂不支持打印'));
                ;
                return;
              }
              bool canClick = DebounceUtil.checkClick(needTime: 1);
              if(!canClick){
                return;
              }
              widget.logic.toPrintSettingPage(context, widget.model,widget.operateType, isFolderShare: true);
            }
          },
          child: Container(
            padding: EdgeInsetsDirectional.fromSTEB(20, 10, 10, 10),
            child: SvgIcon(
              'assets/images/my_template/icon_print.svg',
              width: 20,
              height: 20,
            ),
          ),
        );

      default:
        printBtn = Container();
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [printBtn, _selectStauts(printIds)],
    );
  }

  _isVipTemplate(TemplateData model) {
    if (model.vip || model.hasVipRes) {
      return PositionedDirectional(
          top: 4,
          start: 4,
          child: Container(
              child: SvgIcon(
            'assets/images/icon_flag_vip_template.svg',
          )));
    } else {
      return Container();
    }
  }

  _isEditTemplate(TemplateData model) {
    if (model.isEdited > 0) {
      return PositionedDirectional(
          top: 4,
          end: 4,
          child: Container(
              padding: EdgeInsetsDirectional.symmetric(horizontal: 5, vertical: 2),
              decoration: BoxDecoration(
                  color: model.isEdited == 2 ? Colors.green : Colors.red,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(5), bottomRight: Radius.circular(5))),
              child: Text(
                model.isEdited == 2 ? "已编辑" : "待编辑",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                ),
              )));
    } else {
      return Container();
    }
  }

  List<WidgetSpan> _buildTemplateFlagWidgetV2(TemplateData model) {
    List<WidgetSpan> widgets = [];
    if (model.local_type == 1 || widget.model.local_type == 2) {
      widgets.add(WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: Padding(
            padding: const EdgeInsetsDirectional.only(end: 2),
            child: SvgIcon(
              'assets/images/icon_flag_offline_template.svg',
            ),
          )));
    }

    if (model.commodityTemplate || model.isCommodity()) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_good_template.svg',
          ),
        ),
      ));
    } else if (model.isExcel()) {
      widgets.add(WidgetSpan(
          alignment: PlaceholderAlignment.middle,
          child: Padding(
            padding: const EdgeInsetsDirectional.only(end: 2),
            child: SvgIcon(
              'assets/images/icon_flag_excel_template.svg',
            ),
          )));
    }
    return widgets;
  }

  _batchPrintBtn(TemplateData model) {
    var logics = widget.logic as PersonalTemplateLogic;
    final TextEditingController printEditController = TextEditingController();
    var id = 0;
    TemplateData? template;
    widget.logic.state.myTemplateSelectList.forEach((element) {
      if (element.id == widget.model.id) {
        template = element;
      }
    });
    if (template != null) {
      id = template!.printPaper;
    }
    printEditController.text = id.toString();
    printEditController.selection =
        TextSelection.fromPosition(TextPosition(offset: printEditController.text.length)); // 设置光标在文本的第 5 个字符之后

    return id > 0 && widget.logic.state.operateType == TemplateOperateState.batchPrint
        ? Row(
            children: [
              Expanded(child: Container()),
              Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(20, 0, 10, 10),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          logics.deleteTemplateCopies(model.id!, printFocusNode);
                          setState(() {});
                        },
                        child: Container(
                          color: ThemeColor.background,
                          padding: const EdgeInsets.all(7.0),
                          child: Icon(
                            Icons.remove,
                            size: 20,
                          ),
                        ),
                      ),
                      Container(
                        width: 53,
                        height: 32,
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: ThemeColor.COLOR_0D000000,
                        ),
                        child: KeyboardActions(
                          config: _buildConfig(),
                          autoScroll: false,
                          disableScroll: true,
                          child: InputField(
                            focusNode: printFocusNode,
                            textEditingController: printEditController,
                            height: 21,
                            maxLength: 3,
                            keyboardType: TextInputType.number,
                            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                            textAlign: TextAlign.center,
                            contentPadding: EdgeInsets.zero,
                            onChanged: (text) {
                              logics.editTemplateCopies(model.id!, int.parse(text));
                              setState(() {});
                            },
                            onTap: () {
                              logics.personalTemplateState.selectIndex = widget.index;
                            },
                            textStyle: const TextStyle(
                              color: ThemeColor.title,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          logics.addTemplateCopies(model);
                          setState(() {});
                        },
                        child: Container(
                          color: ThemeColor.background,
                          padding: const EdgeInsets.all(7.0),
                          child: Icon(
                            Icons.add,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  )),
            ],
          )
        : Container();
  }

  CustomPopupMenuItem _PopupMenuItem(String path, String name, TemplateEventStatus event,
      {bool isDelete = false, bool isFirst = false, bool isSectionStart = false}) {
    return CustomPopupMenuItem(
      height: 5,
      padding: EdgeInsets.zero,
      child: Container(
        // width: 150,
        padding: EdgeInsetsDirectional.symmetric(vertical: 0, horizontal: 0),
        child: Column(
          children: [
            isFirst
                ? Container()
                : Container(
                    width: double.infinity,
                    child: Divider(
                      height: 1,
                      thickness: (isDelete || isSectionStart) ? 4 : 0.5,
                      color: ThemeColor.listBackground,
                    ),
                  ),
            Container(
              padding: isDelete || isFirst
                  ? isFirst
                      ? EdgeInsetsDirectional.fromSTEB(10, 4, 50, 10)
                      : EdgeInsetsDirectional.fromSTEB(10, 10, 50, 4)
                  : EdgeInsetsDirectional.fromSTEB(10, 10, 50, 10),
              child: Row(
                children: [
                  SvgIcon(
                    path,
                    width: 21,
                    height: 21,
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Expanded(
                      child: Text(
                    name,
                    style: TextStyle(
                      color: isDelete ? ThemeColor.brand : ThemeColor.title,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
      ),
      value: event,
    );
  }

  CustomPopupMenuItem _PopupMenuItemForSingle(String path, String name, TemplateEventStatus event) {
    return CustomPopupMenuItem(
      height: 5,
      padding: EdgeInsets.zero,
      child: Container(
        // width: 150,
        padding: EdgeInsetsDirectional.symmetric(vertical: 0, horizontal: 0),
        child: Column(
          children: [
           Container(),
            Container(
              padding: EdgeInsetsDirectional.fromSTEB(10, 4, 50, 4),
              child: Row(
                children: [
                  SvgIcon(
                    path,
                    width: 21,
                    height: 21,
                  ),
                  SizedBox(
                    width: 6,
                  ),
                  Text(
                    name,
                    style: TextStyle(
                      color: ThemeColor.title,
                      fontSize: 16,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      value: event,
    );
  }

  TextSpan _richText(String allStr, Color fontColor, FontWeight fontWeight, double fontSize, String searchKey) {
    String name = allStr;
    String lowerName = name.toLowerCase();
    String lowerSearchKey = searchKey.toLowerCase();
    List<String> components = [];
    List<TextSpan> spans = [];
    List<String> splitComponents = lowerName.split(lowerSearchKey);
    for (int i = 0; i < splitComponents.length; i++) {
      String val = splitComponents[i];
      components.add(val);
      if (i + 1 < splitComponents.length) {
        components.add(lowerSearchKey);
      }
    }

    int index = 0;
    int keyLength = searchKey.length;
    for (int i = 0; i < components.length; i++) {
      if (components[i].compareTo(lowerSearchKey) == 0) {
        String realKey = name.substring(index, index + keyLength);
        spans.add(
            TextSpan(text: realKey, style: TextStyle(color: Colors.red, fontSize: fontSize, fontWeight: fontWeight)));
      } else {
        String realKey = name.substring(index, index + components[i].length);
        if (realKey.length > 0) {
          spans.add(
              TextSpan(text: realKey, style: TextStyle(color: fontColor, fontSize: fontSize, fontWeight: fontWeight)));
        }
      }
      index += components[i].length;
    }
    return TextSpan(children: spans);
  }
}

class FileImageEx extends FileImage {
  int fileSize = 0;

  FileImageEx(File file, {double scale = 1.0}) : super(file, scale: scale) {
    fileSize = file.lengthSync();
  }

  @override
  bool operator ==(dynamic other) {
    if (other.runtimeType != runtimeType) return false;
    final FileImageEx typedOther = other;
    return file.path == typedOther.file.path && scale == typedOther.scale && fileSize == typedOther.fileSize;
  }
}
