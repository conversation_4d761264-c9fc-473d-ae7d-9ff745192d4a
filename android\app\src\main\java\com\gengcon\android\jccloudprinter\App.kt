package com.gengcon.android.jccloudprinter

import JCApiManager
import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ProcessUtils
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEngineManager
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEventRegister
import com.gengcon.android.jccloudprinter.parser.NetSecurityConfigDomainConfig
import com.gengcon.android.jccloudprinter.parser.NetSecurityConfigDomainWithPin
import com.gengcon.android.jccloudprinter.parser.NetSecurityConfigParser
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connectui.DeviceConnectNewActivity
import com.gengcon.connectui.helper.DeviceDetailsHelper
import com.gengcon.connectui.helper.DeviceUpgradeHelper
import com.gengcon.print.draw.print.PrintHelper
import com.gengcon.print.draw.print.PrintManager
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.gengcon.print.draw.proxy.RfidColorProxy
import com.gengcon.print.draw.view.operate.menubar.detail.common.FontManagerHelper
import com.gengcon.www.jcprintersdk.JCAPI
import com.gengcon.www.jcprintersdk.log.PrintConnLog
import com.gengcon.www.jcprintersdk.log.PrintLog
import com.gu.toolargetool.TooLargeTool
import com.idlefish.flutterboost.FlutterBoost
import com.idlefish.flutterboost.containers.FlutterBoostActivity
import com.jc.repositories.webview.library.WVSdk
import com.jc.repositories.webview.library.eventbus.AppLifecycleEvent
import com.jc.repositories.webview.library.eventbus.ShowTabEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.view.JCWebViewFactory
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.riskshield.RiskShieldHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.common.skin.SkinHelper
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.niimbot.storelibrary.excel.activity.ExcelHookActivity
import com.niimbot.templatecoordinator.manager.TemplateManager
import com.niimbot.utiliylibray.util.GetAndroidUniqueMark
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.viplibrary.VipHelper
import com.niimbot.viplibrary.service.VipService
import com.nimmbot.business.livecode.CapAppHelper
import com.nimmbot.business.livecode.CapAppHostActivity
import com.nimmbot.business.livecode.service.CapAppEventProcessor
import com.qyx.languagelibrary.utils.TextHookUtil
import com.suofang.jcbqdy.network.signature.flutter_net_signature.FlutterNetSignatureInterceptor
import com.tencent.bugly.crashreport.CrashReport
import dagger.hilt.android.HiltAndroidApp
import io.flutter.app.FlutterApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.baselibrary.JCApi
import melon.south.com.baselibrary.base.JCPrintApplication
import melon.south.com.baselibrary.base.LaboratoryActivity
import melon.south.com.baselibrary.intercepter.HeaderInterceptor
import melon.south.com.baselibrary.local.util.BannerLocalUtils
import melon.south.com.baselibrary.local.util.DevicesLocalUtils
import melon.south.com.baselibrary.local.util.DevicesSeriesLocalUtils
import melon.south.com.baselibrary.local.util.PrintDataLocalUtils
import melon.south.com.baselibrary.share.ShareBusiness
import melon.south.com.baselibrary.util.AppLanguageUtils
import melon.south.com.mainlibrary.util.NpsVipUtil
import melon.south.com.mainlibrary.v.AdActivity
import melon.south.com.mainlibrary.v.event.FirmwareCanUpdateEvent
import melon.south.com.templatelibrary.dao.TemplateIndustryLocalUtils
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import okhttp3.CertificatePinner
import org.greenrobot.eventbus.EventBus

@HiltAndroidApp
class App : FlutterApplication() {
    companion object {
        const val TAG_CURRENT_VERSION_CODE = "app_version_code"
        const val TAG_UPGRADE_VERSION_ID = "upgrade_version_id"
    }

    val applicationWrapper: JCPrintApplicationWrapper = JCPrintApplicationWrapper()
    private val appConnectManager = AppConnectManager.getInstance(this, applicationWrapper)
    var appEventProcessor = AppEventProcessor(this)

    override fun onCreate() {
        super.onCreate()
        Log.e("=================", "云打印application onCreate")

        if(AppUtils.isAppDebug()) {
            Log.i("RefactoringAutoConnect", "start app")
        }
        applicationWrapper.languageUpdateApply = {
            FlutterEventRegister.notifyEventLanguageType(it)
        }
        applicationWrapper.startMainActivityListener = {
            MainScope().launch {
                delay(1000)
                val device = RFIDConnectionProxyManager.connectedDevice
                val updateBean = DeviceDetailsHelper.updateBean
                if(device != null && !updateBean?.url.isNullOrEmpty() && updateBean?.isForceUpdate() == true)
                {
                    applicationWrapper.mCurShowActivityReference.get()?.apply {
                        if(this.javaClass != LaunchActivity::class.java && this.javaClass != AdActivity::class.java)
                            DeviceUpgradeHelper.showFirmwareUpgradeDialog(
                                this,
                                device.softwareVersion,
                                device.hardwareVersion,
                                updateBean,
                                device.isForbiddenPowerInUpgrade()
                            )
                    }
                }
            }
        }
        ShareBusiness.miniAppCateringClipboardListener = { content, activity ->
            CapAppHelper.clipboardContent = content
        }
        ShareBusiness.c1TemplateShareCodeListener = {
            FlutterEventRegister.notifyC1TemplateShareCode(it)
        }
        applicationWrapper.onCreate(this)
        CapAppEventProcessor.imageDataForMiniAppCatering = {
            appEventProcessor.imageDataForMiniAppCatering(it)
        }
        DeviceConnectNewActivity.modifyAliasListener = {
            CapAppHelper.sendMachineAliasChangeEvent(it)
        }
        //极光推送注册,测试暂时放这里 todo
        applicationInit()
    }
    private fun applicationInit() {
        LogUtils.e("=================applicationInit-->currentProcessName: ${ProcessUtils.getCurrentProcessName()}")
        if (!ProcessUtils.isMainProcess()) return
        FlutterEngineManager.init(this)
        TooLargeTool.startLogging(this)
      //  JCAPI.setLog(BuildConfig.DEBUG)
        if (AppUtils.isAppDebug()) {
            PrintLog.init(this)
            PrintConnLog.init(this)
            if (LaboratoryActivity.getSDKLogSwitch()) {
                PrintLog.enableDebug()
                PrintConnLog.enableDebug()
            }
        }
        TextHookUtil.getInstance().forceLTR = LaboratoryActivity.isForceLTR()
        if (ProcessUtils.isMainProcess()) {
            checkUpdate()
            appConnectManager.initConnectManager()
        }
        //webview初始化
        WVSdk.init(applicationContext)
        //初始化系列数据
        DevicesSeriesLocalUtils.setCurrentSeriesModuleByLanguage()
        FontUtils.onInitCompleteListener = { FontManagerHelper.initMyFonts() }
        FontUtils.initFontInfo(
            this,
            JCApi.GET_FILE_LIST_URL,
            JCApi.GET_FONT_CLASSIFY_URL,
            JCApi.FONT_LIST_CDN_URL,
            true
        )

        register()

        initDevices()

//        JPushInterface.setDebugMode(BuildConfig.DEBUG)
//        if (NiimbotGlobal.hasEnsurePrivacy()) {
//            AppInitManager.thirdLibInit(this)
//            JCollectionAuth.setAuth(this, true)
//        }else{
//            JCollectionAuth.setAuth(this, false) // 后续初始化过程将被拦截
//        }
//        JPushInterface.init(this)
        checkAppSign()
        SkinHelper.init(TextHookUtil.getInstance().languageName)
        RfidColorProxy.rfidColorUpdate = {
            FlutterEventRegister.notifyRfidColorUpdate(it)
        }

        SyncTemplatePresenter.setListener()
        RFIDConnectionProxyManager.machineCascadeDetailListener = {
            val device = RFIDConnectionProxyManager.connectedDevice
            val updateBean = DeviceDetailsHelper.updateBean
            if(device != null && !updateBean?.url.isNullOrEmpty())
            {
                EventBus.getDefault().post(FirmwareCanUpdateEvent(true, any2Json(updateBean)))
                if (updateBean!!.isForceUpdate()) {
                    applicationWrapper.mCurShowActivityReference.get()?.let {
                        if(it.javaClass != LaunchActivity::class.java && it.javaClass != AdActivity::class.java) {
                            DeviceUpgradeHelper.showFirmwareUpgradeDialog(
                                it,
                                device.softwareVersion,
                                device.hardwareVersion,
                                updateBean,
                                device.isForbiddenPowerInUpgrade()
                            )
                        }
                    }
                }
            }
        }
        RFIDConnectionProxyManager.clearDeviceDetailListener = {
            DeviceDetailsHelper.clearMachineCascadeDetail()
        }
        RiskShieldHelper.requestAntiCounterfeiKey = {
            var antiCounterfeiKey = ""
            val connectDevice = RFIDConnectionProxyManager.connectedDevice
            if (it.isNotEmpty() && connectDevice != null && connectDevice.deviceName == it) {
                if (connectDevice.antiCounterfeiKey == null) {
                    antiCounterfeiKey = PrintManager.getAntiCounterfeiKey()
                    if (RFIDConnectionProxyManager.connectedDevice?.deviceName == it) {
                        connectDevice.antiCounterfeiKey = antiCounterfeiKey
                    }
                } else {
                    antiCounterfeiKey = connectDevice.antiCounterfeiKey!!
                }
            }
            antiCounterfeiKey
        }
        VipHelper.getCurrentActivity = {
            FlutterBoost.instance().currentActivity()
        }
        PrintDataLocalUtils.uploadPrintDataLog = {
            FlutterEventRegister.notifyUploadPrintDataLog()
        }
//        PrintHelper.printFinishListener = {
//            FlutterEventRegister.notifyPrintFinish()
//        }
        ExcelHookActivity.checkFromC1Page = {
            DeviceC1Helper.isC1Page
        }
        handleShopBusiness()
        MainScope().launch {
            NpsVipUtil.getAppConfig()
        }
    }

    private fun handleShopBusiness() {
        ShopManager.getShopSiteInfo()
        ShopManager.shopInfoListener = { delay, info ->
            if (delay) {
                GlobalScope.launch(Dispatchers.Main) {
                    delay(2000)
                    FlutterEventRegister.notifyShopInfo(info)
                }
            } else {
                FlutterEventRegister.notifyShopInfo(info)
            }
        }
        BannerLocalUtils.getSiteCodeFun = {
            ShopManager.getShopSiteCode()
        }
        JCApiManager.getSiteCodeFun = {
            ShopManager.getShopSiteCode()
        }
        VipHelper.getShopCoupon = {
            ShopManager.SHOP_MY_COUPON
        }
        VipService.getSiteCodeFun = {
            ShopManager.getShopSiteCode()
        }
        HeaderInterceptor.Companion.getShopSiteCode = {
            ShopManager.getShopSiteCode()
        }
    }


    override fun onTerminate() {
        super.onTerminate()
        JCWebViewFactory.getInstance().destroyWebView(this)
        appConnectManager.onDestroy()
        unRegister()
        FlutterEngineManager.onDestroy()
    }


    private fun initDevices() {
        DevicesLocalUtils.getModule {
            LogUtils.e("connectDevice: ${any2Json(RFIDConnectionProxyManager.connectedDevice)}")
        }
    }

    private fun checkUpdate() {
        //检测版本号，无版本号则是从3.5.x升上来的，需清理登陆信息
        val currentVersionCode = PreferencesUtils.getInt(TAG_CURRENT_VERSION_CODE, -1)
        if (currentVersionCode < 0) {
           LoginDataEnum.unLogin()
            PreferencesUtils.put(
                TAG_CURRENT_VERSION_CODE,
                BuildConfig.VERSION_CODE
            )
        }

        if (BuildConfig.VERSION_CODE > currentVersionCode) {
            FontUtils.deleteDefault()
            PreferencesUtils.put(
                TAG_CURRENT_VERSION_CODE,
                BuildConfig.VERSION_CODE
            )
        }

        //检测是否内测升级用户
        val upgradeVersionId = PreferencesUtils.getString(TAG_UPGRADE_VERSION_ID)
        if (BuildConfig.VERSION_CODE > currentVersionCode && !upgradeVersionId.isNullOrEmpty() && !upgradeVersionId.equals(
                "null",
                true
            )
        ) {
            JCApiManager.postApkUpdateCount(upgradeVersionId)
            PreferencesUtils.remove(TAG_UPGRADE_VERSION_ID)
        }
    }


    private fun register() {
        appEventProcessor.register()
        TemplateManager.register()
    }

    private fun unRegister() {
        appEventProcessor.unRegister()
        TemplateManager.unRegister()
    }


    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(
            AppLanguageUtils.attachBaseContext(
                base,
                TextHookUtil.getInstance().locale.toString()
            )
        )


        PreferencesUtils.init(this)
        //   AppInitManager.initBugly(this)
        applicationWrapper.attachBaseContext(base)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        val systemLocale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            newConfig.locales.get(0)
        } else {
            newConfig.locale
        }
        onLanguageChange()
    }

    private fun onLanguageChange() {

        try {
            val systemLocale = TextHookUtil.getInstance().languageName
            AppLanguageUtils.changeAppLanguage(
                this,
                AppLanguageUtils.getSupportLanguage(systemLocale)
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 检查应用签名
     */
    private fun checkAppSign() {
        if (NiimbotGlobal.isGoogleChannel()) return
        val signList = AppUtils.getAppSignaturesSHA1()
        if (signList.isNullOrEmpty() || signList[0] != com.gengcon.android.jccloudprinter.BuildConfig.App_SHA1) {
            showToast("非法安装包，请重新从应用市场下载")
            CrashReport.postCatchedException(
                Throwable(
                    "非法安装包，请重新从应用市场下载, deviceId:${
                        GetAndroidUniqueMark.getUniqueId(
                            this
                        )
                    }"
                )
            )
            CoroutineScope(Dispatchers.IO).launch {
                delay(2000)
                AppUtils.exitApp()
            }
        }
    }

    inner class JCPrintApplicationWrapper : JCPrintApplication() {
        override fun onActivityStarted(activity: Activity?) {
            super.onActivityStarted(activity)
        }

        override fun onBackground() {
            super.onBackground()
            appConnectManager.onBackground()
            PrintManager.notifySDKIfBackground(true)

            BuriedHelper.trackEvent("appback", "001")
            FlutterEventRegister.notifyFlutterAppBackground()
            AppInitManager.isOnBackground = true
            EventBus.getDefault().post(AppLifecycleEvent(true))
        }

        override fun onForeground() {
            super.onForeground()
            appConnectManager.onForeground()
            AppInitManager.isOnBackground = false
            PrintManager.notifySDKIfBackground(false)
//            if (LoginDataEnum.isLogin) {
//                TemplateIndustryLocalUtils.getRecentUsedList(10) { successForServer, it ->
//                }
//            }
            BuriedHelper.trackEvent(
                "appfront", "001", hashMapOf(
                    Pair("is_device_rooted", NiimbotGlobal.isDeviceRooted()),
                    Pair("userId", LoginDataEnum.id),
                    Pair("device_id", GetAndroidUniqueMark.getUniqueFromSP(application))
                )
            )
            FlutterEventRegister.notifyFlutterAppForeground()
            EventBus.getDefault().post(AppLifecycleEvent(false))
        }

        override fun onAppFinish() {
            super.onAppFinish()

            BuriedHelper.trackEvent("append", "001")
        }

        override fun afterAddInterceptor() {
            super.afterAddInterceptor()
            DokitOkGo.addInterceptor(FlutterNetSignatureInterceptor())
            rebuildPinnerClient()


        }

        private fun rebuildPinnerClient() {
            if (com.gengcon.android.jccloudprinter.BuildConfig.DEBUG) {
                return
            }
            val configs =
                NetSecurityConfigParser.parse(applicationContext, R.xml.network_security_config)
            val allPins: MutableList<NetSecurityConfigDomainWithPin> = mutableListOf()
            for (config in configs) {
                flattenPins(config, allPins)
            }
            if (allPins.size > 0) {
                val certificatePinnerBuilder = CertificatePinner.Builder()
                for (pin in allPins) {
                    certificatePinnerBuilder.add(pin.domain, "sha256/${pin.pin}")
                }
                val reClientBuilder = DokitOkGo.getInstance().okHttpClient.newBuilder()
                reClientBuilder.certificatePinner(certificatePinnerBuilder.build())
                DokitOkGo.getInstance().setOkHttpClient(reClientBuilder.build())
            }
        }

        private fun flattenPins(
            config: NetSecurityConfigDomainConfig,
            allPins: MutableList<NetSecurityConfigDomainWithPin>
        ) {
            if (config.domains != null && config.pinSets != null && config.pinSets!!.pins != null) {
                for (domain in config.domains!!) {
                    for (pin in config.pinSets!!.pins!!) {
                        allPins.add(NetSecurityConfigDomainWithPin(domain, pin))
                    }
                }
            }
            if (config.children == null) return
            for (child in config.children!!) {
                flattenPins(child, allPins)
            }
        }


        override fun languageUpdate() {
            super.languageUpdate()
            VipHelper.clearVipCacheWhenLanguageUpdate()
            BannerLocalUtils.clearCache()
            DevicesSeriesLocalUtils.clearCache()
            DevicesLocalUtils.clearCache()
            JCApiManager.clearVersionUpdateCache()
            BuriedHelper.setLanguage(TextHookUtil.getInstance().languageName)
        }
    }
}
