import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/pages/industry_template/select_label/custom_grid_view.dart';
import 'package:text/pages/industry_template/select_label/label_category_list_model.dart';
import 'package:text/pages/industry_template/select_label/label_web_page.dart';
import 'package:text/pages/industry_template/select_label/model/consumable_model.dart';
import 'package:text/pages/industry_template/select_label/model/label_performance_model.dart';
import 'package:text/pages/industry_template/select_label/model/shop_product_business.dart';
import 'package:text/pages/industry_template/select_label/model/shop_product_model.dart';
import 'package:text/pages/meProfile/me_profile_presenter.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/image_utils.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/flow_label_widget.dart';

import '../../../utils/templateLayout/layout_helper.dart';

class LabelDetailsPage extends StatefulWidget {
  Item labelData;
  bool? isFullScreen;
  String? shopSource;
  bool? showLabelBg;
  int source;
  LabelDetailsPage(this.labelData, {Key? key, this.isFullScreen = false, this.shopSource = '', this.showLabelBg = true,this.source = 0})
      : super(key: key);

  @override
  _LabelDetailsPageState createState() => _LabelDetailsPageState();
}

class _LabelDetailsPageState extends State<LabelDetailsPage> with PageVisibilityObserver {
  List<ConsumableModel> consumableList = [];
  ShopProductModel? shopProductModel;
  LabelPerformanceModel? labelPerformanceModel;
  late PageController controller;

  late List<String> bgs;
  int currentIndex = 0;
  Timer? _timer;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // _getConsumeGoods();
    _getShopBuyInfoNew();
    getLabelPerformanceInfo();
    controller = PageController(initialPage: 0, viewportFraction: 1.0, keepPage: true);
    bgs = widget.labelData.backgroundImage?.split(",") ?? [];
    if (bgs.length > 1 && widget.isFullScreen != true && widget.showLabelBg == true) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        startTimer();
      });
    }
  }

  void startTimer() {
    //间隔两秒时间
    _timer?.cancel();
    _timer = Timer.periodic(Duration(milliseconds: 2000), (value) {
      currentIndex++;
      // currentIndex = currentIndex % bgs.length;
      //触发轮播切换
      controller.animateToPage(currentIndex, duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
      //刷新
      setState(() {});
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, ModalRoute.of(context) as Route);
  }

  @override
  void onPageShow() {
    super.onPageShow();
    if (bgs.length > 1 && widget.isFullScreen == true) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        startTimer();
      });
    }
  }

  @override
  void onPageHide() {
    super.onPageHide();
    _timer?.cancel();
    _timer = null;
  }

  @override
  void dispose() {
    super.dispose();
    PageVisibilityBinding.instance.removeObserver(this);
    _timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    Log.d(widget.labelData.toString());
    if (widget.isFullScreen == true) {
      return Scaffold(
        appBar: _topWidget(),
        body: Container(
          color: ThemeColor.background,
          child: _buildContentWidget(context),
        ),
      );
    } else {
      return Container(
        decoration: const BoxDecoration(
          color: ThemeColor.background,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
        ),
        padding:
            EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
        height: MediaQuery.sizeOf(context).height - 60,
        child: _buildContentWidget(context),
      );
    }
  }

  _topWidget() {
    return AppBar(
      centerTitle: true,
      backgroundColor: ThemeColor.background,
      elevation: 0,
      leading: InkWell(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 15),
            child: Image(
              image: ImageUtils.getAssetImage('pageback'),
              matchTextDirection: true,
            ),
          ),
          onTap: () {
            Navigator.of(context).pop();
          }),
      title: Text(
        intlanguage('app100000720', '标签纸详情'),
        style: TextStyle(
          fontSize: 17,
          color: ThemeColor.title,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildContentWidget(BuildContext context) {
    return Column(
      children: [
        if (widget.isFullScreen != true) ...[_titleBarWidget()],
        Expanded(
          child: Container(
            // padding: EdgeInsets.only(left: 16, right: 16),
            child: ListView(
              padding: EdgeInsets.all(0),
              shrinkWrap: true,
              children: [
                if (widget.showLabelBg == true) _buildLabelBackgroundWidget(),
                if (shopProductModel?.checkValid() == true) ...[_buildBuyInfoWidgetNew()],
                _labelPerformanceWidget(),
                _labelDetailsWidget(),
                //_consumeGoodsListWidget()
              ],
            ),
          ),
        )
      ],
    );
  }

  //标签性能
  _labelPerformanceWidget() {
    return Offstage(
      offstage: !(labelPerformanceModel?.items != null && (labelPerformanceModel?.items ?? []).length > 0),
      child: Container(
        padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
        child: Column(
          children: [
            _labelPerformanceTopWidget(),
            SizedBox(
              height: 6,
            ),
            OptionGridView(
              itemBuilder: (BuildContext context, int index) {
                return getLabelPerformanceItem(labelPerformanceModel!.items[index]);
              },
              itemCount: (labelPerformanceModel?.items ?? []).length,
              rowCount: 2,
              mainAxisSpacing: 8,
              crossAxisSpacing: 10,
            ),
            SizedBox(
              height: 14,
            ),
            Container(
              height: 0.5,
              color: ThemeColor.divider,
            )
          ],
        ),
      ),
    );
  }

  Widget getLabelPerformanceItem(StarItem item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          intlanguage(item.textId ?? "", '标签纸详情'),
          style: const TextStyle(color: ThemeColor.COLOR_595959, fontSize: 13, fontWeight: FontWeight.w400),
          textAlign: TextAlign.left,
        ),
        Container(
          padding: EdgeInsetsDirectional.fromSTEB(0, 1, 0, 0),
          child: item.value.isNum
              ? Row(children: _labelStarWidget(int.parse(item.value), item.getColor()))
              : Text(
                  item.value,
                  style: TextStyle(
                    fontSize: 10,
                    color: ThemeColor.subtitle,
                    fontWeight: FontWeight.w400,
                  ),
                ),
        ),
      ],
    );
  }

  List<Widget> _labelStarWidget(int value, Color valueColor) {
    List<Widget> stars = [];
    for (var i = 0; i < 5; i++) {
      Widget star = _starWidget(i < value ? valueColor : Color(0xFFDDDDDD));
      stars.add(star);
    }
    return stars;
  }

  Widget _starWidget(Color color) {
    Widget star = Container(height: 12, width: 11, child: Icon(color: color, Icons.star, size: 12));
    return star;
  }

  _labelPerformanceTopWidget() {
    return Row(
      children: [
        Text(
          intlanguage('app100001531', '标签纸性能'),
          style: const TextStyle(color: ThemeColor.subtitle, fontSize: 12, fontWeight: FontWeight.w600),
        ),
        Spacer(),
        Offstage(
          offstage: !(labelPerformanceModel?.detailsUrl != null && labelPerformanceModel!.detailsUrl.length > 0),
          child: GestureDetector(
            onTap: () {
              _knowMoreClick();
            },
            child: Row(
              children: [
                Text(
                  intlanguage('app100001532', '了解更多'),
                  style: const TextStyle(color: Color(0xFF537FB7), fontSize: 12, fontWeight: FontWeight.w600),
                ),
                Image(
                  width: 12,
                  height: 16,
                  image: AssetImage('assets/images/industry_template/home/<USER>'),
                  matchTextDirection: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _knowMoreClick() {
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "013_306_274",
      "ext": {"tag_id": widget.labelData.id ?? ""}
    });
    if (widget.isFullScreen == true) {
      CustomNavigation.gotoNextPage(
              'labelWeb',
              {
                "title": intlanguage('app100001531', '标签纸性能'),
                'url': labelPerformanceModel?.detailsUrl ?? '',
                "isFullScreen": true
              },
              withContainer: false)
          .then((value) {});
    } else {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: false,
        // barrierColor: Colors.transparent,
        barrierColor: widget.isFullScreen == true ? Colors.black.withOpacity(0.35) : Colors.transparent,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (BuildContext context) {
          return LabelWebPage(
            url: labelPerformanceModel?.detailsUrl ?? '',
            title: intlanguage('app100001531', '标签纸性能'),
            rightCloseText: intlanguage('app01584', '关闭'),
          );
        },
      ).then((value) {});
    }
  }

  ///标题
  _titleBarWidget() {
    return Container(
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 7),
      child: Stack(
        children: [
          Align(
            alignment: Alignment.center,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxWidth: MediaQuery.sizeOf(context).width - 200),
              child: Padding(
                padding: EdgeInsets.only(top: 2.5),
                child: Text(
                  intlanguage('app100000720', '标签纸详情'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: ThemeColor.title, fontSize: 17, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
          Align(
            alignment: AlignmentDirectional.topEnd,
            child: PlaneButton(
              width: 30,
              height: 30,
              child: const SvgIcon('assets/images/industry_template/replace_label/close_line.svg'),
              onTap: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }

  _buildLabelBackgroundWidget() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColor.listBackground,
      ),
      child: Column(
        children: [
          Container(
            width: MediaQuery.sizeOf(context).width,
            height: 148,
            padding: EdgeInsetsDirectional.only(top: 24),
            child: PageView.builder(
              pageSnapping: true,
              controller: controller,
              itemCount: bgs.length > 1 ? bgs.length * 1000 : bgs.length,
              onPageChanged: _onPageChange,
              itemBuilder: (_, index) {
                return Center(
                  child: GestureDetector(
                    onTap: () {
                      _useLabelPage();
                    },
                    child: CacheImageUtil().netCacheImage(
                        width: 246,
                        height: 124,
                        imageUrl: bgs[index % bgs.length] ?? '',
                        errorWidget: ThemeWidget.placeholder(
                            backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20))),
                  ),
                );
              },
            ),
          ),
          Offstage(
            offstage: bgs.length == 1,
            child: Padding(
              padding: EdgeInsetsDirectional.only(top: 14),
              child: SmoothPageIndicator(
                controller: controller,
                count: bgs.length,
                effect: const WormEffect(
                  dotHeight: 8,
                  dotWidth: 8,
                  dotColor: ThemeColor.COLOR_BLACK_10,
                  activeDotColor: ThemeColor.COLOR_BLACK_30,
                  type: WormType.normal,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              _useLabelPage();
            },
            child: Container(
              padding: const EdgeInsetsDirectional.only(start: 32, end: 32, bottom: 24, top: 20),
              decoration: BoxDecoration(
                  gradient: LinearGradient(
                      begin: AlignmentDirectional.topCenter,
                      end: AlignmentDirectional.bottomCenter,
                      colors: [ThemeColor.listBackground, Colors.white])),
              child: Container(
                height: 44,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12.0),
                    border: Border.all(width: 0.5, color: ThemeColor.divider),
                    boxShadow: [
                      BoxShadow(color: Colors.black.withOpacity(0.10), offset: const Offset(0, 5), blurRadius: 30)
                    ]),
                child: Center(
                  child: Text(
                    intlanguage('app100001354', '使用此标签纸'),
                    style: TextStyle(color: ThemeColor.brand, fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _useLabelPage() {
    if (!DebounceUtil.checkClick(needTime: 2)) return;
    if (widget.isFullScreen == true) {
      ToNativeMethodChannel.finishFlutterPages().then((value) async {
        Map<String, dynamic>? layoutTemplate = await LayoutHelper().getLayoutTemplate(widget.labelData.rawJson,source: widget.source);
        if (layoutTemplate != null) {
          layoutTemplate["name"] = intlanguage('app100000728', '未命名模板');
          CustomNavigation.gotoNextPage('toCanvasPage', {'labelData': layoutTemplate, "isCustomLabel": false});
        }
      });
    } else {
      Navigator.of(context).pop(widget.labelData);
    }
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "013_284",
      "ext": {"tag_id": widget.labelData.id}
    });
  }

  _onPageChange(int index) {
    setState(() {
      // currentIndex = index % bgs.length;
      currentIndex = index;
    });
  }

  ///预览图
  _previewWidget() {
    return Container(
      margin: EdgeInsets.only(bottom: 12, top: 10),
      padding: EdgeInsets.all(10),
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          height: 170,
          child: Stack(
            fit: StackFit.expand,
            children: [
              Container(
                decoration:
                    const BoxDecoration(color: Color(0xFFF7F7F7), borderRadius: BorderRadius.all(Radius.circular(12))),
              ),
              Positioned(
                  top: 15,
                  left: 25,
                  right: 25,
                  bottom: 15,
                  child: CacheImageUtil().netCacheImage(
                      imageUrl: widget.labelData.previewImage ?? '',
                      errorWidget: ThemeWidget.placeholder(
                          backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20)))),
            ],
          ),
        ),
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 5),
          child: Text(
            widget.labelData.name ?? '',
            softWrap: true,
            // overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.start,
            style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w600),
          ),
        )
      ]),
    );
  }

  ///复购信息
  _buildBuyInfoWidget() {
    String price = "";
    Color goodNameColor;
    Color goodPriceColor;
    bool buyState = shopProductModel!.checkBuyState() == true;
    if (buyState) {
      ///可以正常购买，显示价格
      price = shopProductModel!.getPriceWithCurrencySymbol();
      goodNameColor = ThemeColor.title;
      goodPriceColor = ThemeColor.brand;
    } else {
      ///不能正常购买，显示补货中或已下架
      price = shopProductModel!.getSimilarStateTip();
      goodNameColor = ThemeColor.COLOR_FFBFBFBF;
      goodPriceColor = ThemeColor.title;
    }

    return Container(
      color: ThemeColor.background,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(18, 7, 18, 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 54,
                  height: 54,
                  decoration: BoxDecoration(
                      color: ThemeColor.listBackground, borderRadius: BorderRadius.all(Radius.circular(8))),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CacheImageUtil().netCacheImage(
                        fit: BoxFit.contain,
                        width: 44,
                        height: 44,
                        imageUrl: shopProductModel?.goodsImage ?? "",
                        errorWidget: ThemeWidget.placeholder(
                            backgroundColor: Colors.transparent, padding: const EdgeInsets.all(30)),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 17,
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: AlignmentDirectional.centerStart,
                        width: MediaQuery.sizeOf(context).width - 136,
                        child: Text(shopProductModel?.goodsName ?? "",
                            maxLines: 1,
                            style: TextStyle(
                              color: goodNameColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              overflow: TextOverflow.ellipsis,
                            )),
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                      Container(
                        alignment: AlignmentDirectional.centerStart,
                        width: MediaQuery.sizeOf(context).width - 136,
                        child: Text(
                          price,
                          style: TextStyle(
                              color: goodPriceColor,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              overflow: TextOverflow.ellipsis),
                        ),
                      )
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    var link = shopProductModel?.buttonLink ?? '';
                    if (link.isURL) {
                      link = link.contains('?')
                          ? '${link}&jumpSource=${widget.shopSource}'
                          : '${link}?jumpSource=${widget.shopSource}';
                    }
                    CustomNavigation.gotoNextPage('ToBuyLabelPage', {
                      'link': link,
                    });
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "013_037_047",
                      "ext": {
                        "tag_id": widget.labelData.id,
                        "goods_sku_id": shopProductModel?.goodsSku ?? "",
                        "button_type": shopProductModel?.buttonType
                      }
                    });
                  },
                  child: Container(
                    alignment: AlignmentDirectional.center,
                    width: 66,
                    height: 30,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(15)), color: ThemeColor.COLOR_F5F5F5),
                    child: Text(shopProductModel?.getDisplayButtonName() ?? '购买',
                        style: const TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w600)),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 0.5,
            color: ThemeColor.divider,
            margin: EdgeInsetsDirectional.fromSTEB(0, 10, 0, 5),
          ),
        ],
      ),
    );
  }

  ///复购信息
  _buildBuyInfoWidgetNew() {
    return Container(
      color: ThemeColor.background,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsetsDirectional.fromSTEB(18, 7, 18, 0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  width: 54,
                  height: 54,
                  decoration: BoxDecoration(
                      color: ThemeColor.listBackground, borderRadius: BorderRadius.all(Radius.circular(8))),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CacheImageUtil().netCacheImage(
                        fit: BoxFit.contain,
                        width: 44,
                        height: 44,
                        imageUrl: shopProductModel?.goodsImage ?? "",
                        errorWidget: ThemeWidget.placeholder(
                            backgroundColor: Colors.transparent, padding: const EdgeInsets.all(30)),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 17,
                ),
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: AlignmentDirectional.centerStart,
                        width: MediaQuery.sizeOf(context).width - 136,
                        child: Text(shopProductModel?.goodsName ?? "",
                            maxLines: 2,
                            style: TextStyle(
                              color: ThemeColor.title,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              overflow: TextOverflow.ellipsis,
                            )),
                      )
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    var link = shopProductModel?.buttonLink ?? '';
                    CustomNavigation.gotoNextPage('ToBuyLabelPage', {
                      'link': link,
                    });
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "013_037_047",
                      "ext": {"tag_id": widget.labelData.id, "button_type": shopProductModel?.buttonType}
                    });
                  },
                  child: Container(
                    alignment: AlignmentDirectional.center,
                    width: 66,
                    height: 30,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(15)), color: ThemeColor.COLOR_F5F5F5),
                    child: Text(intlanguage("app01165", "购买"),
                        style: const TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w400)),
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 0.5,
            color: ThemeColor.divider,
            margin: EdgeInsetsDirectional.fromSTEB(0, 10, 0, 5),
          ),
        ],
      ),
    );
  }

  ///标签信息展示页
  _labelDetailsWidget() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsetsDirectional.symmetric(horizontal: 18),
          child: Table(columnWidths: {
            0: IntrinsicColumnWidth(),
            1: FlexColumnWidth(),
          }, children: [
            _tableRow(intlanguage('app100000451', '标签名称'), widget.labelData.name),
            if (widget.labelData.profile?.extrain?.materialModelSn?.isNotEmpty ?? false) ...[
              _tableRow(intlanguage('app100001328', '标签ID'), widget.labelData.profile?.extrain?.materialModelSn)
            ],
            _tableRow(intlanguage('app00229', '标签大小'),
                widget.labelData.width!.toInt().toString() + '*' + widget.labelData.height!.toInt().toString() + 'mm'),
            _tableRow(intlanguage('app00235', '适用机型'), widget.labelData.rawJson['profile']['machineName'].toString()),
            _tableRow(intlanguage('app00234', '纸张类型'), intlanguage(_papeType(widget.labelData.paperType!.toInt()), '')),
            _tableRow(
                intlanguage('app100000452', '标签材质'),
                intlanguage(
                    (widget.labelData.rawJson['consumableTypeTextId'] != null &&
                            widget.labelData.rawJson['consumableTypeTextId'].toString().isNotEmpty)
                        ? widget.labelData.rawJson['consumableTypeTextId'].toString()
                        : getConsumableTypeString(widget.labelData.rawJson['consumableType'].toInt().toString()),
                    '')),
            _tableRow(intlanguage('app01137', '出纸方向'), _paperDirection(widget.labelData.rotate!.toInt())),
          ]),
        ),
      ],
    );
  }

  TableRow _tableRow(String title, String? value) {
    var textDirection = Directionality.of(context);
    // 对于含有*字符在RTL下被翻转的处理，例70*20mm被翻转为20mm*70
    if (value?.contains('*') ?? true && Directionality.of(context) == TextDirection.rtl) {
      textDirection = TextDirection.ltr;
    }
    return TableRow(children: [
      TableCell(
        verticalAlignment: TableCellVerticalAlignment.top,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 7),
          constraints: const BoxConstraints(
            maxWidth: 120,
          ),
          //width: 70,
          child: Text(
            title,
            softWrap: true,
            textAlign: TextAlign.start,
            style: const TextStyle(color: ThemeColor.subtitle, fontSize: 14, fontWeight: FontWeight.w400),
          ),
        ),
      ),
      TableCell(
        verticalAlignment: TableCellVerticalAlignment.top,
        child: Container(
            padding: const EdgeInsetsDirectional.only(start: 13, bottom: 7, top: 7),
            child: _buildLabelInfoCell(textDirection, title, value)),
      ),
    ]);
  }

  Widget _buildLabelInfoCell(TextDirection textDirection, String title, String? value) {
    if (title == intlanguage('app01137', '出纸方向')) {
      return _buildPaperDirectionWiget(textDirection, value);
    } else if (title == intlanguage('app00235', '适用机型')) {
      List<String>? names = value?.split(",");
      return names == null || names.isEmpty
          ? Container()
          : FlowLabelWidget(
              children: List.generate(names.length, (i) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: ThemeColor.listBackground,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(names[i], style: const TextStyle(color: ThemeColor.subtitle, fontSize: 12)),
                );
              }),
            );
    } else {
      return Text(
        value ?? '',
        textDirection: textDirection,
        textAlign: Directionality.of(context) == TextDirection.rtl && textDirection == TextDirection.ltr
            ? TextAlign.end
            : TextAlign.start,
        style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
      );
    }
  }

  ///出纸方向带箭头特殊处理
  _buildPaperDirectionWiget(TextDirection textDirection, String? value) {
    String imagePath = "";
    if (value != null) {
      if (value == intlanguage('app100000597', '向上')) {
        imagePath = "assets/images/icon_paper_direction_up.svg";
      } else if (value == intlanguage('app100000043', '向下')) {
        imagePath = "assets/images/icon_paper_direction_down.svg";
      } else if (value == intlanguage('app100000044', '向左')) {
        imagePath = "assets/images/icon_paper_direction_left.svg";
      } else if (value == intlanguage('app100000042', '向右')) {
        imagePath = "assets/images/icon_paper_direction_right.svg";
      }
    }
    return Row(
      children: [
        Text(
          value ?? '',
          textDirection: textDirection,
          textAlign: Directionality.of(context) == TextDirection.rtl && textDirection == TextDirection.ltr
              ? TextAlign.end
              : TextAlign.start,
          style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
        ),
        SvgIcon(imagePath),
      ],
    );
  }

  ///耗材信息
  _consumeGoodsListWidget(BuildContext context) {
    return consumableList.isNotEmpty
        ? Column(
            children: [
              Container(
                height: 0.5,
                color: ThemeColor.divider,
                margin: EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
              ),
              ListView.separated(
                itemCount: consumableList.length,
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return ConsumableDetailsItem(context, consumableList[index]);
                },
                separatorBuilder: (BuildContext context, int index) => Divider(
                  height: 0.5,
                  color: ThemeColor.divider,
                  indent: 18,
                  thickness: 0.5,
                ),
              ),
            ],
          )
        : Container();
  }

  ///标签信息
  _labelDetailsItem(String title, String? value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 7),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: 70,
            child: Text(
              title,
              softWrap: true,
              // overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: const TextStyle(color: ThemeColor.subtitle, fontSize: 14, fontWeight: FontWeight.w400),
            ),
          ),
          SizedBox(
            width: 14,
          ),
          Container(
            constraints: BoxConstraints(maxWidth: 170),
            child: Text(
              value ?? '',
              // overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: const TextStyle(color: ThemeColor.title, fontSize: 15, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  ///耗材Item
  ConsumableDetailsItem(BuildContext context, ConsumableModel model) {
    String pageTitle = intlanguage(model.detailPageTitleCode ?? '', ' ');

    return GestureDetector(
      onTap: () {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "013_206",
          "ext": {"type_id": model.documentId}
        });
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          enableDrag: false,
          // barrierColor: Colors.transparent,
          barrierColor: widget.isFullScreen == true ? Colors.black.withOpacity(0.35) : Colors.transparent,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
          builder: (BuildContext context) {
            Log.d(model.title ?? "");
            String url = Application.webUrl + model.documentId.toString();
            return LabelWebPage(
              title: pageTitle,
              url: url ?? '',
            );
          },
        ).then((value) {});
      },
      child: Container(
        // padding: EdgeInsets.symmetric(vertical: 14),
        padding: EdgeInsetsDirectional.symmetric(horizontal: 18, vertical: 13),
        child: AbsorbPointer(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: CacheImageUtil().netCacheImage(
                    imageUrl: model.iconUrl ?? '',
                    width: 20,
                    height: 20,
                    errorWidget: ThemeWidget.placeholder(
                        backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20))),
              ),
              Container(
                padding: EdgeInsetsDirectional.only(start: 7, bottom: 2),
                width: context.width * 0.65,
                child: Text(
                  model.title ?? '',
                  softWrap: true,
                  // overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.start,
                  style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                ),
              ),
              Expanded(child: Container()),
              const SvgIcon(
                'assets/images/industry_template/home/<USER>',
                matchTextDirection: true,
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///纸张类型处理
  _papeType(int type) {
    return HardWareManager.instance().getPaperTypeNameByCode(type.toString());
  }

  ///标签材质
  String getConsumableTypeString(String consumableType) {
    return HardWareManager.instance().getConsumableNameByCode(consumableType);
  }

  ///出纸方向
  _paperDirection(int direction) {
    switch (direction) {
      case 0:
        return intlanguage('app100000597', '向上');
      case 90:
        return intlanguage('app100000044', '向左');
      case 180:
        return intlanguage('app100000043', '向下');
      case 270:
        return intlanguage('app100000042', '向右');
      default:
        return intlanguage('app100000597', '向上');
    }
  }

  ///上报场景数据为空
  _getConsumeGoods() {
    var consumableType = widget.labelData.rawJson['consumableType'].toInt().toString();
    String gql = '''
        query getConsumableFeature(\$consumableCode: String!){
          getConsumableFeature(consumableCode:\$consumableCode){
          title
          iconUrl
          id
          documentId
          detailPageTitleCode
          }
        }
        ''';

    GraphQLUtils.sharedInstance().configCustomHeaders({"languageCode": Application.currentAppLanguageType});
    GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {"consumableCode": consumableType},
            authorization: true,
            headers: {"languageCode": Application.currentAppLanguageType, "content-type": "application/json"})
        .then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException && result.data!["getConsumableFeature"] != null) {
        consumableList =
            List<ConsumableModel>.from(result.data!["getConsumableFeature"].map((x) => ConsumableModel.fromJson(x)));
        if (mounted) {
          setState(() {});
        }
      } else {
        var error = intlanguage('app01139', '网络异常');
        showToast(msg: error);
      }
    });
  }

  getLabelPerformanceInfo() {
    String lableId = widget.labelData.profile?.extrain.labelId ?? "";
    if (lableId.length == 0) {
      return;
    }
    String url = '/labels/:id/consumable-attributes';
    String resultUrl = url.replaceAll(":id", lableId);
    Map<String, dynamic> labelPerformanceApi = {
      'method': 'GET',
      'path': resultUrl,
      'needLogin': false,
    };
    var consumableType = widget.labelData.rawJson['consumableType'].toInt().toString();
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.get, labelPerformanceApi,
        queryParameters: {"consumableMaterial": consumableType}, onSuccess: (data) {
      try {
        if (data != null && data.isNotEmpty) {
          LabelPerformanceModel model = LabelPerformanceModel.fromJson(data);
          labelPerformanceModel = model;
          if (mounted) {
            setState(() {});
          }
        }
      } catch (e, s) {
        print('异常信息:\n $e');
        print('调用栈信息:\n $s');
      }
    }, onError: (code, message) {}, isList: false);
  }

  _getShopBuyInfo() {
    if (MeProfilePresenter.shopState == 0) {
      return;
    }
    // var oneCode = "120523111";
    var oneCode = getTemplateCode();
    if (oneCode.isEmpty) {
      return;
    }
    ShopProductBusiness.getShopProductInfo(oneCode, (value) {
      shopProductModel = value;
      setState(() {});
      if (shopProductModel?.checkValid() == true) {
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "show",
          "posCode": "013_037_047",
          "ext": {
            "tag_id": widget.labelData.id,
            "goods_sku_id": shopProductModel?.goodsSku ?? "",
            "button_type": shopProductModel?.buttonType
          }
        });
      }
    }, (errorCode, errorMsg) {});
  }

  _getShopBuyInfoNew() {
    if (MeProfilePresenter.shopState == 0) {
      return;
    }
    // var oneCode = "120523111";
    var templateCode = getTemplateCode();
    if (templateCode.isEmpty) {
      return;
    }
    var oneCode = templateCode.split(',').first;
    if (oneCode.isEmpty) {
      return;
    }
    ToNativeMethodChannel.getLabelShopLink(oneCode, widget.shopSource!).then((url) {
      ShopProductModel model = ShopProductModel();
      model.oneCode = oneCode;
      String languageCode = Application.currentAppLanguageType.toLowerCase();
      if (languageCode.isEmpty) {
        languageCode = "zh-cn";
      }
      LabelNames? labelNames = widget.labelData.labelNames
          ?.firstWhereOrNull((element) => element.languageCode?.toLowerCase() == languageCode);
      if (labelNames == null) {
        labelNames =
            widget.labelData.labelNames?.firstWhereOrNull((element) => element.languageCode?.toLowerCase() == "en");
      }
      model.goodsName = labelNames?.name;
      model.goodsImage = widget.labelData.backgroundImage?.split(',').first;
      model.buttonType = 1;
      model.buttonName = intlanguage("app01165", "购买");
      model.buttonLink = url;
      shopProductModel = model;
      setState(() {});
      ToNativeMethodChannel().sendTrackingToNative({
        "track": "show",
        "posCode": "013_037_047",
        "ext": {"tag_id": widget.labelData.id, "button_type": shopProductModel?.buttonType}
      });
    });
  }

  String getTemplateCode() {
    Profile? profile = widget.labelData.profile;
    if (profile == null) {
      return "";
    }
    StringBuffer code = StringBuffer("");
    if (profile.barcode.isNotEmpty) {
      code.write(profile.barcode);
      code.write(",");
    }
    var extrain = profile.extrain;
    if (extrain.virtualBarCode != null && (extrain.virtualBarCode ?? "").isNotEmpty) {
      code.write(extrain.virtualBarCode);
      code.write(",");
    }
    if (extrain.amazonCodeWuhan != null && (extrain.amazonCodeWuhan ?? "").isNotEmpty) {
      code.write(extrain.amazonCodeWuhan);
      code.write(",");
    }
    if (extrain.amazonCodeBeijing != null && (extrain.amazonCodeBeijing ?? "").isNotEmpty) {
      code.write(extrain.amazonCodeBeijing);
      code.write(",");
    }
    if (extrain.sparedCode != null && (extrain.sparedCode ?? "").isNotEmpty) {
      code.write(extrain.sparedCode);
      code.write(",");
    }
    if (extrain.barcodeCategoryMap != null && (extrain.barcodeCategoryMap ?? {}).isNotEmpty) {
      extrain.barcodeCategoryMap?.values.forEach((it) {
        if (it.isNotEmpty) {
          code.write(it);
          code.write(",");
        }
      });
    }
    return code.toString();
  }
}
