//
//  JCShopMallsViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/2/22.
//  Copyright © 2019 JingchenSoft. All rights reserved.
//

#import "JCShopMallsViewController.h"
#import "QQLBXScanViewController.h"
#import <AlipaySDK/AlipaySDK.h>
#import "Global.h"
#import "StyleDIY.h"
#import "ONImagePickerController.h"
#import <ZLPhotoBrowser/ZLPhotoBrowser.h>


@interface JCShopMallsViewController ()<UIScrollViewDelegate>
@property (nonatomic,copy) NSString *lastUrl;
@property (nonatomic,assign) BOOL nativiFunctionCalled;
@property (nonatomic,assign) BOOL hasPreloading;
@end

@implementation JCShopMallsViewController

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.isNeedReload = YES;
        self.isNeedBackApp = NO;
        self.nativiFunctionCalled = NO;
        [self addStepLogDescr:@"商城首页初始化init"];
        self.jumpSource = @"y_page_main";
        self.entrance_type_id = @"1";
        
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(reloadShopHomeView) name:LOGIN_CHANGED_SHOP object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeLanguage) name:JCNOTICATION_ChANGELANGUAGE object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillShow) name:UIKeyboardWillShowNotification object:nil];
      
        UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
        if (@available(iOS 13.0, *)) {
            statusBarStyle = UIStatusBarStyleDarkContent;
        }//
        self.statusBarStyle = statusBarStyle;
        self.statusBackColor = 0xFFFFFF;
        self.isHomePageShop = YES;
        NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(60 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            if(self.logStepArr.count > 0 && !self.nativiFunctionCalled){
                NSString *stepLogDetail = [self.logStepArr componentsJoinedByString:@";\n"];
                stepLogDetail = [NSString stringWithFormat:@"商店未调用原生方法：\n%@ \n 信息：%@ \n日志生成时间：%@",stepLogDetail,userAgent,[XYTool getCurrentTimesWithoutTSZ]];
                uploadLogInfoFlutter(UN_NIL(stepLogDetail), @"shopErrorDetail",^(id x,id y){},J_get_sls_Log_Token);
            }
        });
        // 设置scrollView的代理，禁止WebView滚动，避免键盘弹出时页面滚动
        self.webView.scrollView.delegate = self;
        // 不展示进度条
        self.progressView.hidden = YES;
    }
    return self;
}

- (void)keyboardWillShow {
  [self.webView.inputAccessoryView removeFromSuperview];
}

#pragma mark - UIScrollViewDelegate
- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 禁用WebView滚动，保持内容在初始位置
    if (scrollView == self.webView.scrollView) {
        [scrollView setContentOffset:CGPointZero animated:NO];
    }
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self addStepLogDescr:@"商城首页ViewDidload"];
    self.view.backgroundColor = COLOR_WHITE;
    [self setupWebViewFrame];
    // 禁用滚动反弹效果
    self.webView.scrollView.bounces = NO;
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
    self.webView.scrollView.contentInsetAdjustmentBehavior = UIScrollViewContentInsetAdjustmentNever;
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self addStepLogDescr:@"商城首页viewWillAppear"];
    if (self.isNeedReload) {
        self.shopHomeHost = [XYCenter sharedInstance].shopHomeHost;
#ifdef DEBUG
        NSString *shopHost = [[NSUserDefaults standardUserDefaults] stringForKey:@"LaboratoryTypeShopDomain"];
        if (shopHost) {
            self.shopHomeHost = shopHost;
            [XYCenter sharedInstance].shopHomeHost = shopHost;
        }
#endif
        // 国内商城地址：【服务域名+/h5_project/#/?key=value&key=value】
        // 海外商城地址：【服务域名+/#/home?key=value&key=value】
        // path
        // 国内商城地址：【服务域名+/h5_project/#/path?key=value&key=value】
        // 海外商城地址：【服务域名+/#/path?key=value&key=value】
        self.lastUrl = [self.shopHomeHost stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/" : @"#/home"];
        [self loadUrl:self.lastUrl];
        self.isNeedReload = NO;
    }
    // 通知JS端界面将要展示
    NSString *jsStr = [NSString stringWithFormat:@"viewWillAppear()"];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
      
    }];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = self.statusBarStyle;
    self.detailStateView.backgroundColor = HEX_RGB(self.statusBackColor);
    [self addStepLogDescr:@"商城首页viewDidAppear"];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [self addStepLogDescr:@"商城首页viewDidAppear"];
    // 通知JS端界面将要消失
    NSString *jsStr = [NSString stringWithFormat:@"viewWillDisappear()"];
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {

    }];
}

- (void)changeLanguage {
    // 标记需要重新加载
    self.isNeedReload = YES;
}

//支付完成刷新webview
-(void)refreshWeb{
    UIViewController *rootVC = [UIApplication sharedApplication].keyWindow.rootViewController;
    if([rootVC isKindOfClass:[UITabBarController class]]){
        UITabBarController *root = (UITabBarController*)rootVC;
        UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
        if(![naviVC.visibleViewController isKindOfClass:[self class]]){
            self.isNeedReload = YES;
        }else{
            NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
            NSURL *url = [NSURL URLWithString:myStr];
            NSURLRequest *req = [NSURLRequest requestWithURL:url ];
            [self.webView loadRequest:req];
        }
    }
    
}

// 商城埋点
- (void)callH5Mothord{
    NSString *jsStr = @"appUserClickEvent()";
    [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
        NSLog(@"%@----%@",result, error);
    }];
}

//刷新商城首页
- (void)reloadShopHomeView{
    if(xy_isLogin && !STR_IS_NIL(m_userModel.shopToken)){
        NSString *jsStr = [NSString stringWithFormat:@"AppSetToken('%@', '%@', '%@', '%@')",m_userModel.shopToken, [JCKeychainTool getDeviceIDInKeychain], m_userModel.userId, @"iOS_YDY"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
    }else if(!xy_isLogin){
        NSString *jsStr = [NSString stringWithFormat:@"AppSetToken('', '%@', '', '%@', '%@')",[JCKeychainTool getDeviceIDInKeychain], @"iOS_YDY", @"1"];
        [self.webView evaluateJavaScript:jsStr completionHandler:^(id _Nullable result, NSError * _Nullable error) {
            NSLog(@"%@----%@",result, error);
        }];
        NSString *url = self.shopHomeHost;
        if(![url containsString:@"#/"]){
            url = [NSString stringWithFormat:@"%@#/",url];
        }
        self.isNeedReload = YES;
    }
}

//刷新根据Url路径刷新商城
- (void)refreshWebViewWith:(NSString *)myUrlStr{
    NSLog(@"开始加载URL：%@",myUrlStr);
    if([myUrlStr rangeOfString:@"#/"].location != NSNotFound){
        if([myUrlStr rangeOfString:@"?"].location != NSNotFound){
            self.lastUrl = [NSString stringWithFormat:@"%@&", myUrlStr];
        }else{
            self.lastUrl = [NSString stringWithFormat:@"%@?", myUrlStr];
        }
    }else{
        self.lastUrl = [NSString stringWithFormat:@"%@#/", myUrlStr];
    }
}

/// 预加载WebView
- (void)preLoadWebView {
    self.shopHomeHost = [XYCenter sharedInstance].shopHomeHost;
  #ifdef DEBUG
    NSString *shopHost = [[NSUserDefaults standardUserDefaults] stringForKey:@"LaboratoryTypeShopDomain"];
    if (shopHost) {
        self.shopHomeHost = shopHost;
        [XYCenter sharedInstance].shopHomeHost = shopHost;
    }
  #endif
    // 国内商城地址：【服务域名+/h5_project/#/?key=value&key=value】
    // 海外商城地址：【服务域名+/#/home?key=value&key=value】
    // path
    // 国内商城地址：【服务域名+/h5_project/#/path?key=value&key=value】
    // 海外商城地址：【服务域名+/#/path?key=value&key=value】
    self.lastUrl = [self.shopHomeHost stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/?preloading=1" : @"#/home?preloading=1"];
    [self loadUrl:self.lastUrl];
    self.isNeedReload = NO;
}
    

//H5交互
#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    // 子类特有的处理逻辑
    [self addStepLogDescr:@"商城首页开始调用原生方法"];
    NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
    if(self.logStepArr.count > 0 && !self.nativiFunctionCalled){
        NSString *stepLogDetail = [self.logStepArr componentsJoinedByString:@";\n"];
        stepLogDetail = [NSString stringWithFormat:@"商店成功调用原生方法：\n%@ \n 信息：%@ \n日志生成时间：%@",stepLogDetail,userAgent,[XYTool getCurrentTimesWithoutTSZ]];
        uploadLogInfoFlutter(UN_NIL(stepLogDetail), @"shopErrorDetail",^(id x,id y){},J_get_sls_Log_Token);
    }
    self.isNeedReload = NO;
    self.nativiFunctionCalled = YES;
    self.progressView.alpha = 0;
    
    // 调用基类的消息处理方法
    [super userContentController:userContentController didReceiveScriptMessage:message];
}

//支付宝支付完成或取消时候回到App回调
- (void)webView:(WKWebView*)webView decidePolicyForNavigationAction:(WKNavigationAction*)navigationAction decisionHandler:(void(^)(WKNavigationActionPolicy))decisionHandler{
    __block WKNavigationActionPolicy actionPolicy = WKNavigationActionPolicyAllow;
    NSString *str = [navigationAction.request.URL absoluteString];
    NSString *wxPayRedirectUrl = [NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX];
    NSString *redirect_Url = [NSString stringWithFormat:@"redirect_url=%@",wxPayRedirectUrl];
    if([str hasPrefix:@"https://wx.tenpay.com/cgi-bin/mmpayweb-bin/checkmweb"] && ![str hasSuffix:redirect_Url]){
        decisionHandler(WKNavigationActionPolicyCancel);
        NSString *redirectUrl = nil;
        if([str containsString:@"redirect_url="]){
            NSRange range = [str rangeOfString:@"redirect_url="];
            redirectUrl = [[str substringToIndex:range.location] stringByAppendingString:redirect_Url];
        }else{
            redirectUrl = [str stringByAppendingString:redirect_Url];
        }
        NSMutableURLRequest *newRequest = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:redirectUrl] cachePolicy:NSURLRequestUseProtocolCachePolicy timeoutInterval:30];
        newRequest.allHTTPHeaderFields = navigationAction.request.allHTTPHeaderFields;
        [newRequest setValue:wxPayRedirectUrl forHTTPHeaderField:@"Referer"];
        newRequest.URL = [NSURL URLWithString:redirectUrl];
        [webView loadRequest:newRequest];
        return;
    }
    if ([@"jcydy://com.suofang.jcbqdy/orderlist" isEqualToString:str]) {
        actionPolicy = WKNavigationActionPolicyCancel;
        NSString *url = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/paySuccess?" : @"#/paySuccess?"];
        [self loadUrl:url];
    }
    if([str isEqualToString:[NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX]]){
        [[NSNotificationCenter defaultCenter] postNotificationName:@"payBackToRefrshWeb" object:nil];
    }
    BOOL isIntercepted = NO;
    isIntercepted = [[AlipaySDK defaultService] payInterceptorWithUrl:[navigationAction.request.URL absoluteString] fromScheme:@"JCYDY" callback:^(NSDictionary *result) {
        // 处理支付结果
        // isProcessUrlPay 代表 支付宝已经处理该URL
        if ([result[@"isProcessUrlPay"]boolValue]) {
            actionPolicy = WKNavigationActionPolicyCancel;
            NSString *url = [ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/?" : @"#/home?"];
            [self loadUrl:url];
        }
    }];
    if(isIntercepted){
        actionPolicy =WKNavigationActionPolicyCancel;
    }
    
    NSString *urlString = [[navigationAction.request.URL absoluteString] stringByRemovingPercentEncoding];
    if ([urlString containsString:@"weixin://wap/pay?"]) {
        actionPolicy =WKNavigationActionPolicyCancel;
        //解决wkwebview weixin://无法打开微信客户端的处理
        NSURL*url = [NSURL URLWithString:urlString];
        BOOL bSucc = [[UIApplication sharedApplication] canOpenURL:url];
        if(!bSucc) {
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"",@"未检测到微信APP，请您先安装")];
        } else {
            [[UIApplication sharedApplication] openURL:url options:@{UIApplicationOpenURLOptionUniversalLinksOnly: @NO} completionHandler:^(BOOL success) {
            }];
        }
    }
    
    // 加载新链接，内部跳转
    if (navigationAction.targetFrame == nil) {
        [webView loadRequest:navigationAction.request];
    }
    
    decisionHandler(actionPolicy);
}

// 图片选择方法已在基类中实现，无需重复定义

// 上传图片、定位权限等方法已在基类中实现，无需重复定义

@end
