import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/macro/color.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/industry_template/search_template/controller/search_template_controller.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';

import '../../../utils/cachedImageUtil.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/no_template_result_widget.dart';

class SearchTemplatePage extends StatefulWidget {
  SearchTemplatePage({Key? key}) : super(key: key);

  @override
  State<SearchTemplatePage> createState() => _SearchTemplatePageState();
}

class _SearchTemplatePageState extends State<SearchTemplatePage> {
  @override
  void initState() {
    SearchTemplateController _controller = SearchTemplateController();
    Get.put(_controller);
    super.initState();
    _controller.updateHistroyList(this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 0),
        padding: EdgeInsets.only(top: MediaQuery.paddingOf(context).top),
        child: Column(
          children: [
            const _AppBarContent(),
            Expanded(
              child: GetX<SearchTemplateController>(
                builder: (controller) {
                  return Container(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 250),
                      child: controller.fade.value == CrossFadeState.showFirst &&
                              controller.searchState != IndustryHomeState.showContainer
                          ? Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                controller.searchList.value!.length > 0 ? _HistoryBanner() : Container(),
                                _HistoryItem(),
                              ],
                            )
                          : _ResultList(),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AppBarContent extends StatefulWidget {
  const _AppBarContent({Key? key}) : super(key: key);

  @override
  State<_AppBarContent> createState() => _AppBarContentState();
}

class _AppBarContentState extends State<_AppBarContent> {
  final TextEditingController _textController = TextEditingController();

  final SearchTemplateController _controller = Get.find<SearchTemplateController>();

  final FocusNode node = FocusNode();

  @override
  void initState() {
    _controller.addEventListen((event) {
      _textController.text = event;
      Get.focusScope?.unfocus();
      _submittedAction(event);
    });
    super.initState();

    _textController.addListener(() {
      _controller.update([1]);
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    Get.delete<SearchTemplateController>();
    super.dispose();
  }

  /// 提交Action
  _submittedAction(String text) {
    if (text.isEmpty) {
      text = intlanguage('app100000494', '搜索模板名称、内容、关键字');
      // return;
    }

    // 存储一次记录
    _controller.saveSearchText(this, text);

    // 获取搜索结果
    _controller.getSearchResult(
        searchKey: text,
        resultClosure: (model) {
          _controller.fade.value = CrossFadeState.showSecond;
          _controller.update();
          // 搜索页_行业模板-搜索行为
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "click",
            "posCode": "070_171",
            "ext": {'key_word': text.trim(), 'source': 1, "result": (model == null || model.list.isEmpty) ? 0 : 1}
          });
        });
  }

  /// 取消事件
  _backAction() {
    _textController.clear();
    CustomNavigation.pop();
  }

  /// 清除事件
  _clearAction() {
    _textController.clear();
    _controller.fade.value = CrossFadeState.showFirst;
    node.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsetsDirectional.symmetric(vertical: 5.0),
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 返回
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _backAction,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(14, 5, 8, 5),
                child: const SvgIcon('assets/images/industry_template/search_template/search_bar_back.svg',
                    matchTextDirection: true),
              )),
          // 搜索栏
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                CupertinoTextField(
                  focusNode: node,
                  autofocus: true,
                  controller: _textController,
                  cursorColor: KColor.RED,
                  cursorHeight: 18,
                  onSubmitted: _submittedAction,
                  placeholder: intlanguage('app100000494', '搜索模板名称、内容、关键字'),
                  placeholderStyle: const TextStyle(
                    color: Color(0xFF999999),
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                  ),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                  ),
                  prefix: const Padding(
                    padding: EdgeInsetsDirectional.fromSTEB(14, 8, 8, 8),
                    child: Image(
                      image: AssetImage('assets/images/industry_template/home/<USER>'),
                      matchTextDirection: true,
                    ),
                  ),
                  suffix: Padding(
                      padding: const EdgeInsetsDirectional.only(end: 8),
                      child: GetBuilder<SearchTemplateController>(
                          id: 1,
                          builder: (controller) {
                            return Visibility(
                              visible: _textController.text.isNotEmpty,
                              child: GestureDetector(
                                  onTap: _clearAction,
                                  child: const SvgIcon(
                                      'assets/images/industry_template/search_template/search_clear.svg')),
                            );
                          })),
                  textInputAction: TextInputAction.search,
                  decoration: BoxDecoration(color: const Color(0xFFF7F7F7), borderRadius: BorderRadius.circular(20)),
                ),
              ],
            ),
          ),
          // 搜索
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () => _controller.sendEvent(_textController.text),
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 5, 16, 5),
                child: Text(
                  intlanguage('app00226', '搜索'),
                  style: const TextStyle(fontSize: 15.0, fontWeight: FontWeight.w400, color: ThemeColor.brand),
                ),
              ))
        ],
      ),
    );
  }
}

class _HistoryBanner extends StatelessWidget {
  final SearchTemplateController _controller = Get.find<SearchTemplateController>();

  _HistoryBanner({Key? key}) : super(key: key);

  _tapAction() {
    _controller.deleteAllHistory();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 16, bottom: 10),
      child: Row(
        children: [
          Text(
            intlanguage('app100000502', '历史记录'),
            style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.w600, color: KColor.title),
          ),
          const Spacer(),
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _tapAction,
              child: const Padding(
                padding: EdgeInsets.all(8.0),
                child: SizedBox(
                    width: 15,
                    height: 17,
                    child: SvgIcon('assets/images/industry_template/search_template/delete_gray.svg')),
              ))
        ],
      ),
    );
  }
}

class _HistoryItem extends StatelessWidget {
  final _controller = Get.find<SearchTemplateController>();

  _HistoryItem({Key? key}) : super(key: key);

  /// 点击事件
  _tapAction(int index) {
    var text = _controller.searchList.value?[index] ?? '';
    _controller.sendEvent(text);
    _controller.insertSearchTextToFront(index);
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GetX<SearchTemplateController>(builder: (controller) {
        return ConstrainedBox(
          constraints: BoxConstraints(minWidth: context.width - 32),
          child: SingleChildScrollView(
            child: Wrap(
              spacing: 10,
              runSpacing: 10,
              children: List.generate(controller.searchList.value?.length ?? 0, (index) {
                return GestureDetector(
                  onTap: () => _tapAction(index),
                  child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(61.0),
                          border: Border.all(color: KColor.color_divider, width: 0.5)),
                      child: Text(
                        controller.searchList.value?[index] ?? '',
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.title),
                      )),
                );
              }),
            ),
          ),
        );
      }),
    );
  }
}

class _ResultList extends StatelessWidget {
  final _controller = Get.find<SearchTemplateController>();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  /// 模版滑动管理器
  final ScrollController _scrollController = ScrollController();

  int page = 1;

  _ResultList({Key? key}) : super(key: key);

  _onRefresh() {
    Log.d('-----_onRefresh-----');
    // 拉取新一页的page
    page = 1;
    _refreshController.resetNoData();
    _controller.getSearchResult(
        page: page,
        searchKey: _controller.searchList.value?.first ?? '',
        isMoreData: false,
        resultClosure: (model) {
          if (model == null) {
            _refreshController.refreshFailed();
          } else {
            _refreshController.refreshCompleted();
          }
          _controller.update();
        });
  }

  _onLoading() {
    Log.d('-----_onLoading----');
    // 拉取新一页的page
    _controller.getSearchResult(
        page: page += 1,
        searchKey: _controller.searchList.value?.first ?? '',
        isMoreData: true,
        resultClosure: (model) {
          if (model == null) {
            _refreshController.loadFailed();
          } else if (model.list.isEmpty) {
            _refreshController.loadNoData();
          } else {
            _refreshController.loadComplete();
          }
          _controller.update();
        });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SearchTemplateController>(
      builder: (controller) {
        return _searchListState(controller, context);
      },
    );
  }

  _searchListState(SearchTemplateController controller, BuildContext context) {
    if (controller.searchState != IndustryHomeState.error) {
      return controller.templatesResult.isEmpty
          ? Container(
              padding: const EdgeInsets.only(bottom: 180),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: (controller.templatesResult.isEmpty && controller.isSearching.value == true)
                    ? []
                    : [
                  NoTemplateResultWidget(onFeedbackTap: () async{
                    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "011_453_455", "ext": {"source":2 }});
                    bool? result = await CustomNavigation.gotoPage("templateFeedback", {"source":"industry"},withContainer: true);
                    if(result == true){
                      controller.notifyState = true;
                      _controller.update();
                    }
                  },hasFeedback: controller.notifyState,),
                  // const SvgIcon(
                  //   'assets/images/no_result.svg',
                  //   matchTextDirection: true,
                  // ),
                  // SizedBox(height: 8,),
                  //       Text(
                  //         intlanguage('app100000515', '暂无相关结果'),
                  //         style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle),
                  //       ),
                  // SizedBox(height: 8,),
                  // Text(
                  //   intlanguage('app100000627', '反馈需求我们将尽快为您制作相关模板'),
                  //   style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                  // ),
                  // SizedBox(height: 16,),
                  // GestureDetector(
                  //   onTap: (){
                  //     CustomNavigation.gotoPage("templateFeedback", {},withContainer: true);
                  //   },
                  //   child: Container(
                  //       padding: EdgeInsets.symmetric(vertical: 6,horizontal: 16),
                  //       decoration: BoxDecoration(
                  //         color: true
                  //             ? ThemeColor.brand
                  //             : ThemeColor.brand.withOpacity(0.3),
                  //         borderRadius: BorderRadius.circular(18),
                  //       ),
                  //       child: Text(
                  //         intlanguage('app100000628', '我要反馈'),
                  //         textAlign: TextAlign.center,
                  //         style: TextStyle(
                  //           fontSize: 14,
                  //           color: Colors.white,
                  //           fontWeight: FontWeight.w400,
                  //         ),
                  //       )),
                  // )
                      ],
              ))
          : SmartRefresher(
              enablePullUp: true,
              controller: _refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              child: ListView.separated(
                  cacheExtent: _controller.isLabelBarcode ? 259 : 223,
                  itemCount: controller.templatesResult.length,
                  itemBuilder: (BuildContext context, int index) {
                    return _ReslutListCell(model: controller.templatesResult[index]);
                  },
                  separatorBuilder: (BuildContext context, int index) => const SizedBox(height: 12)),
            );
    } else {
      if (controller.templatesResult.length > 1 && page != 1) {
        page--;
        return SmartRefresher(
          enablePullUp: true,
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: ListView.separated(
              controller: _scrollController,
              itemCount: controller.templatesResult.length,
              itemBuilder: (BuildContext context, int index) {
                return _ReslutListCell(model: controller.templatesResult[index]);
              },
              separatorBuilder: (BuildContext context, int index) => const SizedBox(height: 12)),
        );
      } else {
        return Container(
            padding: const EdgeInsets.only(bottom: 180),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(ImageUtils.getImgPath('no_net'), fit: BoxFit.contain),
                Text(
                  intlanguage('app100000625', '当前网络状态异常'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
              ],
            ));
      }
    }
  }

  showIconToast() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            'assets/images/industry_template/home/<USER>',
            height: 20,
            width: 20,
          ),
          SizedBox(
            width: 12.0,
          ),
          Text(
            intlanguage('app100000629', '反馈成功!'),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.background),
          ),
        ],
      ),
    );
  }
}

class _ReslutListCell extends StatelessWidget {
  final SearchTemplateController _controller = Get.find<SearchTemplateController>();

  final TemplateData model;

  _ReslutListCell({Key? key, required this.model}) : super(key: key);

  _assocationTapAction() {
    // 返回标签纸元数据，作为尺寸进行筛选
    CustomNavigation.pop(result: {'labelData': _controller.templatesResult.first.rawData});
  }

  _assocationTemplate() {
    Widget _recommandTemplate;
    if (_controller.isLabelBarcode) {
      _recommandTemplate = Column(mainAxisSize: MainAxisSize.min, children: [
        const Divider(
          height: 1,
          color: KColor.color_divider,
        ),
        GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: _assocationTapAction,
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 13, vertical: 11),
                child: Row(
                  children: [
                    Text(intlanguage('app100000516', '相关推荐模板'),
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999))),
                    const Spacer(),
                    const SvgIcon(
                      'assets/images/industry_template/home/<USER>',
                      matchTextDirection: true,
                    ),
                  ],
                )))
      ]);
    } else {
      _recommandTemplate = const SizedBox.shrink();
    }
    return _recommandTemplate;
  }

  _templateTapAction() {
    // 搜索结果列表点击埋点
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "070_175",
      "ext": {
        'type': model.profile?.extrain?.templateClass == 1 ? 1 : 2,
        'material_id': model.id,
        'pos': 1,
        'source': 1,
        'key_word':_controller.currentSearchKey,
        'industry_temp_id': model.id,
      }
    });
    _controller.gotoCanvasPage(model);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _templateTapAction,
      child: Container(
        height: _controller.isLabelBarcode ? 259 : 223,
        decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFEBEBEB), width: 0.5), borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Stack(
                fit: StackFit.expand,
                children: [
                  Container(
                    decoration: const BoxDecoration(
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
                        color: Color(0xFFF7F7F7)),
                  ),
                  Positioned(
                      top: 22,
                      left: 64,
                      right: 64,
                      bottom: 22,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // RotatedBox(
                          //     quarterTurns: model.canvasRotate ~/ 90,
                          //     child: CachedNetworkImage(
                          //         imageUrl: model.backgroundImage ?? '',
                          //         errorWidget: (_, __, ___) => ThemeWidget.placeholder(
                          //             backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20)))),
                          model.previewImage == ''
                              ? Container()
                              : CacheImageUtil().netCacheImage(
                                  imageUrl: model.previewImage ?? '',
                                  errorWidget: ThemeWidget.placeholder(
                                      backgroundColor: Colors.transparent, padding: const EdgeInsets.all(20))),
                        ],
                      )),
                  _isVipTemplate(model)
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(12, 10, 12, 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(
                        TextSpan(
                          children: [
                            ..._buildTemplateFlagWidget(model),
                            TextSpan(
                                text: _controller.isLabelBarcode ? model.getDisplayName() : model.name ?? '',
                                style: const TextStyle(
                                    fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
                          ],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${model.width}x${model.height}mm',
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                      ),
                      if (model.profile?.extrain?.materialModelSn?.isNotEmpty ?? false) ...[
                        Text(
                          'ID:${model.profile?.extrain?.materialModelSn}',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                        )
                      ]
                    ],
                  ),
                ],
              ),
            ),
            _assocationTemplate(),
          ],
        ),
      ),
    );
  }

  List<WidgetSpan> _buildTemplateFlagWidget(TemplateData model) {
    List<WidgetSpan> widgets = [];
    if (model.rawData['profile']['extrain']['templateType'] == 2) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_good_template.svg',
          ),
        ),
      ));
    }
    return widgets;
  }

  _isVipTemplate(TemplateData model) {
    if (model.vip! || model.hasVipRes) {
      return PositionedDirectional(
          top: 4,
          start: 4,
          child: Container(
              child: SvgIcon(
            'assets/images/icon_flag_vip_template.svg',
          )));
    } else {
      return Container();
    }
  }
}
