//
//  NSBundle+AppLanguageSwitch.m
//  https://github.com/zengqingf/iOSAppLanguageSwitch
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/6/13.
//  Copyright © 2017年 zengqingfu. All rights reserved.
//

#import <objc/runtime.h>

static const char kBundleKey = 0;
@interface ZZBundleEx : NSBundle
@end

@implementation ZZBundleEx

- (NSString *)localizedStringForKey:(NSString *)key value:(NSString *)value table:(NSString *)tableName {
    NSBundle *bundle = objc_getAssociatedObject(self, &kBundleKey);
    if (bundle) {
        return [bundle localizedStringForKey:key value:value table:tableName];
    } else {
        return [super localizedStringForKey:key value:value table:tableName];
    }
}
@end


static NSString *AppLanguageSwitchKey = @"App_Language_Switch_Key";
static NSString *is_traditional = @"is_traditional";
@implementation NSBundle (AppLanguageSwitch)
+ (void)setCusLanguage:(NSString *)language {
    id value = nil;
    NSUserDefaults *df = [NSUserDefaults standardUserDefaults];
    if (language) {
        value = [NSBundle bundleWithPath:[[NSBundle mainBundle] pathForResource:language ofType:@"lproj"]];
        NSString *langType = [XYTool getSystemLanguageNameFromCommonString:language];
        [df setObject:langType forKey:AppLanguageSwitchKey];
        [df synchronize];
    } else {
        [df removeObjectForKey:AppLanguageSwitchKey];
        [df synchronize];
    }
//    objc_setAssociatedObject([NSBundle mainBundle], &kBundleKey, value, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_ChANGELANGUAGE object:nil];
    });
}

+ (NSString *)getCusLanguage {
    NSUserDefaults *df = [NSUserDefaults standardUserDefaults];
    NSString *language = [df objectForKey:AppLanguageSwitchKey];
    return language;
}

+ (void)restoreSysLanguage {
    [self setCusLanguage:nil];
}

+ (void)load {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
//        object_setClass([NSBundle mainBundle],[ZZBundleEx class]);
        NSString *language = [self getCusLanguage];
        if (language) {
            [self setCusLanguage:language];
        }
    });
    Class clazz = NSClassFromString(@"NSBundle");
    Method instance_mj_localized = class_getClassMethod(clazz, @selector(mj_localizedStringForKey:value:));
    Method instance_mj_localized2 = class_getClassMethod(self, @selector(mj_localizedStringForKey2:value:));
    method_exchangeImplementations(instance_mj_localized, instance_mj_localized2);
}

+ (NSString *)mj_localizedStringForKey2:(NSString *)key value:(NSString *)value
{
    // （iOS获取的语言字符串比较不稳定）目前框架只处理en、zh-Hans、zh-Hant三种情况，其他按照系统默认处理
    NSString *language = [self getCusLanguage];
    // 从MJRefresh.bundle中查找资源
    if ([language hasPrefix:@"zh"]) {
        if([XYCenter sharedInstance].is_traditional){
            language = @"zh-Hant";
        }else{
            language = @"zh-Hans";
        }
    } else if([language hasPrefix:@"ja"]){
        language = @"ja";
    }else if([language hasPrefix:@"ru"]){
        language = @"ru";
    }
    else if([language hasPrefix:@"ko"]){
        language = @"ko";
    }
    else if([language hasPrefix:@"fr"]){
        language = @"fr";
    }
    else if([language hasPrefix:@"es"]){
        language = @"es";
    }
    else if([language hasPrefix:@"de"]){
        language = @"de";
    }
    else if([language hasPrefix:@"it"]){
        language = @"it";
    }
    else {
        language = @"en";
    }
    
    NSString *langBundlePath = [[NSBundle mainBundle] pathForResource:@"MJRefreshLanguage" ofType:@"bundle"];
    NSString *path = [NSString stringWithFormat:@"%@/%@.lproj",langBundlePath,language];
    NSBundle *bundle = [NSBundle bundleWithPath:path];
    value = [bundle localizedStringForKey:key value:value table:nil];
    return [[NSBundle mainBundle] localizedStringForKey:key value:value table:nil];
}

+ (void) initLanguageSwitchSystem{
    NSString *language = [self getCusLanguage];
    NSString *currentSystemLanguage = [[NSLocale preferredLanguages] firstObject];
    if ([currentSystemLanguage hasPrefix:@"zh"]) {
        if(![currentSystemLanguage hasPrefix:@"zh-Hans"]){
            currentSystemLanguage = @"zh-Hant"; // 繁体中文
        }else{
            currentSystemLanguage = @"zh-Hans";
        }
    }else if([currentSystemLanguage hasPrefix:@"ja"]){
        currentSystemLanguage = @"ja";
    }else if([currentSystemLanguage hasPrefix:@"ru"]){
        currentSystemLanguage = @"ru";
    }
    else if([currentSystemLanguage hasPrefix:@"ko"]){
        currentSystemLanguage = @"ko";
    }
    else if([currentSystemLanguage hasPrefix:@"fr"]){
           currentSystemLanguage = @"fr";
    }
    else if([currentSystemLanguage hasPrefix:@"es"]){
           currentSystemLanguage = @"es";
    }
    else if([currentSystemLanguage hasPrefix:@"de"]){
           currentSystemLanguage = @"de";
    }
    else if([currentSystemLanguage hasPrefix:@"it"]){
           currentSystemLanguage = @"it";
    }
    else if([currentSystemLanguage hasPrefix:@"ar"]){
           currentSystemLanguage = @"ar";
    }
    else if([currentSystemLanguage hasPrefix:@"af"]){
           currentSystemLanguage = @"af";
    }
    else if([currentSystemLanguage hasPrefix:@"pt"]){
           currentSystemLanguage = @"pt";
    }
    else if([currentSystemLanguage hasPrefix:@"th"]){
           currentSystemLanguage = @"th";
    }
    else if([currentSystemLanguage hasPrefix:@"pl"]){
           currentSystemLanguage = @"Polish";
    }
    else if([currentSystemLanguage hasPrefix:@"cs"]){
           currentSystemLanguage = @"Czech";
    }
    else if([currentSystemLanguage hasPrefix:@"id"]){
           currentSystemLanguage = @"ind";
    }
    else if([currentSystemLanguage hasPrefix:@"hi"]){
           currentSystemLanguage = @"hi";
    }
    else if([currentSystemLanguage hasPrefix:@"tr"]){
           currentSystemLanguage = @"tr";
    }
    else if([currentSystemLanguage hasPrefix:@"ms"]){
           currentSystemLanguage = @"ms";
    }
    else if([currentSystemLanguage hasPrefix:@"fil"]){
           currentSystemLanguage = @"fil";
    }
    else if([currentSystemLanguage hasPrefix:@"vi"]){
           currentSystemLanguage = @"vi";
    }
    else if([currentSystemLanguage hasPrefix:@"uk"]){
           currentSystemLanguage = @"uk";
    }
    else if([currentSystemLanguage hasPrefix:@"bg"]){
           currentSystemLanguage = @"bg";
    }
    else if([currentSystemLanguage hasPrefix:@"el"]){
      currentSystemLanguage = @"el";
    }
    else{
        currentSystemLanguage = @"en";
    }
    
    if(language == nil){
        language = currentSystemLanguage;
        if([language isEqualToString:@"zh-Hant"]){
            [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:is_traditional];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }else{
            [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:is_traditional];
            [[NSUserDefaults standardUserDefaults] synchronize];
        }
        [self setCusLanguage:language];
    }
}
@end
