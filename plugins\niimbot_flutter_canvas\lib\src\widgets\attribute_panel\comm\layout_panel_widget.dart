
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/attr_icon_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/handler/layout_panel_handler.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/add_subtract_throttle_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:provider/provider.dart';

import '/src/localization/localization_public.dart';

final Logger _logger = Logger("LayoutPanelWidget", on: kDebugMode);

class LayoutPanelWidget extends StatefulWidget {
  final List<CanvasElement> canvasElements;
  final JsonElement firstJsonElement;

  final VoidCallback? refresh;

  LayoutPanelWidget({super.key, required this.canvasElements, this.refresh})
      : firstJsonElement = canvasElements.first.data;

  @override
  State<LayoutPanelWidget> createState() => _LayoutPanelWidgetState();
}

class _LayoutPanelWidgetState extends State<LayoutPanelWidget> {
  late TemplateData canvasData;
  late String dataPathInnerApp;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  List<CanvasElement> get canvasElements => widget.canvasElements;

  JsonElement get firstJsonElement => widget.firstJsonElement;

  @override
  Widget build(BuildContext context) {
    double? templateWidth =
        CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble();
    double? templateHeight =
        CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble();

    JsonElement firstJsonElement = canvasElements.first.data;

    double leftPartWidth =
        (MediaQuery.sizeOf(context).width - 16 * 2 - 10) * 0.55;
    double rightPartWidth =
        (MediaQuery.sizeOf(context).width - 16 * 2 - 10) * 0.45;
    double leftPartHeight = rightPartWidth;
    double leftPartSection01Height = (leftPartHeight - 10) / 3 * 2;
    double leftPartSection02Height = (leftPartHeight - 10) / 3 * 1;
    double rightPartHeight = leftPartSection01Height;

    List<String> hIcons = [
      'assets/element/attribute/align_left.svg',
      'assets/element/attribute/align_center_h.svg',
      'assets/element/attribute/align_right.svg',
    ];

    List<String> vIcons = [
      'assets/element/attribute/align_top.svg',
      'assets/element/attribute/align_center_v.svg',
      'assets/element/attribute/align_bottom.svg',
    ];

    List<String> uniformIcons = [
      'assets/element/attribute/align_uniform_h.svg',
      'assets/element/attribute/align_uniform_v.svg',
    ];

    List<String> mirrorIcons = [
      'assets/element/attribute/mirror_none.svg',
      'assets/element/attribute/mirror_by_center.svg',
      // 'assets/element/attribute/mirror_by_y.svg',
      // 'assets/element/attribute/mirror_by_x.svg',
    ];
    bool isContanierTable = false;
    bool hasLockedElement = false;
    for (var element in canvasElements) {
      if (element.elementType == "table") {
        isContanierTable = true;
      }
      if (element.data.isLock == 1) {
        hasLockedElement = true;
      }
    }

    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: leftPartWidth,
                    height: leftPartSection01Height,
                    decoration: BoxDecoration(
                        color: CanvasTheme.of(context)
                            .attributeGroupBackgroundColor,
                        borderRadius: BorderRadius.all(Radius.circular(12))),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: hIcons
                                .map((e) => AttrIconButton(
                                      SvgIcon(e,color: !hasLockedElement
                                      ? CanvasTheme.of(context).iconColor
                                      : CanvasTheme.of(context)
                                          .iconDisableColor),
                                      onTap: () {
                                        if (hasLockedElement) {
                                          return;
                                        }
                                        int index = hIcons.indexOf(e);
                                        String trackName = "";
                                        if (index == 0) {
                                          trackName = "左对齐";
                                        } else if (index == 1) {
                                          trackName = "水平居中";
                                        } else if (index == 2) {
                                          trackName = "右对齐";
                                        }
                                        CanvasPluginManager()
                                            .nativeMethodImpl
                                            ?.sendTrackingToNative({
                                          "track": "click",
                                          "posCode": "108_072_136",
                                          "ext": {
                                            "b_name": trackName,
                                            "module_name":
                                                TrackUtils.getElementsTypeStr(
                                                    widget.canvasElements)
                                          }
                                        });
                                        LayoutPanelHandler
                                            .handleHorizontalAdjust(
                                                context, canvasElements, index);
                                      },
                                    ))
                                .toList(),
                          ),
                        )),
                        Container(
                          width: double.infinity,
                          height: 1,
                          color: CanvasTheme.of(context).backgroundColor,
                        ),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 6.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: vIcons
                                .map((e) => AttrIconButton(
                                      SvgIcon(e,color: !hasLockedElement
                                      ? CanvasTheme.of(context).iconColor
                                      : CanvasTheme.of(context)
                                          .iconDisableColor),
                                      onTap: () {
                                        if (hasLockedElement) {
                                          return;
                                        }
                                        int index = vIcons.indexOf(e);
                                        String trackName = "";
                                        if (index == 0) {
                                          trackName = "上对齐";
                                        } else if (index == 1) {
                                          trackName = "竖直居中";
                                        } else if (index == 2) {
                                          trackName = "下对齐";
                                        }
                                        CanvasPluginManager()
                                            .nativeMethodImpl
                                            ?.sendTrackingToNative({
                                          "track": "click",
                                          "posCode": "108_072_136",
                                          "ext": {
                                            "b_name": trackName,
                                            "module_name":
                                                TrackUtils.getElementsTypeStr(
                                                    widget.canvasElements)
                                          }
                                        });
                                        LayoutPanelHandler.handleVerticalAdjust(
                                            context, canvasElements, index);
                                      },
                                    ))
                                .toList(),
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(
                width: 10,
              ),
              Container(
                width: rightPartWidth,
                height: rightPartHeight,
                decoration: BoxDecoration(
                    color:
                        CanvasTheme.of(context).attributeGroupBackgroundColor,
                    borderRadius: BorderRadius.all(Radius.circular(12))),
                padding: EdgeInsets.all(6),
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: AttrIconButton(
                        SvgIcon('assets/element/attribute/align_move_left.svg',color: !hasLockedElement
                        ? CanvasTheme.of(context).iconColor
                        : CanvasTheme.of(context)
                            .iconDisableColor),
                        onTap: () {
                          if (hasLockedElement) {
                            return;
                          }
                          canvasElements.forEach((element) {
                            element.data.x -= 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context,
                                  listen: false)
                              .setDataChangedElements(canvasElements);
                          CanvasPluginManager()
                              .nativeMethodImpl
                              ?.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_072_137",
                            "ext": {
                              "b_name": "向左",
                              "module_name": TrackUtils.getElementsTypeStr(
                                  widget.canvasElements)
                            }
                          });
                        },
                        onLongPress: () {
                          canvasElements.forEach((element) {
                            element.data.x -= 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                              .setDataChangedElements(canvasElements);
                        },
                        duration: Duration(milliseconds: 100),
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: AttrIconButton(
                        SvgIcon('assets/element/attribute/align_move_bottom.svg',color: !hasLockedElement
                        ? CanvasTheme.of(context).iconColor
                        : CanvasTheme.of(context)
                            .iconDisableColor),
                        onTap: () {
                          if (hasLockedElement) {
                            return;
                          }
                          canvasElements.forEach((element) {
                            element.data.y += 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context,
                                  listen: false)
                              .setDataChangedElements(canvasElements);
                          CanvasPluginManager()
                              .nativeMethodImpl
                              ?.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_072_137",
                            "ext": {
                              "b_name": "向下",
                              "module_name": TrackUtils.getElementsTypeStr(
                                  widget.canvasElements)
                            }
                          });
                        },
                        onLongPress: () {
                          canvasElements.forEach((element) {
                            element.data.y += 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                              .setDataChangedElements(canvasElements);
                        },
                        duration: Duration(milliseconds: 100),
                      ),
                    ),
                    Align(
                      alignment: Alignment.centerRight,
                      child: AttrIconButton(
                        SvgIcon('assets/element/attribute/align_move_right.svg',color: !hasLockedElement
                        ? CanvasTheme.of(context).iconColor
                        : CanvasTheme.of(context)
                            .iconDisableColor),
                        onTap: () {
                          if (hasLockedElement) {
                            return;
                          }
                          canvasElements.forEach((element) {
                            element.data.x += 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                              .setDataChangedElements(canvasElements);
                          CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_072_137",
                            "ext": {"b_name": "向右", "module_name": TrackUtils.getElementsTypeStr(widget.canvasElements)}
                          });
                        },
                        onLongPress: () {
                          canvasElements.forEach((element) {
                            element.data.x += 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context,
                                  listen: false)
                              .setDataChangedElements(canvasElements);
                        },
                        duration: Duration(milliseconds: 100),
                      ),
                    ),
                    Align(
                      alignment: Alignment.topCenter,
                      child: AttrIconButton(
                        SvgIcon('assets/element/attribute/align_move_top.svg',color: !hasLockedElement
                        ? CanvasTheme.of(context).iconColor
                        : CanvasTheme.of(context)
                            .iconDisableColor),
                        onTap: () {
                          if (hasLockedElement) {
                            return;
                          }
                          canvasElements.forEach((element) {
                            element.data.y -= 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context,
                                  listen: false)
                              .setDataChangedElements(canvasElements);
                          CanvasPluginManager()
                              .nativeMethodImpl
                              ?.sendTrackingToNative({
                            "track": "click",
                            "posCode": "108_072_137",
                            "ext": {
                              "b_name": "向上",
                              "module_name": TrackUtils.getElementsTypeStr(
                                  widget.canvasElements)
                            }
                          });
                        },
                        onLongPress: () {
                          canvasElements.forEach((element) {
                            element.data.y -= 0.5;
                          });
                          Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                              .setDataChangedElements(canvasElements);
                        },
                        duration: Duration(milliseconds: 100),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Container(
            height: leftPartSection01Height,
            decoration: BoxDecoration(
                color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                borderRadius: const BorderRadius.all(Radius.circular(12))),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                    child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                intlanguage('app100000789', '垂直间距'),
                                style: TextStyle(
                                    fontSize:
                                        CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType() == "ru" ? 10 : 14,
                                    fontWeight: FontWeight.w400,
                                    color: canvasElements.length > 2 ? ThemeColor.title : ThemeColor.COLOR_BFBFBF),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            GestureDetector(
                              onTap: canvasElements.length > 2 && !hasLockedElement
                                  ? () {
                                      LayoutPanelHandler
                                          .handleVerticalEquidistant(
                                              context, canvasElements);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_234",
                                      //   "ext": {
                                      //     "b_name": "垂直等距",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_234",
                                        "ext": {
                                          "b_name": "垂直等距",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                              child: SvgIcon(uniformIcons[1],
                                  color: canvasElements.length > 2 && !hasLockedElement
                                      ? CanvasTheme.of(context).iconColor
                                      : CanvasTheme.of(context)
                                          .iconDisableColor),
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                            AddSubtractThrottleButton(
                              isSubtractEnable: canvasElements.length > 2,
                              isAddEnable: canvasElements.length > 2,
                              onSubtractClicked: canvasElements.length > 2
                                  ? () {
                                      LayoutPanelHandler
                                          .handleMultiVerticalAdjust(
                                              context, canvasElements, true);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_234",
                                      //   "ext": {
                                      //     "b_name": "垂直更紧凑",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_234",
                                        "ext": {
                                          "b_name": "垂直更紧凑",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                              onAddClicked: canvasElements.length > 2
                                  ? () {
                                      LayoutPanelHandler
                                          .handleMultiVerticalAdjust(
                                              context, canvasElements, false);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_234",
                                      //   "ext": {
                                      //     "b_name": "垂直更疏松",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_234",
                                        "ext": {
                                          "b_name": "垂直更疏松",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                            ),
                          ],
                        ))),
                Container(
                  width: double.infinity,
                  height: 1,
                  color: CanvasTheme.of(context).backgroundColor,
                ),
                Expanded(
                    child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                intlanguage('app100000790', '水平间距'),
                                style: TextStyle(
                                    fontSize:
                                        CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType() == "ru" ? 10 : 14,
                                    fontWeight: FontWeight.w400,
                                    color: canvasElements.length > 2 ? ThemeColor.title : ThemeColor.COLOR_BFBFBF),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            GestureDetector(
                              onTap: canvasElements.length > 2 && !hasLockedElement
                                  ? () {
                                      LayoutPanelHandler
                                          .handleHorizontalEquidistant(
                                              context, canvasElements);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_235",
                                      //   "ext": {
                                      //     "b_name": "水平等距",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_235",
                                        "ext": {
                                          "b_name": "水平等距",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                              child: SvgIcon(uniformIcons[0],
                                  color: canvasElements.length > 2 && !hasLockedElement
                                      ? CanvasTheme.of(context).iconColor
                                      : CanvasTheme.of(context)
                                          .iconDisableColor),
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                            AddSubtractThrottleButton(
                              isSubtractEnable: canvasElements.length > 2,
                              isAddEnable: canvasElements.length > 2,
                              onSubtractClicked: canvasElements.length > 2
                                  ? () {
                                      LayoutPanelHandler
                                          .handleMultiHorizontalAdjust(
                                              context, canvasElements, true);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_235",
                                      //   "ext": {
                                      //     "b_name": "水平更紧凑",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_235",
                                        "ext": {
                                          "b_name": "水平更紧凑",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                              onAddClicked: canvasElements.length > 2
                                  ? () {
                                      LayoutPanelHandler
                                          .handleMultiHorizontalAdjust(
                                              context, canvasElements, false);
                                      // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                                      //   "track": "click",
                                      //   "posCode": "108_072_235",
                                      //   "ext": {
                                      //     "b_name": "水平更疏松",
                                      //     "module_name": TrackUtils.getElementsTypeStr(canvasElements)
                                      //   }
                                      // });
                                      TrackUtils.sendTrackingWrapTemplateId({
                                        "track": "click",
                                        "posCode": "108_072_235",
                                        "ext": {
                                          "b_name": "水平更疏松",
                                          "module_name":
                                              TrackUtils.getElementsTypeStr(
                                                  canvasElements)
                                        }
                                      });
                                    }
                                  : null,
                            ),
                          ],
                        ))),
              ],
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          if (!isContanierTable)
            Container(
              height: 44,
              decoration: BoxDecoration(
                  color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                  borderRadius: const BorderRadius.all(Radius.circular(12))),
              child: Row(
                children: [
                  const SizedBox(
                    width: 12,
                  ),
                  Text(
                    intlanguage('app01424', '镜像'),
                    style: CanvasTheme.of(context).attributeTitleTextStyle,
                  ),
                  const Spacer(),
                  // GroupButton(
                  //   key: UniqueKey(),
                  //   isRadio: true,
                  //   spacing: 6,
                  //   onSelected: (index, isSelected) {
                  //     if (index == 0) {
                  //       /// 关闭镜像
                  //       if (firstJsonElement.isOpenMirror == 1) {
                  //         firstJsonElement.isOpenMirror = 0;
                  //         firstJsonElement.mirrorId = '';
                  //         firstJsonElement.mirrorType = 0;
                  //         canvasElements.first.mirrorNeedRefresh = true;
                  //       }
                  //     } else {
                  //       if (firstJsonElement.isOpenMirror == 1) {
                  //         /// 切换镜像方式
                  //         if (index - 1 != firstJsonElement.mirrorType) {
                  //           firstJsonElement.mirrorType = index - 1;
                  //           canvasElements.first.mirrorNeedRefresh = true;
                  //         }
                  //       } else {
                  //         /// 开启镜像
                  //         firstJsonElement.isOpenMirror = 1;
                  //         firstJsonElement.mirrorType = index - 1;
                  //         canvasElements.first.mirrorNeedRefresh = true;
                  //       }
                  //     }

                  //     canvasElements.forEach((element) {
                  //       if (element != canvasElements.first) {
                  //         if (element.data.isOpenMirror != firstJsonElement.isOpenMirror ||
                  //             element.data.mirrorType != firstJsonElement.mirrorType) {
                  //           element.data.isOpenMirror = firstJsonElement.isOpenMirror;
                  //           element.data.mirrorType = firstJsonElement.mirrorType;
                  //           canvasElements.first.mirrorNeedRefresh = true;
                  //         }
                  //       }
                  //     });

                  //     Provider.of<ElementsDataChangedNotifier>(context, listen: false)
                  //         .setDataChangedElements(canvasElements);
                  //     widget.refresh?.call();
                  //   },
                  //   isIconButtons: true,
                  //   buttonWidth: 50,
                  //   buttonHeight: 34,
                  //   unselectedColor: Colors.transparent,
                  //   selectedColor: CanvasTheme.of(context).backgroundLightColor,
                  //   borderRadius: BorderRadius.all(Radius.circular(8)),
                  //   buttons: mirrorIcons,
                  //   selectedButtons: [
                  //     if (firstJsonElement.isOpenMirror == 0) mirrorIcons.first,
                  //     if (firstJsonElement.isOpenMirror == 1) mirrorIcons[firstJsonElement.mirrorType + 1],
                  //   ],
                  //   unselectedShadow: null,
                  // ),
                  const Spacer(),
                  Transform.scale(
                      scale: 1,
                      child: CupertinoSwitch(
                        activeColor: const Color(0xFFFB4B42),
                        thumbColor: const Color(0xFFFFFFFF),
                        trackColor: const Color(0xFFF5F5F5),
                        value: openMirrorValue() == 1,
                        onChanged: firstJsonElement.isLock == 1 ? null : (value) {
                          // CanvasPluginManager().nativeMethodImpl.sendTrackingToNative({
                          //   "track": "click",
                          //   "posCode": "108_069_098",
                          //   "ext": {"b_name": "镜像", "module_name": TrackUtils.getElementsTypeStr(canvasElements)}
                          // });
                          TrackUtils.sendTrackingWrapTemplateId({
                            "track": "click",
                            "posCode": "108_069_098",
                            "ext": {
                              "b_name": "镜像",
                              "module_name":
                                  TrackUtils.getElementsTypeStr(canvasElements)
                            }
                          });
                          JsonElement firstJsonElement =
                              canvasElements.first.data;
                          if (!value) {
                            /// 关闭镜像
                            if (firstJsonElement.isOpenMirror == 1) {
                              firstJsonElement.isOpenMirror = 0;
                              firstJsonElement.mirrorId = '';
                              firstJsonElement.mirrorType = 0;
                              canvasElements.first.mirrorNeedRefresh = true;
                            }
                          } else {
                            firstJsonElement.isOpenMirror = 1;
                            firstJsonElement.mirrorType = 0;
                            canvasElements.first.mirrorNeedRefresh = true;
                          }
                          canvasElements.forEach((element) {
                            if (element != canvasElements.first) {
                              if (element.data.isOpenMirror !=
                                  firstJsonElement.isOpenMirror) {
                                element.data.isOpenMirror =
                                    firstJsonElement.isOpenMirror;
                                element.data.mirrorType =
                                    firstJsonElement.mirrorType;
                                element.data.mirrorId = '';
                                element.mirrorNeedRefresh = true;
                              }
                            }
                          });
                          Provider.of<ElementsDataChangedNotifier>(context,
                                  listen: false)
                              .setDataChangedElements(canvasElements);
                          widget.refresh?.call();
                        },
                      )),
                  SizedBox(
                    width: 6,
                  ),
                ],
              ),
            ),
          SizedBox(
            height: MediaQuery.paddingOf(context).bottom,
          )
        ],
      ),
    );
  }

  int openMirrorValue() {
    int value = 0;
    JsonElement firstJsonElement = canvasElements.first.data;
    if (widget.canvasElements.length == 1) {
      value = firstJsonElement.isOpenMirror;
    } else {
      bool isSameValue = true;
      int firstValue = firstJsonElement.isOpenMirror;
      for (var element in widget.canvasElements) {
        if (firstValue != element.data.isOpenMirror) {
          isSameValue = false;
        }
      }
      if (!isSameValue) {
        value = 0;
      } else {
        value = firstValue;
      }
    }
    return value;
  }

  void _notifyCanvasElementsDataChanged() {
    Provider.of<ElementsDataChangedNotifier>(context, listen: false)
        .setDataChangedElements(widget.canvasElements);
  }
}
