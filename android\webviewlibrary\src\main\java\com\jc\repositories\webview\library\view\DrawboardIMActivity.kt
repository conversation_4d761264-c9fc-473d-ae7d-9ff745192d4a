package com.jc.repositories.webview.library.view

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.view.View
import android.view.WindowManager
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ThreadUtils
import com.jc.repositories.webview.library.R
import com.jc.repositories.webview.library.databinding.ActivityDrawboardImBinding
import com.jc.repositories.webview.library.view.webview.BaseWebChromeClient
import com.jc.repositories.webview.library.view.webview.BaseWebClient
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.visible
import com.niimbot.utiliylibray.util.SystemUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import melon.south.com.baselibrary.base.viewBinding
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.math.min


class DrawboardIMActivity : AppCompatActivity() {

    val FILE_CHOOSER_REQUEST_CODE = 1
    private var cameraPhotoPath: String? = null
    private var cameraVideoPath: String? = null

    private val binding by viewBinding(ActivityDrawboardImBinding::inflate)
    private var imLink = ""
    private var wv_base: LollipopFixedWebView? = null
    private var permissionListener: ((Boolean) -> Unit)? = null

    // 请求一组权限
    private val requestMultiplePermissions =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { result: Map<String, Boolean> ->
            var allGranted = true
            result.entries.forEach {
                allGranted = allGranted && it.value
            }
            permissionListener?.invoke(allGranted)
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        overridePendingTransition(com.niimbot.appframework_library.R.anim.slide_in_up, 0)
        setContentView(R.layout.activity_drawboard_im)
        com.niimbot.appframework_library.utils.AppUtils.setStatusBarColor(
            this,
            ColorUtils.getColor(R.color.transparent)
        )
        try {
            KeyboardUtils.fixAndroidBug5497(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        imLink = intent.getStringExtra("imLink")!!
        binding.progressBar.visible(false)
        binding.webviewContainer.removeAllViews()
        wv_base = JCWebViewFactory.getInstance().createWebView(this)
        wv_base?.apply {
            clearHistory()
            // 设置与Js交互的权限
            settings?.javaScriptEnabled = true

            val originUA = settings?.userAgentString
            if (originUA != null && !originUA.contains("AppId/com.gengcon.android.jccloudprinter", true)) {
                settings?.userAgentString =
                    originUA + " " + SystemUtil.getUserAgent(this@DrawboardIMActivity)
            }
            var userAgentStr = settings?.userAgentString
            LogUtils.d("ShopLoadInitializeUtil UA == $userAgentStr")
            webViewClient = BaseWebClient(this@DrawboardIMActivity)
            webChromeClient = BaseWebChromeClient(this@DrawboardIMActivity)
            if (null != savedInstanceState) {
                this.restoreState(savedInstanceState)
            } else {
                loadUrl(imLink)
            }
            binding.webviewContainer?.addView(this@apply)
            binding.progressBar.visible()
        }
        binding.pageCloseIcon.setOnNotDoubleClickListener {
            finish()
        }
    }

    fun doLoading(progress: Int) {
        if (progress >= 100) {
            GlobalScope.launch(Dispatchers.Main) {
                binding.progressBar?.visibility = View.VISIBLE
                binding.progressBar?.progress = min(progress, 100)
                delay(50)
                binding.progressBar?.visibility = View.GONE
            }
        } else {
            binding.progressBar?.visibility = View.VISIBLE
            binding.progressBar?.progress = progress
        }
    }

    fun openFileChooseProcess(fileChooserParams: WebChromeClient.FileChooserParams?) {
        val acceptTypes = fileChooserParams!!.acceptTypes
        if (acceptTypes.size > 0) {
            val acceptType = acceptTypes[0]
            if (acceptType == "video/*" || acceptType == "image/*") {
                requestCameraPermission {
                    if (!it) return@requestCameraPermission
                    if (acceptType == "image/*") {
                        openImageChooser()
                    } else {
                        openVideoChooser()
                    }
                }
                return
            }
        }
        openFileChooser()
    }

    var filePathCallback: ValueCallback<Array<Uri>>? = null
    fun openImageChooser() {
        // 创建用于调用相机的Intent
        var takePictureIntent: Intent? = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
        if (takePictureIntent!!.resolveActivity(packageManager) != null) {
            // 创建用于存储拍摄图片的文件
            var photoFile: File? = null
            try {
                photoFile = createImageFile()
                takePictureIntent.putExtra("PhotoPath", cameraPhotoPath)
            } catch (ex: IOException) {
                // 错误处理
                ex.printStackTrace()
            }
            if (photoFile != null) {
                cameraPhotoPath = "file:" + photoFile.absolutePath
                val photoURI = FileProvider.getUriForFile(
                    this,
                    "com.gengcon.android.jccloudprinter.fileProvider",
                    photoFile
                )
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI)
            } else {
                takePictureIntent = null
            }
        }
        startActivityForResult(takePictureIntent!!, FILE_CHOOSER_REQUEST_CODE)
    }

    @Throws(IOException::class)
    private fun createImageFile(): File? {
        // 创建图片文件名
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val imageFileName = "JPEG_" + timeStamp + "_"
        val storageDir = getExternalFilesDir(null)
        return File.createTempFile(imageFileName, ".jpg", storageDir)
    }

    fun openVideoChooser() {
        // 创建用于调用相机录像的Intent
        var takeVideoIntent: Intent? = Intent(MediaStore.ACTION_VIDEO_CAPTURE)
        if (takeVideoIntent!!.resolveActivity(packageManager) != null) {
            // 创建用于存储录像文件
            var videoFile: File? = null
            try {
                videoFile = createVideoFile()
                takeVideoIntent.putExtra("VideoPath", cameraVideoPath)
            } catch (ex: IOException) {
                // 错误处理
                ex.printStackTrace()
            }
            if (videoFile != null) {
                cameraVideoPath = "file:" + videoFile.absolutePath
                val videoURI = FileProvider.getUriForFile(
                    this,
                    "com.gengcon.android.jccloudprinter.fileProvider",
                    videoFile
                )
                takeVideoIntent.putExtra(MediaStore.EXTRA_OUTPUT, videoURI)
            } else {
                takeVideoIntent = null
            }
        }
        startActivityForResult(takeVideoIntent!!, FILE_CHOOSER_REQUEST_CODE)
    }

    @Throws(IOException::class)
    private fun createVideoFile(): File? {
        // 创建视频文件名
        val timeStamp: String = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
        val videoFileName = "MP4_" + timeStamp + "_"
        val storageDir = getExternalFilesDir(null)
        return File.createTempFile(videoFileName, ".mp4", storageDir)
    }

    fun openFileChooser() {
        val takePictureIntent = Intent(Intent.ACTION_GET_CONTENT)
        takePictureIntent.setType("*/*")
        startActivityForResult(takePictureIntent, FILE_CHOOSER_REQUEST_CODE)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode === FILE_CHOOSER_REQUEST_CODE && resultCode === RESULT_OK) {
            var results: Array<Uri>? = null
            if (resultCode === RESULT_OK) {
                if (data == null || data.getData() == null) {
                    if (cameraPhotoPath != null) {
                        results = arrayOf(Uri.parse(cameraPhotoPath))
                    }
                } else {
                    val dataString: String? = data.getDataString()
                    if (dataString != null) {
                        results = arrayOf(Uri.parse(dataString))
                    }
                }
            }
            filePathCallback?.onReceiveValue(results)
            filePathCallback = null
        } else {
            if (filePathCallback != null) {
                filePathCallback!!.onReceiveValue(null)
                filePathCallback = null
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        XPermissionUtils.onRequestPermissionsResult(this, requestCode, permissions, grantResults)
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun onDestroy() {
        binding.webviewContainer.removeAllViews()
        wv_base?.let {
            if (!isDestroyed) {
                JCWebViewFactory.getInstance().recycleWebView(it)
                it.loadUrl("about:blank")
                ThreadUtils.runOnUiThreadDelayed({ it.clearHistory() }, 500)
            }
        }
        super.onDestroy()
    }

    override fun finish() {
        super.finish()
//        overridePendingTransition(0, R.anim.slide_out_down)
    }

    private fun requestCameraPermission(callback: (Boolean) -> Unit) {
        PermissionDialogUtils.showCameraPermissionDialog(
            this,
            RequestCode.MORE,
            object : XPermissionUtils.OnPermissionListener {
                override fun onPermissionGranted() {
                    callback.invoke(true)
                }

                override fun onPermissionDenied(
                    deniedPermissions: Array<String>,
                    alwaysDenied: Boolean
                ) {
                    if (filePathCallback != null) {
                        filePathCallback!!.onReceiveValue(null)
                        filePathCallback = null
                    }
                    com.niimbot.appframework_library.utils.showToast("app01309")
                    callback.invoke(false)
                }
            })
    }
}
