package com.niimbot.appframework_library.common.module.template.item

import com.niimbot.fastjson.annotation.JSONField

/**
 * 图片
 */
class PictureItemModule : BaseItemModuleEx() {
    //图片base64数据
    var imageData = ""
    //图片本地保存路径
    var localUrl = ""
    var localImageUrl = ""
    //图片保存在服务器的url
    var imageUrl = ""
    //算法类型，1:阈值法，2:渐变
    var imageProcessingType = 1
    //算法参数
    var imageProcessingValue = arrayListOf<Int>()
    //素材库图片的id，不是素材库图片时为空
    var materialId: String = ""
    //素材id有值的情况下类型：图标=1，边框=2; 素材id 为空的情况下图片=1,涂鸦=2, 类型为2的能自由拉伸,
    var materialType: String? = ""
    //是否点9图, 点9图作为图片下的特殊类型
    @JSONField(name = "isNinePatch")
    var isNinePatch = false
    @JSONField(name = "isDefaultImage")
    var isDefaultImage = false
    //点9图本地路径Url
    var ninePatchLocalUrl = ""
    //点9图服务器地址,用于网络传输，上传到服务器存储
    var ninePatchUrl = ""


    override fun isEdit(compareItemModuleEx: BaseItemModuleEx): Boolean {
        if(super.isEdit(compareItemModuleEx)){
            return true
        }

        val comparePictureItemModule = compareItemModuleEx as PictureItemModule
        if(comparePictureItemModule.imageProcessingValue != imageProcessingValue){
            return true
        }
        if(comparePictureItemModule.materialId != materialId){
            return true
        }
        return false
    }
}
