import 'dart:convert';
import 'dart:math' as math;

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/display_util.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/netal_image_result.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:netal_plugin/niimbot_netal.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/utils/rfid_util.dart';
import 'package:niimbot_flutter_canvas/src/widgets/elements/interactive_element.dart';

Logger _logger = Logger("Table", on: kDebugMode);

class TableWidget extends StatefulWidget {
  final TemplateData? templateData;
  final TableElement tableElement;
  final bool isFocus;
  final Map<String, dynamic>? usedFonts;
  final VoidCallback? onCellFocusChanged;
  final ValueChanged<TableCellElement>? onCellDoubleTap;

  const TableWidget({
    super.key,
    required this.templateData,
    required this.tableElement,
    this.isFocus = false,
    required this.usedFonts,
    this.onCellFocusChanged,
    this.onCellDoubleTap,
  });

  @override
  State<StatefulWidget> createState() {
    return TableState();
  }
}

class TableCell {
  TableCellElement tableCellElement;

  bool isCombine;
  String id;
  String combineId;
  Rect rect;

  TableCell({
    required this.tableCellElement,
    required this.id,
    this.isCombine = false,
    required this.combineId,
    this.rect = Rect.zero,
  });
}

class TableState extends State<TableWidget> {
  late List<TableCell> _tableCells;

  @override
  void initState() {
    super.initState();
  }

  void calculateCellsRect() {
    _tableCells = [];

    List<TableCell> cells = [];

    /// 整理表格 cell
    for (int row = 0; row < widget.tableElement.row; row++) {
      for (int column = 0; column < widget.tableElement.column; column++) {
        final cellElement = widget.tableElement.cells
            .singleWhereOrNull((element) => element.rowIndex == row && element.columnIndex == column);

        if (cellElement != null) {
          TableCell cell = TableCell(
            tableCellElement: cellElement,
            id: cellElement.id,
            combineId: cellElement.combineId,
          );
          double y = 0.0;
          for (int index = 0; index < row; index++) {
            y += widget.tableElement.rowHeight[index];
          }
          double x = 0.0;
          for (int index = 0; index < column; index++) {
            x += widget.tableElement.columnWidth[index];
          }
          cell.rect = Rect.fromLTWH(
              (x + column * widget.tableElement.lineWidth),
              (y + row * widget.tableElement.lineWidth),
              (widget.tableElement.columnWidth[column] + widget.tableElement.lineWidth * 2),
              (widget.tableElement.rowHeight[row] + widget.tableElement.lineWidth * 2));
          cell.tableCellElement.width = cell.rect.width - 2 * widget.tableElement.lineWidth;
          cell.tableCellElement.height = cell.rect.height - 2 * widget.tableElement.lineWidth;

          cells.add(cell);
        }
      }
    }

    /// 整理合并的单元格, 计算合并单元格的 rect
    widget.tableElement.combineCells.forEach((combineElement) {
      TableCell combineCell = TableCell(
        tableCellElement: combineElement,
        id: combineElement.id,
        isCombine: true,
        combineId: combineElement.combineId,
      );

      /// 查找选中单元格的左上
      CellPosition topLeft = CellPosition(widget.tableElement.column - 1, widget.tableElement.row - 1);

      cells.where((element) => element.combineId == combineElement.id).forEach((element) {
        if (combineCell.rect == Rect.zero) {
          combineCell.rect = element.rect;
        } else {
          combineCell.rect = combineCell.rect.expandToInclude(element.rect);
        }

        if (element.tableCellElement.columnIndex < topLeft.columnIndex) {
          topLeft.columnIndex = element.tableCellElement.columnIndex;
        }
        if (element.tableCellElement.rowIndex < topLeft.rowIndex) {
          topLeft.rowIndex = element.tableCellElement.rowIndex;
        }
      });

      /// 查找合并单元格包含的左上角的表格
      /// 绑定到合并单元格的数据结构上
      final firstCellOfCombine = cells
          .singleWhereOrNull((element) =>
              element.tableCellElement.rowIndex == topLeft.rowIndex &&
              element.tableCellElement.columnIndex == topLeft.columnIndex)
          ?.tableCellElement;
      combineCell.tableCellElement.firstCellOfCombine = firstCellOfCombine;

      /// 添加合并的单元格到待绘制表格
      _tableCells.add(combineCell);

      /// 将第一次整理的完整 cell 剔除包含在合并单元格里的 cell
      cells.removeWhere((element) => element.combineId == combineElement.id);
    });

    /// 将已经剔除包含在合并单元格里 cell 的剩余 cell 加入待绘制表格
    _tableCells.addAll(cells);
  }

  late StateSetter _selectionStateSetter;

  @override
  Widget build(BuildContext context) {
    calculateCellsRect();
    refreshCellsSelection();

    //return Container(width: widget.tableElement.width * DisplayUtil.dpRatio,height: widget.tableElement.height * DisplayUtil.dpRatio,color: Colors.transparent,);
    return Container(
      width: widget.tableElement.width * DisplayUtil.dpRatio,
      height: widget.tableElement.height * DisplayUtil.dpRatio,
      color: Colors.transparent,
      child: Stack(
        children: _tableCells.map((e) {
          Rect cellRect = Rect.fromLTWH(e.rect.left * DisplayUtil.dpRatio, e.rect.top * DisplayUtil.dpRatio,
              e.rect.width * DisplayUtil.dpRatio, e.rect.height * DisplayUtil.dpRatio);
          return Positioned.fromRect(
              key: Key(e.id),
              rect: cellRect,
              child: widget.isFocus
                  ? GestureDetector(
                      onTap: () {
                        if (e.tableCellElement.focused == true &&
                            _tableCells.where((element) => element.tableCellElement.focused == true).length == 1) {
                          ///2023/12/20 Ice_Liu 选中状态下的cell，再次点击触发输入
                          widget.tableElement.clearFocused();
                          e.tableCellElement.focused = true;
                          widget.onCellDoubleTap?.call(e.tableCellElement);
                        } else {
                          widget.tableElement.clearFocused();
                          e.tableCellElement.focused = true;
                          widget.onCellFocusChanged?.call();
                        }
                      },
                      onDoubleTap: () {
                        // if (widget.tableElement.isLock == 1) {
                        //   return;
                        // }
                        widget.tableElement.clearFocused();
                        e.tableCellElement.focused = true;

                        widget.onCellDoubleTap?.call(e.tableCellElement);
                      },
                      onTapDown: (TapDownDetails details) =>
                          _tapDownOffset = details.localPosition + Offset(cellRect.left, cellRect.top),
                      onPanStart: null,
                      onPanUpdate: e.tableCellElement.focused == true
                          ? (DragUpdateDetails details) {
                              _latestLocalPosition = details.localPosition;
                              _handlePanUpdate(details.localPosition + Offset(cellRect.left, cellRect.top));
                            }
                          : null,
                      onPanEnd: e.tableCellElement.focused == true
                          ? (DragEndDetails details) {
                              _handlePanUpdate(_latestLocalPosition + Offset(cellRect.left, cellRect.top),
                                  isPanEnd: true);
                              widget.onCellFocusChanged?.call();
                            }
                          : null,
                      child: buildTableCell(e),
                    )
                  : buildTableCell(e));
        }).toList()
          ..add(Positioned.fill(child: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              _selectionStateSetter = setState;
              return Stack(
                children: [
                  ...[
                    Positioned.fromRect(
                      rect: _selectionRect,
                      child: Offstage(
                        offstage: _selectionRect.isEmpty,
                        child: IgnorePointer(
                          child: Container(
                              margin: EdgeInsets.all(widget.tableElement.lineWidth * DisplayUtil.dpRatio),
                              decoration: BoxDecoration(
                                  border: Border.all(color: Color(0xFF00A7FF), width: 2),
                                  color: CanvasTheme.of(context).cellSelectionColor)),
                        ),
                      ),
                    ),
                    // ..._dotFrameWidget()
                  ]
                ],
              );
            },
          ))),
      ),
    );
  }

  // List<Widget> _dotFrameWidget() {
  //   List<Widget> dotsWidgets = [];
  //   CellPosition topLeft = widget.tableElement.findFocusedCellsTopLeft();
  //   CellPosition bottomRight =
  //       widget.tableElement.findFocusedCellsBottomRight();
  //
  //   ///绘制左上角，右下角圆点
  //   if (topLeft.columnIndex != 0 && topLeft.rowIndex != 0) {
  //     dotsWidgets.add(Positioned(
  //         left: _selectionRect.left +
  //             widget.tableElement.lineWidth * DisplayUtil.dpRatio +
  //             1 -
  //             8,
  //         top: _selectionRect.top +
  //             widget.tableElement.lineWidth * DisplayUtil.dpRatio +
  //             1 -
  //             8,
  //         child: Container(
  //           alignment: Alignment.center,
  //           width: 16,
  //           height: 16,
  //           decoration: BoxDecoration(
  //             shape: BoxShape.circle,
  //             color: Colors.white,
  //           ),
  //           child: Container(
  //             width: 12,
  //             height: 12,
  //             decoration: BoxDecoration(
  //               shape: BoxShape.circle,
  //               color: Color(0xFF00A7FF),
  //             ),
  //           ),
  //         )));
  //   }
  //
  //   ///只要不是最右下角的点就绘制,最右下角什么也不做，防止和元素右下角拖拽点重叠
  //   if (bottomRight.columnIndex != widget.tableElement.column - 1 &&
  //       bottomRight.rowIndex != widget.tableElement.row - 1) {
  //     dotsWidgets.add(Positioned.fromRect(
  //         rect: Rect.fromLTWH(
  //             _selectionRect.right -
  //                 widget.tableElement.lineWidth * DisplayUtil.dpRatio -
  //                 1 -
  //                 8,
  //             _selectionRect.bottom -
  //                 widget.tableElement.lineWidth * DisplayUtil.dpRatio -
  //                 1 -
  //                 8,
  //             16,
  //             16),
  //         child: Container(
  //           alignment: Alignment.center,
  //           width: 16,
  //           height: 16,
  //           decoration: BoxDecoration(
  //             shape: BoxShape.circle,
  //             color: Colors.white,
  //           ),
  //           child: Container(
  //             width: 12,
  //             height: 12,
  //             decoration: BoxDecoration(
  //               shape: BoxShape.circle,
  //               color: Color(0xFF00A7FF),
  //             ),
  //           ),
  //         )));
  //   }
  //   return dotsWidgets;
  // }

  Offset _latestLocalPosition = Offset.zero;
  late Offset _tapDownOffset;
  late Rect _selectionRect;

  _handlePanUpdate(Offset offset, {bool isPanEnd = false}) {
    String focusedIdsBefore =
        _tableCells.where((e) => e.tableCellElement.focused == true).map((e) => e.tableCellElement.id).join('|');

    bool isContinue;

    do {
      isContinue = false;
      bool isCombineCellAdded = false;

      double margin = widget.tableElement.lineWidth * DisplayUtil.dpRatio + 2;
      Rect panRect = isPanEnd
          ? Rect.fromLTRB(_selectionRect.left + margin, _selectionRect.top + margin, _selectionRect.right - margin,
              _selectionRect.bottom - margin)
          : Rect.fromLTRB(
              math.min(_tapDownOffset.dx, offset.dx),
              math.min(_tapDownOffset.dy, offset.dy),
              math.max(_tapDownOffset.dx, offset.dx),
              math.max(_tapDownOffset.dy, offset.dy),
            );

      _tableCells.forEach((e) {
        Rect cellRect = Rect.fromLTWH(e.rect.left * DisplayUtil.dpRatio, e.rect.top * DisplayUtil.dpRatio,
            e.rect.width * DisplayUtil.dpRatio, e.rect.height * DisplayUtil.dpRatio);

        /// 判断矩形是否重叠
        bool isOverlaps = cellRect.overlaps(panRect);
        if (isOverlaps != e.tableCellElement.focused) {
          e.tableCellElement.focused = isOverlaps;

          /// 标记是否新增了合并的单元格
          /// 新增了合并的单元格, 则需要递归计算框选的矩形范围
          if (e.isCombine && isOverlaps) {
            isCombineCellAdded = true;
          }
        }
      });

      /// 检测是否新增了合并的单元格
      /// 新增合并的单元格后，保证选中为一个矩形，需将周边矩形内的单元格设置为选中状态
      if (isCombineCellAdded) {
        isContinue = true;
        refreshCellsSelection();
      }
    } while (isPanEnd && isContinue);

    String focusedIdsAfter =
        _tableCells.where((e) => e.tableCellElement.focused == true).map((e) => e.tableCellElement.id).join('|');

    /// 检测滑动事件前后被选中的 cell 是否变化
    if (focusedIdsBefore != focusedIdsAfter) {
      refreshCellsSelection();
      _selectionStateSetter(() {});
    }
  }

  refreshCellsSelection() {
    _selectionRect = generateCellsSelection();
  }

  generateCellsSelection() {
    Rect? selectionRect;
    _tableCells.where((e) => e.tableCellElement.focused == true).forEach((e) {
      Rect cellRect = Rect.fromLTWH(e.rect.left * DisplayUtil.dpRatio, e.rect.top * DisplayUtil.dpRatio,
          e.rect.width * DisplayUtil.dpRatio, e.rect.height * DisplayUtil.dpRatio);

      if (selectionRect == null) {
        selectionRect = cellRect;
      } else {
        selectionRect = selectionRect?.expandToInclude(cellRect);
      }
    });
    return selectionRect ?? Rect.zero;
  }

  Container buildTableCell(TableCell e) {
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    Color boardColor = Colors.black;
    if (printColor.isNotEmpty && !RfidUtil.is16GrayColorStr(printColor)) {
      boardColor = Color.fromARGB(255, int.parse(printColor.split(".")[0]), int.parse(printColor.split(".")[1]),
          int.parse(printColor.split(".")[2]));
    } else {
      if ((widget.tableElement.lineColor ?? []).isNotEmpty) {
        boardColor = Color.fromARGB(255, widget.tableElement.lineColor![1], widget.tableElement.lineColor![2],
            widget.tableElement.lineColor![3]);
      }
    }
    if (RfidUtil.supportSixteenGrayPrint) {
      boardColor = Colors.black;
    }
    return Container(
      child: buildCellImage(e),
      decoration: BoxDecoration(
        /// 使用背景色填充的方式显示选中
        // color: e.tableCellElement.focused == true
        //     ? CanvasTheme.of(context).cellSelectionColor
        //     : Colors.transparent, //boardColor
        // color: e.tableCellElement.focused == true ? Colors.red : null,
        border: Border.all(color: boardColor, width: widget.tableElement.lineWidth * DisplayUtil.dpRatio),
      ),
    );
    // return Stack(
    //   children: [
    //     Positioned(child: Container()),
    //   ],
    // );
  }

  Widget buildCellImage(TableCell e) {
    e.tableCellElement.width = e.rect.width - 2 * widget.tableElement.lineWidth;
    e.tableCellElement.height = e.rect.height - 2 * widget.tableElement.lineWidth;
    // if ((e.tableCellElement.value ?? '').length == 0 && e.tableCellElement.isBinding != 1) {
    if ((e.tableCellElement.value ?? '').length == 0 && !e.tableCellElement.isBindingElement()) {
      return Container();
    }
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    Color? contentColor = Colors.black;
    if (printColor.isNotEmpty && !RfidUtil.is16GrayColorStr(printColor)) {
      contentColor = Color.fromARGB(255, int.parse(printColor.split(".")[0]), int.parse(printColor.split(".")[1]),
          int.parse(printColor.split(".")[2]));
    } else {
      contentColor = null;
    }
    NetalImageResult? imageData;
    if ( // e.tableCellElement.focused == true ||
        e.tableCellElement.imageCache == null) {
      e.tableCellElement.width = e.rect.width - 2 * widget.tableElement.lineWidth;
      e.tableCellElement.height = e.rect.height - 2 * widget.tableElement.lineWidth;

      // _logger.log('->table cell width: ${e.cellObject.width}, height: ${e.cellObject.height}');
      e.tableCellElement.paperColorIndex = widget.tableElement.contentColorChannel;
      e.tableCellElement.colorChannel = widget.tableElement.contentColorChannel;
      e.tableCellElement.elementColor = widget.tableElement.contentColor;
      var canvasData = TemplateData(
        width: 50,
        height: 30,
        canvasElements: [e.tableCellElement.toCanvasElement()],
        usedFonts: widget.usedFonts,
      );
      var elementCanvasDataJsonStr = jsonEncode(canvasData.toJson(
          escapeValue: true,
          escapeBindingMethod: widget.templateData?.existBatchBindingData() == true
              ? (JsonElement jsonElement) {
                  return widget.templateData
                      ?.escapeBindingValue(jsonElement, widget.templateData?.currentPageIndex, parseContentAffix: true);
                }
              : null));

      // _logger.log('->table cell: $elementCanvasDataJsonStr');

      imageData = NiimbotNetal.generateImageFromElementJson(
          jsonString: elementCanvasDataJsonStr, ratio: DisplayUtil.pxRatio, color: contentColor);
      e.tableCellElement.imageCache = imageData;
    } else {
      imageData = e.tableCellElement.imageCache;
    }
    return Stack(
      children: [
        Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            top: 0,
            child: Offstage(
              offstage: !((e.tableCellElement.focused ?? false) && e.tableCellElement.isBindingElement()),
              child: Container(
                decoration: BoxDecoration(),
                clipBehavior: Clip.hardEdge,
                child: CustomPaint(
                  painter: BingingElementBGCustomPainter(0, 0),
                ),
              ),
            )),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          top: 0,
          child: !(imageData != null && imageData.pixels.isNotEmpty)
              ? Container()
              : Stack(
                  children: [
                    Positioned(
                        left: 0.0,
                        top: 0.0,
                        child: Image.memory(
                          imageData.pixels,
                          /** 避免闪动 */
                          gaplessPlayback: true,
                          fit: BoxFit.none,
                          // alignment: alignment,
                          width: imageData.width.px2dp(),
                          height: imageData.height.px2dp(),
                          scale: DisplayUtil.pxRatio / DisplayUtil.dpRatio,
                        ))
                  ],
                ),
        ),
      ],
    );
  }
}
