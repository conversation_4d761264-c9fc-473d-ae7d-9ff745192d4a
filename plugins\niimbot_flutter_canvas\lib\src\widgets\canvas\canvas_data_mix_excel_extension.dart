import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:get/get.dart';
import 'package:niimbot_excel/models/data_bind_modify.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/models/interface.dart';
import 'package:niimbot_excel/models/range.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/bar_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/json_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/qr_code_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/data_import_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/stack/stack_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/template_data.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/provider/floating_bar_visible_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_data_mix.dart';
import 'package:niimbot_flutter_canvas/src/widgets/rfid_bind/rfid_repository.dart';
import 'package:provider/provider.dart';

Logger _logger = Logger("MixNbCanvasData", on: kDebugMode);

enum ElementImportResult {
  ///导入前后没有变化
  notChangeImport,

  ///导入结果正好匹配元素
  matchImport,

  ///导入结果不匹配元素（新导入多项，或新导入一项但类型与元素不匹配）
  notMatchImport
}

extension CanvasDataMixExcelExtension on CanvasDataMixState {
  ///变更excel文件
  handleChangeExcel(ExcelDataModel? excelData, {bool commodityChangeModify = true}) {
    TemplateData? templateData = widget.templateData;
    if (excelData == null) {
      return;
    }
    RfidRepository rfidRepository = RfidRepository();
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    String excelIdBefore = dataSource?.hash ?? "";
    String excelIdAfter = excelData.excelId ?? "";
    bool excelFileChanged = excelIdBefore != excelIdAfter;
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    if (excelFileChanged) {
      excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = templateData?.currentPageIndex;
    }
    excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData?.modify);
    List<JsonElement> elementsOfBefore = [];
    List<CanvasElement>? beforeBindingElements = templateData?.getAllBindingExcelCanvasElements(true);
    beforeBindingElements?.forEach((element) {
      elementsOfBefore.add(element.data.clone(keepId: true));
    });
    //删除默认元素
    if (templateData?.canvasElements != null &&
        templateData?.canvasElements.length == 1 &&
        templateData?.canvasElements.first.data.type == ElementItemType.text &&
        ((templateData?.canvasElements.first.data.value == "") ||
            (templateData?.canvasElements.first.data.value == intlanguage('app00364', '双击文本框编辑')))) {
      CanvasElement? defaultCanvasElement = templateData?.canvasElements.removeAt(0);
      if (defaultCanvasElement?.data != null) {
        elementsOfBefore.add(defaultCanvasElement!.data.clone(keepId: true));
      }
    }

    excelBusinessSnap.elementsOfBefore = elementsOfBefore;

    //注入数据源
    _injectExcelData(templateData, excelData, []);
    //如果数据源有更换的话，则清空modify信息
    if (excelFileChanged && commodityChangeModify) {
      templateData?.modify?.clear();
    }
    _elementImportOrderV2(templateData, beforeBindingElements, excelData, commodityChangeModify);
    DataImportUtils.clearBindingMap();
    excelBusinessSnap.excelFileChanged = excelFileChanged;
    if (excelFileChanged) {
      excelBusinessSnap.dataSourceAfter = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexAfter = templateData?.currentPageIndex;
    }
    excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(templateData?.modify);
    excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
    List<JsonElement> elementsOfAfter = [];
    templateData?.getAllBindingExcelElements(true)?.forEach((element) {
      elementsOfAfter.add(element.clone(keepId: true));
    });

    ///表格元素在导入后有可能变为非绑定元素，但是表格不会被删除所以也要记录在变动后的元素内
    // tableElements.forEach((element) {
    //   if (!element.isBindingElement()) {
    //     elementsOfAfter.add(element.clone(keepId: true));
    //   }
    // });
    beforeBindingElements?.where((element) => element.data.type == ElementItemType.table).forEach((element) {
      if (!element.data.isBindingElement()) {
        elementsOfAfter.add(element.data.clone(keepId: true));
      }
    });

    excelBusinessSnap.elementsOfAfter = elementsOfAfter;
    StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
  }

  _elementImportOrderV2(TemplateData? templateData, List<CanvasElement>? originBeforeBindingElements,
      ExcelDataModel excelData, bool commodityChangeModify) {
    for (var i = 0; i < (originBeforeBindingElements ?? []).length; i++) {
      originBeforeBindingElements?[i].resetImageCache();
    }
    List<CanvasElement>? beforeBindingElements =
        originBeforeBindingElements?.where((element) => !element.data.isMirrorElement()).toList();
    // 按照y值从小到大排序，y值相等则按照x值从小到大排序
    beforeBindingElements?.sort((a, b) {
      if (a.data.y != b.data.y) {
        return a.data.y.compareTo(b.data.y); // 按照y值从小到大排序
      } else {
        return a.data.x.compareTo(b.data.x); // y值相等时，按照x值从小到大排序
      }
    });
    //处理数据灌入逻辑
    //原始绑定元素中如果存在表格的话，表格单元格中有对应的列则更新，无对应的列则清除对应的单元格cell
    List<ExcelBindPair> bindPairs = excelData?.bindPairs?.where((element) => element.open == true)?.toList() ?? [];
    List<CanvasElement> delCanvasElement = [];
    List<ExcelBindPair> toAddBindPairs = [];
    //新创建的元素集合
    List<JsonElement> createElements = [];

    ///获取模版中的动态元素，动态元素为表格的话，摊平表格中的动态元素
    List<JsonElement> beforeDynamicJsonElements = [];
    for (var i = 0; i < (beforeBindingElements ?? []).length; i++) {
      JsonElement jsonElement = beforeBindingElements![i].data;
      if (jsonElement.type == ElementItemType.table) {
        TableElement tableElement = jsonElement as TableElement;

        ///动态单元格元素进行排序
        List<TableCellElement> cells = [];
        cells.addAll(tableElement
            .getBindingExcelCells()
            .where((element) => element.combineId == null || element.combineId.isEmpty));
        cells.sort((a, b) {
          int aC = a.columnIndex ?? 0;
          int bC = b.columnIndex ?? 0;
          int aR = a.rowIndex ?? 0;
          int bR = b.rowIndex ?? 0;
          if (aR != bR) {
            return aR.compareTo(bR); // 按照y值从小到大排序
          } else {
            return aC.compareTo(bC); // y值相等时，按照x值从小到大排序
          }
        });
        cells.addAll(tableElement.getBindingExcelCombineCells());
        beforeDynamicJsonElements.addAll(cells);
      } else {
        beforeDynamicJsonElements.add(jsonElement);
      }
    }
    var bindingResult = DataImportUtils.processBindingsV2(beforeDynamicJsonElements, bindPairs);
    var bindPairAndElementMap = bindingResult.bindPairAndElementMap;

    ///删除原模版中多余的动态元素
    for (var i = 0; i < bindingResult.toDelElement.length; i++) {
      JsonElement tempJsonElement = bindingResult.toDelElement[i];
      if (tempJsonElement is TableCellElement) {
        ///清除单元格绑定关系
        tempJsonElement.dataBind = null;
        tempJsonElement.value = "";
        TemplateUtils.delElementModify(tempJsonElement);
      } else {
        CanvasElement? toDelCanvasElement =
            beforeBindingElements?.firstWhereOrNull((element) => element.data.id == tempJsonElement.id);
        if (toDelCanvasElement != null) {
          delCanvasElement.add(toDelCanvasElement);
        }
      }
    }
    //添加多出的bindPair前，当前画板映射后的元素集合---用于计算bindPair的添加起始位置
    List<JsonElement> currentElementsBeforeAddPair = [];

    ///模版中的动态元素和导入的列一一对应
    for (var i = 0; i < bindPairs.length; i++) {
      ExcelBindPair bindPair = bindPairs[i];

      ///没有对应关系的bindPair直接添加
      JsonElement? dynamicJsonElement = bindPairAndElementMap[bindPair];
      if (dynamicJsonElement == null) {
        toAddBindPairs.add(bindPair);
        continue;
      }

      ///模版中的对应位置动态元素和对应位置导入的列---类型一致，直接替换
      if (bindPair.bindElementType == dynamicJsonElement.type) {
        if (dynamicJsonElement is TableCellElement) {
          currentElementsBeforeAddPair.add(dynamicJsonElement.tableElement!);
        } else {
          currentElementsBeforeAddPair.add(dynamicJsonElement);
        }
        if (dynamicJsonElement.type == ElementItemType.barcode) {
          (dynamicJsonElement as BarCodeElement).codeType = bindPair.codeType;
          (dynamicJsonElement).dataBind = excelData.dataBind;
          int codeType = bindPair.codeType;
          if (codeType == BarcodeType.UPC_A ||
              codeType == BarcodeType.UPC_E ||
              codeType == BarcodeType.EAN8 ||
              codeType == BarcodeType.EAN13) {
            (dynamicJsonElement).textPosition = 0;
          }
        } else if (dynamicJsonElement.type == ElementItemType.qrcode) {
          (dynamicJsonElement as QrCodeElement).codeType = bindPair.codeType;
          (dynamicJsonElement).dataBind = excelData.dataBind;
        } else if (dynamicJsonElement.type == ElementItemType.text) {
          (dynamicJsonElement as TextElement).dataBind = excelData.dataBind;
          if (commodityChangeModify) {
            TemplateUtils.changeElementModify(dynamicJsonElement, ElementModifyType.modifyUseTitle,
                displayHeader: excelData.displayHeader ?? false);
          }
        }
        dynamicJsonElement.value = ExcelTransformManager().getCellPlaceHolder(bindPair.columnIndex);
        dynamicJsonElement.bindingColumn = bindPair.columnIndex;
        //如果该元素存在镜像元素，则一并修改下
        if ((dynamicJsonElement.mirrorId ?? '').length > 0) {
          JsonElement? dynamicJsonElementMirror =
              originBeforeBindingElements?.firstWhere((element) => element.data.id == dynamicJsonElement.mirrorId).data;
          dynamicJsonElementMirror?.value = ExcelTransformManager().getCellPlaceHolder(bindPair.columnIndex);
          dynamicJsonElementMirror?.bindingColumn = bindPair.columnIndex;
          dynamicJsonElementMirror?.dataBind = excelData.dataBind;

          if (dynamicJsonElement.type == ElementItemType.barcode && dynamicJsonElementMirror != null) {
            (dynamicJsonElementMirror as BarCodeElement).codeType = (dynamicJsonElement as BarCodeElement).codeType;
            (dynamicJsonElementMirror).textPosition = (dynamicJsonElement).textPosition;
          }

          if (dynamicJsonElement.type == ElementItemType.qrcode && dynamicJsonElementMirror != null) {
            (dynamicJsonElementMirror as QrCodeElement).codeType = (dynamicJsonElement as QrCodeElement).codeType;
          }
        }
      }

      ///模版中的对应位置动态元素和对应位置导入的列---类型不一致，并且该对应位置不是单元格,则删除模版对应位置元素，并在删除元素位置重新添加元素
      if (bindPair.bindElementType != dynamicJsonElement.type) {
        if (!(dynamicJsonElement is TableCellElement)) {
          CanvasElement? toDelCanvasElement =
              beforeBindingElements?.firstWhereOrNull((element) => element.data.id == dynamicJsonElement.id);
          if (toDelCanvasElement != null) {
            delCanvasElement.add(toDelCanvasElement);
          }
          //排版时考虑旋转
          double ancorX;
          double ancorY;
          if (dynamicJsonElement.rotate == 90 || dynamicJsonElement.rotate == 270) {
            ancorX = dynamicJsonElement.x.toDouble() + 0.5 * (dynamicJsonElement.width - dynamicJsonElement.height);
            ancorY = dynamicJsonElement.y.toDouble() + 0.5 * (dynamicJsonElement.height - dynamicJsonElement.width);
          } else {
            ancorX = dynamicJsonElement.x.toDouble();
            ancorY = dynamicJsonElement.y.toDouble();
          }
          List<JsonElement> elements1 =
              _createElementFromPosition(templateData, ancorX, ancorY, [bindPair], excelData.displayHeader);
          createElements.addAll(elements1);
          currentElementsBeforeAddPair.add(elements1[0]);
        } else {
          ///清除单元格绑定关系
          dynamicJsonElement.dataBind = null;
          dynamicJsonElement.value = "";
          TemplateUtils.delElementModify(dynamicJsonElement);

          ///对应位置导入列添加至待添加列表
          toAddBindPairs.add(bindPair);
          currentElementsBeforeAddPair.add(dynamicJsonElement.tableElement!);
        }
      }
    }
    _delCanvasElements(templateData, delCanvasElement);
    //基于最后一个动态元素作为锚点
    if (toAddBindPairs.isNotEmpty) {
      double x = 2;
      double y = 2;
      List<JsonElement>? unBindingElements = templateData?.canvasElements
          ?.map((canvasElement) => canvasElement.data)
          ?.where((e) => !e.isBindingElement())
          ?.toList();
      List<JsonElement> mergedElements = [...?unBindingElements, ...currentElementsBeforeAddPair];
      if (mergedElements.isNotEmpty) {
        double maxY = 0;
        double lowElementX = 0;
        double tempX;
        double tempY;
        for (JsonElement element in mergedElements) {
          if (element.isMirrorElement()) {
            continue;
          }
          //排版时考虑旋转
          if (element.rotate == 90 || element.rotate == 270) {
            tempX = element.x.toDouble() + 0.5 * (element.width - element.height);
            tempY = element.y.toDouble() + 0.5 * (element.height + element.width);
          } else {
            tempX = element.x.toDouble();
            tempY = element.y.toDouble() + element.height;
          }
          if (maxY < tempY) {
            lowElementX = tempX.toDouble();
          }
          maxY = max(maxY, tempY.toDouble());
        }
        x = lowElementX;
        y = maxY;
      }
      // _addElementToCanvas(templateData, x,y, toAddBindPairs, excelData.displayHeader);
      List<JsonElement> elements2 =
          _createElementFromPosition(templateData, x, y, toAddBindPairs, excelData.displayHeader);
      createElements.addAll(elements2);
    }
    //把新创建的元素添加到画布
    addCanvasElementsFromDynamicSource(createElements.map((e) => e.toCanvasElement()).toList(), bindPairs);
  }

  /// 通过元素（文本、条码、二维码和表格cell）导入Excel
  ElementImportResult handleElementImportExcelResult(CanvasElement importExcelElement, ExcelDataModel excelData) {
    RfidRepository rfidRepository = RfidRepository();
    TemplateData? templateData = widget.templateData;
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    String excelIdBefore = dataSource?.hash ?? "";
    String excelIdAfter = excelData.excelId ?? "";
    bool excelFileChanged =
        excelIdBefore != excelIdAfter || excelData.type == DataSourceType.commodity.getStringValue();
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    if (excelFileChanged) {
      excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = templateData?.currentPageIndex;
    }
    excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData?.modify);
    excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    List<JsonElement> elementsOfBefore = [];
    excelBusinessSnap.elementsOfBefore = elementsOfBefore;
    JsonElement jsonElement = importExcelElement.data;
    if (!jsonElement.isBindingExcel()) {
      if (jsonElement is TableCellElement) {
        elementsOfBefore.add(jsonElement.tableElement!.clone(keepId: true));
      } else {
        elementsOfBefore.add(jsonElement.clone(keepId: true));
        String mirrorId = jsonElement.mirrorId;
        if (mirrorId.isNotEmpty) {
          final mirrorJsonElement =
              templateData?.canvasElements.firstWhereOrNull((element) => element.elementId == mirrorId)?.data;
          if (mirrorJsonElement != null) {
            elementsOfBefore.add(mirrorJsonElement.clone(keepId: true));
          }
        }
      }
    }
    _injectExcelData(templateData, excelData, []);
    List<JsonElement> afterElements = _addImportedColumn(templateData, importExcelElement, excelData);
    excelBusinessSnap.excelFileChanged = excelFileChanged;
    if (excelFileChanged) {
      excelBusinessSnap.dataSourceAfter = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexAfter = templateData?.currentPageIndex;
    }
    excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(templateData?.modify);
    excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
    List<JsonElement> elementsOfAfter = [];
    afterElements.forEach((element) {
      elementsOfAfter.add(element.clone(keepId: true));
    });
    excelBusinessSnap.elementsOfAfter = elementsOfAfter;
    StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
    return ElementImportResult.matchImport;
  }

  ElementImportResult handlUpdateBindDataResult(CanvasElement importExcelElement, ExcelDataModel excelData) {
    TemplateData? templateData = widget.templateData;
    templateData?.injectExcelData(excelData);
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    // String excelIdBefore = templateData.externalData?.id ?? "";
    String excelIdBefore = dataSource?.hash ?? "";
    String excelIdAfter = excelData.excelId ?? "";
    bool excelFileChanged = excelIdBefore != excelIdAfter;
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    if (excelFileChanged) {
      // excelBusinessSnap.externalDataBefore = templateData.externalData?.clone();
      excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = templateData?.currentPageIndex;
      // excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData.modify);
    }
    excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData?.modify);
    List<JsonElement> elementsOfBefore = [];
    templateData?.getAllBindingExcelElements(true)?.forEach((element) {
      elementsOfBefore.add(element.clone(keepId: true));
    });
    excelBusinessSnap.elementsOfBefore = elementsOfBefore;
    JsonElement jsonElement = importExcelElement.data;
    if (!jsonElement.isBindingExcel()) {
      if (jsonElement is TableCellElement) {
        elementsOfBefore.add(jsonElement.tableElement!.clone(keepId: true));
      } else {
        elementsOfBefore.add(jsonElement.clone(keepId: true));
        String mirrorId = jsonElement.mirrorId ?? "";
        if (mirrorId.isNotEmpty) {
          JsonElement? mirrorJsonElement =
              templateData?.canvasElements.firstWhereOrNull((element) => element.elementId == mirrorId)?.data;
          if (mirrorJsonElement != null) {
            elementsOfBefore.add(mirrorJsonElement.clone(keepId: true));
          }
        }
      }
    }
    goodsImport();
    ElementImportResult elementImportResult = ElementImportResult.matchImport;
    return elementImportResult;
  }

  ///删除画布上指定的元素
  _delCanvasElements(TemplateData? templateData, List<CanvasElement> deleteCanvasElements) {
    List<CanvasElement>? canvasElements = templateData?.canvasElements;
    focusedElements.removeWhere((element) => deleteCanvasElements.contains(element));
    canvasElements?.removeWhere((element) => deleteCanvasElements.contains(element));
    deleteCanvasElements.forEach((canvasElement) {
      /// 删除镜像
      if ((canvasElement.data.mirrorId ?? '').length > 0) {
        canvasElements?.removeWhere((element) => element.data.id == canvasElement.data.mirrorId);
        canvasElement.data.isOpenMirror = 0;
        canvasElement.data.mirrorId = '';
      }
    });
  }

  ///从元素静态转动态字段，新增元素
  List<JsonElement> _addImportedColumn(
      TemplateData? templateData, CanvasElement importExcelElement, ExcelDataModel excelData) {
    List<JsonElement> afterElements = [];
    JsonElement jsonElement = importExcelElement.data;
    CanvasElement? mirrorCanvasElement;
    String mirrorId = jsonElement.mirrorId ?? "";
    if (mirrorId.isNotEmpty) {
      mirrorCanvasElement = templateData
          // .getAllBindingExcelCanvasElements(true)
          ?.canvasElements
          .firstWhereOrNull((element) => element.elementId == mirrorId);
    }
    JsonElement? mirrirElement = mirrorCanvasElement?.data;

    if (jsonElement is TableCellElement) {
      TemplateData? templateData = widget.templateData;
      TableElement tableElement = jsonElement.tableElement!;
      CanvasElement? importExcelTable =
          templateData?.canvasElements.firstWhere((element) => element.elementId == tableElement.id);
      importExcelTable?.resetImageCache();
      List<ExcelBindPair> bindPairs = excelData.bindPairs.where((element) => element.open == true).toList() ?? [];
      int columnIndex = bindPairs[0].columnIndex;
      jsonElement.bindingColumn = columnIndex;
      jsonElement.dataBind = excelData.dataBind;
      jsonElement.value = ExcelTransformManager().getCellPlaceHolder(columnIndex);
      TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle,
          displayHeader: excelData.displayHeader ?? false);
      widget.onElementFocusChanged?.call(focusedElements, 0);
      afterElements.add(tableElement);
    } else {
      TemplateData? templateData = widget.templateData;
      List<ExcelBindPair> bindPairs = excelData.bindPairs.where((element) => element.open == true).toList() ?? [];
      List<ExcelBindPair> newAddBindPairs = [...bindPairs];

      ///如果有和锚点元素类型一致的，则替换，否则走删除锚点元素的逻辑
      ExcelBindPair? typeBindPair =
          newAddBindPairs.firstWhereOrNull((bindPair) => bindPair.bindElementType == importExcelElement.data.type);
      if (typeBindPair != null) {
        importExcelElement.resetImageCache();
        mirrorCanvasElement?.resetImageCache();
        afterElements.add(jsonElement);
        newAddBindPairs.remove(typeBindPair);
        int columnIndex = typeBindPair.columnIndex;
        jsonElement.bindingColumn = columnIndex;
        jsonElement.dataBind = excelData.dataBind;
        jsonElement.value = ExcelTransformManager().getCellPlaceHolder(columnIndex);
        if (mirrirElement != null) {
          afterElements.add(mirrirElement);
          mirrirElement.bindingColumn = columnIndex;
          mirrirElement.dataBind = excelData.dataBind;
          mirrirElement.value = jsonElement.value;
        }
        //更新锚点元素以及镜像元素
        if (jsonElement.type == ElementItemType.barcode) {
          (jsonElement as BarCodeElement).codeType = typeBindPair.codeType;
          int codeType = typeBindPair.codeType;
          _changeBarcodeTextPosition(codeType, jsonElement as BarCodeElement);
          if (mirrirElement != null) {
            (mirrirElement as BarCodeElement).codeType = typeBindPair.codeType;
            _changeBarcodeTextPosition(codeType, mirrirElement as BarCodeElement);
          }
        } else if (jsonElement.type == ElementItemType.qrcode) {
          (jsonElement as QrCodeElement).codeType = typeBindPair.codeType;
          if (mirrirElement != null) {
            (mirrirElement as QrCodeElement).codeType = typeBindPair.codeType;
          }
        } else if (jsonElement.type == ElementItemType.text) {
          TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle,
              displayHeader: excelData.displayHeader ?? false);
        }
      } else {
        ///删除锚点元素
        _delCanvasElements(templateData, [importExcelElement]);
      }

      /// 创建元素 坐标以锚点元素作为起始 往下排
      double x = jsonElement.x.toDouble();
      double y = jsonElement.y.toDouble();
      if (typeBindPair != null) {
        if (jsonElement.rotate == 90 || jsonElement.rotate == 270) {
          x = jsonElement.x + 0.5 * (jsonElement.width - jsonElement.height);
          y = jsonElement.y + 0.5 * (jsonElement.height + jsonElement.width);
        } else {
          x = jsonElement.x.toDouble();
          y = jsonElement.y.toDouble() + jsonElement.height;
        }
        // y = jsonElement.y + jsonElement.height;
      }
      if (newAddBindPairs.isNotEmpty) {
        List<JsonElement> createElements =
            _createElementFromPosition(templateData, x, y, newAddBindPairs, excelData.displayHeader);
        afterElements.addAll(createElements);
        addCanvasElementsFromDynamicSource(createElements.map((e) => e.toCanvasElement()).toList(), bindPairs,
            focus: true);
      } else {
        widget.onElementFocusChanged?.call(focusedElements, 0);
      }
    }
    return afterElements;
  }

  _changeBarcodeTextPosition(int codeType, BarCodeElement barCodeElement) {
    if (codeType == BarcodeType.UPC_A ||
        codeType == BarcodeType.UPC_E ||
        codeType == BarcodeType.EAN8 ||
        codeType == BarcodeType.EAN13) {
      barCodeElement.textPosition = 0;
    }
  }

  ///从特定位置创建元素//
  List<JsonElement> _createElementFromPosition(
      TemplateData? templateData, double startX, double startY, List<ExcelBindPair> bindPairs, bool? importWithHeader) {
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    List<String>? dataBind = null;
    if (dataSource != null) {
      if (dataSource.type == DataSourceType.excel) {
        String sheetName = TemplateUtils.getSheetName(dataSource);
        dataBind = [dataSource.hash, sheetName];
      } else if (dataSource.type == DataSourceType.commodity) {
        dataBind = ["", "commodity"];
      }
    }

    /// 创建元素 坐标以锚点元素作为起始 往下排
    double x = startX;
    double y = startY;
    double elementWidth;
    double elementHeight;
    List<JsonElement> elementList = [];
    bindPairs.forEach((element) {
      String valuePlaceHolder = ExcelTransformManager().getCellPlaceHolder(element.columnIndex);
      elementWidth = (templateData?.width ?? 0) - 4.0;
      if (element.bindElementType == ElementItemType.text) {
        if (elementWidth < 10.0) {
          elementWidth = 10.0;
        }
        elementHeight = 4.0;
        TextElement textElement = TextElement(
          id: JsonElement.generateId(),
          isBinding: 1,
          bindingColumn: element.columnIndex,
          width: elementWidth,
          height: elementHeight,
          x: x,
          y: y,
          fontSize: 3.2,
          rotate: 0,
          fontStyle: [],
          textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
          letterSpacing: 0,
          lineSpacing: 0,
          value: valuePlaceHolder,
          dataBind: dataBind,
          wordSpacing: 0,
          lineMode: 2,
          lineBreakMode: 1,
          textAlignVertical: 0, // 6.0.4版本更换为顶对齐
          zIndex: 0,
          textDirection: CanvasHelper.currentTextDirection,
          isEditing: true,
        );
        elementList.add(textElement);
        TemplateUtils.changeElementModify(textElement, ElementModifyType.modifyUseTitle,
            displayHeader: importWithHeader ?? false, isGlobalEffective: true);
        y += 4.0;
      } else if (element.bindElementType == ElementItemType.barcode) {
        // if (elementWidth > 20.0) {
        elementWidth = 20.0;
        // }
        elementHeight = 0.5 * elementWidth;
        BarCodeElement barCodeElement = BarCodeElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            rotate: 0,
            fontSize: 3.2,
            // value: "123456",
            value: valuePlaceHolder,
            dataBind: dataBind,
            textPosition: 0,
            textHeight: 3.4,
            codeType: element.codeType);
        elementList.add(barCodeElement);
        y = y + elementHeight + 2.0;
      } else if (element.bindElementType == ElementItemType.qrcode) {
        elementWidth = 10.0;
        if (element.codeType == QrcodeType.PDF417) {
          elementHeight = 2.0;
        } else {
          elementHeight = elementWidth;
        }
        QrCodeElement qrCodeElement = QrCodeElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            // value: "123456",
            value: valuePlaceHolder,
            dataBind: dataBind,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            rotate: 0,
            correctLevel: 0,
            codeType: element.codeType);
        elementList.add(qrCodeElement);
        y = y + elementHeight + 2.0;
      }
    });
    return elementList;
  }

  ///添加元素到画板
  List<JsonElement> _addElementToCanvas(
      TemplateData? templateData, double startX, double startY, List<ExcelBindPair> bindPairs, bool importWithHeader) {
    int bindLength = bindPairs.length ?? 0;
    bool focused = bindLength == 1 ? true : false;
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    List<String>? dataBind = null;
    if (dataSource != null) {
      if (dataSource.type == DataSourceType.excel) {
        String sheetName = TemplateUtils.getSheetName(dataSource);
        dataBind = [dataSource.hash, sheetName];
      } else if (dataSource.type == DataSourceType.commodity) {
        dataBind = ["", "commodity"];
      }
    }

    /// 创建元素 坐标以锚点元素作为起始 往下排
    // JsonElement jsonElement = importExcelElement.data;
    double x = startX;
    double y = startY;
    double elementWidth;
    double elementHeight;
    List<JsonElement> elementList = [];
    bindPairs.forEach((element) {
      String valuePlaceHolder = ExcelTransformManager().getCellPlaceHolder(element.columnIndex);
      elementWidth = (templateData?.width ?? 0) - 4.0;
      if (element.bindElementType == ElementItemType.text) {
        if (elementWidth < 10.0) {
          elementWidth = 10.0;
        }
        elementHeight = 8.0;
        TextElement textElement = TextElement(
          id: JsonElement.generateId(),
          isBinding: 1,
          bindingColumn: element.columnIndex,
          width: elementWidth,
          height: elementHeight,
          x: x,
          y: y,
          fontSize: 3.2,
          rotate: 0,
          fontStyle: [],
          textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
          letterSpacing: 0,
          lineSpacing: 0,
          value: valuePlaceHolder,
          dataBind: dataBind,
          wordSpacing: 0,
          lineMode: 2,
          lineBreakMode: 1,
          textAlignVertical: 0, // 6.0.4版本更换为顶对齐
          zIndex: 0,
          textDirection: CanvasHelper.currentTextDirection
        );
        elementList.add(textElement);
        TemplateUtils.changeElementModify(textElement, ElementModifyType.modifyUseTitle,
            displayHeader: importWithHeader, isGlobalEffective: true);
        addCanvasElement(textElement.toCanvasElement(), focused: focused, stack: false, fromBusiness: true);
        y += 4.0;
      } else if (element.bindElementType == ElementItemType.barcode) {
        // if (elementWidth > 20.0) {
        elementWidth = 20.0;
        // }
        elementHeight = 0.5 * elementWidth;
        BarCodeElement barCodeElement = BarCodeElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            rotate: 0,
            fontSize: 3.2,
            // value: "123456",
            value: valuePlaceHolder,
            dataBind: dataBind,
            textPosition: 0,
            textHeight: 3.4,
            codeType: element.codeType);
        elementList.add(barCodeElement);
        addCanvasElement(barCodeElement.toCanvasElement(), focused: focused, fromBusiness: true, stack: false);
        y = y + elementHeight + 2.0;
      } else if (element.bindElementType == ElementItemType.qrcode) {
        // if (elementWidth > 10.0) {
        elementWidth = 10.0;
        // }
        if (element.codeType == QrcodeType.PDF417) {
          elementHeight = 2.0;
        } else {
          elementHeight = elementWidth;
        }
        QrCodeElement qrCodeElement = QrCodeElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            // value: "123456",
            value: valuePlaceHolder,
            dataBind: dataBind,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            rotate: 0,
            correctLevel: 0,
            codeType: element.codeType);
        elementList.add(qrCodeElement);
        addCanvasElement(qrCodeElement.toCanvasElement(), focused: focused, fromBusiness: true, stack: false);
        y = y + elementHeight + 2.0;
      }
    });
    return elementList;
  }

  ElementImportResult _handleElementImportExcelResultInner(
      TemplateData templateData, CanvasElement importExcelElement, ExcelDataModel excelData) {
    bool displayHeader = excelData.displayHeader ?? false;
    // ExternalData externalData = templateData.externalData;
    JsonElement jsonElement = importExcelElement.data;
    if (jsonElement is TableCellElement) {
      // if (externalData?.id == excelData.excelId) {
      if (templateData.getExcelMd5() == excelData.excelId) {
        if (jsonElement.bindingColumn == excelData.bindPairs.firstWhere((element) => element.open).columnIndex) {
          ///导入前后没有任何变化
          if ((jsonElement.showExcelTitle() && displayHeader) || (!jsonElement.showExcelTitle() && !displayHeader)) {
            _logger.log("CanvasDataMixExcelExtension: table cell import not changed");
            return ElementImportResult.notChangeImport;
          }
        }
      }
      _handleTableCellImportExcelResult(jsonElement, excelData);

      ///导入结果匹配表格单元格
      _logger.log("CanvasDataMixExcelExtension: table cell import match");
      return ElementImportResult.matchImport;
    } else {
      // if (externalData?.id == excelData.excelId && !_isBindingChanged(templateData, excelData)) {
      if (templateData.getExcelMd5() == excelData.excelId && !_isBindingChanged(templateData, excelData)) {
        _logger.log("CanvasDataMixExcelExtension: common element import not changed");
        if (excelData.excelId.isEmpty) {
          goodsImport();
        }
        return ElementImportResult.notChangeImport;
      }
      ElementImportResult elementImportResult = _handleCommonElementImportExcelResult(importExcelElement, excelData);
      if (elementImportResult == ElementImportResult.matchImport) {
        _logger.log("CanvasDataMixExcelExtension: common element import match");
      } else {
        _logger.log("CanvasDataMixExcelExtension: common element import not match");
      }
      return elementImportResult;
    }
  }

  /// 通过普通元素（文本、条码和二维码）导入Excel
  ElementImportResult _handleCommonElementImportExcelResult(
      CanvasElement importExcelElement, ExcelDataModel? excelData) {
    TemplateData? templateData = widget.templateData;
    List<ExcelBindPair> bindPairs = excelData?.bindPairs.where((element) => element.open == true).toList() ?? [];
    List<ExcelBindPair> newBindPairs = [];
    // if (templateData.externalData?.id != excelData?.excelId) {
    if (templateData?.getExcelMd5() != excelData?.excelId) {
      newBindPairs = bindPairs;
    } else {
      List<CanvasElement> bindingExcelElements = templateData?.getAllBindingExcelCanvasElements(false) ?? [];
      if (bindingExcelElements.isEmpty) {
        newBindPairs = bindPairs;
      } else {
        bindPairs.forEach((bindPair) {
          if (!bindingExcelElements
              .any((element) => _isElementBindingExcelPair(element.data, bindPair, excelData?.displayHeader))) {
            newBindPairs.add(bindPair);
          }
        });
      }
    }
    bool matchElement = false;
    JsonElement jsonElement = importExcelElement.data;

    // List<String> dataBind = ExcelTransformManager().getElementDataBind();
    List<String>? dataBind = excelData?.dataBind;

    ///只有一个新增绑定项，并且绑定项与待绑定的元素类型匹配，则该元素进行Excel关联
    if (newBindPairs.length == 1) {
      ExcelBindPair excelBindPair = newBindPairs[0];
      int columnIndex = excelBindPair.columnIndex;
      if (jsonElement is BarCodeElement) {
        if (excelBindPair.bindElementType == ElementItemType.barcode) {
          // jsonElement.isBinding = 1;
          jsonElement.bindingColumn = excelBindPair.columnIndex;
          jsonElement.dataBind = dataBind;
          jsonElement.value = ExcelTransformManager().getCellPlaceHolder(excelBindPair.columnIndex);
          jsonElement.codeType = excelBindPair.codeType;
          jsonElement.switchBarCodeType(excelBindPair.codeType);
          matchElement = true;
        }
      } else if (jsonElement is QrCodeElement) {
        if (excelBindPair.bindElementType == ElementItemType.qrcode) {
          // jsonElement.isBinding = 1;
          jsonElement.bindingColumn = excelBindPair.columnIndex;
          jsonElement.dataBind = dataBind;
          jsonElement.value = ExcelTransformManager().getCellPlaceHolder(excelBindPair.columnIndex);
          jsonElement.codeType = excelBindPair.codeType;
          matchElement = true;
        }
      } else {
        TextElement textElement = jsonElement as TextElement;
        if (excelBindPair.bindElementType == ElementItemType.text) {
          // textElement.isBinding = 1;
          textElement.bindingColumn = columnIndex;
          jsonElement.dataBind = dataBind;
          jsonElement.value = ExcelTransformManager().getCellPlaceHolder(columnIndex);
          // textElement.isTitle = excelData.displayHeader;
          // textElement.contentTitle = excelData.displayHeader ? excelData.headers[columnIndex] : null;
          TemplateUtils.changeElementModify(textElement, ElementModifyType.modifyUseTitle,
              displayHeader: excelData?.displayHeader ?? false);
          matchElement = true;
        }
      }
    }

    ///否则，该元素取消Excel关联
    if (!matchElement) {
      // jsonElement.isBinding = 0;
      // jsonElement.bindingColumn = -1;
      jsonElement.dataBind = null;
      jsonElement.value = "";
      if (jsonElement is TextElement) {
        // jsonElement.isTitle = false;
        // jsonElement.contentTitle = null;
        TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle, displayHeader: false);
      }
    }
    // if (templateData.externalData?.id == excelData?.excelId) {
    if (templateData?.getExcelMd5() == excelData?.excelId) {
      _handleImportSameExcel(templateData, excelData, importExcelElement: importExcelElement);
    } else {
      int bindLength = excelData?.bindPairs.where((element) => element.open == true).length ?? 0;
      _handleImportDifferentExcel(templateData, excelData,
          importExcelElement: importExcelElement, focused: !matchElement && bindLength == 1);
    }
    if (importExcelElement.data.isBindingExcel()) {
      return ElementImportResult.matchImport;
    } else {
      return ElementImportResult.notMatchImport;
    }
  }

  ///模板绑定Excel是否发生变化
  bool _isBindingChanged(TemplateData templateData, ExcelDataModel? excelData) {
    int oldBindColumnCount = templateData.getBindExcelColumnCount();
    bool importWithHeader = excelData?.displayHeader ?? false;
    List<ExcelBindPair> bindPairs = excelData?.bindPairs.where((element) => element.open == true).toList() ?? [];
    if (oldBindColumnCount == bindPairs.length) {
      List<JsonElement> bindingExcelJsonElements = templateData.getAllBindingExcelElements(false) ?? [];
      for (int i = 0; i < bindingExcelJsonElements.length; i++) {
        JsonElement jsonElement = bindingExcelJsonElements[i];
        if (jsonElement is TextElement) {
          // bool elementDisplayHeader = jsonElement.isTitle ?? false;
          // if (jsonElement.contentTitle != null) {
          //   elementDisplayHeader = true;
          // }
          bool elementDisplayHeader = false;
          DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(jsonElement);
          if (dataBindModify != null && dataBindModify.useTitle == true) {
            elementDisplayHeader = true;
          }
          if (elementDisplayHeader != importWithHeader) {
            return true;
          }
          if (!bindPairs.any((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
            return true;
          }
        } else if (jsonElement is TableElement) {
          List<TableCellElement> cells = jsonElement.getBindingExcelCells();
          for (int j = 0; j < cells.length; j++) {
            TableCellElement cell = cells[j];
            // bool cellDisplayHeader = cell.isTitle ?? false;
            // if (cell.contentTitle != null) {
            //   cellDisplayHeader = true;
            // }
            bool cellDisplayHeader = false;
            DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(cell);
            if (dataBindModify != null && dataBindModify.useTitle == true) {
              cellDisplayHeader = true;
            }

            if (cellDisplayHeader != importWithHeader) {
              return true;
            }
            if (!bindPairs.any((bindPair) =>
                bindPair.columnIndex == cell.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
              return true;
            }
          }
          List<TableCellElement> combineCells = jsonElement.getBindingExcelCombineCells();
          for (int j = 0; j < combineCells.length; j++) {
            TableCellElement cell = combineCells[j];
            // bool cellDisplayHeader = cell.isTitle ?? false;
            // if (cell.contentTitle != null) {
            //   cellDisplayHeader = true;
            // }
            bool cellDisplayHeader = false;
            DataBindModify? dataBindModify = ExcelTransformManager().getElementModify(cell);
            if (dataBindModify != null && dataBindModify.useTitle == true) {
              cellDisplayHeader = true;
            }
            if (cellDisplayHeader != importWithHeader) {
              return true;
            }
            if (!bindPairs.any((bindPair) =>
                bindPair.columnIndex == cell.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
              return true;
            }
          }
        } else if (jsonElement is BarCodeElement) {
          if (!bindPairs.any((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn &&
              bindPair.bindElementType == ElementItemType.barcode &&
              bindPair.codeType == jsonElement.codeType)) {
            return true;
          }
        } else if (jsonElement is QrCodeElement) {
          if (!bindPairs.any((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn &&
              bindPair.bindElementType == ElementItemType.qrcode &&
              bindPair.codeType == jsonElement.codeType)) {
            return true;
          }
        }
      }
    } else {
      return true;
    }
    return false;
  }

  /// 元素是否关联Excel指定列
  bool _isElementBindingExcelPair(JsonElement element, ExcelBindPair excelBindPair, bool? importWithHeader) {
    String bindElementType = excelBindPair.bindElementType;
    int column = excelBindPair.columnIndex;
    if (element is BarCodeElement) {
      return bindElementType == ElementItemType.barcode &&
          element.bindingColumn == column &&
          element.codeType == excelBindPair.codeType;
    } else if (element is QrCodeElement) {
      return bindElementType == ElementItemType.qrcode &&
          element.bindingColumn == column &&
          element.codeType == excelBindPair.codeType;
    }
    TextElement textElement = element as TextElement;
    return bindElementType == ElementItemType.text &&
        element.bindingColumn == column &&
        textElement.showExcelTitle() == importWithHeader;
  }

  /// 通过表格cell绑定Excel
  _handleTableCellImportExcelResult(TableCellElement importExcelTableCell, ExcelDataModel? excelData) {
    TemplateData? templateData = widget.templateData;
    TableElement? tableElement = importExcelTableCell.tableElement;
    CanvasElement? importExcelTable =
        templateData?.canvasElements.firstWhere((element) => element.elementId == tableElement?.id);
    importExcelTable?.resetImageCache();
    // if (templateData.externalData?.id != excelData?.excelId) {
    if (templateData?.getExcelMd5() != excelData?.excelId) {
      tableElement?.getBindingExcelCells().forEach((cell) {
        // cell.isBinding = 0;
        // cell.bindingColumn = -1;
        // cell.isTitle = false;
        // cell.contentTitle = null;
        cell.dataBind = null;
        cell.value = "";
        // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
        TemplateUtils.delElementModify(cell);
      });
      tableElement?.getBindingExcelCombineCells().forEach((cell) {
        // cell.isBinding = 0;
        // cell.bindingColumn = -1;
        // cell.isTitle = false;
        // cell.contentTitle = null;
        cell.dataBind = null;
        cell.value = "";
        // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
        TemplateUtils.delElementModify(cell);
      });
    }
    List<ExcelBindPair> bindPairs = excelData?.bindPairs.where((element) => element.open == true).toList() ?? [];
    int columnIndex = bindPairs[0].columnIndex;
    // importExcelTableCell.isBinding = 1;
    importExcelTableCell.bindingColumn = columnIndex;
    // importExcelTableCell.dataBind = ExcelTransformManager().getElementDataBind();
    importExcelTableCell.dataBind = excelData?.dataBind;
    importExcelTableCell.value = ExcelTransformManager().getCellPlaceHolder(columnIndex);
    // importExcelTableCell.isTitle = excelData.displayHeader;
    // importExcelTableCell.contentTitle = excelData.displayHeader ? excelData.headers[columnIndex] : null;
    TemplateUtils.changeElementModify(importExcelTableCell, ElementModifyType.modifyUseTitle,
        displayHeader: excelData?.displayHeader ?? false);
    // if (templateData.externalData?.id == excelData?.excelId) {
    if (templateData?.getExcelMd5() == excelData?.excelId) {
      widget.onElementFocusChanged?.call(focusedElements, 0);
    } else {
      _handleImportDifferentExcel(templateData, excelData,
          importExcelElement: importExcelTable, importExcelTableCell: importExcelTableCell);
    }
  }

  /// 画板已关联Excel，重复导入相同的Excel
  _handleImportSameExcel(TemplateData? templateData, ExcelDataModel? excelData, {CanvasElement? importExcelElement}) {
    if (excelData == null) {
      return;
    }
    bool focused = false;
    if (importExcelElement == null || !importExcelElement.data.isBindingExcel()) {
      if (excelData.bindPairs.where((element) => element.open).length == 1) {
        focused = true;
      }
    }
    _updateAllMirrorElements();
    bool displayHeader = excelData.displayHeader ?? false;
    List<ExcelBindPair> bindPairs = excelData.bindPairs.where((element) => element.open == true).toList();
    List<int> alreadyBindColumns = [];
    final canvasElements = templateData?.canvasElements;
    final bindingExcelElements = canvasElements?.where((element) => element.data.isBindingExcel()).toList();
    List<CanvasElement> deleteCanvasElements = [];
    bool refresh = false;
    if (bindingExcelElements != null && bindingExcelElements.isNotEmpty) {
      for (int i = 0; i < bindingExcelElements.length; i++) {
        CanvasElement element = bindingExcelElements[i];
        if (element == importExcelElement) {
          element.resetImageCache();
          alreadyBindColumns.add(element.data.bindingColumn);
          refresh = true;
          continue;
        }
        JsonElement jsonElement = element.data;
        if (jsonElement is TextElement) {
          // bool elementDisplayHeader = jsonElement.isTitle ?? false;
          // if (jsonElement.contentTitle != null) {
          //   elementDisplayHeader = true;
          // }
          bool elementDisplayHeader = TemplateUtils.hasTitle(jsonElement);
          if (!bindPairs.any((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
            element.resetImageCache();
            deleteCanvasElements.add(element);
            refresh = true;
          } else {
            int i = (displayHeader ? 1 : 0) + (elementDisplayHeader ? 1 : 0);
            if (i == 1) {
              element.resetImageCache();
              // jsonElement.isTitle = displayHeader;
              // jsonElement.contentTitle = displayHeader ? excelData.headers[jsonElement.bindingColumn] : null;
              // jsonElement.value = "";
              TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle,
                  displayHeader: displayHeader);
              refresh = true;
            }
            if (!alreadyBindColumns.any((column) => column == jsonElement.bindingColumn)) {
              alreadyBindColumns.add(jsonElement.bindingColumn);
            }
          }
        } else if (jsonElement is TableElement) {
          bool resetTable = false;
          jsonElement.getBindingExcelCells().forEach((cell) {
            // bool cellDisplayHeader = cell.isTitle ?? false;
            // if (cell.contentTitle != null) {
            //   cellDisplayHeader = true;
            // }
            bool cellDisplayHeader = TemplateUtils.hasTitle(cell);
            if (!bindPairs.any((bindPair) =>
                bindPair.columnIndex == cell.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
              resetTable = true;
              // cell.isBinding = 0;
              // cell.bindingColumn = -1;
              // cell.isTitle = false;
              // cell.contentTitle = "";
              cell.dataBind = null;
              cell.value = "";
              // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
              TemplateUtils.delElementModify(cell);
            } else {
              int i = (displayHeader ? 1 : 0) + (cellDisplayHeader ? 1 : 0);
              if (i == 1) {
                resetTable = true;
                // cell.isTitle = displayHeader;
                // cell.contentTitle = displayHeader ? excelData.headers[cell.bindingColumn] : null;
                // cell.value = "";
                TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: displayHeader);
              }
              if (!alreadyBindColumns.any((column) => column == cell.bindingColumn)) {
                alreadyBindColumns.add(cell.bindingColumn);
              }
            }
          });
          jsonElement.getBindingExcelCombineCells().forEach((cell) {
            // bool cellDisplayHeader = cell.isTitle ?? false;
            // if (cell.contentTitle != null) {
            //   cellDisplayHeader = true;
            // }
            bool cellDisplayHeader = TemplateUtils.hasTitle(cell);
            if (!bindPairs.any((bindPair) =>
                bindPair.columnIndex == cell.bindingColumn && bindPair.bindElementType == ElementItemType.text)) {
              resetTable = true;
              // cell.isBinding = 0;
              // cell.bindingColumn = -1;
              // cell.isTitle = false;
              // cell.contentTitle = "";
              cell.dataBind = null;
              cell.value = "";
              // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
              TemplateUtils.delElementModify(cell);
            } else {
              int i = (displayHeader ? 1 : 0) + (cellDisplayHeader ? 1 : 0);
              if (i == 1) {
                resetTable = true;
                // cell.isTitle = displayHeader;
                // cell.contentTitle = displayHeader ? excelData.headers[cell.bindingColumn] : null;
                cell.value = "";
                TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: displayHeader);
              }
              if (!alreadyBindColumns.any((column) => column == cell.bindingColumn)) {
                alreadyBindColumns.add(cell.bindingColumn);
              }
            }
          });
          if (resetTable) {
            element.resetImageCache();
            refresh = true;
          }
        } else if (jsonElement is BarCodeElement) {
          final bindPair = bindPairs.firstWhereOrNull((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn && bindPair.bindElementType == ElementItemType.barcode);
          if (bindPair == null) {
            element.resetImageCache();
            deleteCanvasElements.add(element);
            refresh = true;
          } else {
            if (jsonElement.codeType != bindPair.codeType) {
              element.resetImageCache();
              jsonElement.codeType = bindPair.codeType;
              refresh = true;
            }
            if (!alreadyBindColumns.any((column) => column == jsonElement.bindingColumn)) {
              alreadyBindColumns.add(jsonElement.bindingColumn);
            }
          }
        } else if (jsonElement is QrCodeElement) {
          final bindPair = bindPairs.firstWhereOrNull((bindPair) =>
              bindPair.columnIndex == jsonElement.bindingColumn && bindPair.bindElementType == ElementItemType.qrcode);
          if (bindPair == null) {
            element.resetImageCache();
            deleteCanvasElements.add(element);
            refresh = true;
          } else {
            if (jsonElement.codeType != bindPair.codeType) {
              element.resetImageCache();
              jsonElement.codeType = bindPair.codeType;
              refresh = true;
            }
            if (!alreadyBindColumns.any((column) => column == jsonElement.bindingColumn)) {
              alreadyBindColumns.add(jsonElement.bindingColumn);
            }
          }
        } else {
          deleteCanvasElements.add(element);
          refresh = true;
        }
      }
    }
    if (importExcelElement != null &&
        !importExcelElement.data.isBindingExcel() &&
        !deleteCanvasElements.contains(importExcelElement)) {
      deleteCanvasElements.add(importExcelElement);
      refresh = true;
    }
    if (deleteCanvasElements.length > 0) {
      focusedElements.removeWhere((element) => deleteCanvasElements.contains(element));
      canvasElements?.removeWhere((element) => deleteCanvasElements.contains(element));

      deleteCanvasElements.forEach((canvasElement) {
        /// 删除镜像
        if ((canvasElement.data.mirrorId).length > 0) {
          canvasElements?.removeWhere((element) => element.data.id == canvasElement.data.mirrorId);

          canvasElement.data.isOpenMirror = 0;
          canvasElement.data.mirrorId = '';
        }
      });
    }

    List<ExcelBindPair> newAddBindPairs = [];
    bindPairs.forEach((bindPair) {
      if (alreadyBindColumns.any((column) => column == bindPair.columnIndex)) {
        return;
      }
      newAddBindPairs.add(bindPair);
    });
    _injectExcelData(templateData, excelData, newAddBindPairs, focused: focused);

    if (refresh) {
      widget.onElementFocusChanged?.call(focusedElements, 0);
      FloatingBarVisibleNotifier().forceUpdate();
    }
  }

  /// 画板没有关联Excel，或画板已关联Excel又重复导入相关的Excel
  _handleImportDifferentExcel(TemplateData? templateData, ExcelDataModel? excelData,
      {CanvasElement? importExcelElement, TableCellElement? importExcelTableCell, bool focused = false}) {
    _updateAllMirrorElements();

    final canvasElements = templateData?.canvasElements;

    /// 清空旧的修改记录
    // templateData.clearModifyRecord();
    int elementBindingColumn = -1;
    bool refresh = false;

    /// 清空旧的绑定关系
    List<CanvasElement> deleteCanvasElements = [];
    final bindingExcelElements = templateData?.getAllBindingExcelCanvasElements(false);
    if ((bindingExcelElements?.isNotEmpty ?? false)) {
      // List<CanvasElement> updateCanvasElements = [];
      for (int i = 0; i < bindingExcelElements!.length; i++) {
        CanvasElement element = bindingExcelElements[i];
        JsonElement jsonElement = element.data;
        element.resetImageCache();
        if (element == importExcelElement) {
          if (jsonElement is TableElement) {
            elementBindingColumn = importExcelTableCell?.bindingColumn ?? -1;
          } else {
            elementBindingColumn = jsonElement.bindingColumn;
          }
          refresh = true;
          continue;
        }
        if (jsonElement is TableElement) {
          jsonElement.cells.forEach((cell) {
            // cell.isBinding = 0;
            // cell.bindingColumn = -1;
            // cell.isTitle = false;
            // cell.contentTitle = null;
            cell.dataBind = null;
            cell.value = "";
            // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
            TemplateUtils.delElementModify(cell);
            refresh = true;
          });
          jsonElement.combineCells.forEach((cell) {
            // cell.isBinding = 0;
            // cell.bindingColumn = -1;
            // cell.isTitle = false;
            // cell.contentTitle = null;
            cell.dataBind = null;
            cell.value = "";
            // TemplateUtils.changeElementModify(cell, ElementModifyType.modifyUseTitle, displayHeader: false);
            TemplateUtils.delElementModify(cell);
            refresh = true;
          });
        } else {
          deleteCanvasElements.add(element);
        }
      }
      // if (updateCanvasElements.length > 0) {
      //   /// 撤销、恢复
      //   StackManager().updateElements(clearedCanvasElements, context);
      //   StackManager().snapCanvasElements(canvasElements);
      // }
    }
    if (importExcelElement != null &&
        !importExcelElement.data.isBindingExcel() &&
        !deleteCanvasElements.contains(importExcelElement)) {
      deleteCanvasElements.add(importExcelElement);
      refresh = true;
    }
    if (deleteCanvasElements.length > 0) {
      focusedElements.removeWhere((element) => deleteCanvasElements.contains(element));
      canvasElements?.removeWhere((element) => deleteCanvasElements.contains(element));

      deleteCanvasElements.forEach((canvasElement) {
        /// 删除镜像
        if ((canvasElement.data.mirrorId).length > 0) {
          canvasElements?.removeWhere((element) => element.data.id == canvasElement.data.mirrorId);

          canvasElement.data.isOpenMirror = 0;
          canvasElement.data.mirrorId = '';
        }

        // /// 撤销、恢复
        // StackManager().removeElements(deleteCanvasElements, context);
        // StackManager().snapCanvasElements(canvasElements);
      });
      refresh = true;
    }

    List<ExcelBindPair> bindPairs = excelData?.bindPairs.where((element) => element.open == true).toList() ?? [];
    if (elementBindingColumn >= 0) {
      bindPairs.retainWhere((element) => element.columnIndex != elementBindingColumn);
    }
    _injectExcelData(templateData, excelData, bindPairs, focused: focused);

    if (refresh) {
      widget.onElementFocusChanged?.call(focusedElements, 0);
      FloatingBarVisibleNotifier().forceUpdate();
    }
  }

  _updateAllMirrorElements() {
    TemplateData? templateData = widget.templateData;
    final bindingExcelJsonElements = templateData?.getAllBindingExcelElements(false);
    bindingExcelJsonElements?.forEach((jsonElement) {
      if (jsonElement is TextElement) {
        final mirrorCanvasText = _getMirrorJsonElement(jsonElement);
        if (mirrorCanvasText != null) {
          TextElement mirrorText = mirrorCanvasText.data as TextElement;
          // mirrorText.isBinding = 1;
          mirrorText.bindingColumn = jsonElement.bindingColumn;
          mirrorText.dataBind = jsonElement.dataBind;
          mirrorText.value = jsonElement.value;

          // mirrorText.isTitle = jsonElement.isTitle;
          // mirrorText.contentTitle = jsonElement.contentTitle;
          bool hasTitle = TemplateUtils.hasTitle(jsonElement);
          TemplateUtils.changeElementModify(mirrorText, ElementModifyType.modifyUseTitle, displayHeader: hasTitle);
          mirrorCanvasText.resetImageCache();
        }
      } else if (jsonElement is BarCodeElement) {
        final mirrorCanvasBarcode = _getMirrorJsonElement(jsonElement);
        if (mirrorCanvasBarcode != null) {
          BarCodeElement mirrorBarcode = mirrorCanvasBarcode.data as BarCodeElement;
          // mirrorBarcode.isBinding = 1;
          mirrorBarcode.bindingColumn = jsonElement.bindingColumn;
          mirrorBarcode.dataBind = jsonElement.dataBind;
          mirrorBarcode.value = jsonElement.value;
          mirrorBarcode.codeType = jsonElement.codeType;
          mirrorBarcode.switchBarCodeType(jsonElement.codeType);
          mirrorCanvasBarcode.resetImageCache();
        }
      } else if (jsonElement is QrCodeElement) {
        final mirrorCanvasQrCode = _getMirrorJsonElement(jsonElement);
        if (mirrorCanvasQrCode != null) {
          QrCodeElement mirrorQrCode = mirrorCanvasQrCode.data as QrCodeElement;
          // mirrorQrCode.isBinding = 1;
          mirrorQrCode.bindingColumn = jsonElement.bindingColumn;
          mirrorQrCode.dataBind = jsonElement.dataBind;
          mirrorQrCode.value = jsonElement.value;
          mirrorQrCode.codeType = jsonElement.codeType;
          mirrorCanvasQrCode.resetImageCache();
        }
      }
    });
  }

  CanvasElement? _getMirrorJsonElement(JsonElement jsonElement) {
    TemplateData? templateData = widget.templateData;
    String mirrorId = jsonElement.mirrorId;
    if (mirrorId.isNotEmpty) {
      return templateData?.canvasElements.firstWhereOrNull((element) => element.elementId == mirrorId);
    }
    return null;
  }

  /// Excel内容注入模板，并创建新关联的元素
  _injectExcelData(TemplateData? templateData, ExcelDataModel? excelData, List<ExcelBindPair> newAddBindPairs,
      {bool focused = false}) {
    bool? result = templateData?.injectExcelData(excelData);
    DataSource? dataSource = ExcelTransformManager().getDataSource();
    List<String>? dataBind = null;
    if (dataSource != null) {
      if (dataSource.type == DataSourceType.excel) {
        String sheetName = TemplateUtils.getSheetName(dataSource);
        dataBind = [dataSource.hash, sheetName];
      } else if (dataSource.type == DataSourceType.commodity) {
        dataBind = ["", "commodity"];
      } else {}
    }

    if (result != null && !result) {
      _logger.log('inject excel data exception');
    }

    if (newAddBindPairs.isEmpty) {
      return;
    }

    /// 创建元素
    if (excelData?.displayHeader == true) {
      double x = 2.0;
      double y = 2.0;
      double elementWidth;
      double elementHeight;
      newAddBindPairs.forEach((element) {
        String valuePlaceHolder = ExcelTransformManager().getCellPlaceHolder(element.columnIndex);
        elementWidth = (templateData?.width ?? 0) - 4.0;
        if (element.bindElementType == ElementItemType.text) {
          if (elementWidth < 10.0) {
            elementWidth = 10.0;
          }
          elementHeight = 8.0;
          TextElement textElement = TextElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            fontSize: 3.2,
            rotate: 0,
            fontStyle: [],
            textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
            letterSpacing: 0,
            lineSpacing: 0,
            value: valuePlaceHolder,
            dataBind: dataBind,
            isTitle: excelData?.displayHeader == true,
            contentTitle: excelData?.displayHeader == true ? excelData?.headers[element.columnIndex] : null,
            wordSpacing: 0,
            lineMode: 2,
            lineBreakMode: 1,
            textAlignVertical: 0, // 6.0.4版本更换为顶对齐
            zIndex: 0,
            textDirection: CanvasHelper.currentTextDirection
          );
          TemplateUtils.changeElementModify(textElement, ElementModifyType.modifyUseTitle,
              displayHeader: excelData?.displayHeader ?? false, isGlobalEffective: true);
          addCanvasElement(textElement.toCanvasElement(), focused: focused, stack: false, fromBusiness: true);
          y += 4.0;
        } else if (element.bindElementType == ElementItemType.barcode) {
          // if (elementWidth > 20.0) {
          elementWidth = 20.0;
          // }
          elementHeight = 0.5 * elementWidth;
          addCanvasElement(
              BarCodeElement(
                      id: JsonElement.generateId(),
                      isBinding: 1,
                      bindingColumn: element.columnIndex,
                      width: elementWidth,
                      height: elementHeight,
                      x: x,
                      y: y,
                      rotate: 0,
                      fontSize: 3.2,
                      // value: "123456",
                      value: valuePlaceHolder,
                      dataBind: dataBind,
                      textPosition: 0,
                      textHeight: 3.4,
                      codeType: element.codeType)
                  .toCanvasElement(),
              focused: focused,
              fromBusiness: true,
              stack: false);
          y = y + elementHeight + 2.0;
        } else if (element.bindElementType == ElementItemType.qrcode) {
          // if (elementWidth > 10.0) {
          elementWidth = 10.0;
          // }
          if (element.codeType == QrcodeType.PDF417) {
            elementHeight = 2.0;
          } else {
            elementHeight = elementWidth;
          }
          addCanvasElement(
              QrCodeElement(
                      id: JsonElement.generateId(),
                      isBinding: 1,
                      bindingColumn: element.columnIndex,
                      // value: "123456",
                      value: valuePlaceHolder,
                      dataBind: dataBind,
                      width: elementWidth,
                      height: elementHeight,
                      x: x,
                      y: y,
                      rotate: 0,
                      correctLevel: 0,
                      codeType: element.codeType)
                  .toCanvasElement(),
              focused: focused,
              fromBusiness: true,
              stack: false);
          y = y + elementHeight + 2.0;
        }
      });
    } else {
      int index = 0;
      double x;
      double y = 2.0;
      double elementWidth;
      double elementHeight;
      double rowHeight = 0.0;
      newAddBindPairs.forEach((element) {
        String valuePlaceHolder = ExcelTransformManager().getCellPlaceHolder(element.columnIndex);
        elementWidth = 0.5 * (templateData?.width ?? 0) - 4.0;
        if (index % 2 == 0) {
          x = 2.0;
        } else {
          x = 0.5 * (templateData?.width ?? 0);
        }
        if (index % 2 == 0) {
          y += rowHeight;
          rowHeight = 0;
        }
        if (element.bindElementType == ElementItemType.text) {
          elementHeight = 8.0;
          TextElement textElement = TextElement(
            id: JsonElement.generateId(),
            isBinding: 1,
            bindingColumn: element.columnIndex,
            width: elementWidth,
            height: elementHeight,
            x: x,
            y: y,
            fontSize: 3.2,
            rotate: 0,
            fontStyle: [],
            textAlignHorizontal: Directionality.of(context) == TextDirection.rtl ? 2 : 0,
            letterSpacing: 0,
            lineSpacing: 0,
            value: valuePlaceHolder,
            dataBind: dataBind,
            contentTitle: excelData?.displayHeader == true ? excelData?.headers[element.columnIndex] : null,
            wordSpacing: 0,
            lineMode: 2,
            lineBreakMode: 1,
            textAlignVertical: 0, // 6.0.4版本更换为顶对齐
            zIndex: 0,
            textDirection: CanvasHelper.currentTextDirection
          );
          TemplateUtils.changeElementModify(textElement, ElementModifyType.modifyUseTitle,
              displayHeader: excelData?.displayHeader ?? false, isGlobalEffective: true);
          addCanvasElement(textElement.toCanvasElement(), focused: focused, stack: false, fromBusiness: true);
          if (rowHeight < elementHeight) {
            rowHeight = elementHeight;
          }
        } else if (element.bindElementType == ElementItemType.barcode) {
          // if (elementWidth > 20.0) {
          elementWidth = 20.0;
          // }
          elementHeight = 0.5 * elementWidth;
          addCanvasElement(
              BarCodeElement(
                      id: JsonElement.generateId(),
                      isBinding: 1,
                      bindingColumn: element.columnIndex,
                      width: elementWidth,
                      height: elementHeight,
                      x: x,
                      y: y,
                      rotate: 0,
                      fontSize: 3.2,
                      value: valuePlaceHolder,
                      dataBind: dataBind,
                      textPosition: 0,
                      textHeight: 3.4,
                      codeType: element.codeType)
                  .toCanvasElement(),
              focused: focused,
              fromBusiness: true,
              stack: false);
          if (rowHeight < elementHeight + 2.0) {
            rowHeight = elementHeight + 2.0;
          }
        } else if (element.bindElementType == ElementItemType.qrcode) {
          // if (elementWidth > 10.0) {
          elementWidth = 10.0;
          // }
          elementHeight = elementWidth;
          addCanvasElement(
              QrCodeElement(
                      id: JsonElement.generateId(),
                      isBinding: 1,
                      bindingColumn: element.columnIndex,
                      value: valuePlaceHolder,
                      dataBind: dataBind,
                      width: elementWidth,
                      height: elementWidth,
                      x: x,
                      y: y,
                      rotate: 0,
                      correctLevel: 0,
                      codeType: element.codeType)
                  .toCanvasElement(),
              focused: focused,
              fromBusiness: true,
              stack: false);
          if (rowHeight < elementHeight + 2.0) {
            rowHeight = elementHeight + 2.0;
          }
        }
        index++;
      });
    }
  }

  /// Excel选择行
  handleSelectRow(List<Range> ranges) {
    List<Range> originRanges = ExcelTransformManager.sharedInstance().getSelectRanges();
    if (ExcelTransformManager.sharedInstance().isSameRange(ranges, originRanges)) {
      return;
    }
    RfidRepository rfidRepository = RfidRepository();
    TemplateData? templateData = widget.templateData;
    int pageIndex = templateData?.currentPageIndex ?? 0;
    List<CanvasElement>? bindingExcelElements = templateData?.getAllBindingExcelCanvasElements(true);
    List<JsonElement> elementsOfBefore = [];
    bindingExcelElements?.forEach((element) {
      elementsOfBefore.add(element.data.clone(keepId: true));
    });
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    excelBusinessSnap.excelFileChanged = true;
    excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
    excelBusinessSnap.pageIndexBefore = pageIndex;
    excelBusinessSnap.elementsOfBefore = elementsOfBefore;
    excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    ExcelTransformManager.sharedInstance().changeSelectRows(ranges);
    int totalPage = ExcelTransformManager.sharedInstance().getSelectRowCount(minCount: 1);
    templateData?.currentPageIndex = 0;
    templateData?.totalPage = totalPage;
    ExcelPageInfo pageInfo = ExcelPageInfo(page: 1, total: totalPage);
    templateData?.bindInfo = pageInfo;
    templateData?.resetImageCache();
    setState(() {});
    widget.onAttrPanelChange.call();
    List<JsonElement> elementsOfAfter = [];
    bindingExcelElements?.forEach((element) {
      elementsOfAfter.add(element.data.clone(keepId: true));
    });
    excelBusinessSnap.dataSourceAfter = templateData?.cloneDataSource();
    excelBusinessSnap.pageIndexAfter = templateData?.currentPageIndex;
    excelBusinessSnap.elementsOfAfter = elementsOfAfter;
    excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
    StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
  }

  ///模板断开数据源关联
  clearImportDataSource() {
    TemplateData? templateData = widget.templateData;
    if ((templateData?.isExcelBindingTemplate() ?? false) || (templateData?.isGoodsLabelTemplate() ?? false)) {
      RfidRepository rfidRepository = RfidRepository();
      int pageIndex = templateData?.currentPageIndex ?? 0;
      List<CanvasElement> bindingExcelElements = templateData?.getAllBindingExcelCanvasElements(true) ?? [];
      List<JsonElement> elementsOfBefore = [];
      bindingExcelElements.forEach((element) {
        elementsOfBefore.add(element.data.clone(keepId: true));
      });
      ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
      excelBusinessSnap.excelFileChanged = true;
      excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexBefore = pageIndex;
      excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData?.modify);
      excelBusinessSnap.elementsOfBefore = elementsOfBefore;
      excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
      for (int i = 0; i < bindingExcelElements.length; i++) {
        CanvasElement element = bindingExcelElements[i];
        JsonElement jsonElement = element.data;
        element.resetImageCache();
        if (jsonElement is TableElement) {
          TableElement tableElement = jsonElement;
          tableElement.getBindingExcelCells().forEach((cell) {
            String escapeValue = ExcelTransformManager().getBindingValue(cell, pageIndex, true, true);
            cell.bindingColumn = -1;
            cell.dataBind = null;
            cell.value = escapeValue;
          });
          tableElement.getBindingExcelCombineCells().forEach((cell) {
            String escapeValue = ExcelTransformManager().getBindingValue(cell, pageIndex, true, true);
            cell.bindingColumn = -1;
            cell.dataBind = null;
            cell.value = escapeValue;
          });
        } else {
          if (jsonElement.dataBind == null) {
            continue;
          }
          String escapeValue =
              ExcelTransformManager().getBindingValue(jsonElement, pageIndex, jsonElement is TextElement, true);
          if (jsonElement is BarCodeElement &&
              jsonElement.codeType == BarcodeType.CODE_BAR &&
              (escapeValue.length ?? 0) > 57) {
            escapeValue = escapeValue.substring(0, 57);
          }
          jsonElement.bindingColumn = -1;
          jsonElement.dataBind = null;
          jsonElement.value = escapeValue;
          String mirrorId = jsonElement.mirrorId;
          if (mirrorId.isNotEmpty) {
            JsonElement elementMirror = bindingExcelElements.firstWhere((element) => element.data.id == mirrorId).data;
            elementMirror.bindingColumn = -1;
            elementMirror.dataBind = null;
            elementMirror.value = escapeValue;
          }
        }
      }
      _injectExcelData(templateData, null, []);
      widget.onElementFocusChanged?.call(focusedElements, 0);
      List<JsonElement> elementsOfAfter = [];
      bindingExcelElements.forEach((element) {
        elementsOfAfter.add(element.data.clone(keepId: true));
      });
      excelBusinessSnap.dataSourceAfter = templateData?.cloneDataSource();
      excelBusinessSnap.pageIndexAfter = templateData?.currentPageIndex;
      excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(templateData?.modify);
      excelBusinessSnap.elementsOfAfter = elementsOfAfter;
      excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
      StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
    } else if ((templateData?.isGoodsLabelTemplate() ?? false) &&
        (ExcelTransformManager().rowData?.isNotEmpty ?? false)) {}
  }

  ///元素断开数据源关联
  changeDataManualInputField(CanvasElement canvasElement, CanvasElement? tableCell) {
    TemplateData? templateData = widget.templateData;
    int pageIndex = templateData?.currentPageIndex ?? 0;
    JsonElement jsonElement = canvasElement.data;
    JsonElement? elementMirror;
    String mirrorId = jsonElement.mirrorId;
    RfidRepository rfidRepository = RfidRepository();
    if (tableCell == null && mirrorId.isNotEmpty) {
      elementMirror = templateData?.canvasElements.firstWhere((element) => element.data.id == mirrorId).data;
    }
    List<JsonElement> elementsOfBefore = [];
    elementsOfBefore.add(jsonElement.clone(keepId: true));
    if (elementMirror != null) {
      elementsOfBefore.add(elementMirror.clone(keepId: true));
    }
    ExcelBusinessSnap excelBusinessSnap = ExcelBusinessSnap();
    excelBusinessSnap.excelFileChanged = true;
    excelBusinessSnap.dataSourceBefore = templateData?.cloneDataSource();
    excelBusinessSnap.pageIndexBefore = pageIndex;
    excelBusinessSnap.modifyBefore = TemplateUtils.cloneModify(templateData?.modify);
    excelBusinessSnap.elementsOfBefore = elementsOfBefore;
    excelBusinessSnap.rfidBindingColumnBefore = rfidRepository.getRfidBindingColumn();
    canvasElement.resetImageCache();
    if (tableCell != null) {
      TableCellElement tableCellElement = tableCell.data as TableCellElement;
      String escapeValue = ExcelTransformManager().getBindingValue(tableCellElement, pageIndex, true, true);
      tableCellElement.bindingColumn = -1;
      tableCellElement.dataBind = null;
      tableCellElement.value = escapeValue;
      (canvasElement.data as TableElement).getBindingExcelCells().forEach((cell) {
        if (cell.combineId == tableCellElement.id) {
          String escapeValue = ExcelTransformManager().getBindingValue(cell, pageIndex, true, true);
          cell.bindingColumn = -1;
          cell.dataBind = null;
          cell.value = escapeValue;
        }
      });
    } else {
      String escapeValue =
          ExcelTransformManager().getBindingValue(jsonElement, pageIndex, jsonElement is TextElement, true);
      if (jsonElement is BarCodeElement &&
          jsonElement.codeType == BarcodeType.CODE_BAR &&
          (escapeValue.length ?? 0) > 57) {
        escapeValue = escapeValue.substring(0, 57);
      }
      jsonElement.bindingColumn = -1;
      jsonElement.dataBind = null;
      jsonElement.value = escapeValue;
      if (elementMirror != null) {
        elementMirror.bindingColumn = -1;
        elementMirror.dataBind = null;
        elementMirror.value = escapeValue;
      }
    }
    List<CanvasElement>? bindingExcelElements = templateData?.getAllBindingExcelCanvasElements(true);
    if (bindingExcelElements?.isEmpty ?? true) {
      _injectExcelData(templateData, null, []);
    }
    widget.onElementFocusChanged?.call(focusedElements, 0);
    List<JsonElement> elementsOfAfter = [];
    elementsOfAfter.add(jsonElement.clone(keepId: true));
    if (elementMirror != null) {
      elementsOfAfter.add(elementMirror.clone(keepId: true));
    }
    excelBusinessSnap.dataSourceAfter = templateData?.cloneDataSource();
    excelBusinessSnap.pageIndexAfter = templateData?.currentPageIndex;
    excelBusinessSnap.modifyAfter = TemplateUtils.cloneModify(templateData?.modify);
    excelBusinessSnap.elementsOfAfter = elementsOfAfter;
    excelBusinessSnap.rfidBindingColumnAfter = rfidRepository.getRfidBindingColumn();
    StackManager().canvasBindingExcelChanged(excelBusinessSnap, context);
  }

  ///从元素变更列
  changeColumnFromElement(JsonElement jsonElement, ExcelBindPair columnBindInfo, bool importWithHeader) {
    TemplateData? templateData = widget.templateData;
    int index = columnBindInfo.columnIndex;
    if (jsonElement is TableCellElement) {
      int flag = (TemplateUtils.hasTitle(jsonElement) ? 1 : 0) + (importWithHeader ? 1 : 0);
      if (jsonElement.bindingColumn == index && flag != 1) {
        return;
      }
      TableCellElement tableCellElement = jsonElement;
      TableElement? tableElement = tableCellElement.tableElement;
      SwitchExcelColumnSnap switchExcelColumnSnap = SwitchExcelColumnSnap();
      switchExcelColumnSnap.elementsOfBefore = [tableElement!.clone(keepId: true)];
      switchExcelColumnSnap.modifyBefore = templateData?.cloneTemplateModify();

      /// 绑定当前元素
      jsonElement.bindingColumn = index;
      jsonElement.dataBind = ExcelTransformManager().getElementDataBind();
      jsonElement.value = ExcelTransformManager().getCellPlaceHolder(index);
      switchExcelColumnSnap.elementsOfAfter = [tableElement.clone(keepId: true)];

      /// 移除当前元素对应的修改记录
      switchExcelColumnSnap.taskChanged = templateData?.removeModifyRecord([jsonElement]);
      TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle, displayHeader: importWithHeader);
      if (switchExcelColumnSnap.taskChanged ?? false) {
        switchExcelColumnSnap.modifyAfter = templateData?.cloneTemplateModify();
      } else {
        switchExcelColumnSnap.modifyBefore = null;
      }
      StackManager().switchColumn(switchExcelColumnSnap, context);
      CanvasElement? anchorCanvasElement = templateData
          ?.getAllBindingExcelCanvasElements(true)
          ?.firstWhereOrNull((element) => element.elementId == tableElement.id);
      anchorCanvasElement?.resetImageCache();
      Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
    } else {
      CanvasElement? anchorCanvasElement = templateData
          ?.getAllBindingExcelCanvasElements(true)
          ?.firstWhereOrNull((element) => element.elementId == jsonElement.id);
      // int flag = (TemplateUtils.hasTitle(jsonElement) ? 1 : 0) + (importWithHeader ? 1 : 0);
      // if (jsonElement.bindingColumn == index && jsonElement.type == columnBindInfo.bindElementType && flag != 1) {
      //   return;
      // }
      CanvasElement? mirrorCanvasElement;
      String mirrorId = jsonElement.mirrorId ?? "";
      if (mirrorId.isNotEmpty) {
        mirrorCanvasElement = templateData
            ?.getAllBindingExcelCanvasElements(true)
            ?.firstWhereOrNull((element) => element.elementId == mirrorId);
      }
      JsonElement? mirrorTextElement = mirrorCanvasElement?.data;
      SwitchExcelColumnSnap switchExcelColumnSnap = SwitchExcelColumnSnap();
      if (mirrorCanvasElement == null) {
        switchExcelColumnSnap.elementsOfBefore = [jsonElement.clone(keepId: true)];
      } else {
        switchExcelColumnSnap.elementsOfBefore = [
          jsonElement.clone(keepId: true),
          mirrorCanvasElement.data.clone(keepId: true)
        ];
      }
      switchExcelColumnSnap.modifyBefore = templateData?.cloneTemplateModify();

      //区分绑定列类型是否一致，一致则替换内容，不一致删除后重新添加
      if (jsonElement.type == columnBindInfo.bindElementType) {
        //如果列不一致，则清楚改元素修改记录
        if (jsonElement.getBindingColumn() != index) {
          templateData?.removeModifyRecord([jsonElement]);
        }

        /// 绑定当前元素
        jsonElement.bindingColumn = index;
        mirrorTextElement?.bindingColumn = index;
        jsonElement.value = ExcelTransformManager().getCellPlaceHolder(index);
        jsonElement.dataBind = ExcelTransformManager().getElementDataBind();
        mirrorTextElement?.value = ExcelTransformManager().getCellPlaceHolder(index);
        mirrorTextElement?.dataBind = ExcelTransformManager().getElementDataBind();
        if (jsonElement.type == ElementItemType.barcode) {
          (jsonElement as BarCodeElement).codeType = columnBindInfo.codeType;
          (jsonElement as BarCodeElement).switchBarCodeType(columnBindInfo.codeType);
          if (mirrorTextElement != null) {
            (mirrorTextElement as BarCodeElement).codeType = columnBindInfo.codeType;
            (mirrorTextElement as BarCodeElement).switchBarCodeType(columnBindInfo.codeType);
          }
        } else if (jsonElement.type == ElementItemType.qrcode) {
          (jsonElement as QrCodeElement).codeType = columnBindInfo.codeType;
          if (mirrorTextElement != null) {
            (mirrorTextElement as QrCodeElement).codeType = columnBindInfo.codeType;
          }
        } else if (jsonElement.type == ElementItemType.text) {
          TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle,
              displayHeader: importWithHeader);
        }

        if (mirrorCanvasElement == null) {
          switchExcelColumnSnap.elementsOfAfter = [jsonElement.clone(keepId: true)];
        } else {
          switchExcelColumnSnap.elementsOfAfter = [
            jsonElement.clone(keepId: true),
            mirrorCanvasElement.data.clone(keepId: true)
          ];
        }
        // TemplateUtils.changeElementModify(jsonElement, ElementModifyType.modifyUseTitle,
        //     displayHeader: importWithHeader);
        // TemplateUtils.changeElementModify(mirrorTextElement, ElementModifyType.modifyUseTitle,
        //     displayHeader: importWithHeader);
        switchExcelColumnSnap.taskChanged = true;
      } else {
        /// 移除当前元素对应的修改记录
        switchExcelColumnSnap.taskChanged = templateData?.removeModifyRecord([jsonElement]);

        ///删除当前元素，重新生成元素
        _delCanvasElements(templateData, [anchorCanvasElement!]);
        List<JsonElement> addElements = _addElementToCanvas(
            templateData, jsonElement.x.toDouble(), jsonElement.y.toDouble(), [columnBindInfo], importWithHeader);
        List<JsonElement> elementsOfAfter = [];
        addElements.forEach((element) {
          elementsOfAfter.add(element.clone(keepId: true));
        });
        switchExcelColumnSnap.elementsOfAfter = elementsOfAfter;
      }
      if (switchExcelColumnSnap.taskChanged ?? false) {
        switchExcelColumnSnap.modifyAfter = templateData?.cloneTemplateModify();
      } else {
        switchExcelColumnSnap.modifyBefore = null;
      }
      StackManager().switchColumn(switchExcelColumnSnap, context);
      anchorCanvasElement?.resetImageCache();
      mirrorCanvasElement?.resetImageCache();
      // Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements([]);
      widget.onElementFocusChanged?.call(focusedElements, 0);
    }
  }
}

class ExcelBusinessSnap {
  List<JsonElement>? elementsOfBefore;
  List<JsonElement>? elementsOfAfter;
  bool? showTitleChanged;
  bool? showTitleBefore;
  bool? showTitleAfter;
  bool? excelFileChanged;

  // ExternalData externalDataBefore;
  // ExternalData externalDataAfter;
  DataSource? dataSourceBefore;
  DataSource? dataSourceAfter;
  int? pageIndexBefore;
  int? pageIndexAfter;
  TemplateModify? modifyBefore;
  TemplateModify? modifyAfter;
  int? rfidBindingColumnBefore;
  int? rfidBindingColumnAfter;

  ExcelBusinessSnap(
      {this.elementsOfBefore,
      this.elementsOfAfter,
      this.showTitleChanged,
      this.showTitleBefore,
      this.showTitleAfter,
      this.excelFileChanged,
      // this.externalDataBefore,
      // this.externalDataAfter,
      this.modifyBefore,
      this.modifyAfter,
      this.dataSourceBefore,
      this.dataSourceAfter,
      this.pageIndexBefore,
      this.pageIndexAfter,
      this.rfidBindingColumnBefore,
      this.rfidBindingColumnAfter});
}

class SwitchExcelColumnSnap {
  List<JsonElement>? elementsOfBefore;
  List<JsonElement>? elementsOfAfter;
  bool? showTitleChanged;
  bool? showTitleBefore;
  bool? showTitleAfter;
  bool? taskChanged;

  // TemplateTask taskBefore;
  // TemplateTask taskAfter;
  TemplateModify? modifyBefore;
  TemplateModify? modifyAfter;

  SwitchExcelColumnSnap({
    this.elementsOfBefore,
    this.elementsOfAfter,
    this.showTitleChanged,
    this.showTitleBefore,
    this.showTitleAfter,
    this.taskChanged,
    // this.taskBefore,
    // this.taskAfter,
    this.modifyBefore,
    this.modifyAfter,
  });
}
