package melon.south.com.mainlibrary.v

import JCApiManager
import RootCheckUtils
import ShopLoadInitializeUtil
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.graphics.drawable.StateListDrawable
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.DeviceUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.android.jccloudprinter.push.PushBindUtil
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connect.JCConnectionManager
import com.gengcon.print.draw.module.event.CoverOpenEvent
import com.gengcon.print.draw.module.event.SHOP_SOURCE_MAIN_DIALOG
import com.gengcon.print.draw.print.PrintManager
import com.gengcon.print.draw.proxy.ConnectionProxyImpl
import com.gengcon.print.draw.proxy.ConnectionProxyManager
import com.gengcon.print.draw.proxy.PrintSettingsProxyListener
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.gengcon.print.draw.view.operate.menubar.detail.common.FontManagerHelper
import com.idlefish.flutterboost.containers.FlutterBoostFragment
import com.jc.repositories.webview.library.eventbus.ScanExchangeSuccessEvent
import com.jc.repositories.webview.library.eventbus.ShopStoreBeanEvent
import com.jc.repositories.webview.library.eventbus.ShowTabEvent
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.shop.ShopType
import com.jc.repositories.webview.library.view.LabelCustomizeFragment
import com.jc.repositories.webview.library.view.NewStoreFragment
import com.niimbot.appframework_library.CURRENT_DEVICE_ELECTRICIRY
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.dialog.CustomDialog
import com.niimbot.appframework_library.dialog.SuperFactory
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.CommonUtil
import com.niimbot.appframework_library.utils.ImageLoader
import com.niimbot.appframework_library.utils.LoginUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.riskshield.RiskShieldHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.common.skin.SkinHelper
import com.niimbot.common.skin.SkinResource
import com.niimbot.graphqllibrary.Apollo
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.PreferencesUtils.getInt
import com.niimbot.utiliylibray.util.PreferencesUtils.put
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.json2Any
import com.niimbot.utiliylibray.util.logI
import com.niimbot.viplibrary.VipHelper
import com.nimmbot.business.livecode.CapAppHelper
import com.qyx.languagelibrary.LanguageRadioButton
import com.qyx.languagelibrary.utils.LanguageSPHelper
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.json2Array
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.base.BaseNoTitleBarActivity
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.bluetooth.BluetoothCoverStatusListener
import melon.south.com.baselibrary.bluetooth.BluetoothStatusManager
import melon.south.com.baselibrary.eventbus.LogoutTabStateEvent
import melon.south.com.baselibrary.eventbus.MeBubbleChangeEvent
import melon.south.com.baselibrary.eventbus.ShopBubbleChangeEvent
import melon.south.com.baselibrary.local.module.VersionBean
import melon.south.com.baselibrary.local.util.ExcelLocalManager
import melon.south.com.baselibrary.local.util.PrintDataLocalUtils
import melon.south.com.baselibrary.local.util.TemplateUsedRecordUtils
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.AppLanguageUtils
import melon.south.com.baselibrary.util.AppUpgradeDialog
import melon.south.com.baselibrary.util.EventBusUtils
import melon.south.com.baselibrary.util.StringUtil
import melon.south.com.loginlibrary.module.UserApiManager
import melon.south.com.mainlibrary.BuildConfig
import melon.south.com.mainlibrary.R
import melon.south.com.mainlibrary.databinding.ActivityMainBinding
import melon.south.com.mainlibrary.m.Double11Module
import melon.south.com.mainlibrary.m.InnerAppList
import melon.south.com.mainlibrary.util.Double11Utils
import melon.south.com.mainlibrary.util.MachineActUtils
import melon.south.com.mainlibrary.util.NpsUtils
import melon.south.com.mainlibrary.util.NpsVipUtil
import melon.south.com.mainlibrary.util.UserCenterEventHandle
import melon.south.com.mainlibrary.v.event.FirmwareCanUpdateEvent
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * 首页
 */
@SuppressLint("SetTextI18n")
class MainActivity : BaseNoTitleBarActivity(), BluetoothCoverStatusListener,
    LoginDataEnum.LoginStatusChangeListener, SkinHelper.SkinUpdateListener {
    private val binding by viewBinding(ActivityMainBinding::inflate)

    companion object {
        var showDeviceBubble = false
        var hasCheckVersion = false
        var unreadMessageCount = 0

        var tag_rb_color_normal = "color_main_tab_normal"
        var tag_rb_color_select = "color_main_tab_select"
        var tag_main_bubble = "color_main_tab_bubble"
        var tag_main_bubble_text = "color_main_tab_bubble_text"
        var tag_icon_rb_home_normal = "icon_rb_home_normal"
        var tag_icon_rb_home_select = "icon_rb_home_select"
        var tag_icon_rb_innerapp_normal = "icon_rb_innerapp_normal"
        var tag_icon_rb_innerapp_select = "icon_rb_innerapp_select"
        var tag_icon_rb_create_normal = "icon_rb_create_normal"
        var tag_icon_rb_shop_normal = "icon_rb_shop_normal"
        var tag_icon_rb_shop_select = "icon_rb_shop_select"
        var tag_icon_rb_usercenter_normal = "icon_rb_usercenter_normal"
        var tag_icon_rb_usercenter_select = "icon_rb_usercenter_select"

        fun showConfirmOpenDdfDialog(context: Context, filePath: String, confirmFinishPage: (() -> Unit)? = null) {
            val width = (AppUtils.getScreenWidth() * 0.7).toInt()
            val height = ViewGroup.LayoutParams.WRAP_CONTENT
            val holder = SuperFactory
                .createDialog(context)
                .setLayout(R.layout.dialog_open_outside_pdf)
                .setWidth(width)
                .setHeight(height)
                .setDimAmount(0.5f)
                .build()
            val dialogWindow = holder.window
            dialogWindow?.setBackgroundDrawableResource(android.R.color.transparent)
            holder.setCanceledOnTouchOutside(false)
            holder.show()
            holder.findViewById<View>(R.id.tvCancel).setOnClickListener { holder.dismiss() }
            holder.findViewById<View>(R.id.tvConfirm).setOnClickListener {
                holder.dismiss()
                confirmFinishPage?.invoke()
                FlutterMethodInvokeManager.openOutsidePdf(filePath)
            }
        }
    }

    private val TAG: String = "MainActivity"

    /**因为离线状态下，盒盖回调和rfid读取回调会有时序问题，导致先显示了rfid信息后又显示盒盖关闭，所以加了状态判断*/
    private var hasReadRfidSuccessed = false
    private var machineActUtils: MachineActUtils = melon.south.com.mainlibrary.util.MachineActUtils()

    override fun loginStatusChange(isLogin: Boolean) {
//        AppDataUtil.updateShopFirstPurchase(ShopFirstPurchaseBean())
        FontManagerHelper.initMyFonts()
//        if (LoginDataEnum.isLogin) {
//            checkStore()
//        }
        VipHelper.clearVipCacheWhenLoginChanged()
        TemplateUsedRecordUtils.postTemplateUsedRecord()     //账号切换上传本地数据库缓存历史记录
        CapAppHelper.closeCurrentApp()


        UserCenterEventHandle.get().notifyEventUserInfo(LoginDataEnum.userModule)

        BuriedHelper.trackLogin(LoginDataEnum.id.toString())

        riskCheck(isLogin)
        LogUtils.e("jctest loginStatusChange $isLogin")
    }

    override fun getBindingView() = binding.root

    override fun userInfoChange() {
        super.userInfoChange()
        UserCenterEventHandle.get().notifyEventUserInfo(LoginDataEnum.userModule)
    }

    override fun onRfidReadStatus(state: Int) {
        LogUtils.iTag("ConnectRfidRead", "MainActivity onRfidReadStatus, state = $state")
        if (!AppDataUtil.coverIsOpen) {
            homeFragment?.showCoversNew(RFIDConnectionProxyManager.isSupportCard())
        }
        if (state == PrintManager.GET_RFID_SUCCESSED) {
            hasReadRfidSuccessed = true
//            RFIDConnectionProxyManager.checkCacheData {
//                MainScope().launch {
//                    homeFragment?.updatePrinterInfo(true, isFromRfidRead = true)
//                }
//            }
            MainScope().launch {
                homeFragment?.updatePrinterInfo(true, isFromRfidRead = true)
            }
        }
    }

    override fun onETagStatusChange() {
        homeFragment?.changeConnectState()
    }

    override fun onCoverOpen() {
        Log.d(TAG, "onCoverOpen()")
        hasReadRfidSuccessed = false
        if (RFIDConnectionProxyManager.checkDeviceSupportRFID()) {
            homeFragment?.showCovers(true)
        } else {
            homeFragment?.hideCardInfo()
            return
        }
        EventBus.getDefault().post(CoverOpenEvent())
    }

    override fun onCoverClose() {
        Log.d(TAG, "onCoverClose()")
        if (!RFIDConnectionProxyManager.checkDeviceSupportRFID()) {
            homeFragment?.hideCardInfo()
            return
        } else {
            LogUtils.e("首页检查刷新RFID显示")
            if (!hasReadRfidSuccessed) {
                homeFragment?.showCoversNew(RFIDConnectionProxyManager.isSupportCard())
            }
        }
    }

    override fun onElectricityChange(electricity: Int) {
        super.onElectricityChange(electricity)
        Log.d(TAG, " onElectricityChange = $electricity  ")
        homeFragment?.changeElectricity(electricity)
//        ucFragment?.changeElectricity(electricity)
    }

    override fun onDisConnected() {
        super.onDisConnected()
        Log.d(TAG, "onDisConnected()")
        AppDataUtil.isInitConnect = false
        homeFragment?.hideCardInfo()
        hasReadRfidSuccessed = false
    }

    var isResume = false
    var needShowStore = false
    var popupBean: Double11Module.PopupBean? = null
    var isShowTab = true

    override fun onResume() {
        super.onResume()
        isResume = true
        if (needShowStore && popupBean != null && !AppUpgradeDialog.isUpgradeDialogShowing) {
            getDouble11(true)
        }
        if (!TextHookUtil.getInstance().isChina() || fragment == storeFragment) {
            binding.ivFirstPurchase.setImageResource(0)
        }
        checkShopHomeType()

        fragment?.let {
            when (it) {
                is HomeFragment -> BuriedHelper.trackEvent("view", "003")
//                is UserCenterFragment -> BuriedHelper.trackEvent("view", "006")
                else -> {}
            }
        }
    }

    override fun onPause() {
        super.onPause()
        isResume = false
    }

    private var homeFragment: HomeFragment? = null
    private var storeFragment: NewStoreFragment? = null
//    private var ucFragment: UserCenterFragment? = null
private var ucFragment: FlutterBoostFragment? = null
    private var labelFragment: LabelCustomizeFragment? = null
    private var innerAppFragment: InnerAppFragment? = null

    private var fragment: Fragment? = null

    override fun getLayoutId() = R.layout.activity_main

    override fun onNetChange(isNet: Boolean, msg: String) {
        super.onNetChange(isNet, msg)
        if (isNet) {
//            TemplateSyncLocalUtils.sync()
            PrintDataLocalUtils.syncData()
            ExcelLocalManager.sync {
                if (!it.isNullOrEmpty()) {
                    JCApiManager.deleteCloudFile(it)
                }
            }
            ShopManager.requestShopSiteIfNecessary()
        } else {
            TemplateSyncLocalUtils.stopTemplateSyncProcessIfNecessary()
        }
        GlobalScope.launch(Dispatchers.Main) {
            homeFragment?.onNetChange(isNet, msg)
            if (isNet && !ShopLoadInitializeUtil.get().loadSuccess && null != storeFragment) {
                ShopLoadInitializeUtil.get().initWevView(this@MainActivity)
            }
        }
        FlutterMethodInvokeManager.notifyNetworkChange(NetworkUtils.getNetworkStatus())
    }

    override fun init() {
        NpsUtils.preloadNpsWebview(this)
        NpsVipUtil.preloadNpsWebview(this)
        SkinHelper.register(this)
        EventBusUtils.register(this)
        AppUtils.setSuspendStatusBar(this)
        ShopManager.initShopType()
//        getData()
//        if (ShopManager.shopType == ShopType.China) {
//            rbStore.visibility = View.VISIBLE
//            rbLabel.visibility = View.VISIBLE
//            shop_btn.gone()
//        } else {
//            rbStore.visibility = View.GONE
//            rbLabel.visibility = View.GONE
//            ifShowStoreOverSea()
//        }
        if(ShopManager.shopType == ShopType.China){
            binding.rbStore.visibility = View.VISIBLE
            binding.rbLabel.visibility = View.VISIBLE
            binding.rbNew.visibility = View.VISIBLE
            binding.rbNew1.visibility = View.GONE
        }
        else if(ShopManager.shopType == ShopType.Oversea){
            binding.rbStore.visibility = View.VISIBLE
            binding.rbNew1.visibility = View.VISIBLE
            binding.rbLabel.visibility = View.GONE
            binding.rbNew.visibility = View.GONE
        }
        else{
            binding.rbNew.visibility = View.VISIBLE
            binding.rbNew1.visibility = View.GONE
            binding.rbStore.visibility = View.GONE
            binding.rbLabel.visibility = View.GONE
        }
        initFragment()
        when (intent.getIntExtra("index", 0)) {
            1 -> {
                binding.rbLabel.isChecked = true
                innerAppFragment
            }
            3 -> {
                binding.rbStore.isChecked = true
                checkStore()
                storeFragment
            }
            4 -> {
                binding.rbUserCenter.isChecked = true
                ucFragment
            }
            else -> {
                binding.rbHome.isChecked = true
                homeFragment
            }
        }?.let { showFragment(it) }
        BluetoothStatusManager.addWatcher(this)
        initConnect()
        storeFragment?.exeRxTimer()
        LoginDataEnum.registerChange(this)
        setStatusBar(Color.TRANSPARENT)

        binding.rbUserCenter.setBubbleParam(showDeviceBubble, unreadMessageCount)

        check2ConnectPage()

        parseScheme()

        notifyUpdateSkin()

//        checkVipNPS()

        checkDeviceRooted()
        LoginDataEnum.registerChange(this)

        riskCheckForInitConnect()

        freshBottomBg()

        if(isConnected()) {
            UserApiManager.freshLogin()
            VipHelper.preLoad()
        }
//        var hasRequestNotification = PreferencesUtils.getBoolean("hasRequestNotification",false)
//        //无通知权限并且没有申请过，则申请
//        if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED && !hasRequestNotification) {
//            PreferencesUtils.put("hasRequestNotification",true)
//            ActivityCompat.requestPermissions(this, arrayOf(Manifest.permission.POST_NOTIFICATIONS), 100)
//        }

    }

    private fun getData() {
        val currentLanguage = TextHookUtil.getInstance().languageName
        GlobalScope.launch {
            try{
                var appList = ArrayList<InnerAppList>()
                val appListJson=  PreferencesUtils.getString("tag_innerapps_app_list_$currentLanguage", "")
                if(appListJson.isNotEmpty()){
                    appList = json2Array(appListJson, InnerAppList::class.java) as ArrayList<InnerAppList>
                    InnerAppFragment.staticAppList=appList
                }
                Apollo.queryInnerAppList().collect { it ->
                    it.data?.getInnerAppList?.let { list ->
                        appList = json2Array(any2Json(list), InnerAppList::class.java) as ArrayList<InnerAppList>
                        InnerAppFragment.staticAppList=appList
                        PreferencesUtils.put("tag_innerapps_app_list_$currentLanguage", any2Json(list))
                    } ?: it.errors?.getOrNull(0)?.let { error ->
                        showToast(error.message)
                    }
                }
            } catch(e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun initFragment() {
        if (null == homeFragment) {
            homeFragment = HomeFragment()
        }
//        if (null == storeFragment) {
//            storeFragment = NewStoreFragment()
//        }
        checkStore()
        if (null == labelFragment) {
            labelFragment = LabelCustomizeFragment()
        }
        if (null == innerAppFragment) {
            innerAppFragment = melon.south.com.mainlibrary.v.InnerAppFragment.newInstance(InnerAppFragment.SOURCE_HOME)
            innerAppFragment?.prepareData(this)
        }
        if (null == ucFragment) {
//            ucFragment = UserCenterFragment()
//            ucFragment?.getUserShopInfo(this)
            ucFragment = FlutterBoostFragment.CachedEngineFragmentBuilder(FlutterBoostFragment::class.java)
                .url("meProfile")
                .urlParams(mapOf("isEnablePopGesture" to false))
                .build() as FlutterBoostFragment
        }
    }

    private fun checkStore() {
        if (null == storeFragment && LoginDataEnum.isLogin) {
            ShopLoadInitializeUtil.get().initWevView(this)
            storeFragment = NewStoreFragment()
        }
    }

    private var isDouble11 = false
    private var defaultDrawable: Drawable? = null
    private var checkDrawable: Drawable? = null
    private var hasCheckDouble11 = false
    private fun getDouble11(showOnResume: Boolean = false) {
        if (shopIsService) {
            //服务性商城 不请求双11
            return
        }
        var listener = object : Double11Utils.OnDoubleListener {
            override fun isShowDialog(): Boolean {
                return homeFragment?.isAdded ?: false && homeFragment?.isVisible ?: false
            }

            override fun onTab(double11NavigateBean: Double11Utils.Double11NavigateBean) {
                this@MainActivity.isDouble11 = true
                <EMAIL> = double11NavigateBean.defaultDrawable
                <EMAIL> = double11NavigateBean.checkDrawable
//                rbStore.setCompoundDrawablesWithIntrinsicBounds(null, defaultDrawable, null, null)
//                rbStore.text = double11NavigateBean.text ?: ""
//                double11NavigateBean.textColor?.let { rbStore.setTextColor(it) }
            }

            override fun onClick(path: String, typeCode: Int, name: String) {
                LogUtils.d("typeCode= $typeCode  ,path= $path")
                var finalPath = path
                // 2024/8/21 Ice_Liu url拼接/#/会造成链接错误，这种处理应该是web页面去处理
//                if((path.startsWith("http", true) || path.startsWith("https", true))) {
//                    val index = path.lastIndexOf("#/")
//                    if (!path.contains("#/") || path.substring(index).isEmpty()) {
//                        finalPath = "$path/#/"
//                    }
//                }
                NiimbotGlobal.bannerRouter(
                    this@MainActivity,
                    finalPath,
                    typeCode,
                    name,
                    sourcePage = "003",
                    jumpSource = SHOP_SOURCE_MAIN_DIALOG
                )
            }
        }
        if (showOnResume) {
            Double11Utils().showDialogOnResume(this, listener, popupBean!!)
        } else {
            Double11Utils().check(this, listener)
        }
    }


    /**
     * 缓存商城的statusbar 配置颜色状态
     * 目前做法不合理
     */
    var mStatusBarColorCache: NewStoreFragment.StatusBean? = null
    override fun initEvent() {
        binding.rgTab.setOnCheckedChangeListener { _, checkedId ->
            checkStore()
            binding.rbHome.typeface = Typeface.DEFAULT
            binding.rbStore.typeface = Typeface.DEFAULT
            binding.rbLabel.typeface = Typeface.DEFAULT
            binding.rbUserCenter.typeface = Typeface.DEFAULT
//            if ((isDouble11 || shopIsService) && !rbStore.isChecked) {
//                rbStore.setCompoundDrawablesWithIntrinsicBounds(null, defaultDrawable, null, null)
//            }
            when (checkedId) {
                R.id.rbHome -> {
                    showTab(true)
                    isShowTab = true
                    setStatusBar(Color.TRANSPARENT)
                    showFragment(homeFragment!!)
                    homeFragment?.notifyUpdateSkin()
                    homeFragment?.freshNps()
                    BuriedHelper.trackEvent("click", "002_001_001")
                }
                R.id.rbStore -> {
                    isShowTab = false
//                    if ((isDouble11 || shopIsService)) {
//                        rbStore.setCompoundDrawablesWithIntrinsicBounds(null, checkDrawable, null, null)
//                    }
                    if (storeFragment is NewStoreFragment) {
                        binding.rbStore?.postDelayed({
                            storeFragment?.appUserClickEvent()
                        }, 1000)
                    }
                    if (mStatusBarColorCache != null) {
                        var color = Color.parseColor("#ffffff")
                        try {
                            color = Color.parseColor(mStatusBarColorCache?.color)
                        } catch (e: Exception) {
                            color = Color.parseColor("#ffffff")
                        }
                        AppUtils.setStatusBarLightColor(
                            this,
                            color,
                            mStatusBarColorCache?.type == "1"
                        )
                    } else {
                        setStatusBar(Color.TRANSPARENT)
                    }
                    showFragment(storeFragment!!)
                    BuriedHelper.trackEvent("click", "002_001_004")
                }
                R.id.rbLabel -> {
                    showTab(true)
                    isShowTab = true
                    setStatusBar(Color.TRANSPARENT)
                    showFragment(innerAppFragment!!)
                    clearInnerAppBubble()
                    BuriedHelper.trackEvent("click", "002_001_002")
                }
                R.id.rbUserCenter -> {
                    showTab(true)
                    isShowTab = true
                    setStatusBar(Color.TRANSPARENT)
                    showFragment(ucFragment!!)
//                    ucFragment?.freshDeviceBubble(showDeviceBubble)
                    BuriedHelper.trackEvent("click", "002_001_005")
                }
            }
        }

        binding.rbStore.setOnTouchListener { v, event ->
            var isConsume = false
            if (event.action == KeyEvent.ACTION_DOWN) {
                if (!LoginDataEnum.isLogin) {
//                    CustomDialog.showLoginDialog(this@MainActivity)
                    NiimbotGlobal.checkLoginWithDialog(this@MainActivity)
                    isConsume = true
                }
                if (!isConnected()) {
                    showToast("app01139")
                    isConsume = true
                }
            }
            isConsume
        }
        val newAction: (String) -> Unit = {
            if (RFIDConnectionProxyManager.rfidTemplateModuleLocal != null
                && JCConnectionManager.getInstance().isConnected()) {

                val niimbotDrawData = NiimbotDrawData()
                niimbotDrawData.apply {
                    this.niimbotTemplate = TemplateModuleLocal.fromJson(it)!!
                    this.niimbotTemplate.apply {
//                        if (this.elements.isNotEmpty()) {
//                            this.elements.clear()
//                        }
                        var templateMoudle= TemplateModuleTransform.templateModuleLocalToTemplateModule(niimbotDrawData.niimbotTemplate)
                        templateMoudle.user_id = LoginDataEnum.id
                        this.name = LanguageUtil.findLanguageString("app100000728")
//                        TemplateDbUtils.insertOrUpdateTemplate(templateMoudle)
                        TemplateUsedRecordUtils.saveUsedRecord(
                            niimbotDrawData.niimbotTemplate.id,
                            LoginDataEnum.isLogin
                        )

//                        this.addDefaultTextItem(it)
                        this.id = System.currentTimeMillis().toString()
                    }
                    this.setNiimbotGoodsInfo()
                }

                if (!niimbotDrawData.niimbotTemplate.canEdit() && !NetworkUtils.isConnected()) {
                    showToast("app01139")
                }else{
                    GlobalScope.launch {
                        withContext(Dispatchers.Main) {
                            SyncTemplatePresenter.startNiimbotDrawActivity(
                                activity = this@MainActivity,
                                niimbotDrawData = niimbotDrawData,
                                needRfidTemplate = true,
                                isFromScanPrint = false,
                                isRetainActivity = true,
                                isCable=true
                            )
                        }
                        BuriedHelper.trackEvent(
                            "click", "002_001_003", hashMapOf(
                                Pair("type", "1"),
                            )
                        )
                    }
                }

            } else {
                BuriedHelper.trackEvent(
                    "click", "002_001_003", hashMapOf(
                        Pair("type", "0"),
                    )
                )
                NiimbotGlobal.gotoFlutterPage("createLabelHome", false, true)
            }
        }

        //新建标签
        binding.rbNew.setOnNotDoubleClickListener(outTime = 2000) {
            FlutterMethodInvokeManager.getLayoutTemplate(RFIDConnectionProxyManager.rfidTemplateModuleLocal) {
                newAction.invoke(it)
            }
        }

        binding.rbNew1.setOnNotDoubleClickListener(outTime = 2000){
            FlutterMethodInvokeManager.getLayoutTemplate(RFIDConnectionProxyManager.rfidTemplateModuleLocal) {
                newAction.invoke(it)
            }
        }

        binding.shopBtn.setOnNotDoubleClickListener {
            LoginUtils.loginDoAction {
                if (!isConnected()) {
                    showToast("app01139")
                } else {
                    launchShopActivity(this)
                }
            }
        }
    }

    fun isShowHomeFragment() = fragment == homeFragment

    private fun setStatusBar(color: Int) {
        AppUtils.setStatusBarLightColor(this, color, true)
    }

    private fun showFragment(baseFragment: Fragment) {
        AppDataUtil.isStoreFragmentShow = baseFragment is NewStoreFragment
        if (baseFragment == fragment) return
        val transaction = supportFragmentManager.beginTransaction()
        if (!baseFragment.isAdded) {
            transaction.add(R.id.flPager, baseFragment)
        }
        transaction.show(baseFragment)
        fragment?.let { transaction.hide(it) }
        transaction.commitAllowingStateLoss()
        fragment = baseFragment
        if (fragment != storeFragment && AppDataUtil.shopFirstPurchase.isFirst == 1 && TextHookUtil.getInstance()
                .isChina()
        ) {
            ImageLoader.showImage(
                this@MainActivity,
                AppDataUtil.shopFirstPurchase.homeIcon.path,
                binding.ivFirstPurchase
            )
        } else {
            binding.ivFirstPurchase.setImageResource(0)
        }
    }

    fun gotoStore(banner: String, jumpSource: String = "") {
        LoginDataEnum.loginCheck(mActivity) {
            var params = "${if (banner.contains("?")) "&" else "?"}jumpSource=$jumpSource"
            NiimbotGlobal.gotoShopWeb(this, "${banner}$params")
        }
    }

    fun gotoLabel(banner: String, jumpSource: String = "") {
//        showFragment(storeFragment!!)
        if (!LoginDataEnum.isLogin) {
//            CustomDialog.showLoginDialog(mActivity as AppCompatActivity?)
            NiimbotGlobal.checkLoginWithDialog(this@MainActivity)
            return
        }
        var params =
            "${if (banner.contains("?")) "&" else "?"}token=${HttpTokenUtils.getShopToken()}&version=${BuildConfig.VERSION_NAME}&platform_system_id=CP001&jumpSource=$jumpSource"
        labelFragment?.setBannerUrl("$banner$params")
        binding.rbLabel?.isChecked = true
//        showFragment(storeFragment!!)
    }

    override fun onDestroy() {
        super.onDestroy()
        ShopLoadInitializeUtil.get().destroyWebView()
        ConnectionProxyManager.instance.removeConnectionProxyListener(connectionProxyImpl)
        EventBusUtils.unregister(this)
        BluetoothStatusManager.removeWatcher(this)
        BuriedHelper.trackEvent("append", "001")
        TextHookUtil.getInstance().unregisterChange(this)
        LoginDataEnum.unregisterChange(this)
        DokitOkGo.getInstance().cancelAll()
        SkinHelper.unregister(this)
    }

    override fun onAttachFragment(fragment: Fragment) {
        super.onAttachFragment(fragment)
        fragment?.apply {
            if (homeFragment == null && fragment is HomeFragment) homeFragment = fragment
            if (storeFragment == null && fragment is NewStoreFragment) storeFragment = fragment
            if (labelFragment == null && fragment is LabelCustomizeFragment) labelFragment = fragment
//            if (ucFragment == null && fragment is UserCenterFragment) ucFragment = fragment
            if (ucFragment == null && fragment is FlutterBoostFragment) ucFragment = fragment
            if (innerAppFragment == null && fragment is InnerAppFragment) innerAppFragment = fragment
        }
    }

    fun showTab(isShow: Boolean) {
        if (isShowTab) {
            return
        }
        if (isShow) {
            checkShopHomeType()
//            checkShopFirstPurchase()
            binding.rgTab.visibility = View.VISIBLE
            binding.bottomTab.visibility = View.VISIBLE
        } else {
            binding.rgTab.visibility = View.GONE
            binding.bottomTab.visibility = View.GONE
        }
    }


    var lastTime: Long = 0L
    override fun onBackPressed() {
        if (binding.rgTab.visibility == View.GONE && binding.rbStore.isChecked) {
            storeFragment?.onBackPressed()
            return
        }
        if (binding.rgTab.visibility == View.GONE && binding.rbLabel.isChecked) {
            labelFragment?.onBackPressed()
            return
        }
        if (System.currentTimeMillis() - lastTime < 1000) {
            super.onBackPressed()
        }
        lastTime = System.currentTimeMillis()
    }

    private fun checkShopFirstPurchase() {
    }

    private fun checkVersion(isCheck: Boolean = false, callback: ()->Unit) {
        if (isCheck) {
            showLoading()
        }
        if(!NetworkUtils.isConnected()){
            try {
                val checkInfo = PreferencesUtils.getString("versionCheckInfo","")
                LogUtils.e("checkVersion checkInfo==${checkInfo}")
                if(TextUtils.isEmpty(checkInfo)){
                    callback.invoke()
                }else{
                    val versionBean = json2Any<VersionBean>(checkInfo)
                    if (versionBean != null && CommonUtil.isAppVersionNew(versionBean.version)) {
                        regularlyUpdate(versionBean, isCheck, callback)
                    }
                }
            }catch (e: Exception){
                e.printStackTrace()
            }

            return
        }

        JCApiManager.validateAppVersion(object : JCTResponseListener<VersionBean> {
            override fun onSuccess(body: VersionBean) {
                LogUtils.e("checkVersion success==${body.toString()}")
                if (isCheck) {
                    dismissLoading()
                }
                if (!TextUtils.isEmpty(body.version)) {
                    put("versionCheckInfo", any2Json(body))
                    put("hasNewVersion", true)
                    regularlyUpdate(body, isCheck, callback)
                    return
                } else{
                    //没有更新的时候清除缓存
                    put("versionCheckInfo", "")
                }
                if (isCheck) {
                    showToast("app00495")
                }

                put("hasNewVersion", false)
                callback.invoke()
            }

            override fun onError(message: String) {
                LogUtils.e("checkVersion error==$message")
                callback.invoke()
                if (isCheck) {
                    dismissLoading()
                    if (!TextUtils.isEmpty(message)) {
                        showToast(message)
                    }
                }
            }
        })
    }

    private fun regularlyUpdate(versionBean: VersionBean, isCheck: Boolean, callback: () -> Unit) {
        AppUpgradeDialog(
            versionBean.installationPackageAddress,
            versionBean.version,
            versionBean.instructions,
            this,
            versionBean.isForceUpdate
        ).showUpgradeDialog{ isCancel ->
            if(isCancel){
                callback.invoke()
            }
        }
        put("version_code", versionBean.versionCode)
    }

    private val connectionProxyImpl = ConnectionProxyImpl()
    private fun initConnect() {
        AppDataUtil.isInitConnect = false
        ConnectionProxyManager.instance.addConnectionProxyListener(connectionProxyImpl)
        connectionProxyImpl.printSettingsProxyListener = object : PrintSettingsProxyListener {
            override fun onConnectedRefreshUI(deviceName: String?) {
                super.onConnectedRefreshUI(deviceName)
                UserCenterEventHandle.get().notifyEventPrinterName(deviceName)
                AppDataUtil.isInitConnect = true
                LogUtils.d("============RFID onConnectedRefreshUI")
//                ucFragment?.changeDeviceConnectStatus(true)
                homeFragment?.changeDeviceConnectStatus(true)

                GlobalScope.launch(Dispatchers.Main) {
                    val electricity = getInt(CURRENT_DEVICE_ELECTRICIRY, -1)
                    if (electricity > 0) {
                        homeFragment?.changeElectricity(electricity)
//                        ucFragment?.changeElectricity(electricity)
                    }
                }
                if(DeviceC1Helper.isC1Page || RFIDConnectionProxyManager.connectedDevice?.isC1() == true){
                    return
                }

                GlobalScope.launch(Dispatchers.Main) {
                    if (RFIDConnectionProxyManager.isSupportCard()) {
                        RFIDConnectionProxyManager.initRFID {
//                            RFIDConnectionProxyManager.checkCacheData {
//                                MainScope().launch {
//                                    homeFragment?.showCardInfo()
//                                }
//                            }
                            MainScope().launch {
                                homeFragment?.showCardInfo()
                            }
                        }
                    }
                }
            }

            override fun onStateOffRefreshUI() {
                super.onStateOffRefreshUI()
                LogUtils.d("============RFID onStateOffRefreshUI")
                AppDataUtil.isInitConnect = false
                homeFragment?.disConnectReInitUi(false)
//                ucFragment?.changeDeviceConnectStatus(false)
                UserCenterEventHandle.get().notifyEventPrinterName("")
           //     UserCenterEventHandle.get().notifyEventPrintUpdate(false)
            }
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(
            AppLanguageUtils.attachBaseContext(
                newBase,
                TextHookUtil.getInstance().locale.toString()
            )
        )
    }

    private fun setCheckedRadioButton() {
        binding.rgTab?.clearCheck()
        binding.rgTab?.check(R.id.rbHome)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLogoutTabStateEvent(event: LogoutTabStateEvent) {
        setCheckedRadioButton()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onShowTabEvent(event: ShowTabEvent) {
        showTab(event.hasShow)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onShopStoreBeanEvent(event: ShopStoreBeanEvent) {
        mStatusBarColorCache = event.statusBean
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: ShopBubbleChangeEvent) {
        if (event.tabNum >= 0) {
           notifyShopBubble(event.tabNum)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: MeBubbleChangeEvent) {
        unreadMessageCount = event.tabNum
        if (event.tabNum >= 0) {
            notifyMeBubble(event.tabNum)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: ScanExchangeSuccessEvent) {
        UserApiManager.freshLogin()
    }
    //在首页主动连接打印机成功事件
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(homeManualConnectSuccessEvent: String) {
        if (!TextUtils.isEmpty(homeManualConnectSuccessEvent)) {
            try {
                val json = JSONObject(homeManualConnectSuccessEvent)
                val action = json.optString("action")
                if(action == "homeManualConnectSuccess"){
                    GlobalScope.launch {
                        val canShowMachineAct = machineActUtils.canShowMachineAct()
                        if(canShowMachineAct){
                            machineActUtils.fetchDeviceAct(object : JCTResponseListener<MachineActUtils.MachineActBean>{
                                override fun onSuccess(body: MachineActUtils.MachineActBean) {
                                    if(body.titleCode != null){
                                        machineActUtils.showMachineActDialog(this@MainActivity,body)
                                        BuriedHelper.trackEvent("show", "003_397")
                                    }
                                }

                                override fun onError(message: String) {
                                }

                            })
                        }
                    }
                }
            }catch (e: Exception){

            }
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFirmwareCanUpdateEvent(event: FirmwareCanUpdateEvent) {
        showDeviceBubble = event.showBubble
//        binding.rbUserCenter.setNeedBubble(event.showBubble)
        binding.rbUserCenter.setBubbleParam(event.showBubble, unreadMessageCount)
//        ucFragment?.freshDeviceBubble(event.showBubble)
        UserCenterEventHandle.get().notifyEventPrintUpdate(showDeviceBubble)
    }

    /**
     * 测试事件传递
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMessageEvent(data: String) {
//        LogUtils.d("========================data = ${data}")
        if (!TextUtils.isEmpty(data)) {
            try {
                val json = JSONObject(data)
                when (json.optString("operateCode")) {
                    "SAVE" -> showToast("外部保存，定制化需求")
                    "SETTING" -> startHandbookAction(json.optJSONObject("result").optString("url"))
                    "PRINT" -> showToast("外部调用打印，定制化需求")
                    "GOTO_STORE" -> {
                        val result = json.optJSONObject("result")
                        val templateCode = result.optString("templateCode")
                        val templateType = result.optInt("templateType")
                        val jumpSource = result.optString("jumpSource")
                        gotoStore(this, templateCode, templateType, jumpSource)
                    }
                    else -> {}
                }
            }catch (e: Exception){

            }
        }
    }

    private fun startHandbookAction(url: String, name: String = "") {
        if (url.isNullOrEmpty()) return
        NiimbotGlobal.routingByScheme(this, url, name, catchBackEvent = true)
//        LeMessageManager.getInstance().dispatchMessage(
//            LeMessage(
//                LeMessageIds.MSG_ACTION_GO_ACTIVITY,
//                WebViewActivityConfig(this).apply {
//                    intent.putExtra("title", name)
//                    intent.putExtra("url", url)
//                    intent.putExtra("machineId", BluetoothUtil.getLastDeviceId())
//                }
//            )
//        )
    }

    var shopIsCheck = false
    var shopIsService = false
    private fun checkShopHomeType() {
        ShopManager.checkChinaShopHomeType {
            val getDouble11Fun = {
                if(!hasCheckDouble11){
                    hasCheckDouble11 = true
                    getDouble11()
                }
            }
            if(!hasCheckVersion) {
                hasCheckVersion = true
                checkVersion(isCheck = false, getDouble11Fun)
            }
            else{
                if(!AppUpgradeDialog.isUpgradeDialogShowing){
                    getDouble11Fun.invoke()
                }

            }
        }
    }

    private fun setShopServiceUi() {
        defaultDrawable = resources.getDrawable(R.drawable.ic_shop_service_u)
        checkDrawable = resources.getDrawable(R.drawable.ic_shop_service_u_s)
        if (binding.rbStore.isChecked) {
            binding.rbStore.setCompoundDrawablesWithIntrinsicBounds(null, checkDrawable, null, null)
        } else {
            binding.rbStore.setCompoundDrawablesWithIntrinsicBounds(null, defaultDrawable, null, null)
        }
        binding.rbStore.text = "服务"
    }

    /**
     * 跳转商城
     */
    private fun gotoStore(
        context: Context,
        templateCode: String,
        templateType: Int,
        jumpSource: String = ""
    ) {
        val url = if (templateCode.isNullOrEmpty())
            ShopManager.shopHome + "?jumpSource=$jumpSource"
        else
            if (templateCode.contains("http") || templateCode.contains("https")) {
                "$templateCode?mType=$templateType&jumpSource=$jumpSource"
            } else {
                ShopManager.SHOP_PURCHASE + "/$templateCode?mType=$templateType&jumpSource=$jumpSource"
            }
        NiimbotGlobal.gotoShopWeb(context as Activity, url)
    }

    /**
     * 是否显示海外商城
     * 2021/11/2 Ice_Liu 根据IP地址只在北美地区显示
     */
    private fun ifShowStoreOverSea() {
//        JCApiManager.checkUserNorthAmerica(object : JCTResponseListener<Boolean> {
//            override fun onSuccess(body: Boolean) {
//                try {
//                    shop_btn.visible(body)
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                }
//            }
//
//            override fun onError(message: String) {
//                shop_btn.gone()
//            }
//        })
        if(ShopManager.shopType == ShopType.Oversea){
            binding.shopBtn.visible()
        }
        else{
            binding.shopBtn.gone()
        }
    }

    private fun check2ConnectPage() {
        if (intent.getBooleanExtra("toConnectPage", false)) {
            intent.putExtra("toConnectPage", false)
            GlobalScope.launch {
                delay(1000)
                withContext(Dispatchers.Main) {
                    LeMessageManager.getInstance().dispatchMessage(
                        LeMessage(
                            LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                            DeviceConnectActivityConfig(this@MainActivity, true)
                        )
                    )
                }
            }
        }
    }

    private var newInnerAppId: String = ""
    fun notifyNewInnerApp(needBubble: Boolean, newId: String) {
        binding.rbLabel.postDelayed({
            newInnerAppId += "-$newId"
            binding.rbLabel.setNeedBubble(needBubble)
        }, 1000)
    }

    private fun clearInnerAppBubble() {
        if (!newInnerAppId.isNullOrBlank()) {
            var ids = PreferencesUtils.getString("tag_innerapps_new_id_red", "")
            put("tag_innerapps_new_id_red", "$newInnerAppId$ids")
            binding.rbLabel.setNeedBubble(false)
        }
    }

    /**
     * 刷新商城红点
     * @param num Int
     */
    fun notifyShopBubble(num: Int) {
        binding.rbStore.postDelayed({
            binding.rbStore.setNeedBubble(num)
        }, 1000)
    }

    /**
     * 刷新我的红点
     * @param num Int
     */
    fun notifyMeBubble(num: Int) {
        binding.rbUserCenter.postDelayed({
            binding.rbUserCenter.setBubbleParam(showDeviceBubble,num)

        }, 1000)
    }

    @SuppressLint("MissingSuperCall")
    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        parseScheme()
    }


    private fun parseScheme() {
        val languageUpdate = intent.getBooleanExtra("languageUpdate", false)
        intent.removeExtra("languageUpdate")
        val handleOutsidePdf = intent.getBooleanExtra("handleOutsidePdf", false)
        if(handleOutsidePdf) {
            if(languageUpdate) {
                return
            }
            val pdfFilePath = intent.getStringExtra("pdfFilePath")
            if (pdfFilePath?.isNotEmpty() == true) {
                MainScope().launch {
                    delay(500)
                    showConfirmOpenDdfDialog(this@MainActivity, pdfFilePath)
                }
            }
            else {
                val errorMsg = intent.getStringExtra("errorMsg")
                if(errorMsg?.isNotEmpty() == true) {
                    MainScope().launch {
                        delay(500)
                        showToast(errorMsg)
                    }
                }
            }
        }
        else {
            val redirectUrl = intent.getStringExtra("redirectUrl")
            if (!redirectUrl.isNullOrBlank()) {
                val isWeb = intent.getBooleanExtra("isWeb", false)
                if (isWeb) {
                    NiimbotGlobal.gotoWeb(this, redirectUrl, "")
                } else {
                    NiimbotGlobal.routingByScheme(this, redirectUrl)
                }
                intent.removeExtra("redirectUrl")
            }
            parsePushNotification()
        }
    }

    private fun parsePushNotification() {
        val isFromPushNotification = intent.getBooleanExtra("isFromPushNotification",false)
        if(isFromPushNotification){
            val pushContent = intent.getStringExtra("pushContent")
            PushBindUtil.parsePushNotification(pushContent)
        }
    }

    override fun notifyUpdateSkin() {
        if (!SkinHelper.hasActiveSkin(TextHookUtil.getInstance().languageName)) return
        var rbTextColorNormal =
            SkinResource.getColor(TextHookUtil.getInstance().languageName, tag_rb_color_normal)
        var rbTextColorSelect =
            SkinResource.getColor(TextHookUtil.getInstance().languageName, tag_rb_color_select)
        if (rbTextColorNormal != -1 && rbTextColorSelect != -1) {
            var stateArray = arrayOfNulls<IntArray>(2)
            stateArray[0] = intArrayOf(android.R.attr.state_checked)
            stateArray[1] = intArrayOf()
            var colorState =
                ColorStateList(stateArray, intArrayOf(rbTextColorSelect, rbTextColorNormal))
            binding.rbHome?.setTextColor(colorState)
            binding.rbLabel?.setTextColor(colorState)
            binding.rbNew?.setTextColor(colorState)
            binding.rbStore?.setTextColor(colorState)
            binding.rbUserCenter?.setTextColor(colorState)
        }

        freshCreateBtn()
        freshHomeBtn(tag_icon_rb_home_normal, tag_icon_rb_home_select, binding.rbHome)
        freshHomeBtn(tag_icon_rb_innerapp_normal, tag_icon_rb_innerapp_select, binding.rbLabel)
        freshHomeBtn(tag_icon_rb_shop_normal, tag_icon_rb_shop_select, binding.rbStore)
        freshHomeBtn(tag_icon_rb_usercenter_normal, tag_icon_rb_usercenter_select, binding.rbUserCenter)

        homeFragment?.notifyUpdateSkin()
    }

    private fun freshCreateBtn() {
        var iconNormal = SkinResource.getDrawable(
            TextHookUtil.getInstance().languageName,
            tag_icon_rb_create_normal
        )
        if (null != iconNormal) {
            if (TextHookUtil.getInstance().isTraditionalChinese()) {
                binding.rbNew1?.setCompoundDrawablesWithIntrinsicBounds(null, iconNormal, null, null)
            } else {
                binding.rbNew?.setCompoundDrawablesWithIntrinsicBounds(null, iconNormal, null, null)
            }
        }
    }

    private fun freshHomeBtn(tagNormal: String, tagSelect: String, target: LanguageRadioButton?) {
        var iconNormal =
            SkinResource.getDrawable(TextHookUtil.getInstance().languageName, tagNormal)
        var iconSelect =
            SkinResource.getDrawable(TextHookUtil.getInstance().languageName, tagSelect)
        if (null != iconNormal && null != iconSelect) {
            var stateArray = arrayOfNulls<IntArray>(2)
            stateArray[0] = intArrayOf(-android.R.attr.state_checked)
            stateArray[1] = intArrayOf(android.R.attr.state_checked)
            var stateDrawable = StateListDrawable()
            stateDrawable.addState(stateArray[0], iconNormal)
            stateDrawable.addState(stateArray[1], iconSelect)
            target?.setCompoundDrawablesWithIntrinsicBounds(null, stateDrawable, null, null)
        }
    }

    private fun freshBottomBg() {
        MainScope().launch {
            var bottomBgUrl = LanguageUtil.findLanguageString("app100001262")
            if (StringUtil.isUrl(bottomBgUrl)) {
                val bitmap = withContext(Dispatchers.Default){ ImageLoader.getBitmap(this@MainActivity, bottomBgUrl) }
                if (bitmap != null){
                    binding.rgTab.background = BitmapDrawable(SuperUtils.superContext.resources, bitmap)
                }
            }
        }
    }

    override fun languageUpdate() {
        super.languageUpdate()
        LogUtils.e("jctest languageUpdate ${TextHookUtil.getInstance().languageName}")
        UserCenterEventHandle.get().notifyEventLanguageType(TextHookUtil.getInstance().languageName)
        homeFragment?.languageChange(TextHookUtil.getInstance().languageName)
    }

    private fun checkVipNPS() {
        MainScope().launch {
            delay(1000)
            val vipIds = PreferencesUtils.getString("has_report_vip_nps")
            if (isResume && LoginDataEnum.isLogin && !vipIds.contains(LoginDataEnum.id.toString()) && VipHelper.isCurrentUserVip() && (TextHookUtil.getInstance().isChina()
                || TextHookUtil.getInstance().isSimpleChinese()
                || TextHookUtil.getInstance().isEnglish()
                || TextHookUtil.getInstance().isKorean())) {
                NiimbotGlobal.gotoFlutterPage("vipNPSGrade", hashMapOf("token" to HttpTokenUtils.getAccessToken()), isTransParent = true)
                put("has_report_vip_nps", "$vipIds-${LoginDataEnum.id}")
            }
        }
    }

    private fun checkDeviceRooted() {
        if (RootCheckUtils.isDeviceRooted() || RootCheckUtils.isEmulator()) {
            CustomDialog.showRedButtonDialog(
                this,
                "app00032", "app100000634", null, "app00707"
            ) {
                com.blankj.utilcode.util.AppUtils.exitApp()
            }
        }
    }

    private fun riskCheck(isLogin: Boolean) {
        if (isLogin) {
            val currentDevice = RFIDConnectionProxyManager.connectedDevice
            if (JCConnectionManager.getInstance().isConnected() && currentDevice != null) {
                val connectType = if(JCConnectionManager.getInstance().isBTConnected()) 1 else 2
                RiskShieldHelper.riskCheck(
                    action = 1,
                    deviceConnectType = connectType,
                    firmwareVersion = currentDevice.softwareVersion,
                    hardwareVersion = currentDevice.hardwareVersion,
                    machineId = currentDevice.deviceName
                )
            } else {
                RiskShieldHelper.riskCheck(1)
            }
        }
    }

    private fun riskCheckForInitConnect() {
        val currentDevice = RFIDConnectionProxyManager.connectedDevice
        if (JCConnectionManager.getInstance().isConnected() && currentDevice != null) {
            val connectType = if (JCConnectionManager.getInstance().isBTConnected()) 1 else 2
            val map = RFIDConnectionProxyManager.getRfidInfo()
            RiskShieldHelper.riskCheck(
                action = 3,
                deviceConnectType = connectType,
                firmwareVersion = currentDevice.softwareVersion,
                hardwareVersion = currentDevice.hardwareVersion,
                machineId = currentDevice.deviceName,
                paperLengthUsed = map["paperLengthUsed"]?.toFloatOrNull() ?: -1f,
                paperLengthUsedQuantitySum = map["paperLengthUsedQuantitySum"]?.toFloatOrNull()
                    ?: -1f,
                paperSerial = map["paperSerial"] ?: "",
                paperUsed = map["paperUsed"]?.toFloatOrNull()?.toInt() ?: -1,
                paperUsedQuantitySum = map["paperUsedQuantitySum"]?.toFloatOrNull()?.toInt() ?: -1,
                ribbonSerial = map["ribbonSerial"] ?: "",
                ribbonUsed = map["ribbonUsed"]?.toFloatOrNull() ?: -1f,
                ribbonUsedQuantitySum = map["ribbonUsedQuantitySum"]?.toFloatOrNull() ?: -1f
            )
        }
    }
}
