//
//  JCShopProductVC.m
//  Runner
//
//  Created by xy on 2018/10/17.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopAliPay.h"

@interface JCShopAliPay ()
@end

@implementation JCShopAliPay

- (id)init {
    self = [super init];
    if(self){
        self.isNewCreate = YES;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
    if (@available(iOS 13.0, *)) {
        statusBarStyle = UIStatusBarStyleDarkContent;
    }
    [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self setupWebViewFrame];
    [self loadUrl:[ShopURL stringByAppendingString: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/paySuccess?" : @"#/paySuccess?"]];
    // 脚本消息处理器已在基类中注册，无需重复注册
    
} 


#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    // 处理特殊的返回逻辑
    if ([message.name isEqualToString:@"back_home"]) {
        [self.navigationController popViewControllerAnimated:YES];
        return;
    }
    
    // 调用基类的消息处理方法处理其他消息
    [super userContentController:userContentController didReceiveScriptMessage:message];
}

@end
