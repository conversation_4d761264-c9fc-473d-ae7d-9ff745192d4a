package com.gengcon.android.jccloudprinter

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import com.andsync.xpermission.XPermissionUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEventRegister
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.riskshield.RiskShieldHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSONObject
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.logE
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import melon.south.com.baselibrary.local.util.AdLocalUtils
import melon.south.com.baselibrary.privacy.AppPrivacySettingsUtil
import melon.south.com.baselibrary.util.AppLanguageUtils
import melon.south.com.mainlibrary.R
import melon.south.com.mainlibrary.v.AdActivity
import melon.south.com.mainlibrary.v.MainActivity
import org.greenrobot.eventbus.EventBus

class LaunchActivity: AppCompatActivity(), TextHookUtil.TextChangeListener {
    private lateinit var img: ImageView

    override fun onCreate(savedInstanceState: Bundle?) {
        overridePendingTransition(R.anim.launch_in, R.anim.launch_out)
        super.onCreate(savedInstanceState)
        AppUtils.setStatusBarLightColor(this, resources.getColor(R.color.transparent))
        setContentView(R.layout.activity_launch)
        img = findViewById(R.id.img)
        if (TextHookUtil.getInstance().isChina()) {
            img.setImageResource(R.drawable.ic_launch_zh)
        } else {
            img.setImageResource(R.drawable.ic_launch_en)
        }
        printInfo()
        if (!isTaskRoot) {
            finish()
            return

        }
        isPrivacySettings()


    }


    private fun toGuide() {
        NiimbotGlobal.gotoFlutterPage("welcome", isTransParent = true)
    }


    override fun onStart() {

        super.onStart()
        TextHookUtil.getInstance().registerContextChange(this)
    }




    private suspend fun delayFinishLaunch() {
        if (needStartAd()) {
            startActivity(Intent(this@LaunchActivity, AdActivity::class.java))
        } else {
            if (PreferencesUtils.getBoolean("isFirstStart", true)) {
                PreferencesUtils.put("isFirstStart", false)
                toGuide()
                return
            } else {
                startActivity(
                    Intent(
                        this@LaunchActivity,
                        MainActivity::class.java
                    ).setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                )
            }
        }
        <EMAIL>()
    }

    /**
     * 是否需要启动广告页
     */
    private suspend fun needStartAd(): Boolean {
        val pageSwitch = PreferencesUtils.getBoolean("${LoginDataEnum.id}-launchPageSwitch",true)
        return pageSwitch && AdLocalUtils.isNeedAd()
    }


    /**
     * 如果有权限没有赋予会有两次调用：
     * 第一次启动； permissions.size=0
     * 第二次选择完权限赋予 permissions.size = 请求的权限个数（总数）
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        logE("isGrant", "${permissions.size}")
        if (permissions.isNotEmpty()) {
            XPermissionUtils.onRequestPermissionsResult(
                this,
                requestCode,
                permissions,
                grantResults
            )
        }
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    private fun printInfo() {
        LogUtils.d("+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++")
        LogUtils.d(("flag: " + getFlag()))
        LogUtils.d("taskId: $taskId")
        LogUtils.d("Pid: " + android.os.Process.myPid())
    }

    private fun getFlag(): String {
        var flag = ""
        if (intent.flags and Intent.FLAG_ACTIVITY_NEW_TASK != 0) {
            flag += "Intent.FLAG_ACTIVITY_NEW_TASK "
        }
        if (intent.flags and Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED != 0) {
            flag += "Intent.FLAG_ACTIVITY_RESET_TASK_IF_NEEDED "
        }

        if (intent.flags and Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT != 0) {
            flag += "Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT "
        }
        return flag
    }


    private fun reInitSync() {
        LogUtils.d("-----------syncEventData reInitSync-----------")
        postThirdLibInit()
        img.postDelayed({
            MainScope().launch {
                delayFinishLaunch()
                FlutterEventRegister.notifyRefreshUserAgent()
                riskCheck()

                BuriedHelper.trackEvent(
                    "appstart", "001", hashMapOf(
                        Pair("is_device_rooted", NiimbotGlobal.isDeviceRooted())
                    )
                )
            }
        }, 1500)
    }

    private fun riskCheck() {
        MainScope().launch {
            delay(5000)
            RiskShieldHelper.riskCheck(0)
        }
    }

    private fun postThirdLibInit() {
        val jsonObject = JSONObject()
        jsonObject["action"] = "thirdLibInit"
        EventBus.getDefault().post(any2Json(jsonObject))
    }

    private fun isPrivacySettings() {
        AppPrivacySettingsUtil.isPrivacySettings(this,
            object : AppPrivacySettingsUtil.AppPrivacySettingsLisener {
                override fun reInitUi() {
                    reInitSync()
                }
            })
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(
            AppLanguageUtils.attachBaseContext(
                newBase,
                TextHookUtil.getInstance().locale.toString()
            )
        )
    }

    override fun languageUpdate() {
    }

    override fun onDestroy() {
        XPermissionUtils.clear()
        TextHookUtil.getInstance().unregisterChange(this)
        super.onDestroy()
    }
}
