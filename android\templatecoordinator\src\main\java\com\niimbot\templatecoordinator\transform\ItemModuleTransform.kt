package com.niimbot.templatecoordinator.transform

import com.niimbot.appframework_library.common.module.template.item.BaseItemModuleEx
import com.niimbot.appframework_library.common.module.template.item.FlowItemModule
import com.niimbot.appframework_library.common.module.template.item.LineItemModule
import com.niimbot.appframework_library.common.module.template.item.PictureItemModule
import com.niimbot.appframework_library.common.module.template.item.RectItemModule
import com.niimbot.appframework_library.common.module.template.item.TextItemModule
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.module.template.item.barcode.BarcodeItemModule
import com.niimbot.appframework_library.common.module.template.item.qrcode.QrCodeItemModule
import com.niimbot.appframework_library.common.module.template.item.table.TableItemModule
import com.niimbot.utiliylibray.util.any2Json
import melon.south.com.baselibrary.module.BaseItemModule

/**
 *
 *
 * <AUTHOR>
 * @since 2020/5/19
 */
object ItemModuleTransform {
    fun itemModuleExToItemModule(baseItemModuleEx: BaseItemModuleEx): BaseItemModule{
        val baseItemModule = BaseItemModule()
        baseItemModule.itemId = baseItemModuleEx.id
        baseItemModule.itemType = baseItemModuleEx.type
        baseItemModule.x = baseItemModuleEx.x
        baseItemModule.y = baseItemModuleEx.y
        baseItemModule.width = baseItemModuleEx.width
        baseItemModule.height = baseItemModuleEx.height
        baseItemModule.rotate = baseItemModuleEx.rotate
        baseItemModule.isLock = baseItemModuleEx.isLock
        baseItemModule.zIndex = baseItemModuleEx.zIndex
        baseItemModule.isOpenMirror = baseItemModuleEx.isOpenMirror
        baseItemModule.mirrorId = baseItemModuleEx.mirrorId
        baseItemModule.hasVipRes = baseItemModuleEx.hasVipRes
        baseItemModule.elementColor = baseItemModuleEx.elementColor
        baseItemModule.colorReverse = baseItemModuleEx.colorReverse
        baseItemModule.colorChannel = baseItemModuleEx.colorChannel
        baseItemModule.paperColorIndex = baseItemModuleEx.paperColorIndex
        when(baseItemModuleEx){
            is BarcodeItemModule -> transformBarcodeItemModule(baseItemModule, baseItemModuleEx)
            is QrCodeItemModule -> transformQrCodeItemModule(baseItemModule, baseItemModuleEx)
            is TableItemModule -> transformTableItemModule(baseItemModule, baseItemModuleEx)
            is PictureItemModule -> transformPictureItemModule(baseItemModule, baseItemModuleEx)
            is FlowItemModule -> {
                transformTextItemModule(baseItemModule, baseItemModuleEx)
                transformFlowItemModule(baseItemModule, baseItemModuleEx)
            }
            is TimeItemModule -> {
                transformTextItemModule(baseItemModule, baseItemModuleEx)
                transformTimeItemModule(baseItemModule, baseItemModuleEx)
            }
            is TextItemModule -> transformTextItemModule(baseItemModule, baseItemModuleEx)
            is RectItemModule -> transformRectItemModule(baseItemModule, baseItemModuleEx)
            is LineItemModule -> transformLineItemModule(baseItemModule, baseItemModuleEx)
        }
        return baseItemModule
    }

    private fun transformBarcodeItemModule(baseItemModule: BaseItemModule, barcodeItemModule: BarcodeItemModule){
        baseItemModule.value = barcodeItemModule.value
        baseItemModule.dataBind = barcodeItemModule.dataBind
        baseItemModule.fontSize = barcodeItemModule.fontSize
        baseItemModule.textPosition = barcodeItemModule.textPosition
        baseItemModule.codeType = barcodeItemModule.codeType
        baseItemModule.fieldName = barcodeItemModule.fieldName
        baseItemModule.textHeight = barcodeItemModule.textHeight
    }

    private fun transformQrCodeItemModule(baseItemModule: BaseItemModule, qrCodeItemModule: QrCodeItemModule){
        baseItemModule.value = qrCodeItemModule.value
        baseItemModule.dataBind = qrCodeItemModule.dataBind
        baseItemModule.codeType = qrCodeItemModule.codeType
        baseItemModule.correctLevel = qrCodeItemModule.correctLevel
        baseItemModule.liveCodeId = qrCodeItemModule.liveCodeId
        baseItemModule.isLive = qrCodeItemModule.isLive
        baseItemModule.isForm = qrCodeItemModule.isForm
        baseItemModule.formId = qrCodeItemModule.formId
    }

    private fun transformTableItemModule(baseItemModule: BaseItemModule, tableItemModule: TableItemModule){
        baseItemModule.row = tableItemModule.row
        baseItemModule.column = tableItemModule.column
        baseItemModule.rowHeight = any2Json(tableItemModule.rowHeight)
        baseItemModule.columnWidth = any2Json(tableItemModule.columnWidth)
        baseItemModule.lineWidth = tableItemModule.lineWidth
        baseItemModule.lineType = tableItemModule.lineType
        baseItemModule.cells = any2Json(tableItemModule.cells)
        baseItemModule.combineCells = any2Json(tableItemModule.combineCells)
        baseItemModule.lineColor = tableItemModule.lineColor
        baseItemModule.contentColor = tableItemModule.contentColor
        baseItemModule.lineColorChannel = tableItemModule.lineColorChannel
        baseItemModule.contentColorChannel = tableItemModule.contentColorChannel
    }

    private fun transformFlowItemModule(baseItemModule: BaseItemModule, flowItemModule: FlowItemModule){
        baseItemModule.prefix = flowItemModule.prefix
        baseItemModule.suffix = flowItemModule.suffix
        baseItemModule.startNumber = flowItemModule.startNumber
        baseItemModule.incrementValue = flowItemModule.incrementValue
        baseItemModule.fixValue = flowItemModule.fixValue
        baseItemModule.fixLength = flowItemModule.fixLength
        baseItemModule.boxStyle = flowItemModule.boxStyle
        baseItemModule.textStyle = any2Json(flowItemModule.textStyle)
    }

    private fun transformLineItemModule(baseItemModule: BaseItemModule, lineItemModule: LineItemModule){
        baseItemModule.lineType = lineItemModule.lineType
        baseItemModule.lineWidth = lineItemModule.lineWidth
        baseItemModule.dashwidth = any2Json(lineItemModule.dashwidth)
    }

    private fun transformPictureItemModule(baseItemModule: BaseItemModule, pictureItemModule: PictureItemModule){
        baseItemModule.imageUrl = pictureItemModule.imageUrl
        baseItemModule.localUrl = pictureItemModule.localUrl
        baseItemModule.imageProcessingType = pictureItemModule.imageProcessingType
        baseItemModule.imageProcessingValue = any2Json(pictureItemModule.imageProcessingValue)
        baseItemModule.materialId = pictureItemModule.materialId
        baseItemModule.materialType = pictureItemModule.materialType
        baseItemModule.isNinePatch = pictureItemModule.isNinePatch
        baseItemModule.isDefaultImage = pictureItemModule.isDefaultImage
        baseItemModule.ninePatchUrl = pictureItemModule.ninePatchUrl
        baseItemModule.ninePatchLocalUrl = pictureItemModule.ninePatchLocalUrl

    }

    private fun transformRectItemModule(baseItemModule: BaseItemModule, rectItemModule: RectItemModule){
        baseItemModule.lineWidth = rectItemModule.lineWidth
        baseItemModule.lineType = rectItemModule.lineType
        baseItemModule.graphType = rectItemModule.graphType
        baseItemModule.cornerRadius = rectItemModule.cornerRadius
        baseItemModule.dashwidth = any2Json(rectItemModule.dashwidth)
    }

    private fun transformTextItemModule(baseItemModule: BaseItemModule, textItemModule: TextItemModule){
        baseItemModule.textAlignHorizonral = textItemModule.textAlignHorizonral
        baseItemModule.textAlignVertical = textItemModule.textAlignVertical
        baseItemModule.lineMode = textItemModule.lineMode
        baseItemModule.value = textItemModule.value
        baseItemModule.dataBind = textItemModule.dataBind
        baseItemModule.wordSpacing = textItemModule.wordSpacing
        baseItemModule.letterSpacing = textItemModule.letterSpacing
        baseItemModule.lineSpacing = textItemModule.lineSpacing
        baseItemModule.fontCode = textItemModule.fontCode
        baseItemModule.fontFamily = textItemModule.fontFamily
        baseItemModule.fontStyle = any2Json(textItemModule.fontStyle)
        baseItemModule.fontSize = textItemModule.fontSize
        baseItemModule.fieldName = textItemModule.fieldName
        baseItemModule.isTitle = textItemModule.isTitle
        baseItemModule.contentTitle = textItemModule.contentTitle
        baseItemModule.delimiter = textItemModule.delimiter
        baseItemModule.lineBreakMode = textItemModule.lineBreakMode
        baseItemModule.typesettingMode = textItemModule.typesettingMode
        baseItemModule.typesettingParam = textItemModule.typesettingParam
        baseItemModule.boxStyle = textItemModule.boxStyle
        baseItemModule.textStyle = any2Json(textItemModule.textStyle)
        baseItemModule.textDirection = textItemModule.textDirection
    }

    private fun transformTimeItemModule(baseItemModule: BaseItemModule, timeItemModule: TimeItemModule){
        baseItemModule.dateFormat = timeItemModule.dateFormat
        baseItemModule.timeFormat = timeItemModule.timeFormat
        baseItemModule.dateIsRefresh = timeItemModule.dateIsRefresh
        baseItemModule.boxStyle = timeItemModule.boxStyle
        baseItemModule.textStyle = any2Json(timeItemModule.textStyle)
        baseItemModule.timeOffset=timeItemModule.timeOffset
        baseItemModule.associated=timeItemModule.associated
        baseItemModule.associateId=timeItemModule.associateId
        baseItemModule.validityPeriod=timeItemModule.validityPeriod
        baseItemModule.validityPeriodNew=timeItemModule.validityPeriodNew
        baseItemModule.validityPeriodUnit=timeItemModule.validityPeriodUnit
        baseItemModule.timeUnit=timeItemModule.timeUnit
        baseItemModule.contentTitle=timeItemModule.contentTitle
        baseItemModule.time = timeItemModule.time
    }
}
