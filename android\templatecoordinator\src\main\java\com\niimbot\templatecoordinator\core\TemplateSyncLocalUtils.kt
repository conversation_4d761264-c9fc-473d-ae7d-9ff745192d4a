package com.niimbot.templatecoordinator.core

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Bitmap
import android.util.Log
import androidx.fragment.app.Fragment
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.FileIOUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.print.draw.module.qrcode.FormService
import com.gengcon.print.draw.module.qrcode.LiveCodeService
import com.gengcon.print.draw.view.operate.menubar.detail.common.FontManagerHelper
import com.niimbot.appframework_library.common.module.eventbus.TemplateEvent
import com.niimbot.appframework_library.common.module.eventbus.TemplateEventType
import com.niimbot.appframework_library.common.module.eventbus.TemplateSyncData
import com.niimbot.appframework_library.common.module.font.FontLibBean
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.dialog.CustomDialog
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.utils.LoginUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSONObject
import com.niimbot.templatecoordinator.manager.TemplateManager
import com.niimbot.templatecoordinator.module.TemplateRequest
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.utiliylibray.util.FileUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.json2Any
import com.niimbot.utiliylibray.util.logE
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.LanguageUtil
import com.southcity.watermelon.listener.OnFragmentLifeCycleListener
import com.tencent.bugly.crashreport.CrashReport
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.base.LaboratoryActivity
import melon.south.com.baselibrary.local.module.PrintHistoryModule
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.local.util.TemplateUsedRecordUtils
import melon.south.com.baselibrary.module.CategoryInfo
import melon.south.com.baselibrary.module.PrintModule
import melon.south.com.baselibrary.module.SizeBean
import melon.south.com.baselibrary.module.TemplateExampleModuleNew
import melon.south.com.baselibrary.module.TemplateInfo
import melon.south.com.baselibrary.util.TimeUtils
import melon.south.com.baselibrary.util.showToast
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.io.FileOutputStream
import java.util.UUID
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 模板同步本地工具类
 */
object TemplateSyncLocalUtils {

    /**
     * 扫描打印改版后，全走离线逻辑
     */
    fun save(
        templateModule: TemplateModule,
        thumpBitmap: Bitmap,
        contentThumbBitmap: Bitmap?,
        isOtherSave: Boolean = false,
        isEtagTemplate: Boolean = false
    ) {
        when (templateModule.template_type) {
            TemplateModule.TEMPLATE_NEW -> createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            )

            TemplateModule.TEMPLATE_INDUSTRY -> createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            )

            TemplateModule.TEMPLATE_MY -> if (isOtherSave || templateModule.template_type == TemplateModule.CREATE) createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            ) else updateTemplate(templateModule, thumpBitmap, contentThumbBitmap, isEtagTemplate)

            TemplateModule.TEMPLATE_EXAMPLE_NEW -> createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            )

            TemplateModule.TEMPLATE_EXAMPLE_INDUSTRY -> createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            )

            TemplateModule.TEMPLATE_EXAMPLE_MY -> if (isOtherSave) createTemplate(
                templateModule,
                thumpBitmap,
                contentThumbBitmap,
                isEtagTemplate
            ) else updateTemplate(templateModule, thumpBitmap, contentThumbBitmap, isEtagTemplate)
        }
    }

    fun getTemplateDetailFromDB(
        id: String,
        isPersonalTemplate: Boolean,
        isShare: Boolean = false,
        needLoadFonts: Boolean = true,
        needPostEvent: Boolean = true,
        listener: (result: Boolean, templateModule: TemplateModule?, errorMsg: String?) -> Unit,
    ) {
        FlutterMethodInvokeManager.queryTemplateById(id) { isSuccess, templateJsonStr, errMsg ->
            GlobalScope.launch(Dispatchers.Main) {
                var localTemplate: TemplateModule? = null
                withContext(Dispatchers.IO) {
                    if (!templateJsonStr.isNullOrEmpty()) {
                        var resultTemplateModuleLocal =
                            TemplateModuleLocal.fromJson(templateJsonStr)
                        localTemplate = TemplateModuleTransform.templateModuleLocalToTemplateModule(
                            resultTemplateModuleLocal!!
                        )
                    }
                    if (null != localTemplate) {
                        listener.invoke(true, localTemplate, null)
                    } else {
                        listener.invoke(false, null, "app01139")
                    }
                }
            }
        }
//        val localTemplate = TemplateDbUtils.queryTemplate(id)
//        if (null != localTemplate) {
//            listener.invoke(true, localTemplate, null)
//        } else {
//            listener.invoke(false, null, "app01139")
//        }
    }

    /**
     * 获取个人模板或云模板详情
     * isShare: 是否来自分享，分享过来的模板强制获取详情
     */
    fun getTemplateDetails(
        id: String,
        isPersonalTemplate: Boolean,
        isShare: Boolean = false,
        needLoadFonts: Boolean = true,
        needPostEvent: Boolean = true,
        needOldURL: Boolean = false,
        isFolderShare: Boolean = false,
        ignoreBackground: Boolean = false,
        listener: (result: Boolean, templateModule: TemplateModule?, errorMsg: String?) -> Unit,
    ) {
        FlutterMethodInvokeManager.getTemplateDetailWithParams(templateId = id,isPersonalTemplate= isPersonalTemplate,isShare=isShare,isFolderShare=isFolderShare){
            isSuccess,templateJsonStr,errMsg ->
            GlobalScope.launch(Dispatchers.Main) {
                var resultTemplateModule: TemplateModule? = null
                withContext(Dispatchers.IO) {
                    if(!templateJsonStr.isNullOrEmpty()){
                        var resultTemplateModuleLocal = TemplateModuleLocal.fromJson(templateJsonStr)
                        resultTemplateModule = TemplateModuleTransform.templateModuleLocalToTemplateModule(resultTemplateModuleLocal!!)
                        if (isPersonalTemplate || resultTemplateModule!!.user_id != LoginDataEnum.id) {
                            if (needPostEvent) {
                                sendUpdatePersonalTemplateMessage(resultTemplateModule!!)
                            }
                        } else {
                            sendUpdateCloudTemplateMessage(resultTemplateModule!!)
                        }
                        if (needLoadFonts && isConnected()) { //是否需要下载字体
//                            val fontCodes = resultTemplateModuleLocal.checkNotDownloadFontCodes()
//                            if (fontCodes.isNotEmpty()) {
//                                FontUtils.downloadFonts(fontCodes)
//                            }
                            downloadTemplateFonts(ActivityUtils.getTopActivity(),resultTemplateModuleLocal)
                        }
                    }
                }
                listener.invoke(isSuccess,resultTemplateModule,errMsg)
            }
        }


//        val localTemplate = DaoFactory.templateModuleDao().queryBuilder()
//            .where(TemplateModuleDao.Properties.Id.eq(id))
//            .where(TemplateModuleDao.Properties.Local_type.notEq(TemplateModule.DELETE)).unique()
//        if (!isConnected() || ((null != localTemplate && localTemplate.isLocal) && !isFolderShare)) {
//            if (localTemplate != null && localTemplate.local_type != TemplateModule.DEFAULT && localTemplate.canEdit(
//                    ignoreBackground
//                ) && (!localTemplate.commodityTemplate || localTemplate.template_type == 2)
//            ) {
//                loadLiveCode(localTemplate.liveCodeIds)
//                loadForm(localTemplate.formIds)
//                listener.invoke(true, localTemplate, null)
//            } else {
//                listener.invoke(false, null, "app01139")
//            }
//            return
//        }
//        val responseListener = object : JCTResponseListener<TemplateModule> {
//            override fun onSuccess(body: TemplateModule) {
//                GlobalScope.launch(Dispatchers.Main) {
//                    val template = withContext(Dispatchers.IO) {
//                        var queryTemplate = if (null != localTemplate && body.id.isNullOrBlank()) {
//                            localTemplate
//                        } else {
//                            val templateModule = fixDuplicateElementId(body)
////                            val templateModule = body
//                            if (!isPersonalTemplate || templateModule.user_id != LoginDataEnum.id) {
//                                templateModule.user_id = -1
//                            }
//                            templateModule.cloudTemplateId = localTemplate?.cloudTemplateId
//                            writeModuleImg(templateModule, !isShare)
//                            if (!isShare) {
//                                DaoFactory.templateModuleDao().insertOrReplace(templateModule)
//                            }
//                            if (!templateModule.canEdit()) {
//                                writeModuleImg(templateModule, !isShare)
//                            }
//                            if (templateModule.canEdit()) {
//                                templateModule.local_type = TemplateModule.SYNC
//                            }
//                            if (isPersonalTemplate || templateModule.user_id != LoginDataEnum.id) {
//                                if (needPostEvent) {
//                                    sendUpdatePersonalTemplateMessage(templateModule)
//                                }
//                            } else {
//                                sendUpdateCloudTemplateMessage(templateModule)
//                            }
//                            templateModule
//                        }
//                        if (needLoadFonts && isConnected()) { //是否需要下载字体
//                            val templateModuleLocal = queryTemplate.toTemplateModuleLocal()
//                            val fontCodes = templateModuleLocal.checkNotDownloadFontCodes()
//                            if (fontCodes.isNotEmpty()) {
//                                FontUtils.downloadFonts(fontCodes)
//                            }
//                        }
//                        queryTemplate
//                    }
//                    loadLiveCode(template.liveCodeIds)
//                    loadForm(template.formIds)
//                    listener.invoke(true, template, null)
////                        if(template.canEdit()) {
////                            LogUtils.eTag("toDraw", "load templateDetail success")
////                            listener.invoke(true, template, null)
////                        }
////                        else{
////                            listener.invoke(false, null , "app01160")
////                        }
//                }
//            }
//
//            override fun onError(message: String) {
//                if (message.isNotEmpty()) {
//                    listener.invoke(false, null, message)
//                } else {
//                    listener.invoke(false, null, "app01160")
//                }
//            }
//        }
//
//        if (isFolderShare) {
//            TemplateRequest.getFolderShareTemplateDetail(
//                id,
//                responseListener
//            )
//        } else {
//            if (needOldURL || null == localTemplate || !localTemplate.canEdit() || isShare || localTemplate.commodityTemplate || localTemplate.template_type == 2) {
//                TemplateRequest.getPersonalTemplateById(id, responseListener, needOldURL)
//            } else {
//                TemplateRequest.getTemplateDetailWithIncrement(
//                    id,
//                    localTemplate.update_time,
//                    responseListener
//                )
//            }
//
//        }
    }

    /**
     * 下载模版所需要的资源，包括图片，表单，活码，字体
     */
    suspend fun downloadTemplateResource(
        mContext: Activity,
        templateModule: TemplateModule?,
        listener: (result: Boolean,resultTemplateModule: TemplateModule?) -> Unit,
    ) {
//        templateModule?.let {
//            writeModuleImg(templateModule, false, needProcessThumb = false)
//            loadLiveCode(templateModule.liveCodeIds)
//            loadForm(templateModule.formIds)
//            unDownloadList?.clear()
//            checkUnDownloadFonts(mContext, templateModule) {
//                listener.invoke(true)
//            }
//        }
        if(!NetworkUtils.isConnected()){
            listener.invoke(true,templateModule)
            return
        }
        templateModule?.let {
           val moduleLocal =  it.toTemplateModuleLocal()
            val templateStr = any2Json(moduleLocal)
            FlutterMethodInvokeManager.downloadTemplateRes(templateStr){ result->
                result?.let {
                    val moduleLocal2 = TemplateModuleLocal.fromJson(result)
                    val templateModule2 =
                        TemplateModuleTransform.templateModuleLocalToTemplateModule(moduleLocal2!!)
                    unDownloadList?.clear()
                    checkUnDownloadFonts(mContext, templateModule2) {
                        listener.invoke(true,templateModule2)
                    }
                }
            }

        }
    }


    suspend fun downloadTemplateFonts(
        mContext: Activity,
        templateModuleLocal: TemplateModuleLocal?,
    ):Boolean {
        return suspendCoroutine { callback ->
            if (templateModuleLocal == null) {
                callback.resume(false)  // 立即返回失败
                return@suspendCoroutine
            }
            val localTemplate =
                TemplateModuleTransform.templateModuleLocalToTemplateModule(templateModuleLocal)
            unDownloadList?.clear()
            checkUnDownloadFonts(mContext, localTemplate) {
                callback.resume(true)
            }
        }

    }

    private val elementIds = arrayListOf<String>()
    private fun fixDuplicateElementId(body: TemplateModule): TemplateModule {
        elementIds.clear()
        val templateModuleLocal = body.toTemplateModuleLocal()
        templateModuleLocal.elements.forEach { element ->
            if (elementIds.contains(element.id)) {
                element.id = UUID.randomUUID().toString().replace("-", "")
            } else {
                elementIds.add(element.id)
            }
        }
        return TemplateModuleTransform.templateModuleLocalToTemplateModule(templateModuleLocal)
    }

    /**
     * 扫码打印获取商品信息（可能包含关联的个人模板信息）
     */
    fun getGoodsInfoByScanCode(
        goodsCode: String,
        needOldURL: Boolean = false,
        listener: (result: Boolean, templateExampleModuleNew: TemplateExampleModuleNew?, errorMsg: String?) -> Unit
    ) {

        val enterNewDrawboard = {
            TemplateRequest.getNewGoodsInfoByScanCode(
                goodsCode,
                object : JCTResponseListener<TemplateExampleModuleNew> {
                    override fun onSuccess(body: TemplateExampleModuleNew) {
                        GlobalScope.launch(Dispatchers.Main) {
                            var templateModule = body.template_info
                            templateModule?.page_total = 1
                            templateModule?.dataSourceBindInfoJson = "{\"page\":1,\"total\":1}"
                            if (templateModule != null) {
                                templateModule.commodityModule = null
                                withContext(Dispatchers.IO) {
                                    templateModule = writeModuleImg(templateModule!!, false)
                                }
                                templateModule!!.local_type = TemplateModule.SYNC
                            }
                            listener.invoke(true, body, null)
                        }
                    }

                    override fun onError(message: String) {
                        listener.invoke(false, null, message)
                    }
                })
        }

        val enterOldDrawboard = {
            TemplateRequest.getGoodsInfoByScanCode(
                goodsCode,
                object : JCTResponseListener<TemplateExampleModuleNew> {
                    override fun onSuccess(body: TemplateExampleModuleNew) {
                        GlobalScope.launch(Dispatchers.Main) {
                            val templateModule = body.template_info

                            if (templateModule != null) {
                                templateModule.commodityModule = null
                                withContext(Dispatchers.IO) {
                                    writeModuleImg(templateModule, false)
                                }
                                templateModule.local_type = TemplateModule.SYNC
                            }
                            listener.invoke(true, body, null)
                        }
                    }

                    override fun onError(message: String) {
                        listener.invoke(false, null, message)
                    }
                })
        }

        if (LaboratoryActivity.getDrawboardEntrance() == LaboratoryActivity.DRAWBOARD_NEW) {
            enterNewDrawboard.invoke()
            return
        }
        //直接进入老画板
        if (LaboratoryActivity.getDrawboardEntrance() == LaboratoryActivity.DRAWBOARD_OLD) {
            enterOldDrawboard.invoke()
            return
        }

        if (needOldURL) enterOldDrawboard.invoke() else enterNewDrawboard.invoke()
//        ABTestUtils.startDrawingBoard { canEnterNewBoard ->
//            if (!needOldURL && (canEnterNewBoard || LaboratoryActivity.getDrawboardEntrance() == LaboratoryActivity.DRAWBOARD_NEW)) {
//                enterNewDrawboard.invoke()
//            } else {
//                enterOldDrawboard.invoke()
//            }
//        }


    }

    /**
     * 从数据库根据onecode查询标签信息
     */
    fun queryRfidTemplateByScanCode(
        oneCode: String,
        listener: (result: Boolean, templateModule: TemplateModule?, errorMsg: String?) -> Unit
    ) {

        FlutterMethodInvokeManager.queryRfidTemplateByScanCode(
            oneCode,
        ) { isSuccess, templateJsonStr, errMsg ->
            GlobalScope.launch(Dispatchers.Main) {
                var resultTemplateModule: TemplateModule? = null
                withContext(Dispatchers.IO) {
                    if (!templateJsonStr.isNullOrEmpty()) {
                        var resultTemplateModuleLocal =
                            TemplateModuleLocal.fromJson(templateJsonStr)
                        resultTemplateModule =
                            TemplateModuleTransform.templateModuleLocalToTemplateModule(
                                resultTemplateModuleLocal!!
                            )
                    }
                }
                listener.invoke(isSuccess, resultTemplateModule, errMsg)
            }
        }
    }

    /**
     * 扫码取模获取云模板信息
     */
    fun getCloudTemplateByScanCode(
        oneCode: String,
        getDetail: Boolean,
        fromLocal: Boolean = false,
        insertDB: Boolean = false,
        listener: (result: Boolean, templateModule: TemplateModule?, errorMsg: String?) -> Unit
    ) {
        LogUtils.iTag(
            "ConnectRfidRead", "TemplateSyncLocalUtils getCloudTemplateByScanCode, " +
                "oneCode = $oneCode, getDetail = $getDetail, fromLocal = $fromLocal, insertDB = $insertDB"
        )

        FlutterMethodInvokeManager.getCloudTemplateByScanCode(oneCode,insertDB){
                isSuccess,templateJsonStr,errMsg ->
            LogUtils.iTag(
                "ConnectRfidRead", "TemplateSyncLocalUtils getCloudTemplateByScanCode, templateJson=$templateJsonStr")
            GlobalScope.launch(Dispatchers.Main) {
                var resultTemplateModule: TemplateModule? = null
                withContext(Dispatchers.IO) {
                    if(!templateJsonStr.isNullOrEmpty()){
                        var resultTemplateModuleLocal = TemplateModuleLocal.fromJson(templateJsonStr)
                        resultTemplateModule = TemplateModuleTransform.templateModuleLocalToTemplateModule(resultTemplateModuleLocal!!)
                    }
                }
                listener.invoke(isSuccess,resultTemplateModule,errMsg)
            }
        }

//        GlobalScope.launch(Dispatchers.Main) {
//            val result =
//                withContext(Dispatchers.IO) { getCloudTemplateByScanCodeFromLocal(oneCode) }
//            if (result.isNotEmpty() && result.first().canEdit() && (fromLocal || !isConnected())) {
//                listener.invoke(true, result.first(), null)
//                return@launch
//            }
//            TemplateRequest.getCloudTemplateByScanCode(
//                oneCode,
//                object : JCTResponseListener<TemplateModuleR?> {
//                    override fun onSuccess(body: TemplateModuleR?) {
//                        if (body != null) {
//                            val templateModuleLocal = TemplateModuleR.convert(body)
//                            val templateModule =
//                                TemplateModuleTransform.templateModuleLocalToTemplateModule(
//                                    templateModuleLocal
//                                )
//                            if (getDetail) {
//                                GlobalScope.launch(Dispatchers.Main) {
//                                    var tmp = withContext(Dispatchers.IO) {
//                                        var result = TemplateDbUtils.queryTemplate(templateModule.id)
//                                        if (null == result || !result.canEdit() || templateModule.upToDate(
//                                                result.update_time
//                                            )
//                                        ) {
//                                            writeModuleImg(templateModule, insertDB)
//                                            result = templateModule
//                                        }
//                                        result.name = templateModule.name
//                                        result
//                                    }
//
//                                    listener.invoke(true, tmp, null)
//                                }
//                            } else {
//                                listener.invoke(true, templateModule, null)
//                            }
//                        } else {
//                            listener.invoke(false, null, "app00321")
//                        }
//                    }
//
//                    override fun onError(message: String) {
//                        listener.invoke(false, null, message)
//                    }
//                })
//        }
    }


    /**
     * 扫码取模获取线缆信息
     */
    fun getCableTemplateByLabelId(
        oneCode: String,
        labelId: String,
        getDetail: Boolean = true,
        fromLocal: Boolean = false,
        insertDB: Boolean = true,
        listener: (result: Boolean, templateModule: TemplateModule?, errorMsg: String?) -> Unit
    ) {
        if (oneCode.isNullOrEmpty() && labelId.isNullOrEmpty()) {
            listener.invoke(false, null, null)
            return
        }
        FlutterMethodInvokeManager.getCloudTemplateByScanCodeOrLabelId(oneCode,labelId,insertDB){
                isSuccess,templateJsonStr,errMsg ->
            GlobalScope.launch(Dispatchers.Main) {
                var resultTemplateModule: TemplateModule? = null
                withContext(Dispatchers.IO) {
                    if(!templateJsonStr.isNullOrEmpty()){
                        var resultTemplateModuleLocal = TemplateModuleLocal.fromJson(templateJsonStr)
                        resultTemplateModule = TemplateModuleTransform.templateModuleLocalToTemplateModule(resultTemplateModuleLocal!!)
                    }
                }
                listener.invoke(isSuccess,resultTemplateModule,errMsg)
            }
        }
//        var code = if (labelId.isNullOrEmpty() || labelId == "0") oneCode else labelId
//        GlobalScope.launch(Dispatchers.Main) {
//            val result =
//                withContext(Dispatchers.IO) { getCloudTemplateByScanCodeFromLocal(code) }
//            if (result.isNotEmpty() && result.first().canEdit() && (fromLocal || !isConnected())) {
//                listener.invoke(true, result.first(), null)
//                return@launch
//            }
//
//
//            val responseResult = { body: TemplateModuleR? ->
//                if (body != null) {0
//                    val templateModuleLocal = TemplateModuleR.convert(body)
//                    val templateModule =
//                        TemplateModuleTransform.templateModuleLocalToTemplateModule(
//                            templateModuleLocal
//                        )
//                    if (getDetail) {
//                        GlobalScope.launch(Dispatchers.Main) {
//                            var tmp = withContext(Dispatchers.IO) {
//                                writeModuleImg(templateModule, insertDB)
//                                var result = TemplateDbUtils.queryTemplate(templateModule.id)
//                                if (null == result || !result.canEdit() || templateModule.upToDate(
//                                        result.update_time
//                                    )
//                                ) {
//                                    result = templateModule
//                                }
//                                result.name = templateModule.name
//                                result
//                            }
//
//                            listener.invoke(true, tmp, null)
//                        }
//                    } else {
//                        listener.invoke(true, templateModule, null)
//                    }
//                } else {
//                    listener.invoke(false, null, "app00321")
//                }
//            }
//
//            if (labelId.isNullOrEmpty() || labelId == "0") {
//                TemplateRequest.getCloudTemplateByScanCode(
//                    code,
//                    object : JCTResponseListener<TemplateModuleR?> {
//                        override fun onSuccess(body: TemplateModuleR?) {
//                            responseResult.invoke(body)
//                        }
//
//                        override fun onError(message: String) {
//                            listener.invoke(false, null, message)
//                        }
//                    })
//            } else {
//                TemplateRequest.getLabelIdTemplateByScanCode(
//                    code,
//                    object : JCTResponseListener<TemplateModuleR?> {
//                        override fun onSuccess(body: TemplateModuleR?) {
//                            responseResult.invoke(body)
//                        }
//
//                        override fun onError(message: String) {
//                            listener.invoke(false, null, message)
//                        }
//                    })
//            }
//
//        }
    }


    /**
     *标签资源缓存本地
     */
    suspend fun moduleToLocal(
        module: TemplateModuleLocal?,
        isUpdateDatabase: Boolean,
        clearElements: Boolean = true
    ): TemplateModuleLocal? {
        if (null == module) return null
//        val dbTemplate = TemplateDbUtils.queryTemplate(module.id)
//        val result = if (null != dbTemplate) {
//            dbTemplate.add_time = TimeUtil.getLocalTimeString("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
//            dbTemplate.update_time = dbTemplate.add_time
//            if (!dbTemplate.canEdit()) {
//                writeModuleImg(dbTemplate, true)
//            } else {
////                DaoFactory.templateModuleDao().saveInTx(dbTemplate)
//                TemplateDbUtils.insertOrUpdateTemplate(dbTemplate)
//            }
//            dbTemplate
//        } else {
//            val drawData = TemplateModuleTransform.templateModuleLocalToTemplateModule(module)
//            drawData.add_time = TimeUtil.getLocalTimeString("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
//            drawData.update_time = drawData.add_time
//            writeModuleImg(drawData, isUpdateDatabase)
////            DaoFactory.templateModuleDao().saveInTx(drawData)
//            TemplateDbUtils.insertOrUpdateTemplate(drawData)
//            drawData
//        }
//        val templateModuleLocal = result?.toTemplateModuleLocal()
//        if (clearElements && templateModuleLocal?.elements?.isNotEmpty() == true) {
//            templateModuleLocal?.elements?.clear()
//        }
        var result: TemplateModuleLocal? =null
        val templateJson = FlutterMethodInvokeManager.getTemplateDetail(module.id,needUpdateDb = true)
        templateJson?.let {
            result = TemplateModuleLocal.fromJson(it)
        }
        if (clearElements && result?.elements?.isNotEmpty() == true) {
            result?.elements?.clear()
        }

        return result
    }

//    private fun getCloudTemplateByScanCodeFromLocal(oneCode: String): MutableList<TemplateModule> {
//        return DaoFactory.templateModuleDao()
//            .queryBuilder()
//            .where(TemplateModuleDao.Properties.Template_type.eq(TemplateModule.TEMPLATE_INDUSTRY))
//            .where(TemplateModuleDao.Properties.Local_type.notEq(TemplateModule.DELETE))
//            .whereOr(
//                TemplateModuleDao.Properties.One_code.eq(oneCode),
//                TemplateModuleDao.Properties.Spared_code.eq(oneCode),
//                TemplateModuleDao.Properties.Virtual_bar_code.eq(oneCode),
//                TemplateModuleDao.Properties.Amazon_code.eq(oneCode),
//                TemplateModuleDao.Properties.Amazon_code_wuhan.eq(oneCode),
//                TemplateModuleDao.Properties.BarcodeCategoryMap.like("%$oneCode%")
//            )
//            .list()
//        return TemplateDbUtils.queryCloudTemplateByScanCode(oneCode)
//    }

//    fun getLabelTemplate(oneCode: String): MutableList<TemplateModule> {
//        return DaoFactory.templateModuleDao()
//            .queryBuilder()
//            .where(TemplateModuleDao.Properties.Template_type.eq(TemplateModule.TEMPLATE_INDUSTRY))
//            .where(TemplateModuleDao.Properties.Template_class.eq(TemplateModule.TAG_LABEL))
//            .where(TemplateModuleDao.Properties.User_id.eq(-1))
//            .whereOr(
//                TemplateModuleDao.Properties.One_code.eq(oneCode),
//                TemplateModuleDao.Properties.Spared_code.eq(oneCode),
//                TemplateModuleDao.Properties.Virtual_bar_code.eq(oneCode),
//                TemplateModuleDao.Properties.Amazon_code.eq(oneCode),
//                TemplateModuleDao.Properties.Amazon_code_wuhan.eq(oneCode),
//                TemplateModuleDao.Properties.BarcodeCategoryMap.like("%$oneCode%")
//            )
//            .list()
//        return TemplateDbUtils.queryRfidLabelTemplate(oneCode)
//    }

    /**
     * 删除个人模板
     */
    fun deleteTemplate(id: String) {

//        GlobalScope.launch(Dispatchers.Main) {
//            val templateModule = DaoFactory.templateModuleDao().queryBuilder()
//                .where(TemplateModuleDao.Properties.Id.eq(id)).unique()
//            if (templateModule == null) {
//                deleteTemplateToService(id)
//            } else {
//                withContext(Dispatchers.IO) {
//                    val thumbFile = File(templateModule.local_thumb)
//                    if (thumbFile.exists()) {
//                        thumbFile.delete()
//                    }
//                    val printModule = PrintModule.fromJson(templateModule.json)
//                    printModule?.apply {
//                        this.localBackground.forEach {
//                            val backgroundFile = File(it)
//                            if (backgroundFile.exists()) {
//                                backgroundFile.delete()
//                            }
//                        }
//                        this.actionModel.models.forEach {
//                            if (it.localUrl.isNotEmpty()) {
//                                val pictureFile = File(it.localUrl)
//                                pictureFile.delete()
//                            }
//                        }
//                    }
//                }
//                val templateEvent = TemplateEvent()
//                templateEvent.eventType = TemplateEventType.DELETE_PERSONAL_TEMPLATE_SYNC
//                val templateSyncData = TemplateSyncData()
//                templateSyncData.niimbotTemplate = templateModule.toTemplateModuleLocal()
//                templateEvent.eventData = any2Json(templateSyncData)
//                if (templateModule.local_type == TemplateModule.CREATE) {
//                    DaoFactory.templateModuleDao().deleteInTx(templateModule)
//                    EventBus.getDefault().post(templateEvent)
//                    showToast("app01188")
//                } else {
//                    templateModule.local_type = TemplateModule.DELETE
//                    templateModule.update_time = TimeUtils.millis2String(System.currentTimeMillis())
//                    DaoFactory.templateModuleDao().saveInTx(templateModule)
////                    DaoFactory.templateModuleDao().deleteInTx(templateModule)
//                    EventBus.getDefault().post(templateEvent)
//                    deleteTemplateToService(id)
//                    showToast("app01188")
//                }
//            }
//            val jsonObject = JSONObject()
//            jsonObject["action"] = "myTemplateRefresh"
//            jsonObject["myTemplateRefresh"] = ""
//            EventBus.getDefault().post(any2Json(jsonObject))
////            val templateEvent = TemplateEvent()
////            templateEvent.eventType = TemplateEventType.UPDATE_HOME_TEMPLATE_SYNC
////            EventBus.getDefault().post(templateEvent)
//        }
    }



    fun getRfidServiceDetails(
        code: String,
        listener: (OnFragmentLifeCycleListener<TemplateModule>)? = null
    ) {
        TemplateRequest.getRfidCloudTemplate(code, object : JCTResponseListener<TemplateModule> {
            override fun onSuccess(body: TemplateModule) {
                GlobalScope.launch(Dispatchers.Main) {
                    var result: TemplateModule
                    withContext(Dispatchers.IO) {
                        result = writeModuleImg(body, true)
                    }
                    listener?.callBack(result)
                }
            }

            override fun onError(message: String) {
                val errorObj = TemplateModule().apply {
                    one_code = "-1"
                }
                listener?.callBack(errorObj)
            }
        })
    }

    private fun createTemplate(
        templateModule: TemplateModule,
        thumpBitmap: Bitmap,
        contentThumbBitmap: Bitmap?,
        isEtagTemplate: Boolean = false
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            val newTemplate = templateModule.copy() ?: throw NullPointerException("")
            newTemplate.sql_id = null
//            newTemplate.id = "create_${System.currentTimeMillis()}"
            newTemplate.id = System.currentTimeMillis().toString()
            newTemplate.local_type = TemplateModule.CREATE
            if (templateModule.template_type == TemplateModule.TEMPLATE_NEW || templateModule.template_type == TemplateModule.TEMPLATE_INDUSTRY) {
                newTemplate.template_type = TemplateModule.TEMPLATE_MY
            } else if (templateModule.template_type == TemplateModule.TEMPLATE_EXAMPLE_NEW || templateModule.template_type == TemplateModule.TEMPLATE_EXAMPLE_INDUSTRY) {
                newTemplate.template_type = TemplateModule.TEMPLATE_EXAMPLE_MY
            }
            newTemplate.user_id = com.niimbot.baselibrary.user.LoginDataEnum.id
            val times = TimeUtils.millis2String(System.currentTimeMillis())
            newTemplate.add_time = times
            newTemplate.update_time = times
            newTemplate.local_thumb = ""
            newTemplate.thumb = ""

            saveThumb(newTemplate, thumpBitmap)
            if (contentThumbBitmap != null) {
                saveContentThumb(newTemplate, contentThumbBitmap)
            }
            saveBackgroundAndElement(newTemplate, templateModule.json)
            //创建时清理用于市场运营的字段
            newTemplate.isEdited = 0
            if (isEtagTemplate) {

            } /*else {
                DaoFactory.templateModuleDao().insertOrReplaceInTx(newTemplate).let {
                    val printJson = JSONObject()
                    printJson["action"] = "savePrintUpdateCanvasData"
                    printJson["savePrintUpdateCanvasData"] = ""
                    printJson["saveSuccessData"] = any2Json(newTemplate.toTemplateModuleLocal())
                    EventBus.getDefault().post(any2Json(printJson))
                }
            }*/
            //通知画板界面更新创建的模板信息
            logE("createTemplate 2", "home page ${templateModule.id}")
            sendSavePersonalTemplateMessage(newTemplate)
/*            if (!isEtagTemplate) {
                //通知首页更新创建的模板信息
                val templateEvent = TemplateEvent()
                templateEvent.eventType = TemplateEventType.CREATE_PERSONAL_TEMPLATE_SYNC
                val templateSyncData = TemplateSyncData()
                templateSyncData.niimbotTemplate = newTemplate.toTemplateModuleLocal()
                templateEvent.eventData = any2Json(templateSyncData)
                EventBus.getDefault().post(templateEvent)
            }*/
            if (isConnected() && LoginUtils.isLogin()) {
                createTemplateToService(newTemplate, isEtagTemplate = isEtagTemplate)
            }
//            showToast("app00349")
        }
    }

    // 已同步的模板<旧id， 新id>
    private val hasSyncedIds = HashMap<String, String>()

    /**
     * 创建模板上传服务器
     */
    private fun createTemplateToService(
        templateModule: TemplateModule,
        isEtagTemplate: Boolean = false,
    ) {
        //已经同步过则跳过，防止重复创建模版
        if (hasSyncedIds.contains(templateModule.id)) {
//            callback?.onResult(templateModule.id)
            return
        }
        if (!syncTemplateIdList.contains(templateModule.id)) {
            syncTemplateIdList.add(templateModule.id)
        }
        GlobalScope.launch(Dispatchers.Main) {
            // 2021/4/2 Ice_Liu 新建模板时，不上传背景图片，使用云标签的背景地址，避免重复上传，优化创建耗时
            var isUpdateDatabase = !isEtagTemplate
            val uploadImageResult =
                UploadImageUtils.handleUploadTemplateImages(
                    templateModule,
                    false,
                    isUpdateDatabase = isUpdateDatabase
                )
//            if(uploadImageResult) {
            val templateModuleLocal = templateModule.toTemplateModuleLocal()
            val templateModuleR =
                TemplateModuleLocal.convert(templateModuleLocal)
            TemplateRequest.createPersonalTemplate(
                templateModuleR,
                isEtagTemplate = isEtagTemplate,
                object : JCTResponseListener<Long> {
                    override fun onSuccess(body: Long) {
                        if (body <= 0) {
                            LogUtils.e("新建模板上传服务器失败，id <= 0")
                            CrashReport.postCatchedException(Throwable("新建模板上传服务器失败，id <= 0"))
                            return
                        }
                        if (isEtagTemplate) {
                            //保存成功，缓存电子价签模板
                            val oldId = templateModuleLocal.id
                            templateModuleLocal.id = body.toString()
                            val content = any2Json(templateModuleLocal)
                            EtagTemplateFileUtils.saveEtagTemplate(content, templateModuleLocal.id)
                            val json = JSONObject()
                            json["action"] = "saveUpdateCanvasData"
                            json["saveUpdateCanvasData"] = ""
                            json["saveSuccessData"] = any2Json(templateModuleLocal)
                            json["oldId"] = oldId
                            EventBus.getDefault().post(any2Json(json))
                            return
                        }
//                        DaoFactory.templateModuleDao().queryBuilder()
//                            .where(TemplateModuleDao.Properties.Id.eq(templateModule.id)).unique()
//                            ?.apply {
//                                val oldId = this.id
//                                this.local_type = TemplateModule.SYNC
//                                this.id = body.toString()
//                                hasSyncedIds[oldId] = this.id
//                                try {
//                                    DaoFactory.templateModuleDao().saveInTx(this)
//                                } catch (e: Exception) {
//                                    e.printStackTrace()
//                                    CrashReport.postCatchedException(Throwable("新建模板上传服务器返回异常，oldId:$oldId, serverId: $body"))
//                                }
//                                val json = JSONObject()
//                                json["action"] = "saveUpdateCanvasData"
//                                json["saveUpdateCanvasData"] = ""
//                                json["saveSuccessData"] = any2Json(this.toTemplateModuleLocal())
//                                json["oldId"] = oldId
//                                EventBus.getDefault().post(any2Json(json))
//
//                                val printJson = JSONObject()
//                                printJson["action"] = "savePrintUpdateCanvasData"
//                                printJson["savePrintUpdateCanvasData"] = ""
//                                printJson["saveSuccessData"] =
//                                    any2Json(this.toTemplateModuleLocal())
//                                EventBus.getDefault().post(any2Json(printJson))
//
//                                sendUpdatePersonalTemplateMessage(this, oldId)
//                                val jsonObject = JSONObject()
//                                jsonObject["action"] = "myTemplateRefresh"
//                                jsonObject["myTemplateRefresh"] = ""
//                                EventBus.getDefault().post(any2Json(jsonObject))
//                                val templateEvent = TemplateEvent()
//                                templateEvent.eventType =
//                                    TemplateEventType.UPDATE_HOME_TEMPLATE_SYNC
//                                EventBus.getDefault().post(templateEvent)
//                                callback?.onResult(body.toString())
////                                if (this.id == this.cloudTemplateId) {
////                                    BuriedHelper.trackEvent(
////                                        "click", "025_071_100", hashMapOf(
////                                            Pair("b_name", "保存"),
////                                            Pair("temp_id", this.id)
////                                        )
////                                    )
////                                } else {
////                                    BuriedHelper.trackEvent(
////                                        "click", "025_071_100", hashMapOf(
////                                            Pair("b_name", "保存"),
////                                            Pair("temp_id", this.id),
////                                            Pair("industry_temp_id", this.cloudTemplateId)
////                                        )
////                                    )
////                                }
//                            }
//                        if (syncTemplateIdList.contains(templateModule.id)) {
//                            syncTemplateIdList.remove(templateModule.id)
//                        }

                    }

                    override fun onError(message: String) {
//                        if (syncTemplateIdList.contains(templateModule.id)) {
//                            syncTemplateIdList.remove(templateModule.id)
//                        }
//                        sendSavePersonalTemplateFailed()
                        if (isEtagTemplate) {
                            BuriedHelper.trackEvent(
                                "show", "109_270", hashMapOf()
                            )
                        }
                    }
                })
//            }
//            else{
//                if(syncTemplateIdList.contains(templateModule.id)) {
//                    syncTemplateIdList.remove(templateModule.id)
//                }
//                sendSavePersonalTemplateFailed()
//            }
        }
    }

    private fun updateTemplate(
        templateModule: TemplateModule,
        thumpBitmap: Bitmap,
        contentThumbBitmap: Bitmap?,
        isEtagTemplate: Boolean = false
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            if (templateModule.local_type != TemplateModule.CREATE) {
                templateModule.local_type = TemplateModule.UPDATE
            }
            //用于市场运营的字段
            if (templateModule.isEdited > 0) {
                templateModule.isEdited = 2
            }
            templateModule.update_time = TimeUtils.millis2String(System.currentTimeMillis())
            templateModule.user_id = com.niimbot.baselibrary.user.LoginDataEnum.id
//            templateModule.thumb = ""
            saveThumb(templateModule, thumpBitmap)
            if (contentThumbBitmap != null) {
                saveContentThumb(templateModule, contentThumbBitmap)
            }
            if (isEtagTemplate) {

            } /*else {
                // DaoFactory.templateModuleDao().insertOrReplaceInTx(templateModule)

                DaoFactory.templateModuleDao().insertOrReplaceInTx(templateModule).let {
                    val printJson = JSONObject()
                    printJson["action"] = "savePrintUpdateCanvasData"
                    printJson["savePrintUpdateCanvasData"] = ""
                    printJson["saveSuccessData"] = any2Json(templateModule.toTemplateModuleLocal())
                    EventBus.getDefault().post(any2Json(printJson))
                }
            }*/
            val logObject = JSONObject()
            logObject["action"] = "writeLogInfoToFile"
            logObject["moudle"] = "saveTemplate"
            logObject["event"] = "template_change"
            logObject["data"] = mapOf(
                "templateId" to templateModule.id,
                "updateTime" to templateModule.update_time,
            )
            EventBus.getDefault().post(any2Json(logObject))
            logI("updateTemplate", "home page ${templateModule.id}")
            sendSavePersonalTemplateMessage(templateModule)
            if (!isEtagTemplate) {
                val templateEvent = TemplateEvent()
                templateEvent.eventType = TemplateEventType.CREATE_PERSONAL_TEMPLATE_SYNC
                EventBus.getDefault().post(templateEvent)
                sendUpdatePersonalTemplateMessage(templateModule)
            }
            if (isConnected() && LoginUtils.isLogin()) {
                updateTemplateToService(templateModule, isEtagTemplate = isEtagTemplate)
            }
            val jsonObject = JSONObject()
            jsonObject["action"] = "myTemplateRefresh"
            jsonObject["myTemplateRefresh"] = ""
            EventBus.getDefault().post(any2Json(jsonObject))
        }
    }

    /**
     * 更新模板上传服务器
     */
    private fun updateTemplateToService(
        templateModule: TemplateModule,
        isEtagTemplate: Boolean = false
    ) {
        if (!syncTemplateIdList.contains(templateModule.id)) {
            syncTemplateIdList.add(templateModule.id)
        }
        GlobalScope.launch(Dispatchers.Main) {
            var isUpdateDatabase = !isEtagTemplate
            val uploadImageResult =
                UploadImageUtils.handleUploadTemplateImages(
                    templateModule,
                    true,
                    isUpdateDatabase = isUpdateDatabase
                )
//            if (uploadImageResult) {
            var templateModuleLocal = templateModule.toTemplateModuleLocal()
            val templateModuleR =
                TemplateModuleLocal.convert(templateModuleLocal)
            TemplateRequest.updatePersonalTemplate(
                templateModuleR,
                object : JCTResponseListener<String> {
                    override fun onSuccess(body: String) {
                        try {
                            if (isEtagTemplate) {
                                //保存成功，缓存电子价签模板
                                val content = any2Json(templateModuleLocal)
                                EtagTemplateFileUtils.saveEtagTemplate(
                                    content,
                                    templateModuleLocal.id
                                )
                                val json = JSONObject()
                                json["action"] = "saveUpdateCanvasData"
                                json["saveUpdateCanvasData"] = ""
                                json["saveSuccessData"] = content
                                EventBus.getDefault().post(any2Json(json))
                                return
                            }
                          /*  DaoFactory.templateModuleDao().queryBuilder()
                                .where(TemplateModuleDao.Properties.Id.eq(templateModule.id))
                                .unique()?.apply {
                                    this.local_type = TemplateModule.SYNC
                                    DaoFactory.templateModuleDao().saveInTx(this)
                                    if (callback == null) {
                                        logI(
                                            "updateTemplateToService",
                                            "home page ${templateModule.id}"
                                        )
                                        sendSavePersonalTemplateMessage(this, serverSuccess = true)
                                    }
                                    val content = any2Json(templateModuleLocal)
                                    val json = JSONObject()
                                    json["action"] = "saveUpdateCanvasData"
                                    json["saveUpdateCanvasData"] = ""
                                    json["saveSuccessData"] = content
                                    EventBus.getDefault().post(any2Json(json))
                                    sendUpdatePersonalTemplateMessage(this)
                                    val templateEvent = TemplateEvent()
                                    templateEvent.eventType =
                                        TemplateEventType.UPDATE_HOME_TEMPLATE_SYNC
                                    EventBus.getDefault().post(templateEvent)
                                    callback?.onResult(templateModule.id)
//                                if (this.id == this.cloudTemplateId) {
//                                    BuriedHelper.trackEvent(
//                                        "click", "025_071_100", hashMapOf(
//                                            Pair("b_name", "保存"),
//                                            Pair("temp_id", this.id)
//                                        )
//                                    )
//                                } else {
//                                    BuriedHelper.trackEvent(
//                                        "click", "025_071_100", hashMapOf(
//                                            Pair("b_name", "保存"),
//                                            Pair("temp_id", this.id),
//                                            Pair("industry_temp_id", this.cloudTemplateId)
//                                        )
//                                    )
//                                }
                                }
                            if (syncTemplateIdList.contains(templateModule.id)) {
                                syncTemplateIdList.remove(templateModule.id)
                            }*/
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }

                    override fun onError(message: String) {
                        if (syncTemplateIdList.contains(templateModule.id)) {
                            syncTemplateIdList.remove(templateModule.id)
                        }
                        //   sendSavePersonalTemplateFailed()
                        if (isEtagTemplate) {
                            BuriedHelper.trackEvent(
                                "show", "109_270", hashMapOf()
                            )
                        }
                    }
                }, isEtagTemplate = isEtagTemplate
            )
//            }
//            else{
//                if(syncTemplateIdList.contains(templateModule.id)) {
//                    syncTemplateIdList.remove(templateModule.id)
//                }
//                sendSavePersonalTemplateFailed()
//            }
        }
    }

    suspend fun uploadTemplateResources(
        templateModule: TemplateModule,
        thumpBitmap: Bitmap,
        contentThumbBitmap: Bitmap?
    ): Boolean {
        saveThumb(templateModule, thumpBitmap)
        if (contentThumbBitmap != null) {
            saveContentThumb(templateModule, contentThumbBitmap)
        }
        saveBackgroundAndElement(templateModule, templateModule.json)
        //上传所有图片
        return UploadImageUtils.handleUploadTemplateImages(
            templateModule,
            false,
            isUpdateDatabase = false
        )
    }


    private suspend fun saveThumb(newTemplateModule: TemplateModule, thumpBitmap: Bitmap) {
        withContext(Dispatchers.IO) {
//            if (newTemplateModule.local_thumb.isNotEmpty()) {
//                val file = File(newTemplateModule.local_thumb)
//                if (file.exists()) {
//                    file.delete()
//                }
//            }
            val thumbFile = newTemplateModule.thumbFile
            if (!thumbFile.exists()) FileUtils.createFile(thumbFile.absolutePath)
            val fos = FileOutputStream(thumbFile)
            thumpBitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
            fos.flush()
            fos.close()
            newTemplateModule.local_thumb = thumbFile.absolutePath
        }
    }

    private suspend fun saveContentThumb(
        newTemplateModule: TemplateModule,
        contentThumbBitmap: Bitmap
    ) {
        withContext(Dispatchers.IO) {
            if (newTemplateModule.localContentThumb.isNotEmpty()) {
                val file = File(newTemplateModule.localContentThumb)
                if (file.exists()) {
                    file.delete()
                }
            }
            val contentThumbFile = newTemplateModule.contentThumbFile
            if (!contentThumbFile.exists()) FileUtils.createFile(contentThumbFile.absolutePath)
            val fos = FileOutputStream(contentThumbFile)
            contentThumbBitmap.compress(Bitmap.CompressFormat.PNG, 100, fos)
            fos.flush()
            fos.close()
            newTemplateModule.localContentThumb = contentThumbFile.absolutePath
        }
    }

    private suspend fun saveBackgroundAndElement(newTemplateModule: TemplateModule, json: String) {
        withContext(Dispatchers.IO) {
            val printModule = json2Any<PrintModule>(json)
            printModule ?: return@withContext
//            val localBackground: ArrayList<String> = arrayListOf()
//            localBackground.addAll(printModule.localBackground)
            val models = printModule.actionModel.models
//            printModule.localBackground.clear()
//            localBackground.withIndex().forEach { (index, it) ->
//                val oldFile = File(it)
//                if (oldFile.exists()) {
//                    var newFile = newTemplateModule.getBackgroundFile(index.toString())
//                    if (newFile.absolutePath == oldFile.absolutePath) {
//                        newFile =
//                            newTemplateModule.getBackgroundFile("${System.currentTimeMillis()}")
//                    }
//                    if (!newFile.exists()) FileUtils.createFile(newFile.absolutePath)
//                    try {
//                        FileUtils.copyFile(it, newFile.absolutePath)
//                    } catch (e: Exception) {
//                        LogUtils.e("copy file failed")
//                    }
//                    printModule.localBackground.add(newFile.absolutePath)
//                }
//            }
            models.withIndex().forEach { (index, it) ->
                val oldFile = File(it.localUrl)
                if(oldFile.exists() && oldFile.absolutePath.contains("cache/pdf_image")){
                    val newFile = newTemplateModule.getElementFile(it.itemId)
                    FileUtils.copyFile(oldFile, newFile.absolutePath)
                    it.localUrl = newFile.absolutePath
                }
//                if (oldFile.exists()) {
//                    val newFile = newTemplateModule.getElementFile(it.itemId)
////                    if(oldFile.name == "element_img${it.itemId}.png"){
////                        oldFile.renameTo(newFile)
////                    }
////                    else {
//                    if (!newFile.exists()) {
//                        FileUtils.createFile(newFile.absolutePath)
//                        FileUtils.copyFile(oldFile, newFile.absolutePath)
//                    }
////                    if(isPdfHsitory){
////                        FileUtils.copyFile(oldFile, newFile.absolutePath)
////                    }
////                    }
//                    it.localUrl = newFile.absolutePath
//                }
            }
            newTemplateModule.json = any2Json(printModule)
        }
    }

    /**
     * 删除服务器上的个人模板
     */
//    private fun deleteTemplateToService(id: String) {
//        val arrayList = ArrayList<String>()
//        arrayList.add(id)
//        TemplateRequest.deletePersonalTemplates(arrayList, object : JCTResponseListener<String> {
//            override fun onSuccess(body: String) {
//                DaoFactory.templateModuleDao().queryBuilder()
//                    .where(TemplateModuleDao.Properties.Id.eq(id)).unique()?.apply {
//                        DaoFactory.templateModuleDao().deleteInTx(this)
//                    }
//            }
//
//            override fun onError(message: String) {
//
//            }
//        })
//    }

    fun sendSavePersonalTemplateMessage(
        templateModule: TemplateModule,
        oldId: String = templateModule.id,
        serverSuccess: Boolean = false
    ) {
        val templateEvent = TemplateEvent()
        templateEvent.eventType = TemplateEventType.SAVE_PERSONAL_TEMPLATE_SYNC
        val templateSyncData = TemplateSyncData()
        templateSyncData.niimbotTemplate = templateModule.toTemplateModuleLocal()
        templateSyncData.templateId = oldId
        templateEvent.eventData = any2Json(templateSyncData)
        templateEvent.serverSuccess = serverSuccess
        EventBus.getDefault().post(templateEvent)
    }

    private fun sendSavePersonalTemplateFailed() {
        val templateEvent = TemplateEvent()
        templateEvent.eventType = TemplateEventType.SAVE_PERSONAL_TEMPLATE_FAIL
        EventBus.getDefault().post(templateEvent)
    }

    /**
     * 预加载活码资源
     * @param codes String
     */
    private fun loadLiveCode(codes: String) {
        if (!codes.isNullOrEmpty()) {
            LiveCodeService.getLiveCodeByIds(codes)
        }
    }

    private fun loadForm(codes: String) {
        if (!codes.isNullOrEmpty()) {
            FormService.getFormByIds(codes)
        }
    }

    suspend fun writeModuleImgV2(
        module: TemplateModuleLocal,
    ): TemplateModuleLocal {
        val templateStr = any2Json(module)
        var result = FlutterMethodInvokeManager.downloadTemplateResSuspend(templateStr)
        if (result == null) {
            return module
        } else {
            val moduleLocal2 = TemplateModuleLocal.fromJson(result)
            return moduleLocal2!!
        }
    }

    /**
     * 非网络请求时调用时，请勿更新sync状态
     * 此方法应该是获取网络详情后缓存到本地时使用的，因此需要更新sync状态
     */
    suspend fun writeModuleImg(
        module: TemplateModule,
        isUpdateDatabase: Boolean,
        needProcessThumb: Boolean = true,
        ignoreSyncState: Boolean = false
    ): TemplateModule {
        LogUtils.d("writeModuleImg() id = ${module.id} module.json= ${module.json}")

        val moduleLocal =  module.toTemplateModuleLocal()
        val templateStr = any2Json(moduleLocal)
        var result = FlutterMethodInvokeManager.downloadTemplateResSuspend(templateStr)
        if(result == null){
            return module
        }else {
            val moduleLocal2 = TemplateModuleLocal.fromJson(result)
            return TemplateModuleTransform.templateModuleLocalToTemplateModule(moduleLocal2!!)
        }

//        val printModule = PrintModule.fromJson(module.json)
//        if (needProcessThumb) {
//            val thumbFile = module.thumbFile
//            val thumbBackupFile = module.thumbBackupFile
//            if (!File(module.local_thumb).exists() && !module.thumb.isNullOrBlank() && FileUtils.writeFile2Disk(
//                    module.thumb,
//                    thumbBackupFile
//                ).isSuccessFull
//            ) {
//                val oldThumbFile = File(module.local_thumb)
//                if (oldThumbFile.exists()) {
//                    oldThumbFile.delete()
//                }
//                thumbBackupFile.renameTo(thumbFile)
//                module.local_thumb = thumbFile.absolutePath
//            }
//        }
//
//        printModule?.apply {
//            var localBackgroundExists = localBackground.isNotEmpty()
//            localBackground.forEach {
//                val oldLocalBackgroundFile = File(it)
//                if (!oldLocalBackgroundFile.exists()) {
////                        oldLocalBackgroundFile.delete()
//                    localBackgroundExists = false
//                }
//            }
//            if (!localBackgroundExists) {
//                localBackground.clear()
//                backgrounds().withIndex().forEach { (index, background) ->
//                    var backgroundFile = module.getBackgroundFile(index.toString())
//                    if (backgroundFile.exists()) {
//                        backgroundFile = module.getBackgroundFile("${System.currentTimeMillis()}")
//                    }
//                    val targetWidth = SuperUtils.getWidth()
//                    val targetHeight = (targetWidth * module.height / module.width).toInt()
//                    val pictureLocalPath = PictureUtils.downloadPicture(
//                        background,
//                        backgroundFile.absolutePath,
//                        targetWidth,
//                        targetHeight,
//                        canvasRotate = module.canvasRotate
//                    )
//                    localBackground.add(pictureLocalPath)
//                }
//            }
//
//            if (module.layoutSchema.isNotEmpty()) {
//                val gson = Gson()
//                val type = object : TypeToken<MutableMap<String, Any>>() {}.type
//                var map = gson.fromJson(module.layoutSchema, type) as? MutableMap<String, Any>
//
//                if (map != null) {
//                    map["image"].let {
//                        if (backgrounds().contains(it)) {
//                            map["localBackground"] = localBackground
//                        } else {
//                            val imageUrl = map["image"].toString()
//                            var backgroundFile = module.getCableBackgroundFile()
//                            if (backgroundFile.exists()) {
//                                backgroundFile = module.getCableBackgroundFile()
//                            }
//                            map.put("localBackground", arrayListOf(backgroundFile.absolutePath))
//                            val targetWidth = SuperUtils.getWidth()
//                            val targetHeight = (targetWidth * module.height / module.width).toInt()
//                            val pictureLocalPath = PictureUtils.downloadPicture(
//                                imageUrl,
//                                backgroundFile.absolutePath,
//                                targetWidth,
//                                targetHeight
//                            )
//                        }
//                    }
//                }
//                module.layoutSchema = gson.toJson(map)
//
//            }
//
//
//        }
//        printModule?.actionModel?.models?.withIndex()?.forEach { (index, element) ->
//            if (element.itemType == ItemType.PICTURE.type) {
//                val itemFile = module.getElementFile(element.itemId)
//                if (element.localUrl.isEmpty() || !File(element.localUrl).exists()) {
//                    val pictureLocalPath = PictureUtils.downloadPicture(
//                        element.imageUrl,
//                        itemFile.absolutePath,
//                        module.elementCompressDir,
//                        compress = true
//                    )
//                    element.localUrl = pictureLocalPath
////                    if (DaoFactory.writeFile2Disk(element.imageUrl, itemFile)) {
////                        element.localUrl = itemFile.absolutePath
////                    }
//                } else {
////                    if(element.localUrl != itemFile.absolutePath) {
////                        File(element.localUrl).renameTo(itemFile)
////                        element.localUrl = itemFile.absolutePath
////                    }
////                    val pictureLocalPath = PictureUtils.compressPictureIfNecessary(element.localUrl, module.elementCompressDir)
////                    element.localUrl = pictureLocalPath
//                }
//                if (element.isNinePatch && element.ninePatchUrl.isNotEmpty()) {
//                    val itemNinePatchFile = module.getElementNinePatchFile(element.itemId)
//                    if (element.ninePatchLocalUrl.isEmpty() || !File(element.ninePatchLocalUrl).exists()) {
//                        val ninePatchSaver = FileUtils.writeFile2Disk(element.ninePatchUrl, itemNinePatchFile.absoluteFile)
////                        val ninePatchLocalUrl = PictureUtils.downloadPicture(
////                            element.ninePatchUrl,
////                            itemNinePatchFile.absolutePath,
////                            module.elementCompressDir,
////                            compress = false
////                        )
//                        if (ninePatchSaver.isSuccessFull) element.ninePatchLocalUrl = itemNinePatchFile.absolutePath
//                    }
//                }
//            }
//        }
//        module.json = any2Json(printModule)
//        if (!ignoreSyncState) {
//            module.local_type = TemplateModule.SYNC
//        }
//        module.phone = arrayListOf(LoginDataEnum.phone)
    }


    private var unSaveFontCodes = ArrayList<String>()


    var unDownloadList = ArrayList<String>()
    fun checkUnDownloadFonts(
        mContext: Activity,
        drawTemplate: TemplateModule,
        callbacklistener: ((result: Boolean) -> Unit)
    ) {


        drawTemplate.let { template ->

//            if (unDownloadList.contains(template.id)) {
//                callbacklistener.invoke(true)
//                return
//            }


            val templateModuleLocal = template.toTemplateModuleLocal()
            val fontCodes = templateModuleLocal.checkNotDownloadFontCodes()
            if (fontCodes.isEmpty()) {
                callbacklistener.invoke(true)
                return@let
            }
            FontUtils.getFontInfoFromNet(ignoreLanguage = true) { result, fontInfo, _ ->
                if (!result) {
                    showToast("app01160")
                    return@getFontInfoFromNet
                }
                val fontCodeNeedDownload = arrayListOf<FontLibBean>()
                fontCodes.forEach { code ->
                    fontInfo.firstOrNull { it.code == code }?.let {
                        if (!it.url.isNullOrEmpty()) {
                            fontCodeNeedDownload.add(it)
                        }
                    }
                }
                GlobalScope.launch(Dispatchers.Main) {
                    if (fontCodeNeedDownload.isNotEmpty()) {
                        if (fontCodeNeedDownload.size != fontCodes.size) {
                            //部分可下载
                            val message =
                                fontCodeNeedDownload.size.toString() + LanguageUtil.findLanguageString(
                                    "app01246",
                                    mContext
                                ) + "， ${fontCodes.size - fontCodeNeedDownload.size}" + LanguageUtil.findLanguageString(
                                    "app01247",
                                    mContext
                                )
                            showDownloadFontDialog(
                                mContext,
                                message,
                                fontCodeNeedDownload,
                                callbacklistener,
                                template
                            )
                        } else {
                            //全部可下载
                            showDownloadFontDialog(
                                mContext,
                                fontCodes.size.toString() + LanguageUtil.findLanguageString(
                                    "app01217",
                                    mContext
                                ), fontCodeNeedDownload, callbacklistener, template
                            )
                        }
                    } else {
                        //全部不能下载
                        CustomDialog.Builder(mContext)
                            .setTitle("app01218")
                            .setMessage("app01248")
                            .setNegativeButton("app00707") { dialog, _ ->
                                dialog.dismiss()
                                callbacklistener.invoke(true)
                            }
                            .setCancel(false)
                            .create().show()
                    }
                }
            }
        }
    }


    @SuppressLint("MissingPermission")
    private fun showDownloadFontDialog(
        context: Activity,
        message: String,
        fontCodes: ArrayList<FontLibBean>,
        callbacklistener: (result: Boolean) -> Unit,
        template: TemplateModule,
    ) {
        CustomDialog.Builder(context)
            .setTitle("app01218")
            .setMessage(message)
            .setPositiveButton("app01220") { dialog, _ ->
                if (NetworkUtils.isConnected()) {
                    dialog.dismiss()
                    FontUtils.showDownloadingDialog(context, fontCodes, listener = {
                        FontManagerHelper.updateUserFonts()
                        unDownloadList.add(template.id)
                        callbacklistener.invoke(true)
                    }) {
                        FontManagerHelper.freshFontInfo(it)
                    }
                } else {
                    //  callbacklistener.invoke(true)
                    showToast("app01139")
                }
            }
            .setNegativeButton("app00030") { dialog, _ ->
                unDownloadList.add(template.id)
                callbacklistener.invoke(true)
                dialog.dismiss()
            }
            .setCancel(false)
            .create().show()
    }

    fun sendUpdatePersonalTemplateMessage(
        templateModule: TemplateModule,
        oldId: String = templateModule.id
    ) {
        val templateEvent = TemplateEvent()
        templateEvent.eventType = TemplateEventType.UPDATE_PERSONAL_TEMPLATE_SYNC
        val templateSyncData = TemplateSyncData()
        templateSyncData.niimbotTemplate = templateModule.toTemplateModuleLocal()
        templateSyncData.templateId = oldId
        templateEvent.eventData = any2Json(templateSyncData)
        EventBus.getDefault().post(templateEvent)
    }

    fun sendUpdateCloudTemplateMessage(templateModule: TemplateModule) {
        val templateEvent = TemplateEvent()
        templateEvent.eventType = TemplateEventType.UPDATE_CLOUD_TEMPLATE_SYNC
        val templateSyncData = TemplateSyncData()
        templateSyncData.niimbotTemplate = templateModule.toTemplateModuleLocal()
        templateEvent.eventData = any2Json(templateSyncData)
        EventBus.getDefault().post(templateEvent)
    }

    /**
     * 离线数据同步，数据同步异常
     */
    fun sync() {
        if (!isConnected()) return
        if (!com.niimbot.baselibrary.user.LoginDataEnum.isLogin) return
//        GlobalScope.launch {
//            syncUserInfo()
//            syncSortTemplate()
//        }
    }

    /**
     * 打印历史记录，离线数据同步
     */
    fun syncPrintHistory() {
        if (!isConnected()) return
        if (!LoginDataEnum.isLogin) return
//        GlobalScope.launch {
            syncPrintHistoryRecord()
//        }
    }

    private fun syncPrintHistoryRecord() {
        FlutterMethodInvokeManager.getAllPrintHistory({list ->
            if(list.isEmpty()){
                return@getAllPrintHistory
            }
//            val list = json2Array(it, PrintHistoryModule::class.java)
            LogUtils.d("syncPrintHistoryRecord() list.size = ${list.size}")
//            val ids = arrayListOf<String>()
//            list.forEach {
//                ids.add(it["uniqueId"] as String)
//            }
//            Log.i("PrintHistoryBug", "UploadPrintHistoryLog: getAllPrintHistory, ids = $ids")
            GlobalScope.launch {
                for (map in list) {
                    try{
                        TemplateManager.uploadPrintHistory(PrintHistoryModule.fromMap(map))
                    } catch(e: Exception) {e.printStackTrace()}
                }
            }
        }, {

        })
    }

    fun stopTemplateSyncProcessIfNecessary() {
        mQueueList.clear()
//        syncErrorReTryTimes = 0
    }

//    private suspend fun syncUserInfo() {
//        if (!com.niimbot.baselibrary.user.LoginDataEnum.isLogin) return
//        withContext(Dispatchers.IO) {
//            try {
//                TemplateDbUtils.updateTemplateDefaultUserId(com.niimbot.baselibrary.user.LoginDataEnum.id)
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//        }
//    }

    private fun syncSortTemplate() {
//        try {
//            val list = DaoFactory.templateModuleDao().queryBuilder()
//                .whereOr(
//                    TemplateModuleDao.Properties.Local_type.eq(TemplateModule.CREATE),
//                    TemplateModuleDao.Properties.Local_type.eq(TemplateModule.UPDATE)
//                )
//                .where(TemplateModuleDao.Properties.Id.notIn(syncTemplateIdList))
//                .where(TemplateModuleDao.Properties.Id.notEq(cachedTemplateId))
//                .orderDesc(TemplateModuleDao.Properties.Update_time)
//                .list()
//            LogUtils.d("syncSortTemplate() list.size = ${list.size}")
//            mQueueList.clear()
//            for (index in 0 until list.size) {
//                val templateModule = list[index]
//                mQueueList.add(templateModule)
//            }
//
//            doTask()
//            DaoFactory.templateModuleDao().queryBuilder()
//                .where(TemplateModuleDao.Properties.Local_type.eq(TemplateModule.DELETE)).list()
//                ?.forEach {
//                    deleteTemplateToService(it.id)
//                }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
    }

    /***
     * 同步先根据本地时间排序，再传递到服务端
     */
    var mQueueList: MutableList<TemplateModule> = ArrayList()
//    private fun doTask() {
//        if (mQueueList.size > 0) {
//            val task = mQueueList[0]
//            doReqTemplate(task)
//        }
//    }

    private var syncTemplateIdList: ArrayList<String> = arrayListOf()
//    private fun doReqTemplate(templateModule: TemplateModule) {
//        if (null == templateModule) return
//        LogUtils.d("syncSortTemplate() id= ${templateModule.id} template_type = ${templateModule.template_type} ,update_time = ${templateModule.update_time} ,local_type = ${templateModule.local_type} , mQueueList.size = ${mQueueList.size}")
//        if (syncTemplateIdList.any { it == templateModule.id }) {
//            if (mQueueList.size > 0) {
//                try {
//                    mQueueList.removeAt(0)
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                }
//                doTask()
//            }
//        } else {
//            // 2024/7/19 Ice_Liu 因为线上bug的原因，用户侧有一些模板存在用户本地但type是update导致无法成功上传
//            if (templateModule.local_type == TemplateModule.CREATE || templateModule.id.length > 10) {
//                createTemplateToService(templateModule, mSyncResultCallback)
//            } else {
//                updateTemplateToService(templateModule, mSyncResultCallback)
//                val jsonObject = JSONObject()
//                jsonObject["action"] = "myTemplateRefresh"
//                jsonObject["myTemplateRefresh"] = ""
//                EventBus.getDefault().post(any2Json(jsonObject))
//            }
//        }
//    }

    /**
     * 离线数据同步统一走这里
     */
//    private var syncErrorReTryTimes: Int = 0
//    private var mSyncResultCallback = object : SyncResultCallback {
//        override fun onResult(id: String) {
//            syncErrorReTryTimes = 0
//            if (mQueueList.size > 0) {
//                mQueueList.removeAt(0)
//                doTask()
//            }
//        }
//
//        override fun onError(code: String) {
//            if (isConnected() && com.niimbot.baselibrary.user.LoginDataEnum.isLogin) {
//                //同步失败重复做2次
//                if (syncErrorReTryTimes >= 1) {
//                    syncErrorReTryTimes = 0
//                    if (mQueueList.size > 0) {
//                        mQueueList.removeAt(0)
//                    }
//                } else {
//                    syncErrorReTryTimes++
//                }
//                doTask()
//            }
//        }
//    }
//
//    interface SyncResultCallback {
//        fun onResult(id: String)
//        fun onError(code: String)
//    }

    /**
     * 获取推荐的标签类别（尺寸）
     */
    fun getRecommendLabelCategory(
        industryId: Int,
        categoryId: Int,
        template: TemplateModule,
        callback: JCTResponseListener<List<SizeBean>>
    ) {
        TemplateRequest.getRecommendLabelCategory(industryId, categoryId, template, callback)
    }

    /**
     * 获取推荐的标签列表
     */
    fun getRecommendLabelList(
        page: Int,
        limit: Int,
        relateWidth: String,
        relateHeight: String,
        industryId: Int,
        categoryId: Int,
        template: TemplateModule,
        callback: JCTResponseListener<TemplateInfo>
    ) {
        TemplateRequest.getRecommendLabelList(
            page,
            limit,
            relateWidth,
            relateHeight,
            industryId,
            categoryId,
            template,
            callback
        )
    }

    /**
     * 获取推荐的模板分类
     */
    fun getRecommendTemplateCategory(
        industryId: Int,
        categoryId: Int,
        template: TemplateModule,
        callback: JCTResponseListener<CategoryInfo>
    ) {
        TemplateRequest.getRecommendTemplateCategory(industryId, categoryId, template, callback)
    }

    /**
     * 获取推荐的标签列表
     */
    fun getRecommendTemplateList(
        page: Int,
        limit: Int,
        industryId: Int,
        categoryId: Int,
        template: TemplateModule,
        callback: JCTResponseListener<TemplateInfo>
    ) {
        TemplateRequest.getRecommendTemplateList(
            page,
            limit,
            industryId,
            categoryId,
            template,
            callback
        )
    }

    /**
     * 通过推荐模板-->历史内容
     */
/*    fun getTemplateHistory(
        page: Int,
        limit: Int,
        template: TemplateModule,
        callback: JCTResponseListener<TemplateInfo>
    ) {
        TemplateRequest.getTemplateHistory(
            page,
            limit,
            template.one_code,
            template.name,
            object : JCTResponseListener<TemplateInfo> {
                override fun onSuccess(body: TemplateInfo) {
                    var localList = arrayListOf<TemplateModule>()
                    body.list.forEach { serverTemplate ->
                        val localTemplate = DaoFactory.templateModuleDao().queryBuilder()
                            .where(TemplateModuleDao.Properties.Id.eq(serverTemplate.id)).unique()
                        localTemplate?.let { localList.add(it) } ?: localList.add(serverTemplate)
                    }
                    body.list = localList
                    callback.onSuccess(body)
                }

                override fun onError(message: String) {
                    callback.onError(message)
                }
            })
    }*/

    var cachedTemplateId = "wifiAppCachedTemplate"
//    fun saveWifiAppCachedTemplate2DB(
//        newTemplate: TemplateModule,
//        listener: (newTemplate: TemplateModule) -> Unit
//    ) {
//        GlobalScope.launch {
//            newTemplate.sql_id = null
//            newTemplate.id = cachedTemplateId
//            newTemplate.local_type = TemplateModule.SYNC
//            newTemplate.template_type = TemplateModule.TEMPLATE_MY
//            newTemplate.user_id = -3
//            val times = TimeUtils.millis2String(System.currentTimeMillis())
//            newTemplate.add_time = times
//            newTemplate.update_time = times
//            newTemplate.local_thumb = ""
//            newTemplate.thumb = ""
//            saveThumb(newTemplate, Bitmap.createBitmap(100, 100, Bitmap.Config.RGB_565))
//            saveBackgroundAndElement(newTemplate, newTemplate.json)
////            DaoFactory.templateModuleDao().insertOrReplaceInTx(newTemplate)
//            TemplateDbUtils.insertOrUpdateTemplate(newTemplate)
//            withContext(Dispatchers.Main) {
//                listener.invoke(newTemplate)
//            }
//        }
//    }

    //wifi码模版保存
    fun saveGenerateWifiAppCachedTemplate2DB(newTemplate: TemplateModuleLocal) {
//        GlobalScope.launch {
//            try {
////                if (newTemplate.id == cachedTemplateId) {
//                var templateModule =
//                    TemplateModuleTransform.templateModuleLocalToTemplateModule(newTemplate)
////                DaoFactory.templateModuleDao().insertOrReplaceInTx(templateModule)
//                TemplateDbUtils.insertOrUpdateTemplate(templateModule)
////                }
//            } catch (e: Exception) {
//                e.printStackTrace()
//            }
//        }
        val templateData = any2Json(newTemplate)
        FlutterMethodInvokeManager.insertOrUpdateTemplate(templateData)
    }

    //注意：模版详情--模版重命名进行的更新 以后模版详情会废弃
    fun updateLocalTemplate(template: TemplateModule) {
//        TemplateDbUtils.insertOrUpdateTemplate(template)
        val moduleLocal = template.toTemplateModuleLocal()
        val templateData = any2Json(moduleLocal)
        FlutterMethodInvokeManager.insertOrUpdateTemplate(templateData)
        sendUpdatePersonalTemplateMessage(template)
    }


    fun saveUsedRecord(template: TemplateModuleLocal) {
        GlobalScope.launch(Dispatchers.IO) {
            moduleToLocal(template.copy(), true)
            TemplateUsedRecordUtils.saveUsedRecord(
                template!!.id,
                LoginDataEnum.isLogin
            )
        }
    }

    fun saveUsedRecordByLabelId(labelId: String) {
        GlobalScope.launch(Dispatchers.IO) {
            TemplateUsedRecordUtils.saveUsedRecord(
                labelId,
                LoginDataEnum.isLogin
            )
        }
    }

    /**
     * 针对离线状态下保存的模板
     * 打开后，在有线环境下修改保存时，需要判断是否已自动同步
     */
    fun checkHasSynced(
        template: TemplateModuleLocal,
        listener: ((module: TemplateModuleLocal?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.IO) {
            if (hasSyncedIds.contains(template.id)) {
                template.id = hasSyncedIds[template.id].toString()
                template.local_type = TemplateModule.SYNC
                listener?.invoke(template)
            }
            listener?.invoke(template)
        }
    }
}
