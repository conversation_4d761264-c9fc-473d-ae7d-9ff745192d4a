import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/macro/color.dart';
import 'package:text/pages/message/controller/message_logic.dart';
import 'package:text/pages/message/controller/message_state.dart';
import 'package:text/pages/message/model/message_item_model.dart';
import 'package:text/pages/message/model/message_promotion_model.dart';
import 'package:text/pages/message/model/message_tab_model.dart';
import 'package:text/pages/message/model/message_type.dart';
import 'package:text/pages/message/utils/message_util.dart';
import 'package:text/pages/message/widget/common_empty_widget.dart';
import 'package:text/pages/message/widget/message_item_widget.dart';
import 'package:text/pages/message/widget/message_tab_item_widget.dart';
import 'package:text/pages/message/widget/net_error_widget.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/image_utils.dart';
import 'package:text/utils/svg_icon.dart';

import '../../application.dart';
import '../../utils/common_fun.dart';
import '../../utils/toast_util.dart';

class MessageListPage extends StatefulWidget {

  MessageListPage({Key? key}) : super(key: key);

  @override
  State<MessageListPage> createState() => MessageListPageState();
}

class MessageListPageState extends State<MessageListPage> {
  late MessageLogic logic;
  late MessageState state;
  late String unreadCountStr;

  @override
  void initState() {
    super.initState();
    logic = Get.put(MessageLogic());
    state = logic.state;
    logic.getMessageUnreadCount();
    logic.getMessageList(refreshController: state.messageRefreshController, category: MessageType.promotion);
    // logic.areNotificationEnabled();
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is String && data == "kBackToForeground") {
        if (!mounted) {
          return;
        }
        if (mounted) {
          logic.getMessageList(refreshController: state.messageRefreshController, category: MessageType.promotion);
          // logic.areNotificationEnabled();
          logic.checkNotificationGranted();
        }
      }
    });
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "view",
      "posCode": "130",
    });
    logic.checkNotificationGranted();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        logic.batchUpdateMessageReadStatus(MessageType.promotion);
        return true;
      },
      child: Scaffold(
          appBar: AppBar(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              elevation: 0,
              centerTitle: true,
              title: GetBuilder<MessageLogic>(
                  id: MessageLogic.updateMessageList,
                  builder: (context) {
                    return Text(
                      state.unreadMessageCount <= 0
                          ? intlanguage("app100001794", "消息")
                          : intlanguage('app100001793', '消息(\$)', param: [logic.getMessageUnreadCountStr(needReverseStr: Application.textDirection == TextDirection.rtl)]),
                      style: const TextStyle(color: KColor.title, fontSize: 17, fontWeight: FontWeight.w600),
                    );
                  }),
              leading: InkWell(
                highlightColor: Colors.transparent,
                splashColor: Colors.transparent,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16),
                  child: Image(
                    width: 24,
                    height: 24,
                    image: ImageUtils.getAssetImage('icon_arrow_back'),
                    matchTextDirection: true,
                  ),
                ),
                onTap: () async {
                  logic.batchUpdateMessageReadStatus(MessageType.promotion);
                  Navigator.of(context).pop();
                },
              ),
              actions: [
                InkWell(
                  onTap: _handleClear,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  child: Padding(
                      padding: const EdgeInsetsDirectional.fromSTEB(15, 0, 16, 0),
                      child: GetBuilder<MessageLogic>(
                          id: MessageLogic.updateMessageList,
                          builder: (context) {
                            return SvgIcon(
                              (Application.networkConnected && state.unreadMessageCount > 0)
                                  ? 'assets/images/print_history/history_clear.svg'
                                  : "assets/images/print_history/unable_history_clear.svg",
                              width: 26,
                              height: 26,
                            );
                          })),
                ),
                // InkWell(
                //   onTap: _handleSetting,
                //   highlightColor: Colors.transparent,
                //   splashColor: Colors.transparent,
                //   child: Padding(
                //       padding: const EdgeInsetsDirectional.fromSTEB(15, 0, 16, 0),
                //       child: const SvgIcon(
                //         'assets/images/print_history/history_setting.svg',
                //         width: 26,
                //         height: 26,
                //       )),
                // ),
              ]),
          body: GetBuilder<MessageLogic>(
            id: MessageLogic.updateMessageList,
            builder: (logic) {
              return Container(
                color: KColor.COLOR_F5F5F5,
                child: _buildContentWidget(),
              );
            },
          )),
    );
  }

  _buildContentWidget() {
    switch (state.messagePageState) {
      case MessagePageState.empty:
        return CommonEmptyWidget("emptyImagePath", intlanguage("app100001799", "暂无消息"));
      case MessagePageState.loading:
        return Container(
            child: Stack(
          children: [
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(bottom: 80),
              child: const CupertinoActivityIndicator(
                radius: 20,
                animating: true,
              ),
            ),
          ],
        ));
      case MessagePageState.data:
        return _buildListWidget();
      case MessagePageState.netError:
        return NetErrorWidget(
          retryCallback: () {
            logic.retryRequestMessages(MessageType.promotion);
          },
        );
    }
  }

  Widget _buildNotificationTipWidget() {
    return GetBuilder<MessageLogic>(
        id: MessageLogic.updateNotificationTip,
        builder: (logic) {
          return Offstage(
            offstage: state.notificationEnabled || MessageLogic.notificationTipsClosed,
            child: Container(
                padding: EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                width: double.infinity,
                color: Color(0xffFFF6E9),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        intlanguage('app100001795', '开启通知，及时获取消息提醒、优惠福利'),
                        //textAlign: TextAlign.left,
                        maxLines: 2, // 限制最多2行
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF1D1D29),height: 1.2),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        ToNativeMethodChannel().jumpToNotificationSetting();
                        ToNativeMethodChannel().sendTrackingToNative({
                          "track": "click",
                          "posCode": "130_373_339",
                        });
                      },
                      child: Container(
                        padding: EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
                        child: Text(
                          intlanguage('app100000575', '去开启'),
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFFFB4B42)),
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        logic.closeNotificationTip();
                      },
                      child: Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(5, 8, 5, 8),
                          child: const SvgIcon(
                            'assets/images/message/icon_tip_close.svg',
                            width: 16,
                            height: 16,
                          )),
                    ),
                  ],
                )),
          );
        });
  }

  Widget _buildListWidget() {
    return Column(
      children: [
        _buildNotificationTipWidget(),
        Expanded(
          child: SmartRefresher(
              controller: state.messageRefreshController,
              enablePullDown: false,
              enablePullUp: true,
              onRefresh: () {},
              onLoading: () {
                logic.getMessageList(
                    refreshController: state.messageRefreshController,
                    category: MessageType.promotion,
                    lastId: state.lastMessage?.id);
              },
              child: ListView.separated(
                padding: EdgeInsetsDirectional.symmetric(horizontal: 16),
                itemCount: state.messageList.length,
                itemBuilder: (context, index) {
                  MessageItemModel messageModel = state.messageList[index];
                  return GestureDetector(
                    onTap: () {
                      if (!Application.networkConnected) {
                        showToast(msg:intlanguage('app01139', '网络异常'));
                        return;
                      }
                      if (messageModel is MessageTabModel) {
                        CustomNavigation.gotoPage("subMessageListPage", {"category": messageModel.category,"isPush" : 0},
                            withContainer: true);
                        ToNativeMethodChannel().sendTrackingToNative({
                          "track": "click",
                          "posCode": "130_374_340",
                          "ext":{"sort_name":getTrackingTitle(messageModel.category)}
                        });
                      } else {
                        logic.updateMessageReadStatus(messageModel as MessagePromotionModel);
                        ToNativeMethodChannel().jumpToMessageDetail(intlanguage("app100001798", "消息详情"), messageModel.redirectUrl ?? "", logic.getMessageJumpType(messageModel));
                        ToNativeMethodChannel().sendTrackingToNative({
                          "track": "click",
                          "posCode": "130_374_341",
                          "ext":{"plan_id":messageModel.planId}
                        });
                      }
                    },
                    child: Padding(
                      padding: EdgeInsetsDirectional.only(
                          top: index == 0 ? 12 : 0, bottom: index == state.messageList.length - 1 ? 10 : 0),
                      child: messageModel is MessagePromotionModel
                          ? MessageItemWidget(messageModel..category = MessageType.promotion)
                          : MessageCatItemWidget(messageModel as MessageTabModel),
                    ),
                  );
                },
                separatorBuilder: (context, index) {
                  return Divider(
                    color: Colors.transparent,
                    height: 10, // 第一个分隔符增加高度
                  );
                },
              )),
        ),
      ],
    );
  }


///一键已读
  _handleClear() {
    if(!Application.networkConnected ||  state.unreadMessageCount <= 0){
      return;
    }
    logic.cleanAllMessageReadStatus();
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "130_372",
    });
  }

  _handleSetting() {}


  @override
  void dispose() {
    super.dispose();
    Get.delete<MessageLogic>();
  }
}
