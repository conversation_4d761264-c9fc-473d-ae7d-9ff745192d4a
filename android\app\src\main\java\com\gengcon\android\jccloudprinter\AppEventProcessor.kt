package com.gengcon.android.jccloudprinter

import android.app.Application
import android.util.Log
import android.view.Gravity
import android.webkit.JavascriptInterface
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.NetworkUtils
import com.blankj.utilcode.util.SizeUtils
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEngineManager
import com.gengcon.android.jccloudprinter.flutterChannel.FlutterEventRegister
import com.gengcon.connectui.DeviceConnectNewActivity
import com.gengcon.print.draw.print.event.ChangeBatchPrintEvent
import com.gengcon.print.draw.print.event.CheckIllegalRfidEvent
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.getcapacitor.JSObject
import com.idlefish.flutterboost.FlutterBoost
import com.idlefish.flutterboost.containers.FlutterBoostActivity
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.view.JCWebViewFactory
import com.jc.repositories.webview.library.view.webview.BaseWebChromeClient
import com.jc.repositories.webview.library.view.webview.BaseWebClient
import com.jc.repositories.webview.library.view.webview.JSAction
import com.jc.repositories.webview.library.view.webview.JSActionEvent
import com.jc.repositories.webview.library.view.webview.WebPrintHelper
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.item.BaseItemModuleEx
import com.niimbot.appframework_library.dialog.DialogFactory
import com.niimbot.appframework_library.expand.clipRounded
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.services.NiimbotServiceFactory
import com.niimbot.appframework_library.utils.font.FontUtils
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSON
import com.niimbot.fastjson.JSONObject
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.templatecoordinator.core.SilentDownloadResources
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import com.niimbot.utiliylibray.util.EventBusUtils
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.SystemUtil
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.viplibrary.VipDialog
import com.niimbot.viplibrary.VipHelper
import com.niimbot.viplibrary.launchVipActivity
import com.nimmbot.business.livecode.CapAppEventHandler
import com.nimmbot.business.livecode.CapAppHelper
import com.nimmbot.business.livecode.service.MiniPrinterHelper
import com.qyx.languagelibrary.utils.TextHookUtil
import com.southcity.watermelon.util.json2Any
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.local.util.DaoFactory
import melon.south.com.baselibrary.local.util.TemplateUsedRecordUtils
import melon.south.com.baselibrary.module.PrintModule
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.TemplateDbUtils
import melon.south.com.baselibrary.util.TimeUtil
import melon.south.com.baselibrary.util.StringUtil
import melon.south.com.baselibrary.util.TimeUtils
import melon.south.com.baselibrary.util.showToast
import melon.south.com.mainlibrary.v.MainActivity
import melon.south.com.templatelibrary.dao.TemplateIndustryLocalUtils
import melon.south.com.templatelibrary.mvp.presenter.SyncTemplatePresenter
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @ClassName: AppEventProcessor
 * @Author: Liuxiaowen
 * @Date: 2023/2/10 9:45
 * @Description:
 */
class AppEventProcessor(var context: Application) {

    fun register() {
        EventBusUtils.register(this)
    }

    fun unRegister() {
        EventBusUtils.unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onAppEvent(data: String) {
        try {
            val event = JSON.parseObject(data)
            val tag = event.getString("action")
            when (tag) {
                "tag_create", "tag_sheet_create", "tag_fresh", "tag_sheet_fresh" -> {
                    FlutterChannelRegister.postEventResult(data)
                }

                "toShopCopunDialog" -> {
                    val height = event.getFloat("height")
                    val url = event.getString("url")
                    val needPadding = event.getBoolean("needPadding")
                    showShopCopunDialog(url, height, needPadding)
                }

                "toLabelDetailPage" -> {
                    val templateId = event.getString("templateId")
                    TemplateSyncLocalUtils.getTemplateDetails(
                        templateId,
                        false,
                        true,
                        false
                    ) { _, templateModule, errorMsg ->
                        if (templateModule != null) {
                            val paramsMap = HashMap<String, Any>()
                            paramsMap["jsonData"] =
                               any2Json(templateModule.toTemplateModuleLocal())
                            NiimbotGlobal.gotoFlutterPage("labelDetailPage", paramsMap)
                        }
                    }

                }

                "toFlutter" -> {
                    val router = event.getString("router")
                    val params = event.getString("params")
                    FlutterEngineManager.toFlutter(router, params)
                }

                "thirdLibInit" -> {
                    AppInitManager.thirdLibInit(context)
                }

                "toVipDialog" -> {
                    BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                        VipDialog.showVipPayDialog(
                            context = it,
                            sourcePage = event.getString("sourcePage"),
                            actName = event.getString("actName"),
                            anchorSubscribeId = event.getString("anchorSubscribeId")
                        )
                    }
                }
                "savePrintUpdateCanvasData"-> {
                    val moudle= GsonUtils.fromJson(event.getString("saveSuccessData"), TemplateModuleLocal::class.java)
                     val oldId= event.getString("oldId")
                    NiimbotServiceFactory.getTemplateSyncService()
                        ?.getTemplateDetail(
                            moudle.id,
                            true
                        ) { _, templateModuleLocalJson, errorMsg ->
                            if (!templateModuleLocalJson.isNullOrBlank()) {
                                val json = JSONObject()
                                json["action"] = "saveUpdateCanvasData"
                                json["saveUpdateCanvasData"] = ""
                                json["saveSuccessData"] = templateModuleLocalJson
                                json["oldId"] =oldId
                                EventBus.getDefault().post(any2Json(json))
                            }
                        }
                }

                "printCommodity" -> {
                    //向ionic小程序发送打印完成事件
                    val taskId = event.getString("taskId")
                    LogUtils.e("============printCommodity打印完成taskId: $taskId")
                    val params = com.niimbot.fastjson.JSONObject()
                        .apply {
                            put("taskId", taskId)
                            put("success", true)
                        }
                    CapAppHelper.sendCapAppEvent(
                        eventName = "print-complete",
                        message = params.toJSONString()
                    )
                }

                "paySuccess" -> {
                    LoginDataEnum.userModule?.vipInfo?.let {
                        val params = JSONObject()
                            .apply { put("token", HttpTokenUtils.getToken()) }
                        CapAppHelper.sendCapAppEvent(
                            eventName = "buy-vip-complete",
                            message = params.toJSONString()
                        )
                    }
                }

                "imageDataForUniapp" -> {
                    try {
                        val commodityInfoJson = event.getString("commodityInfos")
                        val source = event.getString("source")
                        val bean = JSONObject()
                        bean["index"] = event.get("index")
                        bean["total"] = event.get("total")
                        bean["data"] = event.get("data")
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                "imageDataForIonic" -> {
                    try {
                        val bean = JSONObject()
                        bean["templatePreviewBase64"] = event.get("imageData")
                        CapAppHelper.sendPreviewImageEvent(any2Json(bean))
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                "imageDataForWifiApp" -> {
                    try {
                        val bean = JSONObject()
                        bean["data"] = event.get("data")
                        bean["template"] = event.get("template")
                        CapAppHelper.sendWifiCodePreviewImageEvent(any2Json(bean))
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                "flutterLoginCallback" -> {
                    FlutterEventRegister.sendFlutterOneKeyLoginResult(
                        event.getInteger("authType"),
                        event.getString("ispToken"),
                        event.getInteger("authResult")
                    )
                }

                "flutterLanguageChanged" -> {
                    FlutterEventRegister.sendLanguageChanged()
                }
                "closePrintSettingDialog" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "printerConnectState" -> {
                    FlutterEventRegister.sendEvent(event)
                }
                "refreshBatchPrintRourcesStatus" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "setFlutterlabelData" -> {
                    val currentActivity = FlutterBoost.instance().currentActivity()
                    if (currentActivity != null && currentActivity is DeviceConnectNewActivity && currentActivity.fromCanvasSelectLabel) {
                        return
                    }
                    FlutterEventRegister.sendEvent(event)
                }

                "setLoginStatus" -> {
//                    if (event.getBoolean("isGetRecord")) {
//                        TemplateIndustryLocalUtils.getRecentUsedList(10) { successForServer, it ->
//                        }
//                    } else {
//                        DaoFactory.templateSortModuleDao()?.deleteAll()
//                    }
                    FlutterEventRegister.sendEvent(event)
                    // 登陆状态变化，更新Bugly数据
                    AppInitManager.initBugly(context)
                }
                "getRecentUsedList" -> {
                    TemplateIndustryLocalUtils.getRecentUsedList(10) { successForServer, it ->
                    }
                }

                "uniapp_message" -> {
                    handleUnimpMessage(event)
                }

                "closeCapApp" -> {
                    CapAppHelper.closeCurrentApp()
                }

                "labelRecord" -> {
                    //小程序打印完成--记录最近使用  打印设置sdk重构分支已经记录，此处不做处理
//                    handleSaveLabel(event)
                }

                "scanResultByShop" -> {
                    trackScanByShop(
                        if (event.getBoolean("result") == true) 1 else 0,
                        event.getInteger("type") ?: 1
                    )
                }

                "eTagStatus" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "cableCanvas" -> {
                    PreferencesUtils.put("cableEntry", "cableCanvas")
                    val map = HashMap<String, Any>()
                    map["fontPath"] =
                        FontUtils.customFontFile.absolutePath
                    if (null != AppDataUtil.rfidTemplateModule) {
                        AppDataUtil.rfidTemplateModule?.let { templateModule ->
                            val printModule = json2Any(templateModule.json, PrintModule::class.java)
                            if (printModule != null) {

                                GlobalScope.launch {
                                    TemplateSyncLocalUtils.downloadTemplateResource(
                                        ActivityUtils.getTopActivity(),
                                        templateModule
                                    ) {fontResult,templateModule2 ->
                                        if(templateModule2 == null){
                                            return@downloadTemplateResource
                                        }
                                        GlobalScope.launch {
                                            val niimbotDrawData = NiimbotDrawData()
                                            niimbotDrawData.apply {
                                                this.niimbotTemplate =
                                                    templateModule2.toTemplateModuleLocal()
                                                        ?: TemplateModuleLocal()
                                                this.setNiimbotGoodsInfo()
                                            }
                                            val paramsMap = HashMap<String, Any>()
                                            paramsMap["jsonData"] =
                                                any2Json(templateModule2.toTemplateModuleLocal())
                                            paramsMap["fontPath"] =
                                                FontUtils.customFontFile.absolutePath
                                            if (templateModule2.supportedEditors.firstOrNull() == "cable") {
                                                NiimbotGlobal.gotoFlutterPage(
                                                    "cableCanvas",
                                                    paramsMap
                                                )
                                            } else {
                                                NiimbotGlobal.gotoFlutterPage(
                                                    "cableCanvas",
                                                    map
                                                )
                                            }

                                        }

                                    }
                                }

                            } else {
                                NiimbotGlobal.gotoFlutterPage("cableCanvas",map)
                            }
                        }
                    } else {
                        NiimbotGlobal.gotoFlutterPage("cableCanvas",map)
                    }
                }

                "saveUpdateCanvasData" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "myTemplateRefresh" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "nfcScreenInfo" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "eTagStatusWriterListener" -> {
                    FlutterEventRegister.sendEvent(event)
                }

                "writeLogInfoToFile" -> {
                    FlutterMethodInvokeManager.writeLogInfoToFile(event)
                }

                "uploadLogInfoInFlutter" -> {
                    FlutterMethodInvokeManager.uploadLogInfoInFlutter(event)
                }

                "uploadLogFileToSls" -> {
                    FlutterMethodInvokeManager.uploadLogFileToSls(event)
                }

                "printerFirmwareUpgrade"->{
                    FlutterMethodInvokeManager.printerFirmwareUpgrade(event)
                }

                "sendFlutterEvent" -> {
                    try {
                        val flutterEvent = event["flutterEvent"]
                        FlutterEventRegister.sendEvent(flutterEvent as JSONObject)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

                "toAppPayHalfPanel" -> { //该事件应该只相应WebView向唤醒SKU
                    if (VipHelper.isCurrentUserVip()) {
                        launchVipActivity(ActivityUtils.getTopActivity(), sourcePage = "006")
                    } else {
                        val sku = event.get("sku")
                        VipDialog.showVipPayDialog(
                            ActivityUtils.getTopActivity(),
                            anchorSubscribeId = sku as String
                        )
                    }

                }

                "toPointsShop" -> {
                    NiimbotGlobal.gotoShopWeb(
                        ActivityUtils.getTopActivity(),
                        "${ShopManager.SHOP_MY_POINTS}?jumpSource=print_014"
                    )
                }

                "silentDownloadResources" -> {
                    if (NetworkUtils.isConnected()) {
                        val templateIds = event.get("templateIds")
                        val ids = templateIds as List<Map<String, Int>>
                        SilentDownloadResources.hashSet.clear()
                        SilentDownloadResources.hashMap.clear()
                        SilentDownloadResources.ids=null
                        ids.forEach {
                            SilentDownloadResources.downloadTemplateDetails(it["id"].toString())
                        }
                    } else {
                        EventBus.getDefault().post(ChangeBatchPrintEvent())
                    }
                }

                "silentDownloadResourcesForString" -> {
                    if (NetworkUtils.isConnected()) {
                        val templateIds = event.get("templateIds")
                        val ids = templateIds as List<String>
                        SilentDownloadResources.hashSet.clear()
                        SilentDownloadResources.hashMap.clear()
                        SilentDownloadResources.ids=ids
                        ids.forEach {
                            SilentDownloadResources.downloadTemplateDetails(it)
                        }
                    } else {
                        EventBus.getDefault().post(ChangeBatchPrintEvent())
                    }
                }

                "scanForIonic" -> {
                    val result = event.getJSONObject("data")
                    CapAppHelper.sendScanEvent(result.toJSONString())
                }
                "jumpToRecord" -> {
                    CapAppHelper.sendCheckDangerRecordEvent()
                }
                "jumpToCanvas" -> {
                    val result = event.getJSONObject("data")
                    val labelId = result.getString("labelId")
                    val fromShare = event.getBoolean("fromShare") ?: false

                    val printData = NiimbotDrawData()
                    printData.niimbotTemplate = TemplateModuleLocal.fromJson(result.getString("template"))!!
                    if(fromShare){
                        printData.niimbotTemplate.profile.extrain.folderId = "0"
                    }
                    if(labelId.isNullOrEmpty()){

                        GlobalScope.launch(Dispatchers.Main) {
                            withContext(Dispatchers.IO){
                                TemplateSyncLocalUtils.downloadTemplateFonts(ActivityUtils.getTopActivity(),printData.niimbotTemplate)
                            }
                            var activities = ActivityUtils.getActivityList()
                            for (i in 0 until activities.size - 1) {
                                val activity = activities[i]
                                if (activity is FlutterBoostActivity && activity.url == "canvas") {
                                    activity.finish()
                                }
                                if (activity is FlutterBoostActivity && activity.url == "printSetting") {
                                    activity.finish()
                                }
                            }
                            SyncTemplatePresenter.startNiimbotDrawActivity(ActivityUtils.getTopActivity(),printData, isRetainActivity = true, needDownloadFonts = false)
                            GlobalLoadingHelper.dismissLoading()
                        }
                    }else{
                        TemplateSyncLocalUtils.getTemplateDetails(labelId, isPersonalTemplate = false, needLoadFonts = false) { result, templateModule, errorMsg ->
                            GlobalScope.launch(Dispatchers.Main) {
                                if (result && templateModule != null) {
                                    val body = templateModule.toTemplateModuleLocal()
                                    withContext(Dispatchers.IO){
                                        TemplateSyncLocalUtils.downloadTemplateFonts(ActivityUtils.getTopActivity(),body)
                                    }
                                    printData.niimbotTemplate = mixTemplate(printData.niimbotTemplate,body)
                                    SyncTemplatePresenter.startNiimbotDrawActivity(ActivityUtils.getTopActivity(),printData, isFromDangerAPPJump = true, isRetainActivity = true, needDownloadFonts = false)
                                    GlobalLoadingHelper.dismissLoading()
                                }else{
                                    if (!errorMsg.isNullOrEmpty()) {
                                        com.niimbot.appframework_library.utils.showToast(errorMsg)
                                    }
                                    GlobalLoadingHelper.dismissLoading()
                                }
                            }

                        }
                    }



                }
                "openPrintPageWithLayout" -> {
                    val result = event.getJSONObject("data")
                    val labelId = result.getString("labelId")
                    var theme = result.getString("theme")
                    val uniappId = result.getString("uniappId")
                    val template = result.getString("template")
                    val isRePrint = result.getBoolean("isRePrint") ?: false
                    var isRePrintInt = if(isRePrint) 1 else 0

                    val printData = NiimbotDrawData()
                    printData.niimbotTemplate = TemplateModuleLocal.fromJson(template)!!

                    TemplateSyncLocalUtils.getTemplateDetails(labelId, isPersonalTemplate = false){ result, templateModule, errorMsg ->
                        if (result && templateModule != null) {
                            val body = templateModule.toTemplateModuleLocal()
                            printData.niimbotTemplate = mixTemplate(printData.niimbotTemplate,body)
                            //跳转小程序打印设置
                            try {
                                if(theme=="red"){
                                    theme="#FB4B42"
                                }else if(theme == "green"){
                                    theme = "#3E8C80"
                                }
//                                PrintTaskExecutor.capTaskId = data.getString("taskId")
                                var printScene = "mini_app"
                                var appName=  NiimbotGlobal.getUniappTitle(uniappId)
                                var niimbotDrawDataJson = any2Json(printData)
                                NiimbotGlobal.gotoFlutterPage(
                                    "printSettingDialog",
                                    hashMapOf(
                                        "printScene" to printScene,
                                        "showRfid" to true,
                                        "niimbotTemplate" to niimbotDrawDataJson,
                                        "uniAppInfo" to hashMapOf("uniAppId" to uniappId, "uniAppName" to appName,"meetingId" to "",
                                            "themeColor" to theme,"isRePrint" to isRePrintInt),
                                    ), isTransParent = true
                                )
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }

                        }else{
                            if (!errorMsg.isNullOrEmpty()) {
                                com.niimbot.appframework_library.utils.showToast(errorMsg)
                            }
                        }
                    }
                }
                "startDatasourceTemplatePrint" -> {
                    val result = event.getJSONObject("data")
                    val labelId = result.getString("labelId")
                    val codeId = result.getString("codeId")
                    var theme = result.getString("theme")
                    val uniappId = result.getString("uniappId")
                    val template = result.getString("template")

                    val printData = NiimbotDrawData()
                    printData.niimbotTemplate = TemplateModuleLocal.fromJson(template)!!
                    //跳转小程序打印设置
                    try {
                        if(theme=="red"){
                            theme="#FB4B42"
                        }else if(theme == "green"){
                            theme = "#3E8C80"
                        }
                        var printScene = "mini_app"
                        var appName=  NiimbotGlobal.getUniappTitle(uniappId)
                        var niimbotDrawDataJson = any2Json(printData)
                        NiimbotGlobal.gotoFlutterPage(
                            "printSettingDialog",
                            hashMapOf(
                                "printScene" to printScene,
                                "showRfid" to true,
                                "niimbotTemplate" to niimbotDrawDataJson,
                                "uniAppInfo" to hashMapOf("uniAppId" to uniappId, "uniAppName" to appName,"meetingId" to codeId,
                                    "themeColor" to theme),
                            ), isTransParent = true
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                "directStartDatasourceTemplatePrint" -> {
                    val result = event.getJSONObject("data")
                    val uniappId = result.getString("uniappId")
                    val template = result.getString("template")
                    try {
                        NiimbotGlobal.gotoFlutterPage(
                            "printNullUiShowProgress", hashMapOf(
                                "printScene" to "print_null_ui_show_progress",
                                "niimbotTemplate" to template,
                                "uniAppInfo" to hashMapOf("uniAppId" to uniappId)
                            )
                        )
                    }
                    catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                "openCanvasPage" -> {
                    var activities = ActivityUtils.getActivityList()
                    for (i in 0 until activities.size - 1) {
                        val activity = activities[i]
                        if (activity is FlutterBoostActivity && activity.url == "canvas") {
                            activity.finish()
                        }
                    }
                    if (NetworkUtils.isConnected()) {
                        val templateId = event.getString("templateId")
                        val defaultSelect = event.getString("defaultSelect")
                        FlutterEngineManager.jumpToCanvasEditPage(templateId, defaultSelect)
                    } else {
                        showToast("app01139")
                    }
                }
                "replaceLabelEvent" -> {
                    FlutterEventRegister.sendEvent(event)
                }


                WebPrintHelper.TAG_WEB_PRINT -> {

                    val templateStr = event.getString("templateData")
                    val taskId = event.getString("taskId")
                    val rfidEpc = event.getString("rfidEpc")
                    val appId = event.getString("appId") ?: "asset"
                    val density = event.getInteger("density")
                    MiniPrinterHelper.toPrint(templateStr = templateStr, taskId = taskId, appId = appId, rfidEpc = rfidEpc, printDensity = density)
                }

                WebPrintHelper.TAG_WEB_PRINTER_CONNECT -> {
                    LogUtils.e("=============连接打印机")
                    val filterPrinters = event.getString("filterPrinters")
                    val appId = event.getString("appId") ?: "web"
                    MiniPrinterHelper.toConnectPage(appId = appId, filterPrinterList = filterPrinters)
                }

                WebPrintHelper.TAG_UPLOAD_EXCEL_TO_WEB -> {
                    MiniPrinterHelper.toUploadExcel(event.toJSONString()){
                        EventBus.getDefault().post(JSActionEvent(JSAction.JS_SEND_UPLOAD_INFO, it))
                    }
                }
                "openGuziPage" -> {
                    val url = event.getString("url")
                    val suffix = event.getJSONObject("params")?.let {
                        val paramString = it.entries.joinToString("&") { entry -> "${entry.key}=${entry.value}" }
                        "&$paramString"
                    } ?: ""
                    if (url.isNullOrEmpty()) {
                        return
                    }

                    ActivityUtils.getTopActivity()?.let {
                        LoginDataEnum.loginCheck(it) {
                            FlutterMethodInvokeManager.getAuthCode {
                                ActivityUtils.getTopActivity()?.let { it1 ->
                                    val ionicWebUrl = PreferencesUtils.getString("custom_ionic_web_url", "")
                                    if (com.blankj.utilcode.util.AppUtils.isAppDebug() && !ionicWebUrl.isNullOrBlank()) {
                                        NiimbotGlobal.gotoWeb(
                                            it1,
                                            "$ionicWebUrl?authCode=$it$suffix",
                                            showShareIcon = false)
                                    } else {
                                        val target = if (StringUtil.isUrl(url)) {
                                            "$url?authCode=$it$suffix"
                                        } else {
                                            "https://asydy.jc-test.cn/h5?authCode=$it$suffix"
                                        }
                                        NiimbotGlobal.gotoWeb(it1, target, showShareIcon = false, showProgressBar = false)
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {}
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun imageDataForMiniAppCatering(data: JSObject){
        CapAppHelper.sendCapAppDataEvent(eventName = "batch-generate-preview-with-layout", data = data)
    }

    private fun mixTemplate(originTemplate:TemplateModuleLocal,labelTemplate:TemplateModuleLocal): TemplateModuleLocal {
        var mixTemplate: TemplateModuleLocal = TemplateModuleLocal()
        try {
            var copyDrawTemplate = originTemplate.copy()!!
            val remainElements = arrayListOf<BaseItemModuleEx>()
            mixTemplate = copyDrawTemplate
            copyDrawTemplate?.let {
                remainElements.addAll(it.elements)
                labelTemplate.cloudTemplateId = originTemplate?.cloudTemplateId ?: ""
                labelTemplate.id = copyDrawTemplate.id
                labelTemplate.name = copyDrawTemplate.name
                labelTemplate.templateVersion = copyDrawTemplate.templateVersion
                copyDrawTemplate.profile?.extrain?.templateType?.let {
                    labelTemplate.profile?.extrain?.templateType = it
                }
                copyDrawTemplate.profile?.extrain?.userId?.let {
                    labelTemplate.profile?.extrain?.userId = it
                }
                mixTemplate = labelTemplate.copy()!!
                mixTemplate?.updateExcelInfo(
                    copyDrawTemplate.externalData,
                    copyDrawTemplate.task,
                    copyDrawTemplate.currentPage,
                    copyDrawTemplate.totalPage
                )
                mixTemplate?.modify = copyDrawTemplate.modify
                mixTemplate?.dataSource = copyDrawTemplate.dataSource
                mixTemplate?.updateAllElements(remainElements)

            }
        }catch (e : Exception){
            Log.e("wangxuhao",""+e.printStackTrace())
        }

        return mixTemplate

    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: CheckIllegalRfidEvent) {
        if (BaseApplication.getInstance().mCurShowActivityReference?.get()?.javaClass?.simpleName != "MainActivity"
            || (BaseApplication.getInstance().mCurShowActivityReference?.get() as? MainActivity)?.isShowHomeFragment() == false
        ) {
            showToast("app100000719")
        }
    }

    private fun handleUnimpMessage(event: JSONObject) {
        try {
            val targetApi = event.getString("targetAppId")
            val eventName = event.getString("name")
            val data = event.getJSONObject("message")
            CapAppHelper.capApp?.appId?.let {
                if (targetApi.contains(it)) {
                    CapAppEventHandler.handleEvent(it, eventName, data)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

//    private fun handleSaveLabel(event: JSONObject) {
//        try {
//            GlobalScope.launch {
//                val labelData = TemplateModuleLocal.fromJson(event.getString("data"))
//                val drawData =
//                    TemplateModuleTransform.templateModuleLocalToTemplateModule(labelData!!)
//                drawData.add_time = TimeUtil.getLocalTimeString("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
//                drawData.update_time = drawData.add_time
//                TemplateDbUtils.insertOrUpdateTemplate(drawData)
//                TemplateUsedRecordUtils.saveUsedRecord(
//                    labelData.id,
//                    LoginDataEnum.isLogin
//                )
//            }
//
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }

    fun showShopCopunDialog(url: String, height: Float, needPadding: Boolean) {
        val realUrl = buildURL(url)
        val context = ActivityUtils.getTopActivity() as AppCompatActivity
        val dialog = DialogFactory.newBuild(context)
            .setLayout(R.layout.dialog_shop_copun)
            .setWidth(1f)
            .setHeight(height)
            .setDimAmount(0.5f)
            .setGravity(Gravity.BOTTOM)
            .create()
        if (height > 1) {
            dialog.setHeight(height.toInt())
        } else {
            dialog.setHeight(height)
        }
        dialog.getDialog().findViewById<TextView>(R.id.iv_close)
            .setOnNotDoubleClickListener {
                dialog.dismiss()
            }
        val wv_base = JCWebViewFactory.getInstance().createWebView(context)
        wv_base?.apply {
            clipRounded(SizeUtils.dp2px(16f))
            clearHistory()
            // 设置与Js交互的权限
            settings.javaScriptEnabled = true
            val originUA = settings.userAgentString
            if (!originUA.contains("AppId/com.gengcon.android.jccloudprinter", true)) {
                settings.userAgentString = originUA + " " + SystemUtil.getUserAgent(context)
            }
            var userAgentStr = settings.userAgentString
            webViewClient = BaseWebClient(context)
            webChromeClient = BaseWebChromeClient(context)
            addJavascriptInterface(JCWebViewInterface(), "android")
            val webContainer = dialog.getDialog().findViewById<FrameLayout>(R.id.webview_container)
            if (needPadding) {
                webContainer.setPadding(0, SizeUtils.dp2px(64f), 0, 0)
            }
            webContainer?.addView(
                this@apply, FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
            )
        }
        wv_base.loadUrl(realUrl)
        dialog.show()
    }

    private fun buildURL(originUrl: String): String {
        val urls = StringBuilder()
        urls.append(originUrl)
        //区别繁体中文和其他
        if (!urls.contains("lang=")) {
            val languageCode = TextHookUtil.getInstance().languageName
            urls.append("${if (urls.contains("?")) "&" else "?"}lang=" + languageCode)
        }
        if (!urls.contains("userAgent=")) {
            urls.append(
                "${if (urls.contains("?")) "&" else "?"}userAgent=${
                    SystemUtil.getUserAgent(
                        context
                    )
                }"
            )
        }
        if (LoginDataEnum.isLogin) {
            if (!urls.contains("token=")) {
                urls.append("${if (urls.contains("?")) "&" else "?"}token=" + HttpTokenUtils.getToken())
            }
        }
        return urls.toString()
    }

    private fun trackScanByShop(result: Int, scanType: Int) {
        val map = hashMapOf<String, Any>()
        map["source"] = 5
        map["type"] = scanType
        map["s_type"] = 2
        map["result"] = result
        BuriedHelper.trackEvent("show", "002_003_178", map)
    }

    inner class JCWebViewInterface {

        @JavascriptInterface
        fun nativeFunction(url: String) {
            NiimbotGlobal.routingByScheme(ActivityUtils.getTopActivity(), url, sourcePage = "028")
        }
    }

}
