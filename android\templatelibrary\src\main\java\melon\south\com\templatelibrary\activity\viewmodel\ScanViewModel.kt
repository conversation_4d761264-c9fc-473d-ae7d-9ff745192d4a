package melon.south.com.templatelibrary.activity.viewmodel

import android.app.Activity
import androidx.lifecycle.MediatorLiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.gengcon.print.draw.util.Text
import com.gengcon.print.draw.view.operate.menubar.detail.common.GoodsAttrMenuHandler
import com.google.zxing.Result
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.module.template.TemplateModuleR
import com.niimbot.appframework_library.common.module.template.external.DataSource
import com.niimbot.appframework_library.common.module.template.external.ModifyElement
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.ab.ABTestUtils
import com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading
import com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.templatecoordinator.transform.TemplateModuleTransform
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import melon.south.com.baselibrary.local.module.TemplateModule
import melon.south.com.baselibrary.module.TemplateExampleModule
import melon.south.com.baselibrary.module.TemplateExampleModuleNew
import melon.south.com.baselibrary.moduleEx.GoodsInfoR
import melon.south.com.baselibrary.util.AppDataUtil
import melon.south.com.baselibrary.util.showToast
import melon.south.com.templatelibrary.R
import melon.south.com.templatelibrary.activity.NewScanActivity

/**
 * @ClassName: ScanViewModel
 * @Author: Liuxiaowen
 * @Date: 2021/3/9 15:14
 * @Description:
 */
class ScanViewModel : ViewModel() {

    private var errorMsg = MutableLiveData<ErrorData>()
    private var scanResultData = MutableLiveData<ScanResultData>()
    private var ocrResultData = MutableLiveData<TemplateModule>()
    private var scanLoginResultData = MutableLiveData<String>()
    var fromUniapp = false

    var dataSource = MediatorLiveData<PageData>()
        private set

    init {
        dataSource.addSource(errorMsg) {
            dataSource.value = PageData(ActionType.SHOW_ERROR, errorMsg.value)
        }
        dataSource.addSource(scanResultData) {
            dataSource.value = PageData(ActionType.GET_SCAN_RESULT, scanResultData.value)
        }
        dataSource.addSource(ocrResultData) {
            dataSource.value = PageData(ActionType.GET_OCR_RESULT, ocrResultData.value)
        }
        dataSource.addSource(scanLoginResultData) {
            dataSource.value = PageData(ActionType.SCAN_LOGIN_RESULT, scanLoginResultData.value)
        }
    }


    fun checkScanResult(resId: Int, rawResult: Result, activity: NewScanActivity) {
        rawResult?.text?.let {
            when (resId) {
                R.id.tv_auto -> {
                    //自动识别
                    processAutoMode(rawResult, activity)
                }
                R.id.tv_scan -> {
                    //扫商品
                    processScanMode(rawResult, activity)
                }
                R.id.tv_ocr -> {
                    //识标签
                    processOCRMode(rawResult, activity)
                }
                else -> {
                }
            }
        } ?: run {
            if (!NetworkUtils.isConnected()) {
                errorMsg.value = ErrorData(-1, "app01139")
            } else {
                errorMsg.value = ErrorData(0, "app01139")
            }
        }
    }

    fun processQrCodeLogin(client: String = "PC", loginCode: String, activity: NewScanActivity){
        if (!NetworkUtils.isConnected()) {
            errorMsg.value = ErrorData(-1, "app01139")
            return
        }
        if(!LoginDataEnum.isLogin){
            activity.handleLogin = true
        }
        if(activity.hasHandleOcrResult){
            return
        }
        activity.hasHandleOcrResult = true
        LoginDataEnum.directLoginCheck{
            MainScope().launch { showLoading(activity) }
            FlutterMethodInvokeManager.loginScanCode(client, loginCode){result, error ->
                MainScope().launch { dismissLoading() }
                if(result){
                    scanLoginResultData.value = "$client&&$loginCode"
                }
                else{
                    errorMsg.value = ErrorData(6, error)
                    activity.hasHandleOcrResult = false
                }
            }
        }
    }

    private fun getEventTag(resId: Int) = when (resId) {
        R.id.tv_auto -> com.niimbot.baselibrary.Constant.buriedKey.TEMPALTE_SCAN_SCAN_CLICK
        R.id.tv_scan -> com.niimbot.baselibrary.Constant.buriedKey.TEMPALTE_SCAN_LABEL_CLICK
        R.id.tv_ocr -> com.niimbot.baselibrary.Constant.buriedKey.TEMPLATE_SCAN_COMMODITY_CLICK
        else -> ""
    }

    private fun processAutoMode(rawResult: Result, activity: NewScanActivity) {
        val oneCode = rawResult.text
        MainScope().launch { showLoading(activity) }
        TemplateSyncLocalUtils.getCloudTemplateByScanCode(
            oneCode,
            true
        ) { result, templateModule, errorMessage ->
            MainScope().launch { dismissLoading() }
            if (result) {
                ocrResultData.value = templateModule
            } else {
//                if (!com.niimbot.baselibrary.user.LoginDataEnum.isLogin) {
//                    errorMsg.value = "app00321"
//                } else {
                queryScanResult(rawResult, activity)
//                }
            }
        }
    }

    private fun processOCRMode(rawResult: Result, activity: NewScanActivity) {
        val oneCode = rawResult.text
        MainScope().launch { showLoading(activity) }
        TemplateSyncLocalUtils.getCloudTemplateByScanCode(
            oneCode,
            true
        ) { result, templateModule, errorMessage ->
            dismissLoading()
            if (result) {
                ocrResultData.value = templateModule
            } else {
                if (!errorMessage.isNullOrEmpty()) {
                    errorMsg.value = ErrorData(1, errorMessage)
                }
            }
        }
    }

    private fun processScanMode(rawResult: Result, activity: NewScanActivity) {
        queryScanResult(rawResult, activity)
    }

    private fun queryScanResult(rawResult: Result, activity: NewScanActivity) {
        val goodsCode = rawResult.text
        if (!NetworkUtils.isConnected()) {
            errorMsg.value = ErrorData(-1, "app01139")
            return
        }

        var getGoods = {
            MainScope().launch {
                if (activity.isDestroyed || activity.isFinishing) return@launch
                try {
                    showLoading(activity)
                }catch (e: Exception){

                }

            }
            TemplateSyncLocalUtils.getGoodsInfoByScanCode(
                goodsCode,
                needOldURL = fromUniapp
            ) { result, templateExampleModuleNew, _ ->
                MainScope().launch { dismissLoading() }
                if (!result) {
                    errorMsg.value = ErrorData(2, "app01160")
                    return@getGoodsInfoByScanCode
                }
                val applyTemplateExampleModuleNew =
                    templateExampleModuleNew ?: TemplateExampleModuleNew()
                val applyTemplateModule = applyTemplateExampleModuleNew.template_info
                val applyGoodsInfo = (applyTemplateExampleModuleNew.goods_info
                    ?: TemplateExampleModule()).apply { this.one_code = goodsCode }
                var tmp: NiimbotDrawData? = null

                if (applyTemplateModule != null) {
                    tmp = NiimbotDrawData()
                    tmp.niimbotTemplate = applyTemplateModule.toTemplateModuleLocal()
                    tmp.hasGoodsTemplate = true
                    tmp.niimbotGoodsInfo = applyGoodsInfo.getTextMap()

                    if (fromUniapp) {
                        scanResultData.value = ScanResultData(
                            goodsCode,
                            tmp,
                            applyGoodsInfo,
                            null != applyTemplateExampleModuleNew.goods_info
                        )
                    } else {
//                        ABTestUtils.startDrawingBoard { canEnterNewBoard ->
//                            var local = tmp!!.niimbotTemplate
//                            if (!canEnterNewBoard && (local.commodityTemplate || local.profile.extrain.templateType == 2) && local.dataSource!!.isNotEmpty()) {
//                                showToast("app100000343")
//                                errorMsg.value = ErrorData(2, "app100000343")
//                            } else {
//                                scanResultData.value = ScanResultData(
//                                    goodsCode,
//                                    tmp,
//                                    applyGoodsInfo,
//                                    null != applyTemplateExampleModuleNew.goods_info
//                                )
//                            }
//                        }
                        scanResultData.value = ScanResultData(
                            goodsCode,
                            tmp,
                            applyGoodsInfo,
                            null != applyTemplateExampleModuleNew.goods_info
                        )
                    }

                } else if (AppDataUtil.rfidTemplateModule != null) {

                    tmp = NiimbotDrawData()
                    tmp.niimbotTemplate = AppDataUtil.rfidTemplateModule!!.toTemplateModuleLocal()
//                    ABTestUtils.startDrawingBoard { canEnterNewBoard ->
//                        if (canEnterNewBoard) {
                            var templateModule =
                                TemplateModuleLocal().localToRemote(tmp.niimbotTemplate)
                            templateModule.dataSources?.clear()
                            val dataSource = DataSource()
                            dataSource.type = "commodity"
                            val map = mapOf(
                                "name" to applyTemplateExampleModuleNew.goodsInfoR?.name,
                                "norm" to applyTemplateExampleModuleNew.goodsInfoR?.norm,
                                "originPlace" to applyTemplateExampleModuleNew.goodsInfoR?.originPlace,
                                "retailPrice" to applyTemplateExampleModuleNew.goodsInfoR?.retailPrice
                            )
                            //如果id不为空，params优先使用id
                            var goodId = applyTemplateExampleModuleNew.goodsInfoR?.id
                            if (goodId?.isNotEmpty() == true) {
                                dataSource.params = mutableMapOf("ids" to listOf(goodId))
                            } else {
                                dataSource.params = mutableMapOf("commodity" to map)
                            }
//                            dataSource.params = mutableMapOf("commodity" to map)
                            templateModule.dataSources?.add(dataSource)

                            tmp.niimbotTemplate = TemplateModuleR().remoteToLocal(templateModule)
                            buildElements(
                                applyTemplateExampleModuleNew.goodsInfoR,
                                tmp.niimbotTemplate
                            )

                            tmp.niimbotTemplate.elements.forEachIndexed { index, it ->
                                val modifieMap = mutableMapOf(
                                    "0" to ModifyElement().apply {
                                        this.useTitle = false
                                        this.delimiter = "："
                                    },
                                )
                                templateModule.dataSourceModifies?.put(it.id, modifieMap)

                            }

                            tmp.niimbotTemplate.modify = templateModule.dataSourceModifies
                            tmp.niimbotTemplate.commodityTemplate = true
                            tmp.niimbotTemplate.name =
                                applyTemplateExampleModuleNew.goodsInfoR?.name
                                    ?: tmp.niimbotTemplate.name
                            tmp.niimbotGoodsInfo = applyGoodsInfo.getTextMap()
                            var transformTemplate =
                                TemplateModuleTransform.templateModuleLocalToTemplateModule(tmp.niimbotTemplate)
                            GlobalScope.launch(Dispatchers.IO) {
                                transformTemplate = TemplateSyncLocalUtils.writeModuleImg(transformTemplate, false)
                            }
                            tmp.niimbotTemplate = transformTemplate.toTemplateModuleLocal()
                            scanResultData.value = ScanResultData(
                                goodsCode,
                                tmp,
                                applyGoodsInfo,
                                null != applyTemplateExampleModuleNew.goods_info
                            )
//                        } else {
//
//                            tmp.niimbotGoodsInfo = applyGoodsInfo.getTextMap()
//                            scanResultData.value = ScanResultData(
//                                goodsCode,
//                                tmp,
//                                applyGoodsInfo,
//                                null != applyTemplateExampleModuleNew.goods_info
//                            )
//                        }
//                    }

                    //  tmp.niimbotGoodsInfo = applyGoodsInfo.getTextMap()
                } else {
                    scanResultData.value = ScanResultData(
                        goodsCode,
                        tmp,
                        applyGoodsInfo,
                        null != applyTemplateExampleModuleNew.goods_info
                    )
                }
            }
        }

        NiimbotGlobal.loginCancelCallback =  {
            errorMsg.value = ErrorData(5, "app01160")
        }
        LoginDataEnum.loginCheckConfirm(activity, "app100001162", { it ->
            if (!it) {
                errorMsg.value = ErrorData(5, "app01160")
                return@loginCheckConfirm
            }
        }, {
            if (LoginDataEnum.isLogin) {
                getGoods.invoke()
            }
        })
    }

    private fun buildElements(tmp: GoodsInfoR?, niimbotTemplate: TemplateModuleLocal) {
        if (tmp != null) {
            // 2021/10/9 Ice_Liu  新建的商品库模板 默认元素的开关为打开状态
            // 2022/5/31 Ice_Liu 默认商品模板只显示“品名、规格、产地、零售价”这几个字段
            GoodsAttrMenuHandler.resetAttrMap()
            niimbotTemplate.addElementTextItem(1, Text.GOODS_NAME.fledName)
            GoodsAttrMenuHandler.freshAttr(Text.GOODS_NAME.fledName, true)
            niimbotTemplate.addElementTextItem(2, Text.ORIGIN.fledName)
            GoodsAttrMenuHandler.freshAttr(Text.GOODS_NAME.fledName, true)
            niimbotTemplate.addElementTextItem(3, Text.NORM.fledName)
            GoodsAttrMenuHandler.freshAttr(Text.GOODS_NAME.fledName, true)
            niimbotTemplate.addElementTextItem(4, Text.RETAIL_PRICE.fledName)
            GoodsAttrMenuHandler.freshAttr(Text.GOODS_NAME.fledName, true)
        }
    }
}

enum class ActionType { SHOW_ERROR, GET_SCAN_RESULT, GET_OCR_RESULT, SCAN_LOGIN_RESULT }
data class ScanResultData(
    val goodsCode: String,
    val niimbotDrawData: NiimbotDrawData?,
    val applyGoodsInfo: TemplateExampleModule,
    val getGoods: Boolean
)

data class PageData(val actionType: ActionType, val data: Any?)
data class ErrorData(val code: Int, val msg: String)
