import 'package:flutter/material.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/common_fun.dart';
import 'package:flutter/cupertino.dart';

// ignore: must_be_immutable
class PrintHistorySetting extends StatefulWidget {
  bool isOpen;
  PrintHistorySetting({Key? key, this.isOpen = false}) : super(key: key);

  @override
  _PrintHistorySettingState createState() => _PrintHistorySettingState();
}

class _PrintHistorySettingState extends State<PrintHistorySetting> {

  bool isOpen = true;

  @override
  void initState() {
    super.initState();
    isOpen = widget.isOpen;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsetsDirectional.fromSTEB(12, 0, 12, 30),
      child: Container(
          decoration: const BoxDecoration(
            color: ThemeColor.background,
            borderRadius: BorderRadius.all(Radius.circular(18)),
          ),
          padding: EdgeInsetsDirectional.fromSTEB(20, 20, 20, 20),
          child: IntrinsicHeight(
              child: Column(
            children: [
              Text(
                intlanguage('app100001724', '打印记录设置'),
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF262626)),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0, 4, 0, 0),
                child:Container(
                  //color: Colors.red,
                  child: Row(
                  children: [
                    Text(
                      intlanguage('app100000396', '打印记录'),
                      textAlign: TextAlign.left,
                      style: const TextStyle(fontSize: 15, fontWeight: FontWeight.w400, color: Color(0xFF161616)),
                    ),
                    Spacer(),
                    Transform.scale(
                      scale: 0.8,
                      child: CupertinoSwitch(value: this.isOpen, activeColor: ThemeColor.brand, onChanged: (bool value) {
                      isOpen = value;
                      setState(() {
                        
                      });
                     }),
                    )
                  ],
                ),
                ),
               
              ),
              Text(
                intlanguage('app100001725', '关闭后，打印记录将无法存储到您的个人云端；且所 有打印记录将被清空。'),
               // textAlign: TextAlign.left,
                style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
              ),
              Container(
                height: 44,
                margin: EdgeInsets.only(top: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(right: 0.0),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.all(Radius.circular(10)),
                            ),
                            height: double.infinity, // 占满父容器高度
                            child: Center(
                              child: Text(
                                intlanguage('app100000692', '取消'),
                                style: const TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w400, color: Color(0xFF262626)),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 0, 0),//EdgeInsets.only(left: 10.0),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.pop(context,isOpen);
                          },
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.all(Radius.circular(10)),
                            ),
                            height: double.infinity, // 占满父容器高度
                            child: Center(
                              child: Text(
                                intlanguage("app00048", "确定"),
                                style: const TextStyle(
                                    fontSize: 17, fontWeight: FontWeight.w400, color: Color(0xFFFB4B42)),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ))),
    );
  }
}
