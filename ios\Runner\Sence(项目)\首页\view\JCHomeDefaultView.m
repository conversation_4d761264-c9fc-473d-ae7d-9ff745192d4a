//
//  JCHomeDefaultView.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/5/8.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//

#import "JCHomeDefaultView.h"
@interface JCHomeDefaultView()
@property (weak, nonatomic) IBOutlet UIButton *loginBtn;
@property (weak, nonatomic) IBOutlet UILabel *tipLabel;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *creatBtnWidth;
@property (weak, nonatomic) IBOutlet UIButton *emptyImgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *creatBtnTop;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *tipTop;
@property (weak, nonatomic) IBOutlet UIView *bgView;
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bgViewCenterY;

@end
@implementation JCHomeDefaultView

- (void)awakeFromNib{
    [super awakeFromNib];
    self.bgView.clipsToBounds = false;
    self.loginBtn.layer.cornerRadius = 6;
    self.loginBtn.layer.borderWidth = 1;
    self.loginBtn.layer.borderColor = COLOR_NEW_THEME.CGColor;
    [self refreshHomeDefaultView];
  
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshHomeDefaultView) name:JCNOTICATION_ChANGELANGUAGE object:nil];

}

- (void)refreshHomeDefaultView{
    NSMutableAttributedString *str = [[NSMutableAttributedString alloc] initWithString:XY_LANGUAGE_TITLE_NAMED(@"app01191", @"立即登录")];
    NSRange range1 = [[str string] rangeOfString:XY_LANGUAGE_TITLE_NAMED(@"app01191", @"立即登录")];
    [str addAttribute:NSForegroundColorAttributeName value:HEX_RGB(0x537FB7) range:range1];
    [self.loginBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191", @"立即登录") forState:UIControlStateNormal];
    if(xy_isLogin){
        self.bgViewCenterY.constant = 0;
        self.loginBtn.hidden = YES;
        self.emptyImgView.hidden = NO;
        self.tipLabel.font = [UIFont fontWithName:@"PingFang-SC-Medium" size:14];
        str = [[NSMutableAttributedString alloc] initWithString:@""];
        NSString *str = [XY_LANGUAGE_TITLE_NAMED(@"app00782", @"暂无模板") stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n" ];
        self.tipLabel.text = str;
    }else{
        self.bgViewCenterY.constant = -130;
        self.loginBtn.hidden = NO;
        self.emptyImgView.hidden = YES;
        self.tipLabel.font = [UIFont fontWithName:@"PingFang-SC-Regular" size:14];
        self.tipLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100002080", @"请登录后查看模板");
        [self.loginBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01191", @"立即登录") forState:UIControlStateNormal];
        CGSize creatSize = [XY_LANGUAGE_TITLE_NAMED(@"app01191", @"立即登录") sizeWithAttributes:@{NSFontAttributeName:self.loginBtn.titleLabel.font}];
        self.creatBtnWidth.constant = creatSize.width + 50;
       JC_TrackWithparms(@"show",@"003_005_463",@{});
    }
}

- (IBAction)createTagEvent:(id)sender {
    if(self.loginBlock){
        self.loginBlock(@"");
    }
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

@end
