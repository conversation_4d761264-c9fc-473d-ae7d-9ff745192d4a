/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Застосунку потрібен доступ до камери для сканування штрих-кодів, розпізнавання тексту та зйомки. Дозволити використання камери?";
NSBluetoothPeripheralUsageDescription = "Додатку потрібен доступ до Bluetooth для під'єднання принтера. Дозволити використання Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Додатку потрібен доступ до Bluetooth для під'єднання принтера. Дозволити використання Bluetooth?";
NSContactsUsageDescription = "Додатку потрібен доступ до контактів для читання списку контактів. Дозволити доступ до контактів?";
NSMicrophoneUsageDescription = "Додатку потрібен доступ до мікрофона для розпізнавання мови. Дозволити використання мікрофона?";
NSPhotoLibraryUsageDescription = "Цей дозвіл необхідний для друку зображень, розпізнавання штрих-кодів і QR-кодів, розпізнавання тексту і встановлення користувацького аватара. Виберіть «Дозволити доступ до всіх фотографій» для коректної роботи з галереєю в додатку «NIIMBOT». При виборі «Вибрати фотографії...» невибрані та нові фотографії будуть недоступні в додатку.";
NSLocationWhenInUseUsageDescription = "Для використання найближчих мереж Wi-Fi додатку «NIIMBOT» потрібен дозвіл на визначення місця розташування.";
NSLocationAlwaysUsageDescription = "Для використання найближчих мереж Wi-Fi додатку «NIIMBOT» потрібен дозвіл на визначення місця розташування.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Для використання найближчих мереж Wi-Fi додатку «NIIMBOT» потрібен дозвіл на визначення місця розташування.";
NSSpeechRecognitionUsageDescription = "Додатку потрібен ваш дозвіл для доступу до функції розпізнавання мови. Дозволити доступ до розпізнавання мови?";
NSLocalNetworkUsageDescription = "Цій програмі потрібен доступ до ​Локальної мережі (LAN)​​ для служб пошуку пристроїв у локальній мережі та налаштування мережі.";
UILaunchStoryboardName = "LaunchScreen";
