import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:image/image.dart' as img;
import 'package:netal_plugin/netal_image_result.dart';
import 'package:niimbot_template/models/elements/base_element.dart';
import 'package:niimbot_template/models/elements/image_element.dart';
import 'package:niimbot_template/models/elements/qr_code_element.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:niimbot_template/template_generate.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/log_utils.dart';
import 'package:text/pages/my_template/model/template_data_extension.dart';
import 'package:text/template/constant/template_local_type.dart';
import 'package:text/template/model/api_error.dart';
import 'package:text/template/model/template_detail_result.dart';
import 'package:text/template/model/template_list_model.dart';
import 'package:text/template/model/template_operation_status.dart';
import 'package:text/template/service/form_service.dart';
import 'package:text/template/service/live_code_service.dart';
import 'package:text/template/service/template_service.dart';
import 'package:text/template/util/template_image_utils.dart';
import 'package:text/template/util/template_misc_utils.dart';
import 'package:text/template/util/template_path_generator_utils.dart';
import 'package:text/template/util/template_sync_service.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/event_bus.dart';

import '../utils/toast_util.dart';
import 'util/template_detail_db_utils.dart';

class TemplateManager {
  TemplateManager._();

  static final TemplateManager _instance = TemplateManager._();

  factory TemplateManager() => _instance;

  static TemplateManager get instance => _instance;
  static TemplateService templateService = TemplateService();
  List<String> toServiceTemplateIdList = [];

  Future<bool> checkContainFile(String? templateId, {bool isEtag = false}) async {
    if (templateId == null || templateId.isEmpty) {
      return false;
    }
    if (isEtag) {
      SharedPreferences sp = await SharedPreferences.getInstance();
      return sp.getBool("etag_$templateId") ?? false;
    } else {
      return TemplateDetailDBUtils.checkTemplateExist(templateId);
    }
  }

  ///复制模版
  Future<TemplateData> copyTemplate(
      TemplateData templateData, Function(TemplateOperationStatus status, TemplateData? template)? callback) async {
    //更改模版id,时间等必要信息
    String templateId = DateTime.now().millisecondsSinceEpoch.toString();
    bool hasVipRes = templateData.hasVipElementV2();
    TemplateData copyTemplate = templateData.copyWith(id: templateId, local_type: TemplateLocalType.CREATE,name: "${templateData.name}-1", hasVipRes: hasVipRes);
    String createTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
    copyTemplate.profile.extra.createTime = createTime;
    copyTemplate.profile.extra.updateTime = createTime;
    await TemplateDetailDBUtils.insertTemplate(copyTemplate);
    if (callback != null) {
      callback(TemplateOperationStatus.localSuccess, copyTemplate);
    }
    bool isNetConnected = await _checkNetworkConnected();
    if (isNetConnected) {
      //调用服务端创建模版接口
      copyTemplate = await TemplateMiscUtils.processDataSourceUrl(copyTemplate);
      String serviceTemplateId = await templateService.createTemplateToService(copyTemplate, false);
      copyTemplate = await TemplateDetailDBUtils.createSyncServiceTemplateId(copyTemplate, serviceTemplateId);
      if (callback != null) {
        callback(TemplateOperationStatus.serverSuccess, copyTemplate);
      }
    }
    return copyTemplate;
  }

  ///新建模版
  Future<TemplateData>
  createTemplate(TemplateData templateData,
      {bool isEtag = false,
      bool keepFolderId = false,
      Function(TemplateOperationStatus status, TemplateData? template)? callback,
    bool isFromCanvas = false}) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    bool hasVipRes = templateData.hasVipElementV2();
    String oldId1 = templateData.id ?? "";
    templateData = templateData.copyWith(id: DateTime.now().millisecondsSinceEpoch.toString(), hasVipRes: hasVipRes);
    String templateVersion = templateData.getTemplateVersion();
    //生成模版缩略图
    String localTemplateThumbnailPath =
        await generateTemplatePreviewThumbnail(templateData, page: templateData.getCurrentPage());
    TemplateData template =
        templateData.copyWith(localThumbnail: localTemplateThumbnailPath, templateVersion: templateVersion);
    TemplateData uploadTemplate = template;
    if (!isEtag) {
      //模版入库，标记local_type=create状态
      uploadTemplate = await TemplateDetailDBUtils.markCreateTemplate(uploadTemplate, keepFolderId);
      uploadTemplate = await checkPdfImagePath(uploadTemplate);
      File localThumbFile =
          await TemplatePathGeneratorUtils.getThumbFile(uploadTemplate.id!, uploadTemplate.profile.extra.updateTime);
      File(localTemplateThumbnailPath).renameSync(localThumbFile.absolute.path);
      uploadTemplate = uploadTemplate.copyWith(localThumbnail: localThumbFile.absolute.path);
      //更新路径
      await TemplateDetailDBUtils.insertOrUpdateTemplate(uploadTemplate);
      if (callback != null) {
        callback(TemplateOperationStatus.localSuccess, uploadTemplate);
      }
    }
    //离线或者未登录逻辑
    bool isNetConnected = await _checkNetworkConnected();
    if (isNetConnected && Application.isLogin) {
      //上传模版相关图片资源
      String oldId2 = uploadTemplate.id??"";
      uploadTemplate = await TemplateImageUtils.uploadTemplateImages(uploadTemplate);
      //调用服务端创建模版接口
      try{
        String serviceTemplateId = await templateService.createTemplateToService(uploadTemplate, isEtag);
        if (isEtag) {
          //标记改电子价签模版本地已存在  电子价签模版不入库
          SharedPreferences sp = await SharedPreferences.getInstance();
          sp.setBool("etag_$serviceTemplateId", true);
          uploadTemplate = uploadTemplate.copyWith(id: serviceTemplateId);
        } else {
          //更新模版id
          debugPrint("templateJson: 模板开始更新本地库");
          uploadTemplate = await TemplateDetailDBUtils.createSyncServiceTemplateId(uploadTemplate, serviceTemplateId);
          debugPrint("templateJson: 模板通知更新Id");
          sp.setString("createTemplate", jsonEncode({"oldId1": oldId1, "oldId2": oldId2, "newId": serviceTemplateId}));
        }
        if (callback != null) {
          callback(TemplateOperationStatus.serverSuccess, uploadTemplate);
        }
      }catch(e,s){
        Log.d("创建模版失败 templateId = ${templateData.id}");
      }
      return uploadTemplate;
    } else {
      return uploadTemplate;
    }
  }

  checkPdfImagePath(TemplateData uploadTemplate) async {
    bool needChangeElementPath = false;
    List<BaseElement> elements = [];
    for (BaseElement element in uploadTemplate.elements) {
      //判断pdf图片在原始路径下 则在保存时拷贝至元素路径 防止退出画板  pdf文件删除导致图片丢失
      if (element is ImageElement &&
          element.localImageUrl.contains("/pdf_image/") &&
          File(element.localImageUrl).existsSync()) {
        String oldFilePath = element.localImageUrl;
        String newFolderPath = (await TemplatePathGeneratorUtils.elementFile()).path;
        newFolderPath =
            await copyImagePreservingName(oldFilePath, newFolderPath, "element_${uploadTemplate.id}_${element.id}.png");
        needChangeElementPath = true;
        element = element.copyWith(localImageUrl: newFolderPath);
      }
      elements.add(element);
    }
    if (needChangeElementPath) {
      uploadTemplate = uploadTemplate.copyWith(elements: elements);
    }
    return uploadTemplate;
  }

  //将PDF图片拷贝到元素图路径
  Future<String> copyImagePreservingName(String sourcePath, String destinationDirPath, String newFileName) async {
    final sourceFile = File(sourcePath);

    if (!await sourceFile.exists()) {
      print('源文件不存在: $sourcePath');
      return sourcePath;
    }

    // 构造目标路径
    final destinationDir = Directory(destinationDirPath);
    if (!await destinationDir.exists()) {
      await destinationDir.create(recursive: true);
    }

    final destinationPath = path.join(destinationDirPath, newFileName);

    // 执行复制
    await sourceFile.copy(destinationPath);
    print('图片已复制到: $destinationPath');
    return destinationPath;
  }

  ///同步离线创建的模版
  Future<TemplateData> syncOfflineCreateTemplate(TemplateData templateData) async {
    //上传模版相关图片资源
    TemplateData uploadTemplate = await TemplateImageUtils.uploadTemplateImages(templateData);
    uploadTemplate = await TemplateMiscUtils.processDataSourceUrl(uploadTemplate);
    //调用服务端创建模版接口
    String serviceTemplateId = await templateService.createTemplateToService(uploadTemplate, false);
    uploadTemplate = await TemplateDetailDBUtils.createSyncServiceTemplateId(uploadTemplate, serviceTemplateId);
    return uploadTemplate;
  }

  ///同步离线更新的模版
  Future<TemplateData> syncOfflineUpdateTemplate(TemplateData templateData) async {
    //上传模版相关图片资源
    TemplateData uploadTemplate = await TemplateImageUtils.uploadTemplateImages(templateData);
    uploadTemplate = await TemplateMiscUtils.processDataSourceUrl(uploadTemplate);
    //调用服务端更新模版接口
    uploadTemplate = await templateService.updateTemplateToService(uploadTemplate, false);
    uploadTemplate = await TemplateDetailDBUtils.updateTemplate(uploadTemplate);
    return uploadTemplate;
  }

  ///更新模版
  Future<TemplateData> updateTemplate(TemplateData templateData,
      {bool isEtag = false, Function(TemplateOperationStatus status, TemplateData? template)? callback,bool isFromCanvas = false}) async {
    String templateVersion = templateData.getTemplateVersion();
    //生成模版缩略图
    String localTemplateThumbnailPath =
        await generateTemplatePreviewThumbnail(templateData, page: templateData.getCurrentPage());
    bool hasVipRes = templateData.hasVipElementV2();
    TemplateData template = templateData.copyWith(localThumbnail: localTemplateThumbnailPath, hasVipRes: hasVipRes, templateVersion: templateVersion);
    TemplateData uploadTemplate = template;
    if (!isEtag) {
      //本地更新模版，标记local_type=update状态
      uploadTemplate = await TemplateDetailDBUtils.markUpdateTemplate(uploadTemplate);
      uploadTemplate = await checkPdfImagePath(uploadTemplate);
      File localThumbFile =
          await TemplatePathGeneratorUtils.getThumbFile(uploadTemplate.id!, uploadTemplate.profile.extra.updateTime);
      File(localTemplateThumbnailPath).renameSync(localThumbFile.absolute.path);
      uploadTemplate = uploadTemplate.copyWith(localThumbnail: localThumbFile.absolute.path);
      await TemplateDetailDBUtils.insertOrUpdateTemplate(uploadTemplate);
      if (callback != null) {
        callback(TemplateOperationStatus.localSuccess, uploadTemplate);
      }
    }
    //离线或者未登录逻辑
    bool isNetConnected = await _checkNetworkConnected();
    if (isNetConnected && Application.isLogin) {
      //上传模版相关图片资源
      uploadTemplate = await TemplateImageUtils.uploadTemplateImages(uploadTemplate);
      if (!isEtag) {
        //更新图片oss地址
        uploadTemplate = await TemplateDetailDBUtils.insertOrUpdateTemplate(uploadTemplate);
      }
      //调用服务端更新模版接口
      try{
        uploadTemplate = await templateService.updateTemplateToService(uploadTemplate, isEtag);
        uploadTemplate = await TemplateDetailDBUtils.updateTemplate(uploadTemplate);
        if (callback != null) {
          callback(TemplateOperationStatus.serverSuccess, uploadTemplate);
        }
      }catch(e,s){
        Log.d("更新模版失败 templateId = ${templateData.id}");
      }
      return uploadTemplate;
    } else {
      return uploadTemplate;
    }
  }

  ///删除模版
  Future<void> delTemplate(TemplateData templateData) async {
    //调用服务端删除模版接口
    await templateService.deleteTemplateToService(templateData);
    //从数据库中删除该模版
    await TemplateDetailDBUtils.deleteTemplate(templateData);
  }

  ///生成模版缩略图
  Future<String> generateTemplatePreviewThumbnail(TemplateData templateData,
      {int page = 1, Color? firstRfidColor, Color? secondRfidColor}) async {
    Color? contentColor = RfidManager.instance.getCarbonARGBForPrint();

    int index = templateData.multipleBackIndex;
    String backgroundLocal = "";
    if (templateData.localBackground.isNotEmpty && index >= 0 && index < templateData.localBackground.length) {
      backgroundLocal = templateData.localBackground[index];
    }
    backgroundLocal = backgroundLocal.trim();
    int canvasRotate = (templateData.canvasRotate % 360).toInt();
    if (backgroundLocal.isNotEmpty && canvasRotate != 0) {
      File labelBackgroundFile = await TemplatePathGeneratorUtils.getLabelBackgroundFile(
          templateData.profile.extra.labelId ?? "", canvasRotate);
      await rotateImageFile(backgroundLocal, labelBackgroundFile.absolute.path, canvasRotate);
      backgroundLocal = labelBackgroundFile.absolute.path;
    }
    NetalImageResult? result = null;
    List<Color>? doubleColorForThumb = RfidManager.instance.getPrintDoubleColorForThumb();
    if (doubleColorForThumb?.length == 2) {
      result = await TemplateGenerate.generateBlackRedPreviewImageAsync(templateData, doubleColorForThumb![0], doubleColorForThumb[1],
          page: page, localBackgroundImageUrl: backgroundLocal);
    } else {
      result = await TemplateGenerate.generatePreviewImageAsync(templateData,
          page: page, color: contentColor, localBackgroundImageUrl: backgroundLocal);
    }

    var imageData = result.pixels;
    if (imageData.length > 0) {
      File thumbnailFile =
          await TemplatePathGeneratorUtils.getThumbFile(templateData.id!, templateData.profile.extra.updateTime);
      await thumbnailFile.create(recursive: true);
      await thumbnailFile.writeAsBytes(imageData, flush: true);
      return thumbnailFile.absolute.path;
    } else {
      return "";
    }
  }

  ///根据角度旋转图片
  Future rotateImageFile(String imageSrc, String imageDes, num angle) async {
    File imageDesFile = File(imageDes);
    if (imageDesFile.existsSync()) {
      return null;
    } else {
      final cmd = img.Command()
        ..decodeImageFile(imageSrc)
        ..copyRotate(angle: angle)
        ..writeToFile(imageDes);
      return await cmd.executeThread();
    }
  }

  Future<bool> _checkNetworkConnected() async {
    ConnectivityResult connectivityResult = await Connectivity().customCheckConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  ///离线数据同步
  Future<void> localFileToService() async {
    bool localFileToService = await TemplateSyncService().localFileToService();
    if(localFileToService){
      NiimbotEventBus.getDefault().post({"myTemplateRefresh": ""});
    }
    ToNativeMethodChannel.refreshNativeTemplateList();
  }

  Future<int?> getLastRequestFileListServerTime(int userId) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    return sp.getInt(_saveServerTimeKey(userId));
  }

  Future<void> saveRequestFileListServerTime(int userId, int serverTime) async {
    SharedPreferences sp = await SharedPreferences.getInstance();
    sp.setInt(_saveServerTimeKey(userId), serverTime);
  }

  String _saveServerTimeKey(int userId) {
    return "c1_file_list_server_time_$userId";
  }

  /**
   * 下载模版资源（背景、图片、表单信息、活码信息）
   */
  Future<TemplateData> downloadTemplateRes(TemplateData templateData) async {
    TemplateData downloadTemplate = await TemplateImageUtils.downloadTemplateImages(templateData);
    List<String> formIds = [];
    List<String> liveCodeIds = [];
    for (var element in templateData.elements) {
      if (element is QRCodeElement) {
        if (element.isForm) {
          formIds.add(element.formId ?? "");
        } else if (element.isLive) {
          liveCodeIds.add(element.liveCodeId ?? "");
        }
      }
    }
    if (formIds.isNotEmpty) {
      await FormService.getFormByIds(formIds.join(","));
    }
    if (liveCodeIds.isNotEmpty) {
      await LiveCodeService.getLiveCodeByIds(liveCodeIds);
    }
    // await FormService.getFormByIds("1619");
    // await LiveCodeService.getLiveCodeByIds(["6757f5e0d337d0f9437559f4"]);
    return downloadTemplate;
  }

  Future<TemplateData?> getTemplateDetail(String templateId,
      {bool needUpdateDb = false, bool fromLocal = false}) async {
    //先从数据库中查询，再从网络获取
    TemplateData? localTemplate = await TemplateDetailDBUtils.queryTemplateById(templateId);
    bool isNetConnected = await _checkNetworkConnected();
    bool canEditTemplate = await localTemplate?.canEdit() ?? false;
    if (!isNetConnected || fromLocal || (localTemplate != null && canEditTemplate && localTemplate.isLocalTemplate())) {
      return localTemplate;
    }
    TemplateData? remoteTemplate;
    if (localTemplate != null && localTemplate.local_type == TemplateLocalType.SYNC
        && !localTemplate.isExcel()
        && !localTemplate.elements.isEmpty) {
      if ((localTemplate.profile.extra.labelId?.isNotEmpty ?? false) && localTemplate.backgroundImage.isEmpty) {
        remoteTemplate = await templateService.fetchTemplateDetailById(templateId);
      } else {
        remoteTemplate =
        await templateService.fetchTemplateUpdateStatus(templateId, localTemplate.profile.extra.updateTime ?? "");
      }
      remoteTemplate = remoteTemplate ?? localTemplate;
    } else {
      remoteTemplate = await templateService.fetchTemplateDetailById(templateId);
    }
    if (remoteTemplate == null) {
      return localTemplate;
    }
    remoteTemplate = await downloadTemplateRes(remoteTemplate);
    if (needUpdateDb) {
      remoteTemplate = remoteTemplate.copyWith(local_type: TemplateLocalType.SYNC);
      await TemplateDetailDBUtils.insertTemplate(remoteTemplate);
    }
    return remoteTemplate;
  }

  // 下载模版资源
  Future<(bool isSuccess, TemplateData? remoteTemplate)> downloadTemplateResAndUpdateTemplate(
      TemplateData? remoteTemplate,
      {bool needUpdateDb = true}) async {
    if (remoteTemplate == null) {
      return (false, null);
    }
    remoteTemplate = await downloadTemplateRes(remoteTemplate);
    if (needUpdateDb) {
      // remoteTemplate.local_type = TemplateLocalType.SYNC;
      await TemplateDetailDBUtils.insertTemplate(remoteTemplate);
    }
    return (true, remoteTemplate);
  }

  /**
   * 本地库根据模版id查询模版
   * templateId--模版id
   */
  Future<TemplateDetailResult> queryTemplateById(String templateId) async {
    TemplateDetailResult templateDetailResult = TemplateDetailResult();
    TemplateData? templateData = await TemplateDetailDBUtils.queryTemplateById(templateId);
    if (templateData != null) {
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = templateData;
      return templateDetailResult;
    }
    return templateDetailResult;
  }

  /**
   * 扫码获取标签详情
   * onecode--扫的条码
   */
  Future<TemplateDetailResult> queryLableDetailByScanCode(String oneCode) async {
    TemplateDetailResult templateDetailResult = TemplateDetailResult();
    List<TemplateData> cloudTs = await TemplateDetailDBUtils.queryRfidTemplateByScanCode(oneCode);
    if (cloudTs.isNotEmpty) {
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = cloudTs[0];
      return templateDetailResult;
    }
    return templateDetailResult;
  }

  /**
   * 扫码获取模版详情
   * onecode--扫的条码
   * needUpdateDb--是否需要更新表
   */
  Future<TemplateDetailResult> getTemplateDetailByScanCode(String oneCode, {bool needUpdateDb = false}) async {
    TemplateDetailResult templateDetailResult = TemplateDetailResult();
    bool isNetConnected = await _checkNetworkConnected();
    List<TemplateData> cloudTs = await TemplateDetailDBUtils.queryCloudTemplateByScanCode(oneCode);
    TemplateData? firstCloudT = cloudTs.firstOrNull;
    bool canEditTemplate = await firstCloudT?.canEdit() ?? false;
    if (cloudTs.isNotEmpty && !isNetConnected && canEditTemplate) {
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = cloudTs[0];
      return templateDetailResult;
    }
    try {
      TemplateData? remoteTemplate = await templateService.fetchCloudTemplateByScanCode(oneCode);
      if (remoteTemplate == null) {
        templateDetailResult.isSuccess = false;
        templateDetailResult.templateData = null;
        templateDetailResult.errorMsg = "app00321";
      } else {
        remoteTemplate = await downloadTemplateRes(remoteTemplate);
        if (needUpdateDb) {
          remoteTemplate = remoteTemplate.copyWith(local_type: TemplateLocalType.SYNC);
          await TemplateDetailDBUtils.insertTemplate(remoteTemplate);
        }
        templateDetailResult.isSuccess = true;
        templateDetailResult.templateData = remoteTemplate;
      }
    } catch (e, s) {
      templateDetailResult.isSuccess = false;
      templateDetailResult.templateData = null;
      if (e is ApiError) {
        templateDetailResult.errorMsg = e.msg;
        templateDetailResult.errorCode = e.code;
      }
    }
    return templateDetailResult;
  }

  Future<TemplateDetailResult> getTemplateDetailByScanCodeOrLabelId(String oneCode, String labelId,
      {bool needUpdateDb = false, bool fromLocal = false}) async {
    TemplateDetailResult templateDetailResult = TemplateDetailResult();
    bool isNetConnected = await _checkNetworkConnected();
    List<TemplateData> cloudTs = await TemplateDetailDBUtils.queryCloudTemplateByScanCode(oneCode);
    if(cloudTs.isEmpty){
      TemplateData? cloudT = await TemplateDetailDBUtils.queryTemplateById(labelId);
      if(cloudT != null){
        cloudTs.add(cloudT);
      }
    }
    TemplateData? firstCloudT = cloudTs.firstOrNull;
    bool canEditTemplate = await firstCloudT?.canEdit() ?? false;
    if (cloudTs.isNotEmpty &&  canEditTemplate && (!isNetConnected || fromLocal)) {
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = cloudTs[0];
      return templateDetailResult;
    }
    try {
      late TemplateData? remoteTemplate;
      if (labelId.isEmpty || labelId == "0") {
        remoteTemplate = await templateService.fetchCloudTemplateByScanCode(oneCode);
      } else {
        remoteTemplate = await templateService.fetchTemplateDetailById(labelId, isNewApi: false);
      }
      if (remoteTemplate == null) {
        templateDetailResult.isSuccess = false;
        templateDetailResult.templateData = null;
        templateDetailResult.errorMsg = "app00321";
      } else {
        remoteTemplate = await downloadTemplateRes(remoteTemplate);
        if (needUpdateDb) {
          remoteTemplate = remoteTemplate.copyWith(local_type: TemplateLocalType.SYNC);
          await TemplateDetailDBUtils.insertTemplate(remoteTemplate);
        }
        templateDetailResult.isSuccess = true;
        templateDetailResult.templateData = remoteTemplate;
      }
    } catch (e, s) {
      templateDetailResult.isSuccess = false;
      templateDetailResult.templateData = null;
      if (e is ApiError) {
        templateDetailResult.errorMsg = e.msg;
        templateDetailResult.errorCode = e.code;
      }
    }

    return templateDetailResult;
  }

  Future<TemplateDetailResult> getTemplateDetailWithParams(String templateId,
      {bool isPersonalTemplate = false, bool isShare = false, isFolderShare = false}) async {
    TemplateDetailResult templateDetailResult = TemplateDetailResult();
    //先从数据库中查询，再从网络获取
    TemplateData? localTemplate = await TemplateDetailDBUtils.queryTemplateById(templateId);
    bool isNetConnected = await _checkNetworkConnected();
    bool canEditTemplate = await localTemplate?.canEdit() ?? false;
    if (!isNetConnected || (localTemplate != null && canEditTemplate && localTemplate.isLocalTemplate())) {
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = localTemplate;
      return templateDetailResult;
    }
    TemplateData? remoteTemplate;
    try {
      if (isFolderShare) {
        remoteTemplate = await templateService.fetchFolderShareTemplateDetailById(templateId);
      } else {
        if (localTemplate != null && localTemplate.local_type == TemplateLocalType.SYNC
            && !localTemplate.isExcel()
            && !localTemplate.elements.isEmpty) {
          if ((localTemplate.profile.extra.labelId?.isNotEmpty ?? false) && localTemplate.backgroundImage.isEmpty) {
            remoteTemplate = await templateService.fetchTemplateDetailById(templateId);
          } else {
            remoteTemplate =
                await templateService.fetchTemplateUpdateStatus(templateId, localTemplate.profile.extra.updateTime ?? "");
          }
          remoteTemplate = remoteTemplate ?? localTemplate;
        } else {
          remoteTemplate = await templateService.fetchTemplateDetailById(templateId);
        }
      }
      if (remoteTemplate == null) {
        templateDetailResult.isSuccess = false;
        templateDetailResult.templateData = null;
        return templateDetailResult;
      }
      if (!isPersonalTemplate ||
          remoteTemplate.profile.extra.userId != (Application.user?.id ?? -1).toInt().toString()) {}
      remoteTemplate = await downloadTemplateRes(remoteTemplate);
      if (!isShare) {
        remoteTemplate = remoteTemplate.copyWith(local_type: TemplateLocalType.SYNC);
        await TemplateDetailDBUtils.insertTemplate(remoteTemplate);
      }
      templateDetailResult.isSuccess = true;
      templateDetailResult.templateData = remoteTemplate;
    } catch (e, s) {
      templateDetailResult.isSuccess = false;
      templateDetailResult.templateData = null;
      if (e is ApiError) {
        templateDetailResult.errorMsg = e.msg;
        templateDetailResult.errorCode = e.code;
      }
    }
    return templateDetailResult;
  }

  Future<TemplateListModel> getMyGoodTemplateList(int commodityTemplate, int page, int limit,
      {String searchKey = ""}) async {
    //先从数据库中查询，再从网络获取
    List<TemplateData> localTemplates = [];
    TemplateListModel templateListModel = TemplateListModel();
    try {
      templateListModel = await templateService.fetchMyGoodTemplateList(page, limit, 0, commodityTemplate, searchKey);
      List<String> templateIds = [];
      //找出本地已经是最新的数据Id
      await Future.forEach(templateListModel.list!, (template) async {
        TemplateData? localT = await TemplateDetailDBUtils.queryTemplateById(template.id!);
        if (localT?.local_type == TemplateLocalType.UPDATE) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.DELETE) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.SYNC &&
            template.profile.extra.updateTime == localT?.profile.extra.updateTime) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.DEFAULT &&
            template.profile.extra.updateTime == localT?.profile.extra.updateTime &&
            (localT?.localThumbnail ?? "").isNotEmpty &&
            !File(localT!.localThumbnail).existsSync()) {
          templateIds.add(template.id!);
        }
      });
      List<TemplateData>? filterTs = templateListModel.list?.where((template) {
        if (templateIds.contains(template.id!)) {
          return false;
        }
        return true;
      }).toList();
      if (filterTs != null) {
        // 下载模版缩略图
        filterTs = await downloadTemplateThumbnails(filterTs);
        await TemplateDetailDBUtils.batchInsertTemplate(filterTs);
      }
      localTemplates = await TemplateDetailDBUtils.queryMyGoodTemplateList(page, pageSize: limit, searchKey: searchKey);
      int count = await TemplateDetailDBUtils.countMyGoodTemplateList(searchKey: searchKey);
      templateListModel.total = max(templateListModel.total ?? 0, count);
      templateListModel.list = localTemplates;
      templateListModel.deletedTemplates = [];
      templateListModel.hasMore =
          (templateListModel.page ?? 0) * (templateListModel.limit ?? 0) < (templateListModel.total ?? 0);
    } catch (e, s) {
      localTemplates = await TemplateDetailDBUtils.queryMyGoodTemplateList(page, pageSize: limit, searchKey: searchKey);
      int count = await TemplateDetailDBUtils.countMyGoodTemplateList(searchKey: searchKey);
      templateListModel
        ..total = count
        ..page = page
        ..limit = limit
        ..list = localTemplates
        ..deletedTemplates = []
        ..hasMore = true;
    }

    return templateListModel;
  }

  Future<TemplateListModel> getMyTemplateList(int page, int limit,
      {int folderId = -1, bool fromLocal = false, bool onlyNetwork = false}) async {
    List<TemplateData> localTemplates = [];
    TemplateListModel templateListModel = TemplateListModel();
    try {
      bool isNetConnected = await _checkNetworkConnected();
      if (!isNetConnected || fromLocal || !Application.isLogin) {
        localTemplates = await TemplateDetailDBUtils.queryMyTemplateList(page, pageSize: limit);
        int count = await TemplateDetailDBUtils.countMyTemplateList();
        templateListModel
          ..total = count
          ..page = page
          ..limit = limit
          ..list = localTemplates
          ..deletedTemplates = []
          ..hasMore = true;
        if (templateListModel.list?.isNotEmpty ?? true) {
          return templateListModel;
        }
      }

      templateListModel = await templateService.fetchMyTemplateList(page, limit, folderId: folderId);
      if (onlyNetwork || fromLocal) {
        return templateListModel;
      }
      List<String> templateIds = [];
      //找出本地已经是最新的数据Id
      await Future.forEach(templateListModel.list!, (template) async {
        TemplateData? localT = await TemplateDetailDBUtils.queryTemplateById(template.id!);
        if (localT?.local_type == TemplateLocalType.UPDATE) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.DELETE) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.SYNC &&
            template.profile.extra.updateTime == localT?.profile.extra.updateTime) {
          templateIds.add(template.id!);
        } else if (localT?.local_type == TemplateLocalType.DEFAULT &&
            template.profile.extra.updateTime == localT?.profile.extra.updateTime &&
            (localT?.localThumbnail ?? "").isNotEmpty &&
            !File(localT!.localThumbnail).existsSync()) {
          debugPrint("本地状态非默认 或 存在更新、或本地图片不存在");
          templateIds.add(template.id!);
        }
      });
      List<TemplateData>? filterTs = templateListModel.list?.where((template) {
        if (templateIds.contains(template.id!)) {
          return false;
        }
        return true;
      }).toList();
      if (filterTs?.isNotEmpty ?? false) {
        // 下载模版缩略图
        filterTs = await downloadTemplateThumbnails(filterTs ?? []);
        await TemplateDetailDBUtils.batchInsertTemplate(filterTs ?? []);
      }
      if (templateListModel.deletedTemplates?.isNotEmpty ?? false) {
        await TemplateDetailDBUtils.batchDeleteTemplateByIds(templateListModel.deletedTemplates!);
      }
      localTemplates = await TemplateDetailDBUtils.queryMyTemplateList(page, pageSize: limit);
      int count = await TemplateDetailDBUtils.countMyTemplateList();
      templateListModel.total = max(templateListModel.total ?? 0, count);
      templateListModel.deletedTemplates = [];
      templateListModel.list = localTemplates;
      templateListModel.hasMore =
          (templateListModel.page ?? 0) * (templateListModel.limit ?? 0) < (templateListModel.total ?? 0);
    } catch (e, s) {
      // showToast(msg: "error=${e.toString()}");
      localTemplates = await TemplateDetailDBUtils.queryMyTemplateList(page, pageSize: limit);
      int count = await TemplateDetailDBUtils.countMyTemplateList();
      templateListModel
        ..total = count
        ..page = page
        ..limit = limit
        ..list = localTemplates
        ..deletedTemplates = []
        ..hasMore = true;
    }

    return templateListModel;
  }

  /// 下载模版缩略图
  Future<List<TemplateData>> downloadTemplateThumbnails(List<TemplateData> templates) async {
    List<TemplateData> downloadedTemplates = [];

    await Future.forEach(templates, (template) async {
      try {
        // 使用TemplateImageUtils.downloadTemplateImages下载缩略图
        //下载模版缩略图
        File localThumbFile =
            await TemplatePathGeneratorUtils.getThumbFile(template.id!, template.profile.extra.updateTime);
        bool isValidFile = await TemplateImageUtils.isFileValid(localThumbFile);
        if (!isValidFile) {
          await localThumbFile.create(recursive: true);
          await TemplateImageUtils.downloadImage(template.thumbnail, localThumbFile.absolute.path);
        }
        TemplateData downloadedTemplate = template.copyWith(localThumbnail: localThumbFile.absolute.path);
        // 使用copy方法生成新的data
        downloadedTemplates.add(downloadedTemplate);
      } catch (e) {
        // 下载失败时仍保留原模版
        downloadedTemplates.add(template);
      }
    });

    return downloadedTemplates;
  }
}
