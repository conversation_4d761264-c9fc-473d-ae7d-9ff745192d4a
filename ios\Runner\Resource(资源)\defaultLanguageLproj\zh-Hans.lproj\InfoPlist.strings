/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "精臣云打印";
CFBundleDisplayName = "精臣云打印";
NSCameraUsageDescription = "此应用会在扫描条码、文字识别、拍照的功能中访问您的相机权限，是否允许打开相机？";
NSBluetoothPeripheralUsageDescription = "此应用会在连接打印机的服务中访问您的蓝牙权限，是否允许打开蓝牙？";
NSBluetoothAlwaysUsageDescription = "此应用会在连接打印机的服务中访问您的蓝牙权限，是否允许打开蓝牙？";
NSContactsUsageDescription = "此应用会在读取联系人的服务中访问您的通讯录权限，是否允许打开通讯录？";
//NSMicrophoneUsageDescription = "此应用会在语音识别的服务中访问您的麦克风权限，是否允许打开麦克风？";
NSPhotoLibraryUsageDescription = "该权限将用于打印图片素材、条码识别、二维码识别、文字识别、设置自定义头像等场景。请「允许访问所有照片」，以保证在精臣云打印中能正常访问相册。如果你使用「选择照片...」，则所有未选中的，以及未来新增的照片都将无法在精臣云打印中访问。";
NSLocationAlwaysUsageDescription = "为了方便您使用附近的Wi-Fi网络，精臣云打印向您申请定位权限";
NSLocationWhenInUseUsageDescription = "为了方便您使用附近的Wi-Fi网络，精臣云打印向您申请定位权限";
NSLocationAlwaysAndWhenInUseUsageDescription = "为了方便您使用附近的Wi-Fi网络，精臣云打印向您申请定位权限";
NSSpeechRecognitionUsageDescription = "此应用需要您的同意,才能访问语音识别，是否允许打开语音识别？";
NSLocalNetworkUsageDescription = "此应用在搜索局域网设备、配网的服务中需要访问本地局域网 (LAN)​。";
"UILaunchStoryboardName" = "LaunchScreen";
