import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.telephony.TelephonyManager
import android.text.SpannableStringBuilder
import com.blankj.utilcode.util.Utils
import java.io.BufferedReader
import java.io.File
import java.io.IOException
import java.io.InputStreamReader
import java.util.Locale

object RootCheckUtils {


    fun isDeviceRooted(): Boolean {
        val su = "su"
        val locations = arrayOf(
            "/system/bin/", "/system/xbin/", "/sbin/", "/system/sd/xbin/",
            "/system/bin/failsafe/", "/data/local/xbin/", "/data/local/bin/", "/data/local/",
            "/system/sbin/", "/usr/bin/", "/vendor/bin/"
        )
        for (location in locations) {
            if (File(location + su).exists()) {
                return true
            }
        }
        return false
    }

    fun getDeviceBuildInfoForDisplay(): CharSequence {
        val builder = SpannableStringBuilder()

        fun appendInfo(label: String, value: String?) {
            builder.append("$label: $value\n")
        }

        appendInfo("SERIAL", Build.SERIAL)
        appendInfo("MODEL", Build.MODEL)
        appendInfo("MANUFACTURER", Build.MANUFACTURER)
        appendInfo("BRAND", Build.BRAND)
        appendInfo("TYPE", Build.TYPE)
        appendInfo("PRODUCT", Build.PRODUCT)
        appendInfo("BOARD", Build.BOARD)
        appendInfo("FINGERPRINT", Build.FINGERPRINT)
        appendInfo("Device", Build.DEVICE)

        val canTel = canTel()
        appendInfo("canTel", "${canTel}")
        val checkSuRoot = isDeviceRooted()
        appendInfo("checkSuRoot", "${checkSuRoot}")

        val cpuInfo = isEmulatorByCpu()
        appendInfo("CPUInfo", "${cpuInfo}")
        val operateName = getOperateName()
        appendInfo("operateName", operateName)

        return builder
    }

    fun getOperateName(): String{
        var operatorName = ""
        val tm: TelephonyManager =
            Utils.getApp().getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        if (tm != null) {
            val name: String = tm.getNetworkOperatorName()
            if (name != null) {
                operatorName = name
            }
        }
        return operatorName
    }

    fun canTel(): Boolean{
        val url = "tel:" + "123456"
        val intent: Intent = Intent()
        intent.setData(Uri.parse(url))
        intent.setAction(Intent.ACTION_DIAL)
        val checkDial = intent.resolveActivity(Utils.getApp().getPackageManager()) == null
        return !checkDial
    }

    private fun isEmulatorByCpu(): Boolean {
        val cpuInfo = readCpuInfo()
        return cpuInfo.contains("intel") || cpuInfo.contains("amd")
    }

    /**
     * Return Cpu information
     *
     * @return Cpu info
     */
    private fun readCpuInfo(): String {
        var result = ""
        try {
            val args = arrayOf("/system/bin/cat", "/proc/cpuinfo")
            val cmd = ProcessBuilder(*args)
            val process = cmd.start()
            val sb = StringBuilder()
            var readLine: String?
            val responseReader = BufferedReader(InputStreamReader(process.inputStream, "utf-8"))
            while ((responseReader.readLine().also { readLine = it }) != null) {
                sb.append(readLine)
            }
            responseReader.close()
            result = sb.toString().lowercase(Locale.getDefault())
        } catch (ignored: IOException) {
        }
        return result
    }
    fun isEmulator(): Boolean {
        val checkProperty = (Build.FINGERPRINT.startsWith("generic")
                || Build.FINGERPRINT.toLowerCase().contains("vbox")
                || Build.FINGERPRINT.toLowerCase().contains("test-keys")
                || Build.MODEL.contains("google_sdk")
                || Build.MODEL.contains("Emulator")
                || Build.MODEL.contains("Android SDK built for x86")
                || Build.MANUFACTURER.contains("Genymotion")
                || (Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"))
                || "google_sdk".equals(Build.PRODUCT))
        if (checkProperty) return true

        var operatorName = ""
        val tm: TelephonyManager =
            Utils.getApp().getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        if (tm != null) {
            val name: String = tm.getNetworkOperatorName()
            if (name != null) {
                operatorName = name
            }
        }
        val checkOperatorName: Boolean = operatorName.toLowerCase().equals("android")
        if (checkOperatorName) return true

//        val url = "tel:" + "123456"
//        val intent: Intent = Intent()
//        intent.setData(Uri.parse(url))
//        intent.setAction(Intent.ACTION_DIAL)
//        val checkDial = intent.resolveActivity(Utils.getApp().getPackageManager()) == null
//        if (checkDial) return true
        if (isEmulatorByCpu()) return true

        //        boolean checkDebuggerConnected = Debug.isDebuggerConnected();
        //        if (checkDebuggerConnected) return true;
        return false
    }

}
