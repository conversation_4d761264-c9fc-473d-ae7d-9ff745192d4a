/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Esta aplicación accederá a su cámara para funciones como escaneo de códigos de barras, reconocimiento de texto y fotografía. ¿Desea permitir el acceso a la cámara?";
NSBluetoothPeripheralUsageDescription = "Esta aplicación accederá a su Bluetooth para funciones de conexión con la impresora. ¿Desea permitir el acceso al Bluetooth?";
NSBluetoothAlwaysUsageDescription = "Esta aplicación accederá a su Bluetooth para funciones de conexión con la impresora. ¿Desea permitir el acceso al Bluetooth?";
NSContactsUsageDescription = "Esta aplicación accederá a su lista de contactos para funciones relacionadas. ¿Desea permitir el acceso a los contactos?";
NSMicrophoneUsageDescription = "Esta aplicación accederá a su micrófono para funciones de reconocimiento de voz. ¿Desea permitir el acceso al micrófono?";
NSPhotoLibraryUsageDescription = "Este permiso se utilizará para imprimir imágenes, reconocer códigos de barras y QR, reconocer texto, configurar avatares personalizados, entre otros. Seleccione \"Permitir acceso a todas las fotos\" para garantizar el funcionamiento correcto de NIIMBOT. Si selecciona \"Elegir fotos...\", todas las no seleccionadas, así como las que agregue en el futuro, no podrán ser accedidas por la aplicación.";
NSLocationWhenInUseUsageDescription = "Para facilitar el uso de redes Wi-Fi cercanas, NIIMBOT solicita permiso de ubicación";
NSLocationAlwaysUsageDescription = "Para facilitar el uso de redes Wi-Fi cercanas, NIIMBOT solicita permiso de ubicación";
NSLocationAlwaysAndWhenInUseUsageDescription = "Para facilitar el uso de redes Wi-Fi cercanas, NIIMBOT solicita permiso de ubicación";
NSSpeechRecognitionUsageDescription = "Esta aplicación necesita su consentimiento para acceder al reconocimiento de voz. ¿Desea permitir el acceso al reconocimiento de voz?";
NSLocalNetworkUsageDescription = "Esta aplicación necesita acceder a la ​Red de área local (LAN)​​ para los servicios de búsqueda de dispositivos LAN y configuración de red.";
"UILaunchStoryboardName" = "LaunchScreen";
