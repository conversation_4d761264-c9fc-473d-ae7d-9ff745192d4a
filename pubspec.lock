# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "61.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.13.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: d0481093c50b1da8910eb0bb301626d4d8eb7284aa739614d2b394ee09e3ea04
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.7.0"
  asn1lib:
    dependency: "direct overridden"
    description:
      name: asn1lib
      sha256: "4bae5ae63e6d6dd17c4aac8086f3dec26c0236f6a0f03416c6c19d830c367cf5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.8"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "79b2aef6ac2ed00046867ed354c88778c9c0f029df8a20fe10b5436826721ef9"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.2"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "339086358431fa15d7eca8b6a36e5d783728cf025e559b834f4609a1fcfb7b0a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.2"
  build_runner:
    dependency: "direct main"
    description:
      name: build_runner
      sha256: "028819cfb90051c6b5440c7e574d1896f8037e3c96cf17aaeb054c9311cfbf4d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.13"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: f8126682b87a7282a339b871298cc12009cb67109cfa1614d6436fb0289193e0
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.3.2"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: "direct overridden"
    description:
      name: built_value
      sha256: ea90e81dc4a25a043d9bee692d20ed6d1c4a1662a28c03a96417446c093ed6b4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.9.5"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "7c1183e361e5c8b0a0f21a28401eecdbde252441106a9816400dd4c2b2424916"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.4.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "35814b016e37fbdc91f7ae18c8caf49ba5c88501813f73ce8a07027a395e2829"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.1"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "980842f4e8e2535b8dbd3d5ca0b1f0ba66bf61d14cc3a17a9b4788a3685ba062"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: "direct overridden"
    description:
      name: camera_android
      sha256: e0f9b7eea2d1f4d4f5460f178522f0d02c095d2ae00b01a77419ce61c4184bfe
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.10.7"
  camera_avfoundation:
    dependency: "direct overridden"
    description:
      name: camera_avfoundation
      sha256: a33cd9a250296271cdf556891b7c0986a93772426f286595eccd5f45b185933c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.18+14"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "2f757024a48696ff4814a789b0bd90f5660c0fb25f393ab4564fb483327930e2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.10.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.3.5"
  change:
    dependency: transitive
    description:
      name: change
      sha256: "65db7f966dc7e786687f49900a94c5f08b0eb9ca8c4a3e7eed3a55e980b455e2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.4"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  charset:
    dependency: transitive
    description:
      name: charset
      sha256: "27802032a581e01ac565904ece8c8962564b1070690794f0072f6865958ce8b9"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  chewie:
    dependency: "direct main"
    description:
      name: chewie
      sha256: "335df378c025588aef400c704bd71f0daea479d4cd57c471c88c056c1144e7cd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.8.5"
  cider:
    dependency: transitive
    description:
      name: cider
      sha256: dfff70e9324f99e315857c596c31f54cb7380cfa20dfdfdca11a3631e05b7d3e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.8"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "0ec10bf4a89e4c613960bf1e8b42c64127021740fb21640c29c909826a5eea3e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.10.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.18.0"
  common_utils:
    dependency: "direct main"
    description:
      name: common_utils
      sha256: c26884339b13ff99b0739e56f4b02090c84054ed9dd3a045435cd24e7b99c2c1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: b74247fad72c171381dbe700ca17da24deac637ab6d43c343b42867acb95c991
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.8"
  custom_pop_up_menu:
    dependency: transitive
    description:
      name: custom_pop_up_menu
      sha256: eeac484c6ddffffb25e803dc2a5cc9381e700a29f074e9fcc76fe36b62fde850
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.4"
  dart_quill_delta:
    dependency: transitive
    description:
      name: dart_quill_delta
      sha256: "6aa89f0903ca3e70f5ceeb1d75d722f6ca583e87a2a8893c7b9f42f7a947f6e5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.6.0"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.2"
  dartx:
    dependency: transitive
    description:
      name: dartx
      sha256: "8b25435617027257d43e6508b5fe061012880ddfdaa75a71d607c3de2a13d244"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.11"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "24a261d5d5c87e86c7651c417a5dbdf8bcd7080dd592533910e8d0505a279f21"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  device_info:
    dependency: transitive
    description:
      name: device_info
      sha256: f4a8156cb7b7480d969cb734907d18b333c8f0bc0b1ad0b342cdcecf30d62c48
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  device_info_platform_interface:
    dependency: transitive
    description:
      name: device_info_platform_interface
      sha256: b148e0bf9640145d09a4f8dea96614076f889e7f7f8b5ecab1c7e5c2dbc73c1b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  device_info_plus:
    dependency: "direct overridden"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.2"
  diff_match_patch:
    dependency: transitive
    description:
      name: diff_match_patch
      sha256: "2efc9e6e8f449d0abe15be240e2c2a3bcd977c8d126cfd70598aee60af35c0a4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.4.1"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: d90ee57923d1828ac14e492ca49440f65477f4bb1263575900be731a3dac66a9
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.9.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "7586e476d70caecaf1686d21eee7247ea43ef5c345eab9e0cc3583ff13378d78"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  drawboard_dart_sdk:
    dependency: transitive
    description:
      path: "."
      ref: "feature/flutter_update_new"
      resolved-ref: "2c07b6ea7b9c3642da88dfa6f536f568c2a67328"
      url: "https://git.jc-ai.cn/architect/kalimdor/drawboard-dart-sdk.git"
    source: git
    version: "1.2.2"
  dropdown_below:
    dependency: transitive
    description:
      name: dropdown_below
      sha256: "4fa3bcc9f7ab48a54d970fd5683557676adac161f42f849e125ae8d42b28fa9a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.3"
  easy_localization:
    dependency: "direct main"
    description:
      name: easy_localization
      sha256: "2ccdf9db8fe4d9c5a75c122e6275674508fd0f0d49c827354967b8afcc56bbed"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.8"
  easy_logger:
    dependency: transitive
    description:
      name: easy_logger
      sha256: c764a6e024846f33405a2342caf91c62e357c24b02c04dbc712ef232bf30ffb7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.2"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.0.3"
  enum_to_string:
    dependency: "direct main"
    description:
      name: enum_to_string
      sha256: "93b75963d3b0c9f6a90c095b3af153e1feccb79f6f08282d3274ff8d9eea52bc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.7"
  excel:
    dependency: transitive
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.6"
  extended_image:
    dependency: "direct overridden"
    description:
      name: extended_image
      sha256: "85199f9233e03abc2ce2e68cbb2991648666af4a527ae4e6250935be8edfddae"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.1.0"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: e61dafd94400fff6ef7ed1523d445ff3af137f198f3228e4a3107bc5b4bec5d1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.6"
  extension:
    dependency: transitive
    description:
      name: extension
      sha256: be3a6b7f8adad2f6e2e8c63c895d19811fcf203e23466c6296267941d0ff4f24
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  facebook_auth_desktop:
    dependency: transitive
    description:
      name: facebook_auth_desktop
      sha256: "35ff7b8c62ad37c4bc08eed7d58cf301ab8770a2f4eed46573843ae1e1a1aac3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.9"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: "direct overridden"
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "320fcfb6f33caa90f0b58380489fc5ac05d99ee94b61aa96ec2bff0ba81d3c2b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.3+4"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  fluro:
    dependency: "direct main"
    description:
      name: fluro
      sha256: "24d07d0b285b213ec2045b83e85d076185fa5c23651e44dae0ac6755784b97d0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_boost:
    dependency: "direct overridden"
    description:
      path: "."
      ref: "v5.0.2"
      resolved-ref: b76986f6f541d8cf458b6d669bd75dec93cd43bf
      url: "https://git.jc-ai.cn/architect/flutter/flutter_boost.git"
    source: git
    version: "5.0.2"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "400b6592f16a4409a7f2bb929a9a7e38c72cceb8ffb99ee57bbf2cb2cecf8386"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.4.1"
  flutter_canvas_plugins_interface:
    dependency: "direct main"
    description:
      path: "plugins/flutter_canvas_plugins_interface"
      relative: true
    source: path
    version: "1.0.13"
  flutter_colorpicker:
    dependency: transitive
    description:
      name: flutter_colorpicker
      sha256: "969de5f6f9e2a570ac660fb7b501551451ea2a1ab9e2097e89475f60e07816ea"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  flutter_easyrefresh:
    dependency: "direct main"
    description:
      name: flutter_easyrefresh
      sha256: "5d161ee5dcac34da9065116568147d742dd25fb9bff3b10024d9054b195087ad"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.2"
  flutter_facebook_auth:
    dependency: "direct main"
    description:
      name: flutter_facebook_auth
      sha256: "2ed18ea374919fbca4c85383957662674d23f6f3901dfbf603f8d54cab10d050"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.0.11"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: "0bc5fefc89b012635c4424a34334215e81e0ff38c5b413f869fd9c14a10c6135"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.1"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "6dfd4a3844137fbf7eb4c8d753add1ca15233b280a73a3360d9af46b87680678"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.1"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: "6a126f703b89499818d73305e4ce1e3de33b4ae1c5512e3b8eab4b986f46774c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.18.6"
  flutter_image_compress:
    dependency: transitive
    description:
      name: flutter_image_compress
      sha256: "51d23be39efc2185e72e290042a0da41aed70b14ef97db362a6b5368d0523b27"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  flutter_image_compress_common:
    dependency: transitive
    description:
      name: flutter_image_compress_common
      sha256: c5c5d50c15e97dd7dc72ff96bd7077b9f791932f2076c5c5b6c43f2c88607bfb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: "20019719b71b743aba0ef874ed29c50747461e5e8438980dfa5c2031898f7337"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_ohos:
    dependency: transitive
    description:
      name: flutter_image_compress_ohos
      sha256: e76b92bbc830ee08f5b05962fc78a532011fcd2041f620b5400a593e96da3f51
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.3"
  flutter_image_compress_platform_interface:
    dependency: transitive
    description:
      name: flutter_image_compress_platform_interface
      sha256: "579cb3947fd4309103afe6442a01ca01e1e6f93dc53bb4cbd090e8ce34a41889"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.5"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: b9b141ac7c686a2ce7bb9a98176321e1182c9074650e47bb140741a44b6f5a96
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.5"
  flutter_inappwebview:
    dependency: "direct overridden"
    description:
      name: flutter_inappwebview
      sha256: "80092d13d3e29b6227e25b67973c67c7210bd5e35c4b747ca908e31eb71a46d5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.5"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      name: flutter_inappwebview_android
      sha256: "62557c15a5c2db5d195cb3892aab74fcaec266d7b86d59a6f0027abd672cddba"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "787171d43f8af67864740b6f04166c13190aa74a1468a1f1f1e9ee5b90c359cd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      name: flutter_inappwebview_ios
      sha256: "5818cf9b26cf0cbb0f62ff50772217d41ea8d3d9cc00279c45f8aabaa1b4025d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      name: flutter_inappwebview_macos
      sha256: c1fbb86af1a3738e3541364d7d1866315ffb0468a1a77e34198c9be571287da1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      name: flutter_inappwebview_platform_interface
      sha256: cf5323e194096b6ede7a1ca808c3e0a078e4b33cc3f6338977d75b4024ba2500
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0+1"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      name: flutter_inappwebview_web
      sha256: "55f89c83b0a0d3b7893306b3bb545ba4770a4df018204917148ebb42dc14a598"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_windows:
    dependency: transitive
    description:
      name: flutter_inappwebview_windows
      sha256: "8b4d3a46078a2cdc636c4a3d10d10f2a16882f6be607962dbfff8874d1642055"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  flutter_keyboard_visibility:
    dependency: "direct main"
    description:
      name: flutter_keyboard_visibility
      sha256: "98664be7be0e3ffca00de50f7f6a287ab62c763fc8c762e0a21584584a3ff4f8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.0.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_line_sdk:
    dependency: "direct overridden"
    description:
      name: flutter_line_sdk
      sha256: "81e798c7447bed3820b37a66b59ce01af000c762e8f2497b50181a184377af27"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.4"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "3f41d009ba7172d5ff9be5f6e6e6abb4300e263aab8866d2a0842ed2a70f8f0c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_image:
    dependency: transitive
    description:
      name: flutter_native_image
      sha256: "0ff23d6222064259df8f85ea56925627ea1ec8658814672c5b6c23fc9174c65e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.6+1"
  flutter_net_signature:
    dependency: "direct main"
    description:
      name: flutter_net_signature
      sha256: f6abdc1f4bce9839e226c8d20a18ca8b57fad61968075df7debda67864e8515c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.9"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "1c2b787f99bdca1f3718543f81d38aa1b124817dfeb9fb196201bea85b6134bf"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.26"
  flutter_quill:
    dependency: "direct main"
    description:
      name: flutter_quill
      sha256: "7c37e0761e7101809af8e7994d6aef58519cb8035aa307be66867c710d64abf0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.6.0"
  flutter_quill_delta_from_html:
    dependency: transitive
    description:
      name: flutter_quill_delta_from_html
      sha256: "3cddb27325e005459fd2bbd6af4d813525d5b2bcb6e9828ab2b449f70eaf1147"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.12"
  flutter_screenutil:
    dependency: transitive
    description:
      name: flutter_screenutil
      sha256: "8239210dd68bee6b0577aa4a090890342d04a136ce1c81f98ee513fc0ce891de"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.9.3"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "22dbf16f23a4bcf9d35e51be1c84ad5bb6f627750565edd70dab70f3ff5fff8f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.1.0"
  flutter_secure_storage_linux:
    dependency: "direct overridden"
    description:
      name: flutter_secure_storage_linux
      sha256: bf7404619d7ab5c0a1151d7c4e802edad8f33535abfbeff2f9e1fe1274e2d705
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.2"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: "38f9501c7cb6f38961ef0e1eacacee2b2d4715c63cc83fe56449c4d3d0b47255"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: a857de7ea701f276fd6a6c4c67ae885b60729a3449e42766bb0e655171042801
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.2"
  flutter_spinkit:
    dependency: "direct overridden"
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.1"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: c200fd79c918a40c5cd50ea0877fa13f81bdaf6f0a5d3dbcc2a13e3285d6aa1b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.17"
  flutter_swipe_action_cell:
    dependency: transitive
    description:
      name: flutter_swipe_action_cell
      sha256: f09036bf30fea826d9ee7e86f72f040011f42783e8c629a1a549587b0e057c53
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_xlider:
    dependency: transitive
    description:
      name: flutter_xlider
      sha256: b83da229b8a2153adeefc5d9e08e0060689c8dc2187b30e3502cf67c1a6495be
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.5.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "25e51620424d92d3db3832464774a6143b5053f15e382d8ffbfd40b6e795dcf1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.2.12"
  fluwx:
    dependency: transitive
    description:
      name: fluwx
      sha256: "90740b0a799343343aca2f6bea8b6b82af0e477c0c440e5442381bcd91839b18"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.5.3"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.6.6"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: c3f1ee72c96f8f78935e18aa8cecced9ab132419e8625dc187e1c2408efc20de
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.3"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "5d187c46dc59e02646e10fe82665fc3884a9b71bc1c90c2b8b749316d33ee454"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.3.3+1"
  google_sign_in:
    dependency: transitive
    description:
      name: google_sign_in
      sha256: fad6ddc80c427b0bba705f2116204ce1173e09cf299f85e053d57a55e5b2dd56
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.2.2"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "7af72e5502c313865c729223b60e8ae7bce0a1011b250c24edcf30d3d7032748"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.35"
  google_sign_in_ios:
    dependency: "direct overridden"
    description:
      name: google_sign_in_ios
      sha256: "29cd125f58f50ceb40e8253d3c0209e321eee3e5df16cd6d262495f7cad6a2bd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.8.1"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "5f6f79cf139c197261adb6ac024577518ae48fdff8e53205c5373b5f6430a8aa"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.0"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "460547beb4962b7623ac0fb8122d6b8268c951cf0b646dd150d60498430e4ded"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.12.4+4"
  gql:
    dependency: transitive
    description:
      name: gql
      sha256: "650e79ed60c21579ca3bd17ebae8a8c8d22cde267b03a19bf3b35996baaa843a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315362"
  gql_dedupe_link:
    dependency: transitive
    description:
      name: gql_dedupe_link
      sha256: "10bee0564d67c24e0c8bd08bd56e0682b64a135e58afabbeed30d85d5e9fea96"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.4-alpha+1715521079596"
  gql_dio_link:
    dependency: "direct overridden"
    description:
      name: gql_dio_link
      sha256: "0a38185eb2eeabcc8d81c8322b8b6da9057af36553c7acb46e54fe035866ccef"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1+1"
  gql_error_link:
    dependency: transitive
    description:
      name: gql_error_link
      sha256: "93901458f3c050e33386dedb0ca7173e08cebd7078e4e0deca4bf23ab7a71f63"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0+1"
  gql_exec:
    dependency: transitive
    description:
      name: gql_exec
      sha256: "394944626fae900f1d34343ecf2d62e44eb984826189c8979d305f0ae5846e38"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1-alpha+1699813812660"
  gql_http_link:
    dependency: transitive
    description:
      name: gql_http_link
      sha256: ef6ad24d31beb5a30113e9b919eec20876903cc4b0ee0d31550047aaaba7d5dd
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  gql_link:
    dependency: transitive
    description:
      name: gql_link
      sha256: c2b0adb2f6a60c2599b9128fb095316db5feb99ce444c86fb141a6964acedfa4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315378"
  gql_transform_link:
    dependency: transitive
    description:
      name: gql_transform_link
      sha256: "0645fdd874ca1be695fd327271fdfb24c0cd6fa40774a64b946062f321a59709"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  graphql:
    dependency: "direct overridden"
    description:
      name: graphql
      sha256: b67471ddb954fd538c5930f989c5072e038a9fc910afc85a8a926a7f9b8c56d3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0"
  graphql_flutter:
    dependency: transitive
    description:
      name: graphql_flutter
      sha256: "9de0365b58c8733130a706e9fddb33f6791d9bbbee45e1cd1bc2ca0920941a19"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0-beta.3"
  graphql_intercept_http_code_link:
    dependency: transitive
    description:
      name: graphql_intercept_http_code_link
      sha256: fd5dfeb656326f696296eb5543cd6dfae7652fe792d4c3ca1d9084f6b3caee92
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: "741bbf84165310a68ff28fe9e727332eef1407342fca52759cb21ad8177bb8d0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.2"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  html:
    dependency: "direct overridden"
    description:
      name: html
      sha256: "9475be233c437f0e3637af55e7702cbbe5c23a68bd56e8a5fa2d426297b7c6c8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.15.5+1"
  http:
    dependency: "direct overridden"
    description:
      name: http
      sha256: fe7ab022b76f3034adc518fb6ea04a82387620e19977665ea18d30a1cf43442f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.0"
  http_interceptor:
    dependency: transitive
    description:
      name: http_interceptor
      sha256: "288c6ded4a2c66de2730a16b30cbd29d05d042a5e61304d9b4be0e16378f4082"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: aa6199f908078bb1c5efb8d8638d4ae191aac11b311132c3ef48ce352fb52ef8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.3.0"
  image_editor:
    dependency: transitive
    description:
      name: image_editor
      sha256: "0fe70befea0dbaf24a7cacc32c28311a65118f66637997ad072e9063f59efdd8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.1"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: "93d2f5c8b636f862775dd62a9ec20d09c8272598daa02f935955a4640e1844ee"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  image_editor_ohos:
    dependency: transitive
    description:
      name: image_editor_ohos
      sha256: "06756859586d5acefec6e3b4f356f9b1ce05ef09213bcb9a0ce1680ecea2d054"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.9"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: c7474402a2c1b1c0cbf20e6e240097a096a05476
      url: "https://git.jc-ai.cn/print/foundation/niimbot_image_picker_android"
    source: git
    version: "0.8.12+21"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: "direct overridden"
    description:
      name: image_picker_ios
      sha256: aa7eb2fb0d6182f36d308444e49a6cf3a0f1413ab8679c1841b8c1b92394eda4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.3"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "34a65f6740df08bbbeb0a1abd8e6d32107941fd4868f67a507b25601651022c9"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+2"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "1b90ebbd9dcf98fb6c1d01427e49a55bd96b5d67b8c67cf955d60a5de74207c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+2"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+1"
  internet_file:
    dependency: transitive
    description:
      name: internet_file
      sha256: c3e6aa0c1cc6c08e701bb91019a7784fece1f64e18464f53df1200caa7598b68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.19.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: dfd5a80599cf0165756e3181807ed3e77daf6dd4137caaad72d0b7931597650b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.5"
  isar:
    dependency: transitive
    description:
      name: isar
      sha256: "99165dadb2cf2329d3140198363a7e7bff9bbd441871898a87e26914d25cf1ea"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isar_flutter_libs:
    dependency: transitive
    description:
      name: isar_flutter_libs
      sha256: bc6768cc4b9c61aabff77152e7f33b4b17d2fc93134f7af1c3dd51500fe8d5e8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isar_generator:
    dependency: "direct main"
    description:
      name: isar_generator
      sha256: "76c121e1295a30423604f2f819bc255bc79f852f3bc8743a24017df6068ad133"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isolate:
    dependency: transitive
    description:
      name: isolate
      sha256: "3554ab10fdeec965d27e0074c913ccb2229887633da080d2b35a6322da14938b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.9.0"
  json_serializable:
    dependency: "direct overridden"
    description:
      name: json_serializable
      sha256: ea1432d167339ea9b5bb153f0571d0039607a873d6e04e0117af043f14a1fd4b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.8.0"
  keyboard_actions:
    dependency: "direct main"
    description:
      name: keyboard_actions
      sha256: d621d15d7626303798e451008a223642a86978d06fc4adff7461d77859834aa9
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.0"
  layout_forge:
    dependency: "direct main"
    description:
      path: "."
      ref: master
      resolved-ref: "7e545111e31900bc73e981b7067bd4d3976bc743"
      url: "https://git.jc-ai.cn/architect/TemplateStyler/layout_forge.git"
    source: git
    version: "0.0.1"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  lifecycle:
    dependency: "direct main"
    description:
      name: lifecycle
      sha256: ad96a411440e8ab5b8dd63eb6345b1385863f33a09ae073e0ea110159ea81787
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.8.0"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "976c774dd944a42e83e2467f4cc670daef7eed6295b10b36ae8c85bcbf828235"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.7.0"
  lpinyin:
    dependency: transitive
    description:
      name: lpinyin
      sha256: "0bb843363f1f65170efd09fbdfc760c7ec34fc6354f9fcb2f89e74866a0d814a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.3.0"
  marker:
    dependency: transitive
    description:
      name: marker
      sha256: "3dadd01f3b0ffae148ffb3b1bc04290a98e54a465cddbab59727bd2a9fe57750"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.11.1"
  measure_size:
    dependency: "direct main"
    description:
      name: measure_size
      sha256: "5faef74474db7bbcd8c6f301d8a09abc69b8e999b85167825e532f1a55c38ae6"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.0"
  meta:
    dependency: "direct overridden"
    description:
      name: meta
      sha256: "7687075e408b093f36e6bbf6c91878cc0d4cd10f409506f7bc996f68220b9136"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.12.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  mobile_scanner:
    dependency: transitive
    description:
      name: mobile_scanner
      sha256: b8c0e9afcfd52534f85ec666f3d52156f560b5e6c25b1e3d4fe2087763607926
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.1.1"
  music_visualizer:
    dependency: transitive
    description:
      name: music_visualizer
      sha256: d0eafdbe052021c7b2a2b3e1c87cc163e7262410a47246d570c568ad56935096
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.4"
  native_flutter_proxy:
    dependency: "direct main"
    description:
      name: native_flutter_proxy
      sha256: d8109940d3412ecb0e88e841b5c7efee2e4ef6016e0d2dc1ebaa07a87af7a45b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.15"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  netal_plugin:
    dependency: "direct main"
    description:
      path: "."
      ref: "048c18adb0b022f8dcee5476bb9e3d6dbf446021"
      resolved-ref: "048c18adb0b022f8dcee5476bb9e3d6dbf446021"
      url: "https://git.jc-ai.cn/architect/dboard/netal_plugin.git"
    source: git
    version: "1.2.32"
  nety:
    dependency: transitive
    description:
      path: "."
      ref: "4b0b4a58fc261c7a2ec0e7757522a46faa0b614a"
      resolved-ref: "4b0b4a58fc261c7a2ec0e7757522a46faa0b614a"
      url: "https://git.jc-ai.cn/architect/dboard/nety.git"
    source: git
    version: "0.1.30"
  niim_login:
    dependency: "direct main"
    description:
      path: niim_login
      ref: "feature/6_4_0_qq_update"
      resolved-ref: "2d30fe6a017f2856668512da6f72c10c212f2f87"
      url: "https://git.jc-ai.cn/architect/ios/niimprintmodule.git"
    source: git
    version: "2.0.2"
  niimbot_cache_manager:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/6_3_6_font_categary_sort"
      resolved-ref: fd1ee90638cd07f27b5e88648ab0de7188648f13
      url: "https://git.jc-ai.cn/print/foundation/niimbot_cache_manager.git"
    source: git
    version: "0.0.1"
  niimbot_dio_http_cache:
    dependency: "direct main"
    description:
      path: "."
      ref: main
      resolved-ref: "1a0b63deefa08435bb92cc942414292e6868ec0c"
      url: "https://git.jc-ai.cn/print/foundation/niimbot_dio_http_cache.git"
    source: git
    version: "0.3.0"
  niimbot_excel:
    dependency: "direct overridden"
    description:
      name: niimbot_excel
      sha256: "6a673b5477ecffc7295f48a32cdc0c7f4c039f0d07602942a85920d36acf5fc6"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.15"
  niimbot_flutter_canvas:
    dependency: "direct main"
    description:
      path: "plugins/niimbot_flutter_canvas"
      relative: true
    source: path
    version: "0.0.6"
  niimbot_http:
    dependency: transitive
    description:
      name: niimbot_http
      sha256: "76d1bc25940bcac8464236f5084c692f0ac7dcc8275ab042a53274576ae94443"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.35"
  niimbot_intl:
    dependency: "direct overridden"
    description:
      name: niimbot_intl
      sha256: "163e69f8cfdc638201d577a05586ea1d3bfe79f103498a085a398cd8719354db"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.6"
  niimbot_lego:
    dependency: "direct main"
    description:
      path: "."
      ref: develop
      resolved-ref: "43a1a0667470e71e27aec503fcfa48f2738b201b"
      url: "https://git.jc-ai.cn/architect/dboard/niimbot_lego.git"
    source: git
    version: "1.7.0"
  niimbot_local_storage:
    dependency: transitive
    description:
      name: niimbot_local_storage
      sha256: f4dc495235b4677647f6069ac347ed1892b2a8092e6af7190d3ec076ade8597c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.5"
  niimbot_log_plugin:
    dependency: "direct overridden"
    description:
      path: "."
      ref: "*******"
      resolved-ref: "5258cf6a45c6ff1779021d22f74c81c1ca2b7dd3"
      url: "https://git.jc-ai.cn/print/foundation/niimbot_log_plugin.git"
    source: git
    version: "0.0.1"
  niimbot_mobile_ui:
    dependency: "direct main"
    description:
      name: niimbot_mobile_ui
      sha256: c2f3f7bf85425ac1469523bb6ff2aa39fa2404e524358fd9ac419ee991624e8c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  niimbot_print_setting_plugin:
    dependency: "direct main"
    description:
      path: "plugins/niimbot_print_setting_plugin"
      relative: true
    source: path
    version: "1.0.0+1"
  niimbot_print_strategy:
    dependency: "direct overridden"
    description:
      path: "."
      ref: "8067027f2cc508d8ba992e9abcdbf29cc5980ff4"
      resolved-ref: "8067027f2cc508d8ba992e9abcdbf29cc5980ff4"
      url: "https://git.jc-ai.cn/print/foundation/niimbot_print_strategy.git"
    source: git
    version: "0.0.15"
  niimbot_sls_flutter:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: "094892a4e09ca7d72c67a7001df425afa850a0bd"
      url: "https://git.jc-ai.cn/print/foundation/niimbot_sls_flutter.git"
    source: git
    version: "1.1.2"
  niimbot_template:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/6_3_8_text_mode_template_layout"
      resolved-ref: dc80dacbed805c50debffa340bdb304c0999ed68
      url: "https://git.jc-ai.cn/print/foundation/niimbot_template.git"
    source: git
    version: "0.1.45"
  niimbot_tiny_canvaskit:
    dependency: "direct main"
    description:
      path: "plugins/niimbot_tiny_canvaskit"
      relative: true
    source: path
    version: "0.0.1"
  nimbot_state_manager:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/flutter_update"
      resolved-ref: "0d976feaa2eee6e13e8c9425e78d9ff04d04b9b4"
      url: "https://git.jc-ai.cn/print/foundation/nimbot_state_manager.git"
    source: git
    version: "0.0.1"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.5.0"
  normalize:
    dependency: transitive
    description:
      name: normalize
      sha256: f78bf0552b9640c76369253f0b8fdabad4f3fbfc06bdae9359e71bee9a5b071b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: f096c55ebb7deb7e384101542bfba8c52696c1b56fca2eb62827989ef2353bbc
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  package_info:
    dependency: "direct main"
    description:
      name: package_info
      sha256: "6c07d9d82c69e16afeeeeb6866fe43985a20b3b50df243091bfc4a4ad2b03b75"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.2"
  package_info_plus:
    dependency: "direct overridden"
    description:
      name: package_info_plus
      sha256: "67eae327b1b0faf761964a1d2e5d323c797f3799db0e85aa232db8d9e922bc35"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.2.1"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "202a487f08836a592a6bd4f901ac69b3a8f146af552bbd14407b6b41e1c3f086"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "4adf4fd5423ec60a29506c76581bc05854c55e3a0b72d35bb28d661c9686edf2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.15"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.0"
  pdfx:
    dependency: transitive
    description:
      name: pdfx
      sha256: "16b0a931231b56283c1804738078b5cdd365ee2174b5345ea200d7cf380e1ea8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.8.3"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "63e5216aae014a72fe9579ccd027323395ce7a98271d9defa9d57320d001af81"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.4.3"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.0.2"
  photo_manager:
    dependency: "direct overridden"
    description:
      name: photo_manager
      sha256: ebe91591ec4148ddb4864352b2612a9c9e70c02384d27c8672d8ab604c7021e6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.6.2"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b6015b67b32f345f57cf32c126f871bced2501236c405aafaefa885f7c821e4f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  photo_view:
    dependency: "direct overridden"
    description:
      name: photo_view
      sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.15.0"
  pigeon:
    dependency: "direct dev"
    description:
      name: pigeon
      sha256: "88d6db9a7ff324f377119ee5439a7455f80380d9e48c27addd451672c25a42f4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "17.1.3"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: "direct main"
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.9.1"
  pool:
    dependency: "direct main"
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.1"
  popover:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/0.2.9"
      resolved-ref: "083f1f200bb1c0ef130065df945a3400c8a92e65"
      url: "https://git.jc-ai.cn/print/foundation/popover.git"
    source: git
    version: "0.2.8+5"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      sha256: "68645b24e0716782e58948f8467fd42a880f255096a821f9e7d0ec625b00c84d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0"
  provider:
    dependency: "direct overridden"
    description:
      name: provider
      sha256: "489024f942069c2920c844ee18bb3d467c69e48955a4f32d1677f71be103e310"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.4"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "81876843eb50dc2e1e5b151792c9a985c5ed2536914115ed04e9c8528f6647b0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.2"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  rfc_6901:
    dependency: transitive
    description:
      name: rfc_6901
      sha256: df1bbfa3d023009598f19636d6114c6ac1e0b7bb7bf6a260f0e6e6ce91416820
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  rounded_loading_button_plus:
    dependency: "direct main"
    description:
      name: rounded_loading_button_plus
      sha256: "8baad9b66c4a81b02b597edf7691cf78f7267a999c08a68937e391af7b2da653"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.28.0"
  sensors_plus:
    dependency: transitive
    description:
      name: sensors_plus
      sha256: "89e2bfc3d883743539ce5774a2b93df61effde40ff958ecad78cd66b1a8b8d52"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.2"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: "58815d2f5e46c0c41c40fb39375d3f127306f7742efe3b891c0b1c87e2b5cd5d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  sentry:
    dependency: transitive
    description:
      name: sentry
      sha256: "****************************************************************"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.14.1"
  sentry_flutter:
    dependency: "direct main"
    description:
      name: sentry_flutter
      sha256: ****************************************************************
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.14.1"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "6e8bf70b7fef813df4e9a36f658ac46d107db4b4cfe1048b477d4e453a8159f5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "9f9f3d372d4304723e6136663bb291c0b93f5e4c8a4a6314347f481a33bda2b1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.7"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: c49bd060261c9a3f0ff445892695d6212ff603ef3115edbb448509d407600019
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.3"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: cc36c297b52866d203dbf9332263c94becc2fe0ceaa9681d07b6ef9807023b67
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.1"
  show_fps:
    dependency: "direct main"
    description:
      name: show_fps
      sha256: "398124dd74456ad92fbb791cd6dd1018b5ced5ac3b9d0b401ab8c0e035ac9920"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  sign_in_with_apple:
    dependency: transitive
    description:
      name: sign_in_with_apple
      sha256: ac3b113767dfdd765078c507dad9d4d9fe96b669cc7bd88fc36fc15376fb3400
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.3.0"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: c2ef2ce6273fce0c61acd7e9ff5be7181e33d7aa2b66508b39418b786cca2119
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: "44b66528f576e77847c14999d5e881e17e7223b7b0625a185417829e5306f47a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0+1"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: b21ebb8bc39cf72d11c7cfd809162a48c3800668ced1c9da3aade13a32cf6c1c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "14658ba5f669685cd3d63701d01b31ea748310f7ab854e471962670abcf57832"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.0"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "86d247119aedce8e63f4751bd9626fc9613255935558447569ad42f9f5b48b3c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.5"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: "direct main"
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: "direct main"
    description:
      name: sqflite
      sha256: "2d7299468485dca85efeeadf5d38986909c5eb0cd71fd3db2c2f000e6c9454bb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "761b9740ecbd4d3e66b8916d784e581861fd3c3553eda85e167bc49fdb68f709"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.4+6"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "22adfd9a2c7d634041e96d6241e6e1c8138ca6817018afc5d443fef91dcefa9c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1+1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: "direct overridden"
    description:
      name: synchronized
      sha256: "271977ff1e9e82ceefb4f08424b8839f577c1852e0726b5ce855311b46d3ef83"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.0"
  tencent_kit:
    dependency: transitive
    description:
      name: tencent_kit
      sha256: c4a9b6e6686dce4c868fd5ee699ffbe766f3f6cb895bcbef685d63a41277c9e5
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: "direct overridden"
    description:
      name: test_api
      sha256: "9955ae474176f7ac8ee4e989dadfb411a58c30415bcfb648fa04b2b8a03afa7f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.0"
  time:
    dependency: transitive
    description:
      name: time
      sha256: "370572cf5d1e58adcb3e354c47515da3f7469dac3a95b447117e728e7be6f461"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.5"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "62ee18aca144e4a9f29d212f5a4c6a053be252b895ab14b5821996cff4ed90fe"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.2"
  tuple:
    dependency: "direct main"
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.2"
  twitter_login:
    dependency: transitive
    description:
      name: twitter_login
      sha256: "31ff9db2e37eda878b876a4ce6d1525f51d34b6cd9de9aa185b07027a23ab95b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.4.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  universal_file:
    dependency: transitive
    description:
      name: universal_file
      sha256: d1a957fccaad2a32023b62fe435b273ee47aaf2eb804709795e4bf4afff50960
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "6fc2f56536ee873eeb867ad176ae15f304ccccc357848b351f6f0d8d4a40d193"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7f2022359d4c099eea7df3fdf739f7d3d3b9faf3166fb1dd390775176e0b76cb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.3"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "3284b6d2ac454cf34f114e1d3319866fdd1e19cdc329999057e44ffe936cfa77"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.4"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "44cc7104ff32563122a929e4620cf3efd584194eec6d1d913eb5ba593dbcf6de"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.18"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.4"
  version_manipulation:
    dependency: transitive
    description:
      name: version_manipulation
      sha256: e90782d610bde19765d2808ec06bc8ed9e04640a4dd07d1a3d370728ce9dae7f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  vibration:
    dependency: "direct main"
    description:
      name: vibration
      sha256: "06588a845a4ebc73ab7ff7da555c2b3dbcd9676164b5856a38bf0b2287f1045d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.0"
  vibration_platform_interface:
    dependency: transitive
    description:
      name: vibration_platform_interface
      sha256: "6ffeee63547562a6fef53c05a41d4fdcae2c0595b83ef59a4813b0612cd2bc36"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.3"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "7d78f0cfaddc8c19d4cb2d3bebe1bfef11f2103b0a03e5398b303a1bf65eeb14"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.9.5"
  video_player_android:
    dependency: "direct main"
    description:
      name: video_player_android
      sha256: "0fc42778d794465f12456ccdade3e729e4339c8a112f9e58d170dc00f17b75f2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.11"
  video_player_avfoundation:
    dependency: "direct overridden"
    description:
      name: video_player_avfoundation
      sha256: "84b4752745eeccb6e75865c9aab39b3d28eb27ba5726d352d45db8297fbd75bc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.7.0"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: df534476c341ab2c6a835078066fc681b8265048addd853a1e3c78740316a844
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.0"
  video_player_web:
    dependency: "direct overridden"
    description:
      name: video_player_web
      sha256: "3ef40ea6d72434edbfdba4624b90fd3a80a0740d260667d91e7ecd2d79e13476"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.4"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "14.2.5"
  wakelock_plus:
    dependency: "direct overridden"
    description:
      name: wakelock_plus
      sha256: a474e314c3e8fb5adef1f9ae2d247e57467ad557fa7483a2b895bc1b421c5678
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.2"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: e10444072e50dbc4999d7316fd303f7ea53d31c824aa5eb05d7ccbdd98985207
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.3"
  watcher:
    dependency: "direct overridden"
    description:
      name: watcher
      sha256: "69da27e49efa56a15f8afe8f4438c4ec02eff0a117df1b22ea4aad194fe1c104"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: "868d88a33d8a87b18ffc05f9f030ba328ffefba92d6c127917a2ba740f9cfe4a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  web_socket:
    dependency: "direct overridden"
    description:
      name: web_socket
      sha256: "3c12d96c0c9a4eec095246debcea7b86c0324f22df69893d538fcc6f1b8cce83"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.6"
  web_socket_channel:
    dependency: "direct overridden"
    description:
      name: web_socket_channel
      sha256: "0b8e2457400d8a859b7b2030786835a28a8e80836ef64402abef392ff4f1d0e5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.2"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "392c1d83b70fe2495de3ea2c84531268d5b8de2de3f01086a53334d8b6030a88"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.4"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: a374702564762a562cb6bcd289655c1176ff7c52fcd484685c8487634b8838a3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.8.8"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "812165e4e34ca677bdfbfa58c01e33b27fd03ab5fa75b70832d4b7d4ca1fa8cf"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.5"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: a5364369c758892aa487cbf59ea41d9edd10f9d9baf06a94e80f1bd1b4c7bbc0
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.9.5"
  wechat_assets_picker:
    dependency: "direct main"
    description:
      name: wechat_assets_picker
      sha256: "1d50105517eb6ca3406ea86cf534253d9f009376ecf41255fbfa7acecb2310af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.4.4"
  wechat_camera_picker:
    dependency: "direct main"
    description:
      name: wechat_camera_picker
      sha256: "776ce32feda72d84b63425533a27d3b822bfb93cc063d2aef3cc6d788769f36b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.4.0"
  wechat_picker_library:
    dependency: "direct overridden"
    description:
      name: wechat_picker_library
      sha256: "4600530b1a166254533f1b10c3a296f46f92c6bade9aa5eb22adc317438a8c0f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.4"
  win32:
    dependency: "direct overridden"
    description:
      name: win32
      sha256: a79dbe579cb51ecd6d30b17e0cae4e0ea15e2c0e66f69ad4198f22a6789e94f4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.5.1"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.5.0"
  xxh3:
    dependency: transitive
    description:
      name: xxh3
      sha256: "399a0438f5d426785723c99da6b16e136f4953fb1e9db0bf270bd41dd4619916"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.3"
sdks:
  dart: ">=3.5.0 <4.0.0"
  flutter: ">=3.24.0"
