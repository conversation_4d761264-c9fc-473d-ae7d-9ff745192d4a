package melon.south.com.mainlibrary.settings.activity

import JCApiManager
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.text.TextUtils
import android.view.Gravity
import android.view.View
import com.aliyun.sls.android.producer.Log
import com.blankj.utilcode.util.FileUtils
import com.gengcon.connect.JCConnectionManager
import com.gengcon.print.draw.manager.logo.LogoManager
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.gengcon.www.jcprintersdk.log.PrintConnLog
import com.gengcon.www.jcprintersdk.log.PrintLog
import com.jc.repositories.webview.library.view.JCWebViewFactory
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.dialog.CustomDialog
import com.niimbot.appframework_library.dialog.SuperDialog
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.listener.JCTResponseListener
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.CommonUtil
import com.niimbot.appframework_library.utils.ImageLoader
import com.niimbot.appframework_library.utils.NetworkUtils.isConnected
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.log.LogUploadUtil
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.fastjson.JSON
import com.niimbot.fastjson.JSONObject
import com.niimbot.okgolibrary.okgo.db.CacheManager
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.templatecoordinator.core.TemplateSyncLocalUtils
import com.niimbot.utiliylibray.util.GetAndroidUniqueMark
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.LocaleUtils
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import melon.south.com.baselibrary.BuildConfig
import melon.south.com.baselibrary.JCApi
import melon.south.com.baselibrary.activity_register.LaboratoryActivityConfig
import melon.south.com.baselibrary.base.BaseActivity
import melon.south.com.baselibrary.base.LaboratoryActivity
import melon.south.com.baselibrary.base.viewBinding
import melon.south.com.baselibrary.eventbus.FinishTemplateIndustryEvent
import melon.south.com.baselibrary.local.module.VersionBean
import melon.south.com.baselibrary.module.RollBackInfo
import melon.south.com.baselibrary.privacy.AppPrivacySettingsUtil
import melon.south.com.baselibrary.util.ApkDownloadProgressDialog
import melon.south.com.baselibrary.util.AppLanguageUtils
import melon.south.com.baselibrary.util.AppUpgradeDialog
import melon.south.com.baselibrary.util.CacheUtils
import melon.south.com.baselibrary.util.DataCacheUtils
import melon.south.com.baselibrary.util.EventBusUtils
import melon.south.com.baselibrary.util.RollBackVersionDialog
import melon.south.com.baselibrary.util.ToastInstance
import melon.south.com.baselibrary.util.showToast
import melon.south.com.loginlibrary.module.UserApiManager
import melon.south.com.mainlibrary.R
import melon.south.com.mainlibrary.databinding.ActivitySettingBinding
import melon.south.com.mainlibrary.settings.manager.AppCacheHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

class SettingActivity : BaseActivity() {
    private val binding by viewBinding(ActivitySettingBinding::inflate)

    companion object{
        var openDebug = false
    }
    override fun getBindingView() = binding.root
    override fun getLayoutId() = R.layout.activity_setting

    override fun getTitleBarText() = "app00297"

    private var hasGoToOtherPage: Boolean = false
    private var hasNewVersion: Boolean = false
    //版本回退开关，注意版本号的配置
    private var canRollBackVersion: Boolean = false
    private var hasClearDB = false

    override fun init() {
        hasNewVersion = PreferencesUtils.getBoolean("hasNewVersion", false)
        if (hasNewVersion) {
            binding.vRed.visibility = View.VISIBLE
        } else {
            binding.vRed.visibility = View.GONE
        }
        updateRollBackVisible()
        binding.ltvVersion.text = "V${BuildConfig.VERSION_NAME}"
        binding.ltvCache.text = DataCacheUtils.getTotalCacheSize(mActivity)
        if (!LoginDataEnum.isLogin) {
            binding.tvLogout.visibility = View.GONE
        }
        if(LocaleUtils.isRtl(this)){
            binding.tvLaboratory.gravity = Gravity.RIGHT or Gravity.CENTER_VERTICAL
            binding.tvResourceBundle.gravity = Gravity.RIGHT or Gravity.CENTER_VERTICAL
        }
        binding.llPersonalizedInfo.visible(LoginDataEnum.isLogin)

        EventBusUtils.register(this)
    }

    private fun updateRollBackVisible() {
        val isVisible = canRollBackVersion
        binding.ltvRollVersion.visibility = if (isVisible) View.VISIBLE else View.GONE
    }

    override fun languageUpdate() {
        super.languageUpdate()
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(AppLanguageUtils.attachBaseContext(
            newBase,
            TextHookUtil.getInstance().locale.toString()
        ))
    }

    override fun onResume() {
        super.onResume()
        hasGoToOtherPage = false
        if (hasClearDB) {
            // 2020/10/20 Ice_Liu 版本回退时，下载完成后会删除数据库，如果用户取消安装，需要重启app
            com.blankj.utilcode.util.AppUtils.relaunchApp(true)
        }
    }

    override fun initEvent() {
        //字体管理
        binding.ltvFont.setOnNotDoubleClickListener {
            if (!hasGoToOtherPage) {
                hasGoToOtherPage = true
                startActivity(Intent(this, FontManagerActivity::class.java))
            }
        }
        //语言切换
        binding.ltvLanguage.setOnNotDoubleClickListener {
            if (!hasGoToOtherPage) {
                hasGoToOtherPage = true
                startActivity(Intent(this, LanguageActivity::class.java))
            }
        }
        binding.ltvService.setOnNotDoubleClickListener {
            if (!isConnected()) {
                showToast("app01139")
                return@setOnNotDoubleClickListener
            }
            if (!LoginDataEnum.isLogin) {
                NiimbotGlobal.checkLoginWithDialog(mActivity)
            } else {
                if (!hasGoToOtherPage) {
                    hasGoToOtherPage = true
                    CommentActivity.startCommentActivity(mActivity)
                }
            }
        }
        //关于我们
        binding.ltvUs.setOnNotDoubleClickListener {
            if (!hasGoToOtherPage) {
                hasGoToOtherPage = true
                NiimbotGlobal.routingByScheme(this, JCApi.ABOUT_US, "app00206")
            }
        }
        binding.llCache.setOnNotDoubleClickListener {
            showClearDialog()
        }

        binding.tvLogout.setOnNotDoubleClickListener {
            showLogoutDialog()
        }

        binding.ltvPrivacySettings.setOnNotDoubleClickListener {
            if (!hasGoToOtherPage) {
                hasGoToOtherPage = true
                AppPrivacySettingsUtil.privacySettingsWebView(this)
            }
        }

        binding.llVersion.setOnNotDoubleClickListener {
            if (hasNewVersion) {
                checkVersion()
            }
        }
        binding.ltvRollVersion.setOnNotDoubleClickListener(outTime = 1500) {
            if (canRollBackVersion) {
                showRollBackVersionDialog()
            }
        }

        binding.lbtnLogout.visible(LoginDataEnum.isLogin)
        binding.lbtnLogout.setOnNotDoubleClickListener {
            showAppLogoutDlg()
        }

        binding.swMode.isChecked = openDebug
        binding.swMode.setOnCheckedChangeListener { buttonView, isChecked ->
            switchDebugMode(isChecked)
        }
        binding.swRecommend.isChecked = getRecommendSwitch()
        binding.swRecommend.setOnCheckedChangeListener { buttonView, isChecked ->
            freshRecommendSwitch(isChecked)
        }
        binding.llDebugInfo.setOnClickListener {
            NiimbotGlobal.gotoFlutterPage("showAppInfo")

        }
        binding.llPersonalizedInfo.setOnClickListener {
            if (!isConnected()) {
                showToast("app01139")
                return@setOnClickListener
            }
            val intent = Intent(this, PersonalSettingActivity::class.java)
            startActivity(intent)
        }
        setVersionClick()
        setResourceBundle()
    }

    private fun showRollBackVersionDialog() {
        if (!isConnected()) {
            showToast("app01139")
            return
        }
        JCApiManager.getRollbackInfo(object : JCTResponseListener<RollBackInfo> {
            override fun onSuccess(body: RollBackInfo) {
                RollBackVersionDialog(this@SettingActivity,
                    title = body.title,
                    otherReasonHint = body.otherReason,
                    subTitle = body.remark,
                    checkBoxReasonList = body.reasons,
                    leftTextCallBack = {
                        val uploadArray = arrayListOf<String>()
                        if (it.first.isNotEmpty()) {
                            uploadArray.addAll(it.first)
                        }
                        if (it.second.isNotEmpty()) {
                            uploadArray.add("${body.otherReason}-${it.second}")
                        }
                        uploadRollbackVersionInfo(uploadArray, body.oldVersionUrl)
                    }).show()
            }

            override fun onError(message: String) {
                ToastInstance.INSTANCE.showToast(message)
            }

        })


    }

    private fun uploadRollbackVersionInfo(rollbackInfo: ArrayList<String>, downApkUrl: String) {
        JCApiManager.postRollbackInfo(rollbackInfo, object : JCTResponseListener<Boolean> {
            override fun onSuccess(body: Boolean) {
                val dialog = ApkDownloadProgressDialog(
                    this@SettingActivity,
                    url = downApkUrl,
                    isForce = false
                )
                dialog.setOnClearDBCallback {
                    hasClearDB = it
                }
                dialog.show()

            }

            override fun onError(message: String) {
                ToastInstance.INSTANCE.showToast(message)
            }
        })

    }

    private fun checkVersion() {
        if (!isConnected()) {
            showToast("app01139")
            return
        }
        showLoading()
        JCApiManager.validateAppVersion(object : JCTResponseListener<VersionBean> {
            override fun onSuccess(body: VersionBean) {
                dismissLoading()
                logI("checkVersion", AppUtils.getAppVersionCode())
                if (!TextUtils.isEmpty(body.version)) {
                    PreferencesUtils.put("versionCheckInfo", any2Json(body))
                    PreferencesUtils.put("hasNewVersion", true)
                    updateApp(body)
                } else {
                    PreferencesUtils.put("hasNewVersion", false)
                }
            }

            override fun onError(message: String) {
                dismissLoading()
                showToast(message)
            }
        })
    }

    private fun updateApp(versionBean: VersionBean) {
        AppUpgradeDialog(
            versionBean.installationPackageAddress,
            versionBean.version,
            versionBean.instructions,
            this,
            versionBean.isForceUpdate
        ).showUpgradeDialog()
    }

    private fun showClearDialog() {
        CustomDialog.showBlueButtonDialog(mActivity, "app00032",
            "app00208", "app00030", "app00209") { isConfirm ->
            if (isConfirm) {
                showLoading()
                AppCacheHelper.flutterClearCache {
                    ImageLoader.clearCache()
                    DataCacheUtils.clearAllCache(mActivity)
                    LogoManager.clearLogos()
                    ImageLoader.clearCache()
                    CacheManager.getInstance().clear() //清除网络缓存
                    PreferencesUtils.put(LaboratoryActivity.ENVIRONMENT_KEY, "")
                    PreferencesUtils.remove("save_last_device_address") //清除缓存时，清理自动重连device
                    PreferencesUtils.remove("save_last_device_address_c1")
                    JCConnectionManager.getInstance().closeConnectAndClearAllAutoConnect()
                    JCConnectionManager.getInstance().saveLastPrinterC1(null)
                    CacheUtils.clearCache {
                        binding.ltvCache.text = "0.0MB"
//                        if (RFIDConnectionProxyManager.isSupportRFID()) {
//                            val code = RFIDConnectionProxyManager.getRFIDLabelInfo()?.one_code
//                            if (!code.isNullOrEmpty()) {
//                                TemplateSyncLocalUtils.getRfidServiceDetails(code)
//                            }
//                        }
                        dismissLoading()
                        ToastInstance.INSTANCE.showToast("app00439")
                    }
                    JCWebViewFactory.getInstance().destroyWebView(this)
                    FileUtils.delete("${SuperUtils.superContext.filesDir.absolutePath}/cap_app")
                }
            }
        }
    }

    /**
     * 退出登录
     */
    private fun showLogoutDialog() {
        CustomDialog.Builder(mActivity)
            .setTitle("app00032")
            .setMessage("app00313")
            .setNegativeButton("app00030") { dialogInterface, _ -> dialogInterface.dismiss() }
            .setPositiveButton("app01108") { dialogInterface, _ ->
                dialogInterface.dismiss()
                showLoading()
                UserApiManager.logout(object : JCTResponseListener<String> {
                    override fun onSuccess(body: String) {
                        handleLogout()
                    }

                    override fun onError(message: String) {
                        handleLogout()
                    }
                })

            }.create().show()
    }

    private fun setVersionClick() {
        binding.llLaboratory.visible(com.blankj.utilcode.util.AppUtils.isAppDebug())
        if (com.blankj.utilcode.util.AppUtils.isAppDebug()) {
            binding.llLaboratory.setOnClickListener {
                LeMessageManager.getInstance().dispatchMessage(
                    LeMessage(
                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                        LaboratoryActivityConfig(this)
                    )
                )
            }
        }
    }

    private fun setResourceBundle(){
        binding.llResourceBundle.visible(com.blankj.utilcode.util.AppUtils.isAppDebug())
        binding.llResourceBundle.setOnClickListener {
            val intent = Intent(this, ResourceBundleActivity::class.java)
            startActivity(intent)
        }
    }

    private fun switchDebugMode(isOpen: Boolean) {

        if (isOpen) {
            CustomDialog.Builder(this)
                .setTitle("app00032")
                .setMessage("app01451")
                .setCancel(true)
                .setDismissListener {
                    if (!openDebug) binding.swMode.isChecked = false
                }
                .setPositiveButton(
                    "app01108",
                    { dialogInterface: DialogInterface, i: Int ->
                        openDebug = true
                        dialogInterface.dismiss()
                        PermissionDialogUtils.showSDPermissionDialog(this, RequestCode.MORE,
                            object :
                                XPermissionUtils.OnPermissionListener {
                                override fun onPermissionGranted() {

                                    PrintLog.init(this@SettingActivity)
                                    PrintConnLog.init(this@SettingActivity)
                                    BaseApplication.getInstance().initLog(isOpen)
                                    PrintLog.enableDebug()
                                    PrintConnLog.enableDebug()
                                    binding.swMode.isChecked = true
                                }

                                override fun onPermissionDenied(
                                    deniedPermissions: Array<out String>?,
                                    alwaysDenied: Boolean
                                ) {
                                    showToast("app01370")
                                    openDebug = false
                                    binding.swMode.isChecked = false
                                }

                            })
                    })
                .setNegativeButton(
                    "app00030",
                    { dialogInterface: DialogInterface, i: Int ->
                        openDebug = false
                        dialogInterface.dismiss()
                    }
                ).create().show()
        } else {
            val jsonObject = JSONObject()
            jsonObject["action"] = "uploadLogFileToSls"
            EventBus.getDefault().post(any2Json(jsonObject))
            if (openDebug) {
                showLoadingWithText("app01453")
                PrintConnLog.disableDebug()
                PrintLog.disableDebug()
                LogUploadUtil.uploadLogFiles{ resultCode, message ->
                    MainScope().launch {
                        when(resultCode) {
                            0 -> showUploadSuccessDialog(message)
                            else -> {
                            }
                        }
                        dismissLoading()
                    }
                }
                openDebug = false
            }
        }

    }

    var logUploadResultDialog: SuperDialog? = null
    private fun showUploadSuccessDialog(message: String) {
        if (logUploadResultDialog?.isShowing == true) return
        val displayMsg = getLogUploadMessage(message)
        logUploadResultDialog = CustomDialog.Builder(mActivity).setMessage(displayMsg).setTitle("app01452").setNegativeButton("app00030") { it, _ ->
            it.dismiss()
        }.setPositiveButton("app01457") { it, _ ->
            putTextIntoClip(displayMsg)
            it.dismiss()
        }.create()
        logUploadResultDialog?.show()
    }

    private fun getLogUploadMessage(message: String): String {
        val msgJB = JSON.parseObject(message, Log::class.java)
        val sb = StringBuilder()
        sb.append("${LanguageUtil.findLanguageString("app01454", this)}： ${msgJB.content["record_time"]}\n")
        sb.append("${LanguageUtil.findLanguageString("app01455", this)}： ${msgJB.content["device_id"]}\n")
        sb.append("${LanguageUtil.findLanguageString("app01456", this)}： ${msgJB.content["macNo"]}\n")
        return sb.toString()
    }

    private fun putTextIntoClip(code: String) {
        try{
            val clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
            clipboardManager.setPrimaryClip(ClipData.newPlainText("lable", code))
        } catch (e: Exception) {e.printStackTrace()}
    }

    override fun onDestroy() {
        super.onDestroy()
        logUploadResultDialog?.dismiss()
        binding.llLaboratory.gone()
        EventBusUtils.unregister(this)
    }

    private fun handleLogout() {
        dismissLoading()
        TemplateSyncLocalUtils.stopTemplateSyncProcessIfNecessary()
        DataCacheUtils.clearAllCache(mActivity)
        LoginDataEnum.unLogin()
        binding.tvLogout.visibility = View.GONE
        finish()
    }

    /**
     * 注销确认框
     */
    private fun showAppLogoutDlg(){
        AppPrivacySettingsUtil.showAppLogoutDlg(this,object : AppPrivacySettingsUtil.AppPrivacySettingsLisener{
            override fun reInitUi() {

                if (LoginDataEnum.needBindMainAccount()){
                    NiimbotGlobal.gotoFlutterPage(
                        "socialAccountLogout",
                        hashMapOf("token" to HttpTokenUtils.getAccessToken())
                    )
                }else{
                    NiimbotGlobal.gotoFlutterPage(
                        "mainAccountLogout",
                        hashMapOf("token" to HttpTokenUtils.getAccessToken())
                    )
                }
            }
        })
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFinishTemplateIndustryEvent(event: FinishTemplateIndustryEvent?) {
        onBackPressed()
    }

    private fun getRecommendSwitch(): Boolean{
        return PreferencesUtils.getBoolean("tag_ad_recommend", false)
    }

    private fun freshRecommendSwitch(isChecked: Boolean){
        PreferencesUtils.put("tag_ad_recommend", isChecked)
    }
}
