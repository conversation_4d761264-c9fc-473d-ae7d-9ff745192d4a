<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical">

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <RelativeLayout
      android:id="@+id/paper_info_rl"
      android:layout_width="0dp"
      android:layout_height="66dp"
      android:layout_marginTop="12dp"
      android:layout_weight="1"
      android:background="@drawable/shape_white_10"
      android:paddingStart="16dp"
      android:paddingEnd="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/paper_info_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="app100001743"
        android:textColor="@color/c161616"
        android:textSize="13sp"
        tools:text="标签纸余量" />

      <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">

        <RelativeLayout
          android:id="@+id/paper_total"
          android:layout_width="45dp"
          android:layout_height="5dp"
          android:layout_marginBottom="18dp"
          android:background="@drawable/consumable_total">

          <View
            android:id="@+id/paper_remain"
            android:layout_width="36dp"
            android:layout_height="match_parent"
            android:background="@drawable/consumable_remain_high" />

        </RelativeLayout>

        <com.qyx.languagelibrary.LanguageTextView
          android:id="@+id/paper_use_up_tv"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginBottom="12dp"
          android:ellipsize="end"
          android:maxLines="1"
          android:text="app100001749"
          android:textColor="@color/red_fb4b42"
          android:textSize="12sp"
          android:visibility="gone"
          tools:text="已耗尽" />

      </RelativeLayout>

      <RelativeLayout
        android:id="@+id/shop_paper_rl"
        android:layout_width="40dp"
        android:layout_height="26dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bg_shop_buy">

        <ImageView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_centerInParent="true"
          android:src="@drawable/shop_buy" />

      </RelativeLayout>

    </RelativeLayout>

    <View
      android:id="@+id/rfid_space"
      android:layout_width="12dp"
      android:layout_height="66dp"/>

    <RelativeLayout
      android:id="@+id/ribbon_info_rl"
      android:layout_width="0dp"
      android:layout_height="66dp"
      android:layout_marginTop="12dp"
      android:layout_weight="1"
      android:background="@drawable/shape_white_10"
      android:paddingStart="16dp"
      android:paddingEnd="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/ribbon_info_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="app01222"
        android:textColor="@color/c161616"
        android:textSize="13sp"
        tools:text="碳带余量" />

      <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">

        <RelativeLayout
          android:id="@+id/ribbon_total"
          android:layout_width="45dp"
          android:layout_height="5dp"
          android:layout_marginBottom="18dp"
          android:background="@drawable/consumable_total"
          android:visibility="gone">

          <View
            android:id="@+id/ribbon_remain"
            android:layout_width="18dp"
            android:layout_height="match_parent"
            android:background="@drawable/consumable_remain_middle" />

        </RelativeLayout>

        <com.qyx.languagelibrary.LanguageTextView
          android:id="@+id/ribbon_use_up_tv"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginBottom="12dp"
          android:ellipsize="end"
          android:maxLines="1"
          android:text="app100001749"
          android:textColor="@color/red_fb4b42"
          android:textSize="12sp"
          tools:text="已耗尽" />

      </RelativeLayout>

      <RelativeLayout
        android:id="@+id/shop_ribbon_rl"
        android:layout_width="40dp"
        android:layout_height="26dp"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="8dp"
        android:background="@drawable/bg_shop_buy">

        <ImageView
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_centerInParent="true"
          android:src="@drawable/shop_buy" />

      </RelativeLayout>

    </RelativeLayout>

  </LinearLayout>

  <LinearLayout
    android:id="@+id/tube_calibration_ll"
    android:layout_width="match_parent"
    android:layout_height="65dp"
    android:layout_marginTop="12dp"
    android:gravity="center_vertical"
    android:paddingHorizontal="16dp"
    android:orientation="horizontal"
    android:background="@drawable/shape_white_10">

    <LinearLayout
      android:layout_width="0dp"
      android:layout_height="wrap_content"
      android:layout_weight="6"
      android:orientation="vertical">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="app100001463"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:ellipsize="end"
        tools:text="走管校准"/>

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/tube_calibration_status_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:text="app100001468"
        android:textColor="#999999"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="12sp"
        tools:text="未校准"/>

    </LinearLayout>

    <RelativeLayout
      android:layout_width="0dp"
      android:layout_height="match_parent"
      android:layout_weight="4">

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/tube_calibration_tv"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true"
        android:background="@drawable/bg_shop_buy"
        android:paddingHorizontal="12dp"
        android:gravity="center"
        android:text="app00931"
        android:textColor="@color/red_fb4b42"
        android:singleLine="true"
        android:ellipsize="end"
        android:textSize="13sp"
        tools:text="校准"/>

    </RelativeLayout>

  </LinearLayout>

  <RelativeLayout
    android:id="@+id/printer_startup_action_ll"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:layout_marginTop="12dp"
    android:background="@drawable/shape_white_10">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="match_parent"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="16dp"
      android:paddingEnd="8dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app100001020"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="自定义开机键" />

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/printer_startup_action_tv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="16dp"
        android:drawableEnd="@drawable/arrow_up_down"
        android:drawablePadding="4dp"
        android:gravity="end"
        android:text="app100001030"
        android:textColor="#999999"
        android:textSize="14sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="打印上一张" />

    </LinearLayout>

    <View
      android:id="@+id/printer_startup_action_pop_anchor"
      android:layout_width="1dp"
      android:layout_height="16dp"
      android:layout_alignParentEnd="true"
      android:layout_centerVertical="true"
      android:layout_marginEnd="200dp" />

  </RelativeLayout>

  <LinearLayout
    android:id="@+id/device_other_info_ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <LinearLayout
      android:id="@+id/wifi_config_ll"
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="16dp"
      android:paddingEnd="8dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app100001746"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="WiFi配置" />

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/wifi_config_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:drawableEnd="@drawable/ic_more"
        android:drawablePadding="4dp"
        android:paddingEnd="4dp"
        android:textColor="#999999"
        android:textSize="14sp"
        tools:text="NIIMBOT" />

    </LinearLayout>

    <View
      android:id="@+id/paper_calibration_divider"
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <LinearLayout
      android:id="@+id/paper_calibration_ll"
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingHorizontal="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app00900"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="走纸校准" />

      <ImageView
        android:id="@+id/paper_calibration_arrow_iv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:src="@drawable/ic_more" />

    </LinearLayout>

    <View
      android:id="@+id/auto_shutdown_config_divider"
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <RelativeLayout
      android:id="@+id/auto_shutdown_config_ll"
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:background="@drawable/shape_white_10">

      <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <com.qyx.languagelibrary.LanguageTextView
          android:layout_width="0dp"
          android:layout_height="wrap_content"
          android:layout_weight="1"
          android:maxLines="2"
          android:ellipsize="end"
          android:text="app00920"
          android:textColor="@color/c161616"
          android:textSize="15sp"
          tools:text="自动关机" />

        <com.qyx.languagelibrary.LanguageTextView
          android:id="@+id/auto_shutdown_config_tv"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content"
          android:layout_marginStart="16dp"
          android:drawableEnd="@drawable/arrow_up_down"
          android:drawablePadding="4dp"
          android:textColor="#999999"
          android:textSize="14sp"
          tools:text="15分钟" />

      </LinearLayout>

      <View
        android:id="@+id/auto_shutdown_config_pop_anchor"
        android:layout_width="1dp"
        android:layout_height="16dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="200dp" />

    </RelativeLayout>

    <View
      android:id="@+id/startup_shutdown_voice_divider"
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <LinearLayout
      android:id="@+id/startup_shutdown_voice_ll"
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingHorizontal="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app100000623"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="开关机提示音" />

      <Switch
        android:id="@+id/startup_shutdown_voice_sw"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginStart="16dp"
        android:textOff=""
        android:textOn=""
        android:thumb="@drawable/sw_selector"
        android:track="@drawable/sw_track" />

    </LinearLayout>

    <View
      android:id="@+id/bluetooth_connected_voice_divider"
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <LinearLayout
      android:id="@+id/bluetooth_connected_voice_ll"
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingHorizontal="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app100001019"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="蓝牙连接断开音" />

      <Switch
        android:id="@+id/bluetooth_connected_voice_sw"
        android:layout_width="wrap_content"
        android:layout_height="25dp"
        android:layout_marginStart="16dp"
        android:textOff=""
        android:textOn=""
        android:thumb="@drawable/sw_selector"
        android:track="@drawable/sw_track" />

    </LinearLayout>

  </LinearLayout>

  <LinearLayout
    android:id="@+id/device_version_info_ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical">

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingHorizontal="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app00486"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="固件版本" />

      <TextView
        android:id="@+id/firmware_version_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="#999999"
        android:textSize="14sp"
        tools:text="3.0.5" />

      <com.qyx.languagelibrary.LanguageTextView
        android:id="@+id/firmware_upgrade_tv"
        android:layout_width="wrap_content"
        android:layout_height="26dp"
        android:layout_marginStart="6dp"
        android:background="@drawable/bg_shop_buy"
        android:gravity="center"
        android:paddingHorizontal="12dp"
        android:text="app100001745"
        android:textColor="@color/red_fb4b42"
        android:textSize="13sp"
        tools:text="升级" />

    </LinearLayout>

    <View
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="16dp"
      android:paddingEnd="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app00485"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="硬件版本" />

      <TextView
        android:id="@+id/hardware_version_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="#999999"
        android:textSize="14sp"
        tools:text="3.0.5" />

    </LinearLayout>

    <View
      android:layout_width="match_parent"
      android:layout_height="0.5dp"
      android:layout_marginStart="16dp"
      android:background="@color/cEBEBEB" />

    <LinearLayout
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:gravity="center_vertical"
      android:orientation="horizontal"
      android:paddingStart="16dp"
      android:paddingEnd="16dp">

      <com.qyx.languagelibrary.LanguageTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="app100000107"
        android:textColor="@color/c161616"
        android:textSize="15sp"
        android:maxLines="2"
        android:ellipsize="end"
        tools:text="序列号" />

      <TextView
        android:id="@+id/serial_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:textColor="#999999"
        android:textSize="14sp"
        tools:text="C6270004" />

    </LinearLayout>

  </LinearLayout>

  <LinearLayout
    android:id="@+id/device_recovery_ll"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="12dp"
    android:background="@drawable/shape_white_10"
    android:orientation="vertical"
    android:visibility="gone">

    <com.qyx.languagelibrary.LanguageTextView
      android:layout_width="match_parent"
      android:layout_height="44dp"
      android:paddingHorizontal="16dp"
      android:gravity="center_vertical"
      android:text="app100002039"
      android:textColor="@color/c161616"
      android:textSize="15sp"
      android:maxLines="2"
      android:ellipsize="end"
      tools:text="恢复机器出厂设置..." />

  </LinearLayout>

  <View
    android:layout_width="match_parent"
    android:layout_height="12dp"/>

</LinearLayout>

