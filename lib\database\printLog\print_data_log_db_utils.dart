import 'package:flutter/material.dart';
import 'package:isar/isar.dart';
import 'package:text/application.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/database/printLog/print_data_log_model.dart';

class PrintDataLogDbUtils {
  /// 获取打印日志
  static Future<List<PrintDataLogModel>> getPrintDataLogs() async {
    final isar = DBManagerUtil.instance.isar;
    List<PrintDataLogModel> adList = await isar.printDataLogModels.where().findAll();
    return adList;
  }

  static Future<List<Map<String, dynamic>>> getPrintDataLogsMap() async {
    List<PrintDataLogModel> printDataLogs = await getPrintDataLogs();
    return printDataLogs.map((e) => toJson(e)).toList();
  }

  static Future<PrintDataLogModel> getPrintDataLogForUniqueValue(String uniqueValue) async {
    try {
      final isar = DBManagerUtil.instance.isar;
      // 使用 isar.writeTxn 进行事务操作
      final model = await isar.writeTxn(() async {
        // 查询数据
        return await isar.printDataLogModels.filter().uniqueValueEqualTo(uniqueValue).findFirst();
      });

      // 确保返回非空值
      return model ?? PrintDataLogModel();
    } catch (e, s) {
      // 打印错误信息
      debugPrint('--------$e--------');
      // 返回默认的 PrintDataLogModel 对象
      return PrintDataLogModel();
    }
  }

  /// 打印日志存入数据库
  static Future<bool> insertPrintDataLog(PrintDataLogModel model) async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      isar.printDataLogModels.put(model);
    });
    return true;
  }

  /// 从本地数据库捞需要上传的打印日志（status状态不等于3）
  /// 把这些打印日志标记为正在上传状态，status置为3
  static Future<List<PrintDataLogModel>> markPrintDataLogUpload() async {
    final isar = DBManagerUtil.instance.isar;
    List<PrintDataLogModel> models = await isar.printDataLogModels.filter().not().statusEqualTo(3).findAll();
    models.forEach((e) {
      e.status = 3;
      if (e.userId == null) {
        e.userId = Application.user?.userId;
      }
    });
    await isar.writeTxn(() async {
      isar.printDataLogModels.putAll(models);
    });
    return models;
  }

  /// 本地数据库删除已成功上传的打印日志
  static Future<bool> deletePrintDataLogs(List<PrintDataLogModel> models) async {
    List<int> ids = models.map((e) => e.id).toList();
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      isar.printDataLogModels.deleteAll(ids);
    });
    return true;
  }

  /// 本地数据库更新打印日志
  static Future<bool> refreshPrintDataLogs(PrintDataLogModel models) async {
    PrintDataLogModel printContentModel = models;
    try {
      final isar = DBManagerUtil.instance.isar;
      await isar.writeTxn(() async {
        // 修改数据
        var _printContentModel =
            await isar.printDataLogModels.filter().uniqueValueEqualTo(models.uniqueValue).findFirst();
        if (_printContentModel != null) {
          printContentModel.id = _printContentModel.id;
        }
        await isar.printDataLogModels.put(printContentModel);
      });
      return true;
    } catch (e, s) {
      debugPrint('--------$e--------');
    }
    return false;
  }

  /// 本地数据库删除所有的打印日志
  static Future<bool> clearPrintDataLogs() async {
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      isar.printDataLogModels.clear();
    });
    return true;
  }

  ///本地数据库标记上传失败的打印日志
  static Future<bool> markPrintDataLogsUploadFailed(List<PrintDataLogModel> models) async {
    models.forEach((e) {
      e.status = 2;
    });
    final isar = DBManagerUtil.instance.isar;
    await isar.writeTxn(() async {
      isar.printDataLogModels.putAll(models);
    });
    return true;
  }

  /// 获取未上传的打印日志(status为0或2的记录)
  static Future<List<PrintDataLogModel>> getUnuploadedLogs() async {
    final isar = DBManagerUtil.instance.isar;
    List<PrintDataLogModel> models = await isar.printDataLogModels
        .filter()
        .statusEqualTo(1)
        .or()
        .statusEqualTo(2)
        .findAll();
    return models;
  }

  /// 使用事务删除打印日志
  static Future<bool> deletePrintDataLogsWithTransaction(List<PrintDataLogModel> models) async {
    final isar = DBManagerUtil.instance.isar;
    try {
      await isar.writeTxn(() async {
        List<int> ids = models.map((e) => e.id).toList();
        await isar.printDataLogModels.deleteAll(ids);
      });
      return true;
    } catch (e) {
      debugPrint('删除打印日志失败: $e');
      return false;
    }
  }

  static PrintDataLogModel fromJson(Map<String, dynamic> map) {
    PrintDataLogModel printDataLogModel = PrintDataLogModel();
    printDataLogModel.addTime = map['addTime'];
    printDataLogModel.allTimes = map['allTimes'];
    //app版本号
    printDataLogModel.applicationVersion = map['applicationVersion'];
    printDataLogModel.city = map['city'];
    printDataLogModel.country = map['country'];
    printDataLogModel.district = map['district'];
    //固件版本号
    printDataLogModel.firmwareVersion = map['firmwareVersion'];
    //硬件版本号
    printDataLogModel.hardwareVersion = map['hardwareVersion'];
    printDataLogModel.isCloudTemplate = map['isCloudTemplate'];
    printDataLogModel.latitude = map['latitude'];
    printDataLogModel.longitude = map['longitude'];
    printDataLogModel.macNo = map['macNo'];
    printDataLogModel.machineId = map['machineId'];
    printDataLogModel.machineStatus = map['machineStatus'];
    printDataLogModel.printedProcess = map['printedProcess'];
    printDataLogModel.number = map['number'];
    printDataLogModel.oneCode = map['oneCode'];
    printDataLogModel.phoneBrand = map['phoneBrand'];
    //打印方式：1-蓝牙，2-局域网，3-云端，4-数据线
    printDataLogModel.printStyle = map['printStyle'] is num ? map['printStyle'].toString() : map['printStyle'];
    printDataLogModel.province = map['province'];
    printDataLogModel.recordSource = map['recordSource'];
    //打印记录类别：0-普通，1-RFID
    printDataLogModel.recordType = map['recordType'] is String ? int.tryParse(map['recordType']) : map['recordType'];
    printDataLogModel.rfidPrintNumber = map['rfidPrintNumber'];
    printDataLogModel.rfidSerialNumber = map['rfidSerialNumber'];
    if (map['ribbonUsed'] != null) {
      printDataLogModel.ribbonUsed = (map['ribbonUsed'] as num).toDouble();
    } else {
      printDataLogModel.ribbonUsed = 0;
    }
    printDataLogModel.street = map['street'];
    printDataLogModel.successTimes = map['successTimes'];
    printDataLogModel.systemType = map['systemType'];
    printDataLogModel.systemVersion = map['systemVersion'];
    printDataLogModel.templeteId = map['templeteId'];
    printDataLogModel.cloudTemplateId = map['cloudTemplateId'];
    printDataLogModel.sourceId = map['sourceId'];
    printDataLogModel.width = map['width'];
    printDataLogModel.height = map['height'];
    //打印完成时间（毫秒时间戳）
    printDataLogModel.printFinishTime =
        map['printFinishTime'] is String ? int.tryParse(map['printFinishTime']) : map['printFinishTime'];
    //打印类型：1-批量打印 （包含excel、商品的多张打印），0-普通打印
    printDataLogModel.printType = map['printType'] is String ? int.tryParse(map['printType']) : map['printType'];
    //包含商品个数
    printDataLogModel.commodityCount =
        map['commodityCount'] is String ? int.tryParse(map['commodityCount']) : map['commodityCount'];
    printDataLogModel.uniqueValue = map['uniqueValue'];
    printDataLogModel.userId = map['userId'] is String ? int.tryParse(map['userId']) : map['userId'];
    printDataLogModel.deviceId = map['deviceId'];
    printDataLogModel.deviceRegionCode =
        map['deviceRegionCode'] is String ? int.tryParse(map['deviceRegionCode']) : map['deviceRegionCode'];
    printDataLogModel.printChannel = map['printChannel'];
    //本次打印纸张打印长度cm
    printDataLogModel.paperLengthUsedQuantity = map['paperLengthUsedQuantity'];
    //rfid纸张使用总长度cm
    printDataLogModel.paperLengthUsedQuantitySum = map['paperLengthUsedQuantitySum'];
    printDataLogModel.device_id_dot = map['device_id_dot'];
    printDataLogModel.probationPrivilege = map['probationPrivilege'];
    //状态：0-默认，1-离线存储，2-网络请求失败存储，3-上传中
    printDataLogModel.status = map['status'];
    printDataLogModel.printStrategy = map['printStrategy'];
    printDataLogModel.illegalCode = map['illegalCode'];
    return printDataLogModel;
  }

  static Map<String, dynamic> uploadToJson(PrintDataLogModel printDataLogModel) {
    Map<String, dynamic> map = toJson(printDataLogModel);
    map.remove('status');
    // 直接移除,尝试解决接口调用了 未上传到阿里云日志  -wy
    map.remove('printedProcess');
    if (map['probationPrivilege'] == null || (map['probationPrivilege'] as String).isEmpty) {
      map.remove('probationPrivilege');
    }
    return map;
  }

  static Map<String, dynamic> toJson(PrintDataLogModel printDataLogModel) {
    Map<String, dynamic> map = {};
    map['addTime'] = printDataLogModel.addTime;
    map['allTimes'] = printDataLogModel.allTimes;
    //app版本号
    map['applicationVersion'] = printDataLogModel.applicationVersion;
    map['city'] = printDataLogModel.city;
    map['country'] = printDataLogModel.country;
    map['district'] = printDataLogModel.district;
    //固件版本号
    map['firmwareVersion'] = printDataLogModel.firmwareVersion;
    //硬件版本号
    map['hardwareVersion'] = printDataLogModel.hardwareVersion;
    map['isCloudTemplate'] = printDataLogModel.isCloudTemplate;
    map['latitude'] = printDataLogModel.latitude;
    map['longitude'] = printDataLogModel.longitude;
    map['macNo'] = printDataLogModel.macNo;
    map['machineId'] = printDataLogModel.machineId;
    map['machineStatus'] = printDataLogModel.machineStatus;
    map['printedProcess'] = printDataLogModel.printedProcess;
    map['number'] = printDataLogModel.number;
    map['oneCode'] = printDataLogModel.oneCode;
    map['phoneBrand'] = printDataLogModel.phoneBrand;
    //打印方式：1-蓝牙，2-局域网，3-云端，4-数据线
    map['printStyle'] = printDataLogModel.printStyle;
    map['province'] = printDataLogModel.province;
    map['recordSource'] = printDataLogModel.recordSource;
    //打印记录类别：0-普通，1-RFID
    map['recordType'] = printDataLogModel.recordType;
    map['rfidPrintNumber'] = printDataLogModel.rfidPrintNumber;
    map['rfidSerialNumber'] = printDataLogModel.rfidSerialNumber;
    map['ribbonUsed'] = printDataLogModel.ribbonUsed;
    map['street'] = printDataLogModel.street;
    map['successTimes'] = printDataLogModel.successTimes;
    map['systemType'] = printDataLogModel.systemType;
    map['systemVersion'] = printDataLogModel.systemVersion;
    map['templeteId'] = printDataLogModel.templeteId;
    map['cloudTemplateId'] = printDataLogModel.cloudTemplateId;
    map['sourceId'] = printDataLogModel.sourceId;
    map['width'] = printDataLogModel.width;
    map['height'] = printDataLogModel.height;
    //打印完成时间（毫秒时间戳）
    map['printFinishTime'] = printDataLogModel.printFinishTime;
    //打印类型：1-批量打印 （包含excel、商品的多张打印），0-普通打印
    map['printType'] = printDataLogModel.printType;
    //包含商品个数
    map['commodityCount'] = printDataLogModel.commodityCount;
    map['uniqueValue'] = printDataLogModel.uniqueValue;
    map['userId'] = printDataLogModel.userId;
    map['deviceId'] = printDataLogModel.deviceId;
    map['deviceRegionCode'] = printDataLogModel.deviceRegionCode;
    map['printChannel'] = printDataLogModel.printChannel;
    //本次打印纸张打印长度cm
    map['paperLengthUsedQuantity'] = printDataLogModel.paperLengthUsedQuantity;
    //rfid纸张使用总长度cm
    map['paperLengthUsedQuantitySum'] = printDataLogModel.paperLengthUsedQuantitySum;
    map['device_id_dot'] = printDataLogModel.device_id_dot;
    map['probationPrivilege'] = printDataLogModel.probationPrivilege;
    //状态：0-默认，1-离线存储，2-网络请求失败存储，3-上传中
    map['status'] = printDataLogModel.status;
    map['printStrategy'] = printDataLogModel.printStrategy;
    map['illegalCode'] = printDataLogModel.illegalCode;
    map['offlinePrint'] = printDataLogModel.offlinePrint;
    return map;
  }
}
