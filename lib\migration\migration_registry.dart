import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:text/utils/niimbot_db_config.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'migration_engine.dart';
import 'migration_handler.dart';

class MigrationRegistry {
  static final _handlers = <TableDescrp, MigrationHandler>{};

  static void register<T>(TableDescrp type, MigrationHandler<T> handler) {
    _handlers[type] = handler;
  }

  static Future<void> runAllMigrations() async {
    for (final handler in _handlers.values) {
      // if (await handler.isMigrated && Platform.isAndroid) {
      //   debugPrint('MigrationManager ${handler.migrationKey} 已迁移，跳过');
      //   LogUtil.d('MigrationManager ${handler.migrationKey} 已迁移，跳过');
      //   continue;
      // }
      final engine = MigrationEngine(handler);

      // 监听迁移进度
      engine.status.addListener(() {
        if (engine.status.value.isDone) {
          debugPrint('MigrationManager迁移完成：${engine.status.value.total} 条');
          LogUtil.d('MigrationManager迁移完成：${engine.status.value.total} 条');
          NiimbotLogTool.writeLogToFile({"MigrationManager": 'MigrationManager迁移完成：${engine.status.value.total} 条'});
        } else {
          debugPrint('MigrationManager迁移进度：${engine.status.value.current}/${engine.status.value.total}');
          LogUtil.d('MigrationManager迁移进度：${engine.status.value.current}/${engine.status.value.total}');
          NiimbotLogTool.writeLogToFile({"MigrationManager": 'MigrationManager迁移进度：${engine.status.value.current}/${engine.status.value.total}'});
        }
      });
      await engine.execute();
    }
  }
}
