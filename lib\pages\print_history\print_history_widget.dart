import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:graphql_flutter/graphql_flutter.dart';
import 'package:intl/intl.dart' hide TextDirection;
import 'package:niim_login/login_plugin/login_plugin_host_api.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:niim_login/login_plugin/widget/jc_custom_toast_util.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/utils/print_channel.dart';
import 'package:text/log_utils.dart';
import 'package:text/macro/color.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/pages/print_history/print_history_setting.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/gray_config_manager.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/image_utils.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/vip_helper.dart';
import 'package:text/widget/state_layout.dart';

import '../../application.dart';
import '../../network/entity/user.dart';
import '../../template/constant/template_version.dart';
import '../../utils/cachedImageUtil.dart';
import '../../utils/svg_icon.dart';
import '../../vipTrial/trial_activity.dart';
import '../../vipTrial/vip_trial_manager.dart';

enum HistroyState { normal, showList, showToast, netError }

class PrintHistoryWidget extends StatefulWidget {
  const PrintHistoryWidget({Key? key}) : super(key: key);

  @override
  State<PrintHistoryWidget> createState() => _PrintHistoryWidgetState();
}

class _PrintHistoryWidgetState extends State<PrintHistoryWidget> {
  final EasyRefreshController _refreshController = EasyRefreshController();
  final ScrollController _scrollController = ScrollController();
  final List _models = [];
  StateType _stateType = StateType.loading;
  int _total = 0;
  // int? _vipStatus;
  var isShowToast = HistroyState.normal;

  /// 非 vip 最大允许查看条数
  static const int _allowCount4NotVip = 10;
  static const int _pageSize4NotVip = _allowCount4NotVip;
  static const int _pageSize = 10;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      /// 仅 iOS 实现了通道接口
      // ToNativeMethodChannel().getVipStatus().then((value) {
      //   _vipStatus = value;
      //   _refresh();
      //
      //   /// 埋点 vip 开通展示的次数
      //   if (!_isVip()) {
      //     ToNativeMethodChannel().sendTrackingToNative({"track": "show", "posCode": "065_165", "ext": {}});
      //   }
      // });

      /// 监听原生打印记录刷新
      NiimbotEventBus.getDefault().register(this, (data) {
        try {
          if (data is Map && data.containsKey('refreshPrintRecordList')) {
            Future.delayed(const Duration(milliseconds: 3000), () {
              if (mounted) {
                try {
                  _refresh();
                  _scrollController.animateTo(-0.0001,
                      duration: const Duration(milliseconds: 100), curve: Curves.linear);
                } catch (_) {}
              }
            });
          } else if (data is Map && data.containsKey("userInfo")) {
            if (mounted) {
              try {
                _refresh();
              } catch (_) {}
            }
          }
        } catch (e) {
          Log.d('--> $e');
        }
      });

      ToNativeMethodChannel().sendTrackingToNative({"track": "view", "posCode": "065", "ext": {}});
    });

    isShowRecordToast();
    if (!_isVip()) {
      ToNativeMethodChannel().sendTrackingToNative({"track": "show", "posCode": "065_165", "ext": {}});
    }
    // _refresh();
  }

  @override
  void dispose() {
    if (mounted) {
      JCCustomToastUtil().closeProgress(context);
      NiimbotEventBus.getDefault().unregister(this);
    }
    super.dispose();
  }

  //非vip和vip过期都会显示开通vip banner
  bool _isShowVipBanner() {
    return VipHelper.getVipType() == VIPType.neverPurchase || VipHelper.getVipType() == VIPType.expired;
  }

  _isVip() {
    return VipHelper.isVip();
  }

  _getPageSize() {
    return _isVip() ? _pageSize : _pageSize4NotVip;
  }

  _refresh() {
    _request();
  }

  _requestNextPage() {
    if (!_isVip() || _models.length >= _total) {
      /// 不是 vip, 禁止翻页
      Future.delayed(const Duration(milliseconds: 200), () {
        try {
          _refreshController.finishLoad(success: true);
        } catch (_) {}
      });
      return;
    }

    _request(loadMore: true);
  }

  _request({bool loadMore = false}) {
    String gql = '''
    query userTemplateContentHistoryPage(\$startHistoryId: String, \$pageSize: Float) {
      userTemplateContentHistoryPage( startHistoryId: \$startHistoryId pageSize: \$pageSize ) {
        total
        content {
          userId
          historyId
          copies
          deviceId
          linkedData
          printerName
          systemType
          templateName
          thumbnail
          timestamp
          uniqueId
          hasVipRes
        }
      }
    }
    ''';

    GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {
              'startHistoryId': (!loadMore || _models.isEmpty) ? '' : _models.last['historyId'],
              'pageSize': _getPageSize()
            },
            authorization: true,
            headers: {"niimbot-user-agent": Application.agent})
        .then((QueryResultWrapper wrapper) {
      if (mounted) {
        JCCustomToastUtil().closeProgress(context);
        QueryResult value = wrapper.queryResult;
        if (!value.hasException && value.data != null) {
          Map? map = value.data!['userTemplateContentHistoryPage'];
          if (map != null && map.isNotEmpty && map['content'] is List) {
            List list = map['content'];
            _total = map['total'];
            setState(() {
              _stateType = StateType.none;
              if (!loadMore) {
                _models.clear();
              }
              _models.addAll(list);
            });

            try {
              if (loadMore) {
                _refreshController.finishLoad(noMore: !(_models.length < _total));
              } else {
                _refreshController.finishRefresh();
                _refreshController.resetLoadState();
              }
            } catch (_) {}
          } else {
            /// error
            _handleRequestError(loadMore: loadMore);
          }
        } else {
          /// error
          _handleRequestError(loadMore: loadMore);
        }
      }
    });
  }

  _handleRequestError({bool loadMore = false}) {
    try {
      if (loadMore) {
        _refreshController.finishLoad(noMore: true);
      } else {
        _refreshController.finishRefresh();
      }
    } catch (_) {}

    if (loadMore) {
      showToast(intlanguage('app01160', '数据请求失败'));
    } else {
      setState(() {
        _stateType = StateType.error;
      });
    }
  }

  void showToast(String string, {Function? dismiss}) {
    JCCustomToastUtil().showToast(context, string, dismiss: dismiss);
  }

  _saveUserConfig(bool isOpen, {bool isNeedLoading = true}) {
    String gql = '''
        mutation saveUserConfig(\$input: UserConfigInput!){
          saveUserConfig(input:\$input)
        }
        ''';

    Map arg = {
      'configKey': "PRINT_RECORD",
      'status': isOpen,
    };
    GraphQLUtils.sharedInstance()
        .mutate(
      gql,
      variables: {"input": arg},
      authorization: true,
    )
        .then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException && result.data!["saveUserConfig"] != null) {
        FToast().init(context).showToast(
            child: showIconToast(isOpen ? intlanguage('app100000724', '开启成功!') : intlanguage('app100001727', '关闭成功!')),
            toastDuration: Duration(seconds: 2),
            positionedToastBuilder: (context, child, gravity) {
              return Container(
                child: child,
              );
            });
        isShowToast = HistroyState.showList;
        new Future.delayed(Duration(seconds: 1), () {
          // setState(() {});
          isShowRecordToast(isNeedLoading: false);
        });
      } else {
        var error = intlanguage('app01139', '网络异常');
        showToast(error);
      }
    });
  }

  isShowRecordToast({bool isNeedLoading = true}) {
    if (!Application.networkConnected) {
      isShowToast = HistroyState.netError;
      setState(() {});
      return;
    }
    if (isNeedLoading == true) {
      JCCustomToastUtil().showProgress(context);
    }

    String gql = '''
        query getUserSystemConfig{
          getUserSystemConfig{
          config
          userId
          }
        }
        ''';
    GraphQLUtils.sharedInstance().query(gql,
        authorization: true, headers: {"niimbot-user-agent": Application.agent}).then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;

      if (!result.hasException &&
          result.data!["getUserSystemConfig"] != null &&
          result.data!["getUserSystemConfig"]["config"] != null) {
        var printRecord = result.data!["getUserSystemConfig"]["config"]["PRINT_RECORD"];
        if (printRecord != null && printRecord["status"] != null && bool.tryParse(printRecord["status"]) == true) {
          isShowToast = HistroyState.showList;
          _refresh();
        } else {
          refreshShowToastState();
        }
      } else {
        refreshShowToastState();
      }
    });
  }

  void refreshShowToastState() {
    JCCustomToastUtil().closeProgress(context);
    isShowToast = HistroyState.showToast;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white, // 添加明确的背景色
        appBar: AppBar(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            centerTitle: true,
            title: Text(
              intlanguage('app100000396', '打印记录'),
              style: const TextStyle(color: KColor.title, fontSize: 17, fontWeight: FontWeight.w600),
            ),
            leading: InkWell(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 20),
                child: Image(
                  image: ImageUtils.getAssetImage('pop_window_back_icon'),
                  matchTextDirection: true,
                ),
              ),
              onTap: () {
                back2Native({'vcName': 'sourcePage', 'needConnectPrinter': false, "popDeriction": "left"});
              },
            ),
            actions: [
              Offstage(
                offstage: (Application.networkConnected == false),
                child: InkWell(
                  onTap: _handleClear,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  child: isShowToast != HistroyState.showList
                      ? Container()
                      : Padding(
                          padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                          child: SvgIcon(
                            //unable_history_clear
                            _models.length > 0
                                ? 'assets/images/print_history/history_clear.svg'
                                : "assets/images/print_history/unable_history_clear.svg",
                            width: 26,
                            height: 26,
                          )),
                ),
              ),
              Offstage(
                offstage: (Application.networkConnected == false),
                child: InkWell(
                  onTap: _clickHistorySetting,
                  highlightColor: Colors.transparent,
                  splashColor: Colors.transparent,
                  child: isShowToast == HistroyState.showToast
                      ? Container()
                      : Padding(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              15, 0, 16, 0), //const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                          child: const SvgIcon(
                            'assets/images/print_history/history_setting.svg',
                            width: 26,
                            height: 26,
                          )),
                ),
              ),
            ]),
        body: _selectShowHistroy());
  }

  _clickHistorySetting() {
    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "065_333", "ext": {}});
    showModalBottomSheet(
      barrierColor: Colors.black.withOpacity(0.35),
      context: context,
      isScrollControlled: true,
      enableDrag: true,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return PrintHistorySetting(isOpen: !(isShowToast == HistroyState.showToast));
      },
    ).then((value) {
      if (value != null) {
        bool isOpen = value;
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "065_333_309",
          "ext": {"state": isOpen ? "1" : "0"}
        });
        bool originState = !(isShowToast == HistroyState.showToast);
        //状态有变更才做提交
        if (originState != isOpen) {
          _saveUserConfig(isOpen, isNeedLoading: false);
        }
      }
    }).whenComplete(() {});
  }

  _selectShowHistroy() {
    switch (isShowToast) {
      case HistroyState.normal:
        return Container(color: KColor.content_background);
      case HistroyState.showToast:
        return _openRecordToast();
      case HistroyState.showList:
        return Container(
          color: _models.isEmpty ? Colors.white : KColor.content_background,
          child: Column(
            children: [
              Offstage(
                offstage: !_isShowVipBanner(),
                child: Container(
                    padding: EdgeInsetsDirectional.fromSTEB(10, 5, 16, 5),
                    width: double.infinity,
                    color: Color(0xFFFFF6E8),
                    child: Row(
                      children: [
                        Padding(
                            padding: const EdgeInsetsDirectional.fromSTEB(3, 0, 5, 0),
                            child: const SvgIcon(
                              'assets/images/print_history/print_history_vip_icon.svg',
                              width: 20,
                              height: 20,
                            )),
                        Expanded(
                          child: Text(
                            intlanguage('app100001726', '开通VIP尊享【100条】云端打印记录'),
                            //textAlign: TextAlign.left,
                            style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF9D6C33)),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            _handleOpenVip();
                          },
                          child: Container(
                            padding: EdgeInsetsDirectional.fromSTEB(13, 4, 13, 4),
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Color(0xFFFFEED6), Color(0xFFF0CB8E)], // 渐变色的起始和结束颜色
                                begin: Alignment.topCenter, // 渐变开始的方向
                                end: Alignment.bottomCenter, // 渐变结束的方向
                              ),
                              borderRadius: BorderRadius.all(Radius.circular(13)),
                            ),
                            child: Text(
                              VipHelper.getVipType() == VIPType.expired
                                  ? intlanguage('app01515', '续费')
                                  : intlanguage('app100000711', '去开通'),
                              textAlign: TextAlign.center,
                              style:
                                  const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF9D6C33)),
                            ),
                          ),
                        )
                      ],
                    )),
              ),
              Expanded(child: _buildHistoryList()),
            ],
          ),
          //child: _buildHistoryList()
        );
      case HistroyState.netError:
        return Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Center(
              child: Container(
                margin: EdgeInsets.only(bottom: 40),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(ImageUtils.getImgPath('no_net'), fit: BoxFit.contain),
                    Text(
                      intlanguage('app100000625', '当前网络状态异常'),
                      style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                    ),
                    GestureDetector(
                      onTap: () {
                        if (Application.networkConnected) {
                          isShowRecordToast();
                        }
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        width: 88,
                        height: 30,
                        margin: EdgeInsets.only(top: 12),
                        decoration: new BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(15)), color: ThemeColor.listBackground),
                        child: Center(
                          child: Text(
                            intlanguage('app100000626', '重新试试'),
                            style: TextStyle(color: ThemeColor.brand, fontSize: 13, fontWeight: FontWeight.w600),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ));
      default:
        return Container(color: KColor.content_background);
    }
  }

  ///开启打印记录
  _openRecordToast() {
    return Container(
      color: KColor.WHITE,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            height: 123,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 23.0),
            child: Image(image: ImageUtils.getAssetImage('print_history/record_toast_bg')),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 39.0),
            alignment: Alignment.center,
            child: Text(intlanguage('app100000721', '开启打印记录后，将存储你的打印记录到你个人的云端1'),
                style: const TextStyle(color: KColor.title, fontSize: 15, fontWeight: FontWeight.w600),
                textAlign: TextAlign.center),
          ),
          Container(
            margin: EdgeInsets.only(top: 8),
            padding: const EdgeInsets.symmetric(horizontal: 39.0),
            alignment: Alignment.center,
            child: Text(intlanguage('app100000722', '非VIP用户可存储最近10条打印记录，VIP用户可存储最近100条打印记录。'),
                style: const TextStyle(color: KColor.subtitle, fontSize: 13, fontWeight: FontWeight.w400),
                textAlign: TextAlign.center),
          ),
          GestureDetector(
            onTap: () {
              ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "065_334", "ext": {}});
              _saveUserConfig(true);
            },
            child: Container(
              margin: EdgeInsets.only(top: 30),
              padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 8),
              decoration: const BoxDecoration(
                color: KColor.COLOR_FFEDEC,
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              child: Text(intlanguage('app100000723', '开启打印记录'),
                  style: const TextStyle(color: KColor.RED, fontSize: 15, fontWeight: FontWeight.w600),
                  textAlign: TextAlign.center),
            ),
          )
        ],
      ),
    );
  }

  showIconToast(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 18,
                width: 18,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: ThemeColor.background),
              ),
              Image.asset(
                'assets/images/industry_template/home/<USER>',
                height: 20,
                width: 20,
              ),
            ],
          ),
          SizedBox(
            width: 6.0,
          ),
          Text(
            text, //isOpen ? intlanguage('app100000724', '开启成功!') : intlanguage('app100001727', '关闭成功!'),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.background),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    if (_stateType != StateType.none) {
      return StateLayout(
        type: _stateType,
        hintText: _stateType == StateType.error ? intlanguage('app01160', '数据请求失败') : '',
        action: NetworkAction.none,
      );
    }

    return EasyRefresh(
      enableControlFinishRefresh: true,
      // enableControlFinishLoad: true,
      controller: _refreshController,
      scrollController: _scrollController,
      header: BallPulseHeader(color: KColor.RED),
      footer: !(_models.length < _total) ? BallPulseFooter(color: KColor.WHITE) : BallPulseFooter(color: KColor.RED),
      emptyWidget: _models.isEmpty
          ? Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    height: 220,
                    child: StateLayout(
                      type: StateType.empty,
                      hintText: intlanguage('app100000397', '暂无打印记录'),
                      action: NetworkAction.none,
                    ),
                  ),
                  const SizedBox(
                    height: 40,
                  )
                ],
              ),
            )
          : null,
      child: ListView.builder(
          itemBuilder: (context, index) {
            /// 未大于可预览条数
            return _buildItem(index);
          },
          itemCount: _models.length),
      onRefresh: () async {
        _refresh();
      },
      onLoad: () async {
        _requestNextPage();
      },
    );
  }

  Widget _buildItem(int index) {
    Map data = _models[index];
    String? excelData = data['linkedData'];
    return Container(
      margin: const EdgeInsets.fromLTRB(16, 12, 16, 0),
      decoration: const BoxDecoration(
        color: KColor.WHITE,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    _handleEdit(data);
                    ToNativeMethodChannel().sendTrackingToNative({
                      "track": "click",
                      "posCode": "065_164_163",
                      "ext": {'hid': data['historyId'] ?? ''}
                    });
                  },
                  child: Stack(
                    children: [
                      Container(
                        height: 170,
                        decoration: const BoxDecoration(
                          color: KColor.content_background,
                          borderRadius: BorderRadius.all(Radius.circular(7)),
                        ),
                        padding: const EdgeInsets.all(10),
                        child: CacheImageUtil().netCacheImage(
                          height: 170,
                          // imageUrl: data['thumbnail'] + "?x-oss-process=image/resize,w_500/quality,q_10",
                          imageUrl: data['thumbnail'] + "?x-oss-process=image/resize,w_500/quality,q_10",
                          width: MediaQuery.sizeOf(context).width - 26 * 2,
                          filterQuality: FilterQuality.high,
                          useOldImageOnUrlChange: true,
                          errorWidget: const SvgIcon('assets/images/industry_template/home/<USER>'),
                        ),
                      ),
                      _isVipTemplate(data)
                    ],
                  ),
                )
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.fromLTRB(15, 0, 15, 14),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text.rich(
                        TextSpan(
                          children: [
                            ..._buildTemplateFlagWidget(data),
                            TextSpan(
                              text: data['templateName'] ?? '',
                              style: const TextStyle(
                                  fontSize: 13, fontWeight: FontWeight.w400, color: KColor.subtitle, height: 1.2),
                            ),
                            // _buildAttributeLabel(data['templateName'])
                          ],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(
                        child: _buildAttributeLabel(DateFormat("yyyy-MM-dd HH:mm")
                            .format(DateTime.fromMillisecondsSinceEpoch(data['timestamp'] ?? 0)))),
                  ],
                  crossAxisAlignment: CrossAxisAlignment.start,
                ),
                const SizedBox(
                  height: 5,
                ),
                Row(
                  children: [
                    Expanded(
                        child: _buildAttributeLabel('${intlanguage('app100000109', '打印机')}：${data['printerName']}')),
                    const SizedBox(
                      width: 10,
                    ),
                    Expanded(child: _buildAttributeLabel('${intlanguage('app01103', '打印份数')}：${data['copies']}')),
                  ],
                  crossAxisAlignment: CrossAxisAlignment.start,
                )
              ],
            ),
          ),
          Container(
            height: 0.5,
            color: KColor.color_divider,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ...[
                Expanded(
                    child: InkWell(
                  onTap: () {
                    _handleDelete(_models[index]);
                  },
                  child: SizedBox(
                    height: 40,
                    child: Center(
                      child: Text(
                        intlanguage('app01059', '删除'),
                        style: const TextStyle(fontSize: 13, color: KColor.RED),
                      ),
                    ),
                  ),
                )),
                Container(
                  height: 22,
                  width: 0.5,
                  color: KColor.color_divider,
                )
              ],
              Expanded(
                  child: InkWell(
                onTap: () {
                  _handleEdit(_models[index]);
                },
                child: SizedBox(
                  height: 40,
                  child: Center(
                    child: Text(
                      intlanguage('app00336', '编辑'),
                      style: const TextStyle(fontSize: 13, color: KColor.title),
                    ),
                  ),
                ),
              )),
              Container(
                height: 22,
                width: 0.5,
                color: KColor.color_divider,
              ),
              Expanded(
                  child: InkWell(
                onTap: () {
                  _handlePrint(_models[index]);
                },
                child: SizedBox(
                  height: 40,
                  child: Center(
                      child: Stack(
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Text(
                          intlanguage('app00016', '打印'),
                          style: TextStyle(fontSize: 13, color: KColor.title),
                        ),
                      ),
                    ],
                  )),
                ),
              )),
            ],
          )
        ],
      ),
    );
  }

  List<WidgetSpan> _buildTemplateFlagWidget(Map data) {
    List<WidgetSpan> widgets = [];
    if (isExcelData(data)) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_excel_template.svg',
          ),
        ),
      ));
    } else if (isGoodLibData(data)) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_good_template.svg',
          ),
        ),
      ));
    }
    return widgets;
  }

  _isVipTemplate(Map model) {
    if (model['hasVipRes'] == true) {
      return PositionedDirectional(
          top: 4,
          start: 4,
          child: Container(
              child: SvgIcon(
            'assets/images/icon_flag_vip_template.svg',
          )));
    } else {
      return Container();
    }
  }

  bool isExcelData(Map data) {
    if (isGoodLibData(data)) {
      return false;
    }
    bool isExcel = false;
    //兼容老模版带excelid的数据
    if (((data['linkedData'] ?? '') as String).contains('excelId') &&
        (jsonDecode(data['linkedData'])["excelId"] as String).isNotEmpty &&
        jsonDecode(data['linkedData'])["excelId"] != "0") {
      isExcel = true;
    }
    //新画板保存后会清除externalData造成excelId丢失的问题
    if (((data['linkedData'] ?? '') as String).contains('type') &&
        (jsonDecode(data['linkedData'])["type"] as String).isNotEmpty &&
        jsonDecode(data['linkedData'])["type"] == "excel") {
      isExcel = true;
    }
    return isExcel;
  }

  bool isGoodLibData(Map data) {
    bool isGoodLib = false;
    if (((data['linkedData'] ?? '') as String).contains('type') &&
        (jsonDecode(data['linkedData'])["type"] as String).isNotEmpty &&
        jsonDecode(data['linkedData'])["type"] == "commodity") {
      isGoodLib = true;
    }
    return isGoodLib;
  }

  void _handleEdit(Map model) {
    _requestTemplateContent(model, (String content) {
      ///编辑无需校验VIP是否有效
      _toCanvasEditPage(model, content);
    });

    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "065_164_165",
      "ext": {'hid': model['historyId'] ?? ''}
    });
  }

  void _toCanvasEditPage(Map model, String content) {
    Application.sp.setInt("PrintCount", model['copies']);
    CustomNavigation.gotoNextPage('ToTemplatePage', {
      'content': content ?? '',
      'uniqueId': model['uniqueId'] ?? '',
      'linkedData': model['linkedData'] ?? '',
      'isNewVersion': true,
      'printChannelCode': PrintChannelCode.printRecord.featureCode
    });
  }

  void _handlePrint(Map model) async {
    // 查看当前机型
    HardwareModel? hardwareModel = await HardWareManager.instance().getPrioritizationHardware();
    // B32R才显示RFID标签,此处判断id和name
    bool isShowRFID = hardwareModel?.id == '38' && hardwareModel?.name == 'B32R' ? true : false;
    //isNewVersion 表示新版的打印记录里面点击的打印
    _requestTemplateContent(model, (String content) {
      Map<String, dynamic> jsonMap = jsonDecode(content);
      TemplateData _templateData = TemplateData.fromJson(jsonMap);
      if (_templateData.needVip() && !VipHelper.isVip()) {
        VIPType vipType = VipHelper.getVipType();
        if (vipType == VIPType.expired) {
          VipHelper.showVipExpiredDialog(context, (buyResult) {
            if (buyResult) {
              setState(() {});
              _toPrintSettingPage(model, content, isShowRFID);
            }
          });
        } else {
          VipHelper.showOpenVipDialog(context, (buyResult) {
            if (buyResult) {
              setState(() {});
              _toPrintSettingPage(model, content, isShowRFID);
            }
          });
        }

        return;
      } else {
        _toPrintSettingPage(model, content, isShowRFID);
      }
    });
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "065_164_166",
      "ext": {'hid': model['historyId'] ?? ''}
    });
  }

  void _toPrintSettingPage(Map model, String content, bool isShowRFID) {
    Map<String, dynamic> templateDataMap = Map<String, dynamic>.from(jsonDecode(content));
    //  Application.sp.setInt("PrintCount", model['copies']);
    VoidCallback toPrintPageCallback = () {
      CustomNavigation.gotoNextPage('ToPrintSettingPage', {
        'content': content ?? '',
        'uniqueId': model['uniqueId'] ?? '',
        'linkedData': model['linkedData'] ?? '',
        'copies': model['copies'] ?? '',
        'isNewVersion': true,
        'showRfid': isShowRFID,
        'fromPrintHistory': true,
        'rfidInfo': '',
        'printChannelCode': PrintChannelCode.printRecord.featureCode
      });
    };
    //检查是否存在实时时间 根据VIP试用规则执行
    if (/*templdateData.isContaintInstantTime()*/ false) {
      VipTrialManager().trialActivity(
          context: context,
          trialActivityType: TrialActivityType.instantTimePrint,
          trailActivity: () {
            toPrintPageCallback();
          });
    } else {
      toPrintPageCallback();
    }
  }

  void _requestTemplateContent(Map model, Function(String content) callback) {
    JCCustomToastUtil().showProgress(context);

    String gql = '''
    query getTemplateContentHistoryContent(\$historyId: String!) {
      getTemplateContentHistoryContent( historyId: \$historyId )
    }
    ''';

    GraphQLUtils.sharedInstance()
        .query(gql,
            variables: {'historyId': model['historyId']},
            authorization: true,
            headers: {"niimbot-user-agent": Application.agent})
        .then((QueryResultWrapper wrapper) async {
      JCCustomToastUtil().closeProgress(context);
      Future.delayed(Duration(milliseconds: 200), () async{
        QueryResult value = wrapper.queryResult;
      if (!value.hasException && value.data != null) {
        String? content = value.data!['getTemplateContentHistoryContent'];
        if (content != null) {
          Map<String, dynamic> templateData = json.decode(content);
          templateData["fromOldVersion"] = 2;
          templateData["profile"]["extrain"]["userId"] = "-1";
          var task = templateData["task"];
          if (task == null || task is String) {
            task = <String, dynamic>{};
            task["modifyData"] = <String, dynamic>{};
            task["externalDataID"] = "";
            templateData["task"] = task;
          } else if (task is Map<String, dynamic>) {
            var modifyData = task["modifyData"];
            var externalDataId = task["externalDataID"];

            if (modifyData == null || modifyData is String) {
              task["modifyData"] = <String, dynamic>{};
            }
            if (externalDataId == null) {
              task["externalDataID"] = "";
            }
          }
          var externalData = templateData["externalData"];
          //不在需要externalData数据
          externalData = null;
          if (externalData == null || externalData is String) {
            externalData = <String, dynamic>{};
            externalData["list"] = <dynamic>[];
            externalData["fileName"] = "";
            templateData["externalData"] = externalData;
          } else if (externalData is Map<String, dynamic>) {
            var list = externalData["list"];
            var fileName = externalData["fileName"];
            if (list == null || list is String) {
              externalData["list"] = <dynamic>[];
            }
            if (fileName == null) {
              externalData["fileName"] = "";
            }
          }
          var elements = templateData["elements"];
          if (elements is List) {
            for (var element in elements) {
              if (element is Map<String, dynamic> && element.containsKey("colorReverse")) {
                var colorReverse = element["colorReverse"];
                if (colorReverse is bool) {
                  element["colorReverse"] = colorReverse ? 1 : 0;
                }
              }
            }
          }

          if (templateData["templateVersion"] != null && templateData["templateVersion"].isNotEmpty) {
            String isCanOpen =
                await ToNativeMethodChannel().isCanOpenTemplate(templateVersion: templateData["templateVersion"]);
            if (isCanOpen == "0") {
              if(templateData["templateVersion"] == TemplateVersion.PC_MULTI_IMAGE_VERSION) {
                showToast(intlanguage('app100002075', '请使用电脑端最新版本打开'));
              } else {
                showToast(intlanguage('app100000343', '版本过低，请升级APP'));
              }
              return;
            }
          }

          if (templateData["dataSources"] != null &&
              templateData["dataSources"] is List &&
              templateData["dataSources"].length > 0){
            templateData["dataSource"] = templateData["dataSources"];
          }

          if (templateData["dataSourceModifies"] != null &&
              templateData["dataSourceModifies"] is Map &&
              (templateData["dataSourceModifies"] as Map).isNotEmpty){
            templateData["modify"] = templateData["dataSourceModifies"];
          }

          //  pc端保存的新json格式的画板 在灰度老画板的情况下 不允许打印和跳转 //GrayConfigManager
          if (GrayConfigManager().grayModule(module: GrayModule.newDrawBoard) == GrayBranch.branchA) {
            if (templateData["dataSource"] != null &&
                templateData["dataSource"] is List &&
                templateData["dataSource"].length > 0 &&
                ((templateData["hasExternalData"] == null) || templateData["hasExternalData"].isEmpty) &&
                templateData["templateVersion"] != null &&
                templateData["templateVersion"].isNotEmpty) {
              showToast('仅支持PC端编辑此模版');
              return;
            }
          }

          // callback(content);
          callback(jsonEncode(templateData));
        } else {
          /// error
          showToast(intlanguage('app01160', '数据请求失败'));
        }
      } else {
        if ((value.exception?.graphqlErrors ?? []).length > 0 && value.exception?.graphqlErrors.first.message != null) {
          showToast(value.exception!.graphqlErrors.first.message);
        } else {
          /// error
            showToast(intlanguage('app01160', '数据请求失败'));
          }
        }
      });
    });
  }

  /// 清空记录
  _handleClear() {
    if (_models == null || _models.length == 0) {
      return;
    }
    showCustomDialog(context, intlanguage('app100000742', '确认清空所有打印记录吗?'), intlanguage('app100000743', '清空后将无法恢复'),
        leftFunStr: intlanguage('app00030', '取消'),
        rightFunStr: intlanguage('app01121', '清空'),
        rightTextColor: ThemeColor.brand,
        leftFunCall: () {}, rightFunCall: () {
      JCCustomToastUtil().showProgress(context);

      String gql = '''
      mutation cleanUserTemplateContentHistory {
        cleanUserTemplateContentHistory
      }
    ''';

      GraphQLUtils.sharedInstance().mutate(gql,
          authorization: true, headers: {"niimbot-user-agent": Application.agent}).then((QueryResultWrapper wrapper) {
        JCCustomToastUtil().closeProgress(context);

        QueryResult value = wrapper.queryResult;
        if (!value.hasException && value.data != null) {
          bool? content = value.data!['cleanUserTemplateContentHistory'];
          if (content == true) {
            FToast().init(context).showToast(
                child: showIconToast(intlanguage('app100001730', '清空成功！')),
                toastDuration: Duration(seconds: 2),
                positionedToastBuilder: (context, child, gravity) {
                  return Container(
                    child: child,
                  );
                });
            // showToast(intlanguage('app100001730', '清空成功！'));
            setState(() {
              _models.clear();
              _total = 0;
            });
          } else {
            /// error
            showToast(intlanguage('app01160', '数据请求失败'));
          }
        } else {
          /// error
          showToast(intlanguage('app01160', '数据请求失败'));
        }
      });
    });

    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "065_168", "ext": {}});
  }

  /// 单条记录移除
  void _handleDelete(Map model) {
    showCustomDialog(context, intlanguage('app100000401', '确认删除该打印记录吗?'), intlanguage('app100000402', '删除后将无法恢复'),
        leftFunStr: intlanguage('app00030', '取消'),
        rightFunStr: intlanguage('app00063', '删除'),
        leftFunCall: () {}, rightFunCall: () {
      JCCustomToastUtil().showProgress(context);

      String gql = '''
      mutation deleteUserTemplateContentHistory(\$historyId: String!) {
        deleteUserTemplateContentHistory( historyId: \$historyId)
      }
    ''';

      GraphQLUtils.sharedInstance()
          .mutate(gql,
              variables: {'historyId': model['historyId']},
              authorization: true,
              headers: {"niimbot-user-agent": Application.agent})
          .then((QueryResultWrapper wrapper) {
        JCCustomToastUtil().closeProgress(context);

        QueryResult value = wrapper.queryResult;
        if (!value.hasException && value.data != null) {
          bool? content = value.data!['deleteUserTemplateContentHistory'];
          if (content == true) {
            FToast().init(context).showToast(
                child: showIconToast(intlanguage('app01188', '删除成功')),
                toastDuration: Duration(seconds: 2),
                positionedToastBuilder: (context, child, gravity) {
                  return Container(
                    child: child,
                  );
                });
            setState(() {
              _models.remove(model);
              _total--;
              if (_models.length < _getPageSize()) {
                _refresh();
              }
            });
          } else {
            /// error
            showToast(intlanguage('app01160', '数据请求失败'));
          }
        } else {
          /// error
          showToast(intlanguage('app01160', '数据请求失败'));
        }
      });
    });

    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "065_164_164",
      "ext": {'hid': model['historyId'] ?? ''}
    });
  }

  void _handleOpenVip() {
    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "065_165_167", "ext": {}});
    CustomNavigation.gotoNextPage('ToVipPage', {}).then((value) {
      if (kDebugMode) {
        Log.d('ToVipPage, result: $value ${value is Map}');
      }
      if (value is Map && value['result'] is int && value['result'] > 0) {
        /// 已经开通 vip
        // setState(() {
        //   _vipStatus = 1;
        //   _stateType = StateType.loading;
        // });
        // _refresh();
      }
    });
  }

  Widget _buildAttributeLabel(String content) {
    return Text(
      content ?? '',
      style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: KColor.subtitle, height: 1.2),
    );
  }
}
