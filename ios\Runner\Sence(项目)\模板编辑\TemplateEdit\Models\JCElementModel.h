//
//  JCElementModel.h
//  Runner
//
//  Created by xingling xu on 2020/3/11.
//  Copyright © 2020 Jingchen Technology Co.Ltd . All rights reserved.
//

#import <Foundation/Foundation.h>
#import <YYModel/YYModel.h>

NS_ASSUME_NONNULL_BEGIN

@protocol JCElementModel
@end

@interface JCElementModel : XYBaseModel<NSCoding, NSCopying>
//#############################################################
//                            Common
//#############################################################
@property (nonatomic, copy)     NSString *address;              /** 本地使用：新建元素的内存地址 */
@property (nonatomic, copy)     NSString *elementId;            /** 元素id：时间戳精确到毫秒 */
@property (nonatomic, copy)     NSString *type;                 /** 本地使用:元素类型，*/
@property (nonatomic, assign)   JCTemplateType templateType;    /** 本地使用:元素类型，*/
@property (nonatomic, assign)   CGFloat x;                      /** 左上角为0，单位mm, 可为负数，*/
@property (nonatomic, assign)   CGFloat y;                      /** 左上角为0，单位mm, 可为负数，*/
@property (nonatomic, assign)   CGFloat width;                  /** 元素实际宽度，单位mm */
@property (nonatomic, assign)   CGFloat height;                 /** 元素实际高度，单位mm */
@property (nonatomic, assign)   CGFloat screenX;                /** 屏幕坐标，本地使用 单位像素 */
@property (nonatomic, assign)   CGFloat screenY;                /** 屏幕坐标，本地使用 单位像素 */
@property (nonatomic, assign)   CGFloat screenWidth;            /** 屏幕宽度，本地使用 单位像素 */
@property (nonatomic, assign)   CGFloat screenHeight;           /** 屏幕高度，本地使用 单位像素 */
@property (nonatomic, assign)   NSInteger isLock;                    /** 是否锁定 */
@property (nonatomic, assign)   BOOL isHidden;                  /** 是否隐藏 */
@property (nonatomic, assign)   NSInteger rotate;               /** 框绘制完成后的旋转角度 支持0/90/180/270（后期开放0-360） */
@property (nonatomic, assign)   NSInteger zIndex;               /** 设置元素的堆叠顺序 最下层为0 */
@property (nonatomic, copy)     NSString *fieldName;            /** 绑定的元素属性名称*/
@property (nonatomic, assign)   BOOL isOpenMirror;              /** 是否开启镜像 */
@property (nonatomic, copy)     NSString *mirrorId;             /** 镜像Id */
@property (nonatomic, copy)     NSString *elementVersion;              /** 版本号 */
@property (nonatomic, copy)     NSArray *elementColor;          /**元素颜色*/
@property (nonatomic, assign)   NSInteger colorReverse;        /** 设置元素反白: 0 不反白      1 反白 */
@property (nonatomic, assign)   NSInteger paperColorIndex;      /** 设置元素颜色索引 */
@property (nonatomic, assign)   NSInteger colorChannel;          /** 设置元素颜色打印档位 */
//#############################################################
//                            Text
//#############################################################
@property (nonatomic, assign)   NSInteger textAlignHorizonral;  /** 水平对齐方式 0:左对齐 1:居中对齐 2:右对齐 */
@property (nonatomic, assign)   NSInteger textAlignVertical;    /** 垂直对齐方式 0:顶对齐 1:垂直居中 2:底对齐 */
@property (nonatomic, assign)   JCTextLineMode lineMode;        /** 换行方式,默认JCTextLineModeWidthFixed */
@property (nonatomic, copy)     NSString *value;                /** 显示最终内容，表格传占位符具体参见表格，日期传毫秒时间戳  */
@property (nonatomic, assign)   CGFloat wordSpacing;            /** 字（单词）之间的标准间隔(暂时不做,预留参数) */
@property (nonatomic, assign)   CGFloat letterSpacing;          /** 字母、汉字之间的间隔 */
@property (nonatomic, assign)   CGFloat lineSpacing;            /** 行间距系数 1.0 、1.5、 2.0*/
@property (nonatomic, copy)     NSString *fontFamily;           /** 字体名称,未传输字体为空字符串时使用默认字体 */
@property (nonatomic, copy)     NSString *fontCode;             /** 字体唯一编码,未传输字体为空字符串时使用默认编码 */
@property (nonatomic, assign)   BOOL isTitle;                   /** 是否为excel数据导入标题 */
@property (nonatomic, copy)     NSString *contentTitle;                   /** excel数据标题 */
@property (nonatomic, assign)   NSInteger lineBreakMode;        /** 换行方式 0: 按字符换行(默认)    1: 英文按单词换行 */
@property (nonatomic, assign)   NSInteger textDirection;        /** 文本方向 0:从左到右(ltr) 1:从右到左(rtl) 2:自动检测(auto) */
@property (nonatomic, assign)   NSInteger typesettingMode;      /** 排版方式 1:横向 2:竖排 3:弧形 */
@property (nonatomic, copy)     NSArray *typesettingParam;      /** <int>[part1,part2]]  part1:弧形半径  part2:弧形角度  */
/*
 - fontStyle
  blod,italic,underline,overline
 1.比如:fontStyle:"blod,["blod", "italic", "underline", "overline"]表示加粗协调带删除线下划线。
 2.也可以不设置其中的某个值，比如 fontStyle:["blod", "italic"]也是允许的。未设置的属性会使用其默认值
 3.如果都是默认值，传normal或空数组
 */
@property (nonatomic, copy)     NSArray *fontStyle;             /** 字体样式 */
@property (nonatomic, assign)   CGFloat fontSize;              /** 字体大小 单位mm */
@property (nonatomic, copy)     NSString *boxStyle;             /** 文本框样式 */
@property (nonatomic, copy)     NSArray *textStyle;             /** 内容样式 */

//#############################################################
//                     Date
//#############################################################
@property (nonatomic, copy)     NSString *dateFormat;           /** "yyyy年MM月dd日" */
@property (nonatomic, copy)     NSString *timeFormat;           /** "HH:mm:ss" */
@property (nonatomic, assign)   NSInteger dateIsRefresh;             /** 是否取当前时间 */
@property (nonatomic, assign)   NSInteger timeOffset;           /*时间偏移量  画板上或预览、打印时间以(设置时间/实时时间)±偏移量为准*/
@property (nonatomic, assign)   BOOL associated;              /*第一时间是否被关联 true 被关联  false 未被关联（默认） 第二时间始终为false*/
@property (nonatomic, copy)     NSString *associateId;          /*关联Id （第一时间、第二时间此Id一致*/
@property (nonatomic, assign)   NSInteger validityPeriod;       /*有效期*/
@property (nonatomic, assign)   NSInteger validityPeriodNew;    /*新版有效期*/
@property (nonatomic, copy)     NSString *validityPeriodUnit;   /*有效期单位*/
@property (nonatomic, copy)     NSString *timeUnit;              /*12、24小时制*/
@property (nonatomic, assign)   BOOL timeOpen;
@property (nonatomic, assign)   BOOL dateOpen;
@property (nonatomic, assign)   NSInteger time;       /*有效期*/
//#############################################################
//                     Serial
//#############################################################
@property (nonatomic, copy)     NSString *prefix;               /** 流水号前缀 */
@property (nonatomic, copy)     NSString *startNumber;          /** 流水号起始值 */
@property (nonatomic, copy)     NSString *suffix;               /** 流水号后缀 */
@property (nonatomic, assign)   NSInteger fixLength;            /** 补位的长度 例如：前缀000001后缀 */
@property (nonatomic, copy)     NSString *fixValue;             /** 补位的值，默认0 */
@property (nonatomic, assign)   NSInteger incrementValue;       /** 流水号递增值 */
//#############################################################
//                     Barcode
//#############################################################
@property (nonatomic, assign)   NSInteger textPosition;         /** 0:下方显示 1:上方显示 2:不显示 */
@property (nonatomic, assign)   CGFloat textHeight;             /** 条码文本框的高度 */
@property (nonatomic, assign)   JCCodeType codeType;            /** 条码二维码编码类型 */

//#############################################################
//                     Qrcode
//#############################################################
/*
 0:L(7%)
 1:M(15%)
 2:Q(25%)
 3:H(30%)
 */
@property (nonatomic, assign)   NSInteger correctLevel;         /** 二维码识别率、容错率 */
@property (nonatomic, assign)   BOOL isLive;                    /** 是否活码类型 */
@property (nonatomic, strong)   NSString *liveCodeId;           /** 活码ID */
@property (nonatomic, assign)   BOOL isForm;                    /** 是否表单类型 */
@property (nonatomic, strong)   NSString *formId;           /** 表单ID */
//#############################################################
//                     Line  & Graph
//#############################################################
@property (nonatomic, assign)   CGFloat lineWidth;              /** 线宽 本地使用，实际转化为width属性 */
@property (nonatomic, assign)   CGFloat cornerRadius;           /** 图形圆角半径 */
@property (nonatomic, copy)     NSArray *lineColor;          /**元素颜色*/
@property (nonatomic, copy)     NSArray *contentColor;          /**元素颜色*/
@property (nonatomic, assign)   NSInteger lineColorChannel;          /**表格线框颜色索引*/
@property (nonatomic, assign)   NSInteger contentColorChannel;          /**表格文字颜色索引*/
/*
 - lineType
 1:实线
 2:虚线类型1
 3:虚线类型2(暂时不做)
 */
@property (nonatomic, assign)   NSInteger lineType;
@property (nonatomic, copy)     NSArray *dashwidth;             /** 实线虚线长度 [0.75,0.75] */
@property (nonatomic, assign)   JCGraphType graphType;

//#############################################################
//                     Image
//#############################################################
@property (nonatomic, copy)     NSString *imageData;            /** 图像base64数据 */
@property (nonatomic, copy)     NSString *imageUrl;             /** 用于网络传输，服务器存储 */
@property (nonatomic, copy)     NSString *materialId;           /** 素材id */
@property (nonatomic, copy)     NSString *localUrl;           /** 本地图片路径 */
@property (nonatomic, assign)     BOOL isDefaultImage;          /** 是否使用默认图 */
@property (nonatomic, assign)     BOOL isNinePatch;             /** 是否点9图, 点9图作为图片下的特殊类型 */
@property (nonatomic, copy)     NSString *ninePatchLocalUrl;      /** 点9图本地路径Url*/
@property (nonatomic, copy)     NSString *ninePatchUrl;             /** 点9图服务器地址,用于网络传输，上传到服务器存储 */
@property (nonatomic, copy)      NSString *materialType;          /** 素材id有值的情况下类型：图标=1，边框=2; 素材id 为空的情况下图片=1,涂鸦=2, 类型为2的能自由拉伸,/*
 - imageProcessingType :算法类型（预留）
 1:阈值法
 2:渐变
 3:默认
 */
@property (nonatomic, assign)   NSInteger imageProcessingType;
/*
 算法参数,数组类型表示不同的算法，有不同数量的参数（预留）
 */
@property (nonatomic, copy)     NSArray *imageProcessingValue;

//#############################################################
//                     Table
//#############################################################
@property (nonatomic, assign)   NSInteger row;                  /** 行数 */
@property (nonatomic, assign)   NSInteger column;               /** 列数 */
@property (nonatomic, strong)   NSArray *rowHeight;             /** NSArray<float> */
@property (nonatomic, strong)   NSArray *columnWidth;           /** NSArray<float> */
@property (nonatomic, strong)   NSArray<JCElementModel> *cells;/** 单元格数组 */
@property (nonatomic, copy)     NSString *combineId;            /** 合并后的单元格ID(可以是单元格下标的总和) */
@property (nonatomic, assign)   NSInteger rowIndex;             /** 所在行数 */
@property (nonatomic, assign)   NSInteger columnIndex;          /** 所在列数 */
@property (nonatomic,strong)    NSArray<JCElementModel> *combineCells;/** 合并的单元格数组 */
@property (nonatomic,strong)NSArray <NSString *> * dataBind;//数据源标识,表名


//
//  Returns a Boolean value indicating whether the element's double click can show keyboard
//
- (BOOL)canShowKeyboard;

/** 更新本地化的字段 */
- (void)updateLocalProperty;

- (NSString *)getDateString;
//是否是新增自定义字段的商品库元素
-(BOOL)isCustomField;
//是否是文本元素 排除数据源模版
-(BOOL)isTextElement;

- (void)fixValueExcel;
@end

NS_ASSUME_NONNULL_END
