//
//  JCNewHomeViewController.m
//  Runner
//
//  Created by huzi_0118 on 2019/3/22.
//  Copyright © 2019年 Jingchen Technology Co.Ltd . All rights reserved.
//

//VC
#import "XYTabBarController.h"
#import "JCNewHomeViewController.h"
#import "JCMyTemplateDetailViewController.h"
#import "JCVIPDetailViewController.h"
#import "JCLabelCustomizeViewController.h"
#import "JCShopMallsViewController.h"
#import "JCQAViewController.h"
#import "JCDeviceOffSetInfoModel.h"
#import "JCShopNormalVC.h"
#import "GeneratedPluginRegistrant.h"
#import "JCFlutterViewController.h"
#import "JCWebViewMoreAlert.h"
#import "JCShopProductVC.h"
#import "QQLBXScanViewController.h"
#import "JCTMDataBindGoodsInfoManager.h"
#import "JGOneKeyLoginManager.h"
#import "JCADPopView.h"
//model
#import "JCLanguageDetailModel.h"
#import "JCSwiperImageModel.h"
#import "JCLanguageModel.h"
#import "JCGoodDetailInfo.h"
#import "JCPrinterModel.h"
#import "JCRFIDDeviceModel.h"
#import "JCDownLoadManager.h"
//#import "JGQQLoginManager.h"
#import "JCPrintManager.h"
#import "JCTemplateList.h"

//view
#import "JCCollectionViewCell.h"
#import "JCNewCollectionViewCell.h"
#import "JCHomeView.h"
#import "JCHomeNoDataCell.h"

//功能类
#import "AYCheckManager.h"
#import "StyleDIY.h"
#import "Global.h"
#import "JCBluetoothManager.h"
#import "JCBluetoothManager+Connect.h"
#import "JCPDFFileHandler.h"
#import "JCGoodsElementHelp.h"
#import "JCAdTool.h"
#import "JCRFIDModel.h"
#import "JCTemplateDBManager.h"
#import "JCTemplateImageManager.h"
#import "JCPrintHistoryHelper.h"
#import "JCBluetoothManager.h"
#import "FlutterBoostUtility.h"
#import "JCGrayManager.h"
#import "JCNPSEntranceView.h"
#import <SDWebImage/SDWeakProxy.h>
#import <flutter_boost/FlutterBoost.h>
#import "JCNPSWebviewAlert.h"
#import "JCWebviewPreloadManager.h"
#import "JCNPSWebView.h"
#import "JCVIPTextBannerModel.h"
#import "JCPrintDoneAdManager.h"
# import "JPUSHService.h"
#import "JCFontManager.h"
#import "NMFAWebViewController.h"
// iOS10 注册 APNs 所需头文件
# ifdef NSFoundationVersionNumber_iOS_9_x_Max
# import <UserNotifications/UserNotifications.h>
# endif
#import <PushKit/PushKit.h>
#import <Sentry/Sentry.h>
@interface JCNewHomeViewController ()<UICollectionViewDataSource,UICollectionViewDelegate,UICollectionViewDelegateFlowLayout,WaterFlowLayoutDelegate>{
    dispatch_queue_t refreshListQuene;
    dispatch_group_t refreshListGroup;
}
@property (nonatomic, assign) BOOL isBlueOpen;
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, strong) JCHomeView *mainViwe;
@property (nonatomic, strong) NSArray *myTemplateList;
@property (nonatomic, strong) NSMutableArray *bannerList;
@property (nonatomic, strong) NSMutableArray* heightArr ;
@property (nonatomic ,strong) MBProgressHUD *progressHUD;
@property (nonatomic ,strong) JCRFIDDeviceModel *rfidDeviceModel;
@property (nonatomic ,strong) NSString *originalLang;
@property (nonatomic,strong)NSDictionary * npsData;
@property (nonatomic,strong)JCNPSEntranceView * npsView;
@property (nonatomic,strong)JCNPSEntranceView * npsVipView;
@property (nonatomic,strong)NSString * languageCode;
@end

@implementation JCNewHomeViewController

#pragma mark VC生命周期
- (void)viewDidLoad {
  refreshListQuene = dispatch_queue_create("refresh_list_queue", 0);
  refreshListGroup = dispatch_group_create();
  [super viewDidLoad];
  self.backImageView.hidden = YES;
  self.isBlueOpen = YES;
  self.NavBarHidden = YES;
  AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
  appDelegate.appLaunched = YES;
  if([appDelegate needToAppointPage]){
    [appDelegate outAppToNiimbot];
  }
  self.originalLang = XY_JC_LANGUAGE_REAL;
  [self initVersionUpdate];
  [[XYCenter sharedInstance] checkAppResource];
  self.lsl_prefersNavigationBarHidden = YES;
  //    [[JCFontManager sharedManager] downloadUserFont];
  //    [[JCLoginManager sharedInstance] getOnekeySupport];
  [JCBluetoothManager sharedInstance].isEnterHome = YES;
  NSDictionary *lastPrinterInfo = [JCBluetoothManager printerConnectStatusCache];
  NSString *lastPrinter = [[NSUserDefaults standardUserDefaults] valueForKey:LASTCONNECTPRINTERNAME];
  if(lastPrinterInfo != nil){
    [[JCBluetoothManager sharedInstance] initBluetoothBaby];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      NSString *lastConnectType = lastPrinterInfo[@"connectType"];
      if(![[JCBluetoothManager sharedInstance] getDeviceBluetoothState] && !STR_IS_NIL(lastConnectType) && [[JCBluetoothManager sharedInstance] isWiFiEnabled]){
        [[JCBluetoothManager sharedInstance] startScan:@"" searchType:SEARCH_ALL];
      }
    });
  }else if(!STR_IS_NIL(lastPrinter)){
    //        NSDictionary *printerInfo = @{@"lastPrinter":UN_NIL(lastPrinter),@"connectType":@1};
    [JCBluetoothManager sharedInstance].connectOcca = 1;
  }
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(flutterChangeTemplate:) name:FLUTTER_TEMPLATE_CHANGED object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeLanguage:) name:JCNOTICATION_ChANGELANGUAGE object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loginChanged:) name:LOGIN_CHANGED object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(templatechange:) name:TEMPLATE_CHANGED object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(adminTemplatechange:) name:ADMIN_TEMPLATE_CHANGED object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printerStatusNotification:) name:PrinterStatusNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(synchTemplateFinish:) name:JCNOTICATION_SYNC_FINISH object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(didReciveSynchNotification) name:JCNOTICATION_SYNCH_CHANGE object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(deviceWifiStateChange) name:JCNOTICATION_WIFI_STATE_CHANGE object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rfidPrinterConnectNotification:) name:RfidPrinterConnectedNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(rfidPrinterCoverOpenNotification:) name:PrinterCoverOpenNotification object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(serverStateChangedRefreshView) name:JCNOTICATION_SERVER_STATE_CHANGE object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(changeLanguage:) name:JCNOTICATION_REFRESH_THEME object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(applicationBackForegroundSwitch:) name:JCApplicationBackForegroundSwitch object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshNpsData:) name:JCNOTICATION_LANGUAGECHANGECLICK object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(closeNps) name:JCCloseNps object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadNPSWeb) name:FLUTTER_LOGIN_SUCCESS object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadNPSWeb) name:LOGOUT_SUCCESS object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadNPSVIPWeb) name:FLUTTER_LOGIN_SUCCESS object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadNPSVIPWeb) name:LOGOUT_SUCCESS object:nil];
  [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(loadNPS:) name:JCNOTICATION_VIP_NPS object:nil];
  //    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_HOMEVIEWDIDLOAD object:nil];
  //LOGOUT_SUCCESS
  if([JCBluetoothManager sharedInstance].rfidModel){
    [[NSNotificationCenter defaultCenter] postNotificationName:RfidPrinterConnectedNotification object:nil];
  }
  [self.mainViwe refreshServiceState:m_currentServerState serverStateDetail:m_currentServerStateDetail];
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.01 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    if(!JC_IS_CONNECTED_PRINTER){
      [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:3 surplusLevel:0 isAnimal:NO];
    }else{
      [self refreshRFIDViewWithType:0];
    }
  });
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    [[XYCenter sharedInstance] getCurrentLanguageFont];
    [[XYCenter sharedInstance] getAllVipLogo];
    [[XYCenter sharedInstance] getAllVipBoderMaterial];
  });
  self.languageCode = XY_JC_LANGUAGE_REAL;
  [self getNpsData:XY_JC_LANGUAGE_REAL resultCallback:nil];
  [self getVipNpsData:XY_JC_LANGUAGE_REAL resultCallback:nil];
  // 注册标签纸状态
  [self registerRfidLabelStatus];
  [JCAdTool getAdvertisement];
  if(appDelegate.needToVip){
    JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
    XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:vc];
    [self presentViewController:nav animated:YES completion:nil];
  }
  [self prepareShowData];
  
  [self registrationJpushId];
  
  // 处理冷启动时挂起的PDF文件
  [[JCPDFFileHandler sharedInstance] processPendingPDFIfNeeded];
}

- (NSString *)getCurrentDateAndTime {
  // 获取当前日期
  NSDate *currentDate = [NSDate date];
  // 创建日期格式化器
  NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
  [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];
  
  // 将日期格式化为字符串
  return [dateFormatter stringFromDate:currentDate];
  
}

-(void)getNpsShowResult:(void(^)(NSString *))back{
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"isShowNps" arguments:nil result:^(id  _Nullable result) {
    back(result);
  }
  ];
}

-(void)prepareShowData{
  XYWeakSelf;
  [self getNpsShowResult:^(NSString * isShow) {
    if (@available(iOS 13.0, *)) {
      if([isShow isKindOfClass:[NSString class]] && [isShow isEqualToString: @"1"]){
        [weakSelf prepareNpsView];
      }
    }//
  }];
}

-(void)preloadWebWithUrl:(NSString *)url isVip:(bool)vip isMiniProgram:(bool)isMiniProgram {
  if (isMiniProgram) {
    JCWebviewPreloadManager * manger = [JCWebviewPreloadManager defalutManager];
    [manger preloadWebViewWithUrlString:url webKey:NPSMiniWebKey];
  } else {
    [self preloadWebWithUrl:url isVip:vip];
  }
}

-(void)preloadWebWithUrl:(NSString *)url isVip:(bool)vip {
    JCWebviewPreloadManager * manger = [JCWebviewPreloadManager defalutManager];
    if(vip) {
        [manger preloadWebViewWithUrlString:url webKey:NPSWebVIPKey];
    }else {
        [manger preloadWebViewWithUrlString:url webKey:NPSWebKey];
    }

}

-(void)prepareNpsView{
    __weak typeof(self) weakSelf = self;
    self.npsView = [[JCNPSEntranceView alloc]initWithFrame:CGRectZero closeCallback:^{
        [weakSelf closeNpsView:true];
    } clickCallback:^{
        //[weakSelf openNpsPage];
        JC_TrackWithparms(@"click",@"112_226",@{@"source":@(1)});
        JCNPSWebviewAlert * alert = [[JCNPSWebviewAlert alloc]initWithFrame:CGRectZero webKey:NPSWebKey sku:@"" source:@"home"];
        alert.closeClick = ^{
            [weakSelf closeNpsView:false];
        };
        [alert show];
    }];
    [self.view addSubview:self.npsView];
    [self.npsView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.mas_equalTo(self.view).offset(-5);
        make.bottom.mas_equalTo(self.view).offset(-80);
    }];
    [self.view bringSubviewToFront:self.npsView];
}



-(void)closeNpsView:(BOOL)isNeedBurialPoint{
    if(isNeedBurialPoint){
        JC_TrackWithparms(@"click",@"112_225",@{@"source":@(1)});
    }
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"closeNps" arguments:nil result:nil];
    [self.npsView removeFromSuperview];
}

-(void)closeNps{
    if(self.npsView != nil){
        [self.npsView removeFromSuperview];
    }
}

-(void)refreshNpsData:(NSNotification *)notify{
    NSDictionary *data = notify.object;
    NSString *code = [data objectForKey:@"code"];
    self.languageCode = code;
    [self getNpsData:self.languageCode resultCallback:nil];
    [self getVipNpsData:self.languageCode resultCallback:nil];
}

-(void)getNpsData:(NSString *)languageCode resultCallback:(void(^)(void))resultCallback{
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getNpsJson" arguments:@{@"code":languageCode} result:^(id  _Nullable result) {
        if(![result isKindOfClass:[NSDictionary class]]) return;
        self.npsData = result;
        if(self.npsData != nil && self.npsData.allKeys.count > 0){
            [self preloadWebWithUrl:[self.npsData objectForKey:@"url"] isVip:false];
            [self preloadWebWithUrl:[self.npsData objectForKey:@"uniAppUrl"] isVip:false isMiniProgram:true];
          
            if(resultCallback != nil){
                resultCallback();
            }
        }
    }
   ];
}

-(void)getVipNpsData:(NSString *)languageCode resultCallback:(void(^)(void))resultCallback{
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getNpsVipJson" arguments:@{@"code":languageCode} result:^(id  _Nullable result) {
        if(![result isKindOfClass:[NSDictionary class]]) return;
        self.npsData = result;
        if(self.npsData != nil && self.npsData.allKeys.count > 0){
            [self preloadWebWithUrl:[self.npsData objectForKey:@"url"] isVip:true];
            if(resultCallback != nil){
                resultCallback();
            }
        }
    }
   ];
}

-(void)loadNPS:(NSNotification *)userInfo {
    NSDictionary *data = userInfo.object;
    NSString *sku = [data objectForKey:@"sku"];
    JCNPSWebviewAlert * alert = [[JCNPSWebviewAlert alloc] initWithFrame:CGRectZero webKey:NPSWebVIPKey sku:sku source:@"home"];
    [alert show];
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    [self.mainViwe refreshRightButton];
    [self.mainViwe refreshWithAnimation];
    [JCBluetoothManager sharedInstance].needShowState = NO;
    self.mainViwe.toShopButton.hidden = [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || !xy_isNorthAmerica;
    NSMutableArray *needDownloadList = [NSMutableArray array];
    for (JCTemplateData *templateData in self.myTemplateList) {
        NSString *imageUrlString = [NSString stringWithFormat:@"%@/%@_%@.png",Resource_ThumbBackImage_Path(NO),templateData.idStr,XY_JC_LANGUAGE];
        if(![[NSFileManager defaultManager] fileExistsAtPath:imageUrlString]){
            [needDownloadList addObject:templateData];
        }
    }
    if(needDownloadList.count > 0){
      // TODO: Image Migration
//        [JCTemplateImageManager forceDownLoadThumbImageForList:needDownloadList complete:^{
//            [self.mainViwe.mainCollectionView reloadData];
//            [self.mainViwe refreshContentView:self.heightArr.count];
//        }];
//        [self.mainViwe.mainCollectionView reloadData];
    }
    JC_TrackWithparms(@"view",@"003",(@{}));
    [self refreshBannerLocal];
}

- (void)viewDidAppear:(BOOL)animated{
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
    if([XYCenter sharedInstance].needGuideConnectPrinter){
        [XYCenter sharedInstance].needGuideConnectPrinter = NO;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.7 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)self.navigationController  isFromHome:NO];
        });
    }
}


- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}

- (void)dealloc
{
    JCNCRemoveOb(self, CLEAN_CAECH, nil);
    JCNCRemoveOb(self, TEMPLATE_CHANGED, nil);
    JCNCRemoveOb(self, JCNOTICATION_ChANGELANGUAGE, nil);
    JCNCRemoveOb(self, LOGIN_CHANGED, nil);
    JCNCRemoveOb(self, PrinterStatusNotification, nil);
    JCNCRemoveOb(self, JCNOTICATION_SYNC_FINISH, nil);
    JCNCRemoveOb(self, JCNOTICATION_WIFI_STATE_CHANGE,nil);
    JCNCRemoveOb(self, JCNOTICATION_LANGUAGECHANGECLICK,nil);
    JCNCRemoveOb(self, JCNOTICATION_VIP_NPS,nil);

}

- (void)initUI{
    //    self.view.backgroundColor = [UIColor whiteColor];
    [self initMainView];
    //    [self initScreentingView];

}

- (void)initData{
    self.myTemplateList = [[NSMutableArray alloc] init];
    self.bannerList = [[NSMutableArray alloc] init];
}

- (void)initNet{
    [self getTemplateList:YES];
    [self getLocalSwiper];
    [self getVIPTextBanner];
}

- (void)initMainView{
    XYWeakSelf
    CGFloat tabBarHeight = [UIApplication sharedApplication].keyWindow.safeAreaInsets.bottom + 49;
    JCHomeView *mainViwe = [[JCHomeView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - tabBarHeight)];
    mainViwe.homeOperateBlock = ^(NSNumber *type) {
        [weakSelf homeOperateWithType:type.integerValue];
    };
    mainViwe.bannerOperateBlock = ^(NSNumber *type) {
        [weakSelf homeBannerClickWithIndex:type.integerValue];
    };
    mainViwe.bannerShowBlock = ^(NSNumber *index){
        if(self.bannerList.count > 0){
            UIViewController * currentController =  [XYTool getCurrentVC];
            if([currentController isKindOfClass:[JCNewHomeViewController class]]){
                JCSwiperImageModel *model = [self.bannerList safeObjectAtIndex:index.integerValue];
              JC_TrackWithparms(@"show",@"003_081_001",(@{@"b_name":UN_NIL(model.name),@"b_banner_key":UN_NIL(model.xyid),@"a_name":UN_NIL(model.activityName)}));
            }
        }
    };
    [mainViwe setComfirBlock:^(id x) {
        //        if(JC_CURRENT_PRINTER_RFID_STATUS != 0){
        //            [[JCBluetoothManager sharedInstance] getRFIDInfo:^{
        //
        //            }];
        //        }
    }];
    [mainViwe setToShopBlock:^{
        [weakSelf goToShop];
    }];
    [mainViwe setBuyCurrentLabelBlock:^(NSString *oneCode){
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:weakSelf loginSuccessBlock:^{
            if(xy_isLogin)
            {
                JCShopProductVC *c = [[JCShopProductVC alloc] initWithTemplateType:@"1" oneCode:oneCode sourceType:1];
                c.jumpSource = @"y_page_home_rfid";
                [weakSelf.navigationController pushViewController:c animated:YES];
            }
        }];
    }];
    [mainViwe setWifiCodeMakeBlock:^{
        JCTemplateData *currentRFIDData = [JCPrintManager sharedInstance].rfidTemplateData;
        JC_TrackWithparms(@"click",@"003_148_171",(@{@"tag_id":UN_NIL(currentRFIDData.idStr)}));
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:weakSelf loginSuccessBlock:^{
            NSMutableDictionary *parms = [NSMutableDictionary dictionary];
            [parms setValue:UN_NIL(currentRFIDData.idStr) forKey:@"labelId"];
            [parms setValue:UN_NIL(currentRFIDData.name) forKey:@"labelName"];
            [parms setValue:@(true) forKey:@"isManualChangeLabel"];
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:CAPAppTrackID_WifiCode needKeepLive:YES parms:parms receiveUniappData:^(id x) {

                }];
            });
        }];
    }];
    mainViwe.mainCollectionView.delegate = self;
    mainViwe.mainCollectionView.dataSource = self;
    mainViwe.collectionLayout.delegate = self;
    [mainViwe.mainCollectionView registerNib:@"JCCollectionViewCell"];
    [mainViwe.mainCollectionView registerNib:@"JCNewCollectionViewCell"];
    [mainViwe.mainCollectionView registerClass:@"JCHomeNoDataCell"];
    mainViwe.mainCollectionView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
        [weakSelf getTemplateList:YES];

    }];
    mainViwe.mainCollectionView.mj_header.mj_h = 35;
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated"
    mainViwe.mainCollectionView.mj_footer.automaticallyHidden = YES;
#pragma clang diagnostic pop
    [self.view addSubview:mainViwe];
    self.mainViwe = mainViwe;
}

- (void)getLabelBuyStatusRequest{
    JCTemplateData *currentRFIDData = [JCPrintManager sharedInstance].rfidTemplateData;
    NSMutableArray *oneCodeArr = @[].mutableCopy;
    if(currentRFIDData.profile.extrain.barcodeCategoryMap.allValues.count > 0){
        oneCodeArr = currentRFIDData.profile.extrain.barcodeCategoryMap.allValues.mutableCopy;
    }
    JCProfileExtrain *extrain = currentRFIDData.profile.extrain;
    if(currentRFIDData.profile.barcode.length > 0 && ![oneCodeArr  containsObject:currentRFIDData.profile.barcode]) [oneCodeArr addObject:currentRFIDData.profile.barcode];
    if(extrain.sparedCode.length > 0 && ![oneCodeArr  containsObject:extrain.sparedCode]) [oneCodeArr addObject:extrain.sparedCode];
    if(extrain.virtualBarCode.length > 0 && ![oneCodeArr  containsObject:extrain.virtualBarCode]) [oneCodeArr addObject:extrain.virtualBarCode];
    if(extrain.amazonCodeWuhan.length > 0 && ![oneCodeArr  containsObject:extrain.amazonCodeWuhan]) [oneCodeArr addObject:extrain.amazonCodeWuhan];
    if(extrain.amazonCodeBeijing.length > 0 && ![oneCodeArr  containsObject:extrain.amazonCodeBeijing]) [oneCodeArr addObject:extrain.amazonCodeBeijing];
    if(oneCodeArr.count > 0){
        NSString *oneCodeString = oneCodeArr.firstObject;
        [self getShopStatusRequestWithOnecode:oneCodeString];
    }
}

- (void)getShopStatusRequestWithOnecode:(NSString *)oneCodeString{
    XYWeakSelf
    JCLabelBuyInfoModel *buyInfoModel = [[JCLabelBuyInfoModel alloc] init];
    buyInfoModel.oneCode = oneCodeString;
    buyInfoModel.buttonType = @1;
    [weakSelf.mainViwe refreshShopStatus:buyInfoModel];
}

#pragma mark 初始化
- (void)initVersionUpdate{
    AYCheckManager *checkManger = [AYCheckManager sharedCheckManager];
    checkManger.countryAbbreviation = @"cn";
    NSString *localeIdentifier = [[NSLocale currentLocale] objectForKey:NSLocaleIdentifier];
    NSArray *contentArr = [localeIdentifier componentsSeparatedByString:@"_"];
    if(contentArr.count > 1){
        NSString *country = contentArr[1];
        country = [[country componentsSeparatedByString:@"@"] safeObjectAtIndex:0];
        checkManger.countryAbbreviation = [country lowercaseString];
    }
    [checkManger checkVersion];
}

- (void)getTemplateList:(BOOL)isNeedRequest{
    XYWeakSelf
    [JCTemplateDBManager db_queryTemplateListWithPage:1 limit:10 cloudTemplate:NO
                                              needRequest:isNeedRequest success:^(NSArray * templates) {
      self.myTemplateList = templates;
      [self.mainViwe refreshContentView:self.heightArr.count];
      [self.mainViwe refreshContentView:self.myTemplateList.count];
      [self.mainViwe.mainCollectionView.mj_header endRefreshing];
      [self.mainViwe.mainCollectionView reloadData];
    } failed:^(id x) {
      [self.mainViwe.mainCollectionView.mj_header endRefreshing];
      [weakSelf.mainViwe refreshContentView:self.myTemplateList.count];
      [self.mainViwe refreshContentView:self.heightArr.count];
    }];
//    self.myTemplateList = [self getTemplateDataFromDb].mutableCopy;
//    [self.mainViwe refreshContentView:self.myTemplateList.count];
//    if(!xy_isLogin || (!jc_is_connected_wifi && !jc_is_connected_cellular_network)){
//        [self.mainViwe.mainCollectionView.mj_header endRefreshing];
//        return ;
//    }
//    [self.mainViwe.mainCollectionView reloadData];
//    NSString *lastRequestTime = m_lastRequestServerTime(J_template_my);
//    NSMutableDictionary *postParms = @{@"page":@"1",@"limit":@"10"}.mutableCopy;
//    if(!STR_IS_NIL(lastRequestTime)){
//        [postParms setValue:lastRequestTime forKey:@"fromTimestamp"];
//    }
//    [self java_postWithValues:postParms ModelType:[JCTemplateList class] Path:J_template_my hud:isShowLoading?@"":nil Success:^(__kindof YTKBaseRequest *request, JCTemplateList *requestModel) {
//        [weakSelf.mainViwe.mainCollectionView.mj_header endRefreshing];
//        NSArray *templates = requestModel.list;
//        NSMutableArray *newTemplateDataArr = [NSMutableArray array];
//        NSMutableArray *needDownImageTemplateDataArr = [NSMutableArray array];
//        for (NSInteger index = 0; index < templates.count; index++) {
//            JCTemplateData *model = [templates safeObjectAtIndex:index];
//            NSString *whereTemp = [NSString stringWithFormat:@"where idStr = '%@' and localType != '%ld'",model.idStr,JCLocalType_OffLineDelete];
//            NSArray *dbDataArr = [JCTemplateDBManager db_queryDatasWhere:whereTemp];
//            if(dbDataArr.count > 0){
//                JCTemplateData *localModel = dbDataArr.firstObject;
//                if([localModel.profile.extrain.updateTime isEqualToString:model.profile.extrain.updateTime]){
//                    localModel.thumbnail = model.thumbnail;
//                    localModel.labelNames = model.labelNames;
//                    [newTemplateDataArr addObject:localModel];
//                    NSString *localPath = [NSString stringWithFormat:@"%@/%@_%@.png",Resource_ThumbBackImage_Path(NO),model.idStr,XY_JC_LANGUAGE];
//                    if(![[NSFileManager defaultManager] fileExistsAtPath:localPath]){
//                        [needDownImageTemplateDataArr addObject:model];
//                    }
//                } else{
//                    if(localModel.localType != JCLocalType_OffLineCreate && localModel.localType != JCLocalType_OffLineUpdate && localModel.localType != JCLocalType_OffLineDelete){
//                        [newTemplateDataArr addObject:model];
//                        [needDownImageTemplateDataArr addObject:model];
//                    }
//                }
//            }else{
//                [newTemplateDataArr addObject:model];
//                [needDownImageTemplateDataArr addObject:model];
//            }
//        }
//        [[JCFMDB shareDatabase] jc_inTransaction:^(BOOL *rollback) {
//            if(*rollback){
//                NSLog(@"更新失败");
//            }else{
//                for (NSString *idStr in requestModel.deletedTemplates) {
//                    NSString *sql = [NSString stringWithFormat:@"where idStr = '%@'",idStr];
//                    [JCTemplateDBManager db_deletTemplateDataWhere:sql];
//                }
//                [JCTemplateDBManager db_updateTemplateList:newTemplateDataArr];
//            }
//        }];
//        requestModel.list = (NSArray<JCTemplateData> *)newTemplateDataArr;
//        [weakSelf downTemplateImage:needDownImageTemplateDataArr is_Cloud:NO];
//        weakSelf.myTemplateList = [weakSelf getTemplateDataFromDb].mutableCopy;
//        [weakSelf.mainViwe refreshContentView:self.myTemplateList.count];
//        //        dispatch_group_leave(refreshListGroup);
//    } failure:^(NSString *msg, id model) {
//        if([weakSelf.navigationController.visibleViewController isKindOfClass:[JCNewHomeViewController class]]){
//            if(!STR_IS_NIL(msg)){
//                [MBProgressHUD showToastWithMessageDarkColor:msg];
//            }
//        }
//        [weakSelf.mainViwe.mainCollectionView.mj_header endRefreshing];
//        [weakSelf.mainViwe refreshContentView:self.myTemplateList.count];
//        //        dispatch_group_leave(refreshListGroup);
//    }];

}

-(void)refreshBannerLocal
{
    [self getSwiperDataFromDB:^(NSArray<JCSwiperImageModel *> *swiperDataArr) {
        NSArray *bannerArr = swiperDataArr.mutableCopy;
        if(bannerArr.count > 0){
            NSString *sortString = @"sort";
            NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:sortString ascending:YES];
            NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
            NSArray *sortArray=[bannerArr sortedArrayUsingDescriptors:sortDescriptors];
            self.bannerList = [self filterAdActiveBannerDatas:sortArray.mutableCopy];
            [self.mainViwe refreshBannerWithBannerArr:[self getAdBannerDatas]];
        }else{
            [self.mainViwe refreshBannerWithBannerArr:@[]];
        }
    }];
}

//获取轮播图
- (void)getLocalSwiper{
    XYWeakSelf
    [self refreshBannerLocal];
    NSDictionary *dic = STR_IS_NIL([XYCenter sharedInstance].siteCode) ? @{} : @{@"siteCode": [XYCenter sharedInstance].siteCode};
    [self java_postWithValues:dic ModelType:[JCSwiperImageModel class] Path:J_focus_list hud:nil Success:^(__kindof YTKBaseRequest *request,   id requestModel) {
        if([requestModel isKindOfClass:[NSArray class]]){
            JCSwiperImageModel *swiperModel = [JCSwiperImageModel new];
            swiperModel.list = requestModel;
            NSArray *swiperModelArr = requestModel;
            NSArray<NSDictionary *> *resultMArr = [JSONModel arrayOfDictionariesFromModels:swiperModelArr];
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"saveBannerToDatabase" arguments:resultMArr result:^(NSNumber *value) {
                if ([value isKindOfClass:[NSNumber class]]) {
                    if (value.boolValue) {
                        NSArray *bannerArr = swiperModelArr.mutableCopy;
                        NSString *sortString = @"sort";
                        NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:sortString ascending:YES];
                        NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
                        NSArray *sortArray=[bannerArr sortedArrayUsingDescriptors:sortDescriptors];
                        weakSelf.bannerList = sortArray.mutableCopy;
                        if(weakSelf.bannerList.count > 0)
                        {
                            [weakSelf downTemplateSwiperImage:swiperModel];
                            JCSwiperImageModel *model = [self.bannerList safeObjectAtIndex:0];
                            JC_TrackWithparms(@"show",@"003_081_001",(@{@"b_name":UN_NIL(model.name),@"b_banner_key":UN_NIL(model.xyid),@"a_name":UN_NIL(model.activityName)}));
                        }else{
                            [weakSelf.mainViwe refreshBannerWithBannerArr:@[]];
                        }
                    }
                }
            }];
        }else{
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"clearAllBannerData" arguments:nil result:^(NSNumber *value) {

            }];
            [weakSelf.mainViwe refreshBannerWithBannerArr:@[]];
        }
    } failure:^(NSString *msg, id model) {

    }];
}

-(NSMutableArray *)getAdBannerDatas{
    NSMutableArray *barnnerUrlArr = [NSMutableArray array];
    for (JCSwiperImageModel *bannerModel in self.bannerList) {
        if([[JCActivityAdPopManager shareInstance] isShowWith:JCActivityAdType_homeBanner exhibitionFrequency:bannerModel.exhibitionFrequency adId:bannerModel.xyid]){
            [barnnerUrlArr addObject:bannerModel.local_path];
        }
    }
    return barnnerUrlArr;
}

/// 筛选活跃的ADBanner
/// - Parameter bannerList: ADBanner
-(NSMutableArray *)filterAdActiveBannerDatas:(NSMutableArray<JCSwiperImageModel *> *)bannerList{
    NSMutableArray *activeBarnners = [NSMutableArray array];
    for (JCSwiperImageModel *bannerModel in bannerList) {
        if([[JCActivityAdPopManager shareInstance] isShowWith:JCActivityAdType_homeBanner exhibitionFrequency:bannerModel.exhibitionFrequency adId:bannerModel.xyid]){
            [activeBarnners addObject:bannerModel];
        }
    }
    return activeBarnners;
}

- (void)getSwiperDataFromDB:(void (^)(NSArray<JCSwiperImageModel *> *swiperDataArr))result {
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getBannerFromDatabase" arguments:nil result:^(NSArray<NSDictionary *> *value) {
        NSMutableArray *swiperData = [NSMutableArray array];
        if ([value isKindOfClass:[NSArray class]]) {
            NSArray *bannerData = (NSArray *)value;
            [bannerData enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                JCSwiperImageModel *model = [[JCSwiperImageModel alloc] initWithDictionary:obj error:nil];
            NSString *fileName = model.path.lastPathComponent;
                model.local_path = [NSString stringWithFormat:@"%@/%@_%@",RESOURCE__IMAGE_SWIPER_PATH,XY_JC_LANGUAGE,fileName];
                BOOL timeIsValite = YES;
                if(!STR_IS_NIL(model.effectiveEndTime) && model.effectiveEndTime.integerValue != 0){
                    NSDate *endDate = [NSDate dateWithTimeIntervalSince1970: model.effectiveEndTime.integerValue/1000];
                    timeIsValite = [[NSDate now] compare:endDate] == NSOrderedAscending;
                }
                if(timeIsValite){
                    [swiperData addObject:model];
                }
            }];
        }
        result(swiperData);
    }];
}


- (void)downTemplateSwiperImage:(JCSwiperImageModel *)swiperImageModel{
    XYWeakSelf
    [JCDownLoadManager downloadBannerImage:swiperImageModel complateBlock:^{
        [self getSwiperDataFromDB:^(NSArray<JCSwiperImageModel *> *swiperDataArr) {
            NSArray *bannerArr = swiperDataArr.mutableCopy;
            NSString *sortString = @"sort";
            NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:sortString ascending:YES];
            NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
            NSArray *sortArray=[bannerArr sortedArrayUsingDescriptors:sortDescriptors];
            weakSelf.bannerList = [self filterAdActiveBannerDatas:sortArray.mutableCopy];
            if(weakSelf.bannerList.count > 0)
            {
                [weakSelf refreshBannerList];
            }
        }];
    }];

}

-(void)refreshBannerList{
    [self.mainViwe refreshBannerWithBannerArr:[self getAdBannerDatas]];
}

- (void)getVIPTextBanner {
    XYWeakSelf
    NSDictionary *dic = STR_IS_NIL([XYCenter sharedInstance].siteCode) ? @{} : @{@"siteCode": [XYCenter sharedInstance].siteCode};
    [self java_getWithValues:dic ModelType:[JCVIPTextBannerModel class] Path:J_VIP_Text_Banner hud:nil Success:^(__kindof YTKBaseRequest *request, JCVIPTextBannerModel* result) {
        if (result != nil) {
            NSString *where = [NSString stringWithFormat:@"where xyid = '%@'",result.xyid];
            NSArray *modelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_VIP_TEXT_BANNER dicOrModel:[JCVIPTextBannerModel class] whereFormat:where];
            if (modelArr.count > 0) {
                // 查看规则是否变更
                if (modelArr.firstObject != nil) {
                    JCVIPTextBannerModel *savedModel = modelArr.firstObject;
                    if (![savedModel.exhibitionFrequency isEqualToString:result.exhibitionFrequency]) {
                        // 规则变更
                        // 删除上次纪录，插入新纪录
                        [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_VIP_TEXT_BANNER whereFormat:where];
                        [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_VIP_TEXT_BANNER dicOrModel:result];
                    } else {
                        // 只展示一次 || 每天展示一次
                        // 保留上次关闭时间
                        if ([result.exhibitionFrequency isEqualToString:@"FIRST"] || [result.exhibitionFrequency isEqualToString:@"ONCE_A_DAY"]) {
                            result.closeTime = savedModel.closeTime;
                        }
                        [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_VIP_TEXT_BANNER dicOrModel:result whereFormat:where];
                    }
                }
            } else {
                [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_VIP_TEXT_BANNER dicOrModel:result];
            }
        }
        // 刷新VIP文本展示横幅
        [weakSelf.mainViwe refreshVIPBanner:result];
        if (result.isShow) {
            JC_TrackWithparms(@"show",@"003_286",(@{@"b_name":STR_IS_NIL(result.content) ? @"" : result.content, @"banner_id":result.xyid, @"a_name":result.activityName}));
        }
    } failure:^(NSString *msg, id model) {

    }];
}

- (void)goToShop{
    XYWeakSelf
    if(!xy_isLogin){
        [[JCLoginManager sharedInstance] checkLogin:^{

        } viewController:self loginSuccessBlock:^{
            JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"home" options:nil];
            vc.entrance_type_id = @"3";
            vc.jumpSource = @"y_page_main";
            [weakSelf.navigationController pushViewController:vc animated:YES];
        }];
        return;
    }
    JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopChildPath:@"home" options:nil];
    vc.entrance_type_id = @"3";
    vc.jumpSource = @"y_page_main";
    [self.navigationController pushViewController:vc animated:YES];
}

- (void)homeOperateWithType:(NSInteger)type{
    switch (type) {
        case 1://展示侧滑栏
        {

        }
            break;
        case 2://右上角连接蓝牙
        {
            JC_TrackWithparms(@"click",@"003_003_009",(@{}));
          [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)self.navigationController isFromHome:YES];
        }
            break;
        case 12://RFID模块连接蓝牙
        {
            JC_TrackWithparms(@"click",@"003_145_149",(@{}));
            [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)self.navigationController isFromHome:YES];
        }
            break;
        case 3://行业模板点击
        {
            // 判断行业模板灰度策略入口关闭，直接跳入新的行业模版
            // 获取标签纸信息
            JCTemplateData *data = [JCPrintManager sharedInstance].rfidTemplateData;
            JC_TrackWithparms(@"click",@"003_004_010",(@{@"type":@2}));
            // 替换为Flutter界面模版入口
            [self gotoFlutterPage:@"industryTemplate"
                        arguments:@{
                @"labelData": data == nil ? @{} : data.toDictionary,
                @"token": m_userModel.token == nil ? @"" : m_userModel.token,
            }
                   onPageFinished:^(NSDictionary *_) {
                // 页面结束回传数据
            }];
            break;
        }
        case 4://扫描打印
        {
            JC_TrackWithparms(@"click",@"003_004_011",(@{}));
            [[JCLoginManager sharedInstance] checkLogin:^{
                [JCRecordTool recordWithAction:ClickIndexScanPrint];
            } viewController:self loginSuccessBlock:^{

            }];
            break;
        }
        case 5://扫码取模
        {
            JC_TrackWithparms(@"click",@"003_004_011",(@{}));
            [self doScanWithType:0];
            [JCRecordTool recordWithAction:ClickIndexScanMode];
            break;
        }
        case 6://我的标签
        {
//            [@{@"needDesRequest":@"1",@"paperSerial":@"881dca0bb2960000",@"paperUsedQuantitySum":@40,@"paperUsed":@0,@"machineId":@"M2_H-F712010185",@"hardwareVersion":@"1.01",@"firmwareVersion":@"1.25",@"machineSecret":@""} java_postWithModelType:nil Path:@"system/server/time" hud:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
//
//            } failure:^(NSString *msg, id model) {
//
//            }];
            JC_TrackWithparms(@"click",@"003_189_187",(@{@"b_name":@"我的模板"}));
            uploadLogInfoFlutter(@"toMyTemplate", @"myTemplate",^(id x,id y){},J_get_sls_Log_Token);
            [self gotoFlutterPage:@"myTemplate"
                        arguments:@{@"token": m_userModel.token, @"isPresent": @NO,@"isEnablePopGesture": @NO}
                   onPageFinished:^(NSDictionary *_) {
                // 页面结束回传数据
            }];
            break;
        }
        case 8://登录
        {
            JC_TrackWithparms(@"click",@"003_005_461",@{});
          
            [[JCLoginManager sharedInstance] presentLoginViewController:^{

            } viewController:self loginSuccessBlock:^{

            }];
            //            [[JGWXLoginManager sharedManager] requestWXLogin:^(BOOL success, NSString *code) {
            //                // 拿到code，向服务端发送请求登录即可
            //            }];
            //
            //            [[JGQQLoginManager sharedManager] qqLogin:^(BOOL success, NSString *code) {
            //
            //            }];
            break;
        }
        case 9:{
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return ;
            }
            if(!xy_isLogin){
                [[JCLoginManager sharedInstance] checkLogin:^{

                } viewController:self loginSuccessBlock:^{

                }];
                return;
            }
            [JCRecordTool recordWithAction:ClickIndexShop];
            JCShopMallsViewController *vc = [[JCShopMallsViewController alloc] init];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case 10: {
            JC_TrackWithparms(@"click",@"003_005_161",@{});

            XYWeakSelf
            [[JCLoginManager sharedInstance] checkLogin:^{
            } viewController:self loginSuccessBlock:^{

                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    // 打印记录
                    NSString *token = m_userModel.token;
                    // 替换为Flutter界面模版入口
                    if(STR_IS_NIL(token)){
                        return;
                    }
                    [self gotoFlutterPage:@"printHistory"
                                arguments:@{@"token": m_userModel.token, @"isPresent": @NO}
                           onPageFinished:^(NSDictionary *_) {
                        // 页面结束回传数据
                    }];

                });
            }];
            break;
        }
        case 11: {
          JC_TrackWithparms(@"click",@"003_005_014",(@{}));
          uploadLogInfoFlutter(@"toMyTemplate", @"myTemplate",^(id x,id y){},J_get_sls_Log_Token);
          [self gotoFlutterPage:@"myTemplate"
                      arguments:@{@"token": m_userModel.token, @"isPresent": @NO,@"isEnablePopGesture": @NO}
                 onPageFinished:^(NSDictionary *_) {
               // 页面结束回传数据
          }];
          break;
        }
        case 101:{//RFID编辑 ，，
            [JCRecordTool recordWithAction:click_rfid_create_template];
            JCTemplateData *templateModel = self.mainViwe.rfidViewDataModel;
            if(!templateModel.isCableLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
              // 是否是危废标签纸
              if (!templateModel.isDangerLabel || ![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]) {
                  [FlutterBoostUtility gotoFlutterPage:@"labelDetailPage" arguments:@{@"shopSource":@"print_020",@"jsonData": templateModel.toJSONString} onPageFinished:^(NSDictionary *dic) {

                  }];
               } else {
                 [[JCLoginManager sharedInstance] checkLogin:^{
                 } viewController:nil loginSuccessBlock:^{
                   [[NBCAPMiniAppManager sharedInstance] checkUniMPResourceAndOpenWithId:UniAppTrackID_dangerCap needKeepLive:YES parms:nil receiveUniappData:^(id x) {

                   }];
                 }];
               }
            }else{
                NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
                NSString *canvasType = [[NSUserDefaults standardUserDefaults] stringForKey:@"CanvasType"];
                if(STR_IS_NIL(canvasType) || [canvasType isEqualToString:@"cableCanvas"]){
                    [FlutterBoostUtility gotoFlutterPage:@"cableCanvas" arguments:@{@"jsonData": templateModel.toJSONString,@"isPresent": @NO,@"fontPath": fontPath,
                                                                                    @"isEnablePopGesture": @NO} onPageFinished:^(NSDictionary *dic) {

                    }];
                }else{
                    [FlutterBoostUtility gotoFlutterPage:@"labelDetailPage" arguments:@{@"shopSource":@"print_020",@"jsonData": templateModel.toJSONString} onPageFinished:^(NSDictionary *dic) {

                    }];
                }
            }
            JC_TrackWithparms(@"click",@"003_148_151",(@{@"tag_id":templateModel.idStr}));
            break;
        }
        case 102:{//RFID历史记录
            XYWeakSelf
            [JCRecordTool recordWithAction:click_rfid_history_user_templates];
            break;
        }
        case 103:{//教程
            JC_TrackWithparms(@"click",@"003_003_008",(@{}));
            [JCRecordTool recordWithAction: click_index_tutorial];
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return;
            }
            [JCGrayJumpHelper jumpHelpCenterWithGray];
            break;
        }
        default:
            break;
    }

}


- (void)onekey_loginSuccess:(NSDictionary *)resultDic{

}

- (void)onekey_loginFailure:(NSDictionary *)resultDic{

}

- (void)changeToOtherLoginStyle{

}

- (void)needCheckPolicy{

}

//首页轮播图点击
- (void)homeBannerClickWithIndex:(NSInteger)index{
    if(self.bannerList.count > 0){
        JCSwiperImageModel *model = [self.bannerList safeObjectAtIndex:index];
        JC_TrackWithparms(@"click",@"003_081_001",(@{@"b_name":UN_NIL(model.name),@"b_banner_key":UN_NIL(model.xyid),@"a_name":UN_NIL(model.activityName)}));
        if([model.typeCode isEqualToString:@"3"]){
            XYWeakSelf
            if(!xy_isLogin){
                [[JCLoginManager sharedInstance] checkLogin:^{

                } viewController:self loginSuccessBlock:^{
                    JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:model.url];
                    vc.entrance_type_id = @"3";
                    vc.jumpSource = @"y_page_main";
                    [weakSelf.navigationController pushViewController:vc animated:YES];
                }];
                return;
            }
            [JCRecordTool recordWithAction:ClickIndexBannerShop];
            if(NETWORK_STATE_ERROR){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                return ;
            }
            JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:model.url];
            vc.entrance_type_id = @"3";
            vc.jumpSource = @"y_page_main_banner";
            [self.navigationController pushViewController:vc animated:YES];
        }else if([model.typeCode isEqualToString:@"2"]){
            NSURL *url = [NSURL URLWithString:model.url];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }else{
                NSURL *URL = [NSURL URLWithString:model.url];
                [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
                    //  回调
                }];
            }
        }else if([model.name isEqualToString:@"固定资产小程序"]){
          void (^gotoNMFAVC)(void) = ^void() {
            [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getAuthCode" arguments:nil result:^(id value) {
              if (!STR_IS_NIL(value)) {
                NSString *loadUrl = [NSString stringWithFormat:@"%@?authCode=%@", model.url, value];
                NMFAWebViewController *webViewController = [[NMFAWebViewController alloc] init];
                webViewController.isSupportShare = NO;
                [webViewController loadUrl:loadUrl];
                [self.navigationController pushViewController:webViewController animated:YES];
              }
            }];
          };
          if(!xy_isLogin){
              [[JCLoginManager sharedInstance] checkLogin:^{

              } viewController:self loginSuccessBlock:^{
                if (!STR_IS_NIL(model.url)) {
                  gotoNMFAVC();
                }
              }];
              return;
          }
          if (!STR_IS_NIL(model.url)) {
            gotoNMFAVC();
          }
        } else if([model.name isEqualToString:@"意见反馈"]){
          JCQAViewController *vc = [[JCQAViewController alloc] init];
          [self.navigationController pushViewController:vc animated:YES];
        }else{
            if(model.url.length > 0){
                if([model.url hasPrefix:@"niimbot://app"]){
                    [JCToNativeRouteHelp toNativePageWith:model.url fromType:JC_Home_Banner eventTitle:model.name];
                }else{
                    if(NETWORK_STATE_ERROR){
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                        return ;
                    }

                    XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:model.url];
                    c.title = model.name;
                    c.isSupportShare = [model isCouldShare];
                    [self.navigationController pushViewController:c animated:YES];
                }

            }
        }
    }
}

#pragma mark - WaterFlowLayoutDelegate
- (CGFloat)waterFlowLayout:(JCWaterfallCollectionLayout *)waterFlowLayout heightForRowAtIndex:(NSInteger)index itemWidth:(CGFloat)width
{

    return [self.heightArr[index] floatValue];
}

//获取瀑布流数组高度
-(NSArray *)heightArr{
    _heightArr = nil;
    NSMutableArray *arr = [NSMutableArray array];
    float cellHeight1 = 125;
    float cellWith = (SCREEN_WIDTH - 25 * 2 - 9) / 2;
    for (JCTemplateData *templateModel in self.myTemplateList) {
        if(templateModel.width == 0 || templateModel.height == 0){
            [arr addObject:@(cellHeight1)];
        }else{
            CGFloat aspect = templateModel.height/templateModel.width;
            float actuallCellWith = cellWith - 30;
            float cellHeight = actuallCellWith * aspect + (aspect >= 1 ? 0 : 80) + 34;
            if(cellHeight < 152){
                cellHeight = 152;
            }

            [arr addObject:@(cellHeight)];
        }

    }
    _heightArr = [arr copy];
    if(self.myTemplateList.count == 0){
        _heightArr = [NSMutableArray arrayWithObject:@300];
    }
    return _heightArr;
}

//决定cell的列数
- (NSInteger)cloumnCountInWaterFlowLayout:(JCWaterfallCollectionLayout *)waterFlowLayout
{
    return  self.myTemplateList.count == 0?1:2 ;
}

//决定cell 的列的距离
- (CGFloat)columMarginInWaterFlowLayout:(JCWaterfallCollectionLayout *)waterFlowLayout
{
    return 9;
}

//决定cell 的行的距离
- (CGFloat)rowMarginInWaterFlowLayout:(JCWaterfallCollectionLayout *)waterFlowLayout
{
    return 10 ;
}

//决定cell 的边缘距
- (UIEdgeInsets)edgeInsetInWaterFlowLayout:(JCWaterfallCollectionLayout *)waterFlowLayout
{
    return UIEdgeInsetsMake(10, 25, 10, 25);
}

#pragma mark collectionView代理方法
//返回section个数
- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView
{
    return 1;
}

//每个section的item个数
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section
{
    if(self.myTemplateList.count == 0){
        return 1;
    }else{
        return self.myTemplateList.count;
    }

}

- (UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath
{
    XYWeakSelf
    if(self.myTemplateList.count == 0){
        JCHomeNoDataCell *cell2 = [collectionView dequeueReusableCellWithReuseIdentifier:@"JCHomeNoDataCell" forIndexPath:indexPath];
        cell2.homeOperateBlock = ^(NSNumber *type) {
            [weakSelf homeOperateWithType:type.integerValue];
        };
        [cell2 refreshHomeDefaultView];
        return cell2;
    }
    JCTemplateData *cellModel = [self.myTemplateList safeObjectAtIndex:indexPath.row];
    JCCollectionViewCell *cell = (JCCollectionViewCell *)[collectionView dequeueReusableCellWithReuseIdentifier:@"JCCollectionViewCell" forIndexPath:indexPath];
    [cell setTemplateData:cellModel];
    [cell setPrintBlock:^{
      [JCTemplateFunctionHelper toPrint:cellModel];
      JC_TrackWithparms(@"click",@"003_005_440",(@{@"temp_id":cellModel.idStr,@"pos":@(indexPath.row + 1)}));
    }];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath{
    [collectionView deselectItemAtIndexPath:indexPath animated:YES];
    if(self.myTemplateList.count == 0){
        return;
    }
    JCTemplateData *cellModel = [self.myTemplateList safeObjectAtIndex:indexPath.row];
    NSString *templateId = cellModel.idStr;
    if([[XYCenter sharedInstance].oldNewDic.allKeys containsObject:cellModel.idStr]){
        templateId = [[XYCenter sharedInstance].oldNewDic xy_objectForKey:cellModel.idStr];
        cellModel.idStr = templateId;
    }
    [JCTemplateFunctionHelper toEdit:cellModel];
//    uploadLogInfoFlutter(@"toMyTemplateDetail", @"myTemplateDetail",^(id x,id y){},J_get_sls_Log_Token);
//    JCMyTemplateDetailViewController *vc = [[JCMyTemplateDetailViewController alloc] initWithType:@"2" template:cellModel];
//    [vc setRefreshTemplateBlock:^{
//        [self.mainViwe refreshContentView:self.heightArr.count];
//        [self.mainViwe.mainCollectionView reloadData];
//    }];
//    vc.templateDeleteBlock = ^(id x) {
//        [self.mainViwe refreshContentView:self.heightArr.count];
//        [self.mainViwe.mainCollectionView reloadData];
//    };
//    [self.navigationController pushViewController:vc animated:YES];
    JC_TrackWithparms(@"click",@"003_005_012",(@{@"temp_id":cellModel.idStr,@"pos":@(indexPath.row + 1)}));
}


-(void)scrollViewDidEndDragging:(UIScrollView *)scrollView willDecelerate:(BOOL)decelerate{
    if([scrollView isEqual:self.mainViwe.mainCollectionView]){
        if(!decelerate){
            [self.mainViwe refreshWithAnimation];
        }
    }
}

-(void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
    [self.mainViwe refreshWithAnimation];
}

- (void)applicationBackForegroundSwitch:(NSNotification *)noti {
    NSNumber *stateNumber = noti.object;
    if(stateNumber.integerValue == 1){
        [self getLocalSwiper];
    }else{
        //退到后台刷新banner显示频率
        [self refreshBannerList];
    }
}

-(void)loadNPSWeb{
    JCWebviewPreloadManager * manager = [JCWebviewPreloadManager defalutManager];
    JCNPSWebView * web = (JCNPSWebView *)[manager getWebView:NPSWebKey];
    if(web != nil){
        [web reload];
    }
}

-(void)loadNPSVIPWeb{
    JCWebviewPreloadManager * manager = [JCWebviewPreloadManager defalutManager];
    JCNPSWebView * web = (JCNPSWebView *)[manager getWebView:NPSWebVIPKey];
    if(web != nil){
        [web reload];
    }
}


- (void)loginChanged:(NSNotification *)notification{
    BOOL isLogin = xy_isLogin;
    self.myTemplateList = @[];
    if(isLogin){
      [self.mainViwe refreshContentView:self.heightArr.count];
      [self.mainViwe.mainCollectionView reloadData];
      NSLog(@"JCTemplateSaveManager --- createTemplate -------- JCTemplateImageManager  ---- 14");
      [self getTemplateList:YES];
    }else{
      [self.mainViwe resetOffset];
      self.myTemplateList = @[];
      [self.mainViwe.mainCollectionView reloadData];
      [self.mainViwe refreshContentView:self.heightArr.count];
    }
    
    [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {

    }];

    /// 刷新登录状态
    [[JCAppEventChannel shareInstance] eventData:@{
        @"loginStatusChanged": @{
            @"status": [XYCenter checkIsLogin] ? @1 : @0,
            @"token": m_userModel.token ?: @""
        }
    }];

    [self registrationJpushId];
}

- (void)adminTemplatechange:(NSNotification *)notification{
    NSLog(@"JCTemplateSaveManager --- createTemplate -------- JCTemplateImageManager  ---- 15");
    [self getTemplateList:NO];
}


- (void)changeLanguage:(NSNotification *)notification{
    // 通过Event Channel 传递 data 到 Flutter
    [[JCAppEventChannel shareInstance] eventData: @"languageChanged"];
//    if([XY_JC_LANGUAGE_REAL isEqualToString:@"ko"] || [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
      [[XYCenter sharedInstance] getNiimbotApplications];
//    }
    [self.mainViwe refreshLangChangedView];
//    [self getTemplateList:NO];
    [self.mainViwe.mainCollectionView.mj_header prepare];
    self.mainViwe.mainCollectionView.mj_header.mj_h = 35;
    self.mainViwe.mainCollectionView.mj_header.mj_x = 20;
    self.mainViwe.mainCollectionView.mj_header.mj_w = SCREEN_WIDTH-40;
    CGPoint offset = self.mainViwe.mainCollectionView.contentOffset;
    [self.mainViwe.mainCollectionView setContentOffset:CGPointMake(offset.x, offset.y-1) animated:NO];
    [self getLocalSwiper];
    [self getVIPTextBanner];
    if((NETWORK_STATE_ERROR && JC_CURRENT_PRINTER_RFID_STATUS == 1) || !JC_IS_CONNECTED_PRINTER) {
        return;
    };
    [self refreshRFIDViewWithType:2];

    [self registrationJpushId];
}

- (void)templatechange:(NSNotification *)notification{
    [self getTemplateList:NO];
    [[JCAppEventChannel shareInstance] eventData:@{
//        @"myTemplateRefresh": @{}
    }];
}

- (void)flutterChangeTemplate:(NSNotification *)notification{
    [self getTemplateList:NO];
}

- (void)refreshTemplate{
    [self.mainViwe refreshContentView:self.heightArr.count];
}

- (void)synchTemplateFinish:(NSNotification *)notification{
    NSDictionary *oldNewIdDic = notification.object;
    NSString *oldId = [oldNewIdDic objectForKey:@"oldXYId"];
    NSString *newId = [oldNewIdDic objectForKey:@"newXYId"];
    if(oldId.length != 0 && newId.length != 0){
      [self getTemplateList:YES];
    }
}

- (void)serverStateChangedRefreshView{
    if(m_currentServerState == ServerState_Normal){
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self.mainViwe refreshServiceState:m_currentServerState  serverStateDetail:m_currentServerStateDetail];
        });
    }else{
        [self.mainViwe refreshServiceState:m_currentServerState  serverStateDetail:m_currentServerStateDetail];
    }
}

static BOOL isNetWorkErrGetingRFID = NO;

- (void)didReciveSynchNotification{
    if(NETWORK_STATE_ERROR){
        return;
    }
    [[XYCenter sharedInstance] checkTemplateResource];
    if([XYCenter sharedInstance].isWifi){

    }
    [self getLocalSwiper];
    if([JCPrintManager sharedInstance].rfidTemplateData == nil && [JCBluetoothManager sharedInstance].rfidModel != nil){
        [self refreshRFIDViewWithType:3];
    }
    if(isNetWorkErrGetingRFID) return;
    isNetWorkErrGetingRFID = YES;
}

- (void)deviceWifiStateChange{
    if(jc_is_connected_wifi && !JC_IS_CONNECTED_PRINTER){
        NSDictionary *lastPrinterInfo = [JCBluetoothManager printerConnectStatusCache];
        if(lastPrinterInfo != nil){
            NSNumber *lastConnectType = lastPrinterInfo[@"connectType"];
            if(lastConnectType.integerValue == 2){
                [[JCBluetoothManager sharedInstance] startScan:@"" searchType:SEARCH_ALL];
            }
        }
    }
}

-(NSString*)deleteFloatAllZero:(NSString*)string
{
    NSArray * arrStr=[string componentsSeparatedByString:@"."];
    NSString *str=arrStr.firstObject;
    NSString *str1=arrStr.lastObject;
    while ([str1 hasSuffix:@"0"]) {
        str1=[str1 substringToIndex:(str1.length-1)];
    }
    return (str1.length>0)?[NSString stringWithFormat:@"%@.%@",str,str1]:str;
}

- (void)doScanWithType:(NSInteger)type{
    NSString *mediaType = AVMediaTypeVideo;//读取媒体类型
    AVAuthorizationStatus authStatus = [AVCaptureDevice authorizationStatusForMediaType:mediaType];//读取设备授权状态
    if(authStatus == AVAuthorizationStatusRestricted || authStatus == AVAuthorizationStatusDenied){
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{

        } sureBlock:^{
            NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
            if([[UIApplication sharedApplication] canOpenURL:url]) {
                NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
                [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
            }
        }];
        return;
    }else if(authStatus == AVAuthorizationStatusNotDetermined){
        [AVCaptureDevice requestAccessForMediaType:mediaType completionHandler:^(BOOL granted) {
            if(granted){
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self doSaoMaEventWithType:type];
                });
            }
        }];
    }else{
        [self doSaoMaEventWithType:type];
    }
}

- (void)doSaoMaEventWithType:(NSInteger)type
{
    XYWeakSelf
    QQLBXScanViewController *vc = [QQLBXScanViewController new];
    vc.scanCreateType = type;
    vc.libraryType = [Global sharedManager].libraryType;
    vc.scanCodeType = [Global sharedManager].scanCodeType;
    vc.style = [StyleDIY qqStyle];
    vc.scanMergeBlock = ^(LBXScanResult*strResult,NSNumber *scanType){
        NSLog(@"==============获取到扫码内容==============/n%@", strResult.strScanned);
        // 有些GBK编码的串需要兼容  -wy
        NSString *scanCode = [XYTool fixedScanCodeFromString:strResult.strScanned];
        if (scanCode.length > 0) {
            if(scanType.integerValue == 0){
                [weakSelf autoReconTemplateWithCode:strResult];
                [JCRecordTool recordWithAction:template_scan_scan_click withContent:@"" isClickEvent:YES parmeters:@{}];
            }else if(scanType.integerValue == 1){
                [weakSelf scanCreateTemplateWithCode:strResult];
                [JCRecordTool recordWithAction:template_scan_scan_click withContent:@"" isClickEvent:YES parmeters:@{}];
            }else if(scanType.integerValue == 2){
                if(NETWORK_STATE_ERROR){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                }else{
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [[JCLoginManager sharedInstance] checkGoodsLogin:^{

                        } viewController:weakSelf loginSuccessBlock:^{
                            [weakSelf goodsPrintWithCode:strResult];
                        }];
                    });
                }
                [JCRecordTool recordWithAction:template_scan_commodity_click withContent:@"" isClickEvent:YES parmeters:@{}];
            }
            DrawBoardInfo.isFromScanCode = YES;
        };
    };
    vc.isVideoZoom = YES;
    XYNavigationController *navVC = [[XYNavigationController alloc] initWithRootViewController:vc];
    [self presentViewController:navVC animated:YES completion:^{

    }];

}

- (void)autoReconTemplateWithCode:(LBXScanResult *)strResult{
    XYWeakSelf
    NSString *code = strResult.strScanned;
    if(self.progressHUD && !self.progressHUD.hidden){
        [self.progressHUD hideAnimated:NO];
        self.progressHUD = nil;
    }
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    self.progressHUD.label.text = @"";
    [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:@"" barCode:code success:^(JCTemplateData *requestModel) {
      if(requestModel != nil){
        [weakSelf.progressHUD hideAnimated:NO];
        [weakSelf getCloudTemplateSuccessParmWith:code requestModel:requestModel];
        [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
      }else{
          if(!xy_isLogin) [weakSelf.progressHUD hideAnimated:NO];
          [[JCLoginManager sharedInstance] checkGoodsLogin:^{

          } viewController:weakSelf loginSuccessBlock:^{
              [weakSelf goodsPrintWithCode:strResult];
          }];
      }
    } field:^(id x) {
      if(!xy_isLogin) [weakSelf.progressHUD hideAnimated:NO];
      [[JCLoginManager sharedInstance] checkGoodsLogin:^{

      } viewController:weakSelf loginSuccessBlock:^{
          [weakSelf goodsPrintWithCode:strResult];
      }];
    }];
//    if([self isHaveLocalLabelWith:code]){
//        [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
//        return ;
//    }
//    if(NETWORK_STATE_ERROR){
//        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//        return;
//    }
//    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
//    self.progressHUD.label.text = @"";
//    [self java_postWithValues:@{@"oneCode":UN_NIL(code)} ModelType:[JCTemplateData class] Path:J_scan_get_cloud_template hud:nil Success:^(__kindof YTKBaseRequest *request, JCTemplateData *requestModel) {
//        if(requestModel != nil){
//            [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:requestModel success:^(JCTemplateData *labelInfo) {
//                [weakSelf.progressHUD hideAnimated:NO];
//                [weakSelf getCloudTemplateSuccessParmWith:code requestModel:requestModel];
//                [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
//            } field:^(id x) {
//                [weakSelf.progressHUD hideAnimated:NO];
//                [weakSelf getCloudTemplateSuccessParmWith:code requestModel:requestModel];
//                [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
//            }];
//
//        }else{
//            if(!xy_isLogin) [weakSelf.progressHUD hideAnimated:NO];
//            [[JCLoginManager sharedInstance] checkGoodsLogin:^{
//
//            } viewController:weakSelf loginSuccessBlock:^{
//                [weakSelf goodsPrintWithCode:strResult];
//            }];
//        }
//    } failure:^(NSString *msg, id model) {
//        [weakSelf.progressHUD hideAnimated:NO];
//        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01283", @"识别条码失败")];
//    }];
}


- (void)goodsPrintWithCode:(LBXScanResult *)strResult{
    XYWeakSelf
    NSString *code = strResult.strScanned;
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(self.progressHUD && !self.progressHUD.hidden){
        [self.progressHUD hideAnimated:NO];
        self.progressHUD = nil;
    }
    self.progressHUD = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
    self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app01115", @"数据加载中");
    NSDictionary *requestDic = @{@"barcode":UN_NIL(code)};
    if(rfidTemplateModel != nil){
        NSString *labelId = STR_IS_NIL(rfidTemplateModel.idStr)?@"-1":rfidTemplateModel.idStr;
        NSString *labelBarcode = STR_IS_NIL(rfidTemplateModel.barcode)?@"-1":rfidTemplateModel.barcode;
        requestDic = @{@"barcode":UN_NIL(code),@"labelId":labelId,@"labelBarcode":labelBarcode};
    }
    __block NSString *requestUrl = @"";
    [JCGrayManager gotoGrayModulePage:GrayModuleDrawingBoard routeBlock:^(JCGrayModuleModel * _Nonnull grayModel) {
        switch (grayModel.branchType) {
            case GrayBranchA: {
                requestUrl = J_goods_new_scan;
            }
            break;
            case GrayBranchB: {
                requestUrl = J_goods_new_scan_v2;
            }
                break;
            default:
                break;
        }
    }];
    [weakSelf java_postWithValues:requestDic ModelType:[JCGoodResultModel class] Path:requestUrl hud:nil Success:^(__kindof YTKBaseRequest *request, JCGoodResultModel *requestModel) {
        if(requestModel.goods_info.barcode.length > 0){
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"1" searchResult:@"1" source:@"1"];
        }else{
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"1" searchResult:@"0" source:@"1"];
        }
        requestModel.goods_info.barcode = code;
        [weakSelf getGoodsResultSuccessParms:requestModel scanCode:code];
    } failure:^(NSString *msg, id model) {
        [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"1" searchResult:@"0" source:@"1"];
        [JCRecordTool recordWithAction:click_scan_print_callBack_faild];
        [self.progressHUD hideAnimated:NO];
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(msg, @"数据异常")];
    }];
}

- (void)getGoodsResultSuccessParms:(JCGoodResultModel *)requestModel scanCode:(NSString *)scanCode{
    JCTemplateData *rfidTemplateModel = [JCPrintManager sharedInstance].rfidTemplateData;
    if(!STR_IS_NIL(rfidTemplateModel.idStr)){
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:YES];
        }else{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [self jumpToTemplateEditVCWithRFIDModel:rfidTemplateModel goodInfo:requestModel.goods_info];
            });
            [self.progressHUD hideAnimated:NO];
        }
    }else{
        if(!STR_IS_NIL(requestModel.template_info.idStr)){
            [self getTemplateDetailWithGoodsResult:requestModel hasRFID:NO];
        }else{
            [self.progressHUD hideAnimated:NO];
        }
    }
}


- (void)getTemplateDetailWithGoodsResult:(JCGoodResultModel *)goodResult hasRFID:(BOOL)hasRFID{
  goodResult.template_info.labelId = goodResult.template_info.profile.extrain.labelId;
  [JCTemplateImageManager downLoadImagesForData:goodResult.template_info options:DownAll complete:^(JCTemplateData *templateData) {
    [self.progressHUD hideAnimated:NO];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [self jumpToTemplateEditVCWith:templateData goodInfo:goodResult.goods_info hasRFID:hasRFID];
    });
  }];
}

- (void)jumpToTemplateEditVCWith:(JCTemplateData *)requestModel goodInfo:(JCGoodDetailInfo *)goodDetailInfo hasRFID:(BOOL)hasRFID{
    TemplateSource source = TemplateSource_Mine;//
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager templateDataWith:requestModel goodInfo:goodDetailInfo];
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    if(STR_IS_NIL(requestModel.profile.extrain.userId)){
        model.name = STR_IS_NIL(goodDetailInfo.name)?XY_LANGUAGE_TITLE_NAMED(@"app01052", @"品名"):goodDetailInfo.name;
        source = TemplateSource_New;
    }
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [JCTemplateFunctionHelper showTemplteNeedUpgrade:model];
        return;
    }
    NSMutableArray *dataSource = model.dataSource.mutableCopy;
    NSMutableDictionary *dataSourceInfo = [NSMutableDictionary dictionaryWithDictionary:[dataSource safeObjectAtIndex:0]];
    [dataSourceInfo setObject:@"commodity" forKey:@"type"];
    NSMutableDictionary *commodityInfo = [goodDetailInfo getBaseGoodsInfoDic];
    [commodityInfo setValue:UN_NIL(goodDetailInfo.goodId) forKey:@"id"];
    [dataSourceInfo setObject:@{@"commodity":commodityInfo} forKey:@"params"];
    dataSource = [NSMutableArray arrayWithArray:@[dataSourceInfo]];
    model.dataSource = dataSource;
    model.dataSourceBindInfo = [model getDefaultDataBindInfo];
    [JCFlutter2NativeHandler toFlutterCanvasWith:model type:source];
}

- (void)jumpToTemplateEditVCWithRFIDModel:(JCTemplateData *)m goodInfo:(JCGoodDetailInfo *)goodDetailInfo{
    JCTemplateData *model = [JCTMDataBindGoodsInfoManager createGoodsTemplateWith:m goodInfo:goodDetailInfo];
    model.profile.extrain.commodityCategoryId = goodDetailInfo.categoryId;
    model.profile.extrain.industryId = goodDetailInfo.industryId;
    model.profile.extrain.goodsCode = goodDetailInfo.barcode;
    model.goodsList = (NSArray<JCGoodDetailInfo> *)@[goodDetailInfo];
    if([JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:model.templateVersion] == false){
        [JCTemplateFunctionHelper showTemplteNeedUpgrade:model];
        return;
    }

    NSMutableDictionary *dataSourceInfo = [NSMutableDictionary dictionary];
    [dataSourceInfo setObject:@"commodity" forKey:@"type"];
    NSMutableDictionary *commodityInfo = [goodDetailInfo getBaseGoodsInfoDic];
    [commodityInfo setValue:UN_NIL(goodDetailInfo.goodId) forKey:@"id"];
    [dataSourceInfo setObject:@{@"commodity":commodityInfo} forKey:@"params"];
    model.dataSource = @[dataSourceInfo];
    model.dataSourceBindInfo = [model getDefaultDataBindInfo];
    model.dataSourceModifies = [model getDefaultDataSourceModifies];
    [JCFlutter2NativeHandler toFlutterCanvasWith:model type:TemplateSource_New];

}


- (void)scanCreateTemplateWithCode:(LBXScanResult *)strResult{
    NSString *code = strResult.strScanned;
    [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:@"" barCode:code success:^(JCTemplateData *requestModel) {
        if(requestModel != nil){
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
        }else{
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"0" source:@"1"];
        }
        [self getCloudTemplateSuccessParmWith:code requestModel:requestModel];
    } field:^(id x) {
      if(NETWORK_STATE_ERROR){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return ;
      }
      [self getCloudTemplateDetailByScanCreate:strResult];
    }];
//    if([self isHaveLocalLabelWith:code]){
//        return ;
//    }
//    if(NETWORK_STATE_ERROR){
//        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
//        return ;
//    }
//    [self getCloudTemplateDetailByScanCreate:strResult];

}

//- (BOOL)isHaveLocalLabelWith:(NSString *)code{
//    BOOL isHaveLocalLabel = NO;
//    NSString *where = [NSString stringWithFormat:@"where (barcode = '%@' or amazonCodeBeijing = '%@' or amazonCodeWuhan = '%@' or sparedCode = '%@' or virtualBarCode = '%@' or barcodeCategoryMap like '%%*%@*%%') and templateType = '1' and (templateClass = '0' or templateClass = '')",code,code,code,code,code,code];
//    NSArray *dataArr = [JCCloudTemplateDBManager db_queryDatasWhere:where];
//    if(dataArr.count > 0){
//        JCTemplateData *templateModel = dataArr[0];
//        BOOL is_ableEdit_Template = (templateModel.localType == JCLocalType_Sync);
//        if(is_ableEdit_Template){
//            if(!(NETWORK_STATE_ERROR)){
//                [self java_postWithValues:@{@"oneCode":UN_NIL(code)} ModelType:[JCTemplateData class] Path:J_scan_get_cloud_template hud:nil Success:^(__kindof YTKBaseRequest *request, JCTemplateData *requestModel) {
//                    requestModel.localType = JCLocalType_Sync;
//                    [JCTemplateDBManager db_updateTemplateData:requestModel];
//                } failure:^(NSString *msg, id model) {
//                }];
//            }
//            MBProgressHUD *progress = [MBProgressHUD showHUDAddedTo:hudWindow animated:NO contentColor:hudContentColor backColor:hudBackColor];
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                [progress hideAnimated:YES];
//                [FlutterBoostUtility gotoFlutterPage:@"labelDetailPage" arguments:@{@"shopSource":@"print_020",@"jsonData": templateModel.toJSONString} onPageFinished:^(NSDictionary *dic) {
//
//                }];
//            });
//            isHaveLocalLabel = YES;
//        }
//    }
//    return isHaveLocalLabel;
//}

//扫描条码获取标签
- (void)getCloudTemplateSuccessParmWith:(NSString *)scanCode requestModel:(JCTemplateData *)requestModel{
    if (requestModel && [requestModel isKindOfClass:[JCTemplateData class]]) {
      dispatch_async(dispatch_get_main_queue(), ^{
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          [FlutterBoostUtility gotoFlutterPage:@"labelDetailPage" arguments:@{@"shopSource":@"print_020",@"jsonData": requestModel.toJSONString,@"isFromScan":@(true)} onPageFinished:^(NSDictionary *dic) {

          }];
        });

      });

    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00321", @"请使用精臣标签。")];
        });
    }
}

- (void)getCloudTemplateDetailByScanCreate:(LBXScanResult *)strResult{
    NSString *scanCode = strResult.strScanned;
    [self java_postWithValues:@{@"oneCode":UN_NIL(scanCode)} ModelType:[JCTemplateData class] Path:J_scan_get_cloud_template hud:@"" Success:^(__kindof YTKBaseRequest *request, JCTemplateData *requestModel) {
        if(requestModel != nil){
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"1" source:@"1"];
        }else{
            [[XYCenter sharedInstance] scanTrackWithSearchResult:strResult searchtype:@"2" searchResult:@"0" source:@"1"];
        }
        [self getCloudTemplateSuccessParmWith:scanCode requestModel:requestModel];
    } failure:^(NSString *msg, id model) {
        if(!(NETWORK_STATE_ERROR)){
            dispatch_async(dispatch_get_main_queue(), ^{
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00321", @"请使用精臣标签。")];
            });
        }
    }];
}

//设置状态栏颜色
- (void)setStatusBarBackgroundColor:(UIColor *)color {
    UIView *statusBar = [[[UIApplication sharedApplication] valueForKey:@"statusBarWindow"] valueForKey:@"statusBar"];
    if ([statusBar respondsToSelector:@selector(setBackgroundColor:)]) {
        statusBar.backgroundColor = color;
    }
}

//获取打印机信息
- (void)printerStatusNotification:(NSNotification *)noti {
    NSString *state = noti.object;
    self.isBlueOpen = ([state isEqualToString:@"1"] ? YES:NO);
    if(self.isBlueOpen){
        NSString *printerName = JC_CURRENT_CONNECTED_PRINTER;
        NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:YES];
        NSString *wherString = [NSString stringWithFormat:@"where name = '%@'",printType];
        NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:wherString];
        [self.mainViwe resetOffset];
        JCRFIDDeviceModel *model = [JCBluetoothManager sharedInstance].rfidModel;
        self.rfidDeviceModel = model;
        if(JC_CURRENT_CONNECTED_PRINTER_COVER_STATE){
            [self refreshCoverStateChanged:YES];
        }else if(model.error || model.rfid == nil) {
            if(JC_RFID_SUPPORT_GET_TEMPLATE_ONLY || JC_CURRENT_PRINTER_RFID_STATUS == 0){
                [self hiddenRFIDView];
            }else{
                [self refreshRFIDViewWithType:4];
            }
        }else{
          [self refreshRFIDViewWithType:4];
        }

    }else{
        [self.mainViwe resetOffset];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self refreshRFIDViewWithType:4];
        });
        if((state.integerValue == 4 || state.integerValue == 5) && [JCBluetoothManager sharedInstance].deviceType != JCBluetoothETag){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00743",@"打印机连接已断开")];
        }
        if(state.integerValue == 6 && [JCBluetoothManager sharedInstance].deviceType == JCBluetoothNormal){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001085",@"设备已断开")];
        }
    }
    [self.mainViwe refreshRightButton];

    if([JCBluetoothManager sharedInstance].deviceType != JCBluetoothETag){
        // 通过Event Channel传递data到Flutter
        JCPrinterModel *currentModel = JC_CURRENT_CONNECTED_PRINTER_MODEL;
        NSDictionary *printerInfoDic = @{};
        if(currentModel != nil){
            printerInfoDic = [currentModel printerModelToDic];
        }
        NSString *printInfoStr = printerInfoDic.count > 0?printerInfoDic.xy_toJsonString:@"";
        [[JCAppEventChannel shareInstance] eventData:@{
            @"action":@"printerConnectState",
            @"printerConnectState": [[JCBluetoothManager sharedInstance] getPrinterConnectState],
            @"printer":printInfoStr
        }];
    }else if(!self.isBlueOpen){
        [[JCAppEventChannel shareInstance] eventData:@{
            @"action":@"printerConnectState",
            @"printerConnectState": [[JCBluetoothManager sharedInstance] getPrinterConnectState],
            @"printer":@""
        }];
    }
}

-(void)rfidPrinterCoverOpenNotification:(NSNotification *)noti{
    NSString *statusString = noti.object;
    if([statusString isEqualToString:@"0"]){
      [self refreshCoverStateChanged:YES];
    }else{
      [self refreshCoverStateChanged:NO];
    }
}

- (void)refreshCoverStateChanged:(BOOL)isOpen{
  if(JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
      if(JC_IS_CONNECTED_PRINTER){
        if(isOpen){
          [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:4 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1 || self.mainViwe.homeNewRFIDView.showType == -1];
        }else if(JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
          [self hiddenRFIDView];
        }else if(JC_CURRENT_PRINTER_RFID_STATUS == 1){
          [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:NO];
        }else{
          [self hiddenRFIDView];
        }
      }
  }else if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
      //            if([JCAPI printerTypeInfo]/256 == 4){//新SDK联调注释
      if(JCBlUETOOTH_MANAGER.connectedNetyModel.code.integerValue/256 == 4){
          if(JC_IS_CONNECTED_PRINTER){
            if(isOpen){
              [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:4 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1 || self.mainViwe.homeNewRFIDView.showType == -1];
            }else{
              [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:NO];
            }
          }
      }
  }else if(JC_CURRENT_PRINTER_RFID_STATUS == 3){
      if(JC_IS_CONNECTED_PRINTER){
        if(isOpen){
          [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:4 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1 || self.mainViwe.homeNewRFIDView.showType == -1];
        }else{
          [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:NO];
        }
      }
  }
}

-(void)rfidPrinterConnectNotification:(NSNotification *)noti{
    if (self.mainViwe.mainCollectionView.mj_header.isRefreshing) {
        [self.mainViwe.mainCollectionView.mj_header endRefreshing];
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self refreshRFIDViewWithType:1];
        });
    }else{
        [self refreshRFIDViewWithType:1];
    }

}

//refreshType    0  启动刷新   1 RFID识别刷新  2 切换语言刷新 3 切换网络刷新 4 打印机连接刷新
- (void)refreshRFIDViewWithType:(NSInteger)refreshType{
    JCRFIDDeviceModel *model = [JCBluetoothManager sharedInstance].rfidModel;
    self.rfidDeviceModel = model;
    if(!JC_IS_CONNECTED_PRINTER) {
//        if((JC_RFID_SUPPORT_GET_TEMPLATE_ONLY || JC_CURRENT_PRINTER_RFID_STATUS == 0)){
//            [self hiddenRFIDView];
//        }
      [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:3 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
        return;
    };
    if(JC_CURRENT_CONNECTED_PRINTER_COVER_STATE){
        return;
    }//
    else if(model.error || model.rfid == nil) {
        if((JC_RFID_SUPPORT_GET_TEMPLATE_ONLY || JC_CURRENT_PRINTER_RFID_STATUS == 0)){
            [self hiddenRFIDView];
        }else{
            if(JC_IS_CONNECTED_PRINTER){
                if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
                    [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:6 surplusLevel:0 isAnimal:NO];
                }else{
                    [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:NO];
                }
                if(refreshType == 1){
                    JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@2}));
                }
            }
        }
        return;
    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
        float surplusFloat = self.rfidDeviceModel.usedNumber.floatValue/self.rfidDeviceModel.maxNumber.floatValue;
        NSString *p1TipString = [XY_LANGUAGE_TITLE_NAMED(@"app01222", @"碳带余量") stringByReplacingOccurrencesOfString:@"\\n" withString:@" \r\n"];
        NSString *surplusDesc = [[self surplusLevelWithSurplusFloat:surplusFloat] safeObjectAtIndex:1];
        p1TipString = [NSString stringWithFormat:@"%@ %@",p1TipString,surplusDesc];
        NSInteger surplusLevel = ((NSString *)[[self surplusLevelWithSurplusFloat:surplusFloat] safeObjectAtIndex:0]).integerValue;
        if(JC_IS_CONNECTED_PRINTER){
            [self.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:5 surplusLevel:surplusLevel isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
            JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@1}));
            [self trackConsumablesIsLegalWith:refreshType];
        }
    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
        [self getTemplateDetailWithOne_code:model type:refreshType];
    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 3 || JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
        [self getTemplateDetailWithOne_code:model type:refreshType];
    }else if (JC_CURRENT_PRINTER_RFID_STATUS == 0 && !JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
        [self hiddenRFIDView];
    }
}

- (void)trackConsumablesIsLegalWith:(NSInteger)type{
    NSString *rfidCode = @"";
    if(JC_CURRENT_PRINTER_RFID_STATUS == 3){
        rfidCode = [NSString stringWithFormat:@"%@,%@",UN_NIL([JCBluetoothManager sharedInstance].labelRFIDModel.rfid),UN_NIL([JCBluetoothManager sharedInstance].carbonRFIDModel.rfid)];
    }else{
        rfidCode = [JCBluetoothManager sharedInstance].rfidModel.rfid;
    }
    if(type == 1){
        if(!STR_IS_NIL([[JCBluetoothManager sharedInstance] checkConsumablesIsLegal])){
            JC_TrackWithparms(@"show",@"003_200",(@{@"lang":UN_NIL(XY_JC_LANGUAGE_REAL),@"sn":UN_NIL(rfidCode)}));
        }
    }
    if(type == 2 && ![self.originalLang isEqualToString:XY_JC_LANGUAGE_REAL]){
        self.originalLang = XY_JC_LANGUAGE_REAL;
        if(!STR_IS_NIL([[JCBluetoothManager sharedInstance] checkConsumablesIsLegal])){
            JC_TrackWithparms(@"show",@"003_200",(@{@"lang":UN_NIL(XY_JC_LANGUAGE_REAL),@"sn":UN_NIL(rfidCode)}));
        }
    }
}

-(void)hiddenRFIDView{
    [self.mainViwe refreshNewRFIDViewState:NO rfidViewData:nil showType:-1 surplusLevel:0 isAnimal:YES];
}

- (void)getTemplateDetailWithOne_code:(JCRFIDDeviceModel *)model type:(NSInteger)refreshType{
    XYWeakSelf
    NSString *barCode = model.rfidCode;
    [JCPrintManager sharedInstance].rfidTemplateData = nil;
    JCTemplateData *templateModel = nil;
    if((NETWORK_STATE_ERROR) || m_currentServerState == ServerState_Stop){
      //网络异常
      [JCLabelInfoMangerHelper getLocalLabelInfoWith:@"" barCode:barCode result:^(JCTemplateData *templateModel) {
        if(templateModel != nil){
          BOOL is_ableEdit_Template = [templateModel checkTemplateDetailByBackImage:YES containFont:NO];
          if(is_ableEdit_Template){
             [JCPrintManager sharedInstance].rfidTemplateData = templateModel;
              self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00873", @"已更新耗材");
              [self.progressHUD hideAnimated:YES afterDelay:1];
              weakSelf.mainViwe.rfidViewDataModel = templateModel;
              dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                  [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:templateModel showType:1 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType != 1];
                  [weakSelf getLabelBuyStatusRequest];
                  [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"1"];
                  [weakSelf trackConsumablesIsLegalWith:refreshType];
              });
              return;
          }else{
              [self hiddenRFIDView];
              [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
          }
        }else{
          [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
          [self hiddenRFIDView];
        }
      }];
//      [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:@"" barCode:barCode success:^(JCTemplateData *templateModel) {
//        BOOL is_ableEdit_Template = [templateModel checkTemplateDetailByBackImage:YES containFont:NO];
//        if(is_ableEdit_Template){
//            self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00873", @"已更新耗材");
//            [self.progressHUD hideAnimated:YES afterDelay:1];
//            weakSelf.mainViwe.rfidViewDataModel = templateModel;
//            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//                [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:templateModel showType:1 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType != 1];
//                [weakSelf getLabelBuyStatusRequest];
//                [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"1"];
//                [weakSelf trackConsumablesIsLegalWith:refreshType];
//            });
//            return;
//        }else{
//            [self hiddenRFIDView];
//            [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
//        }
//      } field:^(id x) {
//        [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
//        [self hiddenRFIDView];
//      }];
      return;
    }
    //网络正常从网络实时请求RFID标签信息
    if(self.progressHUD && !self.progressHUD.hidden){
        [self.progressHUD hideAnimated:NO];
        self.progressHUD = nil;
    }
    self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00872", @"检测中");
    [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:@"" barCode:barCode success:^(JCTemplateData *templateModel) {
      self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00873", @"已更新耗材");
      [self.progressHUD hideAnimated:YES afterDelay:1];
      if(JC_IS_CONNECTED_PRINTER){
          [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:templateModel showType:1 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType != 1];
          JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@1}));
          [JCPrintManager sharedInstance].rfidTemplateData = templateModel;
          [weakSelf getLabelBuyStatusRequest];
          [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"1"];
          [weakSelf trackConsumablesIsLegalWith:refreshType];
      }
    } field:^(id x) {
      if(JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
          [weakSelf hiddenRFIDView];
      }else{
          weakSelf.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00851", @"耗材信息获取失败");
          if(JC_IS_CONNECTED_PRINTER){
              if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
                  [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:6 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
              }else{
                  [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
              }
              JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@2}));
          }
      }
      [weakSelf.progressHUD hideAnimated:YES afterDelay:1];
      [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
      return ;
    }];
//    [self java_postWithValues:@{@"oneCode":UN_NIL(barCode)} ModelType:[JCTemplateData class] Path:J_scan_get_cloud_template hud:nil Success:^(__kindof YTKBaseRequest *request, JCTemplateData *requestModel) {
//        if(requestModel == nil){
//            if(JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
//                [weakSelf hiddenRFIDView];
//            }else{
//                weakSelf.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00851", @"耗材信息获取失败");
//                if(JC_IS_CONNECTED_PRINTER){
//                    if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
//                        [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:6 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
//                    }else{
//                        [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
//                    }
//                    JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@2}));
//                }
//            }
//            [weakSelf.progressHUD hideAnimated:YES afterDelay:1];
//            [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
//            return ;
//        }
//        JCTemplateData *tagDetailModel = nil;
//        NSArray *dbDataArr = [JCCloudTemplateDBManager db_queryDatasWhere:where];
//        if(dbDataArr.count > 0){
//            tagDetailModel = dbDataArr.firstObject;
//        }
//        if(tagDetailModel != nil){
//            [JCTemplateImageManager downLoadImagesForData:requestModel options:DownAll complete:^{
//                self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00873", @"已更新耗材");
//                [self.progressHUD hideAnimated:YES afterDelay:1];
//                if(JC_IS_CONNECTED_PRINTER){
//                    [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:requestModel showType:1 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType != 1];
//                    [JCPrintManager sharedInstance].rfidTemplateData = requestModel;
//                    [weakSelf getLabelBuyStatusRequest];
//                    [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"1"];
//                    [weakSelf trackConsumablesIsLegalWith:refreshType];
//                    JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@1}));
//                }
//            }];
//            [JCTemplateDBManager db_updateData:requestModel where:where];
//        }else{
//            [JCTemplateDBManager db_insertTemplateData:requestModel];
//            [JCTemplateImageManager downLoadImagesForData:requestModel options:DownAll complete:^{
//                self.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00873", @"已更新耗材");
//                [self.progressHUD hideAnimated:YES afterDelay:1];
//                if(JC_IS_CONNECTED_PRINTER){
//                    [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:requestModel showType:1 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType != 1];
//                    JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@1}));
//                    [JCPrintManager sharedInstance].rfidTemplateData = requestModel;
//                    [weakSelf getLabelBuyStatusRequest];
//                    [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"1"];
//                    [weakSelf trackConsumablesIsLegalWith:refreshType];
//                }
//            }];
//        }
//    } failure:^(NSString *msg, id model) {
//        if(JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
//            [weakSelf.progressHUD hideAnimated:YES afterDelay:1];
//            [weakSelf hiddenRFIDView];
//        }else{
//            weakSelf.progressHUD.label.text = XY_LANGUAGE_TITLE_NAMED(@"app00851", @"耗材信息获取失败");
//            [weakSelf.progressHUD hideAnimated:YES afterDelay:1];
//            if(JC_IS_CONNECTED_PRINTER){
//                if(JC_CURRENT_PRINTER_RFID_STATUS == 2){
//                    [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:6 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
//                }else{
//                    [weakSelf.mainViwe refreshNewRFIDViewState:YES rfidViewData:nil showType:2 surplusLevel:0 isAnimal:self.mainViwe.homeNewRFIDView.showType == 1];
//                }
//                JC_TrackWithparms(@"view",@"003_146",(@{@"machine_id":UN_NIL(JC_CURRENT_CONNECTED_PRINTER),@"type":@2}));
//            }
//        }
//        [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
//    }];
}
- (NSArray *)surplusLevelWithSurplusFloat:(float)surplusFloat{
    if(surplusFloat <= 0.25){
        return @[@"4",XY_LANGUAGE_TITLE_NAMED(@"app01223", @"满")];
    }else if(surplusFloat > 0.25 && surplusFloat <= 0.5){
        return @[@"3",XY_LANGUAGE_TITLE_NAMED(@"app01224", @"高")];
    }else if(surplusFloat > 0.5 && surplusFloat <= 0.75){
        return @[@"2",XY_LANGUAGE_TITLE_NAMED(@"app01225", @"中")];
    }else if(surplusFloat > 0.75 && surplusFloat <= 1){
        return @[@"1",XY_LANGUAGE_TITLE_NAMED(@"app01226", @"低")];
    }else{
        return @[@"1",XY_LANGUAGE_TITLE_NAMED(@"app01226", @"低")];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)registrationJpushId {
    XYWeakSelf
    [JPUSHService registrationIDCompletionHandler:^(int resCode, NSString *registrationID) {
        if(xy_isLogin) {
            [weakSelf java_postWithValues:@{@"deviceId":registrationID} ModelType:nil Path:J_mobile_v30_band_device hud:nil Success:^(__kindof YTKBaseRequest *request, id model) {
                [[NSUserDefaults standardUserDefaults] setObject:[NSString stringWithFormat:@"%@-YES", registrationID] forKey:@"jpushRegistrationID"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            } failure:^(NSString *msg, id model) {
                [[NSUserDefaults standardUserDefaults] setObject:[NSString stringWithFormat:@"%@-NO", registrationID] forKey:@"jpushRegistrationID"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            }];
        }
    }];


}
// MARK: 获取打印机标签纸状态
/// 注册标签纸的通知，发送给Flutter端，用于标签纸的状态更新
- (void)registerRfidLabelStatus {
    WEAK_SELF()
    [[NSNotificationCenter defaultCenter] addObserverForName:RfidGotLabelInfoNotification object: nil queue: nil usingBlock:^(NSNotification * _Nonnull note) {
        NSString *resultValue = note.object;
        if(resultValue.integerValue == 0){
          [JCPrintManager sharedInstance].rfidTemplateData = nil;
        }
        // 获取标签纸信息
        JCTemplateData *data = [JCPrintManager sharedInstance].rfidTemplateData;
        // 通过Event Channel传递data到Flutter
        NSDictionary *labelInfoDic =  data == nil ? @{} : data.toDictionary;
        [[JCAppEventChannel shareInstance] eventData: @{
            @"action": @"setFlutterlabelData",
            @"labelData": labelInfoDic
        }];

        // 发送currentPrintColor事件，刷新画板标签纸颜色问题
        NSString *printColorStr = @"";
        if([JCBluetoothManager sharedInstance].printColor != nil && JC_IS_CONNECTED_PRINTER){
            printColorStr = [JCBluetoothManager sharedInstance].colorRGBStr;
        }else{
            printColorStr = @"";
        }
        NSInteger consumablesType = 0;
        if(JC_IS_CONNECTED_PRINTER){
            if(JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_CURRENT_PRINTER_RFID_STATUS == 0){
                consumablesType = 1;
            }else{
                consumablesType = 2;
            }
        }
        NSArray<NSNumber*> *supportColorsType = [JCBluetoothManager sharedInstance].connectedNetyModel.colorModeSupport;
        BOOL supportSixteenGrayPrint = [supportColorsType containsObject:@3] && [[JCBluetoothManager sharedInstance] isGray16Paper];
        [[JCAppEventChannel shareInstance] eventData:@{
            @"action":@"currentPrintColor",
            @"consumablesType":@(consumablesType),
            @"supportSixteenGrayPrint":@(supportSixteenGrayPrint),
            @"currentPrintColor":printColorStr}];
    }];
}

//检查是否展示vipNps评分
- (void)checkVipNpsGrade{
    BOOL shouldShowVIPNps = [self shouldShowVIPNpsGrade];
    if(shouldShowVIPNps){
        [FlutterBoostUtility gotoTransparentFlutterPage:@"vipNPSGrade"
                                              arguments:@{
            @"token": m_userModel.token,
            @"isPresent": @YES,
            @"isAnimated": @NO,
        }
                                         onPageFinished:^(NSDictionary *_) {
            // 页面结束回传数据
        }];
        [[NSUserDefaults standardUserDefaults] setValue:@YES forKey:@"vip_nps_state"];
        [[NSUserDefaults standardUserDefaults] synchronize];
    }
}

- (BOOL)shouldShowVIPNpsGrade{
    BOOL isLanguageSupport = NO;
    if([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [XY_JC_LANGUAGE_REAL isEqualToString:@"en"] ||
       [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn-t"] || [XY_JC_LANGUAGE_REAL isEqualToString:@"ko"]){
        isLanguageSupport = YES;
    }
    NSNumber *hasShowVipNps = [[NSUserDefaults standardUserDefaults] valueForKey:@"vip_nps_state"];
    if(m_user_vip && !hasShowVipNps.boolValue && isLanguageSupport){
        return YES;
    }else{
        return NO;
    }
}
@end
