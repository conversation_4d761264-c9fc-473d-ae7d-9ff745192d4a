import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:netal_plugin/netal_image_result.dart';
import '/src/model/element/json_element.dart';
import '/src/model/element/table_element.dart';
import '/src/widgets/elements/interactive_element.dart';

class CanvasElement {
  CanvasElement(this.data);

  Rect get rect {
    Offset offset = this.offset;
    Size size = this.size;

    Rect rect = Rect.fromLTWH(offset.dx, offset.dy, size.width, size.height);
    if (this.rotate == 90 || this.rotate == 270) {
      return Rect.fromLTWH(
          rect.center.dx - rect.height / 2.0, rect.center.dy - rect.width / 2.0, rect.height, rect.width);
    }
    return rect;
  }

  Offset get offset => Offset(data.x.mm2dp().toDouble(), data.y.mm2dp().toDouble());

  set offset(Offset value) {
    data.x = value.dx.dp2mm();
    data.y = value.dy.dp2mm();
  }

  Size get size => Size(data.width.mm2dp().toDouble(), data.height.mm2dp().toDouble());

  set size(Size value) {
    /// 校正表格 cell 的宽高
    if (elementType == ElementItemType.table) {
      TableElement element = data as TableElement;
      double rowHeightSum = 0.0;
      element.rowHeight.forEach((element) {
        rowHeightSum += element;
      });

      double columnWidthSum = 0.0;
      element.columnWidth.forEach((element) {
        columnWidthSum += element;
      });

      double heightGap = value.height.dp2mm().toDouble() - data.height;
      double widthGap = value.width.dp2mm().toDouble() - data.width;

      for (int index = 0; index < element.rowHeight.length; index++) {
        element.rowHeight[index] =
            (element.rowHeight[index] + heightGap * element.rowHeight[index] / rowHeightSum).digits(6).toDouble();
      }
      for (int index = 0; index < element.columnWidth.length; index++) {
        element.columnWidth[index] =
            (element.columnWidth[index] + widthGap * element.columnWidth[index] / columnWidthSum).digits(6).toDouble();
      }
    }

    data.width = value.width.dp2mm();
    data.height = value.height.dp2mm();

    resetImageCache();
  }

  int get rotate => data.rotate;

  set rotate(int value) {
    data.rotate = value;
  }

  /// 是否需要切换宽高
  bool get needSwapSize => data.rotate % 180 != 0;

  /// 缓存当前图片
  NetalImageResult? image;

  NetalImageResult? get imageCache => image;

  set imageCache(NetalImageResult? imageCache) {
    image = imageCache;
  }

  resetImageCache() {
    image = null;

    if (data.type == ElementItemType.table) {
      (data as TableElement).resetCellsImageCache();
    }
  }

  /// 标记当前元素是否需要展示为contain
  bool isShowBoxFitContain = false;

  JsonElement data;

  String get elementType => data.type;

  String get elementId => data.id;

  /// 用于标记镜像状态(开启/关闭)是否需要刷新
  bool mirrorNeedRefresh = false;

  /// 标记元素当前正在响应手势
  bool isDragging = false;

  /// 标记当前拖拽类型
  DragType? currentDragType;

  center(Offset newCenter) {
    data.x = newCenter.dx.dp2mm() - data.width / 2;
    data.y = newCenter.dy.dp2mm() - data.height / 2;
  }

  ///位移
  translate(double x, double y) {
    if (x > 0) {
      if (needSwapSize) {
        ///中心点 - 宽度/2 = 左边距
        double left = (x + data.height / 2) - data.width / 2;
        data.x = left;
      } else {
        data.x = x;
      }
    }
    if (y > 0) {
      if (needSwapSize) {
        ///中心点 - 高度/2 = 上边距
        double top = (y + data.width / 2) - data.height / 2;
        data.y = top;
      } else {
        data.y = y;
      }
    }
  }
}

extension copywith on NetalImageResult {
  NetalImageResult copyWith({
    Uint8List? pixels,
    int? size,
    int? width,
    int? height,
    num? x,
    num? y,
    int? boxStyle,
    num? fontSize,
    num? fontSizeThreshold,
    int? lineNumber,
  }) {
    return NetalImageResult(
      pixels: pixels ?? this.pixels,
      size: size ?? this.size,
      width: width ?? this.width,
      height: height ?? this.height,
      x: x ?? this.x,
      y: y ?? this.y,
      boxStyle: boxStyle ?? this.boxStyle,
      fontSize: fontSize ?? this.fontSize,
      fontSizeThreshold: fontSizeThreshold ?? this.fontSizeThreshold,
      lineNumber: lineNumber ?? this.lineNumber,
      errorCode: this.errorCode,
      errorMsg: this.errorMsg,
    );
  }
}
