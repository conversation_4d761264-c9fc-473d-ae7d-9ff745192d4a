package com.niimbot.baselibrary

import com.blankj.utilcode.util.LogUtils
import android.widget.LinearLayout
import android.widget.TextView
import com.blankj.utilcode.util.ActivityUtils
import com.google.gson.Gson
import com.niimbot.appframework_library.R
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.dialog.CustomDialog
import com.niimbot.appframework_library.dialog.SuperDialog
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.fastjson.JSONObject
import com.niimbot.utiliylibray.util.any2Json
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.TextHookUtil
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.coroutines.resume

/**
 * 调用flutter端方法管理类
 */
object FlutterMethodInvokeManager {
    lateinit var methodChannel: MethodChannel
    fun initData(channel: MethodChannel) {
        methodChannel = channel
    }

//    fun updateTemplateLocalThumb(templateId: String,localThumbPath: String ){
//        GlobalScope.launch(Dispatchers.Main) {
//            methodChannel.invokeMethod("updateTemplateLocalThumb", hashMapOf(Pair("templateId", templateId),
//                Pair("localThumbPath", localThumbPath)), object : MethodChannel.Result {
//                override fun success(result: Any?) {
//                }
//
//                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                }
//
//                override fun notImplemented() {
//                }
//            })
//        }
//    }

    fun deleteIndustryTemplate() {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod(
                "deleteIndustryTemplate",
                null,
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                    }

                    override fun notImplemented() {
                    }
                })
        }
    }

    fun queryRfidTemplateByScanCode(
        oneCode: String,
        listener: (result: Boolean, templateJsonStr: String?, errorMsg: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeQueryRfidTemplateByScanCode", hashMapOf(
                Pair("oneCode", oneCode)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateDetailResult = result as? HashMap<String, Any>
                    templateDetailResult?.let {
                        listener?.invoke(
                            it["isSuccess"] as Boolean? ?: false,
                            it["template"] as String?,
                            it["errorMsg"] as String?
                        )
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(false, null, null)
                }

                override fun notImplemented() {
                    listener?.invoke(false, null, null)
                }
            })
        }
    }

    fun queryTemplateById(
        templateId: String,
        listener: (result: Boolean, templateJsonStr: String?, errorMsg: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeQueryTemplateById", hashMapOf(
                Pair("templateId", templateId)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateDetailResult = result as? HashMap<String, Any>
                    templateDetailResult?.let {
                        listener?.invoke(
                            it["isSuccess"] as Boolean? ?: false,
                            it["template"] as String?,
                            it["errorMsg"] as String?
                        )
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(false, null, null)
                }

                override fun notImplemented() {
                    listener?.invoke(false, null, null)
                }
            })
        }
    }

    fun getCloudTemplateByScanCode(
        oneCode: String,
        needUpdateDb: Boolean = false,
        listener: (result: Boolean, templateJsonStr: String?, errorMsg: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeGetCloudTempalteByScanCode", hashMapOf(
                Pair("needUpdateDb", needUpdateDb),
                Pair("oneCode", oneCode)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    LogUtils.iTag(
                        "FlutterMethodInvokeManager",
                        "getCloudTemplateByScanCode result=$result"
                    )
                    val templateDetailResult = result as? HashMap<String, Any>
                    templateDetailResult?.let {
                        listener?.invoke(
                            it["isSuccess"] as Boolean? ?: false,
                            it["template"] as String?,
                            it["errorMsg"] as String?
                        )
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    LogUtils.iTag(
                        "FlutterMethodInvokeManager", "getCloudTemplateByScanCode error"
                    )
                    listener?.invoke(false, null, null)
                }

                override fun notImplemented() {
                    LogUtils.iTag(
                        "FlutterMethodInvokeManager",
                        "getCloudTemplateByScanCode notImplemented"
                    )
                    listener?.invoke(false, null, null)
                }
            })
        }
    }

    fun getCloudTemplateByScanCodeOrLabelId(
        oneCode: String, labelId: String,
        needUpdateDb: Boolean = false,
        listener: (result: Boolean, templateJsonStr: String?, errorMsg: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeGetCloudTempalteByScanCodeOrLabelId", hashMapOf(
                Pair("needUpdateDb", needUpdateDb),
                Pair("oneCode", oneCode), Pair("labelId", labelId)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateDetailResult = result as? HashMap<String, Any>
                    templateDetailResult?.let {
                        listener?.invoke(
                            it["isSuccess"] as Boolean? ?: false,
                            it["template"] as String?,
                            it["errorMsg"] as String?
                        )
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(false, null, null)
                }

                override fun notImplemented() {
                    listener?.invoke(false, null, null)
                }
            })
        }
    }


    fun getMyTemplateList(
        page: Int,
        limit: Int,
        needNet: Boolean,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod("nativeGetMyTemplateList",
            hashMapOf(Pair("page", page), Pair("limit", limit), Pair("fromLocal", !needNet)),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    LogUtils.iTag(
                        "FlutterMethodInvokeManager", "getMyTemplateList result=$result"
                    )
                    val templateJson = result as? String
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "")
                    LogUtils.iTag(
                        "FlutterMethodInvokeManager",
                        "getMyTemplateList error=$errorCode errorMessage=$errorMessage"
                    )
                }

                override fun notImplemented() {
                    fail("getMyTemplateList未实现")
                }
            })
    }

    fun getMyGoodTemplateList(
        page: Int,
        limit: Int,
        commodityTemplate: Int,
        searchKey: String?,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod("nativeGetMyGoodTemplateList",
            hashMapOf(
                Pair("page", page),
                Pair("limit", limit),
                Pair("commodityTemplate", commodityTemplate),
                Pair("searchKey", searchKey)
            ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateJson = result as? String
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "")
                }

                override fun notImplemented() {
                    fail("getMyGoodTemplateList未实现")
                }
            })
    }

    fun insertOrUpdateTemplate(templateJson: String) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod(
                "insertOrUpdateTemplate",
                hashMapOf(Pair("template", templateJson)),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                    }

                    override fun notImplemented() {
                    }
                })
        }
    }

    suspend fun insertOrUpdateTemplateSuspend(templateJson: String): Boolean? {
        return withContext(Dispatchers.Main) {
            val deferred = CompletableDeferred<Boolean?>()
            // 调用 Flutter 方法
            methodChannel.invokeMethod(
                "insertOrUpdateTemplate",
                hashMapOf(Pair("template", templateJson)),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        // 成功时，完成 deferred 并返回结果
                        val invokeResult = result as? Boolean
                        deferred.complete(invokeResult)
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                        // 错误时，完成 deferred 并返回空字符串
                        deferred.complete(null)
                    }

                    override fun notImplemented() {
                        // Flutter 方法没有实现时，完成 deferred 并返回空字符串
                        deferred.complete(null)
                    }
                })
            // 等待异步任务的完成，并返回结果
            deferred.await()
        }
    }


    fun getTemplateDetailWithParams(
        templateId: String, isPersonalTemplate: Boolean = true,
        isShare: Boolean = false,
        isFolderShare: Boolean = false,
        listener: (result: Boolean, templateJsonStr: String?, errorMsg: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeGetTemplateDetailWithParams", hashMapOf(
                Pair("templateId", templateId),
                Pair("isPersonalTemplate", isPersonalTemplate),
                Pair("isShare", isShare),
                Pair("isFolderShare", isFolderShare)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateDetailResult = result as? HashMap<String, Any>
                    templateDetailResult?.let {
                        listener?.invoke(
                            it["isSuccess"] as Boolean? ?: false,
                            it["template"] as String?,
                            it["errorMsg"] as String?
                        )
                    }

                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(false, null, null)
                }

                override fun notImplemented() {
                    listener?.invoke(false, null, null)
                }
            })
        }
    }

    fun downloadTemplateRes(
        templateStr: String,
        listener: (templateJsonStr: String?) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod(
                "nativeDownloadTemplateResAndUpdateTemplate",
                hashMapOf(Pair("template", templateStr)),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        val templateJson = result as? String
                        templateJson?.let {
                            listener?.invoke(templateJson)
                        }

                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                        listener?.invoke(null)
                    }

                    override fun notImplemented() {
                        listener?.invoke(null)
                    }
                })
        }
    }

    suspend fun downloadTemplateResSuspend(templateStr: String): String? =
        withContext(Dispatchers.Main) {
            suspendCancellableCoroutine { continuation ->
                methodChannel.invokeMethod(
                    "nativeDownloadTemplateResAndUpdateTemplate",
                    hashMapOf("template" to templateStr),
                    object : MethodChannel.Result {
                        override fun success(result: Any?) {
                            val templateJson = result as? String
                            continuation.resume(templateJson)
                        }

                        override fun error(
                            errorCode: String,
                            errorMessage: String?,
                            errorDetails: Any?
                        ) {
                            continuation.resume(null)
                        }

                        override fun notImplemented() {
                            continuation.resume(null)
                        }
                    }
                )
            }
    }


    suspend fun getTemplateDetail(
        templateId: String,
        needUpdateDb: Boolean?,
        fromLocal: Boolean = false
    ): String? {
        return withContext(Dispatchers.Main) {
            // 使用 CompletableDeferred 来封装回调
            val deferred = CompletableDeferred<String?>()

            // 调用 Flutter 方法
            methodChannel.invokeMethod(
                "nativeGetTemplateDetail",
                hashMapOf(
                    Pair("templateId", templateId),
                    Pair("needUpdateDb", needUpdateDb),
                    Pair("fromLocal", fromLocal)
                ),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        // 成功时，完成 deferred 并返回结果
                        val templateJson = result as? String
//                    val templateDetail = templateJson?.let {
//                        TemplateModuleLocal.fromJson(it)
//                    }
                        deferred.complete(templateJson)
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                        // 错误时，完成 deferred 并返回空字符串
                        deferred.complete(null)
                    }

                    override fun notImplemented() {
                        // Flutter 方法没有实现时，完成 deferred 并返回空字符串
                        deferred.complete(null)
                    }
                })
            // 等待异步任务的完成，并返回结果
            deferred.await()
        }
    }


    fun getGoodFieldInfoListFromFlutter(
        listener: ((List<HashMap<String, Any>>?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod(
                "getGoodLibFieldsInfoFromFlutter",
                null,
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        listener?.invoke(result as? List<HashMap<String, Any>>?)
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                        listener?.invoke(null)
                    }

                    override fun notImplemented() {
                        listener?.invoke(null)
                    }
                })
        }

    }

    fun getPrintStrategy(
        event: Map<String, Any>,
        listener: ((HashMap<String, Any>?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("getPrintStrategy", event, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    listener?.invoke(result as HashMap<String, Any>)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(null)
                }

                override fun notImplemented() {
                    listener?.invoke(null)
                }
            })
        }
    }

    fun getNpsDataFromFlutter(
        languageCode: String,
        listener: ((HashMap<String, Any>?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("getNpsJson", hashMapOf(
                Pair("code", languageCode)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    listener?.invoke(result as? HashMap<String, Any>?)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(null)
                }

                override fun notImplemented() {
                    listener?.invoke(null)
                }
            })
        }

    }

    fun getPrintAdDataFromFlutter(
        listener: ((HashMap<String, Any>?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("getPrintAdInfo", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    listener?.invoke(result as? HashMap<String, Any>?)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(null)
                }

                override fun notImplemented() {
                    listener?.invoke(null)
                }
            })
        }

    }

    fun isShowNps(listener: ((String?) -> Unit)? = null) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("isShowNps", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    listener?.invoke(result as? String)
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    listener?.invoke(null)
                }

                override fun notImplemented() {
                    listener?.invoke(null)
                }
            })
        }

    }

    fun closeNps(hasSubmitNpsData: Boolean, uniAppId: String? = "") {
        GlobalScope.launch(Dispatchers.Main) {
            var flag = 0
            if (hasSubmitNpsData) {
                flag = 1
            }
            methodChannel.invokeMethod(
                "closeNps", hashMapOf(
                    Pair("closeNps", flag),
                    Pair("uniAppId", uniAppId)
                ), null
            )
        }

    }

    fun refreshPrintRecordList() {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("refreshPrintRecordList", emptyMap<String, String>(), null)
        }

    }

    fun writeLogInfoToFile(event: JSONObject) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("writeLogInfoToFile", event as Map<String, Any>, null)
        }

    }

    fun uploadLogInfoInFlutter(event: JSONObject) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("uploadLogInfoInFlutter", event as Map<String, Any>, null)
        }

    }

    fun uploadLogFileToSls(event: JSONObject) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("uploadLogFileToSls", event as Map<String, Any>, null)
        }

    }

    fun printerFirmwareUpgrade(event: JSONObject) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("printerFirmwareUpgrade", event as Map<String, Any>, null)
        }

    }

    /**
     * 商品数据转成externalData map
     */
    fun transformGoodsToExternalData(
        goodsJson: String,
        listener: ((HashMap<String, Any>?) -> Unit)? = null
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("transformGoodsToExternalData", hashMapOf(
                Pair("goodsJson", goodsJson)
            ), object : MethodChannel.Result {
                override fun success(result: Any?) {
                    listener?.invoke(result as? HashMap<String, Any>?)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    listener?.invoke(null)
                }

                override fun notImplemented() {
                    listener?.invoke(null)
                }
            })
        }

    }

    fun isShowRfid(callback: (Boolean) -> Unit) {
        methodChannel.invokeMethod("isShowRfid", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                callback?.invoke(result as? Boolean ?: false)
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                callback?.invoke(false)
            }

            override fun notImplemented() {
                callback?.invoke(false)
            }
        })
    }

    suspend fun hasExcelFileCache(hash: String): Boolean {

        return withContext(Dispatchers.Main) {
            suspendCancellableCoroutine { continuation ->
                methodChannel.invokeMethod(
                    "hasExcelCache",
                    hashMapOf(Pair("hash", hash)),
                    object : MethodChannel.Result {
                        override fun success(result: Any?) {
                            continuation.resume(result as? Boolean ?: false)
                        }

                        override fun error(
                            errorCode: String,
                            errorMessage: String?,
                            errorDetails: Any?
                        ) {
                            continuation.resume(false)
                        }

                        override fun notImplemented() {
                            continuation.resume(false)
                        }
                    })

            }
        }

    }

    fun getImLink(callback: (String) -> Unit) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("getIMLink", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    callback?.invoke(result as? String ?: "")
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    callback?.invoke("")
                }

                override fun notImplemented() {
                    callback?.invoke("")
                }
            })
        }

    }

    fun getFlutterPageName(callback: (String) -> Unit) {
        methodChannel.invokeMethod("getFlutterPage", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                callback?.invoke(result as? String ?: "")
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                callback?.invoke("")
            }

            override fun notImplemented() {
                callback?.invoke("")
            }
        })
    }

    fun checkC1TemplateShareDecodeStatus(callback: (Boolean) -> Unit) {
        methodChannel.invokeMethod(
            "checkC1TemplateShareDecodeStatus",
            null,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    callback.invoke(result as Boolean)
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    callback.invoke(false)
                }

                override fun notImplemented() {
                    callback.invoke(false)
                }
            })
    }

    fun closeFlutterPage() {
        methodChannel.invokeMethod("closeFlutterPage", null)
    }

    fun loginScanCode(client: String, loginCode: String, callback: (Boolean, String) -> Unit) {
        methodChannel.invokeMethod("loginScanCode",
            hashMapOf(Pair("loginCode", loginCode), Pair("client", client)),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val map = result as? HashMap<String, Any>
                    val res = map?.get("result") as? Boolean ?: false
                    var error = map?.get("error") as? String ?: ""
                    if (error.isNullOrEmpty()) {
                        error = LanguageUtil.findLanguageString("app100000328")
                    }
                    callback?.invoke(res, error)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    var error =
                        if (errorMessage.isNullOrEmpty()) LanguageUtil.findLanguageString("app100000328") else errorMessage
                    callback?.invoke(false, error)
                }

                override fun notImplemented() {
                    callback?.invoke(false, "loginScanCode未实现")
                }
            })
    }

    fun getShopProductInfo(
        oneCode: String,
        success: (HashMap<String, Any>) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod("getShopProductInfo", hashMapOf(Pair("oneCode", oneCode)),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val map = result as? HashMap<String, Any>
                    if (!map.isNullOrEmpty()) {
                        success(map)
                    } else {
                        fail("请求失败")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "请求失败")
                }

                override fun notImplemented() {
                    fail("getShopProductInfo未实现")
                }
            })
    }

    fun getC1PrintTemplate(templateIndex: Int, callback: (TemplateModuleLocal?) -> Unit) {
        methodChannel.invokeMethod("getC1PrintTemplate",
            hashMapOf(Pair("templateIndex", templateIndex)),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val printTemplateJson = result as? String
                    if (!printTemplateJson.isNullOrEmpty()) {
                        val printTemplate = TemplateModuleLocal.fromJson(printTemplateJson)
                        callback.invoke(printTemplate)
                    } else {
                        callback.invoke(null)
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    callback.invoke(null)
                }

                override fun notImplemented() {
                    callback.invoke(null)
                }
            })
    }

    fun clearAllAdData(success: (() -> Unit)? = null, fail: ((String) -> Unit)? = null) {
        methodChannel.invokeMethod("clearAllAdData", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke()
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "clearAllAdData 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("clearAllAdData notImplemented")
            }
        })
    }

    fun refreshAllAdData(
        data: String,
        success: (() -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        methodChannel.invokeMethod("saveAdToDatabase", data, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke()
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "saveAdToDatabase 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("saveAdToDatabase notImplemented")
            }
        })
    }

    fun getAllActiveAdData(success: (String) -> Unit, fail: (String) -> Unit) {
        methodChannel.invokeMethod(
            "getAdFromDatabase",
            hashMapOf(Pair("languageCode", TextHookUtil.getInstance().languageName)),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val list: List<Map<String, Any>>? = result as? List<Map<String, Any>>
                    if (list.isNullOrEmpty()) {
                        success.invoke("")
                    } else {
                        val gson = Gson()
                        val data = gson.toJson(list)
                        success.invoke(data)
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    fail.invoke(errorMessage ?: "getAdFromDatabase 调用失败")
                }

                override fun notImplemented() {
                    fail.invoke("getAdFromDatabase notImplemented")
                }
            })
    }

    fun clearAllBannerData(success: (() -> Unit)? = null, fail: ((String) -> Unit)? = null) {
        methodChannel.invokeMethod("clearAllBannerData", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke()
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "clearAllBannerData 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("clearAllBannerData notImplemented")
            }
        })
    }

    fun refreshAllBannerData(
        data: String,
        success: (() -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        methodChannel.invokeMethod("saveBannerToDatabase", data, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke()
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "saveBannerToDatabase 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("saveBannerToDatabase notImplemented")
            }
        })
    }

    fun getAllBannerData(success: (String) -> Unit, fail: (String) -> Unit) {
        methodChannel.invokeMethod("getBannerFromDatabase", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                val list: List<Map<String, Any>>? = result as? List<Map<String, Any>>
                if (list.isNullOrEmpty()) {
                    success.invoke("")
                } else {
                    val gson = Gson()
                    val data = gson.toJson(list)
                    success.invoke(data)
                }
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail.invoke(errorMessage ?: "getBannerFromDatabase 调用失败")
            }

            override fun notImplemented() {
                fail.invoke("getBannerFromDatabase notImplemented")
            }
        })
    }

    fun savePrintDataLog(
        data: String,
        success: (() -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        methodChannel.invokeMethod(
            "savePrintDataLogToDatabase",
            data,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    success?.invoke()
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    fail?.invoke(errorMessage ?: "savePrintDataLogToDatabase 调用失败")
                }

                override fun notImplemented() {
                    fail?.invoke("savePrintDataLogToDatabase notImplemented")
                }
            })
    }

//    fun savePrintContentLog(data: String, success: (() -> Unit)? = null, fail: ((String) -> Unit)? = null){
//        methodChannel.invokeMethod("savePrintContentLogToDatabase", data, object : MethodChannel.Result{
//            override fun success(result: Any?) {
//                success?.invoke()
//            }
//
//            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                fail?.invoke(errorMessage ?: "savePrintContentLogToDatabase 调用失败")
//            }
//
//            override fun notImplemented() {
//                fail?.invoke("savePrintContentLogToDatabase notImplemented")
//            }
//        })
//    }

//    suspend fun getPrintContentLog(page: Int = 0, pageSize: Int = 10): List<Map<String, Any>> {
//        return suspendCancellableCoroutine { continuation ->
//            val params = mapOf(
//                "page" to page,
//                "pageSize" to pageSize
//            )
//
//            methodChannel.invokeMethod("getPrintContentLogFromDatabase", params, object : MethodChannel.Result {
//                override fun success(result: Any?) {
//                    val list = result as? List<Map<String, Any>> ?: emptyList()
//                    continuation.resume(list) // 返回结果
//                }
//
//                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                    continuation.resumeWithException(Exception(errorMessage ?: "getPrintContentLogFromDatabase 调用失败"))
//                }
//
//                override fun notImplemented() {
//                    continuation.resumeWithException(Exception("getPrintContentLogFromDatabase notImplemented"))
//                }
//            })
//
//            // 如果协程被取消，确保停止当前操作
//            continuation.invokeOnCancellation {
//                // 这里可以添加额外的资源清理逻辑（如果需要）
//            }
//        }
//    }

//    fun getPrintContentLog(page: Int = 0, pageSize: Int = 10, success: (List<Map<String, Any>>) -> Unit, fail: (String) -> Unit){
//        val params = mapOf(
//            "page" to page,
//            "pageSize" to pageSize
//        )
//        methodChannel.invokeMethod("getPrintContentLogFromDatabase", params, object : MethodChannel.Result{
//            override fun success(result: Any?) {
//                val list: List<Map<String, Any>>? = result as? List<Map<String, Any>>
//                if(list.isNullOrEmpty()){
//                    success.invoke(emptyList())
//                }
//                else {
//                    success.invoke(list)
//                }
//            }
//
//            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                fail.invoke(errorMessage ?: "getAllPrintContentLogFromDatabase 调用失败")
//            }
//
//            override fun notImplemented() {
//                fail.invoke("getAllPrintContentLogFromDatabase notImplemented")
//            }
//        })
//    }

//    fun deleteUploadSuccessPrintContentLog(id: Int, success: (() -> Unit)? = null, fail: ((String) -> Unit)? = null){
//        methodChannel.invokeMethod("deleteUploadSuccessPrintContentLogNew", id, object : MethodChannel.Result{
//            override fun success(result: Any?) {
//                success?.invoke()
//            }
//
//            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                fail?.invoke(errorMessage ?: "deleteUploadSuccessPrintContentLogNew 调用失败")
//            }
//
//            override fun notImplemented() {
//                fail?.invoke("deleteUploadSuccessPrintContentLogNew notImplemented")
//            }
//        })
//    }

    fun savePrintHistory(
        data: String,
        success: (() -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        methodChannel.invokeMethod(
            "savePrintHistoryToDatabase",
            data,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    success?.invoke()
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    fail?.invoke(errorMessage ?: "savePrintHistoryToDatabase 调用失败")
                }

                override fun notImplemented() {
                    fail?.invoke("savePrintHistoryToDatabase notImplemented")
                }
            })
    }

    fun getAllPrintHistory(success: (List<Map<String, Any>>) -> Unit, fail: (String) -> Unit) {
        methodChannel.invokeMethod(
            "getAllPrintHistoryFromDatabase",
            null,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val list: List<Map<String, Any>>? = result as? List<Map<String, Any>>
                    if (list.isNullOrEmpty()) {
                        success.invoke(emptyList())
                    } else {
                        success.invoke(list)
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    fail.invoke(errorMessage ?: "getAllPrintHistoryFromDatabase 调用失败")
                }

                override fun notImplemented() {
                    fail.invoke("getAllPrintHistoryFromDatabase notImplemented")
                }
            })
    }

    fun deleteUploadSuccessPrintHistory(
        uniqueId: String,
        success: (() -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        val list = arrayListOf<String>()
        list.add(uniqueId)
        methodChannel.invokeMethod(
            "deleteUploadSuccessPrintHistory",
            list,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
//                Log.i("PrintHistoryBug", "UploadPrintHistoryLog deleteUploadSuccessPrintHistory success: uniqueId = $uniqueId")
                    success?.invoke()
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
//                Log.i("PrintHistoryBug", "UploadPrintHistoryLog deleteUploadSuccessPrintHistory fail: uniqueId = $uniqueId")
                    fail?.invoke(errorMessage ?: "deleteUploadSuccessPrintHistory 调用失败")
                }

                override fun notImplemented() {
                    fail?.invoke("deleteUploadSuccessPrintHistory notImplemented")
                }
            })
    }

    fun closeMessageList(success: (() -> Unit)? = null, fail: ((String) -> Unit)? = null) {
        methodChannel.invokeMethod("closeMessageList", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke()
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "closeMessageList 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("closeMessageList notImplemented")
            }
        })
    }

    fun notifyNetworkChange(connectedStatus: Int) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeNetworkChange", connectedStatus, null)
        }

    }

    fun generateLayoutSchemeTemplate(
        layoutScheme: String,
        formData: String,
        labelId: String,
        isPreview: Boolean = false,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod("generateLayoutSchemeTemplate", hashMapOf(
            Pair("layoutScheme", layoutScheme), Pair("formData", formData),
            Pair("labelId", labelId), Pair("isPreview", isPreview)
        ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateJson = result as? String
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("生成失败")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "生成失败")
                }

                override fun notImplemented() {
                    fail("generateLayoutSchemeTemplate未实现")
                }
            })
    }

    fun generateLayoutSchemeDataSourceTemplate(
        params: String,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod(
            "generateLayoutSchemeDataSourceTemplate", hashMapOf(
                Pair("meetingParams", params)
            ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateJson = result as? String
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("生成失败")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    showToast("app100000346")
                    fail(errorMessage ?: "生成失败")
                }

                override fun notImplemented() {
                    fail("generateLayoutSchemeDataSourceTemplate未实现")
                }
            })
    }

    fun generateTemplateWithLayout(
        params: String,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod(
            "generateTemplateWithLayout", hashMapOf(
                Pair("params", params)
            ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateJson = result as? String
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("生成失败")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "生成失败")
                }

                override fun notImplemented() {
                    fail("generateTemplateWithLayout未实现")
                }
            })
    }

    fun generatePreviewTemplateWithLayouts(
        params: String,
        success: (List<String>) -> Unit,
        fail: (String) -> Unit
    ) {
        methodChannel.invokeMethod(
            "generatePreviewTemplateWithLayouts", hashMapOf(
                Pair("params", params)
            ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    val templateJson = result as? List<String>
                    if (!templateJson.isNullOrEmpty()) {
                        success(templateJson)
                    } else {
                        fail("生成失败")
                    }
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    fail(errorMessage ?: "生成失败")
                }

                override fun notImplemented() {
                    fail("generatePreviewTemplateWithLayouts未实现")
                }
            })
    }

    /**检查离线弹窗策略*/
    fun checkOfflineAlert() {
        MainScope().launch {
            methodChannel.invokeMethod("checkOfflineAlert", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    LogUtils.e("=========checkOfflineAlert, result: ${any2Json(result)}")
                    processOfflineAlert(result)
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {

                }

                override fun notImplemented() {

                }

            })
        }
    }

    var _offlineAlertDialog: SuperDialog? = null
    private fun processOfflineAlert(result: Any?) {
        val currentActivity = ActivityUtils.getTopActivity();
        if (result != null && result is Map<*, *> && currentActivity != null) {
            val alertType = result["alertType"] as Int
            val alertTitle = result["alertTitle"] as? String ?: ""
            val alertMessage = result["alertMessage"] as? String ?: ""

            if (alertType > 0 && alertMessage.isNotEmpty()) {
                if (_offlineAlertDialog?.isShowing == true) {
                    return
                }
                MainScope().launch {
                    when (alertType) {
                        1 -> {
                            _offlineAlertDialog = CustomDialog.Builder(currentActivity)
                                .setLayoutId(R.layout.common_dialog_custom_new)
                                .setTitle(alertTitle)
                                .setMessage(alertMessage)
                                .setCancelOutside(false)
                                .setNegativeButton(
                                    "app00707"
                                ) { dialog, _ ->
                                    dialog.dismiss()
                                }.create()
                            _offlineAlertDialog?.setCancelable(false)
                            _offlineAlertDialog?.show()
                        }

                        2 -> {
                            _offlineAlertDialog = CustomDialog.Builder(currentActivity)
                                .setLayoutId(R.layout.common_dialog_custom_new)
                                .setTitle(alertTitle)
                                .setMessage(alertMessage)
                                .setCancelOutside(false).create()
                            _offlineAlertDialog?.setCancelable(false)
                            _offlineAlertDialog?.findViewById<TextView>(R.id.bottom_line)?.gone()
                            _offlineAlertDialog?.findViewById<LinearLayout>(R.id.bottom_layout)
                                ?.gone()
                            _offlineAlertDialog?.show()
                        }
                    }
                }
            } else {
                _offlineAlertDialog?.dismiss()
            }
        }
    }

    fun getReplaceTemplate(
        source: TemplateModuleLocal,
        target: TemplateModuleLocal,
        success: ((String?) -> Unit)? = null,
        fail: ((String) -> Unit)? = null
    ) {
        val params = mapOf(
            "originTemplate" to any2Json(source),
            "label" to any2Json(target)
        )
        methodChannel.invokeMethod("getReplaceTemplate", params, object : MethodChannel.Result {
            override fun success(result: Any?) {
                success?.invoke(any2Json(result))
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                fail?.invoke(errorMessage ?: "getReplaceTemplate 调用失败")
            }

            override fun notImplemented() {
                fail?.invoke("getReplaceTemplate notImplemented")
            }
        })
    }

    /**
     * 获取一次性授权码
     */
    fun getAuthCode(callback: (String) -> Unit) {
        methodChannel.invokeMethod("getAuthCode", null, object : MethodChannel.Result {
            override fun success(result: Any?) {
                callback.invoke(result.toString())
            }

            override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                callback.invoke("")
            }

            override fun notImplemented() {
                callback.invoke("")
            }
        })
    }

    fun notifyNetworkChange(connected: Boolean) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod("nativeNetworkChange", connected, null)
        }

    }

    fun getFontClassify(
        isFromLocal: Boolean = false,
        success: (String) -> Unit,
        fail: (String) -> Unit
    ) {
        GlobalScope.launch(Dispatchers.Main) {
            methodChannel.invokeMethod(
                "getFontClassify",
                hashMapOf(Pair("isFromLocal", isFromLocal)),
                object : MethodChannel.Result {
                    override fun success(result: Any?) {
                        val fontClassifyJson = result as? List<String>
                        if (!fontClassifyJson.isNullOrEmpty()) {
                            success(com.niimbot.utiliylibray.util.any2Json(fontClassifyJson))
                        } else {
                            fail("获取字体分类失败")
                        }
                    }

                    override fun error(
                        errorCode: String,
                        errorMessage: String?,
                        errorDetails: Any?
                    ) {
                        fail(errorMessage ?: "getFontClassify 调用失败")
                    }

                    override fun notImplemented() {
                        fail("getFontClassify notImplemented")
                    }
                })
        }
    }

    fun openOutsidePdf(filePath: String) {
        val fileName = File(filePath).name
        methodChannel.invokeMethod(
            "openPDFFileEvent", hashMapOf(
                Pair("fileName", fileName),
                Pair("filePath", filePath)
            ),
            object : MethodChannel.Result {
                override fun success(result: Any?) {

                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {

                }

                override fun notImplemented() {

                }
            })
    }

    fun setRtlSwitchStatus(status: Boolean) {
        methodChannel.invokeMethod(
            "setDrawboardRtlSwitchStatus",
            status,
            object : MethodChannel.Result {
                override fun success(result: Any?) {

                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {

                }

                override fun notImplemented() {

                }
            })
    }

    fun getRtlSwitchStatus(languageCode: String, callback: (Boolean) -> Unit) {
        methodChannel.invokeMethod(
            "getDrawboardRtlSwitchStatus",
            languageCode,
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    callback.invoke(result!! as Boolean)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    callback.invoke(false)
                }

                override fun notImplemented() {
                    callback.invoke(false)
                }
            })
    }

    fun getLayoutTemplate(template: TemplateModuleLocal?, callback: (String) -> Unit) {
        methodChannel.invokeMethod(
            "layoutTemplate",
            any2Json(template),
            object : MethodChannel.Result {
                override fun success(result: Any?) {
                    callback.invoke(result!! as String)
                }

                override fun error(
                    errorCode: String,
                    errorMessage: String?,
                    errorDetails: Any?
                ) {
                    callback.invoke("")
                }

                override fun notImplemented() {
                    callback.invoke("")
                }
            })
    }

    suspend fun getRtlSwitchStatusSync(languageCode: String): Boolean {
        return withContext(Dispatchers.Main) {
            suspendCancellableCoroutine { continuation ->
                getRtlSwitchStatus(languageCode) {
                    continuation.resume(it)
                }
            }
        }
    }
}

