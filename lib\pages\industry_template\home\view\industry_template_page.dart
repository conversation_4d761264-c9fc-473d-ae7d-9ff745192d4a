import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/image_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';
import 'package:text/pages/industry_template/home/<USER>/advertisement_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_model_size_page.dart';
import 'package:text/pages/industry_template/home/<USER>/template_sort_page.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/normal_button.dart';

import '../../../../application.dart';
import '../../../../utils/DebounceUtil.dart';
import '../../../../utils/cachedImageUtil.dart';
import '../controller/industry_template_logic.dart';
import '../service/advertisement_manager.dart';
import 'advertisement_widget.dart';
import 'package:niimbot_flutter_canvas/src/pages/canvas_industry_template/widgets/no_template_result_widget.dart';

/**
 * 行业模板
 */
class IndustryTemplatePage extends StatefulWidget {
  @override
  State<IndustryTemplatePage> createState() => _IndustryTemplatePageState();
}

class _IndustryTemplatePageState extends State<IndustryTemplatePage> with PageVisibilityObserver {
  /// 管理类
  late final IndustryTemplateLogic logic;

  /// 状态管理
  late final IndustryTemplateState state;

  @override
  void initState() {
    Get.delete<IndustryTemplateLogic>();
    PageVisibilityBinding.instance.removeObserver(this);

    logic = IndustryTemplateLogic();
    state = logic.state;
    Get.put(logic);
    logic.getPrinterAndLabelInfo();
    logic.refreshAdvertisements();
    super.initState();
  }

  @override
  void onPageShow() {
    super.onPageShow();
    logic.advertisementShowPoint();
  }

  @override
  void onPageHide() {
    super.onPageHide();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, ModalRoute.of(context) as Route);
  }

  @override
  void dispose() {
    // Get.delete<IndustryTemplateLogic>();
    // PageVisibilityBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Container(
        color: ThemeColor.listBackground,
        child: Column(
          children: [
            /// 搜索框
            _searchWidget(context),

            /// 顶部筛选按钮
            _checkFactorWidget(),

            /// 顶部弹窗及内容组件
            _slidingUpPanel(context),
          ],
        ),
      ),
    );
  }

  /// 更新标签纸模型，选中并刷新
  _updateLabelModel(Map<String, dynamic> json) {
    // 更新当前标签纸为尺寸进行筛选
    state.labelUseModel = LabelUsageRecord.fromJson(json);
    // 默认选中此标签纸尺寸,更新尺寸信息
    state.isLabelSizeSelected = true;
    state.size.value = Size(state.labelModel?.width.toDouble() ?? 0, state.labelModel?.height.toDouble() ?? 0);
    state.sizeSelectedState.value = "labelSize";

    logic.checkLabelMatchHardWare(
        labelSupportDeviceSet: {...?(state.labelUseModel?.profile.machineId.split(',').toSet())});
    logic.update(['labelSize']);
    state.hasSelectLabelByUser = true;
    // 刷新行业模版
    logic.getIndustryList();
  }

  _searchWidget(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.only(top: MediaQuery.paddingOf(context).top, end: 16, bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
              onPressed: () {
                //  FocusScope.of(Get.).requestFocus(FocusNode());
                Navigator.of(context).pop();
              },
              icon: const SvgIcon(
                'assets/images/industry_template/home/<USER>',
                width: 10,
                height: 16,
                matchTextDirection: true,
              )),
          Container(
            width: context.width / 1.22,
            decoration: BoxDecoration(
                color: ThemeColor.background,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(color: Colors.black.withOpacity(0.05), offset: const Offset(0, 4), blurRadius: 12)
                ]),
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                // 展开面板进行收起
                if (state.panelController.isPanelOpen) {
                  state.panelController.close();
                }

                ///flutter_boost在低机型下频繁切换存在白屏问题，改为通过原生处理
                // Navigator.of(context).push(MaterialPageRoute(
                //   settings: const RouteSettings(),
                //   builder: (context) => SearchTemplatePage(),
                // ));

                CustomNavigation.gotoNextPage('searchIndustryTemplate', {}, withContainer: false).then((value) {
                  if (value != null && value is Map) {
                    try {
                      _updateLabelModel(Map<String, dynamic>.from(value['labelData']));
                    } catch (error) {
                      Log.d(error.toString());
                    }
                  }
                });
              },
              child: Container(
                child: Row(
                  children: [
                    const SizedBox(
                      width: 12,
                    ),
                    const SvgIcon(
                      'assets/images/industry_template/replace_label/search_icon.svg',
                      matchTextDirection: true,
                    ),
                    Expanded(
                        child: Container(
                            padding: const EdgeInsetsDirectional.only(start: 8),
                            child: Text(
                              intlanguage('app100000494', '搜索模板名称、内容、关键字'),
                              style: const TextStyle(
                                color: ThemeColor.subtitle,
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ))),
                    // const SizedBox(
                    //   width: 40,
                    // ),
                    GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () async {
                          // 展开面板进行收起
                          if (state.panelController.isPanelOpen) {
                            state.panelController.close();
                          }

                          // /// 检查网络连接状态
                          // final connectivityResult = await (Connectivity().checkConnectivity());

                          /// 没有网络显示异常并退出
                          if (!Application.networkConnected) {
                            showToast(msg: intlanguage('app100000625', '当前网络状态异常'));
                            return;
                          }

                          /// 数据埋点
                          ToNativeMethodChannel()
                              .sendTrackingToNative({"track": "click", "posCode": "069_172_175", "ext": {}});

                          CustomNavigation.gotoNextPage('ScanBarcode', {}).then((result) {
                            ToNativeMethodChannel().sendTrackingToNative({
                              "track": "view",
                              "posCode": "011",
                              "ext": {
                                'type': '2',
                                'state': 4,
                                'type1': logic.state.hardWareWithSizeName.value,
                                'type2': logic.state.sortName.value,
                                'source': 1
                              }
                            });

                            /// 获取扫码结果
                            if (result is Map && result['value'] is String && result['value'].isNotEmpty) {
                              /// EasyLoading.show(status: '${intlanguage('app01202', '正在识别')}...');
                              state.panelController.close();

                              /// 识别标签纸编码
                              logic.searchByLabelBarcode(
                                  keywords: result['value'],
                                  resultClosure: (model) {
                                    if (model == null || model.list is! List) {
                                      showToast(msg: intlanguage('app01160', '数据请求失败'));
                                    } else if (model.list is List) {
                                      if (model.list!.isEmpty) {
                                        showToast(msg: intlanguage('app00321', '请使用正品耗材'));
                                      } else {
                                        _updateLabelModel((model.list!.first).rawJson);
                                      }
                                    }
                                  });
                            }
                          });
                        },
                        child: Row(
                          children: [
                            // const SizedBox(
                            //   width: 50,
                            // ),
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 51),
                              height: 20,
                              width: 0.5,
                              color: const Color(0xFFD9D9D9),
                            ),
                            const SizedBox(
                              width: 15,
                              height: 32,
                            ),
                            const SvgIcon(
                              'assets/images/industry_template/home/<USER>',
                              width: 18,
                              height: 18,
                            ),
                            const SizedBox(
                              width: 16,
                            ),
                          ],
                        )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _checkFactorWidget() {
    if (state.hardWareWithSizeName.value.length > 12) {
      state.hardWareWithSizeName.value = state.hardWareWithSizeName.value.substring(0, 12) + '...';
    }

    return Obx(() {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 12),
        decoration: const BoxDecoration(
            color: ThemeColor.background,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                NormalButton(
                  title: state.hardWareWithSizeName.value,
                  width: context.width / 2.2,
                  height: 32,
                  isSelected: state.sceneSelect() == IndustrySelectedState.modelSize ? true : false,
                  iconData: Icons.arrow_drop_down_sharp,
                  iconColor: ThemeColor.brand,
                  backgroundColor: ThemeColor.COLOR_FFEDEC,
                  textColor: ThemeColor.brand,
                  selectedBackgroundColor: ThemeColor.COLOR_FFEDEC,
                  selectedTextColor: ThemeColor.brand,
                  selectedClosure: () {
                    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "011_190", "ext": {}});
                    logic.topDialogClickListener(IndustrySelectedState.modelSize);
                  },
                ),
                SizedBox(
                  width: 0,
                ),
                NormalButton(
                  title: state.sortName.value,
                  width: context.width / 2.2,
                  height: 32,
                  isSelected: state.sceneSelect() == IndustrySelectedState.industryScenes ? true : false,
                  iconData: Icons.arrow_drop_down_sharp,
                  iconColor: state.localIndustrytemplate.value.id == -1 ? ThemeColor.COLOR_666666 : ThemeColor.brand,
                  textColor: state.localIndustrytemplate.value.id == -1 ? ThemeColor.COLOR_595959 : ThemeColor.brand,
                  backgroundColor:
                      state.localIndustrytemplate.value.id == -1 ? ThemeColor.COLOR_F5F5F5 : ThemeColor.COLOR_FFEDEC,
                  selectedTextColor: ThemeColor.brand,
                  selectedClosure: () {
                    logic.topDialogClickListener(IndustrySelectedState.industryScenes);
                  },
                ),
              ],
            ),
            Container(
              height: 2,
              color: ThemeColor.background,
            )
          ],
        ),
      );
    });
  }

  _slidingUpPanel(BuildContext context) {
    return GetBuilder<IndustryTemplateLogic>(builder: (logic) {
      return Expanded(
        child: SlidingUpPanel(
            controller: state.panelController,
            minHeight: 0,
            maxHeight: context.height * 0.6,
            slideDirection: SlideDirection.DOWN,
            isDraggable: false,
            backdropEnabled: true,
            backdropColor: Colors.black,
            backdropOpacity: 0.2,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(12.0),
              bottomRight: Radius.circular(12.0),
            ),
            onPanelSlide: (position) {
              if (state.lastPosition > position + 0.01) {
                // 当标签纸尺寸收起的时候，通过尺寸获取最新的模版数据
                if (state.sceneSelect() == IndustrySelectedState.modelSize) {
                  // 确认尺寸
                  logic.confirmAction();
                }
                // 正在收起
                state.sceneSelect.value = IndustrySelectedState.none;
              }
              state.lastPosition = position;
            },
            onPanelOpened: () {
              if (state.sceneSelect.value == IndustrySelectedState.modelSize) {
                ToNativeMethodChannel().sendTrackingToNative({"track": "show", "posCode": "011_192", "ext": {}});
              } else {
                ToNativeMethodChannel().sendTrackingToNative({
                  "track": "show",
                  "posCode": "011_191",
                  "ext": {'type1': state.hardWareWithSizeName.value, 'source': 1}
                });
              }
            },
            panel: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12.0),
                    bottomRight: Radius.circular(12.0),
                  ),
                ),
                child: _changeTopContainer()),
            body: GetBuilder<IndustryTemplateLogic>(builder: (logic) {
              return Container(
                color: ThemeColor.background,
                child: Column(
                  children: [
                    // 标签匹配提示
                    Obx(() => _labelUnmatch(state.isLabelMatch())),

                    // 内容区域
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(14, 0, 14, 0),
                        child: _buildContentArea(context),
                      ),
                    ),
                  ],
                ),
              );
            })),
      );
    });
  }

  _changeTopContainer() {
    switch (state.sceneSelect.value) {
      case IndustrySelectedState.modelSize:
        return IndustryModelSizePage();
      case IndustrySelectedState.industryScenes:
        if (state.lastHeight != state.interfaceSize.height || state.lastWidth != state.interfaceSize.width) {
          logic.getIndustryList();
        }
        return TemplateSortPage(logic);
      default:
        return Container();
    }
  }

  // 构建内容区域
  Widget _buildContentArea(BuildContext context) {
    switch (state.homeState) {
      case IndustryHomeState.empty:
        return SingleChildScrollView(
          padding: const EdgeInsets.only(top: 120),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              NoTemplateResultWidget(
                onFeedbackTap: () async {
                  ToNativeMethodChannel().sendTrackingToNative({
                    "track": "click",
                    "posCode": "011_453_455",
                    "ext": {"source": 2}
                  });
                  bool? result =
                      await CustomNavigation.gotoPage("templateFeedback", {"source": "industry"}, withContainer: true);
                  if (result == true) {
                    setState(() {
                      state.notifyState = true;
                    });
                  }
                },
                hasFeedback: state.notifyState,
              ),
            ],
          ),
        );

      case IndustryHomeState.loading:
        return Center(
          child: Container(
            padding: const EdgeInsets.only(bottom: 40),
            child: const CupertinoActivityIndicator(
              radius: 20,
              animating: true,
            ),
          ),
        );

      case IndustryHomeState.error:
        return Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.only(bottom: 40),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(ImageUtils.getImgPath('no_net'), fit: BoxFit.contain),
                Text(
                  intlanguage('app100000625', '当前网络状态异常'),
                  style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20),
                  child: NormalButton(
                    title: intlanguage('app100000626', '重新试试	'),
                    height: 40,
                    width: context.width / 3.3,
                    textColor: ThemeColor.brand,
                    decoration:
                        BoxDecoration(color: ThemeColor.listBackground, borderRadius: BorderRadius.circular(30)),
                    selectedClosure: () {
                      state.page = 1;
                      state.categoryListModel.clear();
                      logic.getIndustryList();
                    },
                  ),
                ),
              ],
            ),
          ),
        );

      case IndustryHomeState.showContainer:
        return MediaQuery.removePadding(
          context: context,
          removeTop: true,
          child: SmartRefresher(
            key: const ValueKey('main_refresher'),
            controller: state.refreshController,
            enablePullUp: true,
            onRefresh: () => logic.onRefresh(),
            onLoading: () => logic.onLoading(),
            child: CustomScrollView(
              slivers: [
                // 广告位 - 独占一行
                if (state.categoryListModel.isNotEmpty)
                  SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 15),
                      child: AdvertisementWidget(),
                    ),
                  ),

                // 模板网格 - 2列布局
                SliverMasonryGrid.count(
                  crossAxisCount: 2,
                  mainAxisSpacing: 15,
                  crossAxisSpacing: 15,
                  itemBuilder: (context, index) {
                    return IndustryTemplateItem(
                      model: state.categoryListModel[index],
                      index: index,
                    );
                  },
                  childCount: state.categoryListModel.length,
                ),

                // 使用 SliverFillRemaining 确保内容区域填满剩余空间 这种嵌套的IOS上无解 -wy
                // if (Platform.isIOS)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Container(
                    height: 120, // 提供适当的底部间距
                  ),
                ),
              ],
            ),
          ),
        );

      default:
        return Container();
    }
  }

  _labelUnmatch(bool isLabelMatch) {
    return !isLabelMatch
        ? Padding(
            padding: EdgeInsets.only(bottom: 5, left: 14, right: 14),
            child: Container(
              height: 45,
              decoration: BoxDecoration(color: ThemeColor.imageBackground, borderRadius: BorderRadius.circular(18)),
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 5),
              child: Row(
                children: [
                  const Padding(
                      padding: EdgeInsets.only(right: 3),
                      child: RotatedBox(
                        quarterTurns: 2,
                        child: Icon(
                          Icons.info_outline,
                          size: 20,
                          color: ThemeColor.brand,
                        ),
                      )),
                  Expanded(
                      child: Text(
                    intlanguage('app100000632', '当前标签纸与所选机型不匹配'),
                    style: const TextStyle(color: ThemeColor.brand, fontWeight: FontWeight.w400, fontSize: 14),
                  )),
                ],
              ),
            ),
          )
        : const SizedBox.shrink();
  }

  showIconToast() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 12.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.0),
        color: Colors.black.withOpacity(0.5),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                height: 18,
                width: 18,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(10.0), color: ThemeColor.background),
              ),
              Image.asset(
                'assets/images/industry_template/home/<USER>',
                height: 20,
                width: 20,
              ),
            ],
          ),
          SizedBox(
            width: 12.0,
          ),
          Text(
            intlanguage('app100000629', '反馈成功!'),
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.background),
          ),
        ],
      ),
    );
  }
}

///模板列表子widget
class IndustryTemplateItem extends StatelessWidget {
  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();

  final TemplateData model;
  final int index;

  IndustryTemplateItem({Key? key, required this.model, required this.index}) : super(key: key);

  _tapAction() {
    if (!DebounceUtil.checkClick()) return;
    if (logic.state.showTransitionPageState != 0) return;
    logic.goToCanvasPage(model);
    ToNativeMethodChannel().sendTrackingToNative({
      "track": "click",
      "posCode": "011_032_039",
      "ext": {
        'type': 2,
        'temp_id': model.id,
        'pos': index,
        'tab_id': model.marketingCategoryId,
        'is_vip': model.isVipRes ? 1 : 0,
        'type1': logic.state.hardWareWithSizeName.value,
        'type2': logic.state.sortName.value,
        'source': 1
      }
    });
    final ad = logic.state.currentAdvertisement.value;
    if (ad != null) {
      AdvertisementManager().markAdAsShown(ad);
    }
  }

  @override
  Widget build(BuildContext context) {
    var itemWidth = (MediaQuery.sizeOf(context).width - 45) / 2 - 30;
    var itemHeight = itemWidth / model.width * model.height;
    return GestureDetector(
      onTap: _tapAction,
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: ThemeColor.border, width: 0.5), borderRadius: BorderRadius.circular(12)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              decoration: const BoxDecoration(
                  color: ThemeColor.imageBackground,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
              child: Stack(
                children: [
                  Center(
                    child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical:
                                model.vip || model.hasVipRes || model.rawData['profile']['extrain']['templateType'] == 2
                                    ? 20
                                    : 20,
                            horizontal: 14),
                        child: Container(
                          constraints: const BoxConstraints(maxHeight: 242),
                          height: itemHeight + 4,
                          child: Stack(
                            alignment: AlignmentDirectional.center,
                            children: [
                              // Container(
                              //   child: Positioned.fill(child: LayoutBuilder(builder: (_, BoxConstraints cons) {
                              //     // debugPrint('-------------Positioned--$cons-------------');
                              //     return RotatedBox(
                              //       quarterTurns: model.canvasRotate ~/ 90,
                              //       child: LayoutBuilder(
                              //         builder: (_, BoxConstraints cons) {
                              //           // debugPrint('-------------RotatedBox--$cons-------------');
                              //           return CachedNetworkImage(
                              //               fit: BoxFit.fitWidth,
                              //               imageUrl: model.backgroundImage,
                              //               useOldImageOnUrlChange: true,
                              //               errorWidget: (_, __, ___) => const SvgIcon(
                              //                   'assets/images/industry_template/home/<USER>'));
                              //         },
                              //       ),
                              //     );
                              //   })),
                              // ),

                              // Container(
                              //   child: Image.network(model.contentThumbnail ?? ''),
                              // ),
                              Container(
                                child: CacheImageUtil().netCacheImage(
                                    fit: BoxFit.fitWidth,
                                    imageUrl: model.previewImage ?? '',
                                    filterQuality: FilterQuality.high,
                                    useOldImageOnUrlChange: true,
                                    errorWidget: const SvgIcon(
                                      'assets/images/industry_template/home/<USER>',
                                      width: 85,
                                      height: 15,
                                    )),
                              )
                            ],
                          ),
                        )),
                  ),
                  _isVipTemplate(model)
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(TextSpan(
                    children: [
                      ..._buildTemplateFlagWidget(model),
                      TextSpan(
                          text: model.name ?? '',
                          style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
                    ],
                  )),
                  const SizedBox(height: 2),
                  Text(
                    '${model.width}x${model.height}mm',
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  // _isVipTemplate(TemplateData model) {
  //   if (model.vip || model.hasVipRes) {
  //     if (model.rawData['profile']['extrain']['templateType'] == 2) {
  //       return Positioned(
  //           bottom: 0,
  //           child: Container(
  //               child: Row(
  //             children: [
  //               Padding(
  //                 padding: EdgeInsets.only(right: 3),
  //                 child: Image.asset(
  //                   'assets/images/print_history/vip_bottom.png',
  //                   height: 15,
  //                 ),
  //               ),
  //               Image.asset(
  //                 'assets/images/print_history/store.png',
  //                 height: 15,
  //               ),
  //             ],
  //           )));
  //     } else {
  //       return Positioned(
  //         bottom: 0,
  //         child: Container(
  //           child: Image.asset(
  //             'assets/images/print_history/vip_bottom.png',
  //             //    fit: BoxFit.cover,
  //             height: 15,
  //           ),
  //         ),
  //       );
  //     }
  //   } else if (model.rawData['profile']['extrain']['templateType'] == 2) {
  //     return Positioned(
  //       bottom: 0,
  //       child: Container(
  //         child: Image.asset(
  //           'assets/images/print_history/store.png',
  //           height: 15,
  //         ),
  //       ),
  //     );
  //   } else {
  //     return Container();
  //   }
  // }

  List<WidgetSpan> _buildTemplateFlagWidget(TemplateData model) {
    List<WidgetSpan> widgets = [];
    if (model.rawData['profile']['extrain']['templateType'] == 2) {
      widgets.add(WidgetSpan(
        alignment: PlaceholderAlignment.middle,
        child: Padding(
          padding: const EdgeInsetsDirectional.only(end: 2),
          child: SvgIcon(
            'assets/images/icon_flag_good_template.svg',
          ),
        ),
      ));
    }
    return widgets;
  }

  _isVipTemplate(TemplateData model) {
    if (model.vip! || model.hasVipRes) {
      return PositionedDirectional(
          top: 4,
          start: 4,
          child: Container(
              child: SvgIcon(
            'assets/images/icon_flag_vip_template.svg',
          )));
    } else {
      return Container();
    }
  }
}
