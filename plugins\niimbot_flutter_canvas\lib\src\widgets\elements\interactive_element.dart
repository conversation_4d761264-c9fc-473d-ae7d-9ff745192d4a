import 'dart:math' as math;

import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/src/widgets/box_frame/canvas_box_frame.dart';

import '/src/utils/theme_color.dart';
import '/src/widgets/components/stack2.dart';
import '/src/widgets/box_frame/web.dart';
import '/src/widgets/components/svg_icon.dart';
import '/src/widgets/attribute_panel/comm/font_size_config.dart';
import '../detector/niimbot_gesture_recognizer_conflicter.dart';

Logger _logger = Logger("DraggableResizable -> ", on: kDebugMode);

enum DragType { nothing, angle, position, size, horizontalSize, verticalSize, scale }

/// 控制点类型枚举
enum ControlPointType {
  /// 缩放类型 - 等比缩放拖拽
  scale,

  /// 固定宽高类型 - 固定比例，不允许自由拉伸
  fixedRatio,
}

class DragUpdate {
  const DragUpdate({
    required this.dragType,
    required this.rotate,
    required this.position,
    required this.size,
    required this.constraints,
  });

  final DragType dragType;

  final double rotate;

  final Offset position;

  final Size size;

  final Size constraints;

  @override
  String toString() {
    return "[ angle = $rotate , position = $position ,size = $size ,constraints = $constraints]";
  }
}

class InteractiveElement extends StatefulWidget {
  static const borderWidth = 1.0;
  static const floatingActionDiameter = 45.0;
  static const cornerDiameter = 40.0;
  static const floatingActionPadding = 30.0;
  static const itemMinHeight = 5.0;
  static const itemMinClickableHeight = 30.0;

  /// 控制点触控区域大小（比可见圆点大）
  static const controlPointTouchArea = Size(cornerDiameter, cornerDiameter);

  /// 控制点之间的最小间距
  static const minControlPointSpacing = 0.0;

  ///当元素小号小于等于2.5mm时，不显示底部中间锚点
  ///用锚点的宽度乘以系数
  static const minWidthFactor = 1.6;

  // static const cornerDiameter = 16.0;
  // static const floatingActionPadding = 16.0;

  // static double get offsetX =>
  //     (floatingActionPadding / 2) + (cornerDiameter / 2);

  // static double get offsetY =>
  //     (floatingActionPadding / 2) + (cornerDiameter / 2);

  InteractiveElement({
    super.key,
    required this.child,
    required this.size,
    required this.rotate,
    required this.isWidthHeightChanged,
    this.isLock = false,
    this.isMirror = false,
    this.isAssociateElement = false,
    BoxConstraints? constraints,
    this.onUpdate,
    required this.onDelete,
    required this.onCopy,
    required this.onTap,
    required this.onDoubleTap,
    this.canTransform = false,
    required this.startOffset,
    required this.minChildSize,
    required this.onDragEnd,
    this.onScaleEndWithFontSize,
    this.equalRatio = false,
    this.dragHorizontalOnly = false,
    this.tableCellFocused = false,
    required this.scale,
    this.resizeEnable = true,
    this.isDefaultElement = false,
    required this.isVipElement,
    required this.isBindingElement,
    this.isShowHandleBtn,
    this.showCenterDragBtn = false,
    this.showCornerDragBtn = true,
    this.showBottomDragBtn = false,
    this.textDirection = Axis.horizontal,
    this.currentFontSize,
    this.controlPointType = ControlPointType.fixedRatio,
    PlatformHelper? platformHelper,
  })  : constraints = constraints ?? BoxConstraints.loose(Size.infinite),
        platformHelper = platformHelper ?? PlatformHelper() {
    refreshFrame = true;
  }

  final Widget child;

  final ValueSetter<DragUpdate>? onUpdate;

  final VoidCallback onDelete;
  final VoidCallback onCopy;
  final VoidCallback onTap;
  final VoidCallback onDoubleTap;
  final bool canTransform;

  final Size size;
  final double rotate;
  final int isWidthHeightChanged;
  final bool isLock;
  final bool tableCellFocused;
  final bool isMirror;
  final bool isAssociateElement;
  final bool resizeEnable;

  final BoxConstraints constraints;

  final PlatformHelper platformHelper;

  final Offset startOffset;
  bool refreshFrame = false;

  final Size minChildSize;

  final VoidCallback onDragEnd;

  /// 等比缩放结束时，设置字号
  final ValueSetter<double>? onScaleEndWithFontSize;

  /// 等比放大
  final bool equalRatio;

  /// 仅横向拉升
  final bool dragHorizontalOnly;

  /// 当前画板缩放比例
  final double scale;
  final bool isVipElement;

  final bool isDefaultElement;

  //是否是绑定元素
  final bool isBindingElement;

  //是否处于多选状态
  final isShowHandleBtn;

  // 对角线拖动按钮
  final bool showCornerDragBtn;

  // 水平拖动按钮（根据）
  final bool showCenterDragBtn;

  // 垂直拖动按钮
  final bool showBottomDragBtn;

  // 文字方向
  final Axis textDirection;

  // 当前字号（用于等比缩放计算）
  double? currentFontSize;

  // 控制点类型
  final ControlPointType controlPointType;

  @override
  _InteractiveElementState createState() => _InteractiveElementState();
}

class _InteractiveElementState extends State<InteractiveElement> {
  late Size size;
  late BoxConstraints constraints;
  late double angle;
  late int isWidthHeightChanged;
  late double angleDelta;
  late double baseAngle;

  bool get isTouchInputSupported => widget.platformHelper.isMobile;

  Offset position = Offset.zero;

  /// 记录右下角拖拽的累积距离
  Offset _cumulativeBottomRightDragOffset = Offset.zero;

  /// 判断下方控制点是否应该显示
  bool shouldShowBottomControl(double normalizedWidth, double normalizedHeight) {
    final touchAreaSize = Size(InteractiveElement.controlPointTouchArea.width / widget.scale,
        InteractiveElement.controlPointTouchArea.height / widget.scale);
    final minSpacing = InteractiveElement.minControlPointSpacing / widget.scale;

    // 检查横向方向是否有足够空间
    bool hasHorizontalSpace = (normalizedWidth > touchAreaSize.height*InteractiveElement.minWidthFactor + minSpacing) || (normalizedHeight >  touchAreaSize.height/2);

    return hasHorizontalSpace;
  }

  @override
  void initState() {
    super.initState();
    constraints = const BoxConstraints.expand(width: 1, height: 1);
    angle = widget.rotate;
    isWidthHeightChanged = widget.isWidthHeightChanged;
    baseAngle = widget.rotate;
    angleDelta = 0;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.refreshFrame) {
      widget.refreshFrame = false;
      size = widget.size;
      position = widget.startOffset;
      isWidthHeightChanged = widget.isWidthHeightChanged;
      angle = widget.rotate;
      baseAngle = angle;
      angleDelta = 0;
    }

    double normalizedWidth = size.width;
    double normalizedHeight = size.height;

    final newSize = Size(normalizedWidth, normalizedHeight);

    if (widget.constraints.isSatisfiedBy(newSize)) size = newSize;

    void onUpdate(DragType dragType) {
      widget.onUpdate?.call(
        DragUpdate(
          dragType: dragType,
          position: position,
          size: size,
          constraints: Size(constraints.maxWidth, constraints.maxHeight),
          rotate: angle,
        ),
      );
    }

    void onDragBottomRight(Offset details) {
      details = Offset(details.dx, details.dy);
      double newHeight =
          widget.dragHorizontalOnly ? size.height : math.max(size.height + details.dy, widget.minChildSize.height);
      double newWidth = math.max(size.width + details.dx, widget.minChildSize.width);

      // 根据控制点类型和equalRatio属性调整拖拽行为
      if (widget.equalRatio || widget.controlPointType == ControlPointType.scale) {
        final maxOffset = details.dx;
        double aspectRatio = size.width / size.height;
        newWidth = math.max(size.width + maxOffset, widget.minChildSize.width);
        newHeight = newWidth / aspectRatio;
      }

      print("onDragBottomRight------newWidth = $newWidth, newHeight = $newHeight");

      Size updatedSize = Size(newWidth, newHeight);
      // if (!widget.constraints.isSatisfiedBy(updatedSize)) {
      //   _logger.log("position test isSatisfiedBy false");
      //   return;
      // }
      size = updatedSize;
      // 根据控制点类型选择合适的 DragType
      DragType dragType = widget.controlPointType == ControlPointType.scale ? DragType.scale : DragType.size;
      onUpdate(dragType);
    }

    void onDragCenterRight(Offset details) {
      details = Offset(details.dx, details.dy);
      double newHeight =
          widget.dragHorizontalOnly ? size.height : math.max(size.height + details.dy, widget.minChildSize.height);
      double newWidth = math.max(size.width + details.dx, widget.minChildSize.width);
      if (widget.textDirection == Axis.horizontal) {
        newHeight = size.height;
      } else if (widget.textDirection == Axis.vertical) {
        newWidth = size.width;
      }
      // print("position test onDragBottomRight offset=$details w=$newWidth h=$newHeight size=$size minWidth=${widget.minChildSize.width}");

      if (widget.equalRatio) {
        final maxOffset = details.dx;
        double aspectRatio = size.width / size.height;
        newWidth = math.max(size.width + maxOffset, widget.minChildSize.width);
        newHeight = newWidth / aspectRatio;
      }

      Size updatedSize = Size(newWidth, newHeight);
      // if (!widget.constraints.isSatisfiedBy(updatedSize)) {
      //   _logger.log("position test isSatisfiedBy false");
      //   return;
      // }
      size = updatedSize;
      onUpdate(DragType.horizontalSize);
    }

    void onDragCenterBottom(Offset details) {
      details = Offset(details.dx, details.dy);
      double newWidth = math.max(size.width + details.dx, widget.minChildSize.width);
      double newHeight = math.max(size.height + details.dy, widget.minChildSize.height);

      if (widget.textDirection == Axis.horizontal) {
        newWidth = size.width;
      } else if (widget.textDirection == Axis.vertical) {
        newHeight = size.height;
      }

      if (widget.equalRatio) {
        final maxOffset = details.dy;
        double aspectRatio = size.width / size.height;
        newHeight = math.max(size.height + maxOffset, widget.minChildSize.height);
        newWidth = newHeight * aspectRatio;
      }

      Size updatedSize = Size(newWidth, newHeight);
      size = updatedSize;
      onUpdate(DragType.verticalSize);
    }

    final bindingSelectedBG = Positioned(
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      child: Offstage(
        offstage: !((widget.canTransform && widget.isBindingElement)), //|| widget.tableCellFocused
        child: Container(
          decoration: BoxDecoration(),
          clipBehavior: Clip.hardEdge,
          height: normalizedHeight + InteractiveElement.borderWidth * 2,
          width: normalizedWidth + InteractiveElement.borderWidth * 2,
          //math.pi/4
          child: CustomPaint(
            painter: BingingElementBGCustomPainter(angle, isWidthHeightChanged),
          ),
        ),
      ),
    );

    final decoratedChild = Container(
      key: const Key('draggableResizable_child_container'),
      alignment: Alignment.center,
      height: (normalizedHeight < InteractiveElement.itemMinHeight
              ? InteractiveElement.itemMinClickableHeight
              : normalizedHeight) +
          InteractiveElement.borderWidth * 2,
      width: normalizedWidth + InteractiveElement.borderWidth * 2,
      child: (widget.canTransform && (widget.isMirror || widget.isAssociateElement))
          ? Container(
              height: normalizedHeight,
              width: normalizedWidth,
              child: Stack(
                clipBehavior: Clip.hardEdge,
                children: [
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: DottedBorder(
                        borderType: BorderType.Rect,
                        dashPattern: [InteractiveElement.borderWidth * 2, InteractiveElement.borderWidth * 2],
                        color: widget.isDefaultElement
                            ? Colors.transparent
                            : (widget.isVipElement ? ThemeColor.COLOR_FEC96E : Colors.blue),
                        strokeWidth: InteractiveElement.borderWidth,
                        padding: EdgeInsets.all(0.1),
                        child: Container(
                          height: normalizedHeight,
                          width: normalizedWidth,
                          child: Center(child: widget.child),
                        )),
                  ),
                  //设置在绘制内容的上层 防止反白后被覆盖
                  bindingSelectedBG,
                ],
              ),
            )
          : Container(
              height: normalizedHeight + InteractiveElement.borderWidth * 2,
              width: normalizedWidth + InteractiveElement.borderWidth * 2,
              child: Stack(
                clipBehavior: Clip.hardEdge,
                children: [
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: Container(
                      /// +4 留出边框的占用位置
                      height: normalizedHeight + InteractiveElement.borderWidth * 2,
                      width: normalizedWidth + InteractiveElement.borderWidth * 2,
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: InteractiveElement.borderWidth,
                          color: widget.canTransform
                              ? (widget.isDefaultElement
                                  ? Colors.transparent
                                  : (widget.isVipElement ? ThemeColor.COLOR_FEC96E : Colors.blue))
                              : Colors.transparent,
                        ),
                      ),
                      child: Center(child: widget.child),
                    ),
                  ),
                  //设置在绘制内容的上层 防止反白后被覆盖
                  bindingSelectedBG,
                ],
              ),
            ),
    );

    final bottomRightCorner = _ResizePoint(
      key: const Key('draggableResizable_bottomRight_resizePoint'),
      type: _ResizePointType.bottomRight,
      onDrag: (widget.isLock || widget.isDefaultElement)
          ? null
          : (details) {
              if (widget.controlPointType == ControlPointType.scale) {
                // 累积拖拽距离
                _cumulativeBottomRightDragOffset = Offset(
                  _cumulativeBottomRightDragOffset.dx + details.dx,
                  _cumulativeBottomRightDragOffset.dy + details.dy,
                );
              }
              onDragBottomRight(details);
            },
      onDragEnd: () {
        // 根据拖动的距离计算字号
        if (widget.controlPointType == ControlPointType.scale) {
          // 获取当前标签纸下的最大字号
          List<FontSizeConfig>? fontSizeConfigList = calcMaxFontSize(
              CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble(),
              CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble());
          num scaleRatio = (size.width - _cumulativeBottomRightDragOffset.dx) / size.width;
          double scaledFontSize = widget.currentFontSize! * (1 / scaleRatio);
          int newIndex = fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(scaledFontSize);
          double newFontSize = fontSizeConfigList[newIndex].mm;
          String fontTitle = fontSizeConfigList[newIndex].title;
          print(
              "onDragBottomRight------newIndex = $newIndex, fontTitle = $fontTitle, scaledFontSize = $scaledFontSize");
          // print("onDragBottomRight------newIndex = $newIndex, scaledFontSize = ${allFontSizeConfigList[newIndex].title}");
          // 重置累积拖拽距离
          _cumulativeBottomRightDragOffset = Offset.zero;
          // 字号更新
          widget.onScaleEndWithFontSize?.call(newFontSize);
        }
        widget.onDragEnd();
      },
      iconUrl:
          widget.controlPointType == ControlPointType.scale ? "assets/element/attribute/scale_anchor_point.svg" : null,
      // 固定宽高类型使用默认圆点样式
      isVipElement: widget.isVipElement,
      isDefaultElement: widget.isDefaultElement,
      scale: widget.scale,
      alignment: Alignment.topLeft,
    );

    /// 横向拖动块
    final centerRightCorner = _ResizePoint(
      key: const Key('draggableResizable_centerRight_resizePoint'),
      type: _ResizePointType.centerRight,
      onDrag: widget.isLock ? null : onDragCenterRight,
      onDragEnd: widget.isLock ? null : widget.onDragEnd,
      isVipElement: widget.isVipElement,
      scale: widget.scale,
      alignment: Alignment.centerLeft,
    );

    /// 纵向拖动块
    final centerBottomCorner = _ResizePoint(
      key: const Key('draggableResizable_centerBottom_resizePoint'),
      type: _ResizePointType.centerBottom,
      onDrag: widget.isLock ? null : onDragCenterBottom,
      onDragEnd: widget.isLock ? null : widget.onDragEnd,
      isVipElement: widget.isVipElement,
      scale: widget.scale,
      alignment: Alignment.topCenter,
    );

    final lockedCorner = Center(
      child: Container(
          width: 15, height: 15, child: SvgIcon('assets/common/icon_element_lock.svg', useDefaultColor: false)),
    );

    /// 屏蔽自由角度旋转的交互
    // final center = Offset(-DraggableResizable.cornerDiameter / 2,
    //     (normalizedHeight / 2 + (DraggableResizable.cornerDiameter / 2)));
    //
    // final rotateAnchor = GestureDetector(
    //   key: const Key('draggableResizable_rotate_gestureDetector'),
    //   onScaleStart: (details) {
    //     final offsetFromCenter = details.localFocalPoint - center;
    //     setState(() => angleDelta = baseAngle - offsetFromCenter.direction);
    //   },
    //   onScaleUpdate: (details) {
    //     final offsetFromCenter = details.localFocalPoint - center;
    //     _logger.log(
    //         "details.localFocalPoint = ${details.localFocalPoint} , center = $center");
    //     setState(() => angle = offsetFromCenter.direction + angleDelta);
    //     onUpdate(DragType.angle);
    //   },
    //   onScaleEnd: (_) => setState(() => baseAngle = angle),
    //   // child: _FloatingActionIcon(
    //   //   key: const Key('draggableResizable_rotate_floatingActionIcon'),
    //   //   iconData: Icons.rotate_90_degrees_ccw,
    //   //   onTap: () {},
    //   // ),
    //   child: Container(
    //     width: DraggableResizable.cornerDiameter,
    //     height: DraggableResizable.cornerDiameter,
    //     child: Image.asset("assets/images/ic_canvas_corner_rotate.png"),
    //   ),
    //   //child: Image.asset("assets/images/ic_canvas_corner_rotate.png"),
    // );

    if (this.constraints != constraints) {
      this.constraints = constraints;
      onUpdate(DragType.nothing);
    }

    return Stack2(
      clipBehavior: Clip.none,
      children: <Widget>[
        Positioned(
          top: normalizedHeight < InteractiveElement.itemMinHeight
              ? (position.dy - InteractiveElement.itemMinClickableHeight / 2 - InteractiveElement.borderWidth / 2)
              : (position.dy - InteractiveElement.borderWidth),
          left: position.dx - InteractiveElement.borderWidth,
          child: Transform.rotate(
            alignment: Alignment.center,
            angle: math.pi * angle / 180,
            child: Stack2(
              clipBehavior: Clip.none,
              children: [
                DraggablePoint(
                  key: const Key('draggableResizable_child_draggablePoint'),
                  // onTap: onUpdate,
                  onDrag: widget.isLock || widget.tableCellFocused || widget.isDefaultElement
                      ? null
                      : (d) {
                          setState(() {
                            position = Offset(position.dx + d.dx / widget.scale, position.dy + d.dy / widget.scale);
                          });
                          onUpdate(DragType.position);
                        },
                  onDragEnd: widget.onDragEnd,
                  onRotate: (a) {
                    /// 屏蔽自由角度旋转的交互
                    // setState(() => angle = a);
                    // onUpdate(DragType.angle);
                  },
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    child: decoratedChild,
                    onTap: widget.onTap,
                    onDoubleTap: widget.onDoubleTap,
                  ),
                ),
                if (widget.canTransform && isTouchInputSupported) ...[
                  /// 屏蔽自由角度旋转的交互
                  // Positioned(
                  //   top: DraggableResizable.floatingActionPadding / 2,
                  //   left: normalizedWidth +
                  //       DraggableResizable.floatingActionPadding / 2,
                  //   // child: topRightCorner,
                  //   child: rotateAnchor,
                  // ),
                  //yc to do
                  buildCenterRightCornerWidget(normalizedHeight, normalizedWidth, lockedCorner, centerRightCorner),
                  // 是否足够放下纵向控制点
                  Visibility(
                    visible: shouldShowBottomControl(normalizedWidth, normalizedHeight),
                    child: buildCenterBottomCornerWidget(
                        normalizedHeight, normalizedWidth, lockedCorner, centerBottomCorner),
                  ),
                  // 右下角控制点，根据类型展示，存在缩放类型以及模式三类型，需要根据类型展示不同的控制点
                  buildBottomRightCornerWidget(
                      normalizedHeight, normalizedWidth, lockedCorner, bottomRightCorner, widget.controlPointType),
                ],
                // 右侧控制点
              ],
            ),
          ),
        ),
      ],
    );
  }

  Positioned buildBottomRightCornerWidget(double normalizedHeight, double normalizedWidth, Center lockedCorner,
      _ResizePoint bottomRightCorner, ControlPointType controlPointType) {
    final touchAreaSize = Size(InteractiveElement.controlPointTouchArea.width / widget.scale,
        InteractiveElement.controlPointTouchArea.height / widget.scale);
    final minSpacing = InteractiveElement.minControlPointSpacing / widget.scale;

    // 将 SizedBox 右下角对齐到元素右下角，然后补偿 padding 偏移
    var topPos = (normalizedHeight < InteractiveElement.itemMinHeight
            ? InteractiveElement.itemMinClickableHeight / 2
            : normalizedHeight) +
        InteractiveElement.borderWidth -
        touchAreaSize.height / 4;

    // 在缩放模式下检测与右中控制点的重叠，如果重叠则向下偏移
    if (controlPointType == ControlPointType.scale && widget.showCenterDragBtn) {
      // 计算右中控制点的位置
      final centerRightTop = widget.textDirection == Axis.horizontal
          ? ((normalizedHeight < InteractiveElement.itemMinHeight
                  ? InteractiveElement.itemMinClickableHeight / 2
                  : normalizedHeight / 2) -
              touchAreaSize.height / 2 +
              InteractiveElement.borderWidth)
          : (normalizedHeight + InteractiveElement.borderWidth * 1.5 - touchAreaSize.height / 2);

      // 检测垂直方向是否重叠
      final bottomRightTop = topPos;
      final bottomRightBottom = bottomRightTop + 12;
      final centerRightBottom = centerRightTop + 12;

      if (bottomRightTop < centerRightBottom && bottomRightBottom > centerRightTop) {
        // 存在重叠，将右下角控制点移动到右中控制点下方，刚好不重叠
        topPos = centerRightBottom + minSpacing;
      }
    }

    final leftPos = normalizedWidth + InteractiveElement.borderWidth - touchAreaSize.width / 4;

    return Positioned(
      top: widget.isLock ? topPos - touchAreaSize.height / 4 :topPos,
      left: widget.isLock ? leftPos - touchAreaSize.width / 4:leftPos,
      child: SizedBox(
        width: touchAreaSize.width,
        height: touchAreaSize.height,
        child: (widget.isMirror || widget.isAssociateElement || widget.isDefaultElement)
            ? Container()
            : (widget.isLock && widget.showCornerDragBtn
                ? Center(child: lockedCorner)
                : (widget.resizeEnable && (widget.isShowHandleBtn ?? false) == true && widget.showCornerDragBtn)
                    ? bottomRightCorner
                    : Container()),
      ),
    );
  }

  Positioned buildCenterRightCornerWidget(
      double normalizedHeight, double normalizedWidth, Center lockedCorner, _ResizePoint centerRightCorner) {
    final touchAreaSize = Size(InteractiveElement.controlPointTouchArea.width / widget.scale,
        InteractiveElement.controlPointTouchArea.height / widget.scale);

    // 判断右下角控制点是否显示
    bool isBottomRightShowing = widget.resizeEnable &&
        (widget.isShowHandleBtn ?? false) == true &&
        widget.showCornerDragBtn &&
        !(widget.isMirror || widget.isAssociateElement);

    // 根据文字方向计算位置，然后补偿 padding 偏移
    final topPos = widget.textDirection == Axis.horizontal
        ? ((normalizedHeight < InteractiveElement.itemMinHeight
                ? InteractiveElement.itemMinClickableHeight / 2
                : normalizedHeight / 2) - touchAreaSize.height/2 +
            InteractiveElement.borderWidth)
        : (normalizedHeight + InteractiveElement.borderWidth * 1.5 - touchAreaSize.height / 2);

    final leftPos = widget.textDirection == Axis.horizontal
        ? (normalizedWidth + InteractiveElement.borderWidth - touchAreaSize.width / 4)
        : (normalizedWidth / 2 - touchAreaSize.width / 2 + InteractiveElement.borderWidth);

    return Positioned(
      top: topPos,
      left: leftPos,
      child: SizedBox(
        width: touchAreaSize.width,
        height: touchAreaSize.height,
        child: (widget.isMirror || widget.isAssociateElement)
            ? Container()
            : (widget.isLock && widget.showCenterDragBtn
                ? (isBottomRightShowing
                    ? Container() // 右下角显示时，不显示锁定图标, 显示空
                    : Center(child: lockedCorner)) // 右下角不显示时，显示锁定图标
                : (widget.resizeEnable && (widget.isShowHandleBtn ?? false) == true && widget.showCenterDragBtn)
                    ? centerRightCorner
                    : Container()),
      ),
    );
  }

  Positioned buildCenterBottomCornerWidget(
      double normalizedHeight, double normalizedWidth, Center lockedCorner, _ResizePoint centerBottomCorner) {
    final touchAreaSize = Size(InteractiveElement.controlPointTouchArea.width / widget.scale,
        InteractiveElement.controlPointTouchArea.height / widget.scale);

    // 判断右下角控制点是否显示
    bool isBottomRightShowing = widget.resizeEnable &&
        (widget.isShowHandleBtn ?? false) == true &&
        widget.showCornerDragBtn &&
        !(widget.isMirror || widget.isAssociateElement);

    // 根据文字方向计算位置，然后补偿 padding 偏移
    final topPos = widget.textDirection == Axis.horizontal
        ? (normalizedHeight + InteractiveElement.borderWidth - touchAreaSize.height / 4)
        : (normalizedHeight / 2 - touchAreaSize.height / 2 + InteractiveElement.borderWidth);

    final leftPos = widget.textDirection == Axis.horizontal
        ? (normalizedWidth / 2 - touchAreaSize.width / 2 + InteractiveElement.borderWidth)
        : ((normalizedWidth < InteractiveElement.itemMinHeight
                ? InteractiveElement.itemMinClickableHeight / 2
                : normalizedWidth / 2) -
            touchAreaSize.width / 2 +
            InteractiveElement.borderWidth);

    return Positioned(
      top: topPos,
      left: leftPos,
      child: SizedBox(
        width: touchAreaSize.width,
        height: touchAreaSize.height,
        child: (widget.isMirror || widget.isAssociateElement)
            ? Container()
            : (widget.isLock && widget.showBottomDragBtn
                ? (isBottomRightShowing
                    ? Container() // 右下角显示时，不显示锁定图标, 显示空
                    : Center(child: lockedCorner)) // 右下角不显示时，显示锁定图标
                : (widget.resizeEnable && (widget.isShowHandleBtn ?? false) == true && widget.showBottomDragBtn)
                    ? centerBottomCorner
                    : Container()),
      ),
    );
  }

  Widget corner({String? iconUrl, Function()? onTap}) => GestureDetector(
        onTap: onTap,
        child: Container(
          width: InteractiveElement.cornerDiameter,
          height: InteractiveElement.cornerDiameter,
          child: Container(
            width: InteractiveElement.cornerDiameter,
            height: InteractiveElement.cornerDiameter,
            child: iconUrl != null
                ? Image.asset(iconUrl)
                : Container(
                    decoration: const BoxDecoration(
                      color: Color(0xFFFFFFFF),
                      shape: BoxShape.circle,
                    ),
                  ),
          ),
        ),
      );
}

enum _ResizePointType {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  centerRight,
  centerBottom,
}

const _cursorLookup = <_ResizePointType, MouseCursor>{
  _ResizePointType.topLeft: SystemMouseCursors.resizeUpLeft,
  _ResizePointType.topRight: SystemMouseCursors.resizeUpRight,
  _ResizePointType.bottomLeft: SystemMouseCursors.resizeDownLeft,
  _ResizePointType.bottomRight: SystemMouseCursors.resizeDownRight,
  _ResizePointType.centerRight: SystemMouseCursors.resizeRight,
  _ResizePointType.centerBottom: SystemMouseCursors.resizeDown,
};

class _ResizePoint extends StatelessWidget {
  const _ResizePoint({
    super.key,
    this.onDrag,
    required this.type,
    this.onScale,
    this.iconUrl,
    this.onDragEnd,
    required this.scale,
    required this.isVipElement,
    required this.alignment,
    this.isDefaultElement = false,
  });

  final ValueSetter<Offset>? onDrag;
  final ValueSetter<double>? onScale;
  final _ResizePointType type;
  final String? iconUrl;
  final VoidCallback? onDragEnd;
  final bool isVipElement;
  final double scale;
  final bool isDefaultElement;
  final Alignment alignment;
  MouseCursor get _cursor {
    return _cursorLookup[type]!;
  }

  @override
  Widget build(BuildContext context) {
    Widget visibleDot = AnchorpointWidget(scale,
        isVip: this.isVipElement, svgIcon: this.iconUrl, isDefaultElement: this.isDefaultElement);
    return MouseRegion(
      cursor: _cursor,
      child: DraggablePoint(
        mode: _PositionMode.local,
        onDrag: onDrag,
        onScale: onScale,
        onDragEnd: onDragEnd,
        child: Container(
          alignment: alignment,
          // 可见圆点区域
          width: InteractiveElement.cornerDiameter / scale,
          height: InteractiveElement.cornerDiameter / scale,
          child: Container(
              child: visibleDot,
                  height: InteractiveElement.cornerDiameter / scale*0.5,
            width: InteractiveElement.cornerDiameter / scale*0.5,
          ),
        ),
      ),
    );
  }
}

enum _PositionMode { local, global }

class AnchorpointWidget extends StatelessWidget {
  final bool isVip;
  final bool isDefaultElement;
  final double scale;
  final String? svgIcon;
  const AnchorpointWidget(this.scale,
      {super.key,
      required this.isVip,
      this.svgIcon = "assets/element/attribute/scale_anchor_point.svg",
      this.isDefaultElement = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: InteractiveElement.cornerDiameter / scale,
      height: InteractiveElement.cornerDiameter / scale,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.transparent,
      ),
      child: Container(
        alignment: Alignment.center,
        width: isDefaultElement ? 0 : 10 / scale,
        height: isDefaultElement ? 0 : 10 / scale,
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.25),
              blurRadius: 4,
              offset: Offset(0, 0),
            ),
          ],
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2 / scale, strokeAlign: BorderSide.strokeAlignOutside),
          color: svgIcon == null ? (isVip == true ? Color(0xFFFEC96E) : Color(0xFF00A7FF)) : Colors.white,
        ),
        child: svgIcon != null
            ? SvgIcon(
                svgIcon ?? '',
                color: isVip == true ? Color(0xFFFEC96E) : null,
                width: 12 / scale,
                height: 12 / scale,
                useDefaultColor: false,
                fit: BoxFit.contain,
              )
            : SizedBox.shrink(),
      ),
    );
  }
}

class DraggablePoint extends StatefulWidget {
  const DraggablePoint({
    super.key,
    required this.child,
    this.onDrag,
    this.onScale,
    this.onRotate,
    this.onTap,
    this.mode = _PositionMode.global,
    this.onDragEnd,
  });

  final Widget child;
  final _PositionMode mode;
  final ValueSetter<Offset>? onDrag;
  final ValueSetter<double>? onScale;
  final ValueSetter<double>? onRotate;
  final VoidCallback? onTap;
  final VoidCallback? onDragEnd;

  @override
  _DraggablePointState createState() => _DraggablePointState();
}

class _DraggablePointState extends State<DraggablePoint> {
  late Offset initPoint;
  var baseScaleFactor = 1.0;
  var scaleFactor = 1.0;
  var baseAngle = 0.0;
  var angle = 0.0;

  @override
  Widget build(BuildContext context) {
    return NiimbotCustomItemScaleGestureDetector(
      gestureSettings: widget.onDrag == null ? null : DeviceGestureSettings(touchSlop: 2),
      behavior: (widget.mode == _PositionMode.global ? HitTestBehavior.translucent : HitTestBehavior.opaque),
      mode: (widget.mode == _PositionMode.global
          ? NiimbotPanGesturePositionMode.global
          : NiimbotPanGesturePositionMode.local),
      onTap: () {
        widget.onTap?.call();
      },
      onScaleStart: (details) {
        switch (widget.mode) {
          case _PositionMode.global:
            initPoint = details.focalPoint;
            break;
          case _PositionMode.local:
            initPoint = details.localFocalPoint;
            break;
        }
        if (details.pointerCount > 1) {
          // baseAngle = angle;
          // baseScaleFactor = scaleFactor;
          // widget.onRotate?.call(baseAngle);
          // widget.onScale?.call(baseScaleFactor);
        }
      },
      onScaleUpdate: (details) {
        switch (widget.mode) {
          case _PositionMode.global:
            final dx = details.focalPoint.dx - initPoint.dx;
            final dy = details.focalPoint.dy - initPoint.dy;
            // if (dx.abs() > 1.0 || dy.abs() > 1.0) {
            initPoint = details.focalPoint;
            widget.onDrag?.call(Offset(dx, dy));
            _logger.log("onScaleUpdate global dx=$dx dy=$dy");
            // }
            break;
          case _PositionMode.local:
            final dx = details.localFocalPoint.dx - initPoint.dx;
            final dy = details.localFocalPoint.dy - initPoint.dy;
            // if (dx.abs() > 1.0 || dy.abs() > 1.0) {
            initPoint = details.localFocalPoint;
            widget.onDrag?.call(Offset(dx, dy));
            _logger.log("onScaleUpdate local dx=$dx dy=$dy");
            // }
            break;
        }
        if (details.pointerCount > 1) {
          // scaleFactor = baseScaleFactor * details.scale;
          // // widget.onScale?.call(scaleFactor);
          // angle = baseAngle + details.rotation;
          // widget.onRotate?.call(angle);
        }
      },
      onScaleEnd: (details) {
        if (widget.mode == _PositionMode.global || widget.mode == _PositionMode.local) {
          widget.onDragEnd?.call();
        }
      },
      child: widget.child,
    );
  }
}

class BingingElementBGCustomPainter extends CustomPainter {
  BingingElementBGCustomPainter(this.angle, this.isWidthHeightChanged);

  final double lineWidth = 1;
  final double gapPadding = 4;
  late double angle = 0;
  late int isWidthHeightChanged = 0;

  @override
  void paint(Canvas canvas, Size size) {
    //画背景
    var paint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = lineWidth
      ..color = Color(0xFF444444).withOpacity(0.15)
      ..invertColors = false;
    double caliCount = (math.max(size.height, size.width) * 2) / gapPadding;
    for (int i = 0; i < caliCount; i++) {
      double offset = (lineWidth + gapPadding) * (i + 1.0);
      if ((angle / 90) % 2 == 1) {
        double x1 = offset - (isWidthHeightChanged == 0 ? size.width : size.height);
        double x2 = x1 + size.height;
        canvas.drawLine(Offset(x1, 0), Offset(x2, size.height), paint);
      } else {
        double x1 = (lineWidth + gapPadding) * (i + 1.0);
        double x2 = x1 - size.height;
        canvas.drawLine(Offset(x1, 0), Offset(x2, size.height), paint);
      }
    }
  }

  // double getLastPointHeight(double x,double width){
  //    double xx = width-x;
  //    return xx*height/10 > height ? height : xx*height/10;
  // }

  @override
  bool shouldRepaint(BingingElementBGCustomPainter oldDelegate) {
    return true;
  }
}
