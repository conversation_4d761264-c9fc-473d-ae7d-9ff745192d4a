//
//  NiimbotJsApi.m
//  Runner
//
//  Created by jc on 2025/8/4.
// 此函数定义的js调用的原生的函数
// 定义函数时函数一定要有参数
// 异步函数一定要有completionHandler,都则就是同步函数

#import "NiimbotJsApi.h"
#import "dsbridge.h"
#import "NiimbotJsApi+CallJs.h"
#import "WebTestPageViewController.h"


@implementation NiimbotJsApi{
  JCShopBaseWebViewController *_vc;
}

- (instancetype)initWithWeb:(JCShopBaseWebViewController *)viewController {
  if (self) {
    _vc = viewController;
  }
  return self;
}

- (DWKWebView *)webView {
  return self.viewController.webView;
}

- (JCShopBaseWebViewController *)viewController {
  return _vc;
}


///此处只是一个异步调用的示例
- (void)backApp:(NSDictionary *)args :(JSCallback)completionHandler {
    completionHandler(@"[asyn call]",YES);
}


/// 底部tab显示/隐藏(同步)
/// boolean showShopTab: 商城页面是否显示App底部的tab,default is true
- (NSDictionary *)setBottomTabStatus:(NSDictionary *)args {
//  [self.viewController.view setFrame:UIScreen.mainScreen.bounds];
  BOOL showShopTab = [args[@"showShopTab"] boolValue];
  if(!self.viewController.isHomePageShop){//不是首页web,不响应
    return @{
      @"code":@0,@"msg":@"当前页面不是首页商城页面"
    };
  }
  UIViewController *rootVC = ((AppDelegate *)[UIApplication sharedApplication].delegate).mainVC;
  if([rootVC isKindOfClass:[UITabBarController class]]){
    UITabBarController *root = (UITabBarController*)rootVC;
    UINavigationController *naviVC = [root.viewControllers safeObjectAtIndex:root.selectedIndex];
    if(![naviVC.visibleViewController isKindOfClass:[self.viewController class]]){//当前选中的tab不是商城tab,也不响应
      return @{
        @"code":@0,@"msg":@"当前选中的tab不是商城tab"
      };
    }
  }
  self.viewController.tabBarController.tabBar.hidden = !showShopTab;
  [self.viewController.view setNeedsLayout];
  [self.viewController.view layoutIfNeeded];

  return @{
    @"code":@1
  };
}


#pragma mark - for Test
- (void)test:(NSDictionary *)args {
  WebTestPageViewController *testVC = [[WebTestPageViewController alloc] init];
  testVC.webView = self.webView;
  [self.viewController.navigationController pushViewController:testVC animated:true];
}

- (void)test:(NSDictionary *)args :(JSCallback)completionHandler{
  WebTestPageViewController *testVC = [[WebTestPageViewController alloc] init];
  testVC.webView = self.webView;
  [self.viewController.navigationController pushViewController:testVC animated:true];
}
#pragma mark - for Test end

@end
