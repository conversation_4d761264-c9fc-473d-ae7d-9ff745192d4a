package com.niimbot.baselibrary.log

import com.niimbot.fastjson.JSON
import com.aliyun.sls.android.producer.LogProducerCallback
import com.aliyun.sls.android.producer.LogProducerClient
import com.aliyun.sls.android.producer.LogProducerConfig
import com.aliyun.sls.android.producer.LogProducerException
import com.aliyun.sls.android.producer.LogProducerResult
import com.blankj.utilcode.util.LogUtils
import com.niimbot.baselibrary.core.SLSAuthentication
import com.niimbot.baselibrary.core.SlsAuthCacheManager
import com.niimbot.baselibrary.network.JCHttpConfig
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.niimbot.utiliylibray.util.SuperUtils
import java.io.File

/**
 * @ClassName: ALiSLSUtils
 * @Author: Liuxiaowen
 * @Date: 2022/2/17 14:56
 * @Description: 阿里sls日志服务类
 */
object ALiSLSUtils {
    //获取日志服务sls授权token，有效期为半个小时
    private val GET_SLS_LOG_TOKEN = JCHttpConfig.ROOT_URL + "/sts/slsToken/print/app/log"
    //获取打印内容sls授权token，有效期为半个小时
    private val GET_SLS_PRINT_CONTENT_TOKEN = JCHttpConfig.ROOT_URL + "/sts/slsToken/print/bison/content"

    private var slsAuthCacheManager: SlsAuthCacheManager = SlsAuthCacheManager.getInstance()

    fun getSLSClient(project: String, logStore: String, topic: String, isLog: Boolean = true, callback: LogProducerCallback? = null): LogProducerClient? {
        var client:LogProducerClient? = null
        var url = JCHttpConfig.ROOT_URL + "/sts/ststoken"
        postSLSAuthentication(url)?.apply {
            try {
                var endpointTmp = endpoint
                if (!endpointTmp.startsWith("https://")) {
                    endpointTmp = "https://$endpoint"
                }
                val config = LogProducerConfig(SuperUtils.superContext, endpointTmp, project, logStore, accessKeyId, accessKeySecret, securityToken)
                if (!isLog) {
                    config.setTopic(topic)
                    // 被缓存日志的发送超时时间，如果缓存超时，则会被立即发送，单位为毫秒，默认为3000
                    config.setPacketTimeout(300000)
                    // 1 开启断点续传功能， 0 关闭
                    // 每次发送前会把日志保存到本地的binlog文件，只有发送成功才会删除，保证日志上传At Least Once
                    config.setPersistent(1)
                    // 持久化的文件名，需要保证文件所在的文件夹已创建。
                    // 配置多个客户端时，不应设置相同文件
                    config.setPersistentFilePath("${SuperUtils.superContext.filesDir}${String.format("%slog_data.dat", File.separator)}")
//                        // 是否每次AddLog强制刷新，高可靠性场景建议打开
//                        config.setPersistentForceFlush(0);
                    // 持久化文件滚动个数，建议设置成10。
                    config.setPersistentMaxFileCount(20);
                    // 每个持久化文件的大小，建议设置成1-10M
                    config.setPersistentMaxFileSize(2*1024 * 1024);
                    // 本地最多缓存的日志数，不建议超过1M，通常设置为65536即可
                    config.setPersistentMaxLogCount(65536);
                }
                client = LogProducerClient(config){ resultCode, reqId, errorMessage, logBytes, compressedBytes ->
                    run {
                        val result: LogProducerResult = LogProducerResult.fromInt(resultCode)
                        if (LogProducerResult.LOG_PRODUCER_SEND_UNAUTHORIZED == result || LogProducerResult.LOG_PRODUCER_PARAMETERS_INVALID == result) {
                            slsAuthCacheManager.clearToken(url)
                        }
                        callback?.onCall(resultCode, reqId, errorMessage, logBytes, compressedBytes)
                    }
                }
            } catch (e: LogProducerException) {
                e.printStackTrace()
            }
        }
        return client
    }

    /**
     * 获取打印历史用的slsClient
     */
    fun getSLSClientForPrintHistory(
        slsTemplateToken: SLSAuthentication,
        topic: String,
        isLog: Boolean = true,
        callback: LogProducerCallback? = null
    ): LogProducerClient? {
        val config = LogProducerConfig(
            SuperUtils.superContext,
            "https://${slsTemplateToken?.endpoint}",
            slsTemplateToken?.project,
            slsTemplateToken?.logStore,
            slsTemplateToken?.accessKeyId,
            slsTemplateToken?.accessKeySecret,
            slsTemplateToken?.securityToken
        )
        config.setTopic(topic)
        config.setPacketLogBytes(1024 * 1024 * 2)
        config.setPacketLogCount(1024)
        // 被缓存日志的发送超时时间，如果缓存超时，则会被立即发送，单位为毫秒，默认为3000
        config.setPacketTimeout(5000)
        config.setMaxBufferLimit(64 * 1024 * 1024)
        config.setSendThreadCount(1)
        // 1 开启断点续传功能， 0 关闭
        // 每次发送前会把日志保存到本地的binlog文件，只有发送成功才会删除，保证日志上传At Least Once
        config.setPersistent(1)
        // 持久化的文件名，需要保证文件所在的文件夹已创建。
        // 配置多个客户端时，不应设置相同文件
        config.setPersistentFilePath(
            "${SuperUtils.superContext.filesDir}${
                String.format(
                    "%slog_data1.dat",
                    File.separator
                )
            }"
        )
        config.setPersistentForceFlush(1);
        // 持久化文件滚动个数，建议设置成10。
        config.setPersistentMaxFileCount(20);
        // 每个持久化文件的大小，建议设置成1-10M
        config.setPersistentMaxFileSize(2 * 1024 * 1024);
        // 本地最多缓存的日志数，不建议超过1M，通常设置为65536即可
        config.setPersistentMaxLogCount(65536);
        return LogProducerClient(config, callback)
    }
    fun getSLSClient(isLog: Boolean = true, cacheTag: String = "tag", callback: LogProducerCallback? = null): LogProducerClient? {
        var client:LogProducerClient? = null
        var url = if (isLog) GET_SLS_LOG_TOKEN else GET_SLS_PRINT_CONTENT_TOKEN
        getSLSAuthentication(url)?.apply {
            try {
                val config = LogProducerConfig(SuperUtils.superContext, "https://$endpoint", project, logStore, accessKeyId, accessKeySecret, securityToken)
                if (!isLog) {
                    config.setTopic("printRecords")
                    // 被缓存日志的发送超时时间，如果缓存超时，则会被立即发送，单位为毫秒，默认为3000
                    config.setPacketTimeout(3000)
                    // 1 开启断点续传功能， 0 关闭
                    // 每次发送前会把日志保存到本地的binlog文件，只有发送成功才会删除，保证日志上传At Least Once
                    config.setPersistent(1)
                    // 持久化的文件名，需要保证文件所在的文件夹已创建。
                    // 配置多个客户端时，不应设置相同文件
                    config.setPersistentFilePath("${SuperUtils.superContext.filesDir}${String.format("%slog_data_$cacheTag.dat", File.separator)}")
                    // 是否每次AddLog强制刷新，高可靠性场景建议打开
                    config.setPersistentForceFlush(1);
                    // 持久化文件滚动个数，建议设置成10。
                    config.setPersistentMaxFileCount(10);
                    // 每个持久化文件的大小，建议设置成1-10M
                    config.setPersistentMaxFileSize(4*1024 * 1024);
                    // 本地最多缓存的日志数，不建议超过1M，通常设置为65536即可
                    config.setPersistentMaxLogCount(65536)
                }
                client = LogProducerClient(config
                ) { resultCode, reqId, errorMessage, logBytes, compressedBytes ->
                    run {
                        val result: LogProducerResult = LogProducerResult.fromInt(resultCode)
                        if (LogProducerResult.LOG_PRODUCER_SEND_UNAUTHORIZED == result || LogProducerResult.LOG_PRODUCER_PARAMETERS_INVALID == result) {
                            slsAuthCacheManager.clearToken(url)
                        }
                        callback?.onCall(resultCode, reqId, errorMessage, logBytes, compressedBytes)
                    }
                }
            } catch (e: LogProducerException) {
                e.printStackTrace()
            }
        }
        return client
    }

    fun resetClient() {
    }

    private fun postSLSAuthentication(tokenUrl: String): SLSAuthentication?{
        return try {
            var cacheToken = slsAuthCacheManager.getSlsAuthentication(tokenUrl)
            if (null != cacheToken ) {
                cacheToken
            } else {
                val responseBody = DokitOkGo.post<String>(tokenUrl)
                    .headers("need_encrypt", "1")
                    .execute().body?.string()
                if(responseBody.isNullOrEmpty()) return null
                var result = JSON.parseObject(JSON.parseObject(responseBody).getString("data"), SLSAuthentication::class.java)
                result?.let {
                    slsAuthCacheManager.setToken(tokenUrl,it)
                }
                result
            }
        } catch (e: Exception) {
            LogUtils.e("getSLSAuthentication failed")
            e.printStackTrace()
            null
        }
    }
    /**
     * 获取SLS授权token
     * @return SLSAuthentication?
     */
    private fun getSLSAuthentication(tokenUrl: String): SLSAuthentication?{
        return try {
            var cacheToken = slsAuthCacheManager.getSlsAuthentication(tokenUrl)
            if (cacheToken != null) {
                cacheToken
            } else {
                val responseBody = DokitOkGo.get<String>(tokenUrl)
                    .headers("need_encrypt", "1")
                    .execute().body?.string()
                if(responseBody.isNullOrEmpty()) return null
                var result = JSON.parseObject(
                    JSON.parseObject(responseBody).getString("data"), SLSAuthentication::class.java)
                result?.let {
                    SlsAuthCacheManager.getInstance().setToken(tokenUrl,result)
                }
                result
            }
        } catch (e: Exception) {
            LogUtils.e("getSLSAuthentication failed")
            e.printStackTrace()
            null
        }
    }

}
