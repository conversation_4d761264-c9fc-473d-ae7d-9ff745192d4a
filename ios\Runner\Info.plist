<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<false/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>NIIMBOT</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeIconFiles</key>
			<array>
				<string><EMAIL></string>
				<string>96.png</string>
			</array>
			<key>CFBundleTypeName</key>
			<string>com.suofang.jcbqdy</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.data</string>
				<string>public.text</string>
				<string>public.movie</string>
				<string>public.audio</string>
				<string>public.archive</string>
				<string>public.composite-content</string>
				<string>public.content</string>
				<string>public.image</string>
				<string>com.microsoft.excel.xls</string>
				<string>com.adobe.pdf</string>
				<string>com.microsoft.word.doc</string>
				<string>public.item</string>
				<string>com.microsoft.powerpoint.ppt</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Niimbot</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wxece69872671e1254</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>tencent</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>tencent101553333</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wb3921700954</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array/>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>dingtalk</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dingu9dus74ansatrfsl</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.suofang.jcbqdy</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>JCYDY</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.suofang.jcbqdy</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>shop.jc-test.cn</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>shop.jc-dev.cn</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>line</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>lineeafeaf</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>whatsapp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>whatsapp3213123213</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>shop.jc-saas.com</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>twitter</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>twitterkit-fB5tvRpna1CKK97xZUslbxiet</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.1044601849425-098o1e9dlgndms34vjdd5uaup7algda2</string>
				<string>fb286935759758816</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>line</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>line3rdp.$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.suofang.jcbqdy</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>NIIMBOT</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>qq</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mqqopensdknopasteboard</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>286935759758816</string>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<true/>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>云打印</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>NIIM</string>
		<string>jcentersellsave</string>
		<string>fixedAssets</string>
		<string>dingtalk-sso</string>
		<string>dingtalk-open</string>
		<string>mqqapi</string>
		<string>mqq</string>
		<string>weixin</string>
		<string>wechat</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
		<string>whatsapp</string>
		<string>dingtalk</string>
		<string>mqqOpensdkSSoLogin</string>
		<string>mqqbrowser</string>
		<string>mqqopensdklaunchminiapp</string>
		<string>mqqthirdappgroup</string>
		<string>tencentapi.qzone.reqContent</string>
		<string>mqqopensdknopasteboard</string>
		<string>tencentapi.qq.reqContent</string>
		<string>mqqopensdkgrouptribeshare</string>
		<string>mqqgamebindinggroup</string>
		<string>mqqopensdkdataline</string>
		<string>mqqopensdkminiapp</string>
		<string>mqqopensdkavatar</string>
		<string>mqqopensdkfriend</string>
		<string>mqqapiwallet</string>
		<string>mqzoneopensdkapiV2</string>
		<string>mqzoneopensdkapi19</string>
		<string>mqzoneopensdkapi</string>
		<string>mqzoneopensdk</string>
		<string>mqzone</string>
		<string>mqqopensdkapiV4</string>
		<string>mqqopensdkapiV2</string>
		<string>tim</string>
		<string>fbshareextension</string>
		<string>fbauth2</string>
		<string>fbauth</string>
		<string>fbapi</string>
		<string>fb</string>
		<string>com.amazon.mobile.shopping</string>
		<string>alipay</string>
		<string>alipayshare</string>
		<string>alipays</string>
		<string>linkedin</string>
		<string>line</string>
		<string>lineauth2</string>
		<string>mqqopensdknopasteboard</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>Location Always Usage Description</key>
	<string>此应用在打印机Wi-Fi配置的服务中会使用您的定位权限，是否允许</string>
	<key>Location When In Use Usage Description</key>
	<string>此应用在打印机Wi-Fi配置的服务中会使用您的定位权限，是否允许</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>此应用会在连接打印机的服务中访问您的蓝牙权限，是否允许打开蓝牙？</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>此应用会在连接打印机的服务中访问您的蓝牙权限，是否允许打开蓝牙？</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>NSCameraUsageDescription</key>
	<string>此应用会在扫描二维码的服务中访问您的相机权限，是否允许打开相机？</string>
	<key>NSContactsUsageDescription</key>
	<string>此应用会在读取联系人的服务中访问您的通讯录权限，是否允许打开通讯录？</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>此应用在搜索局域网设备、配网的服务中需要访问本地局域网 (LAN)​。</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>此应用在打印机Wi-Fi配置的服务中会使用您的定位权限，是否允许</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>此应用会在使用打印服务中访问您的位置权限，是否允许打开定位？</string>
	<key>NSLocationUsageDescription</key>
	<string>此应用会在使用打印服务中访问您的位置权限，是否允许打开定位？</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>此应用会在使用打印服务中访问您的位置权限，是否允许打开定位？</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>此应用会在语音识别的服务中访问您的麦克风权限，是否允许打开麦克风？</string>
	<key>NSMotionUsageDescription</key>
	<string>App需要您的同意,才能访问运动与健身</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>此应用会在上传图片的服务中访问您的相册权限，是否允许打开相册？</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>此应用会在上传图片的服务中访问您的相册权限，是否允许打开相册？</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>此应用需要您的同意,才能访问语音识别，是否允许打开语音识别？</string>
	<key>PermissionGroupNotification</key>
	<string>开启通知</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>bluetooth-central</string>
		<string>bluetooth-peripheral</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportsDocumentBrowser</key>
	<true/>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
	<key>UIViewEdgeAntialiasing</key>
	<false/>
	<key>User Interface Style</key>
	<string>Light</string>
	<key>amap</key>
	<dict>
		<key>appkey</key>
		<string>908ad8dcd7d2d08e533848d94c4aac7c</string>
	</dict>
</dict>
</plist>
