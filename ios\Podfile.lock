PODS:
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AliyunLogProducer (3.1.11):
    - AliyunLogProducer/Producer (= 3.1.11)
  - AliyunLogProducer/Producer (3.1.11)
  - AliyunOSSiOS (2.10.22)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - Bugly (2.6.1)
  - camera_avfoundation (0.0.1):
    - Flutter
  - Capacitor (3.9.0):
    - CapacitorCordova
  - CapacitorActionSheet (1.0.8):
    - Capacitor
  - CapacitorApp (1.1.1):
    - Capacitor
  - CapacitorAppLauncher (1.0.9):
    - Capacitor
  - CapacitorBrowser (1.0.7):
    - Capacitor
  - CapacitorCamera (1.3.1):
    - Capacitor
  - CapacitorClipboard (1.0.8):
    - Capacitor
  - CapacitorCordova (3.9.0)
  - CapacitorDevice (1.1.2):
    - Capacitor
  - CapacitorDialog (1.0.7):
    - Capacitor
  - CapacitorFilesystem (1.1.0):
    - Capacitor
  - CapacitorGeolocation (1.3.1):
    - Capacitor
  - CapacitorHaptics (1.1.4):
    - Capacitor
  - CapacitorKeyboard (1.2.3):
    - Capacitor
  - CapacitorLocalNotifications (1.1.0):
    - Capacitor
  - CapacitorNetwork (1.0.7):
    - Capacitor
    - ReachabilitySwift (~> 5.0)
  - CapacitorPushNotifications (1.0.9):
    - Capacitor
  - CapacitorScreenReader (1.0.8):
    - Capacitor
  - CapacitorShare (1.1.2):
    - Capacitor
  - CapacitorSplashScreen (1.2.2):
    - Capacitor
  - CapacitorStatusBar (1.0.8):
    - Capacitor
  - CapacitorTextZoom (1.0.8):
    - Capacitor
  - CapacitorToast (1.0.8):
    - Capacitor
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - ETag-iOS (1.0.0-beta.6)
  - FBAEMKit (16.0.1):
    - FBSDKCoreKit_Basics (= 16.0.1)
  - FBSDKCoreKit (16.0.1):
    - FBAEMKit (= 16.0.1)
    - FBSDKCoreKit_Basics (= 16.0.1)
  - FBSDKCoreKit_Basics (16.0.1)
  - FBSDKLoginKit (16.0.1):
    - FBSDKCoreKit (= 16.0.1)
  - Flutter (1.0.0)
  - flutter_boost (0.0.2):
    - Flutter
  - flutter_facebook_auth (5.0.9):
    - FBSDKLoginKit (~> 16.0.1)
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_line_sdk (2.3.4):
    - Flutter
    - LineSDKSwift (~> 5.3)
  - flutter_native_image (0.0.1):
    - Flutter
  - flutter_net_signature (0.0.1):
    - Flutter
    - YTKNetwork (~> 3.0.6)
  - flutter_secure_storage (6.0.0):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - GCDObjC (0.3.0)
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - HMSegmentedControl (1.5.6)
  - HoundAnalyticsSDK (0.1.8)
  - image_editor_common (1.0.0):
    - Flutter
  - image_gallery_saver (2.0.2):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - IQKeyboardManager (6.5.12)
  - isar_flutter_libs (1.0.0):
    - Flutter
  - JailBrokenDetection (0.1.0)
  - JCAPI-iOS (4.0.1-beta.6)
  - JCore (4.8.0)
  - JPush (5.4.0):
    - JCore (>= 2.0.0)
  - JSONModel (1.8.0)
  - layout_forge (0.0.1):
    - Flutter
  - LBXScan (2.5.1):
    - LBXScan/All (= 2.5.1)
  - LBXScan/All (2.5.1):
    - LBXScan/Types (~> 2.2)
  - LBXScan/Types (2.5.1)
  - LBXZBarSDK (1.3.5)
  - lego-iOS (3.0.0-dev.8)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - LineSDKSwift (5.10.0):
    - LineSDKSwift/Core (= 5.10.0)
  - LineSDKSwift/Core (5.10.0)
  - LookinServer (1.2.8):
    - LookinServer/Core (= 1.2.8)
  - LookinServer/Core (1.2.8)
  - lottie-ios (4.4.1)
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - Masonry (1.1.0)
  - MBProgressHUD (1.2.0)
  - MJRefresh (3.7.9)
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.1.1):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - MyLayout (1.9.10)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - native_flutter_proxy (0.0.1):
    - Flutter
  - netal_plugin (0.0.3):
    - Flutter
    - SkiaRenderLibrary-iOS
  - nety (0.0.1):
    - Flutter
    - JCAPI-iOS
    - YYModel (= 1.0.4)
  - niimbot_cache_manager (0.0.1):
    - Flutter
  - niimbot_excel (1.0.1):
    - Flutter
  - niimbot_lego (1.3.6):
    - Flutter
  - niimbot_log_plugin (0.0.1):
    - Flutter
  - niimbot_print_setting_plugin (0.0.1):
    - Flutter
  - niimbot_sls_flutter (1.1.2):
    - AliyunLogProducer (= 3.1.11)
    - Flutter
  - nimbot_state_manager (0.0.1):
    - Flutter
  - OneKeyLogin (1.1.14)
  - OrderedSet (6.0.3)
  - package_info (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - pdfx (1.0.0):
    - Flutter
  - permission_handler_apple (9.1.1):
    - Flutter
  - PGDatePicker (2.6.9):
    - PGPickerView
  - PGPickerView (1.3.8)
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - pop (1.0.12)
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - ReactiveCocoa (2.5):
    - ReactiveCocoa/UI (= 2.5)
  - ReactiveCocoa/Core (2.5):
    - ReactiveCocoa/no-arc
  - ReactiveCocoa/no-arc (2.5)
  - ReactiveCocoa/UI (2.5):
    - ReactiveCocoa/Core
  - RKNotificationHub (2.0.5)
  - SDWebImage (5.12.6):
    - SDWebImage/Core (= 5.12.6)
  - SDWebImage/Core (5.12.6)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - sensors_plus (0.0.1):
    - Flutter
  - Sentry/HybridSDK (8.46.0)
  - sentry_flutter (8.14.1):
    - Flutter
    - FlutterMacOS
    - Sentry/HybridSDK (= 8.46.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - SkiaRenderLibrary-iOS (1.9.3-dev.7)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SRCountdownTimer (1.3)
  - SSZipArchive (2.4.3)
  - TABAnimated (2.5.1)
  - tencent_kit (6.2.0):
    - Flutter
    - tencent_kit/vendor (= 6.2.0)
  - tencent_kit/vendor (6.2.0):
    - Flutter
  - TMCache (2.1.0)
  - twitter_login (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - Version (0.8.0)
  - vibration (1.7.5):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - WechatOpenSDK-XCFramework (2.0.4)
  - YKWoodpecker (1.2.9)
  - YTKNetwork (3.0.6):
    - AFNetworking/NSURLSession (~> 4.0)
  - YYCache (1.0.4)
  - YYImage (1.0.4):
    - YYImage/Core (= 1.0.4)
  - YYImage/Core (1.0.4)
  - YYModel (1.0.4)
  - YYWebImage (1.0.5):
    - YYCache
    - YYImage
  - ZLPhotoBrowser (1.0):
    - SDWebImage
  - ZXCountDownView (1.0.6)

DEPENDENCIES:
  - AliyunLogProducer (= 3.1.11)
  - AliyunOSSiOS (~> 2.10.14)
  - Bugly (~> 2.6.0)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - "Capacitor (from `../@capacitor/ios`)"
  - "CapacitorActionSheet (from `../@capacitor/action-sheet`)"
  - "CapacitorApp (from `../@capacitor/app`)"
  - "CapacitorAppLauncher (from `../@capacitor/app-launcher`)"
  - "CapacitorBrowser (from `../@capacitor/browser`)"
  - "CapacitorCamera (from `../@capacitor/camera`)"
  - "CapacitorClipboard (from `../@capacitor/clipboard`)"
  - "CapacitorCordova (from `../@capacitor/ios`)"
  - "CapacitorDevice (from `../@capacitor/device`)"
  - "CapacitorDialog (from `../@capacitor/dialog`)"
  - "CapacitorFilesystem (from `../@capacitor/filesystem`)"
  - "CapacitorGeolocation (from `../@capacitor/geolocation`)"
  - "CapacitorHaptics (from `../@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../@capacitor/keyboard`)"
  - "CapacitorLocalNotifications (from `../@capacitor/local-notifications`)"
  - "CapacitorNetwork (from `../@capacitor/network`)"
  - "CapacitorPushNotifications (from `../@capacitor/push-notifications`)"
  - "CapacitorScreenReader (from `../@capacitor/screen-reader`)"
  - "CapacitorShare (from `../@capacitor/share`)"
  - "CapacitorSplashScreen (from `../@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../@capacitor/status-bar`)"
  - "CapacitorTextZoom (from `../@capacitor/text-zoom`)"
  - "CapacitorToast (from `../@capacitor/toast`)"
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info (from `.symlinks/plugins/device_info/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - ETag-iOS (= 1.0.0-beta.6)
  - Flutter (from `Flutter`)
  - flutter_boost (from `.symlinks/plugins/flutter_boost/ios`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_line_sdk (from `.symlinks/plugins/flutter_line_sdk/ios`)
  - flutter_native_image (from `.symlinks/plugins/flutter_native_image/ios`)
  - flutter_net_signature (from `.symlinks/plugins/flutter_net_signature/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - FMDB
  - GCDObjC
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - HMSegmentedControl (~> 1.5.5)
  - HoundAnalyticsSDK (~> 0.1.8)
  - image_editor_common (from `.symlinks/plugins/image_editor_common/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - IQKeyboardManager (= 6.5.12)
  - isar_flutter_libs (from `.symlinks/plugins/isar_flutter_libs/ios`)
  - "JailBrokenDetection (from `****************:architect/ios/JailBrokenDetection.git`, branch `feature/ios_app_size`)"
  - JCAPI-iOS (= 4.0.1-beta.6)
  - JCore (= 4.8.0)
  - JPush (= 5.4.0)
  - JSONModel
  - layout_forge (from `.symlinks/plugins/layout_forge/ios`)
  - LBXScan (~> 2.5.1)
  - LBXZBarSDK (~> 1.3.5)
  - lego-iOS (= 3.0.0-dev.8)
  - LookinServer
  - lottie-ios
  - Masonry
  - MBProgressHUD
  - MJRefresh
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - MyLayout
  - native_flutter_proxy (from `.symlinks/plugins/native_flutter_proxy/ios`)
  - netal_plugin (from `.symlinks/plugins/netal_plugin/ios`)
  - nety (from `.symlinks/plugins/nety/ios`)
  - niimbot_cache_manager (from `.symlinks/plugins/niimbot_cache_manager/ios`)
  - niimbot_excel (from `.symlinks/plugins/niimbot_excel/ios`)
  - niimbot_lego (from `.symlinks/plugins/niimbot_lego/ios`)
  - niimbot_log_plugin (from `.symlinks/plugins/niimbot_log_plugin/ios`)
  - niimbot_print_setting_plugin (from `.symlinks/plugins/niimbot_print_setting_plugin/ios`)
  - niimbot_sls_flutter (from `.symlinks/plugins/niimbot_sls_flutter/ios`)
  - nimbot_state_manager (from `.symlinks/plugins/nimbot_state_manager/ios`)
  - OneKeyLogin (~> 1.1.14)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - pdfx (from `.symlinks/plugins/pdfx/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - PGDatePicker (>= 1.4.2)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - pop
  - ReactiveCocoa (~> 2.5)
  - RKNotificationHub
  - SDWebImage (~> 5.12.6)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - sentry_flutter (from `.symlinks/plugins/sentry_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - SkiaRenderLibrary-iOS (= 1.9.3-dev.7)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - SRCountdownTimer (from `https://git.jc-ai.cn/architect/ios/countdowntimer.git`)
  - SSZipArchive
  - TABAnimated (= 2.5.1)
  - tencent_kit (from `.symlinks/plugins/tencent_kit/ios`)
  - TMCache
  - twitter_login (from `.symlinks/plugins/twitter_login/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - Version
  - vibration (from `.symlinks/plugins/vibration/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - YKWoodpecker
  - YTKNetwork (~> 3.0.6)
  - YYModel
  - YYWebImage
  - ZLPhotoBrowser (from `https://git.jc-ai.cn/architect/ios/ZLPhotoBrowser.git`)
  - ZXCountDownView (~> 1.0.6)

SPEC REPOS:
  "****************:print/foundation/niimbot_specs.git":
    - ETag-iOS
    - JCAPI-iOS
    - lego-iOS
    - SkiaRenderLibrary-iOS
  https://git.jc-ai.cn/architect/ios/houndanalyticssdkpodspecs:
    - HoundAnalyticsSDK
  https://git.jc-ai.cn/architect/ios/onekeyloginpodspecs:
    - OneKeyLogin
  trunk:
    - AFNetworking
    - AliyunLogProducer
    - AliyunOSSiOS
    - AppAuth
    - Bugly
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - FMDB
    - GCDObjC
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleSignIn
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMAppAuth
    - GTMSessionFetcher
    - HMSegmentedControl
    - IQKeyboardManager
    - JCore
    - JPush
    - JSONModel
    - LBXScan
    - LBXZBarSDK
    - libwebp
    - LineSDKSwift
    - LookinServer
    - lottie-ios
    - Mantle
    - Masonry
    - MBProgressHUD
    - MJRefresh
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - MyLayout
    - nanopb
    - OrderedSet
    - PGDatePicker
    - PGPickerView
    - pop
    - PromisesObjC
    - ReachabilitySwift
    - ReactiveCocoa
    - RKNotificationHub
    - SDWebImage
    - SDWebImageWebPCoder
    - Sentry
    - SSZipArchive
    - TABAnimated
    - TMCache
    - Version
    - WechatOpenSDK-XCFramework
    - YKWoodpecker
    - YTKNetwork
    - YYCache
    - YYImage
    - YYModel
    - YYWebImage
    - ZXCountDownView

EXTERNAL SOURCES:
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  Capacitor:
    :path: "../@capacitor/ios"
  CapacitorActionSheet:
    :path: "../@capacitor/action-sheet"
  CapacitorApp:
    :path: "../@capacitor/app"
  CapacitorAppLauncher:
    :path: "../@capacitor/app-launcher"
  CapacitorBrowser:
    :path: "../@capacitor/browser"
  CapacitorCamera:
    :path: "../@capacitor/camera"
  CapacitorClipboard:
    :path: "../@capacitor/clipboard"
  CapacitorCordova:
    :path: "../@capacitor/ios"
  CapacitorDevice:
    :path: "../@capacitor/device"
  CapacitorDialog:
    :path: "../@capacitor/dialog"
  CapacitorFilesystem:
    :path: "../@capacitor/filesystem"
  CapacitorGeolocation:
    :path: "../@capacitor/geolocation"
  CapacitorHaptics:
    :path: "../@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../@capacitor/keyboard"
  CapacitorLocalNotifications:
    :path: "../@capacitor/local-notifications"
  CapacitorNetwork:
    :path: "../@capacitor/network"
  CapacitorPushNotifications:
    :path: "../@capacitor/push-notifications"
  CapacitorScreenReader:
    :path: "../@capacitor/screen-reader"
  CapacitorShare:
    :path: "../@capacitor/share"
  CapacitorSplashScreen:
    :path: "../@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../@capacitor/status-bar"
  CapacitorTextZoom:
    :path: "../@capacitor/text-zoom"
  CapacitorToast:
    :path: "../@capacitor/toast"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info:
    :path: ".symlinks/plugins/device_info/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_boost:
    :path: ".symlinks/plugins/flutter_boost/ios"
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_line_sdk:
    :path: ".symlinks/plugins/flutter_line_sdk/ios"
  flutter_native_image:
    :path: ".symlinks/plugins/flutter_native_image/ios"
  flutter_net_signature:
    :path: ".symlinks/plugins/flutter_net_signature/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_editor_common:
    :path: ".symlinks/plugins/image_editor_common/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  isar_flutter_libs:
    :path: ".symlinks/plugins/isar_flutter_libs/ios"
  JailBrokenDetection:
    :branch: feature/ios_app_size
    :git: "****************:architect/ios/JailBrokenDetection.git"
  layout_forge:
    :path: ".symlinks/plugins/layout_forge/ios"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  native_flutter_proxy:
    :path: ".symlinks/plugins/native_flutter_proxy/ios"
  netal_plugin:
    :path: ".symlinks/plugins/netal_plugin/ios"
  nety:
    :path: ".symlinks/plugins/nety/ios"
  niimbot_cache_manager:
    :path: ".symlinks/plugins/niimbot_cache_manager/ios"
  niimbot_excel:
    :path: ".symlinks/plugins/niimbot_excel/ios"
  niimbot_lego:
    :path: ".symlinks/plugins/niimbot_lego/ios"
  niimbot_log_plugin:
    :path: ".symlinks/plugins/niimbot_log_plugin/ios"
  niimbot_print_setting_plugin:
    :path: ".symlinks/plugins/niimbot_print_setting_plugin/ios"
  niimbot_sls_flutter:
    :path: ".symlinks/plugins/niimbot_sls_flutter/ios"
  nimbot_state_manager:
    :path: ".symlinks/plugins/nimbot_state_manager/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  pdfx:
    :path: ".symlinks/plugins/pdfx/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  sentry_flutter:
    :path: ".symlinks/plugins/sentry_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  SRCountdownTimer:
    :git: https://git.jc-ai.cn/architect/ios/countdowntimer.git
  tencent_kit:
    :path: ".symlinks/plugins/tencent_kit/ios"
  twitter_login:
    :path: ".symlinks/plugins/twitter_login/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  vibration:
    :path: ".symlinks/plugins/vibration/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"
  ZLPhotoBrowser:
    :git: https://git.jc-ai.cn/architect/ios/ZLPhotoBrowser.git

CHECKOUT OPTIONS:
  JailBrokenDetection:
    :commit: 2740138c830b7c9ae6ffedb9249a3239112e9aa2
    :git: "****************:architect/ios/JailBrokenDetection.git"
  SRCountdownTimer:
    :commit: 10c662db7bcbc7b80b66ec43a498d3ffc52167fa
    :git: https://git.jc-ai.cn/architect/ios/countdowntimer.git
  ZLPhotoBrowser:
    :commit: 346296c7f35f8446d762943131a3f07789bf7269
    :git: https://git.jc-ai.cn/architect/ios/ZLPhotoBrowser.git

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AliyunLogProducer: ae1e8b3e57fdb839252a1c9403bcf5081647b307
  AliyunOSSiOS: b46648fd78909a567e3743fe94183748a407b175
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  Bugly: 217ac2ce5f0f2626d43dbaa4f70764c953a26a31
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  Capacitor: 1bee2352ce202532e9193d99665f124f943d8cd3
  CapacitorActionSheet: d83afb21a2fd049a6816b0641ae5f0fb827f20e7
  CapacitorApp: 26b6807b35b4c810050d962342a92d28e40a8e8a
  CapacitorAppLauncher: e2b56d88ca6f56226f567c36ef195ea174b30207
  CapacitorBrowser: 995a15a12190b00f846cd27344397931d017ea95
  CapacitorCamera: 8816cfd7531b396cd11da0935521dc3ad8545ac0
  CapacitorClipboard: cb14daf2c861cf0140373417f4f5fa7249a818dd
  CapacitorCordova: 3c768ee86bbedd4b99b68551a78ed24128bacb40
  CapacitorDevice: cab1a29403e804efa802e2d99ca6f50153c546cb
  CapacitorDialog: f4e61c7977e26181e4dcbe737c44b5e748869739
  CapacitorFilesystem: 927498f7b93199d39c4b4a75b04a4b504731b10a
  CapacitorGeolocation: 9683f304f11b0ea40d85bd73d22e6f4311edc342
  CapacitorHaptics: 66b1c4305362e06803afb096c193e6e459683e60
  CapacitorKeyboard: ccff9e9fc46cad39dafbd1b6f3f11a952d786a07
  CapacitorLocalNotifications: 4f420b7ac287a2098e911d139ce13121764f9356
  CapacitorNetwork: e72197e5e1b2be06c5657be096e3a449ffedf7a9
  CapacitorPushNotifications: 75db84b62b9ca0b4dc9b053678f69123982e49a9
  CapacitorScreenReader: 3623dfad94fe4f71a15afa1b129df88ef627abd1
  CapacitorShare: be3ec179628ae5cf52a71f985f3a77df23960381
  CapacitorSplashScreen: d1202ea3871577430788d1e7bac6ea5cfce83c96
  CapacitorStatusBar: 84928f0fa92f2829ea7cf1d9b9a5d6ee25c52863
  CapacitorTextZoom: 0bd91feb3b4ea01513c18ce1b24abf71741beefb
  CapacitorToast: b695407b1a623a36da8e1e99341d94910ff4f411
  connectivity_plus: 8443422d4c5a53dee0d50779ec5dbcda1071251e
  device_info: 52e8c0c9c61def8d0a92bf175f5f500abbea04bc
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  ETag-iOS: 741c78a1f3cfb53631ff72e8221d026d68c70885
  FBAEMKit: daac7466b918752f020345be5c7d9787f98cfc07
  FBSDKCoreKit: 2cb033464b2134af0138f87d20b859eb3f9be359
  FBSDKCoreKit_Basics: d37280da2e65872f0e931d15f45d97ea324fec37
  FBSDKLoginKit: c47a3b90920702487b10b647c63520dc2676dad9
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_boost: 35d7aaeeba2a591a34841e776e2f76912efc85f1
  flutter_facebook_auth: 3fca4985580f602bc27e2d94b01da28696878a2f
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  flutter_line_sdk: 7bce7bc6a2fbfb0c4d455a96b026a78405e3c138
  flutter_native_image: c0f200e4bc5da392b15878df38654cc85a78b090
  flutter_net_signature: 401a62f222c6e329c4f1be4f56e0100b45a77d31
  flutter_secure_storage: 2c2ff13db9e0a5647389bff88b0ecac56e3f3418
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  GCDObjC: 0c31898f34d8605196ac0343c65b496609d7dee6
  google_sign_in_ios: 19297361f2c51d7d8ac0201b866ef1fa5d1f94a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HMSegmentedControl: 34c1f54d822d8308e7b24f5d901ec674dfa31352
  HoundAnalyticsSDK: bb7fb29b93fd2115cada15173d48bb8a908524e2
  image_editor_common: 3de87e7c4804f4ae24c8f8a998362b98c105cac1
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  IQKeyboardManager: b1fd66ccf7a79095bdcc4a2735231586f744216e
  isar_flutter_libs: 9fc2cfb928c539e1b76c481ba5d143d556d94920
  JailBrokenDetection: 30b639d79d213c8dded919cceaf96101aa7db880
  JCAPI-iOS: a54c6db9fffb83a4b22852c0d9bc5d524465dbf0
  JCore: 72aeac69a0ba49b9ef6e753cdd2d0f7f18deb15f
  JPush: 9ebff4e71616d3bd886d358e8e0039d5a00d0d92
  JSONModel: 02ab723958366a3fd27da57ea2af2113658762e9
  layout_forge: e98ae6a95443d034c0860ade19d7eeced97afe74
  LBXScan: 90ca10d0c38fb4a5a6980d7782354f3f69f50093
  LBXZBarSDK: db826d1265fbb4fbdbb2f30d7d1e96142e32bfff
  lego-iOS: 5591c354e636172502d6e0575ddc96e7f385c19f
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  LineSDKSwift: af3be3b3eb8b408ff0f0de22c85a45df453cb0b9
  LookinServer: 1b2b61c6402ae29fa22182d48f5cd067b4e99e80
  lottie-ios: e047b1d2e6239b787cc5e9755b988869cf190494
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  Masonry: 678fab65091a9290e40e2832a55e7ab731aad201
  MBProgressHUD: 3ee5efcc380f6a79a7cc9b363dd669c5e1ae7406
  MJRefresh: ff9e531227924c84ce459338414550a05d2aea78
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: ba17a89d6a2d1847dad8cad0335856fd4b4ce1f6
  MyLayout: 39ee012bd780936c973bdc5d8ce0f84e3de489d7
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  native_flutter_proxy: a96434620247e3a0e3ebffde6dae1c31e2745256
  netal_plugin: 2e18b0f2e9251850d342658db7160dbd4d2dea91
  nety: 0d8daabd3a7b626ee0a79b42925408554d2f6337
  niimbot_cache_manager: df1850bf8a77eaccfe9d2f8e806a0c0f093d5086
  niimbot_excel: 24f9eb13eef7093ce2d5cb81f822eaa417208cba
  niimbot_lego: 793b94d6f711710af822a7c048c83462cd05d0b4
  niimbot_log_plugin: 74a4acf41325dadb7b42332aa50748a19b7ede98
  niimbot_print_setting_plugin: 627b86b9b55f90dd3868f98df09c1fe0ac897991
  niimbot_sls_flutter: 853bc4b4b679c0e7d7b6fb4c416561ebf3e1143d
  nimbot_state_manager: 82212d00740501d0480121634667984702ea59f4
  OneKeyLogin: d1f0ffffdb60242a7c03eaeb24547ad88d949966
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info: cce50adca9873c79f931618469d2114b91d71189
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  pdfx: 77f4dddc48361fbb01486fa2bdee4532cbb97ef3
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  PGDatePicker: 88960f3760edac96219c10320f25536ad31d0f44
  PGPickerView: 037acfb132a4c8054fb43aaa59a5ff50c7f747f5
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  pop: d582054913807fd11fd50bfe6a539d91c7e1a55a
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  ReactiveCocoa: e2db045570aa97c695e7aa97c2bcab222ae51f4a
  RKNotificationHub: e926748820d719f21ff374657a47fceefa1796b4
  SDWebImage: a47aea9e3d8816015db4e523daff50cfd294499d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  Sentry: da60d980b197a46db0b35ea12cb8f39af48d8854
  sentry_flutter: 942017adbe00f963061cb11ec260414a990b7a42
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  SkiaRenderLibrary-iOS: ba9f860512de1ba42146e3aa5e390fbdcdf00274
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SRCountdownTimer: 44be4ff9cab3f71147b9eca72ad6601945b58aa0
  SSZipArchive: fe6a26b2a54d5a0890f2567b5cc6de5caa600aef
  TABAnimated: f0bfba5c5c7d279b43cda503d9a5328e7e9891c8
  tencent_kit: 6c75fb074b891a9151d53d0f3d335a3033d5d674
  TMCache: 95ebcc9b3c7e90fb5fd8fc3036cba3aa781c9bed
  twitter_login: ea7069c8a3f4d3d786fae20495352c5e99926f03
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  Version: de5907f2c5d0f3cf21708db7801d1d5401139486
  vibration: 8e2f50fc35bb736f9eecb7dd9f7047fbb6a6e888
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 6e6160e04b1e85872253adc5322afe416d9cdddc
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f
  YKWoodpecker: 5433b65498b2afe2a4adc20642f76ff57e161f2a
  YTKNetwork: c16be90b06be003de9e9cd0d3b187cc8eaf35c04
  YYCache: 8105b6638f5e849296c71f331ff83891a4942952
  YYImage: 1e1b62a9997399593e4b9c4ecfbbabbf1d3f3b54
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  YYWebImage: 5f7f36aee2ae293f016d418c7d6ba05c4863e928
  ZLPhotoBrowser: 2dd0ad90e4c4777fa17870221b6ee5c5fb6139f2
  ZXCountDownView: 486d888558551210c6790855ec24e82853a31d9e

PODFILE CHECKSUM: 6d2f3867dc064ad821796e13a0babbd1e824078a

COCOAPODS: 1.16.2
