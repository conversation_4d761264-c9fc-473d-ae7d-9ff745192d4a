import 'package:isar/isar.dart';

part 'print_data_log_model.g.dart'; // ✅ 你必须加上这一行！

@collection
class PrintDataLogModel {
  Id id = Isar.autoIncrement;
  String? addTime;
  String? allTimes;

  //app版本号
  String? applicationVersion;
  String? city;
  String? country;
  String? district;

  //固件版本号
  String? firmwareVersion;

  String? hardwareVersion;

  //硬件版本号
  bool? isCloudTemplate;
  String? latitude;
  String? longitude;
  String? macNo;
  String? machineId;
  bool? machineStatus;
  String? printedProcess;
  String? number;
  String? oneCode;
  String? phoneBrand;

  //打印方式：1-蓝牙，2-局域网，3-云端，4-数据线
  String? printStyle;
  String? province;
  int? recordSource;

  //打印记录类别：0-普通，1-RFID
  int? recordType;
  String? rfidPrintNumber;
  String? rfidSerialNumber;
  double? ribbonUsed;
  String? street;
  String? successTimes;
  String? systemType;
  String? systemVersion;
  String? templeteId;
  String? cloudTemplateId;
  String? sourceId;
  String? width;
  String? height;

  //打印完成时间（毫秒时间戳）
  int? printFinishTime;

  //打印类型：1-批量打印 （包含excel、商品的多张打印），0-普通打印
  int? printType;

  //包含商品个数
  int? commodityCount;
  String? uniqueValue;
  int? userId;
  String? deviceId;
  int? deviceRegionCode;
  String? printChannel;

  //本次打印纸张打印长度cm
  String? paperLengthUsedQuantity;

  //rfid纸张使用总长度cm
  String? paperLengthUsedQuantitySum;
  String? device_id_dot;
  String? probationPrivilege;

  //状态：0-默认，1-离线存储，2-网络请求失败存储，3-正在上传
  int? status;
  String? printStrategy;
  String? illegalCode;

  /* 新增业务额外字段 内容 json string 二期待做需要小程序传参
        会议小程序 ：print_type 1-名牌  2-签到
        二期小程序 ：print_type 1-打印记录 2-物品列表  user_type 1-店长 2-员工 3-企业管理员
   */
  String? extraData;
  /// 是否是离线打印
  bool? offlinePrint;
}
