name: niimbot_flutter_canvas
description: Flutter <PERSON> By Niimbot.
version: 0.0.6
author:
homepage: https://git.jc-ai.cn/print/foundation/niimbot_flutter_canvas

environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.19.0"

enable-null-safety: true

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.9
  provider: ^6.0.3
  flutter_keyboard_visibility: ^6.0.0
  flutter_inappwebview: ^6.0.0
#  flutter_inappwebview:
#    git:
#      url: 'https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_inappwebview.git'
#      path: 'flutter_inappwebview/'
#      ref: 'xcode_16_fix'
  webview_flutter: 3.0.4
  webview_flutter_android: 2.8.8
  keyboard_actions: 4.1.0
  flutter_screenutil: 5.9.3
  # 保存图片到相册
  image_gallery_saver: '^2.0.3'
  # 相册选择
  image_picker: ^1.0.7
  image_picker_android: ^0.8.7
  dio: ^5.0.0
  wechat_camera_picker: ^4.2.1

  # 相机
  camera: ^0.10.5+2
  # 图片裁剪、压缩
  flutter_native_image: ^0.0.6+1
  # 图片处理、翻转、解码
  image: ^4.0.17
  intl: ^0.19.0
  dotted_border: ^2.0.0+2
  internet_file: ^1.2.0
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.0
  permission_handler: 10.4.3
  music_visualizer: ^1.0.2
  auto_size_text: ^3.0.0
  # 扫码
  mobile_scanner: 5.1.1
  #popup
  custom_pop_up_menu: ^1.2.4
  shared_preferences: ^2.0.15
  flutter_easyloading: ^3.0.3
  path_provider: ^2.0.6
  pull_to_refresh: ^2.0.0
  flutter_staggered_grid_view: ^0.6.2
  flutter_swipe_action_cell: ^3.1.0
  connectivity_plus: ^3.0.6
  tuple: ^2.0.2
  device_info_plus: ^10.1.0
  video_player: ^2.5.0
  video_player_android: 2.3.11
  chewie: 1.8.5
  isolate: ^2.1.1
  get: 4.6.6
  pdfx: 2.8.3
  sliding_up_panel: ^2.0.0+1
  flutter_image_compress: 2.1.0
  flutter_image_compress_common: 1.0.3
  flutter_image_compress_platform_interface: 1.0.3
  flutter_easyrefresh: ^2.2.1
  fluttertoast: ^8.2.5
  nimbot_state_manager:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/nimbot_state_manager.git'
      ref: 'feature/flutter_update'
  # Popover弹出
  # https://github.com/minikin/popover
  popover:
    git:
      url: 'https://git.jc-ai.cn/print/foundation/popover.git'
      ref: 'feature/0.2.9'

  # 外层主项目如果是被Flutter_Boost管理，内部的页面生命周期AppLifecycle不会触发，可参考
  # https://github.com/alibaba/flutter_boost/issues/1626
  #flutter_boost: ^3.0.0
  #netal_plugin: ^1.1.2
  flutter_canvas_plugins_interface:
    path: '../flutter_canvas_plugins_interface'
#    git:
#      url: https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_canvas_plugins_interface.git
#      ref: 'master'
  drawboard_dart_sdk:
    git:
      url: https://git.jc-ai.cn/architect/kalimdor/drawboard-dart-sdk.git
      ref: 'feature/flutter_update_new'
#    path: '../drawboard-dart-sdk'
  niimbot_excel: 1.1.15
#  niimbot_excel:
#    git:
#      url: https://git.jc-ai.cn/print/foundation/niimbot_excel.git
#      ref: 'feature/flutter_update_new'
  #    path: '../niimbot_excel'

  collection: ^1.17.0
  url_launcher: ^6.1.11
  lottie: ^2.3.2
  flutter_boost:
    #    path: '../../../../Respository/flutter_boost-5.0.1'
    git:
      url: https://git.jc-ai.cn/architect/flutter/flutter_boost.git
      ref: 'v5.0.2'
  niimbot_lego: 1.6.6
#  niimbot_lego:
#    git:
#      url: https://git.jc-ai.cn/architect/dboard/niimbot_lego.git
#      ref: 'v1.0.2'

  #  wechat_assets_picker:
#    git:
#      url: https://git.jc-ai.cn/print/foundation/flutter_canvas_plugins/flutter_wechat_assets_picker.git
#      ref: 'feature/flutter_update'
  wechat_assets_picker: 8.4.4

  # 模版文件解析与转换图像生成、打印生成“桥”
  niimbot_template:
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_template.git
      ref: 'feature/app_templateManager'


  # 缓存管理器
  niimbot_cache_manager:
    # path: '../../../plugins/niimbot_cache_manager'
    git:
      url: https://git.jc-ai.cn/print/foundation/niimbot_cache_manager.git
      ref: 'feature/6_3_6_font_categary_sort'
  image_editor: 1.5.1



dependency_overrides:
  ffi: ^2.0.2
  http: 1.2.1
  web: ^1.0.0
  device_info_plus: ^10.1.0
  wechat_picker_library: 1.0.4
  extended_image: ^9.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
#  build_runner: ^2.1.11
#  json_serializable: ^6.2.0
#  json_annotation: ^4.5.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # To add assets to your package, add an assets section, like this:
  assets:
    - assets/
    - assets/app_bar/
    - assets/bottom_bar/
    - assets/element/
    - assets/element/attribute/
    - assets/element/icon/
    - assets/floating_shortcut/
    - assets/input_bar/
    - assets/common/
    - assets/translations/
    - assets/scan/
    - assets/ocr/
    - assets/asr/
    - assets/excel/
    - assets/file_import/
    - assets/holder/
    - assets/lottie/
    - assets/live_code/

  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
