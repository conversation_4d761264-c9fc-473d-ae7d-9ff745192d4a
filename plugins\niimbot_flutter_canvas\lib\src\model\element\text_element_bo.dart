import 'dart:math';

import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_size_config.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/text_attr_panel_widget.dart';

/// 文本样式说明
class TextElementTextStyleTip {
  /// 标题
  final String title;

  /// 描述
  final String desc;

  /// 图片
  final String image;

  const TextElementTextStyleTip({
    required this.title,
    required this.desc,
    required this.image,
  });
}

extension TextElementBO on TextElement {
  /// 是否支持文本框样式
  bool isSupportBoxStyle() {
    return (this.type == ElementItemType.date ||
            this.type == ElementItemType.text ||
            this.type == ElementItemType.serial) &&
        !(this is TableCellElement);
  }

  /// 默认文本框样式
  static TextElementBoxStyle defaultBoxStyle() {
    return TextElementBoxStyle.autoWidth;
  }

  /// 默认文本样式
  static List<TextElementTextStyle> defaultTextStyles() {
    return [TextElementTextStyle.norm];
  }

  /// 可用文本样式
  static Map<TextElementTextStyle, String> availableTextStyles() {
    return {
      TextElementTextStyle.adaptive: intlanguage("app100001223", "自适应"),
      TextElementTextStyle.minimized: intlanguage("app100001222", "填满后缩小"),
    };
  }

  /// 可用文本样式说明
  static List<TextElementTextStyleTip> availableTextStyleTips() {
    return [
      TextElementTextStyleTip(
          title: intlanguage("app100001224", "缩小模式"),
          desc: intlanguage("app100001225", "开启文本适应文本框，以防止文字超出而影响排版样式"),
          image: "assets/lottie/text_style_minimized.json"),
      TextElementTextStyleTip(
          title: intlanguage("app100001226", "自适应模式"),
          desc: intlanguage("app100001227", "文字大小随文本框缩放"),
          image: "assets/lottie/text_style_adaptive.json"),
    ];
  }

  /// 处理TextElement横向拖动
  /// 垂直方向为竖向拖动
  /// 文本方向
  /// - 水平方向：模式3不变
  ///  - boxStyle=autoHeight
  ///  - textStyle=norm
  /// - 垂直方向
  ///  - boxStyle=autoHeight
  ///  - textStyle=norm
  /// - 弧形方向：拖动禁用
  ///
  void handleTextElementHorizontal() {
    var boxStyle = TextElementBoxStyle.autoWidth;
    if (this.getTextMode() == TextMode.Horizontal) {
      /// 横向, 模式3不变
      if (this.boxStyle != TextElementBoxStyle.fixedWidthHeight) {
        boxStyle = TextElementBoxStyle.autoHeight;
      } else {
        boxStyle = TextElementBoxStyle.fixedWidthHeight;
      }
    } else if (this.getTextMode() == TextMode.Vertical || this.getTextMode() == TextMode.Horizontal_90) {
      /// 垂直
      boxStyle = TextElementBoxStyle.autoHeight;
    } else if (this.getTextMode() == TextMode.Arc) {
      /// 弧形
      boxStyle = TextElementBoxStyle.autoWidth;
    }
    this.updateBoxStyle(boxStyle);
  }

  /// 处理TextElement纵向拖动（下方控制点）
  /// 拖动下方控制点切换到模式3（宽高都固定）
  void handleTextElementVertical() {
    var boxStyle = TextElementBoxStyle.fixedWidthHeight;
    this.updateBoxStyle(boxStyle);
  }

  /// 处理对角线拖动，只有水平方向支持对角线拖动
  /// 文本方向
  /// - 水平方向
  ///  - boxStyle=fixedWidthHeight
  ///  - textStyle=adaptive
  /// - 垂直方向
  ///  - boxStyle=auto-height
  ///  - textStyle=norm
  void handleTextElementDiagonal() {
    var boxStyle = TextElementBoxStyle.autoWidth;
    if (this.getTextMode() == TextMode.Horizontal) {
      /// 水平方向
      boxStyle = TextElementBoxStyle.fixedWidthHeight;
    } else if (this.getTextMode() == TextMode.Vertical || this.getTextMode() == TextMode.Horizontal_90) {
      /// 垂直方向
      boxStyle = TextElementBoxStyle.autoHeight;
    } else if (this.getTextMode() == TextMode.Arc) {
      boxStyle = TextElementBoxStyle.autoWidth;
    }
    this.updateBoxStyle(boxStyle);
  }

  // /// 处理字号变动逻辑
  // void handleFontSizeChange() {
  //   if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight) {
  //     /// 修改字号，模式3切换为模式2
  //     /// 字号变更时，文本框宽度限制，高度向下延展，框的宽高是贴着文字的。拖动结束后，框得到新的宽高，仍然是模式3
  //     this.boxStyle = TextElementBoxStyle.autoHeight;

  //     /// 标记字号变更
  //     /// 后续图像库返回updateBoxStyleWithInt时，在模式2下变更模式为模式3，且文本适应模式变为缩小
  //     this.fontSizeChanged = true;
  //   }
  //   // if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight
  //   //     && this.textStyle != null
  //   //     && this.textStyle.length > 0
  //   //     && this.textStyle.first == TextElementTextStyle.adaptive) {
  //   //       /// 1. 文本模式在3时，文本适应模式为自适应时，点击字号，文本适应模式变为缩小。
  //   //       this.textStyle = [TextElementTextStyle.minimized];
  //   // }
  // }

  /// 文本方向切换，回退到上次的文本样式
  void handleTextElementTypeSettingMode(int mode) {
    var lastBoxStyle = this.lastBoxStyles[mode] ?? TextElementBoxStyle.autoWidth;

    /// 判断文本方向
    if (mode == 1) {
      /// 水平方向
      lastBoxStyle ??= TextElementBoxStyle.autoWidth;
    } else if (mode == 2) {
      /// 垂直方向
      lastBoxStyle = TextElementBoxStyle.autoHeight;
    } else if (mode == 3) {
      lastBoxStyle = TextElementBoxStyle.autoWidth;
    }
    this.updateBoxStyle(lastBoxStyle);
  }

  /// 处理字符变动触发文本样式变动
  /// 横向模式一回车变成模式二  模式二回车还是模式二  模式三回车还是模式三
  void handleBoxStyleWhenValueChange(String value) {
    if (value.isEmpty) return;
    if (!value.contains('\n')) return;
    var boxStyle = this.boxStyle;
    if (boxStyle == TextElementBoxStyle.autoWidth) {
      boxStyle = TextElementBoxStyle.autoHeight;
    }
    this.updateBoxStyle(boxStyle);
  }

  /// 更新文本框样式
  void updateBoxStyle(TextElementBoxStyle boxStyle) {
    var lastTextStyle = <TextElementTextStyle>[];
    if (boxStyle == TextElementBoxStyle.autoHeight) {
      lastTextStyle = [TextElementTextStyle.norm];
    } else if (boxStyle == TextElementBoxStyle.autoWidth) {
      lastTextStyle = [TextElementTextStyle.norm];
    } else if (boxStyle == TextElementBoxStyle.fixedWidthHeight) {
      if (this.textStyle.length == 0 || this.textStyle.first == TextElementBO.defaultTextStyles().first) {
        lastTextStyle = [TextElementBO.availableTextStyles().keys.last];
      } else {
        lastTextStyle = this.textStyle;
      }
    }
    this.lastBoxStyles ??= Map<int, TextElementBoxStyle>();
    if (this.typesettingMode <= 1) {
      this.lastBoxStyles[0] = boxStyle;
      this.lastBoxStyles[1] = boxStyle;
    } else {
      this.lastBoxStyles[this.typesettingMode] = boxStyle;
    }
    this.boxStyle = boxStyle;
    this.textStyle = lastTextStyle;
    if (this.boxStyle != TextElementBoxStyle.fixedWidthHeight) {
      this.lastUnlessFixWidthHeightBoxStyle = this.boxStyle;
    }
  }

  /// 更新文本框样式
  void updateTextStyle(TextElementTextStyle textStyle) {
    this.textStyle = [textStyle];
  }

  /// 根据int类型调整文本框样式
  void updateBoxStyleWithInt(int boxStyle, CanvasElement canvasElement) {
    TextElementBoxStyle style = TextElementBoxStyle.empty;
    if (boxStyle == 0) {
      style = TextElementBoxStyle.autoWidth;
    } else if (boxStyle == 1) {
      style = TextElementBoxStyle.autoHeight;
    } else if (boxStyle == 2) {
      style = TextElementBoxStyle.fixedWidthHeight;
    }
    this.updateBoxStyle(style);
  }

  /// 更新文本字号，最大字号
  updateFontSize(num fontSize, num fontSizeThreshold, {bool needUpdateFontSize = false}) {
    // fontSizeThreshold || fontSize比最小字号还小时，遵守上次的字号大小
    // 只有模式三才回写字号
    // num转换成double处理
    fontSize = fontSize.toDouble();
    fontSizeThreshold = fontSizeThreshold.toDouble();
    if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight && !needUpdateFontSize) {
      double userFontSize = this.fontSize.toDouble();
      double imageFontSize = fontSize.toDouble();
      int newIndex = allFontSizeConfigList.getClosestAcceptableFontSizeIndexBinary(imageFontSize);
      imageFontSize = allFontSizeConfigList[newIndex].mm;

      if (imageFontSize < userFontSize) {
        // 图像库返回字号比用户设置小，不回写字号，显示提示
        this.shouldShowSizeWarning = true;
        // 保持用户原设置的字号不变
        // this.fontSize 保持不变
      } else if (imageFontSize > userFontSize) {
        // 图像库返回字号比用户设置大，切换到minimized模式
        this.textStyle = [TextElementTextStyle.minimized];
        this.shouldShowSizeWarning = false;
        // 保持用户设置的字号不变
        // this.fontSize 保持不变
      } else {
        // 图像库返回字号等于用户设置，无需提示
        this.shouldShowSizeWarning = false;
      }
    } else {
      // 原有逻辑：非adaptive模式或其他情况
      this.fontSize = min(
          max(
              fontSize.digits(2),
              fontSize < allFontSizeConfigList.first.mm || fontSizeThreshold < allFontSizeConfigList.first.mm
                  ? this.fontSize
                  : allFontSizeConfigList.first.mm),
          allFontSizeConfigList.last.mm);
      this.shouldShowSizeWarning = false;
    }

    this.fixedWidthHeightMaxFontSize = max(fontSizeThreshold.digits(2).toDouble(), allFontSizeConfigList.first.mm);
  }

  /// 文字延展
  /// 根据文本对齐方式确定文字延展方向
  /// - 水平方向
  ///  - 左对齐：向右延展
  ///  - 居中对齐：向两边延展
  ///  - 右对齐：向左延展
  /// - 垂直方向
  ///   - 上对齐：向下延展
  ///   - 居中对齐：向上下延展
  ///   - 下对齐：向上延展
  /// - 弧形方向：无
  /// [isScaleDragging] - 是否是对角线拖拽，如果是则跳过位置计算
  void handleTextElementPostion(double newWidth, double newHeight,
      [bool isScaleDragging = false, bool isEndScale = false]) {
    // 如果是对角线拖拽，跳过位置重新计算，因为Canvas层已经处理了位置校正
    if (isScaleDragging) {
      return;
    }

    if (this.boxStyle == TextElementBoxStyle.autoHeight) {
      if (this.getTextMode() == TextMode.Horizontal) {
        /// 固定宽度，只修改高度
        newHeight = newHeight;
        newWidth = this.width.toDouble();
        // newWidth = isEndScale ? (this.width / this.height) * newHeight : this.width.toDouble();
      } else if (this.getTextMode() == TextMode.Vertical || this.getTextMode() == TextMode.Horizontal_90) {
        /// 固定高度，只修改宽度
        newWidth = newWidth;
        newHeight = newHeight;
      } else {
        newWidth = newWidth;
        newHeight = newHeight;
      }
    } else if (this.boxStyle == TextElementBoxStyle.autoWidth) {
      /// 固定高度，只修改宽度
      newWidth = newWidth;
      newHeight = newHeight;
    } else if (this.boxStyle == TextElementBoxStyle.fixedWidthHeight) {
      /// 宽高固定，不修改
      newWidth = this.width.toDouble();
      newHeight = this.height.toDouble();
    } else {
      newWidth = newWidth;
      newHeight = newHeight;
    }

    final offset =
        Offset(newWidth.toDouble(), newHeight.toDouble()) - Offset(this.width.toDouble(), this.height.toDouble());
    // 文本旋转状态下宽高变化后校正 x,y
    double differenceHeight = newHeight - this.height;
    double differenceWidth = newWidth - this.width;

    Offset center =
        Offset(this.x.toDouble(), this.y.toDouble()) + Offset(this.width.toDouble() / 2, this.height.toDouble() / 2);
    if (this.getTextMode() == TextMode.Horizontal) {
      /// 水平方向
      if (this.textAlignHorizontal == 0) {
        /// 左对齐, minX不变
        if (this.rotate == 0) {
          this.x = this.x;
        } else if (this.rotate == 90) {
          this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
          this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
        } else if (this.rotate == 180) {
          final maxX = this.x + this.width;
          final newX = maxX - newWidth;
          this.x = newX;
          this.y = this.y - offset.dy;
        } else if (this.rotate == 270) {
          this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
          this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
        }
      } else if (this.textAlignHorizontal == 1) {
        /// 居中对齐, midX不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX;
          } else {
            this.x = x;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX - offset.dy / 2.0;
            this.y = this.y - offset.dy / 2.0;
          } else {
            Offset offsetCenter = center + Offset(-differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX;
            this.y = this.y - offset.dy;
          } else {
            this.y -= (newHeight - this.height);
            this.x -= (newWidth - this.width);
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midX = this.x + this.width / 2.0;
            final newX = midX - newWidth / 2.0;
            this.x = newX + offset.dy / 2.0;
            this.y = this.y - offset.dy / 2.0;
          } else {
            Offset offsetCenter = center + Offset(differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignHorizontal == 2) {
        /// 右对齐. maxX不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final maxX = this.x + this.width;
            final newX = maxX - newWidth;
            this.x = newX;
          } else {
            this.x = x;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
            this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
          } else {
            Offset offsetCenter = center + Offset(-differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x;
            this.y = this.y - offset.dy;
          } else {
            this.y -= (newHeight - this.height);
            this.x -= (newWidth - this.width);
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
            this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
          } else {
            Offset offsetCenter = center + Offset(differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      }
    } else if (this.getTextMode() == TextMode.Vertical || this.getTextMode() == TextMode.Horizontal_90) {
      /// 垂直方向
      if (this.textAlignVertical == 0) {
        /// 上对齐,minY不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter = center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // final maxX = this.x + this.width;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
            this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
          } else {
            Offset offsetCenter = center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignVertical == 1) {
        /// 居中对齐, midY不变
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter = center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // this.x = this.x - offset.dy/2.0;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 + offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter = center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      } else if (this.textAlignVertical == 2) {
        /// 下对齐
        if (this.rotate == 0) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final maxY = this.y + this.height;
            final newY = maxY - newHeight;
            this.y = newY;
            this.x = this.x - offset.dx;
          } else {
            /// maxX不变
            final maxX = this.x + width;
            this.x = maxX - newWidth;
            this.y = y;
          }
        } else if (this.rotate == 90) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 - offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter = center + Offset(-differenceHeight, -differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        } else if (this.rotate == 180) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0;
            this.x = this.x - offset.dy / 2.0;
          } else {
            this.y -= (newHeight - this.height);
            // this.x = this.x - offset.dy/2.0;
            this.x = this.x;
          }
        } else if (this.rotate == 270) {
          if (this.boxStyle == TextElementBoxStyle.autoWidth) {
            final midY = this.y + this.height / 2.0;
            final newY = midY - newHeight / 2.0;
            this.y = newY - offset.dy / 2.0 + offset.dx / 2.0;
            this.x = this.x - offset.dy / 2.0 - offset.dx / 2.0;
          } else {
            Offset offsetCenter = center + Offset(differenceHeight, differenceWidth) / 2;
            this.y = (offsetCenter - Offset(newWidth, newHeight) / 2).dy;
            this.x = (offsetCenter - Offset(newWidth, newHeight) / 2).dx;
          }
        }
      }
    } else if (this.getTextMode() == TextMode.Arc) {
      /// 弧形
      if (this.rotate == 0) {
        this.x = this.x;
        this.y = this.y;
      } else if (this.rotate == 90) {
        this.x = this.x - offset.dx / 2.0 - offset.dy / 2.0;
        this.y = this.y + offset.dx / 2.0 - offset.dy / 2.0;
      } else if (this.rotate == 180) {
        final maxX = this.x + this.width;
        final newX = maxX - newWidth;
        this.x = newX;
        this.y = this.y - offset.dy;
      } else if (this.rotate == 270) {
        this.x = this.x - offset.dx / 2.0 + offset.dy / 2.0;
        this.y = this.y - offset.dx / 2.0 - offset.dy / 2.0;
      }
    }
    this.width = newWidth;
    this.height = newHeight;
  }

  TextMode getTextMode() {
    if (typesettingMode == 2) {
      if (rotate == 270) {
        return TextMode.Horizontal_90;
      } else {
        return TextMode.Vertical;
      }
    }
    if (typesettingMode == 3) {
      return TextMode.Arc;
    }
    return TextMode.Horizontal;
  }
}
