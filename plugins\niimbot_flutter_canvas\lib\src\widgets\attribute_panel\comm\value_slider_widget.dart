import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/add_subtract_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_theme_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/canvas_slider_theme.dart';

class ValueSliderWidget<T extends num> extends StatefulWidget {
  /// 最小值
  final T min;

  /// 最大值
  final T max;

  /// 当前值
  final T value;

  /// 步进值
  final T stepValue;

  /// 进度条分段数
  final int divisions;

  /// 标题
  final String? title;

  /// 进度条宽度
  final double width;

  final String Function(T value)? indicatorTextBuilder;

  final ValueChanged<T>? valueChanged;

  final bool hideAddSubtractButton;
  final List<CanvasElement>? canvasElements;
  final int? type; //1代表字号
  final bool enable;

  /// 滑杆前置的Widget, 例如字号的A
  final ValueGetter<Widget>? preWidget;

  const ValueSliderWidget({
    super.key,
    required this.min,
    required this.max,
    required this.value,
    required this.stepValue,
    required this.divisions,
    this.title,
    required this.width,
    this.valueChanged,
    this.indicatorTextBuilder,
    this.hideAddSubtractButton = false,
    this.canvasElements,
    this.type,
    this.enable = true,
    this.preWidget,
  });

  @override
  State<StatefulWidget> createState() {
    return ValueSliderState<T>();
  }
}

class ValueSliderState<E extends num> extends State<ValueSliderWidget<E>> {
  late E _value;

  ///slider总宽度（包括滑块）
  double _sliderWidth = 0;
  double _thumbWidth = 20;
  double _indicatorWidth = 40;
  GlobalKey _key = GlobalKey();

  @override
  void initState() {
    super.initState();
    _value = widget.value;
    WidgetsBinding.instance.addPostFrameCallback(_getSliderWidth);
  }

  @override
  Widget build(BuildContext context) {
    double addSubtractButtonWidth;
    if (widget.hideAddSubtractButton != true) {
      addSubtractButtonWidth = 72;
    } else {
      addSubtractButtonWidth = 0;
    }

    ///滑块下面文字指示器的锚点：滑块中心点与文字指示器中心点对齐
    ///当前value所占格数 = (_value - widget.min) / widget.stepValue
    ///每格宽度 = (_sliderWidth - _thumbWidth) / widget.divisions
    ///滑竿padding = 8
    ///文字容器宽度一半 = 32 * 0.5
    double indicatorAnchor = ((_value - widget.min) / widget.stepValue) *
            (_sliderWidth - _thumbWidth) /
            widget.divisions +
        8 +
        0.5 * _thumbWidth -
        0.5 * _indicatorWidth;
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      if (widget.title != null)
        Padding(
          padding: EdgeInsetsDirectional.fromSTEB(12, 12, 12, 0),
          child: Text(
            widget.title!,
            style: CanvasTheme.of(context).attributeTitleTextStyle,
          ),
        ),
      Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widget.preWidget != null) widget.preWidget!(),
          Expanded(
              child: Container(
            // color: Colors.red,
            height: 46,
            child: Stack(
              alignment: AlignmentDirectional.center,
              children: [
                Container(
                  // color: Colors.green,
                  height: 30,
                  child: Padding(
                    padding:
                        const EdgeInsetsDirectional.symmetric(horizontal: 8),
                    child: CanvasSliderTheme(
                      child: RepaintBoundary(
                        child: Slider(
                          key: _key,
                          value: _value.toDouble(),
                          divisions: widget.divisions,
                          onChanged: (value) {
                            if (widget.enable == false) {
                              return;
                            }
                            _onValueChanged.call(value);
                          },
                          onChangeEnd: widget.enable == false
                              ? null
                              : (value) {
                                  if (widget.enable == false) {
                                    return;
                                  }
                                  if (widget.type == 1) {
                                    CanvasPluginManager()
                                        .nativeMethodImpl
                                        ?.sendTrackingToNative({
                                      "track": "click",
                                      "posCode": "108_072_140",
                                      "ext": {
                                        "b_name": "滑轨调整",
                                        "module_name":
                                            TrackUtils.getElementsTypeStr(
                                                widget.canvasElements)
                                      }
                                    });
                                  }
                                },
                          max: widget.max.toDouble(),
                          min: widget.min.toDouble(),
                        ),
                      ),
                    ),
                  ),
                ),
                if (_sliderWidth != 0)
                  PositionedDirectional(
                    top: 34,
                    start: indicatorAnchor,
                    child: Container(
                      // color: Colors.blue,
                      width: _indicatorWidth,
                      child: Center(
                        child: Text(
                          formattedValue(),
                          style: TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 11,
                              fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  )
              ],
            ),
          )),
          if (widget.hideAddSubtractButton != true)
            AddSubtractButton(
              onSubtractClicked: () {
                if (widget.enable == false) {
                  return;
                }
                num v = _value;
                v -= widget.stepValue;

                _onValueChanged(v);
                if (widget.type == 1) {
                  CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                    "track": "click",
                    "posCode": "108_072_140",
                    "ext": {
                      "b_name": "缩小",
                      "module_name":
                          TrackUtils.getElementsTypeStr(widget.canvasElements)
                    }
                  });
                }
              },
              onAddClicked: () {
                if (widget.enable == false) {
                  return;
                }
                num v = _value;
                v += widget.stepValue;

                _onValueChanged(v);
                if (widget.type == 1) {
                  CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                    "track": "click",
                    "posCode": "108_072_140",
                    "ext": {
                      "b_name": "增大",
                      "module_name":
                          TrackUtils.getElementsTypeStr(widget.canvasElements)
                    }
                  });
                }
              },
              isAddEnable: _value < widget.max.toDouble() && widget.enable,
              isSubtractEnable: _value > widget.min.toDouble() && widget.enable,
            ),
          SizedBox(
            width: widget.hideAddSubtractButton != true ? 8 : 0,
          )
        ],
      )
    ]);
  }

  void _onValueChanged(v) {
    if (v < widget.min) {
      v = widget.min;
    } else if (v > widget.max) {
      v = widget.max;
    }

    E? newValue;
    if (E.toString() == 'double') {
      newValue = v as E;
    } else if (E.toString() == 'int') {
      newValue = v.toInt() as E;
    }

    // print('mewValue:$newValue value:$_value');

    if (_value == newValue) {
      return;
    }
    setState(() {
      _value = newValue!;
    });
    widget.valueChanged?.call(_value);
  }

  String formattedValue() {
    if (widget.indicatorTextBuilder != null) {
      return widget.indicatorTextBuilder!.call(_value);
    }
    return E.toString() == 'double'
        ? (_value as double).toStringAsFixed(1)
        : _value.toString();
  }

  _getSliderWidth(_) {
    _sliderWidth = _key.currentContext!.size!.width;
    setState(() {});
  }

  @override
  void didUpdateWidget(covariant ValueSliderWidget<E> oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    _value = widget.value;
  }
}
