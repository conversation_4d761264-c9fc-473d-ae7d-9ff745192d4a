//
//  JCPrintManager.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2019/5/30.
//  Copyright © 2019 Jingchen Technology Co.Ltd . All rights reserved.
//
#import "JCPrintManager.h"
#import "JCTemplateData+Transfer.h"
#import "JCFlutterManager.h"
#import <Flutter/Flutter.h>
#import "JCTemplateImageManager.h"

#import "JCRFIDSourceManager.h"
#import "JCC1PrintManager.h"
#import "JCC1PrintInfo.h"
#import "FlutterBoostUtility.h"
#import "JCRFIDSourceManager.h"

@interface JCPrintManager()
@property(nonatomic,copy)   XYBlock complateBlock;
@property(nonatomic,copy)   XYBlock capacitorPrintComplateBlock;
@property(nonatomic,assign)BOOL isNotNeedUI;
@property(nonatomic,assign)BOOL isSupport16GrayPrint;

// 打印历史记录 ID
@property (nonatomic, strong) NSString *historyId;

/// RFID写入时，页数错误的页
@property (nonatomic, assign) NSInteger RFIDErrorPage;

/// C1机型切割类型 0 - 不切  1 - 分割线  2 - 半切
@property(nonatomic,assign) NSInteger cutType;

@end


@implementation JCPrintManager
DEF_SINGLETON( JCPrintManager )

- (instancetype)init
{
    if (self = [super init]) {
        _meetingId = @"";
        _RFIDErrorPage = 0;
        _isFolderSharePrint = NO;
        _cutType = -1;
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(quitIonicApp) name:JCUONICAPP_QUIT object:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(unNeedUIPrintComplate) name:JCUONICAPP_PRINT_COMPLATE object:nil];
      [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(printComplate) name:JC_PRINT_COMPLATE object:nil];
      

    }
    return self;
}

//ionic小程序退出
-(void)quitIonicApp
{
    self.meetingId = nil;
    self.uniAppId = @"";
    self.capacitorPrintComplateBlock = nil;
    [JCPrintManager sharedInstance].printThemeColor = nil;
}

//通知ionic小程序本次打印完成
- (void)unNeedUIPrintComplate{
    if(self.capacitorPrintComplateBlock != nil){
      self.capacitorPrintComplateBlock(@(1));
      self.capacitorPrintComplateBlock = nil;
    }
    if(self.complateBlock){
      self.complateBlock(@1);
      self.complateBlock = nil;
    }
    self.meetingId = nil;
    [JCPrintManager sharedInstance].uniAppId = @"";
}

- (void)printComplate{
  if(self.complateBlock){
    self.complateBlock(@1);
    self.complateBlock = nil;
  }
  if(self.capacitorPrintComplateBlock != nil){
    self.capacitorPrintComplateBlock(@(1));
    self.capacitorPrintComplateBlock = nil;
  }
}

- (void)doPrintWithTemplateModel:(NSArray *)printModes
                        uniAppId:(NSString *)uniAppId
                      printScene:(JCPrintScene)printScene
                       historyId:(NSString *)historyId
                   printComplate:(XYBlock)complateBlock {
    JCTemplateData *printMode = printModes.firstObject;
    self.printScene = printScene;
    XYWeakSelf
    self.uniAppId = uniAppId;
    self.complateBlock = complateBlock;
    XYViewController *vc = (XYViewController *)[XYTool getCurrentVC];
    if(!STR_IS_NIL(uniAppId)){
        [JCRFIDSourceManager sharedInstance].isShowRFID = NO;
        [self toPrintSettingVc:printModes currentVC:vc historyId:historyId];
    }else{
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"isShowRfid" arguments:@{} result:^(id  _Nullable result) {
            BOOL isShowRfid = [result isKindOfClass:[NSNumber class]] && [result boolValue];
            [JCRFIDSourceManager sharedInstance].isShowRFID = isShowRfid;
            //原生商品库跳转打印能拿到最新的商品信息 不需要经过flutter中转
            if(([printMode isFromPcExcel] || [self isNeedTranformGoodsTemplateWith:printMode printScene:printScene]) && printScene != JCPrintSceneStoreGoods){
                NSString * jsonStr = [printMode toJSONString];
                [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"transformPcExcelTemplateJson" arguments:@{@"templateJson":jsonStr} result:^(id  _Nullable result) {
                    if([result isKindOfClass:[NSString class]]){
                        JSONModelError *error;
                        NSString * content = result;
                        JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:content
                                                                                        error:&error];
                        //拿到中转后老模版的数据后刷新页码信息
                        [DrawBoardInfo updateInfoWith:templateData];
                        [self toPrintSettingVc:printModes currentVC:vc historyId:historyId];
                    }
                }];
            }else{
                [self toPrintSettingVc:printModes currentVC:vc historyId:historyId];
            }
        }];

    }
}

- (void)doC1PrintWithWithPrintInfo:(JCC1PrintInfo *)printInfo
                   printComplate:(XYBlock)complateBlock {
//    __weak typeof(self) weakSelf = self;
//    self.printScene = JCPrintSceneC1;
//    self.complateBlock = complateBlock;
//    // 设置切割类型  0 - 不切  1 - 分割线  2 - 半切
//    self.cutType = [printInfo.halfCut boolValue] ? 2 : (printInfo.printDivider ? 1 : 0);
//    // 打印回调
//    [[JCC1PrintManager sharedInstance] setPrintBlock:^() {
//        // 设置浓度值
//        [JCPrintManager sharedInstance].printBlackValue = printInfo.printDensity.stringValue;
//        // 从NSNumber中提取浮点值
//        float floatValue = [printInfo.tubeSpecs floatValue];
//        // 保留两位小数
//        float roundedValue = roundf(floatValue * 100) / 100.0;
//        // 设置耗材类型
//        [JCAPI setTubeInfo:printInfo.tubeType.intValue withWidth:roundedValue withCompletion:^(NSDictionary *printDicInfo) {
//            if ([@"0" isEqualToString:printDicInfo[@"statusCode"]]) {
//                // 设置打印是否切割以及切割深度
//                if ([printInfo.halfCut boolValue]) {
//                    // 设置切割深度
//                    [JCAPI setHalfCut:printInfo.cutDepth.intValue withCompletion:^(NSDictionary *printDicInfo) {
//                        if ([@"0" isEqualToString:printDicInfo[@"statusCode"]]) {
//                            [weakSelf statDoPrintWithTemplateModels:printInfo.batchIds excel:weakSelf.excelNum isNotNeedUI:false];
//                        } else {
//                            NSLog(@"设置切割深度失败");
//                        }
//                    }];
//                } else {
//                    // 无需切割直接打印
//                    [weakSelf statDoPrintWithTemplateModels:printInfo.batchIds excel:weakSelf.excelNum isNotNeedUI:false];
//                }
//            } else {
//                NSLog(@"设置耗材类型失败");
//            }
//        }];
//    }];
//    // RFID回调
//    self.getRfidProgressBlock = ^(NSNumber *state){
//        [weakSelf.progressView refreshGetRfidState:state.integerValue];
//    };
    // 打印前检查
    [[JCC1PrintManager sharedInstance] printCheck];
}

-(BOOL)isNeedTranformGoodsTemplateWith:(JCTemplateData *)templateData printScene:(JCPrintScene)printScene{
    BOOL isNeedTranform = false;
    if([templateData.dataSource isKindOfClass:[NSArray class]] && templateData.dataSource.count > 0 && [templateData.dataSource.firstObject isKindOfClass:[NSDictionary class]] && printScene != JCPrintSceneCanvas){
        NSString * type = [templateData.dataSource.firstObject objectForKey:@"type"];
        if([type isEqualToString:@"commodity"]){
            isNeedTranform = true;
        }
    }
    return isNeedTranform;
}

-(void)toPrintSettingVc:(NSArray *)printModes currentVC:(UIViewController *)vc historyId:(NSString *)historyId{
    JCTemplateData *templateData = printModes.firstObject;
    NSArray<JCGoodDetailInfo> *goodsList = templateData.goodsList;
    templateData.goodsList = goodsList;
    NSMutableDictionary *content = [((JCTemplateData *)[templateData copy])
                            .toSdk(NO)
                            .toCache(YES)
                             dataDict].mutableCopy;
  for (NSMutableDictionary *element in content[@"elements"]) {
      NSString *elementType = element[@"type"];
      if([elementType isEqualToString:@"image"]){
        NSNumber *isDefaultImageNumber = element[@"isDefaultImage"];
        element[@"isDefaultImage"] = isDefaultImageNumber.integerValue == 1?@(true):@(false);
      }
  }
    //行业模板id丢失 此处处理
    content[@"cloudTemplateId"] = UN_NIL(templateData.cloudTemplateId);
    NSString *contentStr1 = [content xy_toJsonString];
    NSString *uniAppName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:self.uniAppId];
    NSMutableDictionary *arguments = @{@"niimbotTemplate": UN_NIL(contentStr1),@"resetDisplayMultiple": @true,@"printCount": @1,@"printScene":@"print_scene_template_detail",@"isPresent": @NO,
                                       @"isEnablePopGesture": @YES}.mutableCopy;
    NSString *pageName = @"printSetting";
    if(self.printScene == JCPrintSceneUniApp && !STR_IS_NIL(self.uniAppId)){
       //小程序打印场景
       pageName = @"printSettingDialog";
       arguments[@"isPresent"] = @YES;
       arguments[@"isAnimated"] = @NO;
       arguments[@"printScene"] = @"mini_app";
       arguments[@"uniAppInfo"] =
      @{@"uniAppId":UN_NIL(self.uniAppId),@"uniAppName":UN_NIL(uniAppName),@"meetingId":UN_NIL(self.meetingId),@"themeColor":UN_NIL(self.printThemeColor),@"isRePrint":[JCPrintManager sharedInstance].isDangerCapRePrint?@1:@0};
       [FlutterBoostUtility gotoTransparentFlutterPage:pageName arguments:arguments onPageFinished:^(NSDictionary *dic) {

       }];
       return;
    } else if(self.printScene == JCPrintNullUiShowProgress && !STR_IS_NIL(self.uniAppId)){
      //小程序打印场景
      pageName = @"printNullUiShowProgress";
      arguments[@"isPresent"] = @YES;
      arguments[@"isAnimated"] = @NO;
      arguments[@"printScene"] = @"print_null_ui_show_progress";
      arguments[@"uniAppInfo"] =
      @{@"uniAppId":UN_NIL(self.uniAppId),@"uniAppName":UN_NIL(uniAppName),@"meetingId":UN_NIL(self.meetingId),@"themeColor":UN_NIL(self.printThemeColor),@"isRePrint":[JCPrintManager  sharedInstance].isDangerCapRePrint?@1:@0};
      [FlutterBoostUtility gotoTransparentFlutterPage:pageName arguments:arguments completion:^(BOOL isCompletion) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          [MBProgressHUD hideHUDForView:hudWindow animated:YES];
        });
      } onPageFinished:^(NSDictionary *dic) {
      
       }];
      return;
   }
    self.printThemeColor = nil;
    if(self.printScene == JCPrintSceneStoreGoods){
        //商品库打印场景
        if(templateData.goodsList.count > 0){
            NSMutableArray *goodsInfoArr = [NSMutableArray array];
            for (JCGoodDetailInfo *detailInfo in templateData.goodsList) {
                NSDictionary *goodInfoDic = detailInfo.toDictionary;
                [goodsInfoArr addObject:goodInfoDic];
            }
            arguments[@"batchGoodsList"] = goodsInfoArr;
        }
        arguments[@"printScene"] = @"print_scene_template_detail";
    }else if(self.printScene == JCPrintSceneMyTemplate){
        //我的模版详情打印场景
        arguments[@"printScene"] = @"print_scene_template_detail";
    }else if(self.printScene == JCPrintSceneTemplateShare){
        //我的模版文件夹分享打印场景
        arguments[@"printScene"] = @"folder_share";
    }else if(self.printScene == JCPrintSceneHistory){
        //打印历史模板打印场景
        arguments[@"printHistoryId"] = UN_NIL(historyId);
        arguments[@"printScene"] = @"print_scene_history";
    }else if(self.printScene == JCPrintSceneCanvas){
        //画板打印场景
        arguments[@"printScene"] = @"print_scene_canvas";
    }else if(self.printScene == JCPrintSceneBatchPrint){
        //我的模版批量打印场景
        NSMutableArray *printInfos = [NSMutableArray array];
        for (JCTemplateData *templateData in printModes) {
            NSDictionary *printInfo = @{@"id":UN_NIL(templateData.idStr),@"printPage":@(templateData.printPage)};
            [printInfos addObject:printInfo];
        }
        arguments[@"printCount"] = @1;
        arguments[@"batchtIds"] = [printInfos yy_modelToJSONString];
        arguments[@"printScene"] = @"print_batch";
    }
    bool showRfidBind = [JCRFIDSourceManager sharedInstance].isShowRFID;
    NSString *rfidInfo = @"";
    if(showRfidBind){
        if([JCRFIDSourceManager sharedInstance].sourceModel != nil){
            rfidInfo = [JCRFIDSourceManager sharedInstance].sourceModel.toJSONString;
        }
    }
    arguments[@"showRfidBind"] = @(showRfidBind);
    arguments[@"rfidInfo"] = UN_NIL(rfidInfo);
    arguments[@"pdfBindInfo"] = [[JCPDFPrintSourceManager sharedInstance] getPdfBindInfoMaps];
    UIViewController *currentViewController = [XYTool getCurrentVC];
    NSMutableArray *navPushControllers = [NSMutableArray array];
    for (UIViewController *viewController in currentViewController.navigationController.viewControllers) {
        if(![viewController isKindOfClass:[FBFlutterViewContainer class]] || ([viewController isKindOfClass:[FBFlutterViewContainer class]] && ![((FBFlutterViewContainer *)viewController).name isEqualToString:pageName])){
            if([viewController isKindOfClass:[self class]] && ![viewController isEqual:self]){
                continue;
            }else{
                [navPushControllers addObject:viewController];
            }
        }else if([viewController isEqual:currentViewController.navigationController.jk_rootViewController]){
            [navPushControllers addObject:viewController];
        }
    }
    currentViewController.navigationController.viewControllers = navPushControllers;
    arguments[@"isPresent"] = @(navPushControllers.count == 0);
    [FlutterBoostUtility gotoFlutterPage:pageName arguments:arguments onPageFinished:^(NSDictionary *dic) {

    }];
}

//小程序直接打印调用
-(void)printWithTemplateModel:(JCTemplateData *)templateData excel:(NSInteger)excelNum meetingId:(NSString *)meetingId callBack:(XYBlock)complateBlock{
    self.meetingId = meetingId;
    self.uniAppId = [NBCAPMiniAppManager sharedInstance].currentAppId;
    self.capacitorPrintComplateBlock = complateBlock;
    self.uniAppId = [NBCAPMiniAppManager sharedInstance].currentAppId;
    [self startDoPrintWithTemplateModels:@[templateData] excel:excelNum isNotNeedUI:true];
}

- (void)startDoPrintWithTemplateModels:(NSArray *)templateDatas excel:(NSInteger)excelNum isNotNeedUI:(BOOL)isNotNeedUI{
    self.isUseNativeAlert = YES;
    JCTemplateData *templateData = templateDatas.firstObject;
    NSArray<JCGoodDetailInfo> *goodsList = templateData.goodsList;
    templateData.goodsList = goodsList;
    self.printScene = JCPrintNullUi;
    NSMutableDictionary *arguments = @{@"niimbotTemplate": UN_NIL(templateData.toJSONString),@"resetDisplayMultiple": @true,@"printHistoryId": @"",
                                @"printCount": @1,@"printScene":@"print_null_ui",@"isPresent":@YES,@"isAnimated":@NO}.mutableCopy;
    if(!STR_IS_NIL(self.uniAppId)){
        NSString *uniAppName = [[NBCAPMiniAppManager sharedInstance] getUniAppNameWithAppId:self.uniAppId];
        arguments[@"uniAppInfo"] = @{@"uniAppId":UN_NIL(self.uniAppId),@"uniAppName":UN_NIL(uniAppName),@"meetingId":UN_NIL(self.meetingId)};
    }
    [FlutterBoostUtility gotoTransparentFlutterPage:@"printSetting" arguments:arguments onPageFinished:^(NSDictionary *dic) {

    }];
    return;
}

#pragma mark -RFIDPrint

- (BOOL)currentRFIDTemplateSupportPrint:(JCTemplateData *)rfidTemplateData {
    BOOL isSupport = NO;
    NSString *printerName = JC_CURRENT_CONNECTED_PRINTER;
    NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:printerName isNeedShowChildType:YES];
    NSArray *supportPrinters = [rfidTemplateData.profile.machineName componentsSeparatedByString:@","];
    if([supportPrinters containsObject:printType] || !JC_IS_CONNECTED_PRINTER || JC_RFID_SUPPORT_GET_TEMPLATE_ONLY){
        isSupport = YES;
    }
    return isSupport;
}

@end
