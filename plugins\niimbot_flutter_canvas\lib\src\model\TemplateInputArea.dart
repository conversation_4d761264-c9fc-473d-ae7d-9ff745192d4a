import 'dart:convert';

TemplateInputArea templateInputAreaFromJson(String str) => TemplateInputArea.fromJson(json.decode(str));
String templateInputAreaToJson(TemplateInputArea data) => json.encode(data.toJson());
class TemplateInputArea {
  TemplateInputArea({
      this.h,
      this.name,
      this.w,
      this.x,
      this.y,
      this.type,
      this.productField,
      });

  TemplateInputArea.fromJson(dynamic json) {
    h = json['h'];
    name = json['name'];
    w = json['w'];
    x = json['x'];
    y = json['y'];
    type = json['type'];
    productField = json['productField'];
  }
  num? h;
  String? name;
  num? w;
  num? x;
  num? y;
  String? type;
  String? productField;
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['h'] = h;
    map['name'] = name;
    map['w'] = w;
    map['x'] = x;
    map['y'] = y;
    map['type'] = type;
    map['productField'] = productField;
    return map;
  }

}
