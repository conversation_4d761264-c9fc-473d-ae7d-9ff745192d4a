import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:niimbot_print_setting_plugin/Print_setting_logic.dart';
import 'package:niimbot_print_setting_plugin/utils/svg_icon.dart';

enum OffsetType { left, right, top, bottom }

extension OffsetTypeDescription on OffsetType {
  /// 埋点用的
  String get des {
    switch (this) {
      case OffsetType.left:
        return '左';
      case OffsetType.right:
        return '右';
      case OffsetType.top:
        return '上';

      case OffsetType.bottom:
        return '下';
    }
  }
}

class _OffsetNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    final String text = newValue.text;
    // 允许中间态：空字符串、"-"、"."、"-."，避免用户输入过程中被清空
    if (text.isEmpty || text == '-' || text == '.' || text == '-.') {
      return newValue;
    }
    // 仅允许数字、一个可选的前导负号和一个小数点
    if (RegExp(r'[^0-9\.-]').hasMatch(text)) {
      return oldValue;
    }
    // 负号只能在首位
    if (text.contains('-') && !text.startsWith('-')) {
      return oldValue;
    }
    // 最多一个小数点
    final int dotCount = '.'.allMatches(text).length;
    if (dotCount > 1) {
      return oldValue;
    }
    // 限制小数位最多 1 位
    final int dotIndex = text.indexOf('.');
    if (dotIndex != -1) {
      final String decimals = text.substring(dotIndex + 1);
      if (decimals.length > 1) {
        return oldValue;
      }
    }
    return newValue;
  }
}

class PrintOffsetWidget extends StatefulWidget {
  PrintSettingLogic logic;

  PrintOffsetWidget(this.logic, {Key? key}) : super(key: key);

  @override
  _PrintOffsetWidgetState createState() => _PrintOffsetWidgetState();
}

class _PrintOffsetWidgetState extends State<PrintOffsetWidget> with WidgetsBindingObserver {
  double ofsetX = 0.0;
  double ofsetY = 0.0;
  FocusNode nodeX = FocusNode();
  FocusNode nodeY = FocusNode();
  double keyboardHeight = 0;
  bool isLongPress = false;
  double inputScale = 10;
  double maxOffset = 0.5;
  Offset offset = Offset.zero;
  TextEditingController? ofsetXController = TextEditingController();
  TextEditingController? ofsetYController = TextEditingController();
  late StateSetter offetSetter;
  Timer? _timer;
  double viewBottomSafeHeight = 0;
  bool shouldPopAfterDelay = true;
  @override
  void initState() {
    ofsetX = widget.logic.printData.ofsetX;
    ofsetY = widget.logic.printData.ofsetY;
    ofsetXController?.text = ofsetX.toStringAsFixed(1);
    ofsetYController?.text = ofsetY.toStringAsFixed(1);
    offset = Offset(ofsetX / inputScale, ofsetY / inputScale);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      viewBottomSafeHeight = MediaQuery.of(context).padding.bottom;
      debugPrint("底部高度:$viewBottomSafeHeight");
    });

    nodeX.addListener(() {
      if (!nodeX.hasFocus) {
        if (ofsetXController!.text.isEmpty || ofsetXController!.text == '-' || ofsetXController!.text == '.' || ofsetXController!.text == '-.') {
          ofsetXController!.text = "0.0";
        }
        final double? value = double.tryParse(ofsetXController!.text);
        final double valid = value == null ? 0.0 : value.clamp(-5.0, 5.0);
        ofsetX = valid;
        ofsetXController!.text = valid.toStringAsFixed(1);
        offset = Offset(ofsetX / inputScale, offset.dy);
        setState(() {});
      }
    });
    nodeY.addListener(() {
      if (!nodeY.hasFocus) {
        if (ofsetYController!.text.isEmpty || ofsetYController!.text == '-' || ofsetYController!.text == '.' || ofsetYController!.text == '-.') {
          ofsetYController!.text = "0.0";
        }
        final double? value = double.tryParse(ofsetYController!.text);
        final double valid = value == null ? 0.0 : value.clamp(-5.0, 5.0);
        ofsetY = valid;
        ofsetYController!.text = valid.toStringAsFixed(1);
        offset = Offset(offset.dx, ofsetY / inputScale);
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    nodeX.dispose();
    nodeY.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();

    // 键盘高度
    final double viewInsetsBottom =
        EdgeInsets.fromViewPadding(View.of(context).viewInsets, View.of(context).devicePixelRatio).bottom;
    if (Platform.isIOS) {
      keyboardHeight = viewInsetsBottom;
    } else {
      keyboardHeight = viewInsetsBottom;
    }
    debugPrint("键盘高度1:$keyboardHeight");
    keyboardHeight = keyboardHeight * 3 / 4;
    debugPrint("键盘高度2:$keyboardHeight");
    // 打印键盘高度
    setState(() {});
  }

  KeyboardActionsConfig _buildConfig() {
    return KeyboardActionsConfig(
      keyboardBarColor: Colors.grey[200],
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: nodeX,
          onTapAction: () {},
        ),
        KeyboardActionsItem(
          displayArrows: true,
          focusNode: nodeY,
          onTapAction: () {},
        )
      ],
      defaultDoneWidget: GestureDetector(
        onTap: () {
          nodeX.unfocus();
          nodeY.unfocus();
        },
        child: Text(
          widget.logic.getI18nString('app01031', '完成'),
          style: const TextStyle(
            color: Color(0xFF262626),
            fontSize: 15.0,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final double viewInsetsBottom = EdgeInsets.fromViewPadding(View.of(context).viewInsets, View.of(context).devicePixelRatio).bottom;
    final double desiredHeight = 190 + 300 + keyboardHeight + 20;
    final double maxHeight = MediaQuery.of(context).size.height * 0.9;
    return Container(
      height: math.min(desiredHeight, maxHeight),
      padding: EdgeInsets.only(bottom: viewInsetsBottom),
      decoration: const BoxDecoration(
        color: Color(0xFFF5F5F5),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.0),
          topRight: Radius.circular(12.0),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 16.0),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0),
              ),
            ),
            height: 48,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    constraints: const BoxConstraints(minWidth: 50),
                    height: 40,
                    color: Colors.white,
                    alignment: Alignment.centerLeft,
                    child: Text(
                      widget.logic.getI18nString("login0062", "取消"),
                      style: TextStyle(color: Color(0xFF161616), fontSize: 16.0, fontWeight: FontWeight.w600),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: Text(
                      widget.logic.getI18nString("app00977", "偏移校准"),
                      textAlign: TextAlign.center,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(
                        color: Color(0xFF161616),
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    if (ofsetXController!.text.isEmpty) {
                      ofsetXController!.text = "0.0";
                    }
                    if (ofsetYController!.text.isEmpty) {
                      ofsetYController!.text = "0.0";
                    }
                    final double? offsetX = double.tryParse(ofsetXController!.text);
                    final double? offsetY = double.tryParse(ofsetYController!.text);
                    final double saveX = (offsetX ?? 0.0).clamp(-5.0, 5.0);
                    final double saveY = (offsetY ?? 0.0).clamp(-5.0, 5.0);
                    widget.logic.printData.ofsetX = saveX;
                    widget.logic.printData.ofsetY = saveY;
                    widget.logic.saveDeviceOffset(saveY, saveX);
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    constraints: const BoxConstraints(minWidth: 50),
                    height: 40,
                    color: Colors.white,
                    alignment: Alignment.centerRight,
                    child: Text(
                      widget.logic.getI18nString("app00017", "保存"),
                      style: TextStyle(
                          color: widget.logic.style.saveBtnBgColor, fontSize: 16.0, fontWeight: FontWeight.w600),
                    ),
                  ),
                )
              ],
            ),
          ),
          _ofsetCalibration()
        ],
      ),
    );
  }

  _ofsetCalibration() {
    if (ofsetXController!.text.isEmpty) {
      ofsetXController!.text = "0.0";
    }
    if (ofsetYController!.text.isEmpty) {
      ofsetYController!.text = "0.0";
    }
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      margin: const EdgeInsetsDirectional.symmetric(vertical: 12, horizontal: 16),
      padding: const EdgeInsetsDirectional.symmetric(vertical: 6),
      child: Column(
        children: [
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.logic.getI18nString('app100001510', '水平偏移(mm)'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                  ),
                ),
                Container(
                  width: 53,
                  height: 32,
                  alignment: Alignment.center,
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 3),
                  child: KeyboardActions(
                    config: _buildConfig(),
                    child: CupertinoTextField(
                      controller: ofsetXController,
                      focusNode: nodeX,
                      textInputAction: TextInputAction.done,
                      textAlign: TextAlign.center,
                      textDirection: TextDirection.ltr,
                      inputFormatters: [
                        _OffsetNumberFormatter(),
                      ],
                      scrollPadding: EdgeInsets.all(0),
                      keyboardType: const TextInputType.numberWithOptions(signed: true, decimal: true),
                      style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                      decoration: BoxDecoration(
                        color: Color(0x14747480),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      onChanged: (value) {
                        // 空值或中间态视为 0，但不改动输入框文本，避免光标跳动
                        double parsed = 0.0;
                        if (value.isEmpty || value == '-' || value == '.' || value == '-.') {
                          parsed = 0.0;
                        } else {
                          final double? v = double.tryParse(value);
                          if (v == null) {
                            return;
                          }
                          parsed = v;
                        }
                        if (parsed > 5.0) {
                          parsed = 5.0;
                          const String text = '5';
                          ofsetXController?.value = TextEditingValue(
                            text: text,
                            selection: TextSelection.collapsed(offset: text.length),
                          );
                        } else if (parsed < -5.0) {
                          parsed = -5.0;
                          const String text = '-5';
                          ofsetXController?.value = TextEditingValue(
                            text: text,
                            selection: TextSelection.collapsed(offset: text.length),
                          );
                        }
                        ofsetX = parsed;
                        offset = Offset(ofsetX / inputScale, offset.dy);
                        offetSetter(() {});
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Padding(
            padding:  EdgeInsetsDirectional.only(top: 6, bottom: 6),
            child: Divider(
              color: Color(0x173C3C43),
              height: 0.5,
            ),
          ),
          Container(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Text(
                  widget.logic.getI18nString('app100001511', '垂直偏移(mm)'),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                ),
                Expanded(child: Container()),
                Container(
                  width: 53,
                  height: 32,
                  padding: EdgeInsetsDirectional.symmetric(horizontal: 3),
                  alignment: Alignment.center,
                  child: KeyboardActions(
                    config: _buildConfig(),
                    child: CupertinoTextField(
                      controller: ofsetYController,
                      focusNode: nodeY,
                      textInputAction: TextInputAction.done,
                      textDirection: TextDirection.ltr,
                      textAlign: TextAlign.center,
                      inputFormatters: [
                        _OffsetNumberFormatter(),
                      ],
                      scrollPadding: EdgeInsets.all(0),
                      keyboardType: const TextInputType.numberWithOptions(signed: true, decimal: true),
                      style: const TextStyle(color: Color(0xFF262626), fontSize: 14, fontWeight: FontWeight.w400),
                      decoration: BoxDecoration(
                        color: Color(0x14747480),
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                      ),
                      onChanged: (value) {
                        double parsed = 0.0;
                        if (value.isEmpty || value == '-' || value == '.' || value == '-.') {
                          parsed = 0.0;
                        } else {
                          final double? v = double.tryParse(value);
                          if (v == null) {
                            return;
                          }
                          parsed = v;
                        }
                        if (parsed > 5.0) {
                          parsed = 5.0;
                          const String text = '5';
                          ofsetYController?.value = TextEditingValue(
                            text: text,
                            selection: TextSelection.collapsed(offset: text.length),
                          );
                        } else if (parsed < -5.0) {
                          parsed = -5.0;
                          const String text = '-5';
                          ofsetYController?.value = TextEditingValue(
                            text: text,
                            selection: TextSelection.collapsed(offset: text.length),
                          );
                        }
                        ofsetY = parsed;
                        offset = Offset(offset.dx, ofsetY / inputScale);
                        offetSetter(() {});
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsetsDirectional.only(top: 6, bottom: 6),
            child: Divider(
              color: const Color(0xFF3C3C43).withOpacity(0.09),
              height: 0.5,
            ),
          ),
          Container(
            height: 242,
            color: const Color(0x00ffffff),  
            margin: const EdgeInsets.only(top: 20,bottom: 16,left: 16,right: 16),
            child: StatefulBuilder(builder: (ctx, setter) {
              offetSetter = setter;
              return Stack(
                children: [
                  Align(
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                        onTap: () {
                          offsetAction(OffsetType.left);
                        },
                        child:  SvgIcon('assets/off_set_left.svg', width: 44, height: 44,color: getOffsetDx <= -maxOffset ?
                         const Color(0xFFBFBFBF) :const Color(0xFF262626),)),
                  ),
                  Align(
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                        onTap: () {
                          offsetAction(OffsetType.right);
                        },
                        child:  SvgIcon('assets/off_set_right.svg', width: 44, height: 44,
                          color: getOffsetDx >= maxOffset ? const Color(0xFFBFBFBF) :const Color(0xFF262626),
                        )),
                  ),
                  Align(
                    alignment: Alignment.topCenter,
                    child: GestureDetector(
                        onTap: () {
                          offsetAction(OffsetType.top);
                        },
                        child:  SvgIcon('assets/off_set_up.svg', width: 44, height: 44,
                          color: getOffsetDy <= -maxOffset ? const Color(0xFFBFBFBF) :const Color(0xFF262626),
                        )),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: GestureDetector(
                        onTap: () {
                          offsetAction(OffsetType.bottom);
                        },
                        child:  SvgIcon('assets/off_set_down.svg', width: 44, height: 44,
                          color: getOffsetDy >= maxOffset ? const Color(0xFFBFBFBF) :const Color(0xFF262626),
                        )),
                  ),
                  const Align(
                    alignment: Alignment.center,
                    child: SvgIcon('assets/off_set_preview1.svg', width: 170, height: 102),
                  ),
                  Align(
                    alignment: Alignment(offset.dx * 0.4, offset.dy * 0.4),
                    child: const SvgIcon('assets/off_set_preview.svg', width: 170, height: 102),
                  )
                ],
              );
            }),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(widget.logic.getI18nString('app01137', '出纸方向'),style: TextStyle(color: const Color(0xFF3C3C43).withOpacity(0.3),
               fontSize: 12, fontWeight: FontWeight.w400),),
              const SizedBox(
                width: 2,
              ),
              Transform.rotate(
                angle: -(widget.logic.parameter!.templateMoudle!["rotate"].toDouble()) * (math.pi / 180),
                child: SvgIcon(
                  widget.logic.style.paperArrowIcon,
                  matchTextDirection: true,
                  color: const Color(0xFF3C3C43).withOpacity(0.3),
                ),
              )
            ],
          ),
          const SizedBox(
            height: 8,
          )
        ],
      ),
    );
  }

  double get getOffsetDx => double.parse(offset.dx.toStringAsFixed(3));

  double get getOffsetDy => double.parse(offset.dy.toStringAsFixed(3));

  offsetAction(OffsetType type) {
    shouldPopAfterDelay = false;
    bool isNeedHapticFeedback = false;
    switch (type) {
      case OffsetType.left:
        widget.logic.channel.trackEvent("click", "024_459_462", eventData: {"type": 3,"device_name": widget.logic.style.connectTitle});
        offset -= const Offset(0.05, 0);
        if (getOffsetDx < -maxOffset) {
          offset = Offset(-maxOffset, offset.dy);
        } else {
          isNeedHapticFeedback = true;
        }
        break;
      case OffsetType.right:
        widget.logic.channel.trackEvent("click", "024_459_462", eventData: {"type": 4,"device_name": widget.logic.style.connectTitle});
        offset += const Offset(0.05, 0);
        if (getOffsetDx > maxOffset) {
          offset = Offset(maxOffset, offset.dy);
        } else {
          isNeedHapticFeedback = true;
        }
        break;
      case OffsetType.top:
        widget.logic.channel.trackEvent("click", "024_459_462", eventData: {"type": 1,"device_name": widget.logic.style.connectTitle});
        offset -= const Offset(0, 0.05);
        if (getOffsetDy < -maxOffset) {
          offset = Offset(offset.dx, -maxOffset);
        } else {
          isNeedHapticFeedback = true;
        }
        break;
      case OffsetType.bottom:
        widget.logic.channel.trackEvent("click", "024_459_462", eventData: {"type": 2,"device_name": widget.logic.style.connectTitle});
        offset += const Offset(0, 0.05);
        if (getOffsetDy > maxOffset) {
          offset = Offset(offset.dx, maxOffset);
        } else {
          isNeedHapticFeedback = true;
        }
        break;
    }
    if (isNeedHapticFeedback) {
      HapticFeedback.heavyImpact();
    }
    FocusNode().requestFocus();
    offetSetter(() {});
    var hText = (getOffsetDx * inputScale).toStringAsFixed(1);
    if (hText == '-0.0') hText = '0.0';
    ofsetXController?.text = hText;

    var vText = (getOffsetDy * inputScale).toStringAsFixed(1);
    if (vText == '-0.0') vText = '0.0';
    ofsetYController?.text = vText;
  }
}
