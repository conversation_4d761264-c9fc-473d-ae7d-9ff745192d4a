//
//  JCExcelListViewController.m
//  Runner
//
//  Created by <PERSON><PERSON><PERSON> on 2018/6/13.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//
#import "JCVIPDetailViewController.h"
#import "JCExcelListViewController.h"
#import "JCExcelListTableViewCell.h"
#import "JCExcelInfoModel.h"
#import "JCExcelDetailViewController.h"
#import "JCExcelColNameSelectViewController.h"
#import "JCExcelDataBindViewController.h"
#import "JCNoDataView.h"
#import "JCExcelZDBindView.h"
#import "ZDSTransparentView.h"
#import "JCExcelZDBindView2.h"
#import "JCExcelDetailModel.h"
#import "JCExcelForElement.h"
#import "JCExcelEmptyView.h"
#import "JCExcelFileImportView.h"
#import "JCThirdAppTool.h"
#import "NSData+dataUpload.h"
#import "UIImage+Tool.h"
@interface JCExcelListViewController ()<GroupShadowTableViewDelegate,GroupShadowTableViewDataSource,ZDSTransparentViewDelegate,UIDocumentPickerDelegate>
@property (weak, nonatomic) IBOutlet NSLayoutConstraint *bottomSpaceConstraint;
@property (nonatomic, strong) UIButton *deleteBtn;
@property (strong,nonatomic) NSMutableArray *fileInfoModelsArr;
@property (nonatomic, strong) NSMutableArray *excelItemArr;
@property (nonatomic, strong) NSMutableArray *selectExcelColValueArr;
@property (nonatomic, strong) JCExcelZDBindView *excelZDBindView;
@property (nonatomic, strong) ZDSTransparentView *columnSelectView;
@property (strong ,nonatomic) JCNoDataView *noDataView;
@property(nonatomic,strong) JCExcelEmptyView *emptyView;
@property (nonatomic, strong) NSMutableArray *columnValueArr;
@property (nonatomic, strong) UILabel *selectColNameLabel;
@property (nonatomic, assign) NSInteger fileListState;   // 0 正常  1 编辑
@property (nonatomic, copy)   XYMultipleBlock block;
@property (nonatomic, copy)   XYBlock1 selectFileBlock;
@property (nonatomic, copy)   XYBlock useTilteBlock;
@property (nonatomic, strong) NSOperationQueue *excelOperationQueue;
@property (nonatomic, strong) JCExcelFileImportView *excelImportView;
@property (nonatomic, strong) UIButton *rightButton;
@property (nonatomic, strong) UIButton *leftButton;
@property (nonatomic, assign) BOOL allFile;
@property (nonatomic, assign) NSInteger pageNumber;   // 页码 默认 1
@end

@implementation JCExcelListViewController

- (void)viewDidLoad {
    // 默认500kb
    self.fileLimitKb = 500;
    [super viewDidLoad];
    self.title = XY_LANGUAGE_TITLE_NAMED(@"app00200", @"我的文件");
    self.view.backgroundColor = HEX_RGB(0xF7F7F7);
    _excelOperationQueue = [[NSOperationQueue alloc] init];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(fileNotification:) name:JCNOTICATION_EXCEL_UPLOAD object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(excelDetailLoadFinish) name:JCNOTICATION_EXCEL_DETAIL_DONE object:nil];
    // Do any additional setup after loading the view from its nib.
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
}

- (instancetype)initWithBlock:(XYMultipleBlock)block
{
    self = [super init];
    if (self) {
        self.allFile = NO;
        self.block = block;
        self.fileListState = 0;
    }
    return self;
}

- (instancetype)initWithSelectFileBlock:(XYBlock1)selectFileBlock{
    self = [super init];
    if (self) {
        self.allFile = YES;
        self.selectFileBlock = selectFileBlock;
        self.pageNumber = 1;
    }
    return self;
}

- (instancetype)initWithBlock:(XYMultipleBlock)block useTilteBlock:(XYBlock)useTilteBlock
{
    self = [super init];
    if (self) {
        self.allFile = NO;
        self.block = block;
        self.useTilteBlock = useTilteBlock;
        self.fileListState = 0;
    }
    return self;
}

- (void)dealloc{
    NSLog(@"excelList dealloc");
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)initUI{
    XYWeakSelf
    [super initUI];
    [self initNoDataView];
    [self initBottomView];
    CGFloat navHeight = iPhoneX?88:64;
    float bottomViewHeight = self.excelImportView.height;
    [self.noDataView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.top.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - navHeight-bottomViewHeight);
    }];
    [self.groupShadowTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.top.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - navHeight-bottomViewHeight);
    }];
    [self.excelImportView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.bottom.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(bottomViewHeight);
    }];
}

- (void)initBottomView{
    XYWeakSelf
    float navHeight = iPhoneX?88:64;
    float tipHeight = 0;
//    if(self.allFile){
//        tipHeight = [XY_LANGUAGE_TITLE_NAMED(@"", @"其他应用导入：文件请勿超过300KB其他应用导入：文件请勿超过300KB其他应用导入：文件请勿超过300KB其他应用导入：文件请勿超过300KB") jk_sizeWithFont:[UIFont systemFontOfSize:14] constrainedToWidth:SCREEN_WIDTH - 30].height;
//    }else{
//        tipHeight = [XY_LANGUAGE_TITLE_NAMED(@"app01242", @"文件限制300KB") jk_sizeWithFont:[UIFont systemFontOfSize:14] constrainedToWidth:SCREEN_WIDTH - 30].height;
//    }
   NSString *fileLimitKb = [NSString stringWithFormat:@"%ld",(long)self.fileLimitKb];
   tipHeight = [XY_LANGUAGE_TITLE_NAMED_WITH_PLACEHOLDERS(@"app100002081", @"文件限制$KB",@[fileLimitKb]) jk_sizeWithFont:[UIFont systemFontOfSize:14] constrainedToWidth:SCREEN_WIDTH - 30].height;
    float bottomViewHeight = (iPhoneX?140:124) + tipHeight;
    NSArray *thirdAppCanOpenArr = [JCThirdAppTool thirdAppExcelCanOpen];
    if(thirdAppCanOpenArr.count == 0){
        bottomViewHeight = bottomViewHeight - 24;
    }
    JCExcelFileImportView *excelImportView = [[JCExcelFileImportView alloc] initWithFrame:CGRectMake(0, SCREEN_HEIGHT - navHeight - bottomViewHeight, SCREEN_WIDTH, bottomViewHeight) thirdAppArr:thirdAppCanOpenArr isAllFile:self.allFile limitKb:self.fileLimitKb];
    [excelImportView setOpenThirdAppBlock:^(NSNumber *selectIndex) {
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        JCThirdAppInfo *thirdAppInfo = [thirdAppCanOpenArr safeObjectAtIndex:selectIndex.integerValue];
        NSURL *thirdUrl = [NSURL URLWithString:thirdAppInfo.openUrl];
        [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {}];
        [weakSelf openThirdAppEvent:thirdAppCanOpenArr appIndex:selectIndex.integerValue];
    }];
    [excelImportView setOpenLocalFileBlock:^(id x) {
        if(NETWORK_STATE_ERROR){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
        [weakSelf openLocalFile];
    }];
    [excelImportView setOpenVipBlock:^{
//        [JCIAPHelper openViewWithAlert:self needOpenTip:NO success:^{
//
//        } failure:^(NSString *msg, id model) {
//
//        } sourceInfo:@{@"sourcePage":@"025"}];
        JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
//        vc.sourcePage = @"006";
        [weakSelf.navigationController pushViewController:vc animated:YES];
    }];
    self.excelImportView = excelImportView;
    [self.view addSubview:excelImportView];
    [self.view addSubview:self.deleteBtn];


}

- (void)initNavigationBar{
    UIButton *rightButton = [UIButton buttonWithType:UIButtonTypeCustom];
    rightButton.frame = CGRectMake(0, 0, 90, 44);
    [rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00336", @"编辑") forState:UIControlStateNormal];
    rightButton.titleLabel.font = MY_FONT_Bold(17);
    [rightButton setTitleColor:HEX_RGB(0x333333) forState:UIControlStateNormal];
    rightButton.titleLabel.numberOfLines = 2;
    rightButton.titleLabel.lineBreakMode = NSLineBreakByWordWrapping;

    // 强制设置 auto layout 否则大小可能仍不正确
    [rightButton.widthAnchor constraintEqualToConstant:90].active = YES;
    [rightButton.heightAnchor constraintEqualToConstant:44].active = YES;
    [self showBarButton:NAV_RIGHT button:rightButton];
    self.rightButton = rightButton;
    
    UIButton *leftButton = [UIButton buttonWithType:UIButtonTypeCustom];
    leftButton.frame = CGRectMake(0, 0, 80, 44);
    leftButton.titleLabel.font = MY_FONT_Regular(17);
    [leftButton setTitleColor:HEX_RGB(0x333333) forState:UIControlStateNormal];
    UIImage * backImage = [XY_IMAGE_NAMED(@"new_nav_back") jc_imageFlippedForRightToLeftLayoutDirectionWithIsRTL:self.view.isRTL];
    [leftButton setImage:backImage forState:UIControlStateNormal];
    
    [self showBarButton:NAV_LEFT button:leftButton];
    self.leftButton = leftButton;
}

- (UIButton *)deleteBtn{
    if(_deleteBtn == nil){
         float navHeight = iPhoneX?88:64;
         float bottomViewHeight = iPhoneX?94:80;
        _deleteBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        _deleteBtn.frame = CGRectMake(15, SCREEN_HEIGHT - navHeight - bottomViewHeight + 10, SCREEN_WIDTH - 30, 50);
        _deleteBtn.backgroundColor = COLOR_NEW_THEME;
        _deleteBtn.layer.cornerRadius = 6;
        _deleteBtn.hidden = YES;
        [_deleteBtn setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00063",@"删除") forState:UIControlStateNormal];
        [_deleteBtn addTarget:self action:@selector(deleteExcelFiles) forControlEvents:UIControlEventTouchUpInside];
    }
    return _deleteBtn;
}

- (void)initNet{
    [super initNet];
    if(self.allFile){
        [self http];
    }else{
        NSString *where = [NSString stringWithFormat:@"where is_deleted != '1' and user_id = '%@'",m_userModel.userid];
        NSArray *fileInfoModelsArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_EXCELINFO dicOrModel:[JCExcelInfoModel class] whereFormat:where];
        if(fileInfoModelsArr.count > 0){
            self.fileInfoModelsArr = fileInfoModelsArr.mutableCopy;
            self.excelImportView.hidden = NO;
            [self refreshOperateState:1];
            [self.groupShadowTableView reloadData];
        }
        if(NETWORK_STATE_ERROR){
            if(self.fileInfoModelsArr.count == 0){
                [self refreshOperateState:1];
            }
        }else{
            [self http];
        }
    }
}

- (void)initNoDataView{
    XYWeakSelf
    _noDataView = [JCNoDataView xy_xib];
    _noDataView.backgroundColor = [UIColor clearColor];
    [_noDataView setOperateBlock:^{
        [weakSelf http];
    }];
    [self.view addSubview:_noDataView];
}

- (void)http{
    XYWeakSelf
    if(self.allFile){
        NSString *graphqlStr = [NSString stringWithFormat:@"query GET_DYNAMIC_QRCODE_FILE_LIST($page: Float = %ld, $pageSize: Float = 10) { dynamicQRCodeFileList(page: $page, pageSize: $pageSize) { id fileName size fileFormat url canConvert previewUri }}",self.pageNumber];
        [@{} jc_graphQLRequestWith:graphqlStr hud:@"" graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            [self.groupShadowTableView.mj_header endRefreshing];
            [self.groupShadowTableView.mj_footer endRefreshing];
            if(requestDic != nil && [requestDic isKindOfClass:[NSDictionary class]]){
                NSArray *infoList = requestDic[@"dynamicQRCodeFileList"];
                if(infoList.count > 0){
                    NSMutableArray *infoArr = [NSMutableArray array];
                    for (NSDictionary *infoDic in infoList) {
                        JCLiveCodeFileInfoModel *infoModel = [[JCLiveCodeFileInfoModel alloc] initWithDictionary:infoDic error:nil];
                        [infoArr addObject:infoModel];
                    }
                    if(self.pageNumber == 1){
                        self.fileInfoModelsArr = [NSMutableArray arrayWithArray:infoArr];
                    }else{
                        [self.fileInfoModelsArr addObjectsFromArray:infoArr];
                    }
                    if(infoList.count == 10){
                        self.pageNumber ++ ;
                    }
                }
                self.groupShadowTableView.mj_footer.hidden = infoList.count < 10;
            }
            [weakSelf refreshOperateState:1];
            [weakSelf.groupShadowTableView reloadData];
            weakSelf.excelImportView.hidden = NO;
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    }else{
        [self java_postWithValues:@{@"appCode":@"1",@"page":@"1",@"limit":@"200"} ModelType:[JCExcelInfoModel class] Path:J_file_my hud:@"" Success:^(__kindof YTKBaseRequest *request, JCExcelInfoModel *model) {
            weakSelf.noDataView.hidden = YES;
            weakSelf.fileInfoModelsArr = model.list.mutableCopy;
            for (JCExcelInfoModel *singleModel in weakSelf.fileInfoModelsArr) {
                singleModel.user_id = m_userModel.userid;
            }
            //        [weakSelf loadExcelDetailInfo:weakSelf.fileInfoModelsArr];
            [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_EXCELINFO whereFormat:@""];
            [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_EXCELINFO dicOrModelArray:weakSelf.fileInfoModelsArr];
            [weakSelf refreshOperateState:1];
            [weakSelf.groupShadowTableView reloadData];
            weakSelf.excelImportView.hidden = NO;
        } failure:^(NSString *msg, NSString *requestCode) {
            if(requestCode.integerValue == 401){
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            }else{
                [weakSelf refreshOperateState:0];
            }
        }];
    }
}



- (void)refreshOperateState:(NSInteger)netRequestState{
     self.emptyView.hidden = YES;
    if(netRequestState == 1){
        if(self.fileInfoModelsArr.count > 0){
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00336", @"编辑") forState:UIControlStateNormal];
            [self showBarButton:NAV_RIGHT button:self.rightButton];
        }else{
            self.fileListState = 0;
            self.excelImportView.hidden = NO;
            self.deleteBtn.hidden = YES;
            self.deleteBtn.alpha = 1;
            [self hiddenNavBarButton:1];
            [self.leftButton setImage:[XY_IMAGE_NAMED(@"new_nav_back") jc_imageFlippedForRightToLeftLayoutDirectionWithIsRTL:self.view.isRTL] forState:UIControlStateNormal];
            [self.leftButton setTitle:@"" forState:UIControlStateNormal];
            [self showBarButton:NAV_LEFT button:self.leftButton];
            self.emptyView.hidden = NO;
            [self resetGroupTableFrameWith:NO];
        }
    }else{
        if(self.fileInfoModelsArr.count == 0){
            [self.noDataView showViewWithImage:@"ic_qsy_wangluyichang" tipTitle:XY_LANGUAGE_TITLE_NAMED(@"app00182",@"网络异常，刷新试试") buttonName:XY_LANGUAGE_TITLE_NAMED(@"app01147",@"刷新")];
        }
    }
}
- (void)rightButtonTouch:(UIButton *)sender
{
    if(self.fileListState == 0){
        [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00506", @"全选") forState:UIControlStateNormal];
        [self showBarButton:NAV_RIGHT button:self.rightButton];
        
        [self.leftButton setImage:[UIImage new] forState:UIControlStateNormal];
        [self.leftButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030", @"取消") forState:UIControlStateNormal];
        [self showBarButton:NAV_LEFT button:self.leftButton];
        self.fileListState = 1;
        self.excelImportView.hidden = YES;
        self.deleteBtn.hidden = NO;
        self.deleteBtn.alpha = 0.6;
        [self resetGroupTableFrameWith:YES];
        [self.groupShadowTableView reloadData];
    }else if(self.fileListState == 1){
        BOOL isAllSelected = YES;
        for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
            if([infoModel.isSelected isEqualToString:@"0"]){
                isAllSelected = NO;
            }
        }
        if(isAllSelected){
            for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
                infoModel.isSelected = @"0";
            }
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00506", @"全选") forState:UIControlStateNormal];
            [self showBarButton:NAV_RIGHT button:self.rightButton];
            self.deleteBtn.alpha = 0.6;
            self.deleteBtn.enabled = NO;
        }else{
            for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
                infoModel.isSelected = @"1";
            }
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01328", @"取消全选") forState:UIControlStateNormal];
            [self showBarButton:NAV_RIGHT button:self.rightButton];
            self.deleteBtn.alpha = 1;
            self.deleteBtn.enabled = YES;
        }

        [self.groupShadowTableView reloadData];
    }
}

- (void)leftButtonTouch:(UIButton *)sender{
    if(self.fileListState != 0){
        self.fileListState = 0;
        for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
            infoModel.isSelected = @"0";
        }
        [self.groupShadowTableView reloadData];
        self.excelImportView.hidden = NO;
        self.deleteBtn.hidden = YES;
        self.deleteBtn.alpha = 1;
        [self.leftButton setImage:[XY_IMAGE_NAMED(@"new_nav_back") jc_imageFlippedForRightToLeftLayoutDirectionWithIsRTL:self.view.isRTL] forState:UIControlStateNormal];
        [self.leftButton setTitle:@"" forState:UIControlStateNormal];
        [self showBarButton:NAV_LEFT button:self.leftButton];
        [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00336", @"编辑") forState:UIControlStateNormal];
        [self showBarButton:NAV_RIGHT button:self.rightButton];
        [self resetGroupTableFrameWith:NO];
        return;
    }
    [super leftButtonTouch:sender];

}

- (void)resetGroupTableFrameWith:(BOOL) isEditStatus{
    CGFloat navHeight = iPhoneX?88:64;
    float bottomHeight = iPhoneX?164:150;
    if(isEditStatus){
        bottomHeight = iPhoneX?125:110;
    }
    XYWeakSelf
    [self.groupShadowTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.view).offset(0);
        make.top.equalTo(weakSelf.view).offset(0);
        make.trailing.equalTo(weakSelf.view).offset(0);
        make.height.mas_equalTo(SCREEN_HEIGHT - navHeight-bottomHeight);
    }];
}

- (IBAction)excelImportEvent:(UIButton *)sender {
    XYWeakSelf
    if(sender.tag == 1){
        [self openWeixin];
    }else if(sender.tag == 2){
        [self openQQ];
    } else{
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01090",@"是否确认要删除") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00063",@"删除") cancelBlock:nil sureBlock:^{
            [weakSelf deleteExcelFiles];
        }];
    }
}

/**
 *  需要在info里面添加 LSApplicationQueriesSchemes字段
 */
- (void)openQQ
{
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    NSURL *url = [NSURL URLWithString:@"mqq://"];
    if([[UIApplication sharedApplication] canOpenURL:url]){
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
    } else {
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00311",@"您没有安装手机QQ，请安装手机QQ后重试。") cancelButtonTitle:nil sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048",@"确定") cancelBlock:^{
                        
        } sureBlock:^{
                        
        }];
    }
}

/**
 *  打开微信 , 没有配置
 */
- (void)openWeixin
{
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
        return;
    }
    NSURL *url = [NSURL URLWithString:@"weixin://"];
    if([[UIApplication sharedApplication] canOpenURL:url]){
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
    } else {
        [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00312",@"您没有安装手机微信，请安装手机微信后重试。") cancelButtonTitle:nil sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00048",@"确定") cancelBlock:^{
                        
        } sureBlock:^{
                        
        }];
    }
}

- (void)deleteExcelFiles{
    //删除excel文件
    XYWeakSelf
    if(self.allFile){
        NSString *deleteFiles = @"";
        NSMutableArray *deleteArr = [NSMutableArray array];
        for(JCLiveCodeFileInfoModel *infoModel in self.fileInfoModelsArr){
            if([infoModel.isSelected isEqualToString:@"1"]){
                [deleteArr addObject:infoModel.codeId];
            }
        }
        if(deleteArr.count > 0){
            deleteFiles = [deleteArr componentsJoinedByString:@","];
        }
        
    }else{
        NSString *deleteFiles = @"";
        for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
            if([infoModel.isSelected isEqualToString:@"1"]){
                infoModel.is_deleted = @"1";
                [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_EXCELINFO dicOrModel:infoModel whereFormat:[NSString stringWithFormat:@"where xyid = '%@'",infoModel.xyid]];
                if([deleteFiles isEqualToString:@""]){
                    deleteFiles = infoModel.xyid;
                }else{
                    deleteFiles = [NSString stringWithFormat:@"%@,%@",deleteFiles,infoModel.xyid];
                }
            }
        }
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01188",@"删除成功")];
        [self.fileInfoModelsArr enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(JCExcelInfoModel*  _Nonnull infoModel, NSUInteger idx, BOOL * _Nonnull stop) {
            if([infoModel.isSelected isEqualToString:@"1"]){
                [weakSelf.fileInfoModelsArr removeObject:infoModel];
            }
        }];
        if(weakSelf.fileInfoModelsArr.count == 0){
            [weakSelf refreshOperateState:1];
        }
        [weakSelf.groupShadowTableView reloadData];
        if(jc_is_connected_cellular_network || jc_is_connected_wifi){
    #pragma java
            [self java_postWithValues:@{@"idList":UN_NIL(deleteFiles)} ModelType:nil Path:J_file_delete hud:nil Success:^(__kindof YTKBaseRequest *request, id model) {
                [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_EXCELINFO whereFormat:@"where is_delete = '1'"];
            } failure:^(NSString *msg, id model) {
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            }];

        }
    }
}

- (void)initTableView{
    XYWeakSelf
    [super initTableView];
    self.groupShadowTableView.groupShadowDelegate = self;
    self.groupShadowTableView.groupShadowDataSource = self;
    self.groupShadowTableView.showSeparator = YES;
    self.groupShadowTableView.isCanEdit = YES;
    self.groupShadowTableView.editActionArr = @[XY_LANGUAGE_TITLE_NAMED(@"app00063",@"删除"),XY_LANGUAGE_TITLE_NAMED(@"app01102",@"预览")];
    self.groupShadowTableView.separatorInset = UIEdgeInsetsZero;
    [self.groupShadowTableView registerNib:@"JCExcelListTableViewCell"];
    if(self.allFile){
        self.groupShadowTableView.mj_header = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            weakSelf.pageNumber = 1;
            [weakSelf http];
        }];
        self.groupShadowTableView.mj_footer = [MJRefreshBackNormalFooter footerWithRefreshingBlock:^{
            NSLog(@"上啦加载111");
            [weakSelf http];
        }];
    }
}

#pragma mark -tableViewDelegate

- (NSInteger)numberOfSectionsInGroupShadowTableView:(GroupShadowTableView *)tableView{
    return 1;
}

- (NSInteger)groupShadowTableView:(GroupShadowTableView *)tableView numberOfRowsInSection:(NSInteger)section{
    if(self.fileInfoModelsArr.count == 0){
        return 0;
    }
    if(section == 0){
        return self.fileInfoModelsArr.count;
    }
    return 1;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath{
    return 63;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 15.f;
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForHeaderInSection:(NSInteger)section{
    UIView *headerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    return headerView;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 15.f;
}

- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 15)];
    return footerView;
}

- (UITableViewCell *)groupShadowTableView:(GroupShadowTableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath{
    XYWeakSelf
    id infoModel = [self.fileInfoModelsArr safeObjectAtIndex:indexPath.row];
    JCExcelListTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"JCExcelListTableViewCell"];
    cell.clickCellBtnBlock = ^(id x) {
        if(weakSelf.allFile){
            ((JCLiveCodeFileInfoModel *)infoModel).isSelected = x;
        }else{
            ((JCExcelInfoModel *)infoModel).isSelected = x;
        }
        [weakSelf refreshSelectedState];
    };
    if(self.improtFrom == 4){
        cell.selectionStyle = UITableViewCellSelectionStyleNone;
    }
    [cell setModel:infoModel editState:(self.fileListState == 0 ? NO:YES)];
    return cell;
}

- (void)groupShadowTableView:(GroupShadowTableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath{
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    if(indexPath.section == 1 || self.improtFrom == 4){
        return ;
    }
    JCExcelInfoModel *infoModel = [self.fileInfoModelsArr safeObjectAtIndex:indexPath.row];
    if(NETWORK_STATE_ERROR){
        NSString *excelDataFilePath = [NSString stringWithFormat:@"%@/%@.txt",RESOURCE_EXCEL_PATH,infoModel.xyid];
        if(![[NSFileManager defaultManager] fileExistsAtPath:excelDataFilePath]){
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
            return;
        }
    }
    [self showExcelBindViewWithExcelId:infoModel];
}

- (void)groupShadowTableView:(UITableViewRowAction *)action editRowAtIndexPath:(NSIndexPath *)indexPath{
    id infoModel = [self.fileInfoModelsArr safeObjectAtIndex:indexPath.row];
    if([action.title isEqualToString:XY_LANGUAGE_TITLE_NAMED(@"app00063", @"删除")]){
        //删除
        [self excelOperateEventWithIndex:infoModel operateType:2 indexPath:indexPath];
    }else{
        //c预览
        [self excelOperateEventWithIndex:infoModel operateType:1 indexPath:indexPath];
    }
    [self.groupShadowTableView reloadData];
}

- (void)loadExcelDetalWithFileId:(NSString *)idString{

}

- (void)refreshSelectedState{
    if(self.fileListState == 1){
        BOOL isSelectedAll = YES;
        BOOL isUnSelectedAll = YES;
        for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
            if([infoModel.isSelected isEqualToString:@"0"]){
                isSelectedAll = NO;
            }
            if([infoModel.isSelected isEqualToString:@"1"]){
                isUnSelectedAll = NO;
            }
        }
        if(!isSelectedAll && !isUnSelectedAll){
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00506", @"全选") forState:UIControlStateNormal];
            self.deleteBtn.alpha = 1;
            self.deleteBtn.enabled = YES;
        }else if(isUnSelectedAll){
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app00506", @"全选") forState:UIControlStateNormal];
            self.deleteBtn.alpha = 0.6;
            self.deleteBtn.enabled = NO;
        }else{
            [self.rightButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app01328", @"取消全选") forState:UIControlStateNormal];
            self.deleteBtn.alpha = 1;
            self.deleteBtn.enabled = YES;
        }
        [self showBarButton:NAV_RIGHT button:self.rightButton];
    }
}

- (void)excelOperateEventWithIndex:(id)model operateType:(NSInteger )operateType indexPath:(NSIndexPath *)indexPath{
    XYWeakSelf
    if(self.allFile){
        JCLiveCodeFileInfoModel *infoModel = model;
        if(operateType == 1){
            if(STR_IS_NIL(infoModel.previewUri)) {
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"", @"无法预览该文件")];
                return;
            }
            XYWKWebViewController *vc = [[XYWKWebViewController alloc] initWithUrl:infoModel.previewUri];
            vc.showWebTitle = YES;
            [self.navigationController pushViewController:vc animated:YES];
        }else{
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01090",@"确定要删除该文件吗？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00063",@"删除") cancelBlock:nil sureBlock:^{
                [weakSelf deleteExcelWithExcelId:infoModel.codeId indexPath:indexPath];
            }];
        }
    }else{
        JCExcelInfoModel *infoModel = model;
        if(operateType == 1){
            JCExcelDetailViewController *vc = [[JCExcelDetailViewController alloc] init];
            vc.excelFileId = infoModel.xyid;
            [self.navigationController pushViewController:vc animated:YES];
        }else{
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app01090",@"确定要删除该文件吗？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00063",@"删除") cancelBlock:nil sureBlock:^{
                [weakSelf deleteExcelWithExcelId:infoModel.xyid indexPath:indexPath];
            }];
        }
    }
}

- (void)deleteExcelWithExcelId:(NSString *)excelId indexPath:(NSIndexPath *)indexPath{
    XYWeakSelf
    if(self.allFile){
        NSString *graphqlStr = @"mutation deleteDynamicQRCodeFile($id: String!) { deleteDynamicQRCodeFile(id:$id)}";
        [@{@"id":UN_NIL(excelId)} jc_graphQLRequestWith:graphqlStr hud:@"" graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
            if([requestDic isKindOfClass:[NSDictionary class]]){
                NSNumber *resultNumber = requestDic[@"deleteDynamicQRCodeFile"];
                if(resultNumber.boolValue){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01188",@"删除成功")];
                    for (JCLiveCodeFileInfoModel *infoModel in self.fileInfoModelsArr) {
                        if([infoModel.codeId isEqualToString:excelId]){
                            [self.fileInfoModelsArr removeObject:infoModel];
                            break;
                        }
                    }
                    [self.groupShadowTableView reloadData];
                }
            }
        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    }else{
        for(JCExcelInfoModel *infoModel in self.fileInfoModelsArr){
            if([infoModel.xyid isEqualToString:excelId]){
                infoModel.is_deleted = @"1";
                [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_EXCELINFO dicOrModel:infoModel whereFormat:[NSString stringWithFormat:@"where xyid = '%@'",infoModel.xyid]];
            }
        }
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01188",@"删除成功")];
        [self.fileInfoModelsArr enumerateObjectsWithOptions:NSEnumerationReverse usingBlock:^(JCExcelInfoModel*  _Nonnull infoModel, NSUInteger idx, BOOL * _Nonnull stop) {
            if([infoModel.xyid isEqualToString:excelId]){
                [weakSelf.fileInfoModelsArr removeObject:infoModel];
            }
        }];
        if(weakSelf.fileInfoModelsArr.count == 0){
            [weakSelf refreshOperateState:1];
        }
        [weakSelf refreshSelectedState];
        [weakSelf.groupShadowTableView reloadData];
        if(jc_is_connected_cellular_network || jc_is_connected_wifi){
            [self java_postWithValues:@{@"idList":UN_NIL(excelId)} ModelType:nil Path:J_file_delete hud:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
                [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_EXCELINFO whereFormat:@"where is_delete = '1'"];
            } failure:^(NSString *msg, id model) {
                [MBProgressHUD showToastWithMessageDarkColor:msg];
            }];

        }
    }
}

- (void)showExcelBindViewWithExcelId:(JCExcelInfoModel *)model{
    XYWeakSelf
    if(self.improtFrom == 0){
        JCExcelDataBindViewController *vc = [[JCExcelDataBindViewController alloc] initWithBlock:^(id x, id y,id z) {
            weakSelf.block(x, y,z);
        } useTilteBlock:self.useTilteBlock];
        vc.isUserTitle = self.isUserTitle;
        vc.excelFileId = model.xyid;
        vc.excelFileName = model.file_name;
        [self.navigationController pushViewController:vc animated:YES];
    }else{
        [self  getExcelinfoWith:model];
    }
}

- (void)getExcelinfoWith:(JCExcelInfoModel *)model{
    XYWeakSelf
    NSFileManager *fm = [NSFileManager defaultManager];
    NSString *excelPath = RESOURCE_EXCEL_PATH;
    if(![fm fileExistsAtPath:excelPath]){
        [fm createDirectoryAtPath:excelPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    NSString *excelDataFilePath = [NSString stringWithFormat:@"%@/%@.txt",excelPath,model.xyid];
    if([fm fileExistsAtPath:excelDataFilePath]){
        NSString *dataString = [NSString stringWithContentsOfFile:excelDataFilePath encoding:NSUTF8StringEncoding error:nil];
        if(dataString.length > 0){
            JCExcelDetailModel *detailModel = [[JCExcelDetailModel alloc] initWithString:dataString error:nil];
            weakSelf.columnValueArr = detailModel.list.mutableCopy;
            NSArray *colNameArr = [detailModel.list safeObjectAtIndex:0];
            ZDSTransparentView *columnSelectView = [[ZDSTransparentView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) dataArr:colNameArr type:self.improtFrom];
            columnSelectView.delegate = self;
            columnSelectView.style = ZDSStyleBlack;
            columnSelectView.excelFileName = model.file_name;
            columnSelectView.excelId = model.xyid;
            columnSelectView.excelArrInfo = detailModel.list;
            columnSelectView.backgroundColor = [UIColor colorWithRed:0/255 green:0/255 blue:0/255 alpha:0.5];
            self.columnSelectView = columnSelectView;
            [self.columnSelectView open:nil];
        }

    }else{
        [self java_postWithValues:@{@"idList":UN_NIL(model.xyid)} ModelType:[JCExcelDetailModel class] Path:J_file_data hud:@"" timeoutInterval:20 Success:^(__kindof YTKBaseRequest *request, NSArray *modelArr) {
            if(modelArr.count > 0){
                JCExcelDetailModel *model1 = modelArr[0];
                weakSelf.columnValueArr = model1.list.mutableCopy;
                NSArray *colNameArr = [model1.list safeObjectAtIndex:0];
                [weakSelf addColunEditViewWith:colNameArr fileName:model.file_name excelId:model.xyid];
                [weakSelf.columnSelectView open:nil];
                NSString *modelArrString = model1.toJSONString;
                [modelArrString writeToFile:excelDataFilePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
                [weakSelf.groupShadowTableView reloadData];
            }

        } failure:^(NSString *msg, id model) {
            [MBProgressHUD showToastWithMessageDarkColor:msg];
        }];
    }
}

- (void)addColunEditViewWith:(NSArray *)columnArr fileName:(NSString *)fileName excelId:(NSString *)excelId {
    ZDSTransparentView *columnSelectView = [[ZDSTransparentView alloc] initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT) dataArr:columnArr type:self.improtFrom];
    columnSelectView.delegate = self;
    columnSelectView.style = ZDSStyleBlack;
    columnSelectView.excelFileName = fileName;
    columnSelectView.excelId = excelId;
    columnSelectView.backgroundColor = [UIColor colorWithRed:0/255 green:0/255 blue:0/255 alpha:0.5];
    _columnSelectView = columnSelectView;
}

- (void)transparentSelectView:(NSString *)columnIndex codeType:(NSString *)codeType excelName:(NSString *)name excelId:(NSString *)excelId {
    if(columnIndex == nil && codeType == nil){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00508", @"请选择绑定数据")];
        return ;
    }
//    JCExcelInfoModel *infoModel = [self.fileInfoModelsArr safeObjectAtIndex:indexPath.row];
    JCExcelForElement *object = [JCExcelForElement new];
    NSArray *columnHeaders = self.columnValueArr.firstObject;
    NSMutableArray *list = [NSMutableArray arrayWithCapacity:self.columnValueArr.count];
    for (NSInteger col = 0; col < columnHeaders.count; col++) {
        NSMutableArray *arr = [NSMutableArray arrayWithCapacity:self.columnValueArr.count-1];
        for (NSInteger i = 1; i < self.columnValueArr.count; i++) {
            NSArray *eachRow = [self.columnValueArr safeObjectAtIndex:i];
            NSString *value = [eachRow safeObjectAtIndex:col];
            [arr addObject:UN_NIL(value)];
        }
        [list addObject:arr];
    }
    object.excelFileName = UN_NIL(name);
    object.columnArr = list;
    object.excelId = excelId;
    object.columnHeaders = columnHeaders;

    if(self.improtFrom == 1){
        self.block(object, columnIndex, nil);
        [self.navigationController popViewControllerAnimated:YES];
    } else if(self.improtFrom == 2 || self.improtFrom == 3){
        self.block(object, columnIndex,codeType);
        [self.navigationController popViewControllerAnimated:YES];
    } else{
        self.block(object, columnIndex,nil);
        [self.navigationController popViewControllerAnimated:YES];
    }
    [self.groupShadowTableView reloadData];
}

- (void)transparentSelectView:(NSString *)columnIndex codeType:(NSString *)codeType{
    if(columnIndex == nil && codeType == nil){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00508", @"请选择绑定数据")];
        return ;
    }
    NSString *valueExcelString = @"";
    for (NSInteger index = 1; index < self.columnValueArr.count;index++) {
        NSArray *valueArr = [self.columnValueArr safeObjectAtIndex:index];
        NSString *valueString = [valueArr safeObjectAtIndex:columnIndex.integerValue];
        if(self.columnValueArr.count == 1){
            valueExcelString = valueString;
        }
        else if(index == self.columnValueArr.count -1 ){
            valueExcelString = [NSString stringWithFormat:@"%@%@",valueExcelString,valueString];;
        }else{
            valueExcelString = [NSString stringWithFormat:@"%@%@:excel:",valueExcelString,valueString];
        }

    }
    if(self.improtFrom == 1){
        self.block(@1, valueExcelString,nil);
        [self.navigationController popViewControllerAnimated:YES];
    } else if(self.improtFrom == 2){
        self.block(@2, valueExcelString,codeType);
        [self.navigationController popViewControllerAnimated:YES];
    } else{
        self.block(@3, valueExcelString,nil);
        [self.navigationController popViewControllerAnimated:YES];
    }
    [self.groupShadowTableView reloadData];
}

- (void)excelBindOperateWithType:(NSInteger )operateType with:(NSString *)excelId excelZDBindView:(JCExcelZDBindView *)view{
    XYWeakSelf
    switch (operateType) {
        case 0:{
            [view closeExcelBindView];
            self.selectExcelColValueArr = nil;
            break;
        }
        case 1:{
            JCExcelColNameSelectViewController *vc = [[JCExcelColNameSelectViewController alloc] init];
            vc.excelFileId = excelId;
            [vc setExcelColNameSelectBlock:^(NSString *selectColName, NSArray *selectValueArr) {
                view.colNameLabel.text = selectColName;
                weakSelf.selectExcelColValueArr = selectValueArr.mutableCopy;

            }];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case 2:{ //生成文本
            if (self.block) {
                if(self.selectExcelColValueArr.count == 0){
                    [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00329",@"请选择列名")];
                    return ;
                }
                NSString *vlueString = @"";
                NSInteger count = self.selectExcelColValueArr.count;
                if (count == 1) {
                    vlueString = self.selectExcelColValueArr.firstObject;
                } else {
                    for (NSInteger i = 0; i < count; i++) {
                        NSString *itemString = [self.selectExcelColValueArr safeObjectAtIndex:i];
                        if (i == (count - 1)) {
                            vlueString = [NSString stringWithFormat:@"%@%@",vlueString,itemString];
                        } else {
                            vlueString = [NSString stringWithFormat:@"%@%@:excel:",vlueString,itemString];
                        }
                    }
                }
                self.block(@0, vlueString,nil);
                [view closeExcelBindView];
                [self.navigationController popViewControllerAnimated:YES];
            }
            break;
        }
        case 3:{ //一维码
            if(self.selectExcelColValueArr.count == 0){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00329",@"请选择列名")];
                return ;
            }
            if (self.block) {
                NSString *vlueString = @"";
                NSInteger count = self.selectExcelColValueArr.count;
                if (count == 1) {
                    vlueString = self.selectExcelColValueArr.firstObject;
                } else {
                    for (NSInteger i = 0; i < count; i++) {
                        NSString *itemString = [self.selectExcelColValueArr safeObjectAtIndex:i];
                        if (i == (count - 1)) {
                            vlueString = [NSString stringWithFormat:@"%@%@",vlueString,itemString];
                        } else {
                            vlueString = [NSString stringWithFormat:@"%@%@:excel:",vlueString,itemString];
                        }
                    }
                }
                self.block(@1, vlueString,nil);
                [view closeExcelBindView];
                [self.navigationController popViewControllerAnimated:YES];
            }

            break;
        }
        case 4:{ //二维码
            if(self.selectExcelColValueArr.count == 0){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00329",@"请选择列名")];
                return ;
            }
            if (self.block) {
                NSString *vlueString = @"";
                NSInteger count = self.selectExcelColValueArr.count;
                if (count == 1) {
                    vlueString = self.selectExcelColValueArr.firstObject;
                } else {
                    for (NSInteger i = 0; i < count; i++) {
                        NSString *itemString = [self.selectExcelColValueArr safeObjectAtIndex:i];
                        if (i == (count - 1)) {
                            vlueString = [NSString stringWithFormat:@"%@%@",vlueString,itemString];
                        } else {
                            vlueString = [NSString stringWithFormat:@"%@%@:excel:",vlueString,itemString];
                        }
                    }
                }
                self.block(@2, vlueString,nil);
                [view closeExcelBindView];
                [self.navigationController popViewControllerAnimated:YES];
            }
            break;
        }
        case 5:{ //通用
            if(self.selectExcelColValueArr.count == 0){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00329",@"请选择列名")];
                return ;
            }
            if (self.block) {
                NSString *vlueString = @"";
                NSInteger count = self.selectExcelColValueArr.count;
                if (count == 1) {
                    vlueString = self.selectExcelColValueArr.firstObject;
                } else {
                    for (NSInteger i = 0; i < count; i++) {
                        NSString *itemString = [self.selectExcelColValueArr safeObjectAtIndex:i];
                        if (i == (count - 1)) {
                            vlueString = [NSString stringWithFormat:@"%@%@",vlueString,itemString];
                        } else {
                            vlueString = [NSString stringWithFormat:@"%@%@:excel:",vlueString,itemString];
                        }
                    }
                }
                self.block(@5, vlueString,nil);
                [view closeExcelBindView];
                [self.navigationController popViewControllerAnimated:YES];
            }
            break;
        }
        default:
            break;
    }
}

- (void)excelBind2OperateWithType:(NSInteger )operateType with:(NSString *)excelId excelZDBindView:(JCExcelZDBindView2 *)view{
    XYWeakSelf
    switch (operateType) {
        case 0:{
            [view closeExcelBindView];
            self.selectExcelColValueArr = nil;
            break;
        }
        case 1:{
            JCExcelColNameSelectViewController *vc = [[JCExcelColNameSelectViewController alloc] init];
            vc.excelFileId = excelId;
            [vc setExcelColNameSelectBlock:^(NSString *selectColName, NSArray *selectValueArr) {
                view.colNameLabel.text = selectColName;
                weakSelf.selectExcelColValueArr = selectValueArr.mutableCopy;

            }];
            [self.navigationController pushViewController:vc animated:YES];
            break;
        }
        case 5:{ //通用
            if(self.selectExcelColValueArr.count == 0){
                [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app00329",@"请选择列名")];
                return ;
            }
            if (self.block) {
                NSString *vlueString = @"";
                NSInteger count = self.selectExcelColValueArr.count;
                if (count == 1) {
                    vlueString = self.selectExcelColValueArr.firstObject;
                } else {
                    for (NSInteger i = 0; i < count; i++) {
                        NSString *itemString = [self.selectExcelColValueArr safeObjectAtIndex:i];
                        if (i == (count - 1)) {
                            vlueString = [NSString stringWithFormat:@"%@%@",vlueString,itemString];
                        } else {
                            vlueString = [NSString stringWithFormat:@"%@%@:excel:",vlueString,itemString];
                        }
                    }
                }
                self.block(@5, vlueString,nil);
                [view closeExcelBindView];
                [self.navigationController popViewControllerAnimated:YES];
            }
            break;
        }
        default:
            break;
    }
}

- (void)fileNotification:(NSNotification *)notifcation {
    XYWeakSelf
    NSDictionary *info = notifcation.userInfo;
    NSString *filePath = [info objectForKey:@"filePath"];
    long long fileSize = [self fileSizeAtPath:filePath];
    if(self.allFile){
        if(fileSize > 5 * 1024 * 1024 && !m_user_vip){
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"",@"文件限制5MB，如有更大文件上传需求请开通会员") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01108",@"去开通") cancelBlock:nil sureBlock:^{
                if(NETWORK_STATE_ERROR){
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                    });
                }else{
//                    [JCIAPHelper openViewWithAlert:self needOpenTip:NO success:^{} failure:^(NSString *msg, id model) {
//                        [MBProgressHUD showToastWithMessageDarkColor:msg];
//                    } sourceInfo:@{@"sourcePage":@"0"}];
                    JCVIPDetailViewController *vc = [[JCVIPDetailViewController alloc] init];
            //        vc.sourcePage = @"006";
                    [self.navigationController pushViewController:vc animated:YES];
                }
            }];
        }else{
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00075",@"是否确认将此文件上传？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01108",@"确认") cancelBlock:nil sureBlock:^{
                if(NETWORK_STATE_ERROR){
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                    });
                }else{
                    [weakSelf uploadExcelFileWithPath:filePath uploadTag:[XYTool getNowTimeTimestamp]];
                }
            }];
        }
    }else{
      
        if(fileSize > (self.fileLimitKb * 1024)){
           NSString *fileLimitKb = [NSString stringWithFormat:@"%ld",(long)self.fileLimitKb];
            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED_WITH_PLACEHOLDERS(@"app100002081", @"文件限制$KB",@[fileLimitKb])];
        }else{
            [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示") message:XY_LANGUAGE_TITLE_NAMED(@"app00075",@"是否确认将此文件上传？") cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01108",@"确认") cancelBlock:nil sureBlock:^{
                if(NETWORK_STATE_ERROR){
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                    });
                }else{
                    [weakSelf uploadExcelFileWithPath:filePath];
                }
            }];
        }
    }
}

- (long long) fileSizeAtPath:(NSString*) filePath{

    NSFileManager* manager = [NSFileManager defaultManager];
    if ([manager fileExistsAtPath:filePath]){
        return [[manager attributesOfItemAtPath:filePath error:nil] fileSize];
    }
    return 0;

}

- (void)uploadExcelFileWithPath:(NSString *)pathString{
    XYWeakSelf
    if(NETWORK_STATE_ERROR){
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
    }
    NSLog(@"上传文件");
    [self xy_uploadFileWithValues:@{@"app_id":@"1",@"fileName":UN_NIL(pathString.lastPathComponent)} filePath:pathString modelType:nil Path:J_file_upload FormKey:@"file" hud:@"" Success:^(__kindof YTKBaseRequest *request, id model) {
        [weakSelf http];
    } failure:^(NSString *msg, id model) {
        [MBProgressHUD showToastWithMessageDarkColor:msg];
    }];
}

- (void)excelDetailLoadFinish{
    [self.groupShadowTableView reloadData];
}
#pragma mark 缓存Excel文件
- (void)loadExcelDetailInfo:(NSArray *)excelArr{
    NSMutableArray *requestOperationArr = [NSMutableArray array];
    NSFileManager* fm=[NSFileManager defaultManager];
    NSString *excelPath = RESOURCE_EXCEL_PATH;
    if(![fm fileExistsAtPath:excelPath]){
        [fm createDirectoryAtPath:excelPath withIntermediateDirectories:YES attributes:nil error:nil];
    }
    for (JCExcelInfoModel *tempModel in excelArr) {
        NSString *excelDataFilePath = [NSString stringWithFormat:@"%@/%@.txt",excelPath,tempModel.xyid];
        if([fm fileExistsAtPath:excelDataFilePath]){
            continue;
        }
        NSBlockOperation *operationCreat = [NSBlockOperation blockOperationWithBlock:^{
            dispatch_semaphore_t semaphore = dispatch_semaphore_create(0); //默认创建的信号为0
#pragma mark java
            [@{@"idList":UN_NIL(tempModel.xyid)} xy_postWithModelType:[JCExcelDetailModel class] Path:J_file_data hud:nil tag:0 timeoutInterval:20 Success:^(__kindof YTKBaseRequest *request, NSArray *modelArr) {
                JCExcelDetailModel *detailModel = [modelArr firstObject];
                NSString *modelArrString = detailModel.toJSONString;
                [modelArrString writeToFile:excelDataFilePath atomically:YES encoding:NSUTF8StringEncoding error:nil];
                dispatch_semaphore_signal(semaphore);
            } failure:^(NSString *msg, id model) {
                dispatch_semaphore_signal(semaphore);
            }];
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        }];
        [requestOperationArr addObject:operationCreat];
    }
    if(requestOperationArr.count > 1){
        for (NSInteger i = 1 ; i < requestOperationArr.count ; i++) {
            NSBlockOperation *operationPre = [requestOperationArr safeObjectAtIndex:i-1];
            NSBlockOperation *operationNext = [requestOperationArr safeObjectAtIndex:i];
            [operationNext addDependency:operationPre];
        }
    }
    [self.excelOperationQueue addOperations:requestOperationArr waitUntilFinished:NO];
    [self.excelOperationQueue addObserver:self forKeyPath:@"operationCount" options:0 context:nil];
}

-(void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    if ([keyPath isEqualToString:@"operationCount"]) {
        NSOperationQueue *queue = (NSOperationQueue *)object;
        if (queue.operationCount == 0) {
            NSLog(@"全部完成");
        }
    }
}
- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - lazyLoad
- (JCExcelEmptyView *)emptyView
{
    if (!_emptyView) {
        CGFloat navHeight = iPhoneX?88:64;
        CGFloat bottomHeight = iPhoneX?164:150;
        NSArray *thirdAppCanOpenArr = [JCThirdAppTool thirdAppExcelCanOpen];
        if(thirdAppCanOpenArr.count == 0){
            bottomHeight = iPhoneX?124:110;
        }
         _emptyView = [[JCExcelEmptyView alloc] initWithFrame: CGRectMake(0, 0, SCREEN_WIDTH, SCREEN_HEIGHT - navHeight - bottomHeight)];
          [self.view addSubview:_emptyView];
        _emptyView.hidden = YES;
    }
    return _emptyView;

}

#pragma mark -openlocalFile Thirdfile/

- (void)openThirdAppEvent:(NSArray *)thirdAppCanOpenArr appIndex:(NSInteger)selectIndex{
    NSString *thirdAppSelected = [thirdAppCanOpenArr safeObjectAtIndex:selectIndex];
    NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",thirdAppSelected]];
    [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {}];
}

- (void)openLocalFile{
    NSArray *documentTypes = @[@"public.content", @"com.microsoft.excel.xls"];
    UIDocumentPickerViewController *documentPickerViewController = [[UIDocumentPickerViewController alloc] initWithDocumentTypes:documentTypes inMode:UIDocumentPickerModeImport];
    documentPickerViewController.delegate = self;
    documentPickerViewController.modalPresentationStyle = UIModalPresentationFullScreen;
    [self presentViewController:documentPickerViewController animated:YES completion:^{
    }];
}

#pragma mark - UIDocumentPickerDelegate
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-implementations"
- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentAtURL:(NSURL *)url {

    NSString *fileName = url.lastPathComponent;
    NSString *path = url.absoluteString;
    path = [path stringByRemovingPercentEncoding];
    NSMutableString *string = [[NSMutableString alloc] initWithString:path];
//    string = [string stringByReplacingOccurrencesOfString:@" " withString:@""];
    if ([string hasPrefix:@"file://"]) {
        [string replaceOccurrencesOfString:@"file://" withString:@"" options:NSCaseInsensitiveSearch range:NSMakeRange(0, string.length)];
        NSDictionary *dict = @{@"fileName":UN_NIL(fileName),
                               @"filePath":UN_NIL(string)};
        [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_EXCEL_UPLOAD object:nil userInfo:dict];
    }
}
#pragma clang diagnostic pop

- (void)uploadExcelFileWithPath:(NSString *)filePath uploadTag:(NSString *)uploadTag{
    NSData *fileData = [NSData dataWithContentsOfFile:filePath];
    if(fileData == nil) [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100002078", @"无效文件")];    
    [[JCOSSManager sharedManager] oss_uploadFileWithParmsDic:@{@"module":@"USER_HIPPO"} fullFileName:filePath.lastPathComponent fileData:fileData Success:^(NSString * _Nonnull key, NSString * _Nonnull url) {
            if (url && url.length > 0) {
               
            }
        } failure:^(NSString * _Nonnull key, NSString * _Nonnull errMsg) {
            NSLog(@"日志文件上传失败");
    }];
}



/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/

@end
