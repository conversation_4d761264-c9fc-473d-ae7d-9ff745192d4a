import 'dart:convert';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_net_signature/flutter_net_signature_interceptor.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:netal_plugin/netal_plugin.dart';
import 'package:niim_login/login_plugin/login_plugin_api.dart';
import 'package:niim_login/login_plugin/login_plugin_config.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:niim_login/login_plugin/utils/login_dio_utils.dart';
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/material_badge_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/logger.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:niimbot_local_storage/niimbot_sp.dart';
import 'package:niimbot_log_plugin/niimbot_log_manager.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:niimbot_template/template_parse.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/ad/print_ad_manager.dart';
import 'package:text/business/app/offline_manager.dart';
import 'package:text/business/print/print_log_business.dart';
import 'package:text/connect/nety_connect_helper.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/macro/color.dart';
import 'package:text/macro/constant.dart';
import 'package:text/network/interceptor/interceptor.dart';
import 'package:text/pages/C1/model/c1_file_business.dart';
import 'package:text/pages/canvas/impl/host_method_impl.dart';
import 'package:text/pages/canvas/impl/native_method_impl.dart';
import 'package:text/pages/canvas/model/NiimbotPrinter.dart';
import 'package:text/pages/device/model/device_model.dart';
import 'package:text/pages/login/LoginPluginHostApiNiimbot.dart';
import 'package:text/pages/meProfile/me_profile_presenter.dart';
import 'package:text/print/print_content_manager.dart';
import 'package:text/print/print_history_manager.dart';
import 'package:text/routers/routers.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_sync_service.dart';
import 'package:text/tools/rfid_manager.dart';
import 'package:text/tools/to_Native_Engine_Channel.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/label_paper_rule_manager.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/vipTrial/vip_trial_manager.dart';

import '../international_info_controller.dart';
import 'business/app/app_config_manager.dart';
import 'business/print/print_count_business.dart';
import 'cache/cache_helper.dart';
import 'connect/machine_alias_manager.dart';
import 'migration/migration_manager.dart';
import 'network/entity/user.dart';
import 'pages/industry_template/home/<USER>/advertisement_manager.dart';
import 'pages/message/provider/message_service.dart';

enum AppEnv {
  /// 生产环境
  production,

  /// 测试环境
  test,

  /// 开发环境
  dev
}

class Application {
  static final Logger _logger = Logger("Application", on: kDebugMode);

  static late SharedPreferences sp;
  static FluroRouter? router;
  static late InternationalInfoController intl;
  static String _token = "";
  static late String _mallToken = "";
  static late String agent = "";
  static late String anonymousId = "";
  static late String _appVersion = "";
  static String phone = "";
  static String systemVersion = "";
  static String deviceId = "";
  static late String currentAppLanguageType = "";
  static User? _user;

  static User? get user => _user;

  static String get token => _token;

  /// 是否登录，id为0或者为空代表未登录，否则已登录
  static bool get isLogin => (user?.id == 0 || user?.id == null) ? false : true;

  static String get mallToken => _mallToken;
  static late String connectPrinterName = "";
  static bool isConnected = false;

  static get appVersion => _appVersion;
  static Map currentLanguageData = {};
  static String loginLanguageType = "";

  static double screenPPI = 0; //屏幕分辨率
  static bool hasPrintUpdateNotify = false;
  static double screenScale = 0; //屏幕倍率

  static String userRegion = "CN";
  static bool myAccountPageShowing = true;
  static AppEnv appEnv = AppEnv.production;

  static String webUrl = "https://print.niimbot.com/h5#/consumableCharacteristicH5/";
  static String shopMallBaseUrl = "https://shop.jc-saas.com/open";
  static String pbaUrl = "https://bpa.niimbot.com";
  static String aliServerlessBaseUrl = "https://aliyunfc.niimbot.com";

  static String c1HeplcenterBaseUrl = "";

  // static String ossUrl = "https://niimbot-bison.oss-cn-hangzhou.aliyuncs.com";
  // static String ossCdnUrl = "https://oss-print.niimbot.com";
  static String npsH5Url = "https://n.niimbot.com/#/nps";
  static String npsVipH5Url = "https://n.niimbot.com/niimbot/vip-nps";

  /// TODO 小程序NPS专用 目前只支持中文
  static String npsUniAppH5Url = Application.appEnv == AppEnv.production
      ? "https://n.niimbot.com/program/nps"
      : "https://n.jc-test.cn/program/nps";
  static bool _networkConnected = true;

  static bool get networkConnected => NiimbotNetworkManager().isConnected();

  /// flutter boost 顶部 containerId
  static String? boostTopContainerId;

  static NiimbotPrinter? printer;
  static NiimbotPrinter? selectPrinter;

  /// 页面刷新
  static VoidCallback? fresh;

  static BuildContext? allContext;
  static bool? isAppStore;
  static bool lastLoginStatus = false;

  /// 是否启用全局的手势
  static bool isEnableGesture = true;

  static bool isInLoginPage = false;

  /// 刷新广告
  static const String EVENT_REFRESH_ADVERTISEMENTS = 'refreshAdvertisements';

  /// 语言切换刷新
  static const String EVENT_LANGUAGE_CHANGED = 'languageChanged';

  static List<Function> gotoCanvasSuccessCallbacks = [];

  /*
    1. 如果已经登陆,那么以用户的身份为准
    2. 如果没有登录, 那么看当前设备是否选择过使用地区
    如果以上两处都无法获取当前设备的国家地区信息, 那么需要弹出地区选择窗口
  */
  static String get _userRegion {
    if (_user != null && _user?.countryCode != null) {
      //能获取到用户信息中的国家地区,则以此为准,不能获取就以用户选择的为准
      userRegion = _user?.countryCode ?? deviceRegion;
    } else {
      userRegion = sp.getString(ConstantKey.User_Chose_Region) ?? deviceRegion;
    }
    if (userRegion.isEmpty) userRegion = "cn";
    return (userRegion).toLowerCase();
  }

  static List<DeviceModel> _deviceList = [];
  static late DeviceModel _deviceItem;
  static late String deviceRegion; //用户设备所在的地区, 只用来和ip对比, 其他多语言地方没用

  static String emailForbidRegex =
      "[\u4e00-\u9fa5]|[^\\u0020-\\u007E\\u00A0-\\u00BE\\u2E80-\\uA4CF\\uF900-\\uFAFF\\uFE30-\\uFE4F\\uFF00-\\uFFEF\\u0080-\\u009F\\u2000-\\u201f\r\n]";

  static bool isFacebookAuthEnable = false;

  static bool hasShowOpenBluetoothGuide = false;

  static bool hasShowRequestLocationPermission = false;

  static bool isIphoneX = true; //iOS是否为iphoneX以后版本

  static bool? inProduction = false; //Android是否为生产环境

  static int? currentTimeZone = 8; //Android时区

  static bool isForceLTR = false; //强制LTR模式

  static String localCachePath = "";

  /// Layout模式，默认LTR
  static TextDirection textDirection = TextDirection.ltr;

  //是否点击过nps浮层 当次启动过程中关闭过就不再显示
  static bool isCloseNps = false;

  static bool inMyAccountPage = false;

  static String maxSupportTemplateVersion = '*******';

  static bool hasToken() {
    //是否有token
    return Application.token.isNotEmpty;
  }

  static bool isExistMainAccount() {
    bool ret = false;

    if (_user != null) {
      if (_user!.phone != null && _user!.phone!.isNotEmpty) {
        ret = true;
      }

      if (_user!.email != null && _user!.email!.isNotEmpty) {
        ret = true;
      }
    }

    return ret;
  }

  static setAuthorization(String accessToken) {
    Application._token = accessToken;
  }

  static setMallAuthorization(String accessToken) {
    Log.d("获取到商城token:$accessToken");
    Application._mallToken = accessToken;
  }

  /*
   * 进入app就初始化，后期可以直接使用同步方法
   */
  static Future initSp() async {
    try {
      await DBManagerUtil.instance.init();
      sp = await SharedPreferences.getInstance();
      await NiimbotSp().getInstance();
      ToNativeMethodChannel.sharedInstance();
      registerNativeEventChannel();
      NetyConnectHelper.setNetyCallbackListener();
      if (Platform.isAndroid) {
        ToNativeMethodChannel.notifyFlutterChannel();
      }
      bool debug = await ToNativeMethodChannel.checkDebugMode();
      if (!debug) {
        debugPrint = (String? message, {int? wrapWidth}) {};
      }
      //初始化日志服务
      Map agentData = await ToNativeMethodChannel().getAppConfig();
      debugPrint("初始化日志服务");
      NiimbotLogTool.initLogConfig(NiimbotAppConfig.fromJson(Map<String, dynamic>.from(agentData)));
      _appVersion = agentData["appVserson"] ?? "";
      phone = agentData["phone"] ?? "";
      systemVersion = agentData["systemVersion"] ?? "";
      deviceId = agentData["deviceId"] ?? "";
      // 获取是否强制LTR
      isForceLTR = await ToNativeMethodChannel.isForceLTR();
      maxSupportTemplateVersion = await ToNativeMethodChannel.getMaxSupportTemplateVersion();
      // 获取硬件列表
      ToNativeMethodChannel.setNativeDeviceList(json.encode(deviceList));
      // 语言环境刷新（语言、国家码、用户信息、接口环境、代理配置）
      await _refreshFlutterEnvConfig(appInfo: agentData);
      // 同步缓存管理器环境以及用户Token、语言、userAgent等信息
      syncCacheMangerHttpRequestHeader();
      // 画板语言管理
      CanvasPluginManager().hostMethodImpl = HostMethodImpl();
      CanvasPluginManager().nativeMethodImpl = NativeMethodImpl();
      // // 画板内初始化（sp传入）
      ExcelManager.sharedInstance().init(sp);
      DateElementHelper.init(sp);
      MaterialBadgeHelper.init(sp);
      BadgeHelper.init(sp);
      CacheHelper.getInstance().cacheActionWhenInitSp(sp);
      Directory systemDocumentsPath = await getApplicationDocumentsDirectory();
      AppConfigManager().getAppConfigInfo();
      localCachePath = systemDocumentsPath.path;
      // 注册原生传递的 event data 事件
      // registerNativeEventChannel();
      NiimbotNetworkManager.callback = (isConnected) {
        if (isConnected && CanvasUserCenter().isLogin) {
          C1FileBusiness.instance.localFileToService();
        }
        if (isConnected && Application.isLogin) {
          MigrationManager().setupDataMigration().then((_) {
            if (Application.isLogin) {
              TemplateManager().localFileToService();
            }
          });
        }
        NiimbotEventBus.getDefault().post({'networkChanged': isConnected});
        //画板内用CanvasEventBus监听
        CanvasEventBus.getDefault().post({'networkChanged': isConnected});
        if (isConnected) {
          // 恢复网络同步历史记录到云端
          PrintHistoryManager().syncHistoryToServer();
          // 恢复网络同步打印内容到云端
          PrintContentManager().syncPrintContentToServer();
          // 恢复网络同步打印日志到云端
          PrintLogBusiness().uploadPrintDataLog();
          if (printer != null) {
            RfidManager.instance.buildRiskCheck(RiskRequestCode.Connected.value);
          }
          CacheHelper.getInstance().updateCacheAction();
        }
      };
      NiimbotNetworkManager().sendNetworkStatus();
      NiimbotNetworkManager().addNetworkConnectivityListener();
      // 启动同步历史记录到云端
      PrintHistoryManager().syncHistoryToServer();
      PrintContentManager().syncPrintContentToServer();
      // 监听 event bus 处理全局状态
      await listenEventBusGlobal();
      // 等待初始化图像库以及字体
      await NetalPlugin().init();
      RfidManager.instance.watchSdkRfid();
      await NiimbotSecurityPrintHelper.initSecurityPrintConfig(
          appEnv == AppEnv.production ? NiimbotAppEnv.pro : NiimbotAppEnv.test,
          Application.agent,
          PrintStrategyUserAgentType.inInit, proxy: sp.get("proxy") as String?);
      PrintLogBusiness().uploadPrintDataLog();
      ConnectivityResult connectivityResult = await Connectivity().customCheckConnectivity();
      if (connectivityResult != ConnectivityResult.none && isLogin) {
        C1FileBusiness.instance.localFileToService();
      }
      await MachineAliasManager().loadAllAliasFromCache();

      // 初始化广告管理器
      AdvertisementManager().init();

      // 初始化标签纸规则管理器
      LabelPaperRuleManager();
    } catch (e) {
      Log.e(e.toString());
    }
  }

  static bool getRtlSwitchStatus() {
    // 仅在阿拉伯语、英文显示，其他语言时不显示
    if (currentAppLanguageType.toLowerCase() == "ar" || currentAppLanguageType.toLowerCase() == "en") {
      bool? status = sp.getBool("drawboard_rtl_switch_status");
      return status ?? currentAppLanguageType.toLowerCase() == "ar";
    }
    return false;
  }

  static refreshHttpAgent() async {
    Map agentData = await ToNativeMethodChannel().getAgent();
    if (agentData.isNotEmpty) {
      if (agentData["agent"] != null) {
        Application.agent = agentData["agent"];
        Application.anonymousId = agentData["anonymous_id"] ?? "";
      }
    }
  }

  static bool isAndroidAgentValid() {
    return Platform.isAndroid && agent.isNotEmpty && !agent.contains('0000000000000000');
  }

  static toLocale(String language) {
    List<String> parts = language.split('-');
    String languageCode = parts[0];
    String? countryCode;
    if (parts.length > 1) {
      countryCode = parts[1];
    }
    Locale locale = Locale(languageCode);
    return locale;
  }

  /// 原生层语言变动, 刷新 flutter 配置
  static _refreshFlutterEnvConfig({Map? appInfo}) async {
    bool accept = Application.sp.getBool(ConstantKey.AcceptProtocol_key) ?? false;
    refreshHttpAgent();
    try {
      if (appInfo != null && appInfo.isNotEmpty) {
        Application.isAppStore = appInfo["isAppStore"] == "1";
        Log.d("获取debug信息成功");
      }
    } catch (e) {
      Log.e("获取debug信息成功失败：$e");
    }
    try {
      Map languageData = await ToNativeMethodChannel().getLanguageDetail();
      if (languageData.isNotEmpty) {
        Application.currentLanguageData = languageData;
        Log.d("获取原生语言资源：成功");
      }
    } catch (e) {
      Log.e("获取原生语言资源失败：$e");
    }

    try {
      String languageType = await ToNativeMethodChannel().getAppCurrentLanguageType();
      if (languageType.isNotEmpty) {
        Application.currentAppLanguageType = languageType;
        TemplateParse.matchLocale(toLocale(languageType == 'Polish' ? 'pl' : languageType)).then((_) {});
        Application.loginLanguageType = languageType;
        // 在阿拉伯语言下可能存在强制LTR的情况
        Application.textDirection =
            languageType.toLowerCase() == "ar" && !Application.isForceLTR ? TextDirection.rtl : TextDirection.ltr;
        // 全局刷新语言
        Application.fresh?.call();
        Log.d("获取原生语言类型：成功$languageType");
      }
    } catch (e) {
      Log.e("获取原生语言类型失败：$e");
    }

    try {
      Map? userValue = await ToNativeMethodChannel().getUserInfoFromLocalCache();
      Log.d("获取用户信息：成功$userValue");
      if (userValue != null && userValue.isNotEmpty) {
        String? profileUrl = userValue["avatar"];
        if (null == profileUrl || profileUrl.isEmpty) {
          userValue["profileUrl"] =
              "https://jc-print.oss-cn-hangzhou.aliyuncs.com/print/cloudprint/merchandise/sku/20220915/6441663227815713.png";
        } else {
          userValue["profileUrl"] = profileUrl;
        }
        String? nickname = userValue["nickname"];
        if (null == nickname || nickname.isEmpty) {
          userValue["uid"] = userValue["displayUId"];
        }
        _user = User.fromJson(userValue);

        if (Platform.isAndroid) {
          user?.socialNetworks?.clear();

          json.decode(userValue["networks"]).forEach((e) {
            user?.socialNetworks?.add(SocialInfo.fromJson(e));
          });
        }

        Log.d("获取用户信息token：${userValue["access_token"]}  shoptoken=${userValue["shopToken"]}");
        Application.setAuthorization(userValue["access_token"] as String);
        Application.setMallAuthorization(userValue["shopToken"] as String);
        // 登录插件GraphQL插入token
        GraphQLUtils.sharedInstance().setAuthorization(userValue["access_token"] as String);
      } else {
        _user = null;
      }
      // if (Platform.isAndroid) {
      //   _logger.log("==================初始化或语言更新，loadABTestConfig");
      //   GrayConfigManager().loadABTestConfig();
      // }
    } catch (e, stack) {
      Log.e("获取用户信息：异常$e $stack");
      Log.d("$stack");
      _user = null;
    }

    try {
      appEnv = await ToNativeMethodChannel().getAppEnv();
      Log.d("获取app环境成功: $appEnv");

      /// GraphQL拦截
      GraphQLUtils.sharedInstance().setHttpInterceptors([OutOfServiceInterceptor(), FlutterNetSignatureInterceptor()]);
      if (appEnv == AppEnv.production) {
        GraphQLUtils.sharedInstance().configDomain("https://print.niimbot.com/graphql/");
        LoginDioUtils().configDomain("https://print.niimbot.com/api");
        webUrl = 'https://print.niimbot.com/h5#/consumableCharacteristicH5/';
        shopMallBaseUrl = 'https://shop.jc-saas.com/open';
        pbaUrl = 'https://bpa.niimbot.com';
        // ossUrl = "https://niimbot-bison.oss-cn-hangzhou.aliyuncs.com";
        // ossCdnUrl = "https://oss-print.niimbot.com";
        npsH5Url = "https://n.niimbot.com/#/nps";
        npsVipH5Url = "https://n.niimbot.com/niimbot/vip-nps";
        aliServerlessBaseUrl = "https://aliyunfc.niimbot.com";
        c1HeplcenterBaseUrl =
            "https://n.niimbot.com/niimbot/help-center?module=function&categoryName=applet&subCategoryName=C1&lang=${Application.currentAppLanguageType}&userAgent=${agent}";
      } else {
        GraphQLUtils.sharedInstance().configDomain("https://print.jc-test.cn/graphql/");
        LoginDioUtils().configDomain("https://print.jc-test.cn/api");
        webUrl = 'https://print.jc-test.cn/h5#/consumableCharacteristicH5/';
        shopMallBaseUrl = 'https://openshop.jc-dev.cn/shop-open';
        pbaUrl = 'https://bpa.jc-test.cn';
        // ossUrl = "https://jc-print-print-fat.oss-cn-hangzhou.aliyuncs.com";
        // ossCdnUrl = "https://oss-print-fat.jc-test.cn";
        npsH5Url = "https://n.jc-test.cn/#/nps";
        npsVipH5Url = "https://n.jc-test.cn/niimbot/vip-nps";
        aliServerlessBaseUrl = "https://feiyu.jc-test.cn";
        c1HeplcenterBaseUrl =
            "https://n.jc-test.cn/niimbot/help-center?module=function&categoryName=applet&subCategoryName=C1&lang=${Application.currentAppLanguageType}&userAgent=${agent}";
      }
    } catch (e) {
      Log.e("获取app环境失败：$e");
    }

    try {
      String proxyUrl = await ToNativeMethodChannel.getFlutterProxyUrl();
      Log.d("获取Flutter代理--->成功: $proxyUrl");
      sp.setString("proxy", proxyUrl);
      LoginPluginConfig.sharedInstance().proxyIpAddress = proxyUrl;
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
    }

    try {
      isFacebookAuthEnable = Application.sp.getBool(ConstantKey.Facebook_Login_Switch_key) ?? false;
      ToNativeMethodChannel.sharedInstance().getLocaleLanguage().then((value) {
        String languageCode = value!["languageCode"];
        String countryCode = value["countryCode"];
        Application.deviceRegion = countryCode;
      });
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
    }
  }

  // 同步缓存管理器环境以及用户Token、语言、userAgent等信息
  static syncCacheMangerHttpRequestHeader() {
    NiimbotCacheManager().syncAppEnvWithAttributes(
      appEnv: appEnv == AppEnv.production ? AppEnvironment.production : AppEnvironment.test,
      languageCode: currentAppLanguageType,
      authCode: _token,
      userAgent: agent,
    );
  }

  static bool isProductEnv() {
    return appEnv == AppEnv.production;
  }

  static List<DeviceModel> get deviceList {
    if (_deviceList.isEmpty) {
      String listJson = sp.getString(ConstantKey.Device_list_key) ?? json.encode(defaultDeviceList);
      _deviceList = [];
      json.decode(listJson).forEach((e) {
        _deviceList.add(DeviceModel.fromJson(e));
      });
    }
    return _deviceList;
  }

  static set deviceList(List<DeviceModel> value) {
    if (value.isNotEmpty) {
      _deviceList = value;
      NiimbotEventBus.getDefault().post({'refreshDeviceList': ''});
      value.map((e) => e.toJson()).toList();
      sp.setString(ConstantKey.Device_list_key, json.encode(value));
      //toNavite更改设备类型
      ToNativeMethodChannel.setNativeDeviceList(json.encode(value));
    }
  }

  static DeviceModel get deviceItem {
    return _deviceItem;
  }

  static setDefaultDeviceItemIfNecessary() {
    if (_deviceItem == null && deviceList.isNotEmpty && deviceList.length > 0) {
      deviceItem = deviceList.firstWhere((element) => element.code == "D11", orElse: () {
        return deviceList.first;
      });
    }
  }

  static void showToast(String string) {
    //隐藏原生toast
    ToNativeMethodChannel.sharedInstance().closeNativeToast();
    Fluttertoast.cancel();
    Fluttertoast.showToast(
        msg: string, gravity: ToastGravity.CENTER, backgroundColor: Color(0xCC333333), textColor: KColor.WHITE);
  }

  static set deviceItem(value) {
    if (value != null && _deviceItem != value) {
      _deviceItem = value;
      sp.setString(ConstantKey.Device_item_key, json.encode(_deviceItem));
      NiimbotEventBus.getDefault().post("deviceTypeChanged");
      //toNative更改设备类型
      ToNativeMethodChannel.setNativeDeviceType(json.encode(_deviceItem));
    }
  }

  static initRouter() {
    final router = FluroRouter();
    Routers.configureRoutes(router);
    Application.router = router;
    Log.d("初始化路由信息:");
  }

  static registerNativeEventChannel() {
    Log.d("注册flutter信息");
    ToNativeEngineChangel.registerNativeEventStreams((var value) async {
      Log.d("flutter接收到信息: ${value}");
      if (value == null) {
        return;
      }
      if (value is Map && value.containsKey("agent")) {
        agent = value["agent"];
        anonymousId = value["anonymous_id"];
        if (Platform.isAndroid) {
          deviceId = value["deviceId"];
          NiimbotLogTool.updateDeviceId(deviceId, agent);
        }

        ///android因为隐私权限的问题，初次启动时deviceId为0000000000000000，获取到的灰度策略为空，
        ///同意隐私权限后会刷新header，因此需要重新获取灰度策略
        _logger.log("==================agent刷新，loadABTestConfig");
        syncCacheMangerHttpRequestHeader();
        CacheHelper.getInstance().cacheActionWhenGetAgentFromNative();
      } else if (value is Map && value.containsKey("clipText")) {
        NiimbotEventBus.getDefault().post(value);
      } else if (value is String && value == 'kBackToForeground') {
        NiimbotEventBus.getDefault().post(value);
        NiimbotNetworkManager().sendNetworkStatus();
        Log.d("==========kBackToForeground=========");
      } else if (value is String && value == 'kShareDetailViewShow') {
        SystemChannels.textInput.invokeMethod<String>('TextInput.hide');
      } else if (value is String && value == 'kEnterBackground') {
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey("blueToothState")) {
        //获取蓝牙状态
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey("connectPrinterName")) {
        //获取蓝牙状态
        Application.connectPrinterName = value["connectPrinterName"];
        NiimbotLogTool.updateConfigInfo(user == null ? "" : token, user == null ? "" : (user!.displayUId ?? ""),
            Application.currentAppLanguageType, Application.connectPrinterName);
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey("userInfo")) {
        Map userValue = value["userInfo"];

        User? user = null;
        if (userValue.isNotEmpty) {
          String? profileUrl = userValue["avatar"];
          if (null == profileUrl || profileUrl.isEmpty) {
            userValue["profileUrl"] =
                "https://jc-print.oss-cn-hangzhou.aliyuncs.com/print/cloudprint/merchandise/sku/20220915/6441663227815713.png";
          } else {
            userValue["profileUrl"] = profileUrl;
          }
          Log.d('user value parse start ==$userValue');
          user = User.fromJson(userValue);

          if (Platform.isAndroid) {
            user.socialNetworks?.clear();

            // String? countryCode = user.countryCode;
            // user.countryCode = user.areaCode;
            // user.areaCode = countryCode;
            json.decode(userValue["networks"]).forEach((e) {
              user?.socialNetworks?.add(SocialInfo.fromJson(e));
            });
          }

          Log.d('user value parse end');
          String tokenTemp = _token;
          Application.setAuthorization(userValue["token"] as String);
          Application.setMallAuthorization(userValue["shopToken"] as String);
          if (_token != tokenTemp) {
            syncCacheMangerHttpRequestHeader();
          }
          Log.d("用户通知 $userValue 用户token ${userValue["token"]}用户商城token${userValue["shopToken"]}");
        }
        Log.d("flutter接收到登录信息: ${userValue}");
        Application.user = user;
        _user = user;
        NiimbotLogTool.writeLogToFile({"接收登录/登出信息": '$user.toJson()'});
        // 此处的user-model目前只有我的页面有使用
        Map userInfo = {"userInfo": user};
        NiimbotLogTool.updateConfigInfo(user == null ? "" : token, user == null ? "" : user!.displayUId ?? "",
            Application.currentAppLanguageType, Application.connectPrinterName);
        NiimbotLogManager.instance.resetSTSAliyunLogSdkInof();
        NiimbotEventBus.getDefault().post(userInfo);
      } else if (value is Map && value.containsKey("languageType")) {
        Application.currentLanguageData = value["languageDetail"];
        NiimbotLogTool.updateConfigInfo(user == null ? "" : token, user == null ? "" : user!.displayUId ?? "",
            value["languageType"], Application.connectPrinterName);
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey("printUpdateNotify")) {
        Application.hasPrintUpdateNotify = value["printUpdateNotify"] == "1";
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey('labelData')) {
        if (value["labelData"] is String) {
          value["labelData"] = json.decode(value["labelData"]);
          //  Map<String, dynamic> labelData = Map<String, dynamic>.from(value['labelData']);
          RfidManager.instance.refreshRfidTemplateData(value["labelData"]);
        }
        // 标签纸数据
        NiimbotEventBus.getDefault().post(value);
      } else if (value is String && value == 'languageChanged') {
        /// 刷新 Flutter 配置
        await _refreshFlutterEnvConfig();

        /// 同步缓存管理器环境以及用户Token、语言、userAgent等信息
        syncCacheMangerHttpRequestHeader();
        NiimbotLogTool.updateConfigInfo(user == null ? "" : token, user == null ? "" : user!.displayUId ?? "",
            Application.currentAppLanguageType, Application.connectPrinterName);
        CacheHelper.getInstance().cacheActionWhenSwitchLanguage(currentAppLanguageType);

        // PrintAdManager().requestPrintFinishAdData(MeProfilePresenter.shopSiteCode);

        // 发送刷新广告事件
        NiimbotEventBus.getDefault().post(Application.EVENT_REFRESH_ADVERTISEMENTS);

        // 发送语言切换刷新事件
        NiimbotEventBus.getDefault().post(Application.EVENT_LANGUAGE_CHANGED);

        Log.d("***********languageChanged ${Application.currentAppLanguageType}");
      } else if (value is Map && value.containsKey('loginStatusChanged')) {
        _loginStatusChanged(value);
        NiimbotEventBus.getDefault().post(value);
        bool logged = value['loginStatusChanged']['status'] != 0;
        Log.d(
            "***********loginStatusChanged ${MeProfilePresenter.shopSiteCode} lastLoginStatus=$lastLoginStatus logged=$logged)");
        if (lastLoginStatus != logged) {
          lastLoginStatus = logged;
        }
        Future.delayed(Duration(seconds: 2),(){
          PrintAdManager().requestPrintFinishAdData(MeProfilePresenter.shopSiteCode);
        });

      } else if (value is Map && value.containsKey('shopState') && value.containsKey('shopSiteCode')) {
        int state = value['shopState'];
        String siteCode = value['shopSiteCode'];
        Log.d(
            "***********updateShopInfo: state = $state, oldSiteCode=${MeProfilePresenter.shopSiteCode} siteCode = $siteCode");
        bool result = MeProfilePresenter.updateShopInfo(state, siteCode);
        // if (result) {
        //   PrintAdManager().requestPrintFinishAdData(siteCode);
        // }
      } else if (value is Map && value.containsKey("action")) {
        final action = value['action'];
        switch (action) {
          // /// 打印机连接识别到单色标签纸/碳带
          // case "currentPrintColor":

          /// 打印机连接识别到多色标签纸/碳带
          case "paperSupportColors":

          /// 小程序操作回调
          case "advanceQRCode":
            CanvasEventBus.getDefault().post(value);
            break;

          /// 保存至服务端更新
          /// 打印机连接识别到单色标签纸/碳带
          case "currentPrintColor":
          case "saveUpdateCanvasData":
          case "changeTemplateName":
          case "updateTemplateId":
            NiimbotEventBus.getDefault().post(value);
            CanvasEventBus.getDefault().post(value);
            break;
          case "c1ConnectProcess":
            NiimbotEventBus.getDefault().post(value);
            break;
          default:
            NiimbotEventBus.getDefault().post(value);
            break;
        }
      } else if (value is String && value == 'uploadPrintDataLog') {
        // PrintLogBusiness().uploadPrintDataLog();
        // Future.delayed(Duration(seconds: 1),(){

        // });
      } else if (value is String && value == 'printFinish') {
        NiimbotEventBus.getDefault().post(value);
      } else if (value is Map && value.containsKey("wechatAuthResponse")) {
        Map response = value["wechatAuthResponse"];
        LoginPluginApi.wechatAuthResponse(response);
      } else if (value is Map && value.containsKey("C1TemplateShareCode")) {
        NiimbotEventBus.getDefault().post(value);
      } else if (value is String && value == 'refreshPrintAdvert') {
        PrintAdManager().requestPrintFinishAdData(MeProfilePresenter.shopSiteCode);
      } else {
        NiimbotEventBus.getDefault().post(value);
      }
    });
  }

  static _loginStatusChanged(Map value) {
    bool logged = value['loginStatusChanged']['status'] != 0;
    String token = value['loginStatusChanged']['token'] ?? '';
    Application.setAuthorization(token);
    // 刷新VIP试用管理
    VipTrialManager().getAllTrialActivities();

    /// 刷新登录组件 token
    if (logged) {
      // GoodFieldManager().goodFieldList = [];
      GoodFieldManager().getGoodFields();
      if (token.isNotEmpty) {
        GraphQLUtils.sharedInstance().setAuthorization(token);
      }
      C1FileBusiness.instance.localFileToService();
      MigrationManager().setupDataMigration().then((_) {
        TemplateManager().localFileToService();
      });
      MessageService().fetchMessageUnreadCount().then((unreadCount) {
        ToNativeMethodChannel().notifyUnreadMessageCount(unreadCount);
      });
      PrintCountBusiness().requestUserPrintCount();
    } else {
      ToNativeMethodChannel().notifyUnreadMessageCount(0);
      GoodFieldManager().goodFieldList = [];
      GraphQLUtils.sharedInstance().removeAuthorization();
      // 用户登出时清理模板同步状态
      TemplateSyncService.clearSyncState();
    }
    _logger.log("==================登录状态刷新，loadABTestConfig");
    syncCacheMangerHttpRequestHeader();
    CacheHelper.getInstance().cacheActionWhenLoginStatusChanged(logged);
  }

  static listenEventBusGlobal() {
    /// 监听打印机连接状态
    NiimbotEventBus.getDefault().register(Object(), (data) {
      if (data is Map && data.containsKey('printerConnectState')) {
        ///当前连接的打印机
        if (data['printerConnectState']['connected'] != 0) {
          printer = niimbotPrinterFromJson(data['printer']);
        } else {
          RfidManager.instance.clearConnectDevice();
          printer = null;
        }

        /// 当前连接的设备
        String? seriesId = data['printerConnectState']['seriesId'];
        if ((seriesId ?? '').isNotEmpty) {
          /// 存储当前的连接设备系列 id
          if (!seriesId!.contains("10008")) {
            Application.sp.setString(ConstantKey.latestHardwareSeriesId, '$seriesId');
          }
        }

        /// 当前连接的设备
        String? machineId = data['printerConnectState']['machineId'];
        if ((machineId ?? '').isNotEmpty) {
          /// 存储当前的连接设备系列 id
          Application.sp.setString(ConstantKey.latestHardwareId, '$machineId');
        }

        String? machineName = data['printerConnectState']['machineName'];
        if ((machineName ?? '').isNotEmpty) {
          if (machineName!.contains('-')) {
            List<String> machineNoteArr = machineName.split('-');
            machineName = machineNoteArr.sublist(0, machineNoteArr.length - 1).join('-');
          }

          /// 存储当前连接设备的机型名称（排除电子价签ET10和线号机C1）
          if (!(machineName.contains("ET10") || machineName.startsWith("C1"))) {
            Application.sp.setString(ConstantKey.latestHardwareName, machineName);
          }
        }
      }
    });
  }

  static saveToken(String value) {
    _token = value;
    Log.d('update token: $value');
    _saveToken(value: value);
    ToNativeMethodChannel().setNativeToken(_token);
  }

  static bool get isSubjectEnable {
    if (_user == null ||
        _user?.solarTermInfo == null ||
        _user?.solarTermInfo?.enabledSubjects == null ||
        _user?.solarTermInfo?.enabledSubjects?.isEmpty == true) {
      return false;
    }
    return true;
  }

  static syncLanguageCodeToNative({bool isFirst = false}) {
    ToNativeMethodChannel.sharedInstance().isIphoneX();
    String languageCode;
    String countryCode;
    // if(_user != null && _user.countryCode != null){//服务端有返回用户地区,直接使用客户端使用的语言+服务端的地区
    //   languageCode = Application.currentLan + '-' + _user.countryCode.toLowerCase();
    //   countryCode = _user.countryCode.toLowerCase();
    // }else {//,没有就以用户选择的地区+当前使用的语言使用'-'分隔
    languageCode = Application.currentAppLanguageType + '-' + (Application._userRegion).toLowerCase();
    countryCode = (Application._userRegion).toLowerCase();
    // }

    // if (Platform.isIOS) {
    //   ToNativeMethodChannel.sharedInstance()
    //       .syncLanguageToNative(languageCode, countryCode, isFirst);
    // } else {
    //   ToNativeMethodChannel.sharedInstance().syncLanguageToNative(
    //       Application.currentAppLanguageType, countryCode, isFirst);
    // }
  }

  static syncLanguageCodeToLoginPlugin() {
    String languageCode = currentAppLanguageType;
    // if (languageCode == "zh-cn") {
    //   languageCode = "zh";
    // }
    String countryCode = (Application._userRegion).toLowerCase();
    LoginPluginConfig.sharedInstance().configUserRegionLanguage(countryCode, languageCode, Application.deviceRegion);
    Log.d("同步国家语言信息给插件：$languageCode 国家码：$countryCode");
  }

  static _saveToken({String? value}) async {
    SharedPreferences prefs = sp;
    if (value == null || value.isEmpty) {
      prefs.remove('token');
      return;
    }
    prefs.setString('token', value);
  }

  static Future<void> clearUserInfo() async {
    SharedPreferences prefs = sp;
    prefs.remove('user');
    prefs.remove('token');
    prefs.remove('mallToken');
    user = null;
    _token = "";

    sp.setStringList('search_history', []);
    syncCacheMangerHttpRequestHeader();
  }

  static saveUser({User? value}) async {
    User? saved = value ?? _user;
    SharedPreferences prefs = sp;
    Log.e("保存用户：${saved?.toString()}");
    if (saved == null) {
      prefs.remove('user');
      return;
    }
    user = saved;
    String userJsonString = json.encode(saved);
    prefs.setString('user', userJsonString);
    NiimbotEventBus.getDefault().post("refreshUser");
  }

  static set user(User? value) {
    _user = value;
    if (value != null && !value.isVip) {
      Log.e("VIP状态：${value.isVip}");
    }
    // String userJson = json.encode(value);
    Map param = {"loginData": value?.toJson()};
    // ToNativeMethodChannel.sharedInstance().reportLoginSuccess(json.encode(param));
  }

  static updateUserFromBusiness(Map<String, dynamic> businessData) {
    _user ??= User();
    if (businessData.containsKey('phone') && businessData['phone'].toString().isNotEmpty) {
      _user?.phone = businessData['phone'] ?? '';
    }

    String areaCode_ = businessData['areaCode'] ?? '86';
    _user?.areaCode = areaCode_;
    if (businessData.containsKey('email') && businessData['email'].toString().isNotEmpty) {
      _user?.email = businessData['email'] ?? '';
    }
    _user?.createTime = businessData['firstLoginTime'];
    // user?.profileUrl = businessData['profileUrl'];
    // user?.nickName = businessData['displayName'];
    if (null != _user) {
      SharedPreferences prefs = sp;
      String userJsonString = json.encode(_user);
      prefs.setString('user', userJsonString);
    }
    NiimbotEventBus.getDefault().post("refreshUser");
  }

  static updateUser(Map<String, dynamic> loginData) async {
    _user ??= User();
    _user?.unionId = loginData['uid'];
    double uid = double.parse(loginData['userId'].toString());
    _user?.id = uid.toInt();
    _user?.phone = loginData['phone'];
    _user?.email = loginData['email'];
    _user?.countryCode = loginData['regionCode'];
    _user?.profileUrl = loginData['avatar'];
    _user?.nickName = loginData['nickname'];
    _user?.uid = loginData['displayUId'];
    _user?.areaCode = loginData['areaCode'];
    if (loginData['socialNetworks'] != null) {
      List<SocialInfo>? socialInfos = [];
      loginData['socialNetworks'].forEach((value) {
        Map<String, dynamic> v = Map<String, dynamic>.from(value);
        String displayName = v['nickname'] ?? "";
        String profileUrl = v['avatar'] ?? "";
        if ((_user?.nickName == null || _user?.nickName?.isEmpty == true) && displayName.isNotEmpty) {
          _user?.nickName = displayName;
        }
        if ((_user?.profileUrl == null || _user?.profileUrl?.isEmpty == true) && profileUrl.isNotEmpty) {
          _user?.profileUrl = profileUrl;
        }
        SocialInfo socialInfo = SocialInfo.fromJson(v);
        _user?.isAppleBinded = socialInfo.platform?.toUpperCase() == "APPLE";
        _user?.isGoogleBinded = socialInfo.platform?.toUpperCase() == "GOOGLE";
        _user?.isWechatBinded = socialInfo.platform?.toUpperCase() == "WECHAT_OPEN";
        _user?.isLineBinded = socialInfo.platform?.toUpperCase() == "LINE";
        _user?.isFBBinded = socialInfo.platform?.toUpperCase() == "FACEBOOK";
        _user?.isQQBinded = socialInfo.platform?.toUpperCase() == "QQ";
        socialInfos.add(socialInfo);
      });
      _user?.socialNetworks = socialInfos;
    }
    if (_user?.profileUrl == null || _user?.profileUrl?.isEmpty == true) {
      _user?.profileUrl = LoginPluginHostApiNiimbot.defaultUserIcon;
    }
    if (null != _user) {
      SharedPreferences prefs = sp;
      String userJsonString = json.encode(_user);
      prefs.setString('user', userJsonString);
    }
    NiimbotEventBus.getDefault().post("refreshUser");
  }

  //是否为海外用户,
  static bool get isForeign {
    if (_userRegion.toLowerCase() == 'cn') {
      return false;
    }
    return true;
  }

  static bool get isChineseLanguage {
    return Application.currentLan.startsWith('zh');
  }

  static bool get isSimpleChineseLanguage {
    return currentAppLanguageType.toLowerCase() == "zh-cn";
  }

/*当前使用多语言, 不包含地区,例如, zh,zh-hant, en*/
  static String get currentLan {
    return getAppLanguage();
  }

  static String getAppLanguage() {
    var languageType = currentAppLanguageType;
    if (currentAppLanguageType.toLowerCase() == "zh-cn") {
      languageType = "zh";
    }
    return languageType;
  }

  /*获取用户当前地区的显示名称*/
  static String getUserCountryName() {
    var languageType = currentAppLanguageType;
    if (currentAppLanguageType.toLowerCase() == "zh-cn") {
      languageType = "zh";
    }
    return LoginPluginApi.getAreaDisplayName(_user?.countryCode ?? "CN", languageType);
  }

  /*获取用户是否是VIP*/
  static bool isVIP() {
    return true;
  }

  static Locale getFlutterLocale() {
    if (currentAppLanguageType.toLowerCase() == "ar" && isForceLTR) {
      return const Locale('en', "US");
    }
    switch (currentAppLanguageType.toLowerCase()) {
      case "zh-cn":
        return const Locale.fromSubtags(languageCode: "zh", scriptCode: "Hans");
      case "zh-cn-t":
        return const Locale.fromSubtags(languageCode: "zh", countryCode: "TW");
      case "ja":
        return const Locale('ja', "JP");
      case "ru":
        return const Locale('ru', "RU");
      case "ko":
        return const Locale('ko', "KR");
      case "fr":
        return const Locale('fr', "FR");
      case "it":
        return const Locale('it', "IT");
      case "de":
        return const Locale('de', "DE");
      case "es":
        return const Locale('es', "ES");
      case "Polish":
        return const Locale('pl', "PL");
      case "Czech":
        return const Locale('cs', "CZ");
      case "af":
        return const Locale('nl', "NL");
      case "ind":
        return const Locale('id', "ID");
      case "th":
        return const Locale('th', "TH");
      case "pt":
        return const Locale('pt', "PT");
      case "ar":
        return const Locale('ar', "SA");
      case "hi":
        return const Locale('hi', "IN");
      default:
        return const Locale('en', "US");
    }
  }
}

extension CustomConnectivity on Connectivity {
  Future<ConnectivityResult> customCheckConnectivity() async {
    if (Platform.isAndroid) {
      bool hasNet = await ToNativeMethodChannel.getNativeNetworkState();
      if (hasNet && !NiimbotNetworkManager().isOutOfService()) {
        return ConnectivityResult.mobile;
      } else {
        return ConnectivityResult.none;
      }
    } else {
      ConnectivityResult result = await Connectivity().checkConnectivity();
      if (result != ConnectivityResult.none &&
          result != ConnectivityResult.bluetooth &&
          !NiimbotNetworkManager().isOutOfService()) {
        return ConnectivityResult.mobile;
      } else {
        return ConnectivityResult.none;
      }
    }
  }
}
