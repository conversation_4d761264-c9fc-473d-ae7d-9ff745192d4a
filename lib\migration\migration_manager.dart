import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:niimbot_template/models/template_data.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/niimbot_db_config.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'migration_handler.dart';
import 'migration_registry.dart';

/**
 * 表迁移管理 native-sqlite到flutter-isar
 */
class MigrationManager {
  static final MigrationManager _instance = MigrationManager._internal();

  factory MigrationManager() => _instance;

  MigrationManager._internal();
  bool _isProcessing = false;

  // 在应用启动阶段调用
  Future<void> setupDataMigration() async {
    if (_isProcessing) {
      return;
    }
    try{
      NiimbotLogTool.writeLogToFile({"MigrationManager": '开始执行数据迁移'});
      _isProcessing = true;
      MigrationRegistry.register<TemplateData>(TableDescrp.templateData, TemplateMigrationHandler());
      // 注册文件夹迁移处理器
      MigrationRegistry.register<FolderModel>(TableDescrp.folder, FolderMigrationHandler());
      // iOS存在云模版库，需要迁移
      if (Platform.isIOS) {
        MigrationRegistry.register<TemplateData>(TableDescrp.cloudTemplateData, TemplateCloudMigrationHandler());
      }
      await MigrationRegistry.runAllMigrations();
      ToNativeMethodChannel.nativeTemplateNeedRefresh();
      NiimbotLogTool.writeLogToFile({"MigrationManager": '数据迁移完成'});
      _isProcessing = false;
    } catch (e) {
      LogUtil.d('MigrationManager 迁移出现异常${e}');
      NiimbotLogTool.writeLogToFile({"MigrationManager Error": 'MigrationManager 迁移出现异常${e}'});
    }finally{
      _isProcessing = false;
    }
  }
}
