package com.niimbot.viplibrary

import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Bitmap
import android.graphics.Rect
import android.graphics.drawable.BitmapDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.widget.CheckBox
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.blankj.utilcode.util.ColorUtils
import com.blankj.utilcode.util.SpanUtils
import com.google.android.material.appbar.AppBarLayout
import com.niimbot.appframework_library.BaseActivity
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.showCircleImage
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.ImageLoader
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.appframework_library.viewBindingEx
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.network.JCHttpConfig
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.utiliylibray.util.AdManager
import com.niimbot.utiliylibray.util.AdType
import com.niimbot.viplibrary.adapter.VipFuncAdapter
import com.niimbot.viplibrary.adapter.VipMaterialAdapter
import com.niimbot.viplibrary.adapter.VipPriceAdapter
import com.niimbot.viplibrary.bean.FeaturesTranslation
import com.niimbot.viplibrary.bean.PromotionBanner
import com.niimbot.viplibrary.bean.VipPackageDTO
import com.niimbot.viplibrary.databinding.ActivityVipBinding
import com.niimbot.viplibrary.repository.VipRepository
import com.niimbot.viplibrary.service.VipService
import com.niimbot.viplibrary.theme.VipTheme
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.LocaleUtils
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Timer
import java.util.TimerTask


fun launchVipActivity(context: Context, sourcePage: String = "", actName: String = "") {
    context.startActivity(Intent(context, VipActivity::class.java).apply {
        putExtra("source_page", sourcePage)
        putExtra("act_name", actName)
    })
}

/**
 * @ClassName: VipActivity
 * @Author: Liuxiaowen
 * @Date: 2021/8/24 18:37
 * @Description:
 */
class VipActivity : BaseActivity(), LoginDataEnum.LoginStatusChangeListener {

    override fun bindLayout() = R.layout.activity_vip
    override fun initView(savedInstanceState: Bundle?, contentView: View?) {
        AppUtils.setStatusBarLightColor(this, ColorUtils.getColor(R.color.af_transparent), true)
        binding.vipBenefit.vipBenefitContainer.gone()
        VipBenefitHelper.localSetting(binding.vipBenefit.vipBenefitContainer)
        rtlSetting()
        if (TextHookUtil.getInstance().isSimpleChinese()) {
            binding.llReceipt?.visibility = View.VISIBLE
        }else{
            binding.llReceipt?.visibility = View.GONE
        }
        promotionBanner = VipRetentionPromotionHelper.initVipPromotionBanner(
            binding.vipPromotion.vipPromotionContentTv,
            binding.vipPromotion.vipPromotionCountdownTv,
            binding.vipPromotion.vipPromotionFlash,
            binding.vipPromotion.vipPromotionContainer
        )
    }

    private val binding by viewBindingEx(ActivityVipBinding::inflate)
    var priceAdapter = VipPriceAdapter(arrayListOf())
    var funcAdapter = VipFuncAdapter(arrayListOf())
    var materialAdapter = VipMaterialAdapter(arrayListOf())
    var sourcePage: String = ""
    var actName: String = ""
    var anchorSubscribeId: String = ""
    var popSku: Boolean = false
    private var selectPriceId: Int = -1
    private var hasShowPromotionDialog = false
    private lateinit var promotionBanner: PromotionBanner
    private var userLogin = false

    fun rtlSetting() {
        if (LocaleUtils.isRtl(this)) {
            val drawableArrowRightYellowRtf =
                resources.getDrawable(R.mipmap.ic_arrow_right_yellow_rtl)
            val drawableArrowRightRtf = resources.getDrawable(R.mipmap.ic_vip_arrow_right_rtl)
            binding.icBackDy.setImageResource(R.mipmap.vl_ic_back_white_rtl)
            binding.icBack.setImageResource(R.mipmap.vl_ic_back_white_rtl)
            binding.goToDetailValidIv.setImageResource(R.mipmap.go_to_detail_valid_arrow_rtl)
            binding.tvPromotionCode.setCompoundDrawablesWithIntrinsicBounds(
                drawableArrowRightRtf,
                null,
                null,
                null
            )
            binding.tvPayHistory.setCompoundDrawablesWithIntrinsicBounds(
                drawableArrowRightRtf,
                null,
                null,
                null
            )
            binding.cancelSubscribe.setCompoundDrawablesWithIntrinsicBounds(
                drawableArrowRightRtf,
                null,
                null,
                null
            )
        } else {
            val drawableArrowRightYellow = resources.getDrawable(R.mipmap.ic_arrow_right_yellow)
            val drawableArrowRight = resources.getDrawable(R.mipmap.ic_vip_arrow_right)
            binding.icBackDy.setImageResource(R.mipmap.vl_ic_back_white)
            binding.icBack.setImageResource(R.mipmap.vl_ic_back_white)
            binding.goToDetailValidIv.setImageResource(R.mipmap.go_to_detail_valid_arrow)
            binding.tvPromotionCode.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                drawableArrowRight,
                null
            )
            binding.tvPayHistory.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                drawableArrowRight,
                null
            )
            binding.cancelSubscribe.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                drawableArrowRight,
                null
            )
        }
        if (TextHookUtil.getInstance().forceLTR) {
            binding.tvPrivacyHint.textDirection = View.TEXT_DIRECTION_LTR
        }
    }

    override fun doBusiness() {
        userLogin = LoginDataEnum.isLogin
        LoginDataEnum.registerChange(this)
        initEvent()
        initList()
        startTimer()
        VipRepository.clearCache()
        getVipData(isInit = true, requestVipPromotion = true)
        updateVipStatus(true)
        sourcePage = intent.getStringExtra("source_page") ?: ""
        actName = intent.getStringExtra("act_name") ?: ""
        anchorSubscribeId = intent.getStringExtra("anchorSubscribeId") ?: ""
        if (anchorSubscribeId.isEmpty()) {
            anchorSubscribeId = "niimbot_ydy_vip_02"
        }
        popSku = intent.getBooleanExtra("popSku",false)
    }

    private lateinit var timer: Timer
    private lateinit var handler: Handler
    private fun startTimer(){
        timer = Timer()
        handler = Handler(Looper.getMainLooper()){
            if(VipHelper.isCurrentUserVip()){
                VipRetentionPromotionHelper.checkPromotionCountdown(VipDialog.renewSelectPriceId, VipDialog.renewPromotionBanner) {
                    lifecycleScope.launch {
                        VipRepository.refreshVipData { vipPackageDTO ->
                            if(vipPackageDTO == null){
                                return@refreshVipData
                            }
                            MainScope().launch {
                                vipData = vipPackageDTO
                                VipDialog.refreshVipRenewPriceList(vipData!!)
                            }
                        }
                    }
                }
            }
            else {
                VipRetentionPromotionHelper.checkPromotionCountdown(selectPriceId, promotionBanner) {
                    lifecycleScope.launch {
                        VipRepository.refreshVipData { vipPackageDTO ->
                            if(vipPackageDTO == null){
                                return@refreshVipData
                            }
                            MainScope().launch {
                                vipData = vipPackageDTO
                                priceAdapter.setNewData(vipPackageDTO.vipPackageDTO.prices)
                                funcAdapter.setNewData(vipPackageDTO.featuresTranslations)
                                materialAdapter.setNewData(vipPackageDTO.contentImages)
                                binding.vipBenefit.vipBenefitContainer.visible()
                                VipBenefitHelper.showVipBenefit(
                                    binding.vipBenefit.vipBenefitContainer,
                                    vipPackageDTO.benefitList
                                )
                                freshPromotions()
                                checkVipActivity()
                            }
                        }
                    }
                }
            }
            false
        }
        timer.schedule(object : TimerTask() {
            override fun run() {
                handler.sendEmptyMessage(0)
            }
        }, 0, 1000)
    }

    private fun initEvent() {

        var offset = 0
        binding.appBar.addOnOffsetChangedListener(AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            offset = verticalOffset
        })

        binding.vipInfo.setOnNotDoubleClickListener {
            if (offset < 0) {
                return@setOnNotDoubleClickListener
            }
            LoginDataEnum.userModule?.vipInfo?.let {
                if (VipHelper.isCurrentUserVip()) {
                    NiimbotGlobal.gotoWeb(this, JCHttpConfig.WEB_PAY_DETAIL_URL, "app100000443")
                }
            }
        }
        binding.rlUserLogin.setOnNotDoubleClickListener {
            if (!LoginDataEnum.isLogin) {
                LoginDataEnum.loginCheck(this, confirmListener = {
                }) {
                }
            }
        }
        binding.icBack.setOnNotDoubleClickListener { handleBack() }
        binding.icBackDy.setOnNotDoubleClickListener { handleBack() }

        binding.btnRenew.setOnNotDoubleClickListener {
            VipDialog.showVipRenewDialog(
                this,
                sourcePage,
                actName,
                anchorSubscribeId = anchorSubscribeId,
                fromVipActivity = true
            )
            BuriedHelper.trackEvent(
                "click", "029_087", hashMapOf(
                    Pair("source_page", sourcePage),
                    Pair("act_name", actName)
                )
            )
        }

        binding.btnPay.setOnNotDoubleClickListener {
            if (LoginDataEnum.isLogin) {
                if (!binding.cbPrivacy.isChecked && priceAdapter.getSelectItem() != null) {
                    VipDialog.showConfirmPrivacyDialog(this,VipType.NORMAL,priceAdapter.getSelectItem()!!.autoRenewal) {
                        if (it) {
                            (binding.cbPrivacy as CheckBox).isChecked = true
                            priceAdapter.getSelectItem()?.let {
                                VipHelper.toPayPage(this, it, this.vipData?.gifts)
                                BuriedHelper.trackEvent(
                                    "click",
                                    if (VipHelper.isVipExpired()) "029_087" else "029_079",
                                    hashMapOf(
                                        Pair("source_page", sourcePage),
                                        Pair("act_name", actName)
                                    )
                                )
                            }
                        }
                    }
                    BuriedHelper.trackEvent(
                        "show",
                        "029_321",
                        hashMapOf(
                            Pair("source_page", sourcePage),
                            Pair("act_name", actName)
                        )
                    )
                } else {
                    priceAdapter.getSelectItem()?.let {
                        VipHelper.toPayPage(this, it, this.vipData?.gifts)
                    }
                }
                BuriedHelper.trackEvent(
                    "click", if (VipHelper.isVipExpired()) "029_087" else "029_079", hashMapOf(
                        Pair("source_page", sourcePage),
                        Pair("act_name", actName)
                    )
                )
            } else {
     //           showLoading()
                LoginDataEnum.loginCheck(this, confirmListener = {
      //              dismissLoading()
                }) {
      //              dismissLoading()
                }
            }

        }
        (binding.cbPrivacy as CheckBox).setOnCheckedChangeListener { buttonView, isChecked ->
            BuriedHelper.trackEvent(
                "click", "029_320", hashMapOf(
                    Pair("source_page", sourcePage),
                    Pair("act_name", actName)
                )
            )
        }

        binding.appBar.addOnOffsetChangedListener(object : AppBarLayout.OnOffsetChangedListener {
            override fun onOffsetChanged(appBarLayout: AppBarLayout?, verticalOffset: Int) {
                offset = verticalOffset
            }
        })

        binding.vipInfo.setOnNotDoubleClickListener {
            if (offset < 0) {
                return@setOnNotDoubleClickListener
            }
            LoginDataEnum.userModule?.vipInfo?.let {
                if (VipHelper.isCurrentUserVip()) {
                    NiimbotGlobal.gotoWeb(this, JCHttpConfig.WEB_PAY_DETAIL_URL, "app100000443")
                }
            }
        }

        binding.vipShopCoupon.vipShopCouponContainer.setOnNotDoubleClickListener {
            if (VipHelper.isCurrentUserVip()) {
                VipDialog.toShop(this)
                BuriedHelper.trackEvent(
                    "click",
                    "029_322",
                    hashMapOf(
                        Pair("source_page", sourcePage),
                        Pair("act_name", actName),
                        Pair("is_vip_buy", 1)
                    )
                )
            } else {
                priceAdapter.getSelectItem()?.let {
                    VipDialog.showShopCopunDialog(
                        this,
                        "${JCHttpConfig.H5_ROOT}/coupons/niimbot?productId=${it.productId}",
                        0.95f, false
                    )
                    BuriedHelper.trackEvent(
                        "click",
                        "029_322",
                        hashMapOf(
                            Pair("source_page", sourcePage),
                            Pair("act_name", actName),
                            Pair("sku_name", LanguageUtil.findLanguageString(it.getDisplayTitle())),
                            Pair("is_vip_buy", 0)
                        )
                    )

                }

            }
        }
//        binding.btnPay.isEnabled = LoginDataEnum.isLogin
//        binding.btnPay.alpha = if (LoginDataEnum.isLogin) 1f else .5f

        priceAdapter.listener = { priceBean ->
            selectPriceId = priceBean.id
            binding.tvAutopayHints.visible(priceBean.autoRenewal)
            initPrivacyView(priceBean.autoRenewal);
            if (priceBean.autoRenewal) {
                if (priceBean.isAnnual()) {
                    binding.tvAutopayHints.text = LanguageUtil.findLanguageString(
                        "app100001625",
                        arrayListOf(priceBean.getPriceSymbol(), priceBean.formatAutoPayPrice())
                    )
                } else {
                    binding.tvAutopayHints.text = LanguageUtil.findLanguageString(
                        "app100001626",
                        arrayListOf(priceBean.getPriceSymbol(), priceBean.formatAutoPayPrice())
                    )
                }
            }
            if (priceBean.ifShowShopCoupon()) {
                binding.vipShopCoupon.llCouponInfo.visibility = View.VISIBLE
                binding.vipShopCoupon.tvCouponVip.visibility = View.GONE
                binding.vipShopCoupon.shopCouponCount.text = LanguageUtil.findLanguageString(
                    "app100001632",
                    arrayListOf(priceBean.couponCount.toString())
                )
                binding.vipShopCoupon.shopCouponDiscount.text = LanguageUtil.findLanguageString(
                    "app100001629",
                    arrayListOf(priceBean.getFormatCouponMaxQuota())
                )
                binding.vipShopCoupon.vipShopCouponContainer.visible()
            } else {
                binding.vipShopCoupon.vipShopCouponContainer.gone()
            }
            if(!VipHelper.isCurrentUserVip()) {
                VipRetentionPromotionHelper.checkPriceRetentionPromotionStatus(priceBean.id, promotionBanner)
            }
            if (TextHookUtil.getInstance().isArabic()) {
                binding.btnPay.text =
                    "${priceBean.formatDisplaySalePrice()}${priceBean.getPriceSymbol()} ${
                        LanguageUtil.findLanguageString("app01511")
                    }"
            } else {
                binding.btnPay.text =
                    "${priceBean.getPriceSymbol()}${priceBean.formatDisplaySalePrice()} ${
                        LanguageUtil.findLanguageString("app01511")
                    }"
            }
        }

        binding.tvPromotionCode.setOnNotDoubleClickListener {
            launchVipPromotionCodeActivity(this, sourcePage, actName)
            BuriedHelper.trackEvent(
                "click", "029_236", hashMapOf(
                    Pair("b_name", "使用会员兑换码")
                )
            )
        }
        binding.tvPromotion.setOnNotDoubleClickListener {
            showMorePop(it)
//            launchVipPromotionCodeActivity(this, sourcePage, actName)
//            BuriedHelper.trackEvent("click", "029_252")
        }
        binding.tvPromotionDy.setOnNotDoubleClickListener {
            showMorePop(it)
//            launchVipPromotionCodeActivity(this, sourcePage, actName)
//            BuriedHelper.trackEvent("click", "029_252")
        }
        binding.tvPayHistory.setOnNotDoubleClickListener {
            LoginDataEnum.loginCheck(this) {
                NiimbotGlobal.gotoWeb(this, JCHttpConfig.WEB_PAY_DETAIL_URL, "app100000443")
            }
            BuriedHelper.trackEvent(
                "click", "029_236", hashMapOf(
                    Pair("b_name", "生效明细")
                )
            )
        }
        binding.llReceipt.setOnNotDoubleClickListener {
            if(!NetworkUtils.isConnected()){
                showToast("app01139")
                return@setOnNotDoubleClickListener
            }
            LoginDataEnum.loginCheck(this) {
                NiimbotGlobal.gotoWeb(this, "${JCHttpConfig.H5_ROOT}/invoice/niimbot/index", "",showTitleBar = false)
            }
            BuriedHelper.trackEvent(
                "click", "029_236", hashMapOf(
                    Pair("b_name", "开发票")
                )
            )
        }
        binding.llCancelSubscribe.setOnNotDoubleClickListener {
            if(!NetworkUtils.isConnected()){
                showToast("app01139")
                return@setOnNotDoubleClickListener
            }
            NiimbotGlobal.gotoWeb(this, "${JCHttpConfig.WEB_URL}/h5#/unsubscribe", "app100000150")
            BuriedHelper.trackEvent(
                "click", "029_236", hashMapOf(
                    Pair("b_name", "如何取消订阅服务")
                )
            )
        }
        binding.tvCancelSubscribe.setOnNotDoubleClickListener {
            binding.llCancelSubscribe.performClick()
        }
        initBenfitsPanelEvent()
        var hasProcessBenifitPoint = false
        var hasProcessServicePoint = false
        binding.vScroll.setOnScrollChangeListener { _, _, scrollY, _, _ ->
            val scrollBounds = Rect()
            if (binding.promotionLayout.getLocalVisibleRect(scrollBounds) && !hasProcessServicePoint) {//可见
                hasProcessServicePoint = true
                BuriedHelper.trackEvent(
                    "slide", "029_080", hashMapOf(
                        Pair("source_page", sourcePage),
                        Pair("act_name", actName),
                        Pair("tab_name", "VIP服务"),
                    )
                )
            }
            if (binding.vipBenefit.vipBenefitContainer.getLocalVisibleRect(scrollBounds) && !hasProcessBenifitPoint) {//可见
                hasProcessBenifitPoint = true
                BuriedHelper.trackEvent(
                    "slide", "029_080", hashMapOf(
                        Pair("source_page", sourcePage),
                        Pair("act_name", actName),
                        Pair("tab_name", "VIP权益对比"),
                    )
                )
            }


        }
    }

    private fun showMorePop(anchorView: View) {
        VipDialog.showVipMorePop(this, anchorView) {
            launchVipPromotionCodeActivity(this, sourcePage, actName)
            BuriedHelper.trackEvent("click", "029_252")
        }

    }

    private fun initPrivacyView(autoRenewal: Boolean) {
        val text1 = LanguageUtil.findLanguageString("app01350", "")
        val text2 = LanguageUtil.findLanguageString("app01508", "")
        val text3 = LanguageUtil.findLanguageString("app01509", "")
        val blueColor = resources.getColor(R.color.blue_537fb7)
        CoroutineScope(Dispatchers.Main).launch {
            //会员中心底部会员协议偶先进去之后不显示第二协议得问题，加延时器处理后正常
            delay(100)
            SpanUtils.with(binding.tvPrivacyHint).append(text1)
                .append(text2)
                .setClickSpan(
                    blueColor, false
                ) {
                    VipDialog.toVipPrivacy(this@VipActivity)
                    BuriedHelper.trackEvent(
                        "click", "029_237_222", hashMapOf(
                            Pair("b_name", "会员服务协议")
                        )
                    )
                }.let {
                    if (autoRenewal) {
                        it.append(text3).setClickSpan(
                            blueColor, false
                        ) {
                            VipDialog.toVipAutoPayPrivacy(this@VipActivity)
                            BuriedHelper.trackEvent(
                                "click", "029_237_222", hashMapOf(
                                    Pair("b_name", "自动续费服务协议")
                                )
                            )
                        }.let {
                            it.create()
                        }
                    }else{
                        it.create()
                    }
                }
        }



    }

    var tabNames =
        arrayListOf("SKU信息", "VIP专属功能", "VIP海量内容")

    private fun initList() {
        binding.priceList.layoutManager =
            CustomLinearLayoutManager(this, LinearLayoutManager.HORIZONTAL)
        binding.priceList.adapter = priceAdapter
        binding.funcList.layoutManager =
            GridLayoutManager(this, 2, LinearLayoutManager.HORIZONTAL, false)
        binding.funcList.addItemDecoration(HorizontalGridSpaceItemDecoration(2, 12, 16))
        binding.funcList.adapter = funcAdapter
        funcAdapter.setOnItemClickListener { adapter, _, position ->
            try {
                var model = adapter.data[position]
                if (model is FeaturesTranslation && !model.link.isNullOrEmpty()) {
                    VipDialog.showShopCopunDialog(this, model.link!!, 0.95f, true)
                    BuriedHelper.trackEvent(
                        "show",
                        "029_324",
                        hashMapOf(
                            Pair("source_page", sourcePage),
                            Pair("act_name", actName),
                            Pair("b_name", model.title)
                        )
                    )
                }
                if (model is FeaturesTranslation) {
                    BuriedHelper.trackEvent(
                        "click",
                        "029_323",
                        hashMapOf(
                            Pair("source_page", sourcePage),
                            Pair("act_name", actName),
                            Pair("b_name", model.title)
                        )
                    )
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }

        }
        binding.materialList.adapter = materialAdapter
        arrayOf(
            binding.priceList,
            binding.funcList,
            binding.materialList,
        )
            .forEachIndexed { index, recyclerView ->
                recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
                    override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                        super.onScrollStateChanged(recyclerView, newState)
                        if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                            BuriedHelper.trackEvent(
                                "slide", "029_080", hashMapOf(
                                    Pair("source_page", sourcePage),
                                    Pair("act_name", actName),
                                    Pair("tab_name", tabNames[index]),
                                )
                            )
                        }
                    }
                })
            }
    }

    var vipData: VipPackageDTO? = null
    private fun getVipData(isInit: Boolean = true, requestVipPromotion: Boolean = false) {
        showLoading()
        lifecycleScope.launch {
            VipRepository.getVipPackageDTO { vipPackageDTO ->
                vipPackageDTO?.let { vipPackageDTO ->
                    MainScope().launch {
                        vipData = vipPackageDTO
                        priceAdapter.setNewData(vipPackageDTO.vipPackageDTO.prices)
                        priceAdapter.retentionActivityResetDefaultSelect(vipPackageDTO.vipPackageDTO.prices)
                        if(isInit){
                            priceAdapter.freshSelect(anchorSubscribeId)
                        }
                        funcAdapter.setNewData(vipPackageDTO.featuresTranslations)
                        materialAdapter.setNewData(vipPackageDTO.contentImages)
                        binding.vipBenefit.vipBenefitContainer.visible()
                        VipBenefitHelper.showVipBenefit(
                            binding.vipBenefit.vipBenefitContainer,
                            vipPackageDTO.benefitList
                        )
                        freshPromotions()
                        checkVipActivity()
                        dismissLoading()
                    }
                } ?: kotlin.run {
                    showToast("app100000022")
                    dismissLoading()
                }
            }
            if(requestVipPromotion) {
                VipRetentionPromotionHelper.requestVipPromotionInfo()
            }
        }
    }

    private fun initBenfitsPanelEvent() {
        binding.goToDetailValidIv.visibility =
            if (VipHelper.isCurrentUserVip()) View.VISIBLE else View.GONE
    }


    private fun isGoogleChannel(): Boolean {
        return VipHelper.isGoogleChannel(vipData?.vipPackageDTO?.prices)
    }

    private fun freshPromotions() {
        vipData?.activities?.let {
            val filterBannerList = it.filter { banner -> (banner.effectiveEndTime == 0L ||  banner.effectiveEndTime > System.currentTimeMillis()) && AdManager.canPopupAd(AdType.VIP_BANNER,banner.imgUrl,banner.exhibitionFrequency) }
            VipBannerHelper.initBanner(this, binding.vipBanner, filterBannerList)
        }

//        binding.vipBanner.gone()
//        VipBannerHelper.initBannerList(this, binding.bannerList, vipData?.activities)
    }

    private fun checkVipActivity() {
        var showActivity =
            vipData?.vipPackageDTO?.prices?.firstOrNull { it.showVipActivity } != null
        VipService.getVipTheme(showActivity) {
            theme = it
            updateVipStatus(true)
        }
        if (VipHelper.isCurrentUserVip() && TextHookUtil.getInstance()
                .isSimpleChinese() && (vipData != null && !vipData!!.vipPackageDTO.prices[0].canPayWithGoogle())
        ) {
            binding.vipShopCoupon.llCouponInfo.visibility = View.GONE
            binding.vipShopCoupon.tvCouponVip.visibility = View.VISIBLE
            binding.vipShopCoupon.vipShopCouponContainer.visible()
//            val params =
//                binding.vipShopCoupon.vipShopCouponContainer.layoutParams as LinearLayout.LayoutParams
//            params.topMargin = SizeUtils.dp2px(5f)
//            binding.vipShopCoupon.vipShopCouponContainer.layoutParams = params
        }
    }

    private var theme: VipTheme? = null
    private fun updateVipStatus(isInit: Boolean = false) {
        binding.userAvatar.showCircleImage(R.mipmap.ic_user_default)
        val isUserVip = VipHelper.isCurrentUserVip()
        val vipColor =
            ColorUtils.getColor(if (isUserVip) R.color.yellow_78430e else R.color.white)
        if (LoginDataEnum.userModule?.profile_url?.isNotEmpty() == true) {
            if(LoginDataEnum.userModule?.profile_url!="null"){
                LoginDataEnum.userModule?.profile_url?.let { binding.userAvatar.showCircleImage(it) }
            }else{
                binding.userAvatar.showCircleImage(R.mipmap.ic_user)
            }
        } else {
            binding.userAvatar.showCircleImage(R.mipmap.ic_user)
        }
        binding.tvCancelSubscribe.visible(VipHelper.isCurrentSubscriptionMember())
        // 2021/9/13 Ice_Liu 非自动续费会员显示"立即续费按钮"
        binding.btnRenew.visible(VipHelper.isCurrentUserVip() && !VipHelper.isCurrentSubscriptionMember())
        binding.vipPayLayout.visible(!isUserVip)
        binding.priceList.visible(!isUserVip)
        if(isUserVip){
            promotionBanner.rootView.gone()
        }
        binding.goToDetailValidIv.visibility = if (isUserVip) View.VISIBLE else View.GONE
        theme?.let {
//                binding.topLayout.setBackgroundColor(ColorUtils.getColor(it.topBgColor))
            binding.topLayout.setBackgroundResource(if (isUserVip) R.mipmap.vip_top_bg else R.mipmap.unvip_top_bg)
            binding.mainLayout.setBackgroundResource(it.scrollBg)
            binding.vipPayLayout.setBackgroundColor(ColorUtils.getColor(it.payBgColor))
            if (it.userBottomTintColor > 0) {
                binding.userBottomLayout.imageTintList =
                    ColorStateList.valueOf(ColorUtils.getColor(it.userBottomTintColor))
            }
//                user_container.setBackgroundResource(it.userContainerBg)
            binding.userLayout.setBackgroundResource(it.userLayoutBg)
            val cardTitleColor = ColorUtils.getColor(it.cardTitleColor)
            binding.tvMaterial.setTextColor(cardTitleColor)
            binding.tvMaterial.setTextColor(cardTitleColor)
            binding.tvFunc.setTextColor(cardTitleColor)
            binding.tvVipService.setTextColor(cardTitleColor)
            val userInfoColor = ColorUtils.getColor(it.userInfoColor)
            binding.userId.setTextColor(userInfoColor)
            binding.vipStatus.setTextColor(userInfoColor)
//                binding.vipStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
//                    if (isUserVip) R.mipmap.ic_vip_tag_enable else 0,
//                    0,
//                    0,
//                    0
//                )
            binding.vipInfo.setTextColor(userInfoColor)
            binding.tvCancelSubscribe.setTextColor(userInfoColor)
            binding.topDivide.setBackgroundColor(ColorUtils.getColor(it.bottomDivideColor))
            binding.btnPay.setBackgroundResource(it.payBtnBg)
            binding.btnPay.setTextColor(ColorUtils.getColor(it.payBtnTextColor))
            binding.tvAutopayHints.setTextColor(ColorUtils.getColor(it.autoRenewHintsColor))
                binding.tvAutopayHints.visible(!isUserVip)
            binding.promotionLayout.setBackgroundResource(it.promotionBg)
            binding.tvPromotionCode.setTextColor(ColorUtils.getColor(it.promotionTextColor))
            binding.tvActivityName.visible(!it.activityName.isNullOrBlank() && !isUserVip)
            binding.tvActivityName.text = it.activityName
            binding.tvActivityName.setTextColor(cardTitleColor)
            priceAdapter.freshTheme(it)
        } ?: kotlin.run {
//                binding.topLayout.setBackgroundColor(ColorUtils.getColor(if (isUserVip) R.color.c232325 else R.color.red_fb4b42))
            binding.topLayout.setBackgroundResource(if (isUserVip) R.mipmap.vip_top_bg else R.mipmap.unvip_top_bg)
//                user_container.setBackgroundResource(if (isUserVip) R.drawable.shape_232325 else R.drawable.shape_fb4b42)
            binding.userLayout.setBackgroundResource(if (isUserVip) R.mipmap.vip_header_bg else R.mipmap.unvip_header_bg)
            binding.userId.setTextColor(vipColor)
            binding.vipStatus.setTextColor(vipColor)
//                binding.vipStatus.setCompoundDrawablesRelativeWithIntrinsicBounds(
//                    if (isUserVip) R.mipmap.ic_vip_tag_enable else 0,
//                    0,
//                    0,
//                    0
//                )
            binding.vipInfo.setTextColor(vipColor)
            binding.tvCancelSubscribe.setTextColor(vipColor)
        }
        val vipStatusColor =
            ColorUtils.getColor(if (isUserVip) R.color.yellow_78454 else R.color.white)
        binding.vipStatus.setTextColor(vipStatusColor)
        binding.viewDivide.setBackgroundColor(ColorUtils.getColor(if (isUserVip) R.color.user_divide_vip else R.color.user_divide_unvip))
//            binding.userId.text = "UID：${LoginDataEnum.getDisplayUID()}"

        if(LoginDataEnum.isLogin){
            binding.userId.text = "${LoginDataEnum.getDisplayNickname()}"
            binding.vipStatus.text =
                if (isUserVip) "${VipHelper.getUserVipName()}${VipHelper.getAutoRenewTips()}" else if (VipHelper.isVipExpired()) "app100000001" else "app01494"
        }else{
            binding.userId.text = "app01191"
            binding.vipStatus.text ="app00210"

        }
        binding.vipInfo.text =
            if (isUserVip) VipHelper.getUserVipRenewTips(true) else "app100001594"
//        binding.btnPay.isEnabled = LoginDataEnum.isLogin
//        binding.btnPay.alpha = if (LoginDataEnum.isLogin) 1f else .5f
//            binding.btnPay.text = if (VipHelper.isVipExpired()) "app01512" else "app01511"
        binding.btnPay.text = "app01511"
        if (isUserVip) {
            binding.ivUserAvatarFlag.visibility = View.VISIBLE
            binding.rlUserAvatarBg.setBackgroundResource(R.drawable.shape_bg_user_avatar)
        } else {
            binding.ivUserAvatarFlag.visibility = View.GONE
            binding.rlUserAvatarBg.setBackgroundResource(R.drawable.shape_bg_user_avatar_unvip)
        }
        showTopBackground()
    }

    private fun showTopBackground() {
        val topBgUrl = LanguageUtil.findLanguageString("app100000447")
        val topBgUrlVip = LanguageUtil.findLanguageString("app100001641")
        if (VipHelper.isCurrentUserVip()) {
            if (topBgUrlVip.isNullOrBlank() || topBgUrlVip == "空") return
            ImageLoader.getBitmap(
                this,
                topBgUrlVip,
                listener = object : ImageLoader.BitmapLoadCallback {
                    override fun loadSuccess(url: String, bitmap: Bitmap) {
                        binding.topLayout.background = BitmapDrawable(resources, bitmap)
                    }

                    override fun loadFailed() {

                    }
                })
        } else {
            if (topBgUrl.isNullOrBlank() || topBgUrl == "空") return
            ImageLoader.getBitmap(
                this,
                topBgUrl,
                listener = object : ImageLoader.BitmapLoadCallback {
                    override fun loadSuccess(url: String, bitmap: Bitmap) {
                        binding.topLayout.background = BitmapDrawable(resources, bitmap)
                    }

                    override fun loadFailed() {

                    }
                })
        }

    }

    fun probationVipPromotionRefresh(vipPackageDTO: VipPackageDTO){
        vipData = vipPackageDTO
        priceAdapter.setNewData(vipPackageDTO.vipPackageDTO.prices)
        priceAdapter.retentionActivityResetDefaultSelect(vipPackageDTO.vipPackageDTO.prices)
        funcAdapter.setNewData(vipPackageDTO.featuresTranslations)
        materialAdapter.setNewData(vipPackageDTO.contentImages)
        binding.vipBenefit.vipBenefitContainer.visible()
        VipBenefitHelper.showVipBenefit(
            binding.vipBenefit.vipBenefitContainer,
            vipPackageDTO.benefitList
        )
        freshPromotions()
        checkVipActivity()
    }

    override fun onResume() {
        super.onResume()
        freshPromotions()
        BuriedHelper.trackEvent(
            "view", "029", hashMapOf(
                Pair("source_page", sourcePage),
                Pair("act_name", actName)
            )
        )
    }

    override fun reInitViewByVip() {
        super.reInitViewByVip()
        checkVipActivity()
    }

    override fun loginStatusChange(isLogin: Boolean) {
        var loginChanged = false
        if(isLogin != userLogin) {
            userLogin = isLogin
            loginChanged = true
        }
        getVipData(isInit = false, requestVipPromotion = loginChanged)
        dismissLoading()
    }

    override fun userInfoChange() {
        getVipData(isInit = false)
        dismissLoading()
    }

    private fun handleBack(){
        if(!hasShowPromotionDialog && VipRetentionPromotionHelper.isShowVipPromotionDialog()){
            VipDialog.showVipRetentionDialog(this, false)
            hasShowPromotionDialog = true
            return
        }
        finish()
    }

    override fun onBackPressed() {
        handleBack()
    }

    override fun onDestroy() {
        super.onDestroy()
        timer.cancel()
        handler.removeCallbacksAndMessages(null)
        promotionBanner.animator.cancel()
        VipHelper.clearCache()
        LoginDataEnum.unregisterChange(this)
    }
}
