package melon.south.com.baselibrary.module

import com.niimbot.appframework_library.common.module.template.item.BaseItemModuleEx
import com.niimbot.appframework_library.common.module.template.item.FlowItemModule
import com.niimbot.appframework_library.common.module.template.item.ItemType
import com.niimbot.appframework_library.common.module.template.item.LineItemModule
import com.niimbot.appframework_library.common.module.template.item.PictureItemModule
import com.niimbot.appframework_library.common.module.template.item.RectItemModule
import com.niimbot.appframework_library.common.module.template.item.TextItemModule
import com.niimbot.appframework_library.common.module.template.item.TimeItemModule
import com.niimbot.appframework_library.common.module.template.item.barcode.BarcodeItemModule
import com.niimbot.appframework_library.common.module.template.item.barcode.JCBarcodeFormat
import com.niimbot.appframework_library.common.module.template.item.qrcode.JCQrCodeFormat
import com.niimbot.appframework_library.common.module.template.item.qrcode.QrCodeItemModule
import com.niimbot.appframework_library.common.module.template.item.table.CellInfo
import com.niimbot.appframework_library.common.module.template.item.table.CombineCellInfo
import com.niimbot.appframework_library.common.module.template.item.table.TableItemModule
import com.niimbot.fastjson.annotation.JSONField
import com.niimbot.utiliylibray.util.json2Array
import java.io.Serializable

/**
 * 元素module
 */
open class BaseItemModule : Serializable {
    var itemId: String = ""
    var itemType: String = "text"
    var x: Float = 0f
    var y: Float = 0f
    var width: Float = 0f
    var height: Float = 0f
    var rotate: Int = 0
    var zIndex: Int = 0
    var isLock: Any? = 0
    //水平对齐方式，0:左对齐，1:居中对齐，2:右对齐
    var textAlignHorizonral = 1
    //垂直对齐方式，0:顶对齐，1:垂直居中，2:底对齐
    var textAlignVertical = 1
    //换行方式，1:宽高固定，内容大小自适应，2:宽度固定，高度自适应，3:宽高固定，超出内容后面加...，4:宽高固定,超出内容直裁切
    var lineMode = 2
    //文本内容，保存到服务器上的可能附加外部Excel导入信息的占位符，传给sdk后是处理后的实际文本
    var value = ""
    var dataBind:ArrayList<String>? = ArrayList<String>()

    //单词间距
    var wordSpacing = 0f
    //字符间距
    var letterSpacing = 0f
    //行间距
    var lineSpacing = 0f
    //字体编码
    var fontCode = ""
    //字体名称
    var fontFamily = ""
    //字体样式，bold,italic,underline,overline
    var fontStyle: String = ""
    //文字排版模式：1.横向排版（默认）,2.纵向（垂直）排版, 3.弧形文字排版
    var typesettingMode = 1
    var typesettingParam = intArrayOf(0, 180)
    //字号
    var fontSize = 0f
    var isTitle = false
    //导入商品的属性代号（此处不包括商品的条码代号"one_code"）
    var fieldName = ""
    //条码文字位置，0-条码下方，1-条码上方，2-不显示文字
    var textPosition = 0
    //条码文字高度
    var textHeight = 0f
    //条码或二维码编码类型
    var codeType = JCBarcodeFormat.Code128.codeValue
    //纠错级别，0:L(7%)，1:M(7%)，2:Q(7%)， 3:H(7%)
    var correctLevel = 0
    //前缀
    var prefix = ""
    //后缀
    var suffix = ""
    //起始值
    var startNumber = ""
    //递增值
    var incrementValue: Long = 0
    //补位的值
    var fixValue = "0"
    //补位的长度
    var fixLength = 0
    //线条类型，1-实线，2-虚线
    var lineType = 1
    var dashwidth: String = ""
    //图片本地保存路径
    var localUrl = ""
    //图片保存在服务器的url
    var imageUrl = ""
    //算法类型，1:阈值法，2:渐变
    var imageProcessingType = 1
    //算法参数
    var imageProcessingValue: String = ""
    //素材库图片的id，不是素材库图片时为空
    var materialId: String = ""
   // var materialType: String = ""
    var materialType: String? = null
    //边框宽度
    var lineWidth = 0f
    //形状类型，1-圆，2-椭圆，3-矩形，4-圆角矩形
    var graphType = 1
    //圆角半径
    var cornerRadius = 0f
    //日期格式
    var dateFormat = "yyyy年MM月dd日"
    //时间格式
    var timeFormat = "无"
    //是否更新为当前时间
    var dateIsRefresh = 0
    //行数
    var row: Int = 0
    //列数
    var column: Int = 0
    //行高
    var rowHeight: String = ""
    //列宽
    var columnWidth: String = ""
    //原始单元格信息
    var cells: String = ""
    //合并单元格信息
    var combineCells: String = ""
    //自定义标题，可来自excel导入或者输入
    var contentTitle: String = ""
    //标题和文本之间的分隔符号
    var delimiter: String = "："
    //是否开启了镜像
    var isOpenMirror = false
    //镜像元素id
    var mirrorId = ""
    //是否包含vip元素
    var hasVipRes = false
    //文本换行方式, 0: 按字符换行 1：按单词换行(默认)
    var lineBreakMode = 0

    //时间偏移量  画板上或预览、打印时间以(设置时间/实时时间)±偏移量为准
    var timeOffset:Int = 0
    //第一时间是否被关联 true 被关联  false 未被关联（默认） 第二时间始终为false
    var associated:Boolean=false
    //关联Id （第一时间、第二时间此Id一致
    var associateId:String=""
    //有效期
    var validityPeriod:Int=0

    //新版有效期
    var validityPeriodNew:Int = 0
        get() {
            var validityPeriodNewValue = validityPeriod
            if (validityPeriodUnit == "Associated_Day") {
                validityPeriodNewValue = validityPeriod - 1
            }
            return validityPeriodNewValue
        }
    //有效期单位
    var validityPeriodUnit:String=""

    var timeUnit:String=""
    var time:Long? = 0

    //是否活二维码
    @JSONField(name = "isLive")
    var isLive = false
    //活码的id
    var liveCodeId = ""
    //是否表单
    @JSONField(name = "isForm")
    var isForm = false
    //表单id
    var formId = ""
    //双色打印
    var elementColor = intArrayOf(255, 0, 0, 0)
    //反白打印
    var colorReverse = 0
    //table边框颜色
    var lineColor = intArrayOf(255,0,0,0)
    //table文本内容颜色
    var contentColor = intArrayOf(255,0,0,0)
    //table边框颜色标志
    var lineColorChannel = 0
    //table文本内容颜色标志
    var contentColorChannel = 0
    //打印档位，彩色为1，默认为0
    var colorChannel = 0
    //颜色档位，彩色为1，默认为0
    var paperColorIndex = 0

    // 文本框样式
    var boxStyle = ""
    // 内容样式
    var textStyle = ""

    //是否点9图, 点9图作为图片下的特殊类型
    @JSONField(name = "isNinePatch")
    var isNinePatch = false
    @JSONField(name = "isDefaultImage")
    var isDefaultImage = false
    //点9图本地路径Url
    var ninePatchLocalUrl = ""
    //点9图服务器地址,用于网络传输，上传到服务器存储
    var ninePatchUrl = ""
    var textDirection = 0

    fun toBaseItemModuleEx(): BaseItemModuleEx?{
        val elementType = ItemType.getItemType(itemType)
        val baseItemModuleEx = when(elementType){
            ItemType.TEXT -> {
                toTextItemModule()
            }
            ItemType.BARCODE -> {
                toBarcodeItemModule()
            }
            ItemType.QR_CODE -> {
                toQrCodeItemModule()
            }
            ItemType.TABLE -> {
                toTableItemModule()
            }
            ItemType.PICTURE -> {
                toPictureItemModule()
            }
            ItemType.FLOW -> {
                toFlowItemModule()
            }
            ItemType.TIME -> {
                toTimeItemModule()
            }
            ItemType.SHAPE -> {
                toRectItemModule()
            }
            ItemType.LINE -> {
                toLineItemModule()
            }
            else -> null
        }
        baseItemModuleEx?.id = itemId
        baseItemModuleEx?.type = elementType?.type ?: ItemType.TEXT.type
        baseItemModuleEx?.x = x
        baseItemModuleEx?.y = y
        baseItemModuleEx?.width = width
        baseItemModuleEx?.height = height
        baseItemModuleEx?.rotate = rotate
        baseItemModuleEx?.isLock = isLock
        baseItemModuleEx?.zIndex = zIndex
        baseItemModuleEx?.isOpenMirror = isOpenMirror
        baseItemModuleEx?.mirrorId = mirrorId
        baseItemModuleEx?.hasVipRes = hasVipRes
        baseItemModuleEx?.elementColor = elementColor
        baseItemModuleEx?.colorReverse = colorReverse
        baseItemModuleEx?.colorChannel = colorChannel
        baseItemModuleEx?.paperColorIndex = paperColorIndex
        return baseItemModuleEx
    }

    private fun toTextItemModule(): TextItemModule {
        val textItemModule = TextItemModule()
        textItemModule.textAlignHorizonral = textAlignHorizonral
        textItemModule.textAlignVertical = textAlignVertical
        textItemModule.lineMode = lineMode
        textItemModule.value = value
        textItemModule.dataBind = dataBind
        textItemModule.wordSpacing = wordSpacing
        textItemModule.letterSpacing = letterSpacing
        textItemModule.lineSpacing = lineSpacing
        textItemModule.fontCode = fontCode
        textItemModule.fontFamily = fontFamily
        textItemModule.fontStyle = json2Array(fontStyle, String::class.java) as ArrayList<String>
        textItemModule.fontSize = fontSize
        textItemModule.fieldName = fieldName
        textItemModule.isTitle = isTitle
        textItemModule.contentTitle = contentTitle
        textItemModule.delimiter = delimiter
        textItemModule.lineBreakMode = lineBreakMode
        textItemModule.typesettingMode = typesettingMode
        textItemModule.typesettingParam = typesettingParam
        textItemModule.boxStyle = boxStyle
        textItemModule.textStyle = json2Array(textStyle, String::class.java) as ArrayList<String>
        textItemModule.textDirection = textDirection
        return textItemModule
    }

    private fun toBarcodeItemModule(): BarcodeItemModule {
        val barcodeItemModule = BarcodeItemModule()
        barcodeItemModule.value = value
        barcodeItemModule.dataBind = dataBind
        barcodeItemModule.fontSize = fontSize
        barcodeItemModule.textPosition = textPosition
        barcodeItemModule.textHeight = textHeight
        barcodeItemModule.codeType = if(JCBarcodeFormat.getJCBarcodeFormatByValue(codeType) != null) codeType else JCBarcodeFormat.Code128.codeValue
        barcodeItemModule.fieldName = fieldName
        return barcodeItemModule
    }

    private fun toQrCodeItemModule(): QrCodeItemModule {
        val qrCodeItemModule = QrCodeItemModule()
        qrCodeItemModule.value = value
        qrCodeItemModule.dataBind = dataBind
        qrCodeItemModule.codeType = if(JCQrCodeFormat.getJCQrCodeFormatByValue(codeType) != null) codeType else JCQrCodeFormat.QR_CODE.codeValue
        qrCodeItemModule.correctLevel = correctLevel
        qrCodeItemModule.isLive = isLive
        qrCodeItemModule.liveCodeId = liveCodeId
        qrCodeItemModule.isForm = isForm
        qrCodeItemModule.formId = formId
        return qrCodeItemModule
    }

    private fun toTableItemModule(): TableItemModule {
        val tableItemModule = TableItemModule()
        tableItemModule.row = row
        tableItemModule.column = column
        tableItemModule.rowHeight = json2Array(rowHeight, Float::class.java) as ArrayList<Float>
        tableItemModule.columnWidth = json2Array(columnWidth, Float::class.java) as ArrayList<Float>
        tableItemModule.lineWidth = lineWidth
        tableItemModule.lineType = lineType
        tableItemModule.cells = json2Array(cells, CellInfo::class.java) as ArrayList<CellInfo>
        tableItemModule.combineCells = json2Array(combineCells, CombineCellInfo::class.java) as ArrayList<CombineCellInfo>
        tableItemModule.lineColor = lineColor
        tableItemModule.lineColorChannel = lineColorChannel
        tableItemModule.contentColor = contentColor
        tableItemModule.contentColorChannel = contentColorChannel
        return tableItemModule
    }

    private fun toPictureItemModule(): PictureItemModule {
        val pictureItemModule = PictureItemModule()
        pictureItemModule.imageUrl = imageUrl
        pictureItemModule.localUrl = localUrl
        pictureItemModule.imageProcessingType = imageProcessingType
        pictureItemModule.imageProcessingValue = json2Array(imageProcessingValue, Int::class.java) as ArrayList<Int>
        pictureItemModule.materialId = materialId
        pictureItemModule.materialType = materialType
        pictureItemModule.isNinePatch = isNinePatch
        pictureItemModule.isDefaultImage = isDefaultImage
        pictureItemModule.ninePatchLocalUrl = ninePatchLocalUrl
        pictureItemModule.ninePatchUrl = ninePatchUrl
        return pictureItemModule
    }

    private fun toFlowItemModule(): FlowItemModule {
        val flowItemModule = FlowItemModule()
        flowItemModule.prefix = prefix
        flowItemModule.suffix = suffix
        flowItemModule.startNumber = startNumber
        flowItemModule.incrementValue = incrementValue
        flowItemModule.fixValue = fixValue
        flowItemModule.fixLength = fixLength
        flowItemModule.textAlignHorizonral = textAlignHorizonral
        flowItemModule.textAlignVertical = textAlignVertical
        flowItemModule.lineMode = lineMode
        flowItemModule.value = value
        flowItemModule.wordSpacing = wordSpacing
        flowItemModule.letterSpacing = letterSpacing
        flowItemModule.lineSpacing = lineSpacing
        flowItemModule.fontCode = fontCode
        flowItemModule.fontFamily = fontFamily
        flowItemModule.fontStyle = json2Array(fontStyle, String::class.java) as ArrayList<String>
        flowItemModule.typesettingMode = typesettingMode
        flowItemModule.fontSize = fontSize
        flowItemModule.lineBreakMode = lineBreakMode
        flowItemModule.typesettingMode = typesettingMode
        flowItemModule.typesettingParam = typesettingParam
        flowItemModule.boxStyle = boxStyle
        flowItemModule.textStyle = json2Array(textStyle, String::class.java) as ArrayList<String>
        return flowItemModule
    }

    private fun toTimeItemModule(): TimeItemModule{
        val timeItemModule = TimeItemModule()
        timeItemModule.dateFormat = dateFormat
        timeItemModule.timeFormat = timeFormat
        timeItemModule.dateIsRefresh = dateIsRefresh
        timeItemModule.textAlignHorizonral = textAlignHorizonral
        timeItemModule.textAlignVertical = textAlignVertical
        timeItemModule.lineMode = lineMode
        timeItemModule.value = value
        timeItemModule.wordSpacing = wordSpacing
        timeItemModule.letterSpacing = letterSpacing
        timeItemModule.lineSpacing = lineSpacing
        timeItemModule.fontCode = fontCode
        timeItemModule.fontFamily = fontFamily
        timeItemModule.fontStyle = json2Array(fontStyle, String::class.java) as ArrayList<String>
        timeItemModule.typesettingMode = typesettingMode
        timeItemModule.fontSize = fontSize
        timeItemModule.lineBreakMode = lineBreakMode
        timeItemModule.typesettingMode = typesettingMode
        timeItemModule.typesettingParam = typesettingParam
        timeItemModule.boxStyle = boxStyle
        timeItemModule.timeOffset= timeOffset
        timeItemModule.associated=associated
        timeItemModule.associateId=associateId
        timeItemModule.contentTitle=contentTitle
        timeItemModule.validityPeriod=validityPeriod
        timeItemModule.validityPeriodNew=validityPeriodNew
        timeItemModule.validityPeriodUnit=validityPeriodUnit
        timeItemModule.time = time
        timeItemModule.timeUnit=timeUnit
        timeItemModule.textStyle = json2Array(textStyle, String::class.java) as ArrayList<String>
        return timeItemModule
    }

    private fun toRectItemModule(): RectItemModule{
        val rectItemModule = RectItemModule()
        rectItemModule.lineWidth = lineWidth
        rectItemModule.lineType = lineType
        rectItemModule.graphType = graphType
        rectItemModule.cornerRadius = cornerRadius
        rectItemModule.dashwidth = json2Array(dashwidth, Float::class.java) as ArrayList<Float>
        return rectItemModule
    }

    private fun toLineItemModule(): LineItemModule{
        val lineItemModule = LineItemModule()
        lineItemModule.lineType = lineType
        lineItemModule.lineWidth = lineWidth
        lineItemModule.dashwidth = json2Array(dashwidth, Float::class.java) as ArrayList<Float>
        return lineItemModule
    }
}
