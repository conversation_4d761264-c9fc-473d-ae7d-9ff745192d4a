import 'dart:math' as math;

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_bo.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';
import 'package:niimbot_flutter_canvas/src/provider/elements_data_changed_notifier.dart';
import 'package:niimbot_flutter_canvas/src/utils/canvas_helper.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/utils/track_utils.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/canvas_slider_theme.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/common_line_switch.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/common_num_change_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/custom_Item_selector_pop_menu.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_size_config.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_size_slider_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/floatbar/floating_bar_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/group_button/group_button.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:popover/popover.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'items_selector_popmenu_widget.dart';
import 'color_invert_selector_widget.dart';

class TextAttrPanelWidget extends StatefulWidget {
  List<CanvasElement> canvasElements;
  TextElement firstTextElement;
  bool isTableCellText;
  VoidCallback? refresh;
  ValueChanged? tableCellTextColorChanged;

  TextAttrPanelWidget({
    super.key,
    required this.canvasElements,
    this.isTableCellText = false,
    this.refresh,
    this.tableCellTextColorChanged,
  }) : firstTextElement = canvasElements.first.data as TextElement;

  @override
  State<TextAttrPanelWidget> createState() => _TextAttrPanelWidgetState();
}

final Logger _logger = Logger("_TextAttrPanelWidgetState", on: kDebugMode);

class _TextAttrPanelWidgetState extends State<TextAttrPanelWidget> {
  var canvasData;
  var dataPathInnerApp;
  List<TextModeSettingItem> _modeSettingItems = [
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal.svg',
        trackName: '横排文字',
        textMode: TextMode.Horizontal,
        fromVertical: false),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_90.svg',
        trackName: '横排文字90度',
        textMode: TextMode.Horizontal_90,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_vertical.svg',
        trackName: '竖排文字',
        textMode: TextMode.Vertical,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_arc.svg',
        trackName: '环形文字',
        textMode: TextMode.Arc,
        fromVertical: false),
  ];
  List<TextModeSettingItem> _modeSettingItemsCn = [
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_cn.svg',
        trackName: '横排文字',
        textMode: TextMode.Horizontal,
        fromVertical: false),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_90_cn.svg',
        trackName: '横排文字90度',
        textMode: TextMode.Horizontal_90,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_vertical_cn.svg',
        trackName: '竖排文字',
        textMode: TextMode.Vertical,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_arc_cn.svg',
        trackName: '环形文字',
        textMode: TextMode.Arc,
        fromVertical: false),
  ];

  List<TextModeSettingItem> _modeSettingItemsKo = [
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_ko.svg',
        trackName: '横排文字',
        textMode: TextMode.Horizontal,
        fromVertical: false),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_90_ko.svg',
        trackName: '横排文字90度',
        textMode: TextMode.Horizontal_90,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_vertical_ko.svg',
        trackName: '竖排文字',
        textMode: TextMode.Vertical,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_arc_ko.svg',
        trackName: '环形文字',
        textMode: TextMode.Arc,
        fromVertical: false),
  ];

  List<TextModeSettingItem> _modeSettingItemsJa = [
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_ja.svg',
        trackName: '横排文字',
        textMode: TextMode.Horizontal,
        fromVertical: false),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_horizontal_90_ja.svg',
        trackName: '横排文字90度',
        textMode: TextMode.Horizontal_90,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_vertical_ja.svg',
        trackName: '竖排文字',
        textMode: TextMode.Vertical,
        fromVertical: true),
    TextModeSettingItem(
        icon: 'assets/element/attribute/text_mode_arc_ja.svg',
        trackName: '环形文字',
        textMode: TextMode.Arc,
        fromVertical: false),
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsetsDirectional.fromSTEB(16, 9, 16, 16),
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextAttrPanel(),
        ],
      ),
    );
  }

  List<String> alignmentIcons = [
    'assets/element/attribute/text_align_left.svg',
    'assets/element/attribute/text_align_center.svg',
    'assets/element/attribute/text_align_right.svg',
    'assets/element/attribute/text_align_h_disttibuted.svg'
  ];

  List<String> alignmentVerticalIcons = [
    'assets/element/attribute/text_align_top.svg',
    'assets/element/attribute/text_align_middle.svg',
    'assets/element/attribute/text_align_bottom.svg',
    'assets/element/attribute/text_align_v_disttibuted.svg'
  ];

  List<CustomPopUpModelProtocol> alignmentHSelectors = [
    DefaultPopUpModel(intlanguage('app100001291', '顶部对齐'), 'assets/element/attribute/text_align_h_top.svg'),
    DefaultPopUpModel(intlanguage('app100001292', '中间对齐'), 'assets/element/attribute/text_align_h_middle.svg'),
    DefaultPopUpModel(intlanguage('app100001293', '底部对齐'), 'assets/element/attribute/text_align_h_bottom.svg'),
  ];

  List<CustomPopUpModelProtocol> alignmentVSelectors = [
    DefaultPopUpModel(intlanguage('app100001294', '左侧对齐'), 'assets/element/attribute/text_align_v_left.svg'),
    DefaultPopUpModel(intlanguage('app100001292', '中间对齐'), 'assets/element/attribute/text_align_v_center.svg'),
    DefaultPopUpModel(intlanguage('app100001295', '右侧对齐'), 'assets/element/attribute/text_align_v_right.svg'),
  ];

  Widget _buildTextAttrPanel() {
    List<String> styleIcons = [
      'assets/element/attribute/text_style_bold.svg',
      'assets/element/attribute/text_style_underline.svg',
      'assets/element/attribute/text_style_italic.svg',
    ];
    widget.firstTextElement = widget.canvasElements.first.data as TextElement;

    /// 弧形文字度数
    if ((widget.firstTextElement.typesettingParam ?? []).length < 2) {
      widget.firstTextElement.typesettingParam = [0, 180];
    }
    _logger.log('textAlign_${widget.firstTextElement.textAlignHorizontal}');
    TextElement defaultElement = getDefaultTextElement();
    return Column(
      children: [
        /// 字体样式和字号 - 重新布局现有组件
        Container(
          decoration: BoxDecoration(
            color: CanvasTheme.of(context).attributeGroupBackgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Column(
            children: [
              /// 第一行：粗体、斜体、下划线、字号选择器、字号调整 - 基于Figma node-2783-27532
              Container(
                height: 44,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: Row(
                  children: [
                    /// 复用现有的GroupButton组件 - 粗体、斜体、下划线
                    widget.isTableCellText
                        ? Expanded(
                            child: _buildFontStyleButtons(styleIcons, defaultElement, WrapAlignment.spaceBetween))
                        : _buildFontStyleButtons(styleIcons, defaultElement, WrapAlignment.start),

                    if (!widget.isTableCellText) ...[
                      const SizedBox(width: 20),

                      /// 第一个垂直分隔线
                      Container(
                        width: 0.5,
                        height: 24,
                        color: CanvasTheme.of(context).attributeDividerColor,
                      ),

                      /// 文本方向选择器
                      StatefulBuilder(builder: (context, setState) {
                        return Expanded(
                          child: Center(
                            child: GestureDetector(
                              onTap:  defaultElement.isLock == 1
                                  ? null
                                  : (){
                                // 文本方向选择器点击逻辑
                                showPopover(
                                    context: context,
                                    direction: PopoverDirection.top,
                                    backgroundColor: Colors.white,
                                    barrierColor: Colors.transparent,
                                    transitionDuration: const Duration(milliseconds: 100),
                                    radius: 16,
                                    shadow: [
                                      BoxShadow(
                                          color: Color(0x666666).withOpacity(0.17),
                                          blurRadius: 30,
                                          offset: Offset(0, 10),
                                          spreadRadius: -3),
                                    ],
                                    bodyBuilder: (context) {
                                      return textDirectionWidget(defaultElement);
                                    });
                              },
                              child: Container(
                                height: 28,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    textDirectionSelectorWidget(defaultElement),
                                    SizedBox(width: 2),
                                    SvgIcon(
                                      'assets/element/attribute/unfold.svg',
                                      useDefaultColor: false,
                                      color: Color(0xFF3C3C43).withOpacity(0.6),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        );
                      }),

                      /// 第二个垂直分隔线
                      Container(
                        width: 0.5,
                        height: 24,
                        color: CanvasTheme.of(context).attributeDividerColor,
                      ),

                      /// 颜色与反白选择器 - 仿照文本方向选择器样式
                      Expanded(
                        child: Center(
                          child: StatefulBuilder(builder: (context, setState) {
                            return Builder(
                              builder: (buttonContext) => GestureDetector(
                                behavior: HitTestBehavior.translucent,
                                onTap: () {
                                  showPopover(
                                      context: buttonContext,
                                      direction: PopoverDirection.top,
                                      backgroundColor: Colors.white,
                                      barrierColor: Colors.transparent,
                                      transitionDuration: const Duration(milliseconds: 100),
                                      radius: 16,
                                      shadow: [
                                        BoxShadow(
                                            color: Color(0x666666).withOpacity(0.17),
                                            blurRadius: 30,
                                            offset: Offset(0, 10),
                                            spreadRadius: -3),
                                      ],
                                      bodyBuilder: (context) {
                                        return colorAndInvertWidget(defaultElement);
                                      });
                                },
                                child: Container(
                                  margin: EdgeInsetsDirectional.symmetric(horizontal: 20, vertical: 5),
                                  height: 28,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      colorAndInvertSelectorWidget(defaultElement),
                                      SizedBox(width: 4),
                                      SvgIcon(
                                        'assets/element/attribute/unfold.svg',
                                        useDefaultColor: false,
                                        color: Color(0xFF3C3C43).withOpacity(0.6),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }),
                        ),
                      ),
                    ]
                  ],
                ),
              ),

              /// 分隔线
              Container(
                width: double.infinity,
                height: 0.5,
                color: CanvasTheme.of(context).attributeDividerColor,
              ),

              /// 复用现有的字号滑块
              FontSizeSliderWidget(
                fontSizeConfigList: calcMaxFontSize(CanvasObjectSharedWidget.canvasDataOf(context)?.width?.toDouble(),
                    CanvasObjectSharedWidget.canvasDataOf(context)?.height?.toDouble()),
                mm: defaultElement.fontSize.toDouble(),
                canvasElements: widget.canvasElements,
                showMaxConstraint: _shouldShowTextSizeWarning(defaultElement),
                valueChanged: (FontSizeConfig config) {
                  var fontSize = config.mm;
                  setState(() {
                    widget.canvasElements.forEach((element) {
                      TextElement textElement = element.data as TextElement;
                      textElement.fontSize = fontSize;
                      textElement.shouldShowSizeWarning = false; // 用户手动调整字号时清除警告
                    });
                  });
                  _notifyCanvasElementsDataChanged();
                },
              ),

              /// 文本过长提示组件
              if (_shouldShowTextSizeWarning(defaultElement)) _buildTextSizeWarningWidget(),
            ],
          ),
        ),
        SizedBox(height: 10),

        /// 对齐方式 - 复用现有的GroupButton组件
        Container(
          height: 44,
          decoration: BoxDecoration(
            color: CanvasTheme.of(context).attributeGroupBackgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              children: [
                /// 复用现有的文本对齐GroupButton
                GroupButton(
                  key: UniqueKey(),
                  isRadio: true,
                  spacing: 6,
                  onSelected: (index, isSelected) {
                    widget.canvasElements.forEach((canvasElement) {
                      TextElement element = canvasElement.data as TextElement;
                      if (element.typesettingMode == TypeSettingMode.TEXT_VERTICAL) {
                        element.textAlignVertical = index;
                      } else {
                        element.textAlignHorizontal = index;
                      }
                    });
                    _notifyCanvasElementsDataChanged();
                  },
                  isIconButtons: true,
                  buttonWidth: (MediaQuery.sizeOf(context).width - 16 * 2 - 8 * 2 - 6 * 7 - 0.5) / 7,
                  buttonHeight: 34,
                  unselectedColor: Colors.transparent,
                  selectedColor: CanvasTheme.of(context).backgroundLightColor!,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  selectedButtons: getSelectedButtons(defaultElement),
                  buttons:
                      defaultElement.typesettingMode == TypeSettingMode.TEXT_VERTICAL && defaultElement.rotate != 270
                          ? alignmentVerticalIcons
                          : alignmentIcons,
                  disableButtons: _getDisabledAlignmentButtons(defaultElement),
                  unselectedShadow: [],
                ),
                SizedBox(width: 6),

                /// 分隔线
                Container(
                  width: 0.5,
                  height: 24,
                  color: CanvasTheme.of(context).attributeDividerColor,
                ),
                SizedBox(width: 6),

                /// 垂直对齐按钮组 - 仿照左边的GroupButton样式平铺显示
                GroupButton(
                  key: UniqueKey(),
                  isRadio: true,
                  spacing: 6,
                  onSelected: (index, isSelected) {
                    // 保持原有的itemsSelectedChanged逻辑
                    List<CustomPopUpModelProtocol> currentItems =
                        defaultElement.typesettingMode == TypeSettingMode.TEXT_VERTICAL && defaultElement.rotate != 270
                            ? alignmentVSelectors
                            : alignmentHSelectors;

                    if (index < currentItems.length) {
                      CustomPopUpModelProtocol selectedModel = currentItems[index];

                      setState(() {
                        widget.canvasElements.forEach((canvasElement) {
                          TextElement element = canvasElement.data as TextElement;
                          if (element.typesettingMode == TypeSettingMode.TEXT_VERTICAL) {
                            defaultElement.textAlignHorizontal = alignmentVSelectors.indexOf(selectedModel);
                            element.textAlignHorizontal = alignmentVSelectors.indexOf(selectedModel);
                          } else if (element.boxStyle == TextElementBoxStyle.fixedWidthHeight) {
                            element.textAlignVertical = alignmentHSelectors.indexOf(selectedModel) + 1;
                          } else {
                            element.textAlignVertical = alignmentHSelectors.indexOf(selectedModel);
                          }
                          if (defaultElement.typesettingMode == TypeSettingMode.TEXT_HORIZONTAL ||
                              defaultElement.typesettingMode == -1) {
                            defaultElement.textAlignVertical =
                                defaultElement.boxStyle == TextElementBoxStyle.fixedWidthHeight
                                    ? alignmentHSelectors.indexOf(selectedModel) + 1
                                    : alignmentHSelectors.indexOf(selectedModel);
                          }
                        });
                        _notifyCanvasElementsDataChanged();
                      });

                      // 保持原有的追踪逻辑
                      TrackUtils.sendTrackingWrapTemplateId({
                        "track": "click",
                        "posCode": "108_072_258",
                        "ext": {
                          "module_name":
                              widget.isTableCellText ? "表格" : TrackUtils.getElementsTypeStr(widget.canvasElements)
                        }
                      });
                    }
                  },
                  isIconButtons: true,
                  buttonWidth: (MediaQuery.sizeOf(context).width - 16 * 2 - 8 * 2 - 6 * 7 - 0.5) / 7,
                  buttonHeight: 34,
                  unselectedColor: Colors.transparent,
                  selectedColor: CanvasTheme.of(context).backgroundLightColor!,
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  selectedButtons: _getSelectedVerticalAlignButtons(defaultElement),
                  buttons: _getVerticalAlignIcons(defaultElement),
                  disableButtons: defaultElement.boxStyle != TextElementBoxStyle.fixedWidthHeight
                      ? _getVerticalAlignIcons(defaultElement)
                      : [],
                  unselectedShadow: [],
                ),
              ],
            ),
          ),
        ),
        SizedBox(height: 10),

        /// 字距行距 - 等分布局
        Row(
          children: [
            /// 字距
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsetsDirectional.only(start: 11),
                    child: Text(
                      intlanguage('app01011', '字距'),
                      style: TextStyle(fontSize: 10, color: Color(0xFF3C3C43).withOpacity(0.6)),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    height: 44,
                    child: CommonNumChangeWidget<double>(
                      isShowTitle: false,
                      value: defaultElement.letterSpacing.toDouble(),
                      max: 5.0,
                      min: -5.0,
                      stepValue: 0.1,
                      valueChanged: (v) {
                        setState(() {
                          widget.canvasElements.forEach((canvasElement) {
                            TextElement element = canvasElement.data as TextElement;
                            element.letterSpacing = v;
                            element.wordSpacing = v;
                          });
                        });
                        _notifyCanvasElementsDataChanged();
                      },
                      throttleSubtractCallback: () {},
                      throttleAddCallback: () {},
                    ),
                  ),
                  SizedBox(height: 8),
                ],
              ),
            ),
            SizedBox(width: 10),

            /// 行距
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsetsDirectional.only(start: 11),
                    child: Text(
                      intlanguage('app01010', '行距'),
                      style: TextStyle(fontSize: 10, color: Color(0xFF3C3C43).withOpacity(0.6)),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    decoration: BoxDecoration(
                      color: CanvasTheme.of(context).attributeGroupBackgroundColor,
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                    ),
                    height: 44,
                    child: CommonNumChangeWidget<double>(
                      isShowTitle: false,
                      value: defaultElement.lineSpacing.toDouble(),
                      max: 5.0,
                      min: -5.0,
                      stepValue: 0.1,
                      valueChanged: (v) {
                        setState(() {
                          widget.canvasElements.forEach((canvasElement) {
                            TextElement element = canvasElement.data as TextElement;
                            element.lineSpacing = v;
                          });
                        });
                        _notifyCanvasElementsDataChanged();
                      },
                      throttleSubtractCallback: () {},
                      throttleAddCallback: () {},
                    ),
                  ),
                  SizedBox(height: 8),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 10),

        /// 复用现有的CommonLineSwitch组件
        CommonLineSwitch(
          title: intlanguage('app01591', "按单词换行"),
          value: defaultElement.lineBreakMode == 1,
          enabled: defaultElement.typesettingMode == 0 || defaultElement.typesettingMode == 1 || widget.isTableCellText,
          margin: EdgeInsets.zero,
          valueChanged: (value) {
            widget.canvasElements.forEach((canvasElement) {
              TextElement element = canvasElement.data as TextElement;
              element.lineBreakMode = value ? 1 : 0;
            });
            _notifyCanvasElementsDataChanged();
          },
        ),
      ],
    );
  }

  Widget textDirectionSelectorWidget(TextElement defaultElement) {
    List<TextModeSettingItem> items = _getTextModeSettingItemsByLanguage();
    List<String> selectedItems = items
        .where(
            (element) => element.textMode == _getElementTextMode(defaultElement.typesettingMode, defaultElement.rotate))
        .map((e) => e.icon)
        .toList();
    // 如果selectedItems为空，则返回items的第一个
    if (selectedItems.isEmpty) {
      return SvgIcon(items.first.icon,
          width: 28,
          height: 28,
          color: defaultElement.isLock == 0
              ? CanvasTheme.of(context).iconColor
              : CanvasTheme.of(context).iconDisableColor);
    }
    return SvgIcon(selectedItems.first,
        width: 28,
        height: 28,
        color:
            defaultElement.isLock == 0 ? CanvasTheme.of(context).iconColor : CanvasTheme.of(context).iconDisableColor);
  }

  Widget colorAndInvertSelectorWidget(TextElement defaultElement) {
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    List<String>? paperColors = CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
    String? ribbonColors = CanvasPluginManager().canvasConfigImpl?.getRfidColor();

    // 动态构建当前选择状态 - 每次都重新计算
    ColorInvertModel currentSelection = _buildCurrentColorInvertSelection();

    // 检测是否为多色碳带
    bool isMultiColorRibbon = printColor.isEmpty && _isMultiColorRibbonFromData(ribbonColors);

    if (isMultiColorRibbon && ribbonColors != null) {
      // 多色碳带：使用半圆切分渲染
      List<String> colors = ribbonColors.split(',').map((c) => c.trim()).toList();
      List<Color> colorObjects = colors.map((c) => _parseColorFromStringLocal(c)).toList();

      // 确定是否显示星星
      bool showStar = currentSelection.colorReverse > 0;

      return Stack(
        children: [
          // 多色背景
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Color(0x3C3C43).withOpacity(0.15),
                width: 0.5,
              ),
            ),
            child: ClipOval(
              child: CustomPaint(
                size: Size(20, 20),
                painter: MultiColorCirclePainter(colors: colorObjects),
              ),
            ),
          ),
          // 星星图标
          if (showStar)
            Container(
              width: 20,
              height: 20,
              child: Center(
                child: CustomPaint(
                  size: Size(15, 13),
                  painter: StarIconPainter(color: Colors.white),
                ),
              ),
            ),
        ],
      );
    } else {
      // 单色或多色标签纸：使用原有逻辑
      Color backgroundColor = Colors.black;
      List<String> colorList;
      if (printColor.isNotEmpty) {
        colorList = [printColor];
      } else {
        colorList = paperColors ?? ["0.0.0"];
      }

      if (colorList.length > currentSelection.paperColorIndex) {
        backgroundColor = _parseColorFromStringLocal(colorList[currentSelection.paperColorIndex]);
      }

      // 确定是否显示星星以及星星颜色
      bool showStar = currentSelection.colorReverse > 0;
      Color? starColor;

      if (showStar) {
        if (currentSelection.colorReverse == 1) {
          // 普通反白，根据背景颜色确定星星颜色
          starColor = _getStarColorForBackgroundLocal(backgroundColor);
        } else if (currentSelection.colorReverse >= 2 && colorList.length > 1) {
          // 交叉反白，使用其他颜色作为星星颜色
          int crossColorIndex = currentSelection.colorReverse - 2;
          if (crossColorIndex < colorList.length) {
            starColor = _parseColorFromStringLocal(colorList[crossColorIndex]);
          }
        }
      }

      return Stack(
        children: [
          // 主颜色圆形
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: backgroundColor == Color(0xff9fa0a0) ? null : backgroundColor,
              shape: BoxShape.circle,
              border: Border.all(
                color: Color(0x3C3C43).withOpacity(0.15),
                width: 0.5,
              ),
            ),
            child: Stack(
              children: [
                // 单色特殊处理：Color(0xff9fa0a0)显示渐变
                if (backgroundColor == Color(0xff9fa0a0))
                  ClipOval(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CustomPaint(
                        painter: GradientCirclePainter(
                          startColor: Color(0xff9fa0a0),
                          endColor: Colors.black,
                        ),
                      ),
                    ),
                  ),
                // 星星图标
                if (showStar && starColor != null)
                  Center(
                    child: CustomPaint(
                      size: Size(15, 13),
                      painter: StarIconPainter(color: starColor),
                    ),
                  ),
              ],
            ),
          ),
        ],
      );
    }
  }

  /// 根据背景颜色确定星星颜色，确保可见性
  Color _getStarColorForBackgroundLocal(Color backgroundColor) {
    // 判断是否为白色或接近白色
    if (backgroundColor.red > 240 && backgroundColor.green > 240 && backgroundColor.blue > 240) {
      return Color(0xFFE5E5EA); // 使用灰色确保在白色背景上可见
    }
    return Colors.white; // 其他颜色背景使用白色星星
  }

  /// 解析颜色字符串为Color对象
  Color _parseColorFromStringLocal(String colorString) {
    try {
      List<String> components = colorString.split('.');
      if (components.length >= 3) {
        return Color.fromRGBO(
          int.parse(components[0]),
          int.parse(components[1]),
          int.parse(components[2]),
          1.0,
        );
      }
    } catch (e) {
      // 解析失败时返回默认颜色
    }
    return Colors.black;
  }

  /// 动态构建当前颜色反白选择状态 - 每次调用都重新计算
  ColorInvertModel _buildCurrentColorInvertSelection() {
    // 检查 canvasElements 是否有效
    if (widget.canvasElements.isEmpty) {
      print('TextAttrPanelWidget: _buildCurrentColorInvertSelection called with empty canvasElements');
      return ColorInvertModel(
        paperColorIndex: 0,
        colorReverse: 0,
      );
    }

    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    String? ribbonColors = CanvasPluginManager().canvasConfigImpl?.getRfidColor();

    // 多色碳带：使用元素的 paperColorIndex 和 colorReverse
    if (printColor.isEmpty && _isMultiColorRibbonFromData(ribbonColors)) {
      return ColorInvertModel(
        paperColorIndex: paperColorIndexValue(),
        colorReverse: _getColorReverseValue(),
      );
    }

    // 如果paperColor只有1个且colorReverse不为0，则colorReverse为1
    if (printColor.isNotEmpty && _getColorReverseValue() > 1) {
      return ColorInvertModel(
        paperColorIndex: 0,
        colorReverse: 1,
      );
    }

    if (printColor.isNotEmpty && paperColorIndexValue() != 0) {
      return ColorInvertModel(
        paperColorIndex: 0,
        colorReverse: _getColorReverseValue(),
      );
    }

    return ColorInvertModel(
      paperColorIndex: paperColorIndexValue(),
      colorReverse: _getColorReverseValue(),
    );
  }

  /// 检测多色碳带（从数据源）
  bool _isMultiColorRibbonFromData(String? ribbonColors) {
    if (ribbonColors?.isNotEmpty != true) return false;

    if (ribbonColors!.contains(',')) {
      List<String> colors = ribbonColors.split(',').map((c) => c.trim()).toList();
      return colors.length > 1 && colors.every((c) => c.isNotEmpty);
    }

    return false;
  }

  /// 获取当前元素的 colorReverse 值
  int _getColorReverseValue() {
    if (widget.canvasElements.isEmpty) {
      return 0;
    }

    if (widget.canvasElements.length == 1) {
      return widget.firstTextElement.colorReverse;
    } else {
      // 多选时，如果所有元素的 colorReverse 值相同，返回该值；否则返回0
      int firstValue = widget.firstTextElement.colorReverse;
      for (var element in widget.canvasElements) {
        if (firstValue != element.data.colorReverse) {
          return 0; // 不一致时默认为0（非反白）
        }
      }
      return firstValue;
    }
  }

  Widget fontAdjustSwitchWidget(TextElement defaultElement) {
    /// 文本 && 时间元素特性
    bool ifShowTextStyle = showTextStyle(defaultElement);

    return ifShowTextStyle
        ? /*Container(
            margin: EdgeInsets.only(top: 10, bottom: 0),
            decoration: BoxDecoration(
            color: CanvasTheme.of(context).attributeGroupBackgroundColor,
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
          child: Flex(
            direction: Axis.vertical,
            children: [
              textStyleChooseWidget(widget.firstTextElement)
            ],
          ),
        )*/
        Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _textStyleChooseWidgetNew(widget.firstTextElement),
              Container(
                width: double.infinity,
                height: 0.5,
                color: CanvasTheme.of(context).attributeDividerColor,
              ),
            ],
          )
        : Container();
  }

  bool showTextStyle(TextElement defaultElement) {
    /// 文本 && 时间元素特性
    var showTextStyle = false;
    if (widget.canvasElements.length == 1) {
      /// 单选
      showTextStyle =
          defaultElement.isSupportBoxStyle() && defaultElement.boxStyle == TextElementBoxStyle.fixedWidthHeight;
    } else if (widget.canvasElements.length > 1) {
      showTextStyle = true;

      /// 多选
      for (var i = 0; i < widget.canvasElements.length; i++) {
        TextElement element = widget.canvasElements[i].data as TextElement;
        if (!element.isSupportBoxStyle() || element.boxStyle != TextElementBoxStyle.fixedWidthHeight) {
          showTextStyle = false;
          break;
        }
      }
    }
    return showTextStyle;
  }

  Widget textStyleChooseWidget(TextElement defaultElement) {
    final availableTextStyles = TextElementBO.availableTextStyles();
    var initializeIndex = 0;
    if (defaultElement.textStyle.length > 0) {
      initializeIndex = availableTextStyles.keys
          .toList()
          .indexWhere((element) => element.value == defaultElement.textStyle.first.value);
      initializeIndex = math.max(0, initializeIndex);
    }
    return Container(
      // height: 44,
      decoration: BoxDecoration(
        color: CanvasTheme.of(context).attributeGroupBackgroundColor,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      child: Padding(
        padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
        child: Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Flexible(
                    child: Text(
                      intlanguage('app100001173', "字号适应文本框"),
                      style: CanvasTheme.of(context).attributeTitleTextStyle,
                    ),
                  ),
                ],
              ),
            ),
            ItemsSelectorPopUpWidget(
              popHeight: 100,
              items: availableTextStyles.keys.map((e) => availableTextStyles[e]!).toList(),
              initializeIndex: initializeIndex,
              itemsSelectedChanged: (selectedIndex) {
                if (selectedIndex < availableTextStyles.keys.length) {
                  final textStyle = availableTextStyles.keys.toList()[selectedIndex];
                  setState(() {
                    widget.canvasElements.forEach((canvasElement) {
                      TextElement element = canvasElement.data as TextElement;
                      element.updateTextStyle(textStyle);
                    });
                  });
                  _notifyCanvasElementsDataChanged();
                  TrackUtils.sendTrackingWrapTemplateId({
                    "track": "click",
                    "posCode": "108_072_221",
                    "ext": {
                      "b_name": textStyle.trackName,
                      "module_name":
                          widget.isTableCellText ? "表格" : TrackUtils.getElementsTypeStr(widget.canvasElements)
                    }
                  });
                }
              },
            )
          ],
        ),
      ),
    );
  }

  Widget _textStyleChooseWidgetNew(TextElement defaultElement) {
    final availableTextStyles = TextElementBO.availableTextStyles();
    var initializeIndex = 0;
    if (defaultElement.textStyle.length > 0) {
      initializeIndex = availableTextStyles.keys
          .toList()
          .indexWhere((element) => element.value == defaultElement.textStyle.first.value);
      initializeIndex = math.max(0, initializeIndex);
    }
    return CommonLineSwitch(
      title: intlanguage('app100001273', "自动字号"),
      value: initializeIndex == 0,
      margin: EdgeInsets.symmetric(vertical: 4.0),
      valueChanged: (value) {
        int selectedIndex = value ? 0 : 1;
        if (selectedIndex < availableTextStyles.keys.length) {
          final textStyle = availableTextStyles.keys.toList()[selectedIndex];
          setState(() {
            widget.canvasElements.forEach((canvasElement) {
              TextElement element = canvasElement.data as TextElement;
              element.updateTextStyle(textStyle);
            });
          });
          _notifyCanvasElementsDataChanged();
          TrackUtils.sendTrackingWrapTemplateId({
            "track": "click",
            "posCode": "108_072_221",
            "ext": {
              "b_name": textStyle.trackName,
              "module_name": widget.isTableCellText ? "表格" : TrackUtils.getElementsTypeStr(widget.canvasElements)
            }
          });
        }
      },
    );
  }

  Widget colorAndInvertWidget(TextElement defaultElement) {
    String printColor = CanvasObjectSharedWidget.printColorOf(context);
    List<String>? paperColors = CanvasObjectSharedWidget.canvasDataOf(context)?.paperColor;
    String? ribbonColors = CanvasPluginManager().canvasConfigImpl?.getRfidColor();

    // 动态构建当前选择状态 - 每次都重新计算
    ColorInvertModel currentSelection = _buildCurrentColorInvertSelection();

    return ColorInvertSelectorWidget(
      printColor: printColor.isNotEmpty ? printColor : null,
      paperColors: paperColors,
      ribbonColors: ribbonColors,
      currentSelection: currentSelection,
      onSelectionChanged: (newSelection) {
        setState(() {
          widget.canvasElements.forEach((canvasElement) {
            TextElement element = canvasElement.data as TextElement;
            element.colorChannel = newSelection.paperColorIndex;
            element.paperColorIndex = newSelection.paperColorIndex;
            element.colorReverse = newSelection.colorReverse;
            element.elementColor = newSelection.elementColor;
          });
        });
        _notifyCanvasElementsDataChanged();
      },
    );
  }

  Widget textDirectionWidget(TextElement defaultElement) {
    return Container(
      padding: const EdgeInsetsDirectional.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            intlanguage('app100000761', '文本方向'),
            style: TextStyle(fontSize: 11, color: Color(0xFF3C3C43).withOpacity(0.6)),
          ),
          const SizedBox(height: 8),
          StatefulBuilder(
            builder: (_, setTextDirectionState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [groupButtons(defaultElement, setTextDirectionState)],
                  ),
                  if (defaultElement.getTextMode() == TextMode.Arc) ...[
                    Container(
                      width: 255,
                      height: 40,
                      child: Stack(
                        children: [
                          SizedBox(
                            height: 30,
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8, right: 8, top: 3),
                              child: CanvasSliderTheme(
                                child: Slider(
                                  value: defaultElement.typesettingParam![1].toDouble(),
                                  onChanged: (v) {
                                    setState(() {
                                      int index = v.toInt();
                                      widget.canvasElements.forEach((element) {
                                        TextElement textElement = element.data as TextElement;
                                        textElement.typesettingParam![1] = index;
                                      });
                                    });
                                    setTextDirectionState(() {});
                                    _notifyCanvasElementsDataChanged();
                                  },

                                  /// 进度条分为多少段
                                  divisions: 720,
                                  max: 360,
                                  min: -360,
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            top: 28,
                            left: () {
                              // 容器宽度固定为255px
                              const double containerWidth = 255;
                              // 滑块可滑动区域宽度: 255 - 12*2(padding) - 18(thumb宽度) = 228
                              const double sliderTrackWidth = containerWidth - 12 * 2 - 18;
                              // 滑块值范围是-360到360，总共720
                              double sliderPosition =
                                  sliderTrackWidth / (720 - 1) * (defaultElement.typesettingParam![1] + 360);

                              if (Directionality.of(context) == TextDirection.rtl) {
                                return containerWidth - 30 - 12 - 9 - sliderPosition;
                              } else {
                                return -30 + 12 + 9 + sliderPosition;
                              }
                            }(),
                            child: Container(
                              width: 60,
                              child: Center(
                                child: Text(
                                  '${widget.firstTextElement.typesettingParam![1]}',
                                  style: TextStyle(color: Color(0xFF999999), fontSize: 11, fontWeight: FontWeight.w600),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    )
                  ]
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  SingleChildScrollView groupButtons(TextElement defaultElement, [StateSetter? setPopoverState]) {
    List<TextModeSettingItem> items = _getTextModeSettingItemsByLanguage();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: GroupButton(
        key: UniqueKey(),
        isRadio: true,
        spacing: 5,
        onSelected: (index, isSelected) {
          setState(() {
            TextMode textMode = items[index].textMode;
            widget.canvasElements.forEach((element) {
              Function refreshCallback = (CanvasElement refreshElement) {
                TextElement textElement = refreshElement.data as TextElement;
                if (textMode == TextMode.Horizontal) {
                  if (textElement.isTextVertical()) {
                    textElement.sizeSwapByTextDirectionChanged();
                    if (textElement.rotate == 270) {
                      textElement.resetRotate();
                    }
                    textElement.alignChangeFun(false);
                  }
                  /// 记录上次文本方向
                  textElement.lastTypeSettingMode = textElement.typesettingMode;
                  textElement.typesettingMode = TypeSettingMode.TEXT_HORIZONTAL;
                  textElement.lineMode = 2;
                  textElement.handleTextElementTypeSettingMode(textElement.typesettingMode);
                  textElement.relocationForTypeSettingChange();
                } else if (textMode == TextMode.Arc) {
                  if (textElement.isTextVertical()) {
                    textElement.sizeSwapByTextDirectionChanged();
                    if (textElement.rotate == 270) {
                      textElement.resetRotate();
                    }
                    textElement.alignChangeFun(false);
                  }
                  /// 记录上次文本方向
                  textElement.lastTypeSettingMode = textElement.typesettingMode;
                  textElement.typesettingMode = TypeSettingMode.TEXT_CURVE;
                  textElement.typesettingParam = [0, 180];
                  textElement.lineMode = 2;
                  textElement.handleTextElementTypeSettingMode(textElement.typesettingMode);
                  textElement.relocationForTypeSettingChange();
                  //弧形文字下划线不可用 todo
                } else if (textMode == TextMode.Horizontal_90) {
                  //横排文字90度--竖排文字
                  if (!textElement.isTextVertical()) {
                    textElement.sizeSwapByTextDirectionChanged();
                    textElement.alignChangeFun(true);
                  }
                  /// 记录上次文本方向
                  textElement.lastTypeSettingMode = textElement.typesettingMode;
                  textElement.change2Vertical270();
                  textElement.typesettingMode = TypeSettingMode.TEXT_VERTICAL;
                  textElement.handleTextElementTypeSettingMode(textElement.typesettingMode);
                  textElement.lineMode = 7;
                } else if (textMode == TextMode.Vertical) {
                  //竖排文字
                  if (!textElement.isTextVertical()) {
                    textElement.sizeSwapByTextDirectionChanged();
                    textElement.alignChangeFun(true);
                  } else {
                    textElement.resetRotate();
                  }

                  /// 记录上次文本方向
                  textElement.lastTypeSettingMode = textElement.typesettingMode;
                  textElement.typesettingMode = TypeSettingMode.TEXT_VERTICAL;
                  textElement.lineMode = 7;
                  textElement.handleTextElementTypeSettingMode(textElement.typesettingMode);
                  textElement.relocationForTypeSettingChange();
                }
              };
              var associateCanvasElement = canvasKey.currentState?.getAssociateElement(element);
              if (associateCanvasElement != null) {
                refreshCallback(associateCanvasElement);
              }
              refreshCallback(element);
            });

            // 更新 defaultElement 以保持 UI 状态一致
            // 多选模式下，保持默认元素的属性刷新，单选的时候使用的引用元素，所以不需要更新
            if (widget.canvasElements.length > 1) {
              if (textMode == TextMode.Horizontal) {
                defaultElement.typesettingMode = TypeSettingMode.TEXT_HORIZONTAL;
                defaultElement.lineMode = 2;
                if (defaultElement.rotate == 270) {
                  defaultElement.rotate = 0;
                }
              } else if (textMode == TextMode.Arc) {
                defaultElement.typesettingMode = TypeSettingMode.TEXT_CURVE;
                defaultElement.typesettingParam = [0, 180];
                defaultElement.lineMode = 2;
                if (defaultElement.rotate == 270) {
                  defaultElement.rotate = 0;
                }
              } else if (textMode == TextMode.Horizontal_90) {
                defaultElement.typesettingMode = TypeSettingMode.TEXT_VERTICAL;
                defaultElement.lineMode = 7;
                defaultElement.rotate = 270;
              } else if (textMode == TextMode.Vertical) {
                defaultElement.typesettingMode = TypeSettingMode.TEXT_VERTICAL;
                defaultElement.lineMode = 7;
                defaultElement.rotate = 0;
              }
            }
          });
          // 触发弹窗内容重绘
          if (setPopoverState != null) {
            setPopoverState(() {});
          }
          if (FloatingBarHelper().multiSelect == false) {
            FloatingBarHelper().dismissFloatingBar();
          }
          _notifyCanvasElementsDataChanged();
          CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
            "track": "click",
            "posCode": "108_072_110",
            "ext": {
              "b_name": items[index].trackName,
              "module_name": TrackUtils.getElementsTypeStr(widget.canvasElements)
            }
          });
        },
        isIconButtons: true,
        buttonWidth: 60,
        buttonHeight: 34,
        unselectedColor: Colors.transparent,
        selectedColor: Color(0x747480).withOpacity(0.08),
        borderRadius: BorderRadius.all(Radius.circular(8)),
        selectedBorderColor: Color(0x3C3C43).withOpacity(0.09),
        buttons: items.map((e) => e.icon).toList(),
        disableButtons: defaultElement.checkSupportVerticalText()
            ? []
            : items.where((element) => element.fromVertical).map((e) => e.icon).toList(),
        selectedButtons: items
            .where((element) =>
                element.textMode == _getElementTextMode(defaultElement.typesettingMode, defaultElement.rotate))
            .map((e) => e.icon)
            .toList(),
      ),
    );
  }

  TextMode _getElementTextMode(int typesettingMode, int rotate) {
    if (typesettingMode == 2) {
      if (rotate == 270) {
        return TextMode.Horizontal_90;
      } else {
        return TextMode.Vertical;
      }
    }
    if (typesettingMode == 3) {
      return TextMode.Arc;
    }
    return TextMode.Horizontal;
  }

  bool highLightSwitchValue() {
    bool value = false;
    if (widget.canvasElements.length == 1) {
      value = widget.firstTextElement.colorReverse == 1;
    } else {
      bool isSameValue = true;
      int firstValue = widget.firstTextElement.colorReverse;
      for (var element in widget.canvasElements) {
        if (firstValue != element.data.colorReverse) {
          isSameValue = false;
        }
      }
      if (!isSameValue) {
        value = false;
      } else {
        value = firstValue == 1;
      }
    }
    return value;
  }

  int paperColorIndexValue() {
    if (widget.canvasElements.isEmpty) {
      return 0;
    }

    int value = 0;
    if (widget.canvasElements.length == 1) {
      value = widget.firstTextElement.paperColorIndex;
    } else {
      bool isSameValue = true;
      int firstValue = widget.firstTextElement.paperColorIndex;
      for (var element in widget.canvasElements) {
        if (firstValue != element.data.paperColorIndex) {
          isSameValue = false;
        }
      }
      if (!isSameValue) {
        value = 0;
      } else {
        value = firstValue;
      }
    }
    return value;
  }

  /// 获取文本框中模式2、自适应模式最小字体
  double getDefaultFontSize() {
    double smallValue = widget.firstTextElement.fontSize.toDouble();
    for (var element in widget.canvasElements) {
      if (smallValue > (element.data as TextElement).fontSize.toDouble()) {
        smallValue = (element.data as TextElement).fontSize.toDouble();
      }
    }
    return smallValue;
    // /// 获取模式三文本
    // List<CanvasElement> fixedWidthHeightElements = widget.canvasElements.where((element) => (element.data as TextElement).boxStyle == TextElementBoxStyle.fixedWidthHeight).toList();
    // if (fixedWidthHeightElements != null && fixedWidthHeightElements.length > 0) {
    //   /// 获取模式三最大字号
    //   CanvasElement maxElement = fixedWidthHeightElements.reduce((value, element) {
    //     if ((value.data as TextElement).fontSize > (element.data as TextElement).fontSize) {
    //       return value;
    //     }
    //     return element;
    //   });
    //   return (maxElement.data as TextElement).fontSize;
    // }
    // return smallValue;
  }

  double getDefaultFixedWidthHeightMaxFontSizeFontSize() {
    double smallValue = widget.firstTextElement.fixedWidthHeightMaxFontSize.toDouble();

    /// 获取模式三文本
    List<CanvasElement> fixedWidthHeightElements = widget.canvasElements
        .where((element) => (element.data as TextElement).boxStyle == TextElementBoxStyle.fixedWidthHeight)
        .toList();
    if (fixedWidthHeightElements.length > 0) {
      /// 获取模式三最小阈值字号
      CanvasElement maxElement = fixedWidthHeightElements.reduce((value, element) {
        if ((value.data as TextElement).fixedWidthHeightMaxFontSize >
            (element.data as TextElement).fixedWidthHeightMaxFontSize) {
          return element;
        }
        return value;
      });
      return (maxElement.data as TextElement).fixedWidthHeightMaxFontSize;
    }
    return smallValue;
  }

  /// 获取默认文本库样式，只要存在模式三，标记为模式3
  TextElementBoxStyle getDefaultBoxStyle() {
    TextElementBoxStyle boxStyle = widget.firstTextElement.boxStyle;
    for (var element in widget.canvasElements) {
      if ((element.data as TextElement).boxStyle == TextElementBoxStyle.fixedWidthHeight) {
        boxStyle = TextElementBoxStyle.fixedWidthHeight;
      }
    }
    return boxStyle;
  }

  /// 获取默认文本样式，只要存在自适应模式，标记为自适应
  List<TextElementTextStyle> getDefaultTextStyles() {
    List<TextElementTextStyle> textStyles = widget.firstTextElement.textStyle;
    for (var element in widget.canvasElements) {
      if ((element.data as TextElement).textStyle.contains(TextElementTextStyle.adaptive)) {
        textStyles = [TextElementTextStyle.adaptive];
      }
    }
    return textStyles;
  }

  double getDefaultLetterSpacing() {
    double smallValue = widget.firstTextElement.letterSpacing.toDouble();
    for (var element in widget.canvasElements) {
      if (smallValue > (element.data as TextElement).letterSpacing.toDouble()) {
        smallValue = (element.data as TextElement).letterSpacing.toDouble();
      }
    }
    return smallValue;
  }

  double getDefaultLineSpacing() {
    double smallValue = widget.firstTextElement.lineSpacing.toDouble();
    for (var element in widget.canvasElements) {
      if (smallValue > (element.data as TextElement).lineSpacing.toDouble()) {
        smallValue = (element.data as TextElement).lineSpacing.toDouble();
      }
    }
    return smallValue;
  }

  int getDefaultLineBreakMode() {
    // value: widget.firstTextElement.lineBreakMode == 1,
    // enabled: widget.firstTextElement.typesettingMode == 1,
    int firstValue = widget.firstTextElement.lineBreakMode ?? 0;
    bool isSameValue = true;
    for (var element in widget.canvasElements) {
      if (firstValue != (element.data as TextElement).lineBreakMode) {
        isSameValue = false;
        break;
      }
    }
    if (isSameValue) {
      return firstValue;
    } else {
      return 0;
    }
  }

  /// 获取默认的水平对齐值，用于多选状态下的统一默认值
  int? getDefaultTextAlignHorizontal() {
    if (widget.canvasElements.isEmpty) return null;

    int? firstStandardizedValue = _getStandardizedAlignHorizontal(widget.firstTextElement);
    bool isSameValue = true;

    for (var element in widget.canvasElements) {
      TextElement textElement = element.data as TextElement;
      if (_getStandardizedAlignHorizontal(textElement) != firstStandardizedValue) {
        isSameValue = false;
        break;
      }
    }

    if (isSameValue) {
      return widget.firstTextElement.textAlignHorizontal;
    } else {
      return null; // 不一致时返回null，在UI中显示为未选中状态
    }
  }

  /// 获取默认的垂直对齐值，用于多选状态下的统一默认值
  int? getDefaultTextAlignVertical() {
    if (widget.canvasElements.isEmpty) return null;

    int? firstStandardizedValue = _getStandardizedAlignVertical(widget.firstTextElement);
    bool isSameValue = true;

    for (var element in widget.canvasElements) {
      TextElement textElement = element.data as TextElement;
      if (_getStandardizedAlignVertical(textElement) != firstStandardizedValue) {
        isSameValue = false;
        break;
      }
    }

    if (isSameValue && firstStandardizedValue != null) {
      // 如果所有元素标准化后的值相同，需要根据getDefaultBoxStyle()来决定返回值
      TextElementBoxStyle defaultBoxStyle = getDefaultBoxStyle();

      if (defaultBoxStyle == TextElementBoxStyle.fixedWidthHeight) {
        // 如果默认boxStyle是fixedWidthHeight，需要将标准化值+1转换回存储值
        return firstStandardizedValue + 1;
      } else {
        // 如果默认boxStyle不是fixedWidthHeight，直接返回标准化值
        return firstStandardizedValue;
      }
    } else {
      return null; // 不一致时返回null，在UI中显示为未选中状态
    }
  }

  /// 获取标准化的水平对齐值，用于多选状态下的对比
  int? _getStandardizedAlignHorizontal(TextElement element) {
    // textAlignHorizontal 在所有模式下都直接使用原始值
    return element.textAlignHorizontal;
  }

  /// 获取标准化的垂直对齐值，用于多选状态下的对比
  int? _getStandardizedAlignVertical(TextElement element) {
    if (element.textAlignVertical == null) return null;

    // 只有横排文字模式下的 fixedWidthHeight 需要特殊处理（-1转换）
    if (element.typesettingMode == TypeSettingMode.TEXT_HORIZONTAL &&
        element.boxStyle == TextElementBoxStyle.fixedWidthHeight) {
      return math.max(0, (element.textAlignVertical ?? 1) - 1);
    }

    // 其他所有模式直接使用原始值
    return element.textAlignVertical;
  }

  ///用于多选模式下附初值
  TextElement getDefaultTextElement() {
    TextElement? element;
    if (widget.canvasElements.length == 1) {
      element = widget.firstTextElement;
    } else {
      ///多选状态下，初始属性值统一放入新建的TextElment当中
      TextElement firstElement = widget.firstTextElement;
      element = TextElement(

          ///需要取最小值的
          fontSize: getDefaultFontSize(),
          letterSpacing: getDefaultLetterSpacing(),
          lineSpacing: getDefaultLineSpacing(),
          fontStyle: ['bold', 'underline', 'italic'],
          boxStyle: getDefaultBoxStyle(),
          textStyle: getDefaultTextStyles(),
          lineBreakMode: firstElement.lineBreakMode,
          typesettingMode: firstElement.typesettingMode,
          textAlignHorizontal: getDefaultTextAlignHorizontal(),
          textAlignVertical: getDefaultTextAlignVertical(),
          rotate: firstElement.rotate,
          typesettingParam: firstElement.typesettingParam,
          height: firstElement.height,
          id: JsonElement.generateId(),
          lineMode: firstElement.lineMode,
          value: firstElement.value,
          width: firstElement.width,
          wordSpacing: firstElement.wordSpacing,
          x: firstElement.x,
          y: firstElement.y,
          textDirection: CanvasHelper.currentTextDirection);
      // 设置shouldShowSizeWarning属性
      element.shouldShowSizeWarning = _getDefaultShouldShowSizeWarning();
      for (var canvasElement in widget.canvasElements) {
        TextElement temp = canvasElement.data as TextElement;
        if (temp.lineBreakMode != firstElement.lineBreakMode) {
          element.lineBreakMode = 0;
        }
        if (widget.isTableCellText == false && temp.typesettingMode != firstElement.typesettingMode) {
          element.typesettingMode = -1;
        }
        // 使用标准化的对齐值进行比较，确保相同UI表现的元素不会被误判为不一致
        if (_getStandardizedAlignHorizontal(temp) != _getStandardizedAlignHorizontal(firstElement)) {
          element.textAlignHorizontal = null;
        }
        if (_getStandardizedAlignVertical(temp) != _getStandardizedAlignVertical(firstElement)) {
          element.textAlignVertical = null;
        }
        if (temp.rotate != firstElement.rotate) {
          element.rotate = 0;
        }
        if (!firstElement.fontStyle.contains("bold") ||
            temp.fontStyle.contains("bold") != firstElement.fontStyle.contains("bold")) {
          if (element.fontStyle.contains("bold")) {
            element.fontStyle.remove("bold");
          }
        }
        if (!firstElement.fontStyle.contains("underline") ||
            temp.fontStyle.contains("underline") != firstElement.fontStyle.contains("underline")) {
          if (element.fontStyle.contains("underline")) {
            element.fontStyle.remove("underline");
          }
        }
        if (!firstElement.fontStyle.contains("italic") ||
            (temp.fontStyle.contains("italic") != firstElement.fontStyle.contains("italic"))) {
          if (element.fontStyle.contains("italic")) {
            element.fontStyle.remove("italic");
          }
        }
      }
    }
    return element;
  }

  void _notifyCanvasElementsDataChanged() {
    Provider.of<ElementsDataChangedNotifier>(context, listen: false).setDataChangedElements(widget.canvasElements);
  }

  /// 获取需要禁用的对齐按钮
  List<String> _getDisabledAlignmentButtons(TextElement element) {
    List<String> disabledButtons = [];

    // 弧形模式：禁用所有对齐按钮
    if (element.typesettingMode == TypeSettingMode.TEXT_CURVE) {
      return alignmentIcons;
    }

    // 检查当前语言是否支持分散对齐按钮
    bool isDistributedAlignmentSupported = _isDistributedAlignmentSupported();
    List<String> currentIcons = element.typesettingMode == TypeSettingMode.TEXT_VERTICAL && element.rotate != 270
        ? alignmentVerticalIcons
        : alignmentIcons;

    // 判断是否需要禁用分散对齐按钮的条件
    bool shouldDisableDistributedAlignment = widget.isTableCellText || // 表格模式
        element.typesettingMode == TypeSettingMode.TEXT_VERTICAL || // 横排90°和竖排模式
        !isDistributedAlignmentSupported; // 语言不支持分散对齐

    // 如果需要禁用分散对齐按钮，则添加到禁用列表中
    if (shouldDisableDistributedAlignment && currentIcons.length > 3) {
      disabledButtons.add(currentIcons[3]); // 分散对齐按钮
    }

    return disabledButtons;
  }

  /// 检查当前语言是否支持分散对齐按钮
  /// 只有简中、繁中、英文、日语、韩语支持分散对齐
  bool _isDistributedAlignmentSupported() {
    String? currentLanguage = CanvasPluginManager().hostMethodImpl?.getCurrentLanguageType();
    if (currentLanguage == null) return false;

    // 支持分散对齐的语言列表
    const supportedLanguages = ['zh-cn', 'zh-cn-t', 'en', 'ja', 'ko'];
    return supportedLanguages.contains(currentLanguage.toLowerCase());
  }

  List<String> getSelectedButtons(TextElement element) {
    if (widget.isTableCellText) {
      if (element.textAlignHorizontal != null) {
        return [alignmentIcons[element.textAlignHorizontal!]];
      } else {
        return [];
      }
    } else {
      int typeSettingMode = element.typesettingMode;
      if (typeSettingMode == TypeSettingMode.TEXT_VERTICAL && element.rotate != 270) {
        if (element.textAlignVertical != null) {
          return [alignmentVerticalIcons[element.textAlignVertical!]];
        } else {
          return [];
        }
        // return [alignmentVerticalIcons[element.textAlignVertical]];
      } else if (typeSettingMode == TypeSettingMode.TEXT_VERTICAL && element.rotate == 270) {
        if (element.textAlignVertical != null) {
          return [alignmentIcons[element.textAlignVertical!]];
        } else {
          return [];
        }
      } else if (typeSettingMode == TypeSettingMode.TEXT_HORIZONTAL) {
        if (element.textAlignHorizontal != null) {
          return [alignmentIcons[element.textAlignHorizontal!]];
        } else {
          return [];
        }
      } else {
        return [];
      }
    }
  }

  /// 获取垂直对齐按钮的图标列表
  List<String> _getVerticalAlignIcons(TextElement element) {
    return (element.typesettingMode == TypeSettingMode.TEXT_VERTICAL && element.rotate != 270
            ? alignmentVSelectors
            : alignmentHSelectors)
        .map((model) => model.svgAsset)
        .toList();
  }

  /// 获取垂直对齐按钮的选中状态
  List<String> _getSelectedVerticalAlignButtons(TextElement element) {
    CustomPopUpModelProtocol currentModel = getDefaultAlign(element);
    List<CustomPopUpModelProtocol> currentItems =
        element.typesettingMode == TypeSettingMode.TEXT_VERTICAL && element.rotate != 270
            ? alignmentVSelectors
            : alignmentHSelectors;

    int selectedIndex = currentItems.indexOf(currentModel);
    if (selectedIndex >= 0 && selectedIndex < currentItems.length) {
      return [currentItems[selectedIndex].svgAsset];
    }
    return [];
  }

  String tag_font_size_change = "tag_font_size_change";
  SharedPreferences? sp;

  ///第一次调整字号增加toast显示
  void showFirstFontSizeChangeTip(bool showTextStyle) {
    bool? hasFirstTip = CanvasPluginManager().nativeMethodImpl?.hasShowFontSizeChangeTips;
    if ((hasFirstTip == null || !hasFirstTip) && showTextStyle) {
      LoadingMix.showToast(intlanguage('app100001315', '您设置了字号，自动字号已关闭'));
      CanvasPluginManager().nativeMethodImpl?.hasShowFontSizeChangeTips = true;
    }
  }

  CustomPopUpModelProtocol getDefaultAlign(TextElement element) {
    // 模式三下存在对齐，其余的展示默认，但是不可点击
    // 模式三下的文本竖向对齐需要-1，用于适配老数据
    // 老数据默认为1作为顶对齐
    switch (element.typesettingMode) {
      case TypeSettingMode.TEXT_HORIZONTAL:
        int textAlignVertical = element.boxStyle == TextElementBoxStyle.fixedWidthHeight
            ? ((element.textAlignVertical ?? 1) - 1) < 0
                ? 0
                : (element.textAlignVertical ?? 1) - 1
            : 0;
        return alignmentHSelectors[textAlignVertical];
      case TypeSettingMode.TEXT_VERTICAL:
        return alignmentVSelectors.last;
      case TypeSettingMode.TEXT_CURVE:
        return alignmentHSelectors.first;
      case -1:
        // 多选元素存在不一致对齐，默认顶对齐，但是后面改动后按照改动的值变更
        int? textAlignVertical = element.boxStyle == TextElementBoxStyle.fixedWidthHeight
            ? ((element.textAlignVertical ?? 1) - 1) < 0
                ? 0
                : (element.textAlignVertical ?? 1) - 1
            : element.textAlignVertical;
        return alignmentHSelectors[textAlignVertical ?? 0];
      default:
        return alignmentHSelectors.first;
    }
  }

  /// 构建字体样式按钮（加粗、下划线、斜体）
  Widget _buildFontStyleButtons(List<String> styleIcons, TextElement defaultElement, WrapAlignment wrapAlignment) {
    return GroupButton(
      key: UniqueKey(),
      isRadio: false,
      spacing: 8,
      wrapAlignment: wrapAlignment,
      onSelected: (index, isSelected) {
        List<String> fontStyles = ['bold', 'underline', 'italic'];
        widget.canvasElements.forEach((canvasElement) {
          TextElement element = canvasElement.data as TextElement;
          if (isSelected) {
            if (!element.fontStyle.contains(fontStyles[index])) {
              element.fontStyle.add(fontStyles[index]);
            }
          } else {
            element.fontStyle.removeWhere((element) => element == fontStyles[index]);
          }
        });
        _notifyCanvasElementsDataChanged();
        String trackName = "";
        if (index == 0) {
          trackName = "加粗";
        } else if (index == 1) {
          trackName = "下划线";
        } else if (index == 2) {
          trackName = "斜体";
        }
        CanvasPluginManager().nativeMethodImpl!.sendTrackingToNative({
          "track": "click",
          "posCode": "108_072_138",
          "ext": {
            "b_name": trackName,
            "module_name": widget.isTableCellText ? "表格" : TrackUtils.getElementsTypeStr(widget.canvasElements)
          }
        });
      },
      isIconButtons: true,
      buttonWidth: widget.isTableCellText ? 40 : ((MediaQuery.sizeOf(context).width - 16 * 2) / 2 - 20 * 2) / 3,
      buttonHeight: 34,
      unselectedColor: Colors.transparent,
      selectedColor: CanvasTheme.of(context).backgroundLightColor!,
      borderRadius: BorderRadius.all(Radius.circular(8)),
      selectedButtons: [
        if (defaultElement.fontStyle.contains('bold')) styleIcons[0],
        if (defaultElement.fontStyle.contains('underline') &&
            defaultElement.typesettingMode != TypeSettingMode.TEXT_CURVE)
          styleIcons[1],
        if (defaultElement.fontStyle.contains('italic')) styleIcons[2],
      ],
      disableButtons: defaultElement.typesettingMode == TypeSettingMode.TEXT_CURVE ? [styleIcons[1]] : [],
      buttons: styleIcons,
      unselectedShadow: [],
    );
  }

  /// 根据当前语言获取对应的文本模式设置项
  List<TextModeSettingItem> _getTextModeSettingItemsByLanguage() {
    List<TextModeSettingItem> items;
    //简中、繁中、日、韩用中文图标
    String currentLanguage = CanvasUserCenter().languageCode;
    if (currentLanguage == LanguageName.LANGUAGE_ZH_CN ||
        currentLanguage == LanguageName.LANGUAGE_ZH_TW ||
        currentLanguage == "zh") {
      items = _modeSettingItemsCn;
    } else if (currentLanguage == LanguageName.LANGUAGE_KO) {
      items = _modeSettingItemsKo;
    } else if (currentLanguage == LanguageName.LANGUAGE_JA) {
      items = _modeSettingItemsJa;
    } else {
      items = _modeSettingItems;
      //英文去掉横排90图标；其他语言去掉竖排文字图标
      if (currentLanguage == LanguageName.LANGUAGE_EN) {
        items = _modeSettingItems.where((element) => element.textMode != TextMode.Horizontal_90).toList();
      } else {
        items = _modeSettingItems.where((element) => !element.fromVertical).toList();
      }
    }
    return items;
  }

  /// 判断是否需要显示文本过长警告
  bool _shouldShowTextSizeWarning(TextElement defaultElement) {
    // 条件：fixedWidthHeight模式 + shouldShowSizeWarning标记为true
    return defaultElement.boxStyle == TextElementBoxStyle.fixedWidthHeight && defaultElement.shouldShowSizeWarning;
  }

  /// 构建文本过长警告组件
  Widget _buildTextSizeWarningWidget() {
    return Container(
      padding: EdgeInsetsDirectional.only(start: 8, top: 2, bottom: 7),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgIcon(
            'assets/element/attribute/text_info_outline.svg',
            width: 15,
            height: 15,
            useDefaultColor: false,
          ),
          SizedBox(width: 4),
          Expanded(
            child: Text(
              intlanguage('app100002085', '文本过长，已临时缩小文字'),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w400,
                color: Color(0xFF3C3C43),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取默认的shouldShowSizeWarning状态，用于多选模式
  bool _getDefaultShouldShowSizeWarning() {
    // 只要有任何一个模式三元素的shouldShowSizeWarning为true，就显示警告
    for (var element in widget.canvasElements) {
      TextElement textElement = element.data as TextElement;
      if (textElement.boxStyle == TextElementBoxStyle.fixedWidthHeight && 
          textElement.shouldShowSizeWarning) {
        return true;
      }
    }
    return false;
  }
}

class TextModeSettingItem {
  String icon;
  String trackName;
  TextMode textMode;
  bool fromVertical;

  TextModeSettingItem({
    required this.icon,
    required this.trackName,
    required this.textMode,
    required this.fromVertical,
  });
}

enum TextMode {
  Horizontal,
  Horizontal_90,
  Vertical,
  Arc,
}

class ArrowPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class StarIconPainter extends CustomPainter {
  final Color color;

  StarIconPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 缩放比例以适应size
    final scaleX = size.width / 14;
    final scaleY = size.height / 13;

    final path = Path();

    // 根据SVG路径数据绘制五角星
    // 原始路径: M3.23486 12.418C2.96094 12.2139 2.90723 11.8701 3.05762 11.4243L4.14795 8.18555L1.36572 6.19287C0.984375 5.91895 0.8125 5.60742 0.925293 5.27979C1.03809 4.95752 1.34961 4.80176 1.81689 4.80176H5.23291L6.26953 1.56836C6.41455 1.11182 6.65625 0.870117 7 0.870117C7.34375 0.870117 7.58008 1.11182 7.7251 1.56836L8.76709 4.80176H12.1724C12.6504 4.80176 12.9565 4.95752 13.0693 5.27979C13.1768 5.60742 13.0156 5.91895 12.6289 6.19287L9.84668 8.18555L10.937 11.4243C11.0874 11.8701 11.0337 12.2139 10.7598 12.418C10.4805 12.6328 10.1421 12.563 9.75537 12.2837L7 10.2588L4.23926 12.2837C3.85254 12.563 3.51416 12.6328 3.23486 12.418Z

    path.moveTo(3.23486 * scaleX, 12.418 * scaleY);
    path.cubicTo(
        2.96094 * scaleX, 12.2139 * scaleY, 2.90723 * scaleX, 11.8701 * scaleY, 3.05762 * scaleX, 11.4243 * scaleY);
    path.lineTo(4.14795 * scaleX, 8.18555 * scaleY);
    path.lineTo(1.36572 * scaleX, 6.19287 * scaleY);
    path.cubicTo(
        0.984375 * scaleX, 5.91895 * scaleY, 0.8125 * scaleX, 5.60742 * scaleY, 0.925293 * scaleX, 5.27979 * scaleY);
    path.cubicTo(
        1.03809 * scaleX, 4.95752 * scaleY, 1.34961 * scaleX, 4.80176 * scaleY, 1.81689 * scaleX, 4.80176 * scaleY);
    path.lineTo(5.23291 * scaleX, 4.80176 * scaleY);
    path.lineTo(6.26953 * scaleX, 1.56836 * scaleY);
    path.cubicTo(
        6.41455 * scaleX, 1.11182 * scaleY, 6.65625 * scaleX, 0.870117 * scaleY, 7 * scaleX, 0.870117 * scaleY);
    path.cubicTo(
        7.34375 * scaleX, 0.870117 * scaleY, 7.58008 * scaleX, 1.11182 * scaleY, 7.7251 * scaleX, 1.56836 * scaleY);
    path.lineTo(8.76709 * scaleX, 4.80176 * scaleY);
    path.lineTo(12.1724 * scaleX, 4.80176 * scaleY);
    path.cubicTo(
        12.6504 * scaleX, 4.80176 * scaleY, 12.9565 * scaleX, 4.95752 * scaleY, 13.0693 * scaleX, 5.27979 * scaleY);
    path.cubicTo(
        13.1768 * scaleX, 5.60742 * scaleY, 13.0156 * scaleX, 5.91895 * scaleY, 12.6289 * scaleX, 6.19287 * scaleY);
    path.lineTo(9.84668 * scaleX, 8.18555 * scaleY);
    path.lineTo(10.937 * scaleX, 11.4243 * scaleY);
    path.cubicTo(
        11.0874 * scaleX, 11.8701 * scaleY, 11.0337 * scaleX, 12.2139 * scaleY, 10.7598 * scaleX, 12.418 * scaleY);
    path.cubicTo(
        10.4805 * scaleX, 12.6328 * scaleY, 10.1421 * scaleX, 12.563 * scaleY, 9.75537 * scaleX, 12.2837 * scaleY);
    path.lineTo(7 * scaleX, 10.2588 * scaleY);
    path.lineTo(4.23926 * scaleX, 12.2837 * scaleY);
    path.cubicTo(
        3.85254 * scaleX, 12.563 * scaleY, 3.51416 * scaleX, 12.6328 * scaleY, 3.23486 * scaleX, 12.418 * scaleY);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is StarIconPainter && oldDelegate.color != color;
  }
}
