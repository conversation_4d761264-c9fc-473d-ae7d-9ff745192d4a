import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:text/application.dart';
import 'package:text/utils/DebounceUtil.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/image_utils.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';

import '../../../tools/to_Native_Method_Channel.dart';
import '../../../utils/svg_icon.dart';
import '../../../widget/normal_button.dart';
import 'controller/feedback_logic.dart';
import 'controller/feedback_state.dart';

/// 重构后的反馈页面 - 使用GetX进行状态管理
class FeedbackPageRefactored extends StatefulWidget {
  String source;
  FeedbackPageRefactored({Key? key,required this.source}) : super(key: key);

  @override
  State<FeedbackPageRefactored> createState() => _FeedbackPageRefactoredState();
}

class _FeedbackPageRefactoredState extends State<FeedbackPageRefactored> {
  late FeedbackLogic logic;
  late FeedbackState state;
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _industryKey = GlobalKey();
  final GlobalKey _descKey = GlobalKey();
  final GlobalKey _customSizeKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // 注入FeedbackLogic控制器
    logic = Get.put(FeedbackLogic());
    logic.context = context;
    state = logic.state;
  }

  @override
  void dispose() {
    // 释放控制器资源
    _scrollController.dispose();
    Get.delete<FeedbackLogic>();
    super.dispose();
  }

    /// 滚动到指定组件
  void _scrollToWidget(GlobalKey key) {
    Future.delayed(Duration(milliseconds: 600), () {
      if (mounted && key.currentContext != null && _scrollController.hasClients) {
        // 获取键盘高度
        final keyboardHeight = MediaQuery.viewInsetsOf(context).bottom;

        // 模板描述输入框的滚动逻辑
        if (key == _descKey) {
          final maxScrollExtent = _scrollController.position.maxScrollExtent;
          final currentPosition = _scrollController.position.pixels;

          // 如果键盘弹起，滚动到合适位置
          if (keyboardHeight > 0) {
            final targetPosition = (maxScrollExtent * 0.6).clamp(0.0, maxScrollExtent);
            if (targetPosition > currentPosition) {
              _scrollController.animateTo(
                targetPosition,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            }
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
        title: Text(
          intlanguage("app00205", "意见反馈"),
          style: const TextStyle(
            color: ThemeColor.mainTitle,
            fontSize: 17,
            fontWeight: FontWeight.w600
          )
        ),
        leading: InkWell(
          highlightColor: Colors.transparent,
          splashColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16),
            child: Image(
              width: 24,
              height: 24,
              image: ImageUtils.getAssetImage('icon_arrow_back'),
              matchTextDirection: true,
            ),
          ),
          onTap: () => Navigator.of(context).pop(),
        ),
      ),
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Container(
                padding: EdgeInsetsDirectional.fromSTEB(
                  16,
                  16,
                  16,
                  MediaQuery.viewInsetsOf(context).bottom,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 行业用途输入
                    _buildIndustryInput(context,state),
                    SizedBox(height: 20),
                    // 标签纸尺寸选择
                    _buildSizeSelection(context,logic, state),
                    SizedBox(height: 20),

                    // 模板描述输入
                    _buildDescriptionInput(context,state),
                    SizedBox(height: 20),

                    // 图片上传区域
                    _buildImageUpload(context,logic, state),

                    // 底部留白，确保内容不被底部区域遮挡
                    SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ),

          // 底部区域
          _buildBottomSection(context,logic, state),
        ],
      ),
    );
  }

  /// 构建行业用途输入区域
  Widget _buildIndustryInput(BuildContext context,FeedbackState state) {
    return Container(
      key: _industryKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text.rich(
          TextSpan(
            children: [

              TextSpan(
                  text: intlanguage("app100002035", "行业用途"),
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle)
              ),
              TextSpan(
                  text: ' ',
                  style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle)
              ),
              TextSpan(
                text: intlanguage("app100002057", "(示例：烘焙价签)"),
                style: TextStyle(fontSize: 12, color: Colors.black38),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ),
        SizedBox(height: 8),
        TextField(
          style: const TextStyle(
              fontSize: 14.0, color: ThemeColor.mainTitle, fontWeight: FontWeight.w400),
          controller: state.industryController,
          focusNode: state.industryFocusNode,
          maxLength: 100,
          maxLines: 3,
          minLines: 1,
          textInputAction: TextInputAction.done,
          decoration: InputDecoration(
            hintText: intlanguage("app100002035", "行业用途") + "...",
            hintStyle: TextStyle(fontSize: 14.0, color: Color(0x4D3C3C43)),
            filled: true,
            fillColor: ThemeColor.COLOR_F7F7FA,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            counterText: "", // 隐藏字符计数器
          ),
        ),
      ],
    ));
  }

  /// 构建尺寸选择区域
  Widget _buildSizeSelection(BuildContext context,FeedbackLogic logic, FeedbackState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          intlanguage("app100002030", "标签纸尺寸（mm）"),
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.black)
        ),
        SizedBox(height: 8),
        Wrap(
          spacing: 5,
          runSpacing: 6,
          children: state.sizes.map((size) {
            return Obx(() {
              final isSelected = state.selectedSize.value == size;
              return GestureDetector(
                onTap: () => logic.changeSelectedSize(size),
                child: Container(
                  width: 60, // 固定宽度
                  height: 40, // 固定高度
                  decoration: BoxDecoration(
                    color: isSelected ? ThemeColor.COLOR_FFF2F2 : ThemeColor.COLOR_F7F7FA,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Text(
                      size,
                      textDirection: TextDirection.ltr, // 强制从左到右显示，保持数字顺序
                      style: TextStyle(
                        color: isSelected ? ThemeColor.brand : ThemeColor.mainTitle,
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            });
          }).toList(),
        ),

        // 自定义尺寸输入
        Obx(() {
          if (state.selectedSize.value == intlanguage("app100002037", "其他")) {
            return Container(
              key: _customSizeKey,
              child: Column(
                children: [
                  SizedBox(height: 10),
                  TextField(
                    controller: state.customSizeController,
                    focusNode: state.customSizeFocusNode,
                    maxLength: 20,
                    textInputAction: TextInputAction.done,
                    decoration: InputDecoration(
                      hintText: intlanguage("app100002036", "请输入尺寸..."),
                      hintStyle: TextStyle(fontSize: 14.0, color: Color(0x4D3C3C43)),
                      filled: true,
                      fillColor: ThemeColor.COLOR_F7F7FA,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                      counterText: "", // 隐藏字符计数器
                    ),
                  ),
                ],
              ),
            );
          }
          return SizedBox.shrink();
        }),
      ],
    );
  }

  /// 构建描述输入区域
  Widget _buildDescriptionInput(BuildContext context,FeedbackState state) {
    return Container(
      key: _descKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text.rich(
          TextSpan(
            children: [
              TextSpan(
                text: '*',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.brand)
              ),
              TextSpan(
                text: ' ',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.brand)
              ),
              TextSpan(
                text: intlanguage("app100002031", "模板描述"),
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle)
              ),
              TextSpan(
                text: ' ',
                style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle)
              ),
              TextSpan(
                text: intlanguage("app100002056", "(示例：有品名、价格、面包素材、边框装饰)"),
                style: TextStyle(fontSize: 12, color: Colors.black38),
              ),
            ],
          ),
          textAlign: TextAlign.left,
        ),
        SizedBox(height: 8),
        TextField(
          style: const TextStyle(
              fontSize: 14.0, color: ThemeColor.mainTitle, fontWeight: FontWeight.w400),
          controller: state.descController,
          focusNode: state.descFocusNode,
          maxLines: 3,
          maxLength: 100,
          textInputAction: TextInputAction.done,
          onTap: () {
            // 点击时也触发滚动，确保输入框可见
            _scrollToWidget(_descKey);
          },
          decoration: InputDecoration(
            hintText: intlanguage("app100002032", "希望包含的元素、风格等..."),
            hintStyle: TextStyle(fontSize: 14.0, color: Color(0x4D3C3C43)),
            filled: true,
            fillColor: ThemeColor.COLOR_F7F7FA,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            counterText: "", // 隐藏字符计数器
          ),
        ),
      ],
    ));
  }

  /// 构建图片上传区域
  Widget _buildImageUpload(BuildContext context,FeedbackLogic logic, FeedbackState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              intlanguage("app100002034", "图片"),
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: ThemeColor.mainTitle)
            ),
            SizedBox(width: 8),
            Obx(() => Text(
              logic.getImageText(),
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColor.subtitle)
            )),
          ],
        ),
        SizedBox(height: 4),
        Text(
          intlanguage("app100002033", "请上传当前使用或需要的模板图片"),
          style: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: ThemeColor.subtitle)
        ),
        SizedBox(height: 8),
        Obx(() => SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              ...List.generate(state.images.length, (index) {
                return Stack(
                  alignment: Alignment.topRight,
                  children: [
                    Container(
                      margin: EdgeInsetsDirectional.only(top:8,end: 10),
                      width: 106,
                      height: 106,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Color(0xFFE2E0D9),
                          width: 0.5,
                        ),
                        image: DecorationImage(
                          image: FileImage(File(state.images[index].path)),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    PositionedDirectional(
                      end: 4,
                      top: 2,
                      child: GestureDetector(
                        onTap: () {
                          // 先让输入框失去焦点，避免键盘弹起
                          FocusScope.of(context).unfocus();
                          logic.removeImage(index);
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(Icons.close, size: 16, color: Colors.white),
                        ),
                      ),
                    ),
                  ],
                );
              }),
              if (state.images.length < FeedbackState.maxImageCount)
                GestureDetector(
                  onTap: () {
                    // 先让输入框失去焦点，避免键盘弹起
                    FocusScope.of(context).unfocus();
                    _showImagePickerBottomSheet(context,logic);
                  },
                  child: Container(
                    width: 106,
                    height: 106,
                    decoration: BoxDecoration(
                      color: ThemeColor.COLOR_F7F7FA,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const SvgIcon(
                      'assets/images/icon_image_add.svg',
                      matchTextDirection: true,
                    ),
                  ),
                ),
            ],
          ),
        )),
      ],
    );
  }

  /// 构建底部区域
  Widget _buildBottomSection(BuildContext context,FeedbackLogic logic, FeedbackState state) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(16, 0, 16, 36),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 0.5,
            width: double.infinity,
            color: ThemeColor.divider,
          ),
          SizedBox(height: 8),

          // 提交按钮
          Obx(() {
            final isDescriptionEmpty = state.descController.text.trim().isEmpty && state.canSubmitting.value;
            return Opacity(
              opacity: isDescriptionEmpty ? 0.3 : 1.0,
              child: NormalButton(
                width: MediaQuery.of(context).size.width - 32,
                title: intlanguage("app100000130", "提交"),
                textSize: 16,
                fontWeight: FontWeight.w400,
                padding: const EdgeInsets.symmetric(vertical: 11),
                textColor: Colors.white,
                decoration: BoxDecoration(
                  color:  ThemeColor.brand,
                  borderRadius: BorderRadius.circular(12)
                ),
                selectedClosure: (isDescriptionEmpty) ? null : () async{
                  if (!DebounceUtil.checkClick(needTime: 1)) return;
                  if(!Application.networkConnected){
                    showToast(msg: intlanguage('app01139', '网络异常'));
                    return;
                  }

                  // 检查是否正在提交中，防止重复提交
                  if (state.isSubmitting) return;

                  // 设置提交状态
                  state.setSubmitting(true);

                  try {
                    int source;
                    if(widget.source == "canvas"){
                      source = 1;
                    }else{
                      source = 2;
                    }
                    bool result = await logic.submitFeedback();
                    ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "011_454_456", "ext": {"source":source,"type": result ? 1:0 }});
                    if(result){
                      await Future.delayed(const Duration(seconds: 1), () {
                        if(mounted){
                          Navigator.of(context).pop(result);
                        }
                      });
                    }
                  } finally {
                    // 重置提交状态
                    state.setSubmitting(false);
                  }
                },
              ),
            );
          }),

          SizedBox(height: 12),
          Text(
            intlanguage("app100002038", "如需帮助，请拨打4008608800"),
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.subtitle),
          ),

        ],
      ),
    );
  }

  /// 显示图片选择底部弹窗
  void _showImagePickerBottomSheet(BuildContext context,FeedbackLogic logic) {
    double bottomPadding = MediaQuery.paddingOf(context).bottom;
    showModalBottomSheet(
      context: context,
      barrierColor: Color(0x00000000).withOpacity(0.35),
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.0),
          topRight: Radius.circular(12.0)
        )
      ),
      enableDrag: false,
      isScrollControlled: true,
      builder: (context) => SafeArea(
        child: Container(
          height: 178 + (bottomPadding > 0 ? 0 : 15),
          color: Colors.transparent,
          padding: EdgeInsets.only(right: 12, left: 12),
          child: Column(
            children: [
              Container(
                height: 110.5,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        // 确保输入框失去焦点
                        FocusScope.of(context).unfocus();
                        logic.pickFromGallery();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(12),
                            topRight: Radius.circular(12)
                          )
                        ),
                        height: 55,
                        child: Center(
                          child: Text(
                            intlanguage("app100001879", '相册'),
                            style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400),
                          )
                        ),
                      ),
                    ),
                    Container(
                      color: Color(0xFF3C3C43).withOpacity(0.09),
                      height: 0.5,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.of(context).pop();
                        // 确保输入框失去焦点
                        FocusScope.of(context).unfocus();
                        logic.takePhoto();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(12),
                            bottomRight: Radius.circular(12)
                          )
                        ),
                        height: 55,
                        child: Center(
                          child: Text(
                            intlanguage("app00113", '拍照'),
                            style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400),
                          )
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                  // 确保输入框失去焦点
                  FocusScope.of(context).unfocus();
                },
                child: Container(
                  margin: EdgeInsets.only(top: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12)
                  ),
                  height: 55,
                  child: Center(
                    child: Text(
                      intlanguage("app100000692", '取消'),
                      style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.w400),
                    )
                  ),
                ),
              ),
            ],
          ),
        ),
      )
    );
  }
}
