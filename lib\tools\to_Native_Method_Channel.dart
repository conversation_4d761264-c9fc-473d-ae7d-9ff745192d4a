import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:get/get.dart';
import 'package:niim_login/niim_login.dart';
import 'package:niimbot_excel/niimbot_data_source_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/good_lib_field_info.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_manager.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:niimbot_lego/niimbot_lego.dart' as niimbot_lego;
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;
import 'package:niimbot_print_setting_plugin/print/firmware_upgrade/firmware_upgrade_manager.dart';
import 'package:niimbot_print_strategy/niimbot_print_strategy.dart';
import 'package:text/ad/print_ad_manager.dart';
import 'package:text/application.dart';
import 'package:text/business/app/offline_manager.dart';
import 'package:text/business/print/print_history_business.dart';
import 'package:text/business/print/print_log_business.dart';
import 'package:text/cache/cache_helper.dart';
import 'package:text/cap/meeting_manager.dart';
import 'package:text/cap/model/Preview_with_layouts_params.dart';
import 'package:text/cap/model/meeting_params.dart';
import 'package:text/connect/machine_alias_manager.dart';
import 'package:text/connect/nety_connect_helper.dart';
import 'package:text/database/ad/ad_db_utils.dart';
import 'package:text/database/banner/banner_db_utils.dart';
import 'package:text/database/printHistory/print_history_db_utils.dart';
import 'package:text/database/printLog/print_content_log_db_utils.dart';
import 'package:text/macro/constant.dart';
import 'package:text/pages/C1/model/c1_file_business.dart';
import 'package:text/migration/migration_manager.dart';
import 'package:text/pages/industry_template/home/<USER>/label_usage_record.dart';
import 'package:text/pages/industry_template/select_label/hardware_list_model.dart';
import 'package:text/pages/industry_template/select_label/model/shop_product_business.dart';
import 'package:text/pages/meProfile/me_profile_presenter.dart';
import 'package:text/pages/meProfile/nps_alert/nps_data_helper.dart';
import 'package:text/pages/scan/auth_login_api.dart';
import 'package:text/pages/scan/scan_code_login_presenter.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/template/model/template_detail_result.dart';
import 'package:text/template/model/template_list_model.dart';
import 'package:text/template/template_manager.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/template/template_service.dart';
import 'package:text/tools/gray_config.dart';
import 'package:text/tools/gray_config_manager.dart';
import 'package:text/tools/native_template_method_handler.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/utils/log_utils.dart';
import 'package:text/utils/preferenceUtil.dart';
import 'package:text/utils/toast_util.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import '../utils/layout_scheme_template_utils.dart';

class ToNativeMethodChannel {
  static const String _disConnect = 'dis_connect';

  static const String _setDeviceItem = 'set_DeviceItem';

  static const String _setDeviceList = 'set_DeviceList';

  static const String _setDeviceSeriesList = 'set_DeviceSeriesList';

  static const String _setSelectedDeviceSeries = 'set_selectedDeviceSeries';

  static const String _setToken = 'set_Token';

  static const String _setMallToken = 'set_mall_token';

  static const String _getLanguageDetail = 'getLanguageDetail';

  static const String _getLoginPluginLanguageDetail = 'getLoginPluginLanguageDetail';

  static const String _getAppCurrentLanguageType = 'getAppCurrentLanguageType';

  static const String _getDeviceSeriesData = 'getDeviceSeriesData';

  static const String _setUser = 'set_User';

  static const String _logout = 'log_Out';

  static const String _getAgent = 'get_Agent';

  static const String _getAnonymousId = 'getAnonymousId';

  static const String _getAppConfig = 'get_app_config';

  static const String _getImParams = 'getImParams';

  static const String _getPasteboardSetting = 'pasteboardSetting';

  static const String _setPasteboardSetting = 'setPasteboardSwitch';

  static const String _printSame = 'print_Same';

  static const String _authorization_Status = 'authorization_Status'; //相册

  static const String _camera_authorization_Status = '_camera_authorization_Status'; //照相机

  static const String _sendTrackingToNative = 'sendTrackingToNative';
  static const String _unVipBatchPrintCount = 'unVipBatchPrintCount';
  static const String _notifyLoginStatus = "notifyLoginStatus";

  static const String _antiLost = "antiLost";

  static const String _queryPrinterConnectedInfo = "queryPrinterConnectedInfo";

  static const String _wxPay = '_wxPay';

  static const String _setRfidColor = 'setRfidColor';

  static const String _getDeviceVipState = "_getDeviceVipState";

  static const String _getDeviceIsSupportAnonymity = "getDeviceIsSupportAnonymity";

  static const String _checkAssetImages = 'checkAssetImages';

  static const String _setLanguage = 'set_Language';

  static const String _shareDecode = 'shareDecode';

  static const String _goToShareTemplate = 'goToShareTemplate';

  static const String _getLocaleLanguage = 'getLocaleLanguage';

  static const String _checkLocaleLanguage = 'checkLocaleLanguage';

  static const String _showAntiMap = 'showAntiMap'; //打开防丢器地图

  static const String _clearAntiMap = 'clearAntiMap'; //清理防丢器位置信息

  static const String _requestLocationAuth = 'requestLocationAuth'; //请求定位权限

  static const String _requestAntiLocationDetail = 'requestAntiLocationDetail'; //请求定位信息

  static const String _checkAppsIsExist = 'checkAppsIsExist'; //是否安装app

  static const String _toApps = 'toApps'; //跳转app

  static const String _setLogProxy = '_setLogProxy'; //iOS日志

  static const String _notifyAgreePrivacy = 'notifyAgreePrivacy';

  static const String _notifyHomepageReady = '_notifyHomepageReady'; //广场首页是否准备好

  static const String _getPPIFromNative = 'getPPIFromNative';
  static const String _getScaleFromNative = 'getScaleFromNative';

  static const String _notificationPermissionSetting = 'notificationPermissionSetting'; //通知权限设置
  static const String _requestInnerAppReview = 'storeReview'; //应用内评分
  static const String _getAPNSAuthStatus = 'getAPNSAuthStatus'; //获取离线推送权限状态
  static const String _flutterLog = '_flutterLog'; //获取离线推送权限状态

  static const String _connectSearchDevice = 'connectSearchDevice';
  static const String _cancelSearchConnectDevice = 'cancelSearchConnectDevice';

  static const String _queryPassiveDisconnectedAntiLosts =
      '_queryPassiveDisconnectedAntiLosts'; //查询被动断开的防丢器列表,根据情况显示提醒与否

  static const String _clearPassiveDisconnectedAntiLostList =
      '_clearPassiveDisconnectedAntiLostList'; //清空被动断开的防丢器列表,一旦用户进入防丢器相关页面,那么以后不用显示提醒,所以要清空列表
  static const String _closeNativeToast = '_closeNativeToast'; //关闭原生的toast,防止重叠

  static const String _getSystemLanguage = '_getSystemLanguage'; //获取当前系统的语言

  static const String _syncIntlToNaive = '_syncIntlToNaive'; //同步当前国际化配置到原生

  static const String _getIpAddressFromNative = '_getIpAddressFromNative'; //从原生获取ip地址

  static const String _syncLanguageToNative =
      '_syncLanguageToNative'; //将用户使用的语言,以及所在地区同步给原生, 原生将其配置在请求头中,格式为:简体中文: zh-cn(zh-Hans-cn), 繁体中文: zh-Hant-cn

  static const String _oneKeyLoginAuth = 'oneKeyLoginAuth';
  static const String _wechatLoginAuth = 'wechatLoginAuth';
  static const String _qqLoginAuth = 'qqLoginAuth';
  static const String _oneKeyBindAuth = 'oneKeyBindAuth';
  static const String _oneKeyRegisterAuth = 'oneKeyRegisterAuth';
  static const String _facebookLoginAuth = 'facebookLoginAuth';

  static const String _uploadFileToOSS = '_uploadFileToOSS';
  static const String _downFilefromOSS = '_downFilefromOSS';

  static const String _getBluetoothStatus = 'getBluetoothStatusEx'; //获取蓝牙状态
  static const String _openBluetooth = 'openBluetoothEx';
  static const String _toNiimbotHomePage = 'toNiimbotHomePage';
  static const String _checkLocationPermission = 'checkLocationPermissionEx';
  static const String _requestLocationPermission = 'requestLocationPermissionEx';
  static const String _setLocationPermission = 'setLocationPermissionEx';
  static const String _searchDevice = 'searchDeviceEx'; //搜索设备
  static const String _stopSearchDevice = 'stopSearchDeviceEx'; //停止搜索
  static const String _connectDevice = 'connectDeviceEx'; //连接打印机
  static const String _disconnectDevice = 'disconnectDeviceEx'; //断开连接
  static const String _clearSearchDevice = 'clearSearchDeviceEx'; //清除搜索记录, 如果正在搜索就停止搜索并清空记录
  static const String _registerLocationServiceStatus = 'registerLocationServiceStatus'; //监听定位服务开关状态
  static const String _unRegisterLocationServiceStatus = 'unRegisterLocationServiceStatus'; //注销监听定位服务开关状态

  static const String _isIphoneX = '_isIphoneX'; //是否为iphoneX以后的手机

  static const String _toGoogleMarket = 'toGoogleMarket'; //跳转到google商店

  static const String _popFromFlutterLeft = '_popFromFlutterLeft';

  static const String _setFlutterVCCanSideslip = 'setFlutterVCCanSideslip';

  static const String _popFromFlutterTop = '_popFromFlutterTop';

  static const String _toConnectBluetooth = '_toConnectBluetooth';

  static const String _meProfileEvent = '_meProfileEvent';

  static const String _getPrinterElectricalLevel = 'getPrinterElectricalLevel'; //获取打印机电量

  static const String _isProduction = 'isProduction'; //是否为生产环境

  static const String _getTimeZone = 'getTimeZone'; //获取时区

  static const String _appForLoginThatInstalledInMyPhone = 'appForLoginThatInstalledInMyPhone'; //获取时区

  static const String _getSupportSocialList = 'getSupportSocialList';
  static const String _checkSIMCardInfo = 'checkSIMCardInfo';
  static const String _checkIfInstallNIIM = 'checkIfInstallNIIM';
  static const String _SIMAccountOneKeyConfig = 'SIMAccountOneKeyConfig';
  static const String _SIMAccountOneKeyLogin = 'SIMAccountOneKeyLogin';
  static const String _bindMainAccountSuccess = 'bindMainAccountSuccess';
  static const String _SIMAccountOneKeyRegister = 'SIMAccountOneKeyRegister';
  static const String _SIMAccountOneKeyBinding = 'SIMAccountOneKeyBinding';
  static const String _reportLoginSuccess = 'reportLoginSuccess'; //上报登录成功信息
  static const String _cancelLogin = 'cancelLogin'; //上报登录成功信息
  static const String _updateUserInfo = 'updateUserInfo'; //上报登录成功信息
  static const String _getUserInfo = 'getUserInfo'; //从native获取用户信息
  static const String _getUserMallToken = 'getUserMallToken'; //从native获取商城token
  static const String _getETagConnect = 'getETagConnect'; //电子价签自动回连
  static const String _goodsMixList = 'goodsMixList'; //商品数据混入
  static const String _getPreviewPicture = 'getPreviewPicture'; //获取模板预览图像
  static const String _setConnectDevice = 'setConnectDevice'; //设置连接设备
  static const String _setSDKRfidData = 'setSDKRfidData'; //sdk Rfid 数据
  static const String _goodsMixListAll = 'goodsMixListAll'; //商品数据批量混入
  static const String _endSendPictures = 'endSendPictures'; //本来数据写入完成
  static const String _eTagStatusListener = 'eTagStatusListener'; //商品数据混入
  static const String _etagDisConnect = 'etagDisConnect'; //断开价签机连接
  static const String _resetEtagStatus = 'resetEtagStatus'; //重置价签机状态
  static const String _isIosFristLaunch = 'isIosFristLaunch'; //是否首次启动app
  static const String _unUIPrintComplate = 'unNeedUIPrintComplate'; //无UI打印完成
  static const String _getLocalUserInfoCache = 'getLocalUserInfoCache'; //从native获取用户缓存信息
  static const String _getUseRecentTemplate = 'getUseRecentTemplate'; // 获取最近使用的模版记录

  static const String _getUseRecentLabel = 'getUseRecentLabel'; // 获取最近使用的模版记录

  static const String _getDeviceSeries = 'getDeviceSeries'; // 从机型系列ID获取系列ID下的首个机型模型

  static const String _closeHardWareConnected = 'closeHardWareConnected'; // 断开当前连接机器

  static const String _getPrinterById = 'getPrinterById'; // 根据机器id获取机器详情

  static const String _getAppEnv = 'getAppEnv'; //获取app接口环境是test还是online

  static const String _createAdvanceQRCode = 'createAdvanceQRCode'; //通过画板元素面板闯将高级二维码

  static const String _updateAdvanceQRCodeToNative = 'updateAdvanceQRCodeToNative'; //同步高级二维码信息至原生

  static const String _changeAdvanceQRCode = 'changeAdvanceQRCode'; //通过双击或属性区域更新高级二维码

  static const String _cacheAdvanceQRCode = 'cacheAdvanceQRCodeInfo'; //缓存活码信息

  static const String _previewAdvanceQRCode = 'previewAdvanceQRCode'; //通过属性区域预览高级二维码

  static const String _nativeTemplateNeedRefresh = 'nativeTemplateNeedRefresh'; //通知原生刷新模板列表

  static const String _nativeTemplateDBUpdate = 'nativeTemplateDBUpdate'; //原生数据库更新

  static const String _getNativeDBTemplatePathInfo = 'getNativeDBTemplatePathInfo'; //通过原生获取数据库地址及表名

  static const String _toNativeBatchPrint = 'toNativeBatchPrint'; //通过调用原生批量打印

  static const String _toRiskCheckDialog = 'toRiskCheckDialog'; //全局风控弹窗

  static const String _getAdvanceQRCodeInfo = 'getAdvanceQRCodeInfo'; //通过ID获取高级二维码

  static const String _getAdvanceQRCodeCaches = 'getAdvanceQRCodeCaches'; //获取高级二维码缓存信息

  static const String _nativePersonalTemplateSearchHistory = 'nativePersonalTemplateSearchHistory'; //获取高级二维码缓存信息

  static const String _checkTemplateResourceComplate = "checkTemplateInfoComplate"; //检查模板
  ///保存模板
  static const String _saveTemplate = 'saveTemplate';

  //显示保存模版名称弹窗
  static const String _showSaveNameAlert = "showSaveNameAlert";

  ///获取模板详情
  static const String _getTemplateDetailForNative = 'getTemplateDetail';

  ///获取线缆详情
  static const String _getCableDetailForNative = 'getCableDetail';

  ///是否显示vip
  static const String _ifShowVip = "ifShowVip";

  ///当前是否为登录状态
  static const String _checkLoginStatus = "checkLoginStatus";

  ///获取用户id
  static const String _getUserId = "getUserId";

  ///获取Excel导入的社交列表
  static const String _getImportFileSocialList = "getImportFileSocialList";

  ///从本地导入Excel
  static const String _importFileFromLocal = "importFileFromLocal";

  ///从三方社交导入Excel
  static const String _importExcelFromSocialApp = "importExcelFromSocialApp";

  static const String _checkBarcodeFormat = "checkBarcodeFormat";

  static const String _shopInfoNotifyNumbers = "shopInfoNotifyNumbers";

  static const String _userOffSuccess = "userOffSuccess"; //用户注销成功

  ///从native获取代理地址
  static const String _getFlutterProxyUrl = "getDioProxyUrl";

  ///从native获取手机唯一id
  static const String _getDeviceId = "getDeviceId";

  ///从native获取SP缓存数据
  static const String _getSPData = "getSPData";

  ///从native获取int SP缓存数据
  static const String _getIntSPData = "getIntSPData";

  ///ionic小程序发送打印完成事件
  static const String _printCommodity = "printCommodity";

  /// 同步灰度策略数据给原生
  static const String _syncGrayConfig = "syncGrayConfig";

  /// 强制LTR模式
  static const String _isForceLTR = "isForceLTR";

  /// 是否存在模版信息
  static const String _isExistTemplate = "isExistTemplate";

  /// 获取打印浓度
  static const String _getDensity = "getDensity";

  /// 是否存在模版信息
  static const String _isExistEtagTemplate = "isExistEtagTemplate";

  //是否可以打开历史模版
  static const String _isCanOpenHistoryTemplate = 'isCanOpenHistoryTemplate';

  //获取行业模板详情
  static const String _getIndustryTemplateDetail = 'getIndustryTemplateDetail';

  //下载模版详情
  static const String _downloadTemplateDetail = 'downloadTemplateDetail';

  //下载模版字体
  static const String _downloadTemplateFonts = 'downloadTemplateFonts';

  //市场评分
  static const String _toMarketRating = 'toMarketRating';

  //无UI打印异常事件
  static const String _unUIPrintErrorEvent = 'unUIPrintErrorEvent';

  //服务端RFID可打印上限刷新
  static const String _refreshServerRFIDInfo = 'refreshServerRFIDInfo';

  //标签纸记录
  static const String _labelRecord = 'labelRecord';

  //取消下载模版详情
  static const String _cancelDownloadTemplateDetail = 'cancelDownloadTemplateDetail';

  //去除要下载的模版详情
  static const String _removeTemplateDetail = 'removeTemplateDetail';

  //扫码登录成功
  static const String _showScanLoginSuccess = 'showScanLoginSuccess';

  //是否打印
  static const String _ableToPrint = 'ableToPrint';

  //nps弹窗
  static const String _toNpsAlert = 'toNpsAlert';

  //结束所有的flutter页面
  static const String _finishFlutterPages = 'finishFlutterPages';

  //获取应用信息
  static const String _getAppInfo = 'getAppInfo';

  //进入C1页面
  static const String _notifyEnterC1Page = 'notifyEnterC1Page';

  //退出C1页面
  static const String _notifyBackFromC1Page = 'notifyBackFromC1Page';

  static const String _toC1HomePage = 'toC1HomePage';

  //原生从flutter获取个人商品库维护字段信息
  static const String _getGoodLibFieldsInfoFromFlutter = 'getGoodLibFieldsInfoFromFlutter';

  //获取最大模版支持打开的最大版本号
  static const String _getMaxSupportTemplateVersion = 'getMaxSupportTemplateVersion';

  //商品库模版打印时候的快捷选商品操作
  static const String _chooseGoodsToPrint = "_chooseGoodsToPrint";

  //对应excel文件是否被缓存过
  static const String _hasExcelCache = "hasExcelCache";

  //请求极验验证
  static const String _requestGeetestVerify = "requestGeetestVerify";

  static const String _clearAllAdData = "clearAllAdData";

  static const String _saveAdToDatabase = "saveAdToDatabase";

  static const String _getAdFromDatabase = "getAdFromDatabase";

  /// 刷新AD数据库中的某个model数据
  static const String _refreshAdToDatabase = "refreshAdToDatabase";

  static const String _clearAllBannerData = "clearAllBannerData";

  static const String _saveBannerToDatabase = "saveBannerToDatabase";

  static const String _getBannerFromDatabase = "getBannerFromDatabase";

  /// 刷新Banner数据库中的某个model数据
  static const String _refreshBannerToDatabase = "refreshBannerToDatabase";

  //更换打印数据
  static const String _changePrintData = "changePrintData";

  /// 从数据库获取打印数据
  static const String _getPrintDataLogFromDatabase = "getPrintDataLogFromDatabase";

  /// 保存打印数据日志
  static const String _savePrintDataLogToDatabase = "savePrintDataLogToDatabase";

  /// 清除打印数据日志
  static const String _clearPrintDataLogDatabase = "clearPrintDataLogDatabase";

  /// 上传打印数据日志
  static const String _uploadPrintDataLog = "uploadPrintDataLog";

  //保存打印内容日志
  static const String _savePrintContentLogToDatabase = "savePrintContentLogToDatabase";

  static const String _getAllPrintContentLogFromDatabase = "getAllPrintContentLogFromDatabase";

  /// 分页获取打印内容数据
  static const String _getPrintContentLogFromDatabase = "getPrintContentLogFromDatabase";

  /// 从打印内容日志表中筛选符合ID的模型
  static const String _filterPrintContentLogWithIDFromDatabase = "filterPrintContentLogWithIDFromDatabase";

  /// 从打印内容日志表中筛选未上传的模型
  static const String _filterUNUploadingPrintContentLogFromDatabase = "filterUNUploadingPrintContentLogFromDatabase";

  /// 刷新Banner数据库中的某个model数据
  static const String _refreshPrintContentLogToDatabase = "refreshPrintContentLogToDatabase";

  static const String _deleteUploadSuccessPrintContentLog = "deleteUploadSuccessPrintContentLog";

  static const String _deleteUploadSuccessPrintContentLogNew = "deleteUploadSuccessPrintContentLogNew";

  //保存打印历史
  static const String _savePrintHistoryToDatabase = "savePrintHistoryToDatabase";

  /// 筛选缩略图为不为空的打印历史
  static const String _filterThumbnailNotNUllPrintHistoryFromDatabase =
      "filterThumbnailNotNUllPrintHistoryFromDatabase";

  /// 筛选缩略图为空的打印历史
  static const String _filterThumbnailNUllPrintHistoryFromDatabase = "filterThumbnailNUllPrintHistoryFromDatabase";

  /// 刷新Banner数据库中的某个model数据
  static const String _refreshPrintHistoryToDatabase = "refreshPrintHistoryToDatabase";

  static const String _getAllPrintHistoryFromDatabase = "getAllPrintHistoryFromDatabase";

  static const String _getFontClassify = "getFontClassify";

  static const String _deleteUploadSuccessPrintHistory = "deleteUploadSuccessPrintHistory";

  //获取标签纸跳转商城链接
  static const String _getLabelShopLink = "getLabelShopLink";

  //分享模版
  static const String _shareTemplate = "shareTemplate";

  //获取打印完成广告相关数据
  static const String _getPrintAdInfo = "getPrintAdInfo";

  //获取RFID替换后的模板
  static const String _getReplaceTemplate = "getReplaceTemplate";

  //android原生网络状态变化
  static const String _nativeNetworkChange = "nativeNetworkChange";

  //ios原生网络状态变化
  static const String _flutterOutService = "flutterOutService";

  //同步熔断状态
  static const String _syncOutOfService = "syncOutOfService";

  static const String _getNativeNetworkState = "getNativeNetworkState";

  static const String _checkOfflineAlert = "checkOfflineAlert";

  //消息中心桥接原生方法start
  static const String _areNotificationEnabled = 'areNotificationEnabled';
  static const String _jumpToNotificationSetting = 'jumpToNotificationSetting';
  static const String _notifyUnreadMessageCount = 'notifyUnreadMessageCount';
  static const String _jumpToMessageDetail = 'jumpToMessageDetail';

  //获取一次性登录授权码
  static const String _getAuthCode = 'getAuthCode';

  //消息中心桥接原生方法end

  //通知原生-刷新原生相关模版列表
  static const String _refreshNativeTemplateList = "refreshNativeTemplateList";

  static const String _notifyFlutterChannel = 'notifyFlutterChannel';
  static const String _checkDebugMode = 'checkDebugMode';

  static const String _modifyMachineAlias = 'modifyMachineAlias';

  static const String _getAllMachineAliasList = 'getAllMachineAliasList';

  static const String _checkC1TemplateShareDecodeStatus = 'checkC1TemplateShareDecodeStatus';

  static const String _pushToRoute = 'pushToRoute';

  // 单例公开访问点
  factory ToNativeMethodChannel() => sharedInstance();

  // 静态私有成员，没有初始化
  static ToNativeMethodChannel _instance = ToNativeMethodChannel._();

  static MethodChannel platform = MethodChannel(ConstantKey.JC_Method_Channel);

  // 私有构造函数
  ToNativeMethodChannel._() {
    platform = MethodChannel(ConstantKey.JC_Method_Channel);
    platform.setMethodCallHandler(_methodCallHandler);
  }

  Future<dynamic> _methodCallHandler(MethodCall call) async {
    if (NativeTemplateMethod.matchAny(call.method)) {
      Log.e("==========native call NativeTemplateMethod=${call.method}");
      return NativeTemplateMethodHandler.handleTemplateMethod(call.method, call.arguments);
    } else if (call.method == 'loginPluginCheckSIMAccountStatus') {
      int authType = call.arguments['authType']; //1:一键登录,2:一键注册
      String ispToken = call.arguments['ispToken'];
      int authResult = call.arguments['authResult'] ?? 0; //-1：异常;0：成功；1：切换方式；2：用户取消
      Log.e("==========native call method, authType: $authType, ispToken: $ispToken");
      if (!ispToken.isNotEmpty) {
        Map<String, dynamic> map = {};
        map['registered'] = false;
        map['errorToken'] = true;
        map['authResult'] = authResult;
        try {
          platform.invokeMethod('notifyCheckSIMAccountStatusResult', map);
        } on PlatformException catch (e) {
          Log.d(e.toString());
        }
      } else {
        if (authType == 1) {
          LoginPluginApi.phoneOneKeyLoginCheckRegisterStatus(ispToken, (registered) {
            Map<String, dynamic> map = {};
            map['registered'] = registered;
            try {
              platform.invokeMethod('notifyCheckSIMAccountStatusResult', map);
            } on PlatformException catch (e) {
              Log.d(e.toString());
            }
          });
        } else if (authType == 2) {
          LoginPluginApi.phoneOneKeyRegisterCheckRegisterStatus(ispToken, (registered) {
            Map<String, dynamic> map = {};
            map['registered'] = registered;
            try {
              platform.invokeMethod('notifyCheckSIMAccountStatusResult', map);
            } on PlatformException catch (e) {
              Log.d(e.toString());
            }
          });
        } else if (authType == 3) {
          LoginPluginApi.phoneOneKeyRegisterCheckRegisterStatus(ispToken, (registered) {
            Map<String, dynamic> map = {};
            map['registered'] = registered;
            // map['type'] = authType;
            try {
              platform.invokeMethod('notifyCheckSIMAccountStatusResult', map);
            } on PlatformException catch (e) {
              Log.d(e.toString());
            }
          });
        }
      }
      return Future<Map>.value({});
    } else if (call.method == 'refreshPrintRecordList') {
      Log.d("========== native call method, refreshPrintRecordList ${call.arguments} =========");
      NiimbotEventBus.getDefault().post({'refreshPrintRecordList': {}});
      return Future<Map>.value({});
    } else if (call.method == 'getGrayConfig') {
      try {
        GrayConfig? config = await GrayConfigManager().loadConfigForAndroid();
        String json = grayConfigToJson(config!);
        return json;
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    } else if (call.method == 'riskCheck') {
      ///风控接口调用
      Log.d("============Flutter, 风控接口调用");
      Map<String, dynamic> params = call.arguments.cast<String, dynamic>();
      // Completer<Map<String, dynamic>> result = Completer();
      // Map<String, dynamic> params = call.arguments.cast<String, dynamic>();
      // RiskShieldHelper().checkRisk(params, (p0) {
      //   Log.d("============Flutter, 风控接口调用结果：${p0}");
      //   result.complete(p0);
      // });
      // return result.future;
      // RfidManager.instance.buildRiskCheck(Application.allContext!, params["action"]);
    } else if (call.method == 'transformPcExcelTemplateJson') {
      Map<String, dynamic> params = call.arguments.cast<String, dynamic>();
      try {
        String? json = await TemplateUtils.transformPcNewExcelToCompactTemplateJsonData(params["templateJson"]);
        return json;
      } on PlatformException catch (e) {
        Log.d(e.toString());
        return params["templateJson"];
      }
    } else if (call.method == 'getNpsJson') {
      //MeProfilePresenter
      String code = call.arguments['code'] ?? Application.currentAppLanguageType;
      Completer<Map<String, dynamic>> result = Completer();
      MeProfilePresenter().getNpWebUrl(
          languageCode: code,
          success: (value) {
            result.complete(value);
          });
      return result.future;
    } else if (call.method == 'getPrintStrategy') {
      //获取打印策略
      Map<String, dynamic> rfidInfo = Map<String, dynamic>.from(call.arguments['rfidInfo'] ?? {});
      String stategyScene = call.arguments['stategyScene'] ?? '';
      GetSecurityScene securityScene;
      switch (stategyScene) {
        case 'JCStrategyDeviceConnect':
        case 'JCStrategyBoxLib':
          securityScene = GetSecurityScene.connectDevice;
          securityScene = GetSecurityScene.boxLid;
          break;
        case 'JCStrategyToPrint':
          securityScene = GetSecurityScene.toPrint;
          break;
        case 'JCStrategyAfterPrint':
          securityScene = GetSecurityScene.afterPrint;
          break;
        default:
          securityScene = GetSecurityScene.connectDevice;
      }
      Completer<Map<String, dynamic>> result = Completer();
      bool paperIsSupportDevice = call.arguments['paperIsSupportDevice'] ?? true;
      debugPrint('主工程 $securityScene 获取打印策略 Time:${DateTime.now()}');
      NiimbotSecurityPrintHelper.getSecurityPrint(rfidInfo.isEmpty ? null : ChipRFIDInfo.fromJson(rfidInfo),
              getSecurityScene: securityScene, paperIsSupportDevice: paperIsSupportDevice)
          .then((PrintStrategyResult? strategyResult) {
        debugPrint('主工程 $securityScene 获取到打印策略: ${strategyResult?.toJson()} Time:${DateTime.now()}');
        result.complete(strategyResult == null ? {} : strategyResult.toJson());
      });
      return result.future;
    } else if (call.method == 'getNpsVipJson') {
      String code = call.arguments['code'] ?? Application.currentAppLanguageType;
      Completer<Map<String, dynamic>> result = Completer();
      MeProfilePresenter().getNpVipWebUrl(
          languageCode: code,
          success: (value) {
            result.complete(value);
          });
      return result.future;
    } else if (call.method == 'generateLayoutSchemeTemplate') {
      String layoutScheme = call.arguments['layoutScheme'] ?? "";
      String formData = call.arguments['formData'] ?? "";
      String labelId = call.arguments['labelId'] ?? "";
      bool isPreview = call.arguments['isPreview'] ?? true;
      if (layoutScheme.isEmpty) {
        return "";
      }
      Completer<String> result = Completer();
      LayoutSchemeTemplateUtils.generateSchemeTemplate(layoutScheme, formData, isPreview).then((canvasJson) {
        result.complete(canvasJson);
      });
      return result.future;
    } else if (call.method == 'generateLayoutSchemeDataSourceTemplate') {
      if (!Application.networkConnected) {
        showToast(msg: intlanguage('app01139', '网络异常'));
        return "";
      }
      String result = "";
      try {
        String params = call.arguments['meetingParams'] ?? "";
        MeetingParams meetingParams = meetingParamsFromJson(params);
        MeetingManager.instance.setMeetingParams(meetingParams);
        if (meetingParams.layoutJson?.isEmpty == true) {
          return "";
        }
        if (meetingParams.isCateringCap == true) {
          String layoutScheme = meetingParams.layoutJson!;
          String contentJson = meetingParams.constructContentJson();
          String canvasJson = niimbot_lego.parseLayoutToCanvasJson(layoutScheme, contentJson);
          TemplateData? labelData = await TemplateService().fetchTemplateDetailById(meetingParams.labelId ?? '');
          List<List<String>> rowData = await MeetingManager.instance
              .getRowData(meetingParams.datasourceId ?? "", meetingParams.rowDataIds ?? []);
          if (rowData.isEmpty) {
            showToast(msg: intlanguage('app100000346', '数据不存在'));
            return "";
          }
          String templateJson =
              await LayoutSchemeTemplateUtils.scaleCanvasJson(canvasJson, meetingParams, rowData, labelData);
          String nativeJson = await MeetingManager.instance.generateCateringDataSourceTemplate(templateJson, rowData);
          result = nativeJson;
        } else {
          TemplateData? labelData = await TemplateService().fetchTemplateDetailById(meetingParams.labelId ?? '');
          String canvasJson = await LayoutSchemeTemplateUtils.parseLayoutToCanvasJson(
              meetingParams.layoutJson!, meetingParams.constructContentJson(), labelData);
          String nativeJson = await MeetingManager.instance.generateDataSourceTemplate(canvasJson);
          result = nativeJson;
        }
      } catch (e, stack) {
        result = "";
      }
      if (result.isEmpty) {
        showToast(msg: intlanguage('app01139', '网络异常'));
      }
      return result;
    } else if (call.method == 'generatePreviewTemplateWithLayouts') {
      if (!Application.networkConnected) {
        showToast(msg: intlanguage('app01139', '网络异常'));
        return [];
      }
      try {
        //根据多个版式 生成多个静态模版
        String params = call.arguments['params'] ?? "";
        PreviewWithLayoutsParams meetingParams = previewWithLayoutsParamsFromJson(params);
        TemplateData? labelData = await TemplateService().fetchTemplateDetailById(meetingParams.labelId ?? '');
        List<String> canvasJsons = await LayoutSchemeTemplateUtils.parseLayoutsToListCanvasJson(
            meetingParams, meetingParams.constructContentJson(), labelData);
        return canvasJsons;
      } catch (e, stack) {
        e.printError();
        return [];
      }
    } else if (call.method == 'generateTemplateWithLayout') {
      try {
        String params = call.arguments['params'] ?? "";
        MeetingParams meetingParams = meetingParamsFromJson(params);
        if (meetingParams.layoutJson?.isEmpty == true) {
          return "";
        }
        TemplateData? labelData = await TemplateService().fetchTemplateDetailById(meetingParams.labelId ?? '');
        String canvasJson = await LayoutSchemeTemplateUtils.parseLayoutToCanvasJson(
            meetingParams.layoutJson!, meetingParams.constructContentJson(), labelData);
        return canvasJson;
      } catch (e, stack) {
        e.printError();
        return "";
      }
    } else if (call.method == "isShowNps") {
      return NpsDataHelper.isShowNps() ? "1" : '0';
    } else if (call.method == 'closeNps') {
      int flag = call.arguments['closeNps'] as int? ?? 0;
      String uniAppId = call.arguments['uniAppId'] ?? "";
      bool hasSubmitNpsData = (flag == 1) ? true : false;
      if (uniAppId.isEmpty) {
        NiimbotEventBus.getDefault().post({'closeNps': hasSubmitNpsData});
      }
      NpsDataHelper.closeNps(hasSubmit: hasSubmitNpsData, uniAppId: uniAppId);
      return Future<Map>.value({});
    } else if (call.method == 'transformGoodsToExternalData') {
      Map<String, dynamic> params = call.arguments.cast<String, dynamic>();
      try {
        Map<String, dynamic> externalData = TemplateUtils.getExternalDataWithGoods(params["goodsJson"]);
        return externalData;
      } on PlatformException catch (e) {
        Log.d(e.toString());
        return {};
      }
    } else if (call.method == 'isShowRfid') {
      return HardWareManager.instance().isHardwareSupportRecordRfid();
    } else if (call.method == 'loginScanCode') {
      String loginCode = call.arguments['loginCode'];
      String client = call.arguments['client'];
      Completer<Map> result = Completer();
      ScanCodeLoginPresenter().scanCodeByClient(client, loginCode, (value) {
        result.complete(value);
      });
      return result.future;
    } else if (call.method == 'clearCache') {
      CacheHelper.getInstance().clearFontCache();
      CacheHelper.getInstance().clearMaterialCache();
      return Future<Map>.value({});
    } else if (call.method == 'uploadLogInfoInFlutter') {
      Map<String, dynamic> logInfo = Map<String, dynamic>.from(call.arguments);
      String topic = logInfo["topic"] ?? "";
      logInfo.remove("topic");
      logInfo.remove("stsPath");
      bool uploadSuccess = await uploadLogInstantTime(logInfo, topic: topic);
      return Future<Map>.value({"uploadLogSuccess": uploadSuccess});
    } else if (call.method == 'writeLogInfoToFile') {
      Map<String, dynamic> logInfo = Map<String, dynamic>.from(call.arguments);
      bool uploadSuccess = await writeLogToFile(logInfo);
      return Future<Map>.value({"writeLogInfoSuccess": uploadSuccess});
    } else if (call.method == 'uploadLogFileToSls') {
      Map<String, dynamic> logInfo = Map<String, dynamic>.from(call.arguments);
      String topic = logInfo["topic"] ?? "appLog";
      String localPath = logInfo["localPath"] ?? "";
      Map<dynamic, dynamic> result = await uploadLogFileToSls(topic: topic, localPath: localPath);
      return Future<Map>.value(result);
    } else if (call.method == 'printerFirmwareUpgrade') {
      Map<String, dynamic> upgradeInfo = Map<String, dynamic>.from(call.arguments);
      FirmwareUpgradeManager().printerFirmUpgrade(upgradeInfo);
    } else if (call.method == 'getShopProductInfo') {
      String oneCode = call.arguments['oneCode'];
      Completer<Map> result = Completer();
      ShopProductBusiness.getShopProductInfo(oneCode, (value) {
        result.complete(value.toJson());
      }, (errorCode, errorMsg) {
        result.complete({});
      });
      return result.future;
    } else if (call.method == 'getIMLink') {
      Completer<String> result = Completer();
      ToNativeMethodChannel().getImParams().then((value) {
        MeProfilePresenter.getImLink(value, (url) {
          result.complete(url);
        });
      });
      return result.future;
    } else if (call.method == 'getFlutterPage') {
      var routeName = CustomNavigation.getFlutterPage();
      return routeName;
    } else if (call.method == 'closeFlutterPage') {
      ///首次进入"我的模板"页面会有引导弹窗，需要pop2次
      if (BoostNavigator.instance.pageSize() > 1) {
        BoostNavigator.instance.pop();
      }
      CustomNavigation.pop();
    } else if (call.method == _getGoodLibFieldsInfoFromFlutter) {
      Completer<List<Map<String, dynamic>>> result = Completer();
      GoodFieldManager().getGoodFields().then((value) {
        try {
          List<GoodLibFieldInfo> fields = GoodFieldManager().sortGoodLibFields(datas: value);
          List<Map<String, dynamic>> fieldList =
              fields.where((element) => element.isDeleted != true).map((info) => info.toJson()).toList();
          result.complete(fieldList);
        } catch (e) {
          result.complete([]);
        }
      });
      return result.future;
    } else if (call.method == _getPrintAdInfo) {
      Map<String, dynamic>? data = await PrintAdManager.getPrintAdShowStatus();
      return Future<Map>.value(data);
    } else if (call.method == _hasExcelCache) {
      String excelHash = call.arguments['hash'];
      Completer<bool> result = Completer();
      NiimbotDataSourceUtils.buildLocalDataSourcePath(excelHash).then((filePath) {
        final file = File(filePath);
        if (!file.existsSync()) {
          result.complete(false);
        } else {
          result.complete(true);
        }
      });
      return result.future;
    } else if (call.method == _clearAllAdData) {
      bool result = await AdDbUtils.clearAllAdData();
      return result;
    } else if (call.method == _saveAdToDatabase) {
      List<dynamic>? adData;
      if (call.arguments != null && call.arguments is String) {
        adData = jsonDecode(call.arguments);
      } else {
        adData = call.arguments;
      }
      var data = adData?.map((e) => Map<String, dynamic>.from(e)).toList();
      bool result = await AdDbUtils.insertAdData(data ?? []);
      return result;
    } else if (call.method == _getAdFromDatabase) {
      String languageCode = call.arguments?['languageCode'] ?? "";
      List<Map> result = await AdDbUtils.getActiveAdDataMap(languageCode);
      return result;
    } else if (call.method == _refreshAdToDatabase) {
      try {
        dynamic adData = call.arguments;
        var data = Map<String, dynamic>.from(adData);
        if (data.isNotEmpty) {
          bool result = await AdDbUtils.refreshADModel(data: data);
          return result;
        }
      } catch (e, s) {
        debugPrint('------$e-------');
      }
    } else if (call.method == _clearAllBannerData) {
      bool result = await BannerDbUtils.clearAllBannerData();
      return result;
    } else if (call.method == _saveBannerToDatabase) {
      List<dynamic>? bannerData;
      if (call.arguments != null && call.arguments is String) {
        String json = call.arguments;
        if (json.isNotEmpty) {
          bannerData = jsonDecode(json);
        }
      } else {
        bannerData = call.arguments;
      }
      var data = bannerData?.map((e) => Map<String, dynamic>.from(e)).toList();
      bool result = await BannerDbUtils.refreshBannerData(data ?? []);
      return result;
    } else if (call.method == _getBannerFromDatabase) {
      List<Map> result = await BannerDbUtils.getBannerDataMap();
      return result;
    } else if (call.method == _refreshBannerToDatabase) {
      try {
        dynamic bannerData = call.arguments;
        var data = Map<String, dynamic>.from(bannerData);
        if (data.isNotEmpty) {
          bool result = await BannerDbUtils.refreshBannerModel(data: data);
          return result;
        }
      } catch (e, s) {
        debugPrint('------$e-------');
      }
    } else if (call.method == _getPrintDataLogFromDatabase) {
      var result = await PrintLogBusiness().getPrintDataLogs();
      return result;
    } else if (call.method == _savePrintDataLogToDatabase) {
      Map<String, dynamic> dataLog;
      if (call.arguments != null && call.arguments is String) {
        dataLog = Map<String, dynamic>.from(jsonDecode(call.arguments));
      } else {
        dataLog = Map<String, dynamic>.from(call.arguments);
      }
      bool result = await PrintLogBusiness().savePrintDataLog(dataLog);
      return result;
    } else if (call.method == _clearPrintDataLogDatabase) {
      bool result = await PrintLogBusiness().clearPrintDataLog();
      return result;
    } else if (call.method == _uploadPrintDataLog) {
      // bool isSuccess = await PrintLogBusiness().uploadPrintDataLog();
      return true;
    } else if (call.method == _savePrintContentLogToDatabase) {
      Map<String, dynamic> contentLog;
      if (call.arguments != null && call.arguments is String) {
        contentLog = Map<String, dynamic>.from(jsonDecode(call.arguments));
      } else {
        contentLog = Map<String, dynamic>.from(call.arguments);
      }
      bool result = await PrintContentLogDbUtils.savePrintContentLog(contentLog);
      return result;
    } else if (call.method == _filterPrintContentLogWithIDFromDatabase) {
      String uniqueValue = call.arguments;
      Map<String, dynamic>? result = await PrintContentLogDbUtils.filterPrintContentLog(uniqueValue: uniqueValue);
      return result;
    } else if (call.method == _filterUNUploadingPrintContentLogFromDatabase) {
      List<Map<String, dynamic>>? result = await PrintContentLogDbUtils.filterUNUploadingPrintContentLog();
      return result;
    } else if (call.method == _refreshPrintContentLogToDatabase) {
      try {
        dynamic printContentLog = call.arguments;
        var data = Map<String, dynamic>.from(printContentLog);
        if (data.isNotEmpty) {
          bool result = await PrintContentLogDbUtils.refreshPrintContentLogModel(data: data);
          return result;
        }
      } catch (e, s) {
        debugPrint('------$e-------');
      }
    } else if (call.method == _getAllPrintContentLogFromDatabase) {
      List<Map> result = await PrintContentLogDbUtils.getAllPrintContentLogMap();
      List<Map> data = [];
      result.forEach((map) {
        if (map.containsKey("uniqueValue") && map["uniqueValue"] != null) {
          String uniqueValue = map["uniqueValue"];
          if (uniqueValue.isNotEmpty) {
            data.add(map);
          }
        }
      });
      return data;
    } else if (call.method == _getPrintContentLogFromDatabase) {
      final page = call.arguments?['page'] ?? 0;
      final pageSize = call.arguments?['pageSize'] ?? 10;
      List<Map> result = await PrintContentLogDbUtils.getPrintContentLogMap(page, pageSize);
      return result;
    } else if (call.method == _deleteUploadSuccessPrintContentLog) {
      String uniqueValue = call.arguments;
      bool result = await PrintContentLogDbUtils.deletePrintContentLogByUniqueValue(uniqueValue);
      return result;
    } else if (call.method == _deleteUploadSuccessPrintContentLogNew) {
      int id = call.arguments;
      bool result = await PrintContentLogDbUtils.deletePrintContentLogById(id);
      return result;
    } else if (call.method == _savePrintHistoryToDatabase) {
      Map<String, dynamic> printHistory;
      if (call.arguments != null && call.arguments is String) {
        printHistory = Map<String, dynamic>.from(jsonDecode(call.arguments));
      } else {
        printHistory = Map<String, dynamic>.from(call.arguments);
      }
      bool result = await PrintHistoryBusiness().savePrintHistory(printHistory);
      return result;
    } else if (call.method == _filterThumbnailNotNUllPrintHistoryFromDatabase) {
      List<Map<String, dynamic>>? result = await PrintHistoryDbUtils.filterThumbnailNotNUllPrintHistory();
      return result;
    } else if (call.method == _filterThumbnailNUllPrintHistoryFromDatabase) {
      List<Map<String, dynamic>>? result = await PrintHistoryDbUtils.filterThumbnailNUllPrintHistory();
      return result;
    } else if (call.method == _refreshPrintHistoryToDatabase) {
      try {
        dynamic printHistory = call.arguments;
        var data = Map<String, dynamic>.from(printHistory);
        if (data.isNotEmpty) {
          bool result = await PrintHistoryDbUtils.refreshPrintHistoryModel(data: data);
          return result;
        }
      } catch (e, s) {
        debugPrint('------$e-------');
      }
    } else if (call.method == _getAllPrintHistoryFromDatabase) {
      List<Map> result = await PrintHistoryDbUtils.getAllPrintHistoryMap();
      return result;
    } else if (call.method == _getFontClassify) {
      bool isFromLocal = call.arguments['isFromLocal'] ?? false;
      List result = await FontManager.sharedInstance().getFontClassify(isFromLocal: isFromLocal);
      List fontCategaryInfo = result.map((element) => element.toJson()).toList();
      return fontCategaryInfo;
    } else if (call.method == _deleteUploadSuccessPrintHistory) {
      List<String> uniqueIds = List<String>.from(call.arguments);
      bool result = await PrintHistoryDbUtils.deletePrintHistoryByUniqueIds(uniqueIds);
      return result;
    } else if (call.method == "closeMessageList") {
      await BoostNavigator.instance.popUntil(route: "meProfile");
      await Future.delayed(Duration(milliseconds: 500));
      return true;
    } else if (call.method == _nativeNetworkChange) {
      int status = call.arguments;
      NiimbotNetworkManager().notifyStatus(NetworkTypeParser.fromIndex(status));
      return status;
    } else if (NetyConnectMethod.matchAny(call.method)) {
      return NetyConnectHelper.handleNetySearchConnect(call.method, call.arguments);
    } else if (call.method == _flutterOutService) {
      NiimbotNetworkManager().setStatus(NiimbotNetworkType.outOfService);
    } else if (call.method == _checkOfflineAlert) {
      return NiimbotNetworkManager().checkOfflinePeriod();
    } else if (call.method == _getReplaceTemplate) {
      try {
        debugPrint('------get typesetting template from lego -------');
        // 使用 TemplateData 的 fromJson 创建实例
        final TemplateData originTemplate = TemplateData.fromJson(jsonDecode(call.arguments['originTemplate'] ?? {}));
        final TemplateData label = TemplateData.fromJson(jsonDecode(call.arguments['label'] ?? {}));
        // 调用生成方法并直接返回结果
        final TemplateData result = await OcrUtils.generateRfidReplaceTemplate(originTemplate, label);
        return result.toJson();
      } catch (e) {
        // 错误处理，可以根据实际需求调整
        throw ArgumentError("Invalid arguments for $_getReplaceTemplate: ${e.toString()}");
      }
    } else if (call.method == _getAuthCode) {
      String result = await AuthLoginApi().getAuthCode();
      return result;
    } else if (call.method == _modifyMachineAlias) {
      String machineNo = call.arguments['machineNo'];
      String macNo = call.arguments['macNo'];
      String alias = call.arguments['alias'];
      String? result = await MachineAliasManager().modifyMachineAlias(machineNo, macNo, alias);
      if (result == null) {
        //错误提示为空表示修改设备名称成功
        ToNativeMethodChannel().sendTrackingToNative({"track": "click", "posCode": "010_440_439"});
        NiimbotEventBus.getDefault().post("modifyMachineAlias");
      }
      return result;
    } else if (call.method == _getAllMachineAliasList) {
      List<String> machineNos = List<String>.from(call.arguments);
      Map<String, String> data = await MachineAliasManager().getMachineAliasList(machineNos);
      return data;
    } else if (call.method == _checkC1TemplateShareDecodeStatus) {
      if (C1FileBusiness.instance.isEditStatus) {
        return false;
      }
      if (Application.isInLoginPage) {
        return false;
      }
      return true;
    } else if (call.method == 'setupDataMigration') {
      try {
        final startTime = DateTime.now();
        debugPrint('setupDataMigration--开始执行数据迁移: $startTime');
        NiimbotLogTool.writeLogToFile({"MigrationManager": 'setupDataMigration--开始执行数据迁移: $startTime'});
        await MigrationManager().setupDataMigration();
        // 数据迁移会由Native层在调用此方法前已经完成
        // 这里只返回成功结果
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);
        debugPrint('setupDataMigration--数据迁移完成: $endTime, 耗时: ${duration.inMilliseconds}毫秒');
        NiimbotLogTool.writeLogToFile(
            {"MigrationManager": 'setupDataMigration--数据迁移完成: $endTime, 耗时: ${duration.inMilliseconds}毫秒'});
        return true;
      } catch (e, stackTrace) {
        debugPrint('setupDataMigration--数据迁移失败: $e\n$stackTrace');
        NiimbotLogTool.writeLogToFile({"MigrationManager": 'setupDataMigration--数据迁移失败: $e\n$stackTrace'});
        return false;
      }
    }
    // return Future<Map>.value({});
  }

  // 静态、同步、私有访问点
  static ToNativeMethodChannel sharedInstance() {
    return _instance;
  }

//跳入原生界面
  Future<dynamic>? goToNativePage(Map<String, dynamic> map) async {
    try {
      return await platform.invokeMethod('goToNativePage', map);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  //向原生通知消息未读数变更事件
  void notifyUnreadMessageCount(int unreadCount) async {
    if (unreadCount < 0) {
      unreadCount = 0;
    }
    Log.d("调起消息未读数变更事件");
    return await platform.invokeMethod(_notifyUnreadMessageCount, {"unreadMessageCount": unreadCount});
  }

  //通知开关是否打开
  Future<bool> areNotificationsEnabled() async {
    Log.d("调起通知开关是否打开");
    return await platform.invokeMethod(_areNotificationEnabled, {});
  }

  //跳转到通知设置
  void jumpToNotificationSetting() {
    Log.d("跳转到通知设置");
    platform.invokeMethod(_jumpToNotificationSetting, {});
  }

  //调用原生跳转到消息详情
  void jumpToMessageDetail(String linkTitle, String linkUrl, int linkRouteType) async {
    if (linkUrl.isEmpty) {
      return;
    }
    String jumpUrl = linkUrl;
    if (linkUrl.startsWith("http")) {
      if (linkUrl.contains("?")) {
        jumpUrl = "$jumpUrl&source=1";
      } else {
        jumpUrl = "$jumpUrl?source=1";
      }
    }
    Log.d("调用原生跳转到消息详情");
    return await platform.invokeMethod(
        _jumpToMessageDetail, {"linkTitle": linkTitle, "linkUrl": jumpUrl, "linkRouteType": linkRouteType});
  }

  //调用原生跳转到消息详情
  void pushToRoute(dynamic arguments) async {
    return await platform.invokeMethod(_pushToRoute, arguments);
  }

//微信登录认证
  Future<Map> wechatLoginAuth(String wxAppKey) async {
    Log.d("调起微信登录认证");
    return await platform.invokeMethod(_wechatLoginAuth, {'wxAppKey': wxAppKey});
  }

//调用原生进行埋点
  sendTrackingToNative(Map<String, dynamic> map) async {
    try {
      addCommonTemplateId(map);
      await platform.invokeMethod(_sendTrackingToNative, map);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

//统一拼接模版ID和云模版id
  addCommonTemplateId(Map<String, dynamic> map) {
    String tempId = ExcelTransformManager.sharedInstance().templateData?.id ?? "";
    if (tempId.length > 10) {
      tempId = "";
    }
    String industryTempId = ExcelTransformManager.sharedInstance().templateData?.cloudTemplateId ?? "";
    dynamic posCode = map["posCode"] ?? "";
    if (posCode != null && posCode is String) {
      String code = map["posCode"];
      //判断是否是画板里面的操作行为
      if (code.startsWith("108_")) {
        if (map["ext"] is Map<String, dynamic>) {
          Map<String, dynamic> ext = {};
          ext.addAll(map["ext"]);
          //ext有值
          String? id = map["ext"]["temp_id"];
          if (id == null || id == "") {
            ext["temp_id"] = tempId;
          }
          String? industryId = map["ext"]["industry_temp_id"];
          if (industryId == null || industryId == "") {
            ext["industry_temp_id"] = industryTempId;
          }
          map["ext"] = ext;
          //ext为空
        } else {
          map["ext"] = {};
          map["ext"]["temp_id"] = tempId;
          map["ext"]["industry_temp_id"] = industryTempId;
        }
      }
    }
  }

//获取用户免费打印次数
  static Future<int> getUnVipBatchPrintCount() async {
    try {
      return await platform.invokeMethod(_unVipBatchPrintCount);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return Future.value(0);
    }
  }

//1 正常进入 2 首次进入   3 我的设备进入
  toNativeSearchPage({int? style}) {
    Map<String, dynamic> pars = {'vcName': 'ToSearchPage', "style": style};
    ToNativeMethodChannel.sharedInstance().goToNativePage(pars);
  }

  toNativePermissionSettingPage() {
    PreferencesUtil.put(ConstantKey.Anti_permission_setting_key, true);
    Map<String, dynamic> pars = {'vcName': 'ToPermissionSettingPage'};
    ToNativeMethodChannel.sharedInstance().goToNativePage(pars);
  }

  setNativeToken(String token) {
    try {
      Map data = {"token": token};
      platform.invokeMethod(_setToken, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  setNativeMallToken(String mallToken) {
    try {
      Map data = {"mallToken": mallToken};
      platform.invokeMethod(_setMallToken, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  setNativeUserInfo(String userJson) {
    try {
      Map data = {"userJson": userJson};
      platform.invokeMethod(_setUser, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  reportLoginSuccess(String? resultJson) {
    try {
      Log.d("flutter登录成功回调$resultJson");
      Map data = {"resultJson": resultJson};
      saveUnionId(resultJson);
      platform.invokeMethod(_reportLoginSuccess, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  // 取消登录
  cancelLogin() {
    try {
      Log.d("flutter取消登录");
      platform.invokeMethod(_cancelLogin);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  saveUnionId(String? resultJson) {
    try {
      if (null != resultJson && resultJson.isNotEmpty) {
        Map map = json.decode(resultJson);
        var unionId = map['loginData']['uid'];
        Application.sp.setString(ConstantKey.User_UnionId, unionId);
      }
    } catch (e) {
      Log.d('JsonUtil convert error, Exception：${e.toString()}');
    }
  }

  updateUserInfo(String? resultJson) {
    try {
      Log.e("call native to updateUser: $resultJson");
      Map data = {"userInfo": resultJson};
      platform.invokeMethod(_updateUserInfo, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///电子价签自动回连
  getETagConnect() {
    try {
      platform.invokeMethod(_getETagConnect);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///无UI打印完成
  setUnNeedUIPrintComplete(String status, String uniAppId, String taskId, String message) {
    try {
      platform.invokeMethod(
          _unUIPrintComplate, {'status': status, 'uniAppId': uniAppId, 'taskId': taskId, 'message': message});
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///商品数据混入
  goodsMixList(Map<String, dynamic> data) async {
    try {
      var resList = await platform.invokeMethod(_goodsMixList, data);
      return resList;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///商品数据批量混入
  goodsMixListAll(Map<String, dynamic> data) async {
    try {
      var resList = await platform.invokeMethod(_goodsMixListAll, data);
      return resList;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///商品数据混入
  getPreviewPicture(Map<String, dynamic> data) async {
    try {
      var value = await platform.invokeMethod(_getPreviewPicture, data);
      return value;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///设置原生当前连接设备
  setConnectDevice(Map<String, dynamic> data) async {
    try {
      var dataJson = jsonEncode(data);
      var value = await platform.invokeMethod(_setConnectDevice, dataJson);
      return value;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  //设置SDK Rfid信息
  setSDKRfidData(String data) async {
    try {
      var value = await platform.invokeMethod(_setSDKRfidData, data);
      return value;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///本轮数据写入完成
  endSendPictures() async {
    try {
      var resList = await platform.invokeMethod(_endSendPictures);
      return resList;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///电子价签状态监听
  eTagStatusListener(bool isOpen) async {
    try {
      platform.invokeMethod(_eTagStatusListener, isOpen);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///断开价签机连接
  etagDisConnect() async {
    try {
      platform.invokeMethod(_etagDisConnect);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///重置价签机状态
  resetEtagStatus() async {
    try {
      return await platform.invokeMethod(_resetEtagStatus);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  bindMainAccountSuccess() {
    try {
      platform.invokeMethod(_bindMainAccountSuccess);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  simOneKeyLogin() {
    try {
      platform.invokeMethod(_SIMAccountOneKeyLogin);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  logout(int type) {
    try {
      //1.退出登录  2.注销登录
      Map data = {"outType": type};
      platform.invokeMethod(_logout, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  getAgent() async {
    try {
      Map data = await platform.invokeMethod(_getAgent);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  getAnonymousId() async {
    try {
      var data = await platform.invokeMethod(_getAnonymousId);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  getAppConfig() async {
    try {
      Map data = await platform.invokeMethod(_getAppConfig);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  getAppInfo() async {
    try {
      Map data = await platform.invokeMethod(_getAppInfo);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  Future<Map> getImParams() async {
    try {
      Map data = await platform.invokeMethod(_getImParams);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  Future<String> getDeviceId() async {
    try {
      String data = await platform.invokeMethod(_getDeviceId);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return "";
    }
  }

  Future<dynamic> getSPData(String key) async {
    try {
      var data = await platform.invokeMethod(_getSPData, key);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return "";
    }
  }

  Future<dynamic> getIntSPData(String key) async {
    try {
      var data = await platform.invokeMethod(_getIntSPData, key);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return "";
    }
  }

  Future<dynamic> printCommodity() async {
    try {
      var data = await platform.invokeMethod(_printCommodity);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return "";
    }
  }

  Future<Map> getPasteboardSetting() async {
    try {
      return await platform.invokeMethod(_getPasteboardSetting);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  setPasteboardSetting(String pasteboardSwitchValue) async {
    try {
      await platform.invokeMethod(_setPasteboardSetting, {"value": pasteboardSwitchValue});
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

/*请求定位权限: 0:拒绝, 1:允许*/
  Future<String?> requestLocationStatus({bool showAlert = false, bool shouldRequest = false}) async {
    if (Platform.isIOS) {
      try {
        return await platform.invokeMethod(
            _requestLocationAuth, {"showAlert": showAlert ? 1 : 0, 'shouldRequest': shouldRequest ? 1 : 0});
      } on PlatformException catch (e) {
        Log.d(e.toString());
        return Future.value('0');
      }
    } else {
      return null;
    }
  }

  /*请求防丢器定位信息*/
  Future<Map> requestAntiLocationDetail({String? serialNo}) async {
    try {
      return await platform.invokeMethod(_requestAntiLocationDetail, {'serialNo': serialNo ?? ''});
    } on PlatformException catch (e) {
      Log.d(e.toString());
      Map map = {"address": '', "longitude": 0, "latitude": 0};
      return Future.value(map);
    }
  }

  /*显示防丢器地图*/
  showAntiMap(Map params) async {
    try {
      await platform.invokeMethod(_showAntiMap, params);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*清除本地缓存的防丢器位置信息,如果传空,那么全部清空*/
  clearLocalAntiLocation({String? serialNo}) async {
    try {
      Map params = {"serialNo": serialNo ?? ''};
      await platform.invokeMethod(_clearAntiMap, params);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  //获取原生语言资源
  getLanguageDetail() async {
    try {
      Map data = await platform.invokeMethod(_getLanguageDetail);
      return data;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

// //获取原生语言资源
//   @Deprecated('登录插件语言包已和原生语言包融合，所以登录插件直接使用原生语言包')
//   getLoginPluginLanguageDetail() async {
//     try {
//       Map data = await platform.invokeMethod(_getLoginPluginLanguageDetail);
//       return data;
//     } on PlatformException catch (e) {
//       Log.d(e.toString());
//     }
//   }

  //获取原生语言类型
  getAppCurrentLanguageType() async {
    try {
      String appLanguageType = await platform.invokeMethod(_getAppCurrentLanguageType);
      return appLanguageType;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///断开连接
  static disConnect() async {
    try {
      await platform.invokeMethod(_disConnect);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///更改机器类型
  static setNativeDeviceType(String value) {
    try {
      Map data = {"currentDevice": value, "searchConnect": false, "hasToken": Application.hasToken()};
      platform.invokeMethod(_setDeviceItem, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  } //

  static selectDevice(String value) {
    try {
      Map data = {"currentDevice": value, "searchConnect": true, "hasToken": Application.hasToken()};
      platform.invokeMethod(_setDeviceItem, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///toNative 设备列表
  static setNativeDeviceList(String value) {
    try {
      Map data = {"deviceList": value};
      platform.invokeMethod(_setDeviceList, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///toNative 设备列表
  static setNativeDeviceSeriesList(List<Map> value) {
    try {
      platform.invokeMethod(_setDeviceSeriesList, value);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///toNative 设备列表
  static setSelectedDeviceSeries({Map? value}) {
    if (value != null) {
      try {
        platform.invokeMethod(_setSelectedDeviceSeries, value);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    } else {
      try {
        platform.invokeMethod(_setSelectedDeviceSeries);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  //从原生获取打印机系列资源
  Future<List> getDeviceSeriesList() async {
    try {
      return await platform.invokeMethod(_getDeviceSeriesData);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return [];
    }
  }

  ///toNative 当前系统语言
  static setNativeLanguage(List<String> value) {
    try {
      Map data = {"language": value};
      platform.invokeMethod(_setLanguage, data);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///toNative 当前系统语言
  setFlutterVCCanSideslip(bool value) {
    try {
      platform.invokeMethod(_setFlutterVCCanSideslip, value);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  Future<int> authorizationStatus() async {
    try {
      //获取相机与相册权限 1 没有权限  2 有权限
      return await platform.invokeMethod(_authorization_Status);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return 1;
    }
  }

  static cameraAuthorizationStatus({bool showAlert = true}) async {
    try {
      //获取相机与相册权限 1 没有权限  2 有权限
      int state = await platform.invokeMethod(_authorization_Status, showAlert);
      return state;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  static notifyLoginStatus() {
    try {
      platform.invokeMethod(_notifyLoginStatus);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  static setShopInfoNotifyNumber(String notifyNumbers) {
    try {
      platform.invokeMethod(_shopInfoNotifyNumbers, notifyNumbers);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  commandAntiLost(String identifier, int type) async {
    Map params = new Map();
    params["fucName"] = "setAntiLost";
    params["type"] = type;
    params["identifier"] = identifier;
    Map result = await ToNativeMethodChannel().antiLost(params);
    return result;
  }

  antiLost(Map data) async {
    try {
      var result = await platform.invokeMethod(_antiLost, data);
      return result;
    } catch (e) {
      Log.d(e.toString());
    }
  }

  /// 获取当前打印机连接信息
  /// bluetoothCode 序列号
  /// hardwareVersion 硬件版本号
  /// firmwareVersion 固件版本号
  Future queryPrinterConnectedInfo() async {
    try {
      var result = await platform.invokeMethod(_queryPrinterConnectedInfo, {});
      return result;
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static Future<Map?> wxPay(Map data) async {
    try {
      var result = await platform.invokeMethod(_wxPay, data);
      return result;
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  static Future<Map?> setRfidColor(Map data) async {
    try {
      var result = await platform.invokeMethod(_setRfidColor, data);
      return result;
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  static Future<bool> getDeviceVipState() async {
    var result = await platform.invokeMethod(_getDeviceVipState);
    return result;
  }

  static Future<bool> getDeviceIsSupportAnonymity() async {
    var result = await platform.invokeMethod(_getDeviceIsSupportAnonymity);
    return result;
  }

  Future<Map?> checkAssetImages(List<String> identifiers) async {
    try {
      Map map = {"identifiers": identifiers};
      var result = await platform.invokeMethod(_checkAssetImages, map);
      return result;
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  Future<Map?> shareDecode(String shareCode) async {
    try {
      Map map = {"shareCode": shareCode};
      var result = await platform.invokeMethod(_shareDecode, map);
      return result;
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  goToShareTemplatePage() async {
    try {
      await platform.invokeMethod(_goToShareTemplate);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  Future<Map?> getLocaleLanguage() async {
    try {
      return await platform.invokeMethod(_getLocaleLanguage);
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  Future<Map?> checkLocaleLanguage() async {
    try {
      return await platform.invokeMethod(_checkLocaleLanguage);
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  ///source: 0-淘宝，1-天猫，2-京东
  Future<String?> checkAppsIsExist(int source) async {
    try {
      Map params = {"source": source};
      return await platform.invokeMethod(_checkAppsIsExist, params);
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  ///source: 0-淘宝，1-京东，2-天猫
  ///url：商品链接地址
  toApps(Map params) {
    try {
      platform.invokeMethod(_toApps, params);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  notifyAgreePrivacy() {
    if (Platform.isAndroid) {
      try {
        platform.invokeMethod(_notifyAgreePrivacy);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

/*flutter首页是否准备完毕*/
  homepageReady() {
    try {
      platform.invokeMethod(_notifyHomepageReady, {"hasToken": Application.hasToken()});
    } catch (e) {
      Log.d(e.toString());
    }
  }

  ///从原生获取屏幕ppi
  Future<double> getPPIFromNative() async {
    try {
      return await platform.invokeMethod(_getPPIFromNative);
    } catch (e) {
      Log.d(e.toString());
      return 0;
    }
  }

  ///从原生获取屏幕倍率
  Future<double> getScaleFromNative() async {
    try {
      return await platform.invokeMethod(_getScaleFromNative);
    } catch (e) {
      Log.d(e.toString());
      return 0;
    }
  }

  notificationPermissionSetting() {
    try {
      platform.invokeMethod(_notificationPermissionSetting);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  requestInnerAppReview() {
    if (Platform.isIOS) {
      try {
        platform.invokeMethod(_requestInnerAppReview);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

/*
* 推送权限状态:0-未决定,1-拒绝,2-允许*/
  // ignore: missing_return
  Future<String> getAPNSAuthStatus() async {
    try {
      return await platform.invokeMethod(_getAPNSAuthStatus);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return '1';
    }
  }

  connectSearchDevice(String name) {
    try {
      platform.invokeMethod(_connectSearchDevice, {"name": name});
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  cancelSearchConnectDevice() {
    try {
      platform.invokeMethod(_cancelSearchConnectDevice);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

/*ios 日志代理*/
  flutterLog(String msg) {
    if (Platform.isIOS) {
      try {
        platform.invokeMethod(_flutterLog, 'flutter log: $msg');
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  closeNativeToast() {
    if (Platform.isIOS) {
      try {
        platform.invokeMethod(_closeNativeToast);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  Future? getSystemLanguage() {
    if (Platform.isIOS) {
      try {
        return platform.invokeMethod(_getSystemLanguage);
      } on PlatformException catch (e) {
        Log.d(e.toString());
        return null;
      }
    }
    return null;
  }

  static Future? meProfile(Map parms) {
    try {
      return platform.invokeMethod(_meProfileEvent, parms);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  Future? syncIntlToNaive(String intls) {
    try {
      return platform.invokeMethod(_syncIntlToNaive, intls);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  Future<int?> clearPassiveDisconnectedAntiLostList() async {
    try {
      return platform.invokeMethod(_clearPassiveDisconnectedAntiLostList);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  Future<bool> checkSIMCardInfo() async {
    bool checkSIMCardInfo = await platform.invokeMethod(_checkSIMCardInfo);
    return checkSIMCardInfo;
  }

  Future<int?> queryPassiveDisconnectedAntiLosts() async {
    try {
      return platform.invokeMethod(_queryPassiveDisconnectedAntiLosts);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  syncLanguageToNative(String languageCode, String countryCode, bool isFirst) async {
    try {
      return platform.invokeMethod(
          _syncLanguageToNative, {"languageCode": languageCode, "countryCode": countryCode, "isFirst": isFirst});
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

/*
* filePath: 文件本地路径
* type: 资源类型, 按照存放目录传,如下
    JCOSSTypeJCTag = 0,//除了同步记录和同步记录元数据之外的的资源,例如防丢器的场景图等
    JCOSSTypeJCTagImprint,//当丢弃同步记录
    JCOSSTypeJCTagMetadata,//防丢器同步同步记录中包含的图片信息
    JCOSSTypeImprint,//印迹
    JCOSSTypeImprintTemplate,//印迹模板中包含的模板
    JCOSSTypeCloudliving,//云生活
    JCOSSTypeProfile,//用户信息相关
    JCOSSTypeStorageSpace,//收纳空间
    JCOSSTypeStorageItem//收纳物品
* 返回值为
*   {
      "status":xx,//bool值:是否成功
       "ossPath":xx//字符串, 上传成功后有值, 值为资源存放的oss路径
     }
* */
  Future<Map?> uploadImage(String filePath, int type) async {
    try {
      return await platform.invokeMethod(_uploadFileToOSS, {"filePath": filePath, "type": type});
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /*
  * ossProcess:oss处理参数
  * 返回值为文件存储的本地路径
  * */
  Future<String?> downImage(String filePath, {String? ossProcess}) async {
    try {
      return await platform.invokeMethod(_downFilefromOSS, {"filePath": filePath, "ossProcess": ossProcess ?? ''});
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /*
   * 检查手机蓝牙是否打开，ture-手机蓝牙为开启状态，false-手机蓝牙未开启
   */
  Future<bool?> getBluetoothStatus() async {
    try {
      return await platform.invokeMethod(_getBluetoothStatus);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /*
   * 跳转手机设置界面，以开启蓝牙（只针对android）
   */
  openBluetooth() {
    try {
      platform.invokeMethod(_openBluetooth);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 跳转云打印首页
   */
  toNiimbotHomePage() {
    try {
      platform.invokeMethod(_toNiimbotHomePage);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 检查是否已申请位置权限（只针对android）
   * 返回值
   {
      "locationPermissionType": xx  //1-已申请，2-未申请，3-未申请并且永久拒绝
   }
   */
  Future<Map?> checkLocationPermission() async {
    try {
      return await platform.invokeMethod(_checkLocationPermission);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /*
   * 申请位置权限（只针对android）
   */
  requestLocationPermission() {
    try {
      platform.invokeMethod(_requestLocationPermission);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 跳转设置-应用-臣小印-权限界面，以开启位置权限（只针对android）
   */
  setLocationPermission() {
    try {
      platform.invokeMethod(_setLocationPermission);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 搜索设备
   */
  searchDevice() {
    try {
      platform.invokeMethod(_searchDevice);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 停止搜索设备
   */
  Future stopSearchDevice() async {
    try {
      await platform.invokeMethod(_stopSearchDevice);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 连接设备
   */
  connectDevice(String deviceName) {
    try {
      platform.invokeMethod(_connectDevice, {'deviceName': deviceName});
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 断开连接
   */
  disconnectDevice() {
    try {
      platform.invokeMethod(_disconnectDevice);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 停止搜索，并清空已搜索出来的设备
   */
  clearSearchDevice() {
    try {
      platform.invokeMethod(_clearSearchDevice);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 监听定位服务开关状态
   */
  registerLocationServiceStatus() {
    if (Platform.isAndroid) {
      try {
        platform.invokeMethod(_registerLocationServiceStatus);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  /*
   * 注销监听定位服务开关状态
   */
  unRegisterLocationServiceStatus() {
    if (Platform.isAndroid) {
      try {
        platform.invokeMethod(_unRegisterLocationServiceStatus);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  /*
   * 注销监听定位服务开关状态
   */
  isIphoneX() async {
    if (Platform.isIOS) {
      try {
        int res = await platform.invokeMethod(_isIphoneX);
        Application.isIphoneX = res == 1;
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  /*
   * 跳转到google商店
   */
  toGoogleMarket() {
    if (Platform.isAndroid) {
      try {
        platform.invokeMethod(_toGoogleMarket);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
  }

  popFromFlutterLeft() {
    try {
      platform.invokeMethod(_popFromFlutterLeft);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  popFromFlutterTop() {
    try {
      platform.invokeMethod(_popFromFlutterTop);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  toConnectBluetooth() {
    try {
      platform.invokeMethod(_toConnectBluetooth);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  /*
   * 获取打印机电量
   */
  Future<int?> getPrinterElectricalLevel() async {
    try {
      return await platform.invokeMethod(_getPrinterElectricalLevel);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /*
   * 判断当前是否为生产环境
   */
  Future<bool?> isProduction() async {
    if (Platform.isAndroid) {
      try {
        return await platform.invokeMethod(_isProduction);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    } else {
      return null;
    }
    return null;
  }

  /*
   * 获取时区
   */
  Future<int?> getTimeZone() async {
    if (Platform.isAndroid) {
      try {
        return await platform.invokeMethod(_getTimeZone);
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    } else {
      return null;
    }
    return null;
  }

  ///注销成功
  userOffSuccess() {
    try {
      platform.invokeMethod(_userOffSuccess);
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

/*获取已经安装的社交登录app
* 获取的结果格式为:
* {
                "name":"qq登录",                //社交登录的名称,根据多语言配置获取的
                "scheme":["mqq://"],            //安卓可以根据实际情况返回, 也可不返回,暂时没用
                "type":"qq"                     //登录的类型:qq,weixin, weibo,linkedin, twitter,line,fb,google,apple, ...
            }
            * */
  Future<List<Map<String, dynamic>>?> appForLoginThatInstalledInMyPhone() async {
    if (Platform.isIOS) {
      try {
        String res = await platform.invokeMethod(_appForLoginThatInstalledInMyPhone);
        List<Map<String, dynamic>> v = jsonDecode(res).cast<Map<String, dynamic>>();
        return v;
        return null;
      } on PlatformException catch (e) {
        Log.d(e.toString());
      }
    }
    return null;
  }

  Future<List> getSupportSocialList() async {
    List supportSocialList = [];
    supportSocialList = await platform.invokeMethod(_getSupportSocialList);
    return supportSocialList;
  }

  SIMAccountOneKeyConfig() {
    Map map = {};
    map["remind"] = loginIntl("login0110");
    map["confirmText"] = loginIntl("login0061");
    map["cancelText"] = loginIntl("login0062");
    map["notRegisterTips"] = loginIntl("login0090");
    map["alreadyRegisterTips"] = loginIntl("login0091");
    map["authFailedTips"] = loginIntl("login0092");
    map["loginNavText"] = loginIntl("login0014");
    map["loginLogBtn"] = loginIntl("login0093");
    map["loginSwitch"] = loginIntl("login0094");
    map["loginAgreePrivacyToast"] = loginIntl("login0095");
    map["registerNavText"] = loginIntl("login0038");
    map["registerLogBtn"] = loginIntl("login0096");
    map["registerSwitch"] = loginIntl("login0097");
    map["registerAgreePrivacyToast"] = loginIntl("login0098");
    map["useAgreementTitle"] = loginIntl("login0009");
    map["userPrivacyTitle"] = loginIntl("login0003");
    map["bindingNavText"] = loginIntl("login0079");
    map["bindingLogBtn"] = loginIntl("login0107");
    map["bindingSwitch"] = loginIntl("login0108");
    map["bindingAgreePrivacyToast"] = loginIntl("login0121");
    map["phoneBindedMessage"] = loginIntl("login0109");
    map["agreementUrl"] = "https://jc-common-data-oss.niimbot.com/chenyin/agreement/dist/index.html#/license";
    map["privacyUrl"] = "https://jc-common-data-oss.niimbot.com/chenyin/agreement/dist/index.html#/privacy";
    platform.invokeMethod(_SIMAccountOneKeyConfig, map);
  }

  Future<Map> SIMAccountOneKeyLogin(String oneKeyAuthSecret) async {
    SIMAccountOneKeyConfig();
    Map result = await platform.invokeMethod(_SIMAccountOneKeyLogin, {"oneKeyAuthSecret": oneKeyAuthSecret});
    return result;
  }

  Future<Map> SIMAccountOneKeyRegister(String oneKeyAuthSecret) async {
    SIMAccountOneKeyConfig();
    Map result = await platform.invokeMethod(_SIMAccountOneKeyRegister, {"oneKeyAuthSecret": oneKeyAuthSecret});
    return result;
  }

  Future<Map> SIMAccountOneKeyBinding(String oneKeyAuthSecret) async {
    SIMAccountOneKeyConfig();
    Map result = await platform.invokeMethod(_SIMAccountOneKeyBinding, {"oneKeyAuthSecret": oneKeyAuthSecret});
    return result;
  }

  Future<bool> checkIfInstallCloudPrint() async {
    bool install = await platform.invokeMethod(_checkIfInstallNIIM);
    return install;
  }

  Future<Map<String, dynamic>?> getUserInfoFromNative({String? accessToken, String? tokenType}) async {
    Map? result = await platform.invokeMethod(_getUserInfo, {"accessToken": accessToken, "tokenType": tokenType});
    if (result != null) {
      return Future.value(Map<String, dynamic>.from(result));
    } else {
      return Future.value(null);
    }
  }

  Future<void> getUserInfoMallToken({String? accessToken, String? tokenType}) async {
    await platform.invokeMethod(_getUserMallToken, {"accessToken": accessToken, "tokenType": tokenType});
  }

  Future downloadTemplateDetails(List<String> list) async {
    await platform.invokeMethod(_downloadTemplateDetail, {"templateids": list});
  }

  Future<bool> downloadTemplateFonts(String templateJsonStr) async {
    return await platform.invokeMethod(_downloadTemplateFonts, {"template": templateJsonStr});
  }

  Future toMarketRating(int userPrintCount) async {
    await platform.invokeMethod(_toMarketRating, {"userPrintCount": userPrintCount});
  }

  Future<int> unUIPrintErrorEventCallback(Map<String, dynamic> errorInfo) async {
    try {
      int operateType = await platform.invokeMethod(_unUIPrintErrorEvent, errorInfo);
      return Future.value(operateType);
    } catch (e) {
      return Future.value(0);
    }
  }

  refreshRFIDInfoInService(Map<String, dynamic> serviceRFIDInfo) {
    platform.invokeMethod(_refreshServerRFIDInfo, serviceRFIDInfo);
  }

  saveLabelRecord(Map<String, dynamic> templateData) {
    platform.invokeMethod(_labelRecord, templateData);
  }

  Future removeTemplateDetails(List<String> list) async {
    await platform.invokeMethod(_removeTemplateDetail, {"templateids": list});
  }

  Future cancelDownloadTemplateDetails() async {
    await platform.invokeMethod(_cancelDownloadTemplateDetail);
  }

  getUserInfoFromLocalCache() async {
    Map? result = await platform.invokeMethod(_getLocalUserInfoCache);
    return result;
  }

  getAppEnv() async {
    try {
      /**
       * 获取app接口环境
       * dev: 2
       * test: 1
       * production: 0
       */
      int env = await platform.invokeMethod(_getAppEnv);

      Log.d("getAppEnv == env -- ${env}");
      if (env == 2) {
        return AppEnv.dev;
      } else if (env == 1) {
        return AppEnv.test;
      }
      return AppEnv.production;
    } on PlatformException catch (e) {
      Log.d(e.toString());
    }
  }

  ///url：商品链接地址
  setLogProxy({Map<String, String>? params}) async {
    try {
      if (params != null && params['result']!.isNotEmpty) {
      } else {
        String? res = await Application.sp.getString('logproxy');
        params = {"result": res ?? ""};
      }
      platform.invokeMethod(_setLogProxy, params);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  /// 获取 vip 状态
  Future<int?> getVipStatus() async {
    try {
      return await platform.invokeMethod('getVipStatus');
    } catch (e) {
      Log.d(e.toString());
      return 0;
    }
  }

  Future<bool> getNfcConnectState() async {
    try {
      return await platform.invokeMethod('getNfcConnectState');
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  /// 获取最近使用的标签纸记录, 可传入机型ID进行筛选
  Future<List<LabelUsageRecord>?> getUseRecentLabel({num limit = 1, List<String>? machineIds}) async {
    try {
      Map<String, dynamic> arguments = {'limit': limit};
      arguments.addAllIf(machineIds != null, {'machineIds': machineIds ?? ''});
      var resList = await platform.invokeListMethod(_getUseRecentLabel, arguments);
      resList = resList?.map((e) {
        var json = Map<String, dynamic>.from(e);
        return LabelUsageRecord.fromJson(json);
      }).toList();
      return resList as List<LabelUsageRecord>;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取模板详情
  Future<String?> getTemplateDetailFromNative(String templateId,
      {bool isPersonalTemplate = false,
      bool needLoadFonts = false,
      bool isShare = false,
      int templateClass = 1,
      bool isBatchPrintTemplate = false,
      bool saveRecord = true}) async {
    try {
      var res = await platform.invokeMethod(_getTemplateDetailForNative, {
        'templateId': templateId,
        'isPersonalTemplate': isPersonalTemplate,
        'needLoadFonts': needLoadFonts,
        'isShare': isShare,
        'templateClass': templateClass,
        'saveRecord': saveRecord,
        'isBatchPrintTemplate': isBatchPrintTemplate
      });
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取线缆详情
  Future<String?> getCableDetailFromNative(
    Map map,
    String labelId,
  ) async {
    String barCode = "";
    map.forEach((key, value) {
      if (value != null && value != "") {
        barCode = value;
        return;
      }
    });

    try {
      var res = await platform.invokeMethod(_getCableDetailForNative, {'barCode': barCode, "labelId": labelId});
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取打印机连接状态
  Future<Map?> getPrinterConnectState() async {
    try {
      var res = await platform.invokeMethod('getPrinterConnectState');
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取打印设置设备信息
  Future<List?> getPrintSettingMsg(materialModelSn) async {
    try {
      var res = await platform.invokeMethod('getPrintSettingMsg', materialModelSn);
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取当前打印内标签纸支持颜色
  getCurrentPaperSupportColors() async {
    try {
      var res = await platform.invokeMethod('getCurrentPaperSupportColors');
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 获取当前打印机内标签纸信息
  Future<Map?> getPrinterLabelData() async {
    try {
      var res = await platform.invokeMethod('getPrinterLabelData');
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 检查批量打印模板信息是否下载完整
  Future<int> checkBatchPrintSources(List<Map<String, dynamic>> batchIds) async {
    try {
      int res = await platform.invokeMethod('checkBatchPrintSources', jsonEncode(batchIds));
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return 0;
  }

  writeScreenData(Map<String, dynamic> map) async {
    try {
      return await platform.invokeMethod('writeScreenData', map);
    } catch (error) {
      Log.d(error.toString());
    }
  }

  ///获取NFC屏幕信息
  getNfcScreenData(Map<String, dynamic> map) async {
    try {
      return await platform.invokeMethod('getNfcScreenData', map);
    } catch (error) {
      Log.d(error.toString());
    }
  }

  ///去电子价签页面
  toEtagPage() async {
    try {
      return await platform.invokeMethod('toEtagPage');
    } catch (error) {
      Log.d(error.toString());
    }
  }

  closeNfcTopActivity() async {
    try {
      return await platform.invokeMethod('closeNfcTopActivity');
    } catch (error) {
      Log.d(error.toString());
    }
  }

  /// 获取当前打印机内标签纸信息
  getCurrentSingleColorInfo() async {
    try {
      var res = await platform.invokeMethod('getCurrentSingleColorInfo');
      return res;
    } catch (error) {
      Log.d(error.toString());
    }
    return null;
  }

  /// 从原生获取打印机系列下的首个打印机模型
  Future<HardwareModel?> getDeviceSeries({required String seriesId}) async {
    try {
      var json = await platform.invokeMethod(_getDeviceSeries, {'series': seriesId});

      HardwareModel model = HardwareModel.fromJson(Map<String, dynamic>.from(json));
      return model;
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /// 关闭打印机连接
  Future<bool> closeHardWareConnected() async {
    return await platform.invokeMethod(_closeHardWareConnected) == 1 ? true : false;
  }

  /// 获取当前连接的打印机
  void getConnectDevice() {
    try {
      platform.invokeMethod('getConnectDevice');
    } catch (error) {
      Log.d(error.toString());
    }
  }

  /// 同步rfid模板替换标记
  Future<int> syncRfidReplaceTag(bool hasConfirmAutoRfid) async {
    return await platform.invokeMethod('syncReplaceRfidTag', {'hasConfirmAutoRfid': hasConfirmAutoRfid});
  }

  /// 同步线缆入口标记
  Future<int> setSPData(Map entry) async {
    return await platform.invokeMethod('setSPData', entry);
  }

  /// 同步打印偏移量
  Future<int> setDeviceOffset(Map entry) async {
    return await platform.invokeMethod('setDeviceOffset', entry);
  }

  /// 同步线缆入口标记
  Future<String> getAppFontPath() async {
    try {
      if (Platform.isIOS) {
        return await platform.invokeMethod('appFontPath', {});
      } else {
        return Future.value('');
      }
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return Future.value('');
    }
  }

  /// 获取rfid模板替换标记
  Future<int> getRfidReplaceTag() async {
    return await platform.invokeMethod('getRfidTag');
  }

  /// 查看危废台账
  Future<void> checkDangerRecord() async {
    return await platform.invokeMethod('checkDangerRecord');
  }

  /// 发送生成预览图事件
  Future<void> sendCapGenerateTemplatePreviewEvent(String imageBase64) async {
    return await platform.invokeMethod('sendCapGenerateTemplatePreviewEvent', {"imageBase64": imageBase64});
  }

  Future<bool> isShowVip() async {
    try {
      if (Platform.isAndroid) {
        return await platform.invokeMethod(_ifShowVip);
      } else {
        return Future.value(true);
      }
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return Future.value(false);
    }
  }

  Future<bool> checkLoginStatus() async {
    return await platform.invokeMethod(_checkLoginStatus);
  }

  Future<int> getUserId() async {
    return await platform.invokeMethod(_getUserId);
  }

  Future<List> getImportFileSocialList() async {
    List excelSocialList = await platform.invokeMethod(_getImportFileSocialList);
    return excelSocialList.map((e) {
      switch (e) {
        case 'dingding':
          return 'dingtalk';
        case 'weixin':
          return 'wechat';
        case 'whatapp':
          return 'whatsapp';
        default:
          return e;
      }
    }).toList();
  }

  importFileFromLocal(
      Function(String fileName, Uint8List data, String filePath, int fileSize) resultData, String fileType) async {
    var result = await platform.invokeMethod(_importFileFromLocal, fileType);
    if (result != null && result is Map) {
      resultData(result['fileName'] ?? "", result['data'] ?? Uint8List.fromList([0, 0]), result['filePath'] ?? "",
          result['fileSize'] ?? 0);
    }
  }

  importExcelFromSocialApp(String socialType,
      Function(String fileName, Uint8List data, String filePath, int fileSize) resultData, String fileType) async {
    var result =
        await platform.invokeMethod(_importExcelFromSocialApp, {"socialType": socialType, "fileType": fileType});
    if (result != null && result is Map) {
      resultData(result['fileName'] ?? "", result['data'] ?? Uint8List.fromList([0, 0]), result['filePath'] ?? "",
          result['fileSize'] ?? 0);
    }
  }

  Future<int?> checkBarcodeFormat(String barcodeContent, int barcodeType) async {
    try {
      Map params = {"barcodeContent": barcodeContent, "barcodeType": barcodeType};
      return await platform.invokeMethod(_checkBarcodeFormat, params);
    } catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  /// 从原生获取打印机系列下的首个打印机模型
  Future<String?> getPrinterById(String machineId) async {
    try {
      return await platform.invokeMethod(_getPrinterById, {'machineId': machineId});
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return null;
    }
  }

  static Future<String> getFlutterProxyUrl() async {
    try {
      var result = await platform.invokeMethod(_getFlutterProxyUrl);
      return result;
    } catch (e, s) {
      Log.d('异常信息:\n $e');
      Log.d('调用栈信息:\n $s');
      return "";
    }
  }

  /// 同步灰度策略数据给原生
  static Future<bool> syncGrayConfig(GrayConfig? config) async {
    if (config == null) {
      return false;
    }
    try {
      var result = await platform.invokeMethod(_syncGrayConfig, {'grayConfig': grayConfigToJson(config)});
      return result;
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  static Future<bool> isForceLTR() async {
    try {
      if (Platform.isIOS) {
        return false;
      }
      return await platform.invokeMethod(_isForceLTR);
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  //获取模版打开支持的最大版本号
  static Future<String> getMaxSupportTemplateVersion() async {
    try {
      return await platform.invokeMethod(_getMaxSupportTemplateVersion);
    } catch (e) {
      Log.d(e.toString());
      return '';
    }
  }

  /// 是否存在模版信息
  Future<bool> isExistTemplate({required String templateId}) async {
    try {
      return await platform.invokeMethod(_isExistTemplate, {'templateId': templateId});
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  ///获取打印浓度
  Future<num> getDensity({required String consumableCode}) async {
    try {
      return await platform.invokeMethod(_getDensity, consumableCode);
    } catch (e) {
      Log.d(e.toString());
      return 0;
    }
  }

  /// 是否存在电子价签模版信息
  Future<bool> isExistEtagTemplate({required String templateId}) async {
    try {
      return await platform.invokeMethod(_isExistEtagTemplate, {'templateId': templateId});
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  /// 是否可以打开历史模版
  Future<String> isCanOpenTemplate({required String templateVersion}) async {
    try {
      return await platform.invokeMethod(_isCanOpenHistoryTemplate, {'templateVersion': templateVersion});
    } catch (e) {
      Log.d(e.toString());
      return "0";
    }
  }

  /// 获取行业模板详情
  Future<String> getIndustryTemplateDetail(Map<String, dynamic> templateInfo) async {
    try {
      return await platform.invokeMethod(_getIndustryTemplateDetail, templateInfo);
    } catch (e) {
      Log.d(e.toString());
      return "0";
    }
  }

  hideBottomBar() {
    try {
      platform.invokeMethod("hideBottomBar");
    } catch (e) {
      Log.d(e.toString());
    }
  }

  showBottomBar() {
    try {
      platform.invokeMethod("showBottomBar");
    } catch (e) {
      Log.d(e.toString());
    }
  }

  closeNpsView() {
    try {
      platform.invokeMethod("closeNps");
    } catch (e) {
      Log.d(e.toString());
    }
  }

  /// 上传Flutter异常信息
  static FutureOr<void> postCatchedException(Map<dynamic, dynamic> map) async {
    try {
      await platform.invokeMethod("postCatchedException", map);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  ///  创建高级二维码
  static Future<Map> createAdvanceQRCode(String codeType) async {
    try {
      Map advanceQRCode = await platform.invokeMethod(_createAdvanceQRCode, codeType);
      return advanceQRCode;
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///  同步高级二维码信息至原生缓存
  static Future<void> updateAdvanceQRCodeToNative(Map advanceQRCode) async {
    try {
      await platform.invokeMethod(_updateAdvanceQRCodeToNative, advanceQRCode);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  ///通过双击或属性区域更新高级二维码
  static Future<Map> changeAdvanceQRCode(Map advanceQRCodeInfo) async {
    try {
      return await platform.invokeMethod(_changeAdvanceQRCode, advanceQRCodeInfo);
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///缓存活码信息
  static Future<void> cacheAdvanceQRCodeInfo(Map advanceQRCodeInfo) async {
    try {
      await platform.invokeMethod(_cacheAdvanceQRCode, advanceQRCodeInfo);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  ///通过属性区域预览高级二维码
  static Future<Map> previewAdvanceQRCode(String codeId, String codeType) async {
    try {
      return await platform.invokeMethod(_previewAdvanceQRCode, {"codeId": codeId, "codeType": codeType});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///Flutter 模版操作变更通知原生刷新

  static nativeTemplateNeedRefresh() async {
    try {
      return await platform.invokeMethod(_nativeTemplateNeedRefresh);
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///Flutter 模版操作变更通知原生刷新

  static nativeTemplateDBUpdate(String id,
      {String folderId = "", String name = "", int localType = 0, String updateTime = ""}) async {
    try {
      return await platform.invokeMethod(_nativeTemplateDBUpdate,
          {"id": id, "folderId": folderId, "name": name, "localType": localType, "updateTime": updateTime});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///Flutter 根据场景从原生获取数据库地址及表名

  static Future<Map> getNativeDBTemplatePathInfo(String tableName) async {
    try {
      return await platform.invokeMethod(_getNativeDBTemplatePathInfo, tableName);
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///Flutter 批量打印
  static toNativeBatchPrint(List<Map> printInfos) {
    try {
      platform.invokeMethod(_toNativeBatchPrint, printInfos);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  ///全局风控弹窗
  static toRiskCheckDialog(Map params) {
    try {
      platform.invokeMethod(_toRiskCheckDialog, params);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  /// 检查模板信息及资源是否完整
  static Future<bool> checkTemplateResourceComplate(List<String> ids) async {
    try {
      int result = await platform.invokeMethod(_checkTemplateResourceComplate, ids);
      return Future.value(result == 1);
    } on PlatformException catch (e) {
      Log.d(e.toString());
      return Future.value(false);
    }
  }

  ///通过ID获取高级二维码
  static Future<Map> getAdvanceQRCodeInfo(String codeId, String codeType) async {
    try {
      return await platform.invokeMethod(_getAdvanceQRCodeInfo, {"codeId": codeId, "codeType": codeType});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  ///通过ID获取高级二维码
  static Future<List<Map>> getAdvanceQRCodeCaches(String liveCodeIds, String formIds) async {
    try {
      List advanceQRCodeCaches =
          await platform.invokeMethod(_getAdvanceQRCodeCaches, {"liveCodeIds": liveCodeIds, "formIds": formIds});
      return List<Map>.from(advanceQRCodeCaches);
    } catch (e) {
      Log.d(e.toString());
      return [];
    }
  }

  static Future<List<String>> getNativePersonalTemplateSearchHistory() async {
    try {
      List searchHistory = await platform.invokeMethod(_nativePersonalTemplateSearchHistory);
      return List<String>.from(searchHistory);
    } catch (e) {
      Log.d(e.toString());
      return [];
    }
  }

  static showScanLoginSuccess() {
    try {
      return platform.invokeMethod(_showScanLoginSuccess);
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static ableToPrint(bool isAbleToPrint) {
    try {
      return platform.invokeMethod(_ableToPrint, {"isAbleToPrint": isAbleToPrint});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static showNpsAlert(String source) {
    //_toNpsAlert
    try {
      return platform.invokeMethod(_toNpsAlert, {"source": source});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static showNpsAlertWithParams(Map<String, dynamic> params) {
    try {
      return platform.invokeMethod(_toNpsAlert, params);
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static Future<bool> finishFlutterPages() async {
    try {
      return await platform.invokeMethod(_finishFlutterPages);
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }

  static notifyEnterC1Page() {
    try {
      platform.invokeMethod(_notifyEnterC1Page);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static notifyBackFromC1Page() {
    try {
      platform.invokeMethod(_notifyBackFromC1Page);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static toC1HomePage() async {
    try {
      return await platform.invokeMethod(_toC1HomePage);
    } catch (error) {
      Log.d(error.toString());
    }
  }

  ///请求极验验证
  static Future<Map> requestGeetestVerify(String regionCode, String phone, bool mustCheck) async {
    try {
      return await platform
          .invokeMethod(_requestGeetestVerify, {"regionCode": regionCode, "phone": phone, "mustCheck": mustCheck});
    } catch (e) {
      Log.d(e.toString());
      return {};
    }
  }

  static changePrintData(String templateJson) {
    try {
      return platform.invokeMethod(_changePrintData, {"templateJson": templateJson});
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static Future<String> getLabelShopLink(String oneCode, String jumpSource) async {
    try {
      return await platform.invokeMethod(_getLabelShopLink, {"oneCode": oneCode, "jumpSource": jumpSource});
    } catch (e) {
      Log.d(e.toString());
      return "";
    }
  }

  static Future<void> shareTemplate(String templateStr) async {
    try {
      return await platform.invokeMethod(_shareTemplate, {"template": templateStr});
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static Future<bool> getNativeNetworkState() async {
    try {
      return await platform.invokeMethod(_getNativeNetworkState);
    } catch (e) {
      Log.d(e.toString());
      return true;
    }
  }

  static Future<void> refreshNativeTemplateList() async {
    try {
      return await platform.invokeMethod(_refreshNativeTemplateList);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static Future<bool> toSyncNetworkState(bool isOutOfService) async {
    try {
      return await platform.invokeMethod(_syncOutOfService, {"isOutOfService": isOutOfService});
    } catch (e) {
      Log.d(e.toString());
      return true;
    }
  }

  static notifyFlutterChannel() {
    try {
      platform.invokeMethod(_notifyFlutterChannel);
    } catch (e) {
      Log.d(e.toString());
    }
  }

  static Future<bool> checkDebugMode() async {
    try {
      return await platform.invokeMethod(_checkDebugMode);
    } catch (e) {
      Log.d(e.toString());
      return false;
    }
  }
}
