<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <application>
        <activity
            android:name=".CapAppHostActivity"
            android:configChanges="orientation|keyboard|keyboardHidden|smallestScreenSize|screenLayout|screenSize|mcc|mnc|fontScale|navigation"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:launchMode="singleTask"
            android:permission="com.miui.securitycenter.permission.AppPermissionsEditor"
            android:screenOrientation="user"
            android:excludeFromRecents="true"
            android:taskAffinity=":cap"
            android:alwaysRetainTaskState="true"
            android:autoRemoveFromRecents="true"
            android:windowSoftInputMode="adjustResize" />
<!--            android:theme="@style/CapStyle"-->
<!--            android:process=":cap"-->

        <service
            android:name=".service.CapAppEventService"
            android:enabled="true"
            android:exported="true"/>
        <provider
            android:name="com.nimmbot.business.livecode.provider.CapacitorCameraProvider"
            android:authorities="${applicationId}.capacitorProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:ignore="WrongManifestParent">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>
</manifest>
