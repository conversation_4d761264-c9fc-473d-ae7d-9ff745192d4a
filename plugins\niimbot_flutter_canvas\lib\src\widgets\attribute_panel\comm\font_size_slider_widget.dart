import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/font_size_config.dart';
import 'package:niimbot_flutter_canvas/src/widgets/canvas/canvas_theme_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/value_slider_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';


class FontSizeSliderWidget extends StatefulWidget {
  final ValueChanged<FontSizeConfig>? valueChanged;
  final double mm;
  final List<FontSizeConfig> fontSizeConfigList;
  final List<CanvasElement> canvasElements;
  final bool enable;
  final bool showMaxConstraint;

  const FontSizeSliderWidget({
    super.key,
    this.valueChanged,
    required this.mm,
    required this.fontSizeConfigList,
    required this.canvasElements,
    this.enable = true,
    this.showMaxConstraint = false,
  });

  @override
  State<StatefulWidget> createState() {
    return FontSizeState();
  }
}

class FontSizeState extends State<FontSizeSliderWidget> {
  int _fontSizeConfigIndex = 0;

  @override
  void initState() {
    super.initState();
    _fontSizeConfigIndex = widget.fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(widget.mm);
    if (_fontSizeConfigIndex <= 0) {
      _fontSizeConfigIndex = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: widget.enable ? 1.0 : 0.2,
      child: Container(
          decoration: BoxDecoration(
              color: CanvasTheme.of(context).attributeGroupBackgroundColor,
              borderRadius: BorderRadius.all(Radius.circular(12))),
          child: ValueSliderWidget<int>(
            divisions: widget.fontSizeConfigList.length - 1,
            max: widget.fontSizeConfigList.length - 1,
            min: 0,
            value: _fontSizeConfigIndex<0? 0: _fontSizeConfigIndex,
            type: 1,
            canvasElements: widget.canvasElements,
            stepValue: 1,
            width: MediaQuery.sizeOf(context).width - 16 * 2 - 33,
            enable: widget.enable,
            preWidget: () => Padding(
              padding: EdgeInsetsDirectional.only(start: 12.0),
              child: SvgIcon(
                'assets/element/attribute/text_font_size.svg',
                width: 21,
                height: 14,
                useDefaultColor: false,
              ),
            ),
            indicatorTextBuilder: (int value) {
              String baseTitle = widget.fontSizeConfigList[_fontSizeConfigIndex].title;
              // 如果有文本过长警告，则显示"≤"前缀
              if (widget.showMaxConstraint) {
                return "≤$baseTitle";
              }
              return baseTitle;
            },
            valueChanged: (int v) {
              int index = v.toInt();
              if (_fontSizeConfigIndex == index) {
                return;
              }
              setState(() {
                _fontSizeConfigIndex = index;
                widget.valueChanged
                    ?.call(widget.fontSizeConfigList[_fontSizeConfigIndex]);
              });
            },
          )),
    );
  }

  @override
  void didUpdateWidget(covariant FontSizeSliderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _fontSizeConfigIndex = widget.fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(widget.mm);
    if (_fontSizeConfigIndex <= 0) {
      _fontSizeConfigIndex = 0;
    }
  }

}
