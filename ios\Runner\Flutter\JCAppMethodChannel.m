//
//  JCAppMethodChannel.m
//  Runner
//
//  Created by long he on 2022/4/6.
//
#import "FlutterBoostUtility.h"
#import "JCTemplateImageManager.h"
#import "JCPrintHistoryHelper.h"
#import "JCExcelDetailModel.h"
#import "JCAppMethodChannel.h"
#import "JCAppEventChannel.h"
#import "JCFontManager.h"
#import <Photos/Photos.h>
#import "AppDelegate.h"
#import "JCThirdAppTool.h"
#import "JCLabelUseModel.h"
#import "JCTemplateList.h"
#import "JCBluetoothManager.h"
#import "JCQAViewController.h"
#import "AppDelegate+Sentry.h"
#import "JCBluetoothManager+Connect.h"
#import "JCPlaceHolderSaveView.h"
#import "JCTemplateSaveManager.h"
#import "JCReviewHelp.h"
#import "JCTemplateSaveAsView.h"
#import "JCTemplateDBManager.h"
#import "ImageLibraryBridge.h"
#import "JCGrayConfigModel.h"
#import "JCGrayManager.h"
#import "JCLoginManager.h"
#import "JCExcelTransUtil.h"
#import "JCActivityCodeModel.h"
#import <flutter_boost/FBFlutterViewContainer.h>
#import "NBCAPMiniAppManager.h"
#import "JCEtagFileSaveManager.h"
#import "JCShopNormalVC.h"
#import "JCBatchPrintSourceManager.h"
#import "JCNPSWebviewAlert.h"
#import "JCC1Manager.h"
#import "JCShareTool.h"
#import "NMFAWebViewController.h"
#import "JCRFIDModel.h"


#import "JCGTCaptchaTools.h"
#import <UserNotifications/UserNotifications.h>
#import "JCPrintAlert.h"
#import "JCWifiManager.h"

#define  XY_KEYWindow [UIApplication sharedApplication].keyWindow

@interface JCAppMethodChannel()

@property (nonatomic, strong) FlutterMethodChannel *methodChannel;
@property(nonatomic,strong)__block JCTemplateData *templateData;
@property(nonatomic,strong)__block NSMutableArray *serialSpecifiedDevices;//搜索到的指定设备的列表
@property(nonatomic,strong)dispatch_queue_t intlQueue;
@property(nonatomic,assign)BOOL shouldUpdateFontsAndBorderAndIcon;
/// excel字节Block
@property(nonatomic, copy) void(^excelDataBlock)(NSString *fileName, NSData* data,NSString *filePath);
@property(nonatomic, copy) NSString *fileType;

@property (nonatomic, strong) id<NSObject> fileObserver;
@end


@implementation JCAppMethodChannel

static JCAppMethodChannel *channel = nil;
static  NSString *connectSearchDevice = @"connectSearchDevice";//连接设备
static  NSString *cancelSearchConnectDevice = @"cancelSearchConnectDevice";//取消连接资源清空
static  NSString *setFlutterVCCanSideslip = @"setFlutterVCCanSideslip";//设置当前flutter VC能否侧滑
static  NSString *cameraAuthorizationStatus = @"_camera_authorization_Status";//获取当前相机权限
static  NSString *closeNativeToast = @"_closeNativeToast";//关闭toast


static NSString *getSystemLanguage = @"_getSystemLanguage";//获取当前系统的语言

static NSString *syncIntlToNaive = @"_syncIntlToNaive";//从flutter获取国际化配置

static NSString *getIpAddressFromNative = @"_getIpAddressFromNative";//获取ip地址

static NSString *syncLanguageToNative = @"_syncLanguageToNative";//将用户使用的语言,以及所在地区同步给原生, 原生将其配置在请求头中,格式为:简体中文: zh-cn(zh-Hans-cn), 繁体中文: zh-Hant-cn

//登录相关
static  NSString *oneKeyLoginAuth = @"oneKeyLoginAuth";//
static  NSString *wechatLoginAuth = @"wechatLoginAuth";//
static  NSString *oneKeyBindAuth = @"oneKeyBindAuth";//
static  NSString *oneKeyRegisterAuth = @"oneKeyRegisterAuth";//
static  NSString *facebookLoginAuth = @"facebookLoginAuth";//
static  NSString *reportLoginSuccess = @"reportLoginSuccess";
static  NSString *cancelLogin = @"cancelLogin";
static  NSString *notifyCheckAccountResult = @"notifyCheckAccountResult";
static  NSString *refreshUserInfo = @"updateUserInfo";
static  NSString *getUserInfo = @"getUserInfo";
static  NSString *getUserMallToken = @"getUserMallToken";
static  NSString *getDeviceVipState = @"_getDeviceVipState";
static  NSString *getAppEnv = @"getAppEnv";
//改版连接流程相关=============
//检查手机蓝牙是否打开，ture-手机蓝牙为开启状态，false-手机蓝牙未开启
static  NSString *getBluetoothStatusEx = @"getBluetoothStatusEx";
//检查App通知是否打开，ture-App通知为开启状态，false-App通知未开启
static  NSString *areNotificationEnabled = @"areNotificationEnabled";
//跳转App通知设置
static  NSString *jumpToNotificationSetting = @"jumpToNotificationSetting";
//打开消息详情
static  NSString *jumpToMessageDetail = @"jumpToMessageDetail";
//未读消息同步更新
static  NSString *notifyUnreadMessageCount = @"notifyUnreadMessageCount";
static  NSString *searchDeviceEx = @"searchDeviceEx";
static  NSString *stopSearchDeviceEx = @"stopSearchDeviceEx";
static  NSString *connectDeviceEx = @"connectDeviceEx";
static  NSString *disconnectDeviceEx = @"disconnectDeviceEx";
static  NSString *clearSearchDeviceEx = @"clearSearchDeviceEx";//清除搜索记录, 如果正在搜索就停止搜索并清空记录
static  NSString *isIphoneX = @"_isIphoneX";
static  NSString *popFromFlutter = @"_popFromFlutter";
static  NSString *getPrinterElectricalLevel = @"getPrinterElectricalLevel";//获取打印机电量
static  NSString *appForLoginThatInstalledInMyPhone = @"appForLoginThatInstalledInMyPhone";//获取已经安装的社交登录app

static NSString *_getSupportSocialList = @"getSupportSocialList";//获取支持的已经安装的社交列表
static NSString *_checkSIMCardInfo = @"checkSIMCardInfo";//检查sim状态
static NSString *_SIMAccountOneKeyConfig = @"SIMAccountOneKeyConfig";//一键登录多语言配置
static NSString *_SIMAccountOneKeyLogin = @"SIMAccountOneKeyLogin";//一键登录
static NSString *_SIMAccountOneKeyRegister = @"SIMAccountOneKeyRegister";//一键注册
static NSString *_SIMAccountOneKeyBinding = @"SIMAccountOneKeyBinding";//一键绑定
static NSString *notifyCheckSIMAccountStatusResult = @"notifyCheckSIMAccountStatusResult";//一键登录和注册结果
static NSString *_bindMainAccoutSuccess = @"bindMainAccountSuccess";


static NSString *pushToRoute = @"pushToRoute"; // 路由跳转


//我的界面相关
static NSString *_shopInfoNotifyNumbers = @"shopInfoNotifyNumbers";
static NSString *getLocalUserInfoCache = @"getLocalUserInfoCache";
void(^showAuthAleart)(int type)= ^(int type){
  //    NSString *typeStr = type == 1? XY_LANGUAGE_TITLE_NAMED(@"", @"相册") : XY_LANGUAGE_TITLE_NAMED(@"", @"相机");
  [JCAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app00032",@"提示")
                 message:XY_LANGUAGE_TITLE_NAMED(@"app01310",@"请在“设置-隐私-相机” ， 允许精臣云打印访问你的手机相机")
       cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00030",@"取消")
         sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app00297",@"设置") cancelBlock:^{
  } sureBlock:^{
    NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if([[UIApplication sharedApplication] canOpenURL:url]) {
      NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
      [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
    }
  }];
};

//

+ (JCAppMethodChannel *)shareInstance{
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    channel = [[JCAppMethodChannel alloc] init];
    channel.intlQueue = dispatch_queue_create("intl_queue", DISPATCH_QUEUE_CONCURRENT);
  });
  return channel;
}

- (FlutterMethodChannel *)methodChannel {
  return _methodChannel;
}

static BOOL isResivingRFID = NO;
- (void)registerChannelByEngine:(FlutterEngine *)flutterEngine {
  self.methodChannel = [FlutterMethodChannel methodChannelWithName:jcAppMethodChnnel
                                                   binaryMessenger:[flutterEngine binaryMessenger]];
  XYWeakSelf
  [_methodChannel setMethodCallHandler:^(FlutterMethodCall * _Nonnull call, FlutterResult  _Nonnull result) {
    UIViewController *controller = [XYTool getCurrentVC];
    UINavigationController *nav = controller.navigationController;

    //接收从flutter传递过来的参数
    id arguments = call.arguments;
    NSLog(@"flutter 调用原生方法:%@",call.method);
    if ([call.method isEqualToString:isIphoneX]) {
      BOOL res = iPhoneX;
      result(@(res ? 1 : 0));
    }else if ([call.method isEqualToString:getBluetoothStatusEx]) {
      //检查手机蓝牙是否打开，ture-手机蓝牙为开启状态，false-手机蓝牙未开启
      result(@([JCBlUETOOTH_MANAGER getDeviceBluetoothState]));
    }else if ([call.method isEqualToString:jumpToMessageDetail]) {
      [weakSelf goToMessageDetail:arguments];
    }else if ([call.method isEqualToString:areNotificationEnabled]) {
      //检查手机通知是否打开
      [weakSelf handleAreNotificationsEnabled:result];
    }else if ([call.method isEqualToString:jumpToNotificationSetting]) {
      //开发App设置详情
      NSURL * url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
      if([[UIApplication sharedApplication] canOpenURL:url]) {
        NSURL*url =[NSURL URLWithString:UIApplicationOpenSettingsURLString];
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
      }
    }else if ([call.method isEqualToString:notifyUnreadMessageCount]) {
      //未读消息同步更新
      NSNumber *unreadMessageCount = arguments[@"unreadMessageCount"];
      [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_MIND_TABBAR_BADGE object:unreadMessageCount];
    }else if ([call.method isEqualToString:searchDeviceEx]) {
      [weakSelf searchDevices];
    }else if ([call.method isEqualToString:stopSearchDeviceEx]) {
      weakSelf.serialSpecifiedDevices = nil;
      [JCBlUETOOTH_MANAGER cancelScan:nil];
      result(@"");
    }else if ([call.method isEqualToString:connectDeviceEx]) {
      NSString *name = arguments[@"deviceName"];//连接的设备名称
      JCBluetoothModel *model = [[JCBluetoothModel alloc] init];
      model.name = name;

      [JCBlUETOOTH_MANAGER connectWith:model complate:^(id x) {

      }];


    }else if ([call.method isEqualToString:disconnectDeviceEx]) {
      [JCBlUETOOTH_MANAGER closeConnectedByHand:YES deviceType:JCBluetoothNormal];
    }else if ([call.method isEqualToString:clearSearchDeviceEx]) {
      //清除搜索记录, 如果正在搜索就停止搜索并清空记录
      weakSelf.serialSpecifiedDevices = nil;
      [JCBlUETOOTH_MANAGER cancelScan:nil];
    }else if ([call.method isEqualToString:@"goToNativePage"]) {

      [weakSelf goToNativePage:result arguments:arguments controller:controller];

    }else if ([call.method isEqualToString:@"dis_connect"]){
      [JCBlUETOOTH_MANAGER closeConnectedByHand:YES deviceType:JCBluetoothNormal];
    } else if ([call.method isEqualToString:@"set_DeviceItem"]){
    }else if ([call.method isEqualToString:connectSearchDevice]){
      NSString *name = arguments[@"name"];//连接的设备名称
      for (JCBluetoothModel *model in weakSelf.serialSpecifiedDevices) {
        if ([model.name isEqualToString:name]) {

          [JCBlUETOOTH_MANAGER connectWith:model complate:^(id x) {

          }];
        }
      }
    }else if ([call.method isEqualToString:cancelSearchConnectDevice]){
      weakSelf.serialSpecifiedDevices = nil;
      [JCBlUETOOTH_MANAGER cancelScan:nil];
    }else if ([call.method isEqualToString:syncLanguageToNative]){

    }else if([call.method isEqualToString:popFromFlutter]){
      [nav popViewControllerAnimated:true];
      [weakSelf.methodChannel setMethodCallHandler:nil];
      //            [nav dismissViewControllerAnimated:true completion:nil];
    }else if ([call.method isEqualToString:@"set_DeviceList"]){
      if ([arguments isKindOfClass:[NSDictionary class]]) {
      }
    } else if ([call.method isEqualToString:@"getDeviceSeriesData"]){
      if (result) {
        NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:nil];
        NSMutableArray *arr = [NSMutableArray array];
        if(printerInfoArr.count > 0){
          for (JCSelectDeviceModel *m  in printerInfoArr){
            [arr addObject:m.toDictionary];
          }
        }
        NSLog(@"flutter交互 获取原生设备系列缓存:%@",arr);
        result(arr);
      }
    } else if ([call.method isEqualToString:@"set_DeviceSeriesList"]){
      if ([arguments isKindOfClass:[NSArray class]]) {
        NSMutableArray *arr = [NSMutableArray array];
        for (NSDictionary *deviceDic  in arguments){
          JCSelectDeviceModel *model = [[JCSelectDeviceModel alloc] initWithDictionary:deviceDic error:nil];
          [arr addObject:model];
        }
        NSLog(@"flutter交互 请求设备系列缓存:%@",arr);
        NSString *langFilePath = [NSString stringWithFormat:@"%@/%@",DocumentsPath,@"devices"];
        NSLog(@"============%@",langFilePath);
        NSString *fileName = @"";
#ifdef Pro
        fileName =[NSString stringWithFormat:@"devicesList_%@.text",XY_JC_LANGUAGE];
#elif ReleaseAppStore
        fileName =[NSString stringWithFormat:@"devicesList_%@.text",XY_JC_LANGUAGE];
#elif JCTest
        fileName =[NSString stringWithFormat:@"devicesList_%@_test.text",XY_JC_LANGUAGE];
#elif Develop
        fileName =[NSString stringWithFormat:@"devicesList_%@.text",XY_JC_LANGUAGE];
#endif
        NSData *jsonData = [NSJSONSerialization dataWithJSONObject:arguments options:NSJSONWritingPrettyPrinted error:nil];
        NSString *langString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
        [XYCenter writeToFile:langFilePath fileName:fileName data:langString];
        [[JCFMDB shareDatabase:DB_NAME] jc_deleteTable:TABLE_MYDEVICES whereFormat:@""];
        [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_MYDEVICES dicOrModelArray:arr];
      }
    }else if([call.method isEqualToString:@"set_selectedDeviceSeries"]){
      if ([arguments isKindOfClass:[NSDictionary class]]) {
        JCSelectDeviceModel *model = [[JCSelectDeviceModel alloc] initWithDictionary:arguments error:nil];
        NSArray *machineNameArr = [model.machine_name componentsSeparatedByString:@","];
        if(model.machine_name.length > 0){
          NSString *machineName = machineNameArr[0];
          JCPrinterModel *printerModel = [[XYCenter sharedInstance] getPrinterModelWithMochineId:@"" orMachineName:machineName];
          NSString *interface_type = printerModel.interface_type;
          [[NSUserDefaults standardUserDefaults] setObject:interface_type forKey:@"current_select_printer_manufacturer_type"];
          [[NSUserDefaults standardUserDefaults] synchronize];
        }
        m_currentPrinterModel = model;
      }else if (arguments == nil){
        //设置默认系列
        NSString *interface_type = [[NSUserDefaults standardUserDefaults] valueForKey:@"current_select_printer_manufacturer_type"];
        if(STR_IS_NIL(interface_type)){
          NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:nil];
          if (IsNotEmptyArray(printerInfoArr)) {
            JCSelectDeviceModel *model = printerInfoArr[0];
            NSArray *machineNameArr = [model.machine_name componentsSeparatedByString:@","];
            if(model.machine_name.length > 0){
              NSString *machineName = machineNameArr[0];
              JCPrinterModel *printerModel = [[XYCenter sharedInstance] getPrinterModelWithMochineId:@"" orMachineName:machineName];
              NSString *interface_type = printerModel.interface_type;
              [[NSUserDefaults standardUserDefaults] setObject:interface_type forKey:@"current_select_printer_manufacturer_type"];
              [[NSUserDefaults standardUserDefaults] synchronize];
            }
            m_currentPrinterModel = model;
          }
        }
      }
    }else if ([call.method isEqualToString:@"setFlutterVCCanSideslip"]){
      if ([arguments isKindOfClass:[NSNumber class]]) {
        BOOL currentFlutterSideslip = ((NSNumber *)arguments).boolValue;
        NSLog(@"flutter %@侧滑",currentFlutterSideslip?@"能":@"不能");
        weakSelf.currentFlutterSideslip = currentFlutterSideslip;
        nav.interactivePopGestureRecognizer.enabled = currentFlutterSideslip;
      }
    }
    else if ([call.method isEqualToString:@"set_Token"]){
      if ([arguments isKindOfClass:[NSDictionary class]]) {
      }

    } else if ([call.method isEqualToString:@"set_mall_token"]){
      if ([arguments isKindOfClass:[NSDictionary class]]) {
      }

    }else if([call.method isEqualToString:@"getAppEnv"]){
      /**
       * 获取app接口环境
       * dev: 2
       * test: 1
       * production: 0
       */
      NSInteger appEnv = 0;
#ifdef  JCTest
      appEnv = app_laboratory_env;
#elif   Dev
      appEnv = 2;
#endif
            result(@(appEnv));
        }
        else if ([call.method isEqualToString:@"set_User"]){
            if ([arguments isKindOfClass:[NSDictionary class]] &&[[(NSDictionary*)arguments allKeys] containsObject:@"userJson"]) {
            }
        }else if ([call.method isEqualToString:@"get_Agent"]){
            if (result) {
              NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
              result(@{@"agent":[[XYCenter sharedInstance] getUA],@"anonymous_id":deviceId});
            }

        }else if([call.method  isEqualToString:_SIMAccountOneKeyConfig]){
            [JCLoginManager sharedInstance].config = arguments;
        }else if ([call.method isEqualToString:reportLoginSuccess]){
            if ([arguments isKindOfClass:[NSDictionary class]]) {
                NSString *resultString = arguments[@"resultJson"];
                NSDictionary *resultDic = [resultString xy_toDictionary];
                if(resultDic != nil){
                    NSDictionary *loginData = resultDic[@"loginData"];
                    NSDictionary *loginBusinessData = resultDic[@"loginBusinessData"];
                    if(!(IsNotEmptyDictionary(loginData)) && !(IsNotEmptyDictionary(loginBusinessData))) return;
                    NSString *token = loginData[@"accessToken"];
                    if(loginBusinessData == nil){
                        loginBusinessData = loginData;
                        token = resultDic[@"accessToken"];
                    }
                    UserModel *model = [[UserModel alloc] initWithDictionary:loginBusinessData error:nil];
                    model.token = token;
                    NSString *shopToken = m_userModel.shopToken;
                    if(!STR_IS_NIL(shopToken)){
                        model.shopToken = shopToken;
                    }
                    model.avatar = loginData[@"avatar"];
                    model.nickname = loginData[@"nickname"];
                    [XYCenter sharedInstance].userModel = model;
                    if(![XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"]){
                        [weakSelf postNotification:FLUTTER_LOGIN_SUCCESS];
                        [weakSelf postNotification:LOGIN_CHANGED_SHOP];
                    }else{
                        [[XYCenter sharedInstance] getShopMallUserToken:^{
                            [weakSelf postNotification:FLUTTER_LOGIN_SUCCESS];

                        } failure:^{
                            [weakSelf postNotification:FLUTTER_LOGIN_SUCCESS];
                        }];
                    }
                    [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {

                    } failure:^(NSString *msg, id model) {

                    } isNeedRefreshLoginInfo:NO];
                }

            }
        } else if ([call.method isEqualToString:cancelLogin]){
            // 取消登录
            [[JCLoginManager sharedInstance] cleanLoginSuccessBlock];

        } else if ([call.method isEqualToString:@"_wxPay"]){

        } else if ([call.method isEqualToString:@"getLanguageDetail"]){
            if (result) {
                result([XYCenter sharedInstance].languageDetailDic);
            }
        } else if ([call.method isEqualToString:@"getLoginPluginLanguageDetail"]){
//            if (result) {
//                NSDictionary *loginlLanguageDetailDic = [XYCenter sharedInstance].loginlLanguageDetailDic;
//                result(loginlLanguageDetailDic);
//            }
        }else if ([call.method isEqualToString:@"getAppCurrentLanguageType"]){
            if (result) {
                result(XY_JC_LANGUAGE_REAL);
            }
        }else if ([call.method isEqualToString:@"unNeedUIPrintComplate"]){
              NSDictionary *arguments = call.arguments;
              [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_PRINT_COMPLATE object:nil userInfo:arguments];
              [JCPrintManager sharedInstance].currentPrintState = 0;
        }else if ([call.method isEqualToString:@"refreshServerRFIDInfo"]){
            NSDictionary *serverRFIDInfo = call.arguments;
          if (JC_CURRENT_PRINTER_RFID_STATUS == 1) {
            // 标签
            NSString *paperSerial = serverRFIDInfo[@"paperSerial"];
            NSNumber *paperMax = serverRFIDInfo[@"paperMax"];
            NSString *paperColor = serverRFIDInfo[@"paperColor"];
            JCRFIDModel *seviceLabelRFIDModel = [[JCRFIDModel alloc] init];
            seviceLabelRFIDModel.serial_number = paperSerial;
            seviceLabelRFIDModel.actual_number = paperMax.integerValue == 0 ? @"" : paperMax.stringValue;
            seviceLabelRFIDModel.paperColor = paperColor;
            [JCBluetoothManager sharedInstance].seviceRFIDModel = seviceLabelRFIDModel;
          } else if (JC_CURRENT_PRINTER_RFID_STATUS == 2){
            // 碳带
            NSString *ribbonSerial = serverRFIDInfo[@"ribbonSerial"];
            NSNumber *ribbonMax = serverRFIDInfo[@"ribbonMax"];
            NSString *ribbonColor = serverRFIDInfo[@"ribbonColor"];
            JCRFIDModel *seviceCarbonRFIDModel = [[JCRFIDModel alloc] init];
            seviceCarbonRFIDModel.serial_number = ribbonSerial;
            seviceCarbonRFIDModel.actual_number = ribbonMax.integerValue == 0 ? @"" : ribbonMax.stringValue;
            seviceCarbonRFIDModel.carbonColor = ribbonColor;
            [JCBluetoothManager sharedInstance].seviceRFIDModel = seviceCarbonRFIDModel;
          } else if(JC_CURRENT_PRINTER_RFID_STATUS == 3){
            // 标签+碳带
            NSString *paperSerial = serverRFIDInfo[@"paperSerial"];
            NSNumber *paperMax = serverRFIDInfo[@"paperMax"];
            NSString *paperColor = serverRFIDInfo[@"paperColor"];
            JCRFIDModel *seviceLabelRFIDModel = [[JCRFIDModel alloc] init];
            seviceLabelRFIDModel.serial_number = paperSerial;
            seviceLabelRFIDModel.actual_number = paperMax.integerValue == 0 ? @"" : paperMax.stringValue;
            seviceLabelRFIDModel.paperColor = paperColor;
            [JCBluetoothManager sharedInstance].seviceLabelRFIDModel = seviceLabelRFIDModel;

            NSString *ribbonSerial = serverRFIDInfo[@"ribbonSerial"];
            NSNumber *ribbonMax = serverRFIDInfo[@"ribbonMax"];
            NSString *ribbonColor = serverRFIDInfo[@"ribbonColor"];
            JCRFIDModel *seviceCarbonRFIDModel = [[JCRFIDModel alloc] init];
            seviceCarbonRFIDModel.serial_number = ribbonSerial;
            seviceCarbonRFIDModel.actual_number = ribbonMax.integerValue == 0 ? @"" : ribbonMax.stringValue;
            seviceCarbonRFIDModel.carbonColor = ribbonColor;
            [JCBluetoothManager sharedInstance].sevicecarbonRFIDModel = seviceCarbonRFIDModel;
          }
          [[NSNotificationCenter defaultCenter] postNotificationName:RfidServerInfoNotification object:nil];
        }else if ([call.method isEqualToString:@"setConnectDevice"]){
            ///TODO    flutter连接设备成功后 原生处理
            NSString *connectedPrintInfo = call.arguments;
            if(!STR_IS_NIL(connectedPrintInfo)){
                NSData *jsonData = [connectedPrintInfo dataUsingEncoding:NSUTF8StringEncoding];
                id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
                if([jsonObject isKindOfClass:[NSDictionary class]]){
                    NSDictionary *printerInfo = jsonObject;
                    NSString *printerName = printerInfo[@"name"];
                    NSInteger hardCode = ((NSNumber *)printerInfo[@"code"]).integerValue;
                    [[JCBluetoothManager sharedInstance] refreshConnectedPrintWithName:printerName hardCode:hardCode];
                }else{
                    [[JCBluetoothManager sharedInstance] refreshConnectedPrintWithName:@"" hardCode:1];
                }
            }else{
                [[JCBluetoothManager sharedInstance] refreshConnectedPrintWithName:@"" hardCode:0];
            }
            NSLog(@"flutter回传：已连接打印机信息%@",connectedPrintInfo);
        }else if([call.method  isEqualToString:@"toRiskCheckDialog"]){
          NSDictionary *secret = arguments;
          [JCRiskShieldHelper riskShieldResultParms:arguments result:^(BOOL resultValue){
            result(@(resultValue));
          }];
        }
        else if ([call.method isEqualToString:@"setSDKRfidData"]){
            ///TODO    flutter连接设备成功后 RFID数据刷新
            NSString *jsonStr = call.arguments;
            NSLog(@"flutter回传：获取到rfid信息%@",jsonStr);
            NSData *jsonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
            id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
            if(isResivingRFID && [jsonObject isKindOfClass:[NSArray class]] && ((NSArray *)jsonObject).count == 0){
              NSLog(@"flutter回传：短时间内被调用 return");
              return;
            }
            isResivingRFID = YES;
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
              isResivingRFID = NO;
            });
            if(!STR_IS_NIL(jsonStr)){
                NSData *jsonData = [jsonStr dataUsingEncoding:NSUTF8StringEncoding];
                id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
                if([jsonObject isKindOfClass:[NSArray class]]){
                    NSArray *rfidInfos = jsonObject;
                    JCBlUETOOTH_MANAGER.rfidModel = nil;
                    JCBlUETOOTH_MANAGER.labelRFIDModel = nil;
                    JCBlUETOOTH_MANAGER.rfidModel = nil;
                    for (NSDictionary *rfidInfo in rfidInfos) {
                        JCRFIDDeviceModel *rfidModel = [JCRFIDDeviceModel new];
                        rfidModel.usedNumber = [NSString stringWithFormat:@"%@",rfidInfo[@"usedPaperMeters"] == nil?@"0":rfidInfo[@"usedPaperMeters"]];
                        rfidModel.paperType = [NSString stringWithFormat:@"%@",rfidInfo[@"type"] == nil?@"1":rfidInfo[@"type"]];
                        rfidModel.rfidCode = rfidInfo[@"barcode"];
                        rfidModel.piciCode = rfidInfo[@"piciCode"];
                        rfidModel.maxNumber = [NSString stringWithFormat:@"%@",rfidInfo[@"allPaperMeters"] == nil?@"0":rfidInfo[@"allPaperMeters"]];
                        rfidModel.rfid = rfidInfo[@"uuid"];
                        if(JCBlUETOOTH_MANAGER.connectedModel.printer.fake.integerValue == 3){
                            if(rfidModel.paperType.integerValue == 6 || rfidModel.paperType.integerValue == 8){
                                JCBlUETOOTH_MANAGER.carbonRFIDModel = rfidModel;
                            }else{
                                JCBlUETOOTH_MANAGER.rfidModel = rfidModel;
                                JCBlUETOOTH_MANAGER.labelRFIDModel = rfidModel;
                            }
                        }else if(JCBlUETOOTH_MANAGER.connectedModel.printer.fake.integerValue == 1){
                            if(rfidModel.paperType.integerValue != 6){
                                JCBlUETOOTH_MANAGER.rfidModel = rfidModel;
                                JCBlUETOOTH_MANAGER.labelRFIDModel = rfidModel;
                            }else{
                                JCBlUETOOTH_MANAGER.carbonRFIDModel = rfidModel;
                            }
                        }else if(JCBlUETOOTH_MANAGER.connectedModel.printer.fake.integerValue == 2){
                            if(rfidModel.paperType.integerValue == 6 || rfidModel.paperType.integerValue == 8){
                                JCBlUETOOTH_MANAGER.rfidModel = rfidModel;
                            }
                        }
                        else{
                            JCBlUETOOTH_MANAGER.rfidModel = rfidModel;
                        }
                    }
                    if(rfidInfos.count == 0){
                      [[NSNotificationCenter defaultCenter] postNotificationName:RfidGotLabelInfoNotification object:@"0"];
                      JCBlUETOOTH_MANAGER.multipleColors = @[];
                      JCBlUETOOTH_MANAGER.colorRGBStr = @"";
                      [JCBlUETOOTH_MANAGER setPrintColorWithRGBValue:@""];
                    }
                    [JCPrintManager sharedInstance].rfidTemplateData = nil;
                    [[NSNotificationCenter defaultCenter] postNotificationName:RfidPrinterConnectedNotification object:nil];
                    if(!JC_IS_CONNECTED_PRINTER || JC_RFID_SUPPORT_GET_TEMPLATE_ONLY || [JCBluetoothManager sharedInstance].isCoverOpen) return;
                    NSString *rfidSerialNumber = @"";
                    if(JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_CURRENT_PRINTER_RFID_STATUS == 2){
                        rfidSerialNumber = [JCBluetoothManager sharedInstance].rfidModel.rfid;
                        if(STR_IS_NIL(rfidSerialNumber)) {
                            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                              if(!JC_IS_CONNECTED_PRINTER) return;
                              [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000718", @"未识别到标签纸")];
                            });
                            return;
                        }
                    }else if(JC_CURRENT_PRINTER_RFID_STATUS == 3){
                        NSString *labelRFIDId = [JCBluetoothManager sharedInstance].labelRFIDModel.rfid;
                        NSString *carbonRFIDId = [JCBluetoothManager sharedInstance].carbonRFIDModel.rfid;
                        if(STR_IS_NIL(labelRFIDId) || STR_IS_NIL(carbonRFIDId))  {
                            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                              if(!JC_IS_CONNECTED_PRINTER) return;
                              [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000718", @"未识别到标签纸")];
                            });
                            return;
                        }
                    }else{
                        rfidSerialNumber = [JCBluetoothManager sharedInstance].rfidModel.rfid;
                        if(STR_IS_NIL(rfidSerialNumber))  {
//                            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000718", @"未识别到标签纸")];
                            return;
                        }
                    }

                }
            }else{
                JCBlUETOOTH_MANAGER.rfidModel = nil;
                JCBlUETOOTH_MANAGER.labelRFIDModel = nil;
                JCBlUETOOTH_MANAGER.rfidModel = nil;
                [JCPrintManager sharedInstance].rfidTemplateData = nil;
                [[NSNotificationCenter defaultCenter] postNotificationName:RfidPrinterConnectedNotification object:nil];
            }
        }else if ([call.method isEqualToString:_getSupportSocialList]){
            NSArray *loginthirdArr = [JCThirdAppTool loginThirdAppCanOpen];
            NSMutableArray *appTypeArr = [NSMutableArray array];
            if (result) {
                for (JCThirdAppInfo *appInfo in loginthirdArr) {
                    [appTypeArr addObject:appInfo.appType];
                }
                result(appTypeArr);
            }
        }
        else if([call.method  isEqualToString:_SIMAccountOneKeyLogin]){
            NSString *secret = arguments[@"oneKeyAuthSecret"];
            [[JCLoginManager sharedInstance] onekeyLoginWithSecretKey:secret type:1 completion:^(NSDictionary * _Nonnull res) {
                result(res);
            }];
        }else if([call.method  isEqualToString:@"cacheAdvanceQRCodeInfo"]){
          NSDictionary *liveCodeInfos = arguments;
          NSString *type = liveCodeInfos[@"codeType"];
          NSArray *codeInfos = liveCodeInfos[@"data"];
          if([type isEqualToString:@"liveCode"]){
            NSMutableArray *codeModelArr = @[].mutableArray;
            for (NSDictionary *codeModelDic in codeInfos) {
              JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithString:codeModelDic[@"item"] error:nil];
                NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
                if(dbcodeModelArr.count > 0){
                    [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:codeModel whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
                }else{
                    [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:codeModel];
                }
                [codeModelArr addObject:codeModel];
            }
          }else if([type isEqualToString:@"form"]){
            NSMutableArray *codeModelArr = @[].mutableArray;
            for (NSDictionary *codeModelDic in codeInfos) {
                JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithString:codeModelDic[@"item"] error:nil];
                codeModel.isForm = @"1";
                NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
                if(dbcodeModelArr.count > 0){
                    [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:codeModel whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
                }else{
                    [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:codeModel];
                }
                [codeModelArr addObject:codeModel];
            }
          }
          result(@"");
        }
        else if([call.method  isEqualToString:_SIMAccountOneKeyBinding]){
            NSString *secret = arguments[@"oneKeyAuthSecret"];
            [[JCLoginManager sharedInstance] onekeyLoginWithSecretKey:secret type:3 completion:^(NSDictionary * _Nonnull res) {
                result(res);
            }];
        }else if([call.method  isEqualToString:notifyCheckSIMAccountStatusResult]){
            [[JCLoginManager sharedInstance] checkTokenCompleted:call.arguments];
        }
        else if([call.method  isEqualToString:_checkSIMCardInfo]){
            result(@([XYTool isMainlandSim]));
        }
        else if([call.method  isEqualToString:_SIMAccountOneKeyRegister]){
            NSString *secret = arguments[@"oneKeyAuthSecret"];
            [[JCLoginManager sharedInstance] onekeyLoginWithSecretKey:secret type:2 completion:^(NSDictionary * _Nonnull res) {
                result(res);
            }];
        }
        else if([call.method  isEqualToString:@"unUIPrintErrorEvent"]){
            NSDictionary *errorInfo = arguments;
            NSNumber *isPause = errorInfo[@"isPaused"];
            NSNumber *errorCode = errorInfo[@"errorCode"];
            NSString *errorReason = errorInfo[@"errorReason"];
            NSString *uniAppId = errorInfo[@"uniAppId"];
            NSString *taskId = errorInfo[@"taskId"];
            UIViewController *currentVC = [XYTool getCurrentVC];
            NSString *currentVCStr = NSStringFromClass([currentVC class]);
            if(STR_IS_NIL([JCPrintManager sharedInstance].uniAppId) || [JCPrintManager sharedInstance].printScene != JCPrintNullUi){
              return;
            }
            for (UIView* view in [UIApplication sharedApplication].keyWindow.subviews)
            {
                if([view isKindOfClass:[JCPrintAlert class]]){
                  return;
                }
            }
            if(![JCPrintManager sharedInstance].isUseNativeAlert){
              NSDictionary *unComplateReasonInfo = @{@"status": @"0", @"uniAppId": UN_NIL(uniAppId), @"taskId": UN_NIL(taskId), @"message": errorReason};
              [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_PRINT_COMPLATE object:nil userInfo:unComplateReasonInfo];
            }else{
              if(isPause.boolValue){
                  [JCPrintManager sharedInstance].currentPrintState = 1;
                  [JCPrintAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app01265",@"打印暂停") message:errorReason cancelButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01584",@"关闭") sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01586", @"继续打印") cancelBlock:^{
                      [JCPrintManager sharedInstance].currentPrintState = 0;
                      [JCPrintManager sharedInstance].printScene = JCPrintSceneOthers;
                      result(@0);
                  } sureBlock:^{
                      [JCPrintManager sharedInstance].currentPrintState = 3;
                      result(@1);
                  }];
              }else{
                  NSDictionary *unComplateReasonInfo = @{@"status": @"0", @"uniAppId": UN_NIL(uniAppId), @"taskId": UN_NIL(taskId), @"message": errorReason};
                  if(errorCode.integerValue != 23){
                    [JCPrintManager sharedInstance].currentPrintState = 2;
                    [JCPrintAlert showAlertView:XY_LANGUAGE_TITLE_NAMED(@"app100001233",@"打印中断") message:errorReason cancelButtonTitle:@"" sureButtonTitle:XY_LANGUAGE_TITLE_NAMED(@"app01584",@"关闭") cancelBlock:^{
                        [JCPrintManager sharedInstance].currentPrintState = 0;
                        [JCPrintManager sharedInstance].printScene = JCPrintSceneOthers;
                        [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_PRINT_COMPLATE object:nil userInfo:unComplateReasonInfo];
                    } sureBlock:^{
                        [JCPrintManager sharedInstance].currentPrintState = 0;
                        [JCPrintManager sharedInstance].printScene = JCPrintSceneOthers;
                        [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_PRINT_COMPLATE object:nil userInfo:unComplateReasonInfo];
                    }];
                  }else{
                    [[NSNotificationCenter defaultCenter] postNotificationName:JCUONICAPP_PRINT_COMPLATE object:nil userInfo:unComplateReasonInfo];
                  }
              }
            }
        }
        else if ([call.method isEqualToString:@"resetUnUIPrintState"]){
          [JCPrintManager sharedInstance].currentPrintState = 0;
        }
        else if ([call.method isEqualToString:@"authorization_Status"]){

            int authStatus = 2;
            PHAuthorizationStatus phStatus = [PHPhotoLibrary authorizationStatus];
            if (phStatus == PHAuthorizationStatusDenied) {
                authStatus = 1;
                if (result) {
                    result(@(authStatus));
                }
                showAuthAleart(1);
                return;
            }

            if (result) {
                result(@(authStatus));
            }

        }else if ([call.method isEqualToString:@"getLocaleLanguage"]){
            NSString *localeIdentifier = [[NSLocale currentLocale] objectForKey:NSLocaleIdentifier];
            NSArray *contentArr = [localeIdentifier componentsSeparatedByString:@"_"];
            if(contentArr.count > 1){
                NSString *country = contentArr[1];
                country = [[country componentsSeparatedByString:@"@"] safeObjectAtIndex:0];
                if (result) {
                    result(@{@"languageCode":@"",@"countryCode":[country lowercaseString]});
                }
            }

        }else if ([call.method isEqualToString:cameraAuthorizationStatus]){//获取相机权限状态
            NSNumber *showAlert = arguments;
            int authStatus = 2;
            AVAuthorizationStatus avStatus = [AVCaptureDevice authorizationStatusForMediaType:AVMediaTypeVideo];
            if (avStatus ==AVAuthorizationStatusDenied ) {
                authStatus = 1;
                if (result) {
                    result(@(authStatus));
                }
                if (showAlert.boolValue) {
                    showAuthAleart(2);
                }
                return;
            }
            if (result) {
                result(@(authStatus));
            }
        } else if ([call.method isEqualToString:getUserInfo]){
            if ([arguments isKindOfClass:[NSDictionary class]]) {
                NSDictionary *userArguments = arguments;
                if(!STR_IS_NIL(userArguments[@"accessToken"])){
                    UserModel *userModel = [[UserModel alloc] init];
                    userModel.token = userArguments[@"accessToken"];
                    [XYCenter sharedInstance].userModel = userModel;
                }else{
                    [NBAppDelegate setSentryWith:m_userModel.email uId:m_userModel.uid];
                }
                NSString *graphqlStr = @"query getUserInfo{ getUserInfo{ uid userId phone email regionCode avatar nickname areaCode displayUId socialNetworks { avatar nickname platform } vipInfo { membershipName autoRenewal privileges exclusiveRights startTime expireAt startDatetime expireDatetime vipInterestsExpireTime vipInterestsExpireDatetime title unit valid membership dynamicQRCodeLimit type} } }";
                [@{@"needDesRequest":@"1"} jc_graphQLRequestWith:graphqlStr hud:nil graphQLType:@"query" Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
                    NSDictionary *userInfo = requestDic[@"getUserInfo"];
                    if(userInfo != nil){
                        NSString *token = m_userModel.token;
                        NSString *shopToken = m_userModel.shopToken;
                        UserModel *model = [[UserModel alloc] initWithDictionary:userInfo error:nil];
                        model.token = token;
                        if(!STR_IS_NIL(shopToken)){
                            model.shopToken = shopToken;
                        }
                        [XYCenter sharedInstance].userModel = model;
                        [NBAppDelegate setSentryWith:model.email uId:model.uid];
                        dispatch_group_t group = dispatch_group_create();
                        dispatch_group_enter(group);
                        [[XYCenter sharedInstance] getShopMallUserToken:^{
                            dispatch_group_leave(group);
                        } failure:^{
                            dispatch_group_leave(group);
                        }];
                        dispatch_group_enter(group);
                        NSDictionary *vipDetailParms = @{@"siteCode":UN_NIL([XYCenter sharedInstance].siteCode)};
                        [vipDetailParms java_getWithModelType:nil Path:J_get_VIP_INFO_DETAIL hud:nil Success:^(__kindof YTKBaseRequest *request, NSDictionary *requestDic) {
                            if(requestDic[@"products"] != nil && ![requestDic[@"products"] isKindOfClass:[NSNull class]]){
                                NSArray *products = requestDic[@"products"];
                                NSMutableArray *newProducts = [NSMutableArray array];
                                for (NSDictionary *vipInfo in products) {
                                    NSMutableDictionary *newVipInfo = [NSMutableDictionary dictionaryWithDictionary:vipInfo];
                                    NSString *expireAtTime = StringFromInt(((NSNumber *)newVipInfo[@"expireAt"]).integerValue/1000);
                                    NSDate *endTime = [NSDate dateWithTimeIntervalSince1970:expireAtTime.longLongValue];
                                    NSString *currentTime = m_currentServerTime;
                                    BOOL valid = NO;
                                    if(endTime){
                                        NSDate *nowTime = [NSDate dateWithString:currentTime format:@"yyyy-MM-dd'T'HH:mm:ss.SSSXXX"];
                                        if([nowTime compare:endTime] == NSOrderedAscending){
                                            valid = YES;
                                        }
                                    }
                                    newVipInfo[@"valid"] = valid?@1:@0;
                                    [newProducts addObject:newVipInfo];
                                }
                                model.products = newProducts;
                            }
                            [XYCenter sharedInstance].userModel = model;
                            dispatch_group_leave(group);
                        } failure:^(NSString *msg, id model) {
                            dispatch_group_leave(group);
                        }];
                        dispatch_group_notify(group, dispatch_get_main_queue(),^{
                            NSString *shopToken = m_userModel.shopToken;
                            NSMutableDictionary *userInfoDic = [NSMutableDictionary dictionaryWithDictionary:userInfo];
                            [userInfoDic setValue:UN_NIL(token) forKey:@"token"];
                            [userInfoDic setValue:UN_NIL(shopToken) forKey:@"shopToken"];
                            if( model.products.count > 0){
                                [userInfoDic setValue: model.products forKey:@"products"];
                            }
                            if ([JCAppEventChannel shareInstance].eventSink) {
                                [JCAppEventChannel shareInstance].eventSink(@{@"userInfo":userInfoDic});
                            }
                            if (result) {
                                result(userInfo);
                            }
                            [[NSNotificationCenter defaultCenter] postNotificationName:LOGIN_CHANGED object:nil];
                        });

                    }
                } failure:^(NSString *msg, id model) {
                    if(m_userModel != nil){
                        NSDictionary *userInfo = m_userModel.toDictionary;
                        if (result) {
                            result(userInfo);
                        }
                    }
                }];
            }
        }else if ([call.method isEqualToString:getUserMallToken]){
            [[XYCenter sharedInstance] getShopMallUserToken:^{

            } failure:^{

            }];
        } else if ([call.method isEqualToString:refreshUserInfo]){
            if ([arguments isKindOfClass:[NSDictionary class]]) {
                NSString *userInfoStr = arguments[@"userInfo"];
                NSDictionary *infoDic = [userInfoStr xy_toDictionary];
                if(infoDic[@"accessToken"] != nil){
                    NSString *tokenNew = infoDic[@"accessToken"];
                    if(!STR_IS_NIL(tokenNew)){
                        m_userModel.token = tokenNew;
                        [XYCenter sharedInstance].userModel = m_userModel;
                        [NBAppDelegate setSentryWith:m_userModel.email uId:m_userModel.uid];
                        [[NSNotificationCenter defaultCenter] postNotificationName:LOGIN_TOKEN_CHANGED object:nil];
                    }
                }
            }
        }else if ([call.method isEqualToString:@"log_Out"]){
            [[NSUserDefaults standardUserDefaults] removeObjectForKey:@"pasteboardString"];
            [XYCenter sharedInstance].userModel = nil;
            [weakSelf postNotification:LOGIN_CHANGED_SHOP];
            [weakSelf postNotification:LOGIN_CHANGED];
            if ([JCAppEventChannel shareInstance].eventSink) {
                [JCAppEventChannel shareInstance].eventSink(@{@"userInfo":@{}});
            }
            writeLogInfoToFileInFlutter(@"原生接收Flutter层退出", ^(id x,id y){},YES);
            [JCLoginManager sharedInstance].hasLogined = NO;
            [NBAppDelegate setSentryWith:@"" uId:@""];
        }else if ([call.method isEqualToString:@"getCurrentPaperSupportColors"]){
            NSArray *currentPaperColor = [JCBluetoothManager sharedInstance].multipleColors;
            result(currentPaperColor);
        }else if ([call.method isEqualToString:@"getDeviceIsSupportAnonymity"]){
          BOOL isSupportAnonymityBuyVip = [XYCenter sharedInstance].isSupportAnonymityBuyVip;
          result(@(isSupportAnonymityBuyVip));
        }else if ([call.method isEqualToString:@"getPrinterById"]){
            NSString *printId = arguments[@"machineId"];
            NSArray *printerArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat: [NSString stringWithFormat:@"where xyid = '%@'",printId]];
            if(printerArr.count >0){
                JCPrinterModel *currentModel = printerArr.firstObject;
                NSDictionary *printerInfoDic = @{};
                if(currentModel != nil){
                    printerInfoDic = [currentModel printerModelToDic];
                }
                NSString *printInfoStr = printerInfoDic.count > 0?printerInfoDic.xy_toJsonString:@"";
                result(printInfoStr);
            }else{
                result(@"");
            }
        }
        else if ([call.method isEqualToString:@"getCurrentSingleColorInfo"]){
            NSString *printColorStr = @"";
            if([JCBluetoothManager sharedInstance].printColor != nil && JC_IS_CONNECTED_PRINTER){
                printColorStr = [JCBluetoothManager sharedInstance].colorRGBStr;
            }else{
                printColorStr = @"";
            }
            NSInteger consumablesType = 0;
            if(JC_IS_CONNECTED_PRINTER){
                if(JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_CURRENT_PRINTER_RFID_STATUS == 0){
                    consumablesType = 1;
                }else{
                    consumablesType = 2;
                }
            }

            NSArray<NSNumber*> *supportColorsType = [JCBluetoothManager sharedInstance].connectedNetyModel.colorModeSupport;
            BOOL supportSixteenGrayPrint = [supportColorsType containsObject:@3] && [[JCBluetoothManager sharedInstance] isGray16Paper];
            result(@{
                @"consumablesType":@(consumablesType),@"supportSixteenGrayPrint":@(supportSixteenGrayPrint),
                @"currentPrintColor": printColorStr});
        }else if([call.method isEqualToString:@"checkBarcodeFormat"]){
            NSString *barcodeContent = arguments[@"barcodeContent"];
            NSNumber *barcodeType= arguments[@"barcodeType"];
            JCCodeType codeType = barcodeType.integerValue;
            NSString *errorCode = [ImageLibraryBridge checkBarCode:barcodeContent withBarcodeMode:codeType];
            result(@(errorCode.integerValue));
        }else if([call.method isEqualToString:@"getConnectDevice"]){
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                JCPrinterModel *currentModel = JC_CURRENT_CONNECTED_PRINTER_MODEL;
                NSDictionary *printerInfoDic = @{};
                if([JCBluetoothManager sharedInstance].deviceType != JCBluetoothETag){
                    if(currentModel != nil){
                        printerInfoDic = [currentModel printerModelToDic];
                    }
                    NSString *printInfoStr = printerInfoDic.count > 0?printerInfoDic.xy_toJsonString:@"";
                    [[JCAppEventChannel shareInstance] eventData:@{
                        @"printerConnectState": [[JCBluetoothManager sharedInstance] getPrinterConnectState],
                        @"printer":printInfoStr,
                        @"action":@"printerConnectState"
                    }];
                }
                result(printerInfoDic);
            });
        }else if([call.method isEqualToString:@"syncReplaceRfidTag"]){
            NSNumber *isCheck = arguments[@"hasConfirmAutoRfid"];
            if(isCheck.boolValue){
                jc_ignoreLabelRecogType = isCheck.boolValue?2:0;
            }else{
                NSInteger isCheck = jc_ignoreLabelRecogType == 2?1:0;
                result(@(isCheck));
            }
        }else if([call.method isEqualToString:@"getPrintSettingMsg"]){
            NSString *gap = @"  ";
            NSString *templateSn = arguments;
            NSMutableArray *deviceInfos = [NSMutableArray array];

            NSDictionary *deviceInfoDic = JC_IS_CONNECTED_PRINTER ? [JCBluetoothManager sharedInstance].deviceDict : nil;
            JCTemplateData *rfidData = [JCPrintManager sharedInstance].rfidTemplateData;
            id extrain = rfidData ? rfidData.profile.extrain : nil;

          
            JCBluetoothModel *model = [[JCBluetoothManager sharedInstance] getCurrentConnetedModel];
          
            // 1. SN、B-V  TODO 电压无
            if (JC_IS_CONNECTED_PRINTER) {
                NSString *sn = model.name ?: @"";
                NSString *bv = deviceInfoDic[@"B-V"] ?: @"";
                [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"SN" value1:sn gap:gap key2:@"B-V" value2:bv]];
                // 2. H-V、F-V
                NSString *hv = deviceInfoDic[PRINT_DEVICE_HARDWARE_TYPE] ?: @"";
                NSString *fv = deviceInfoDic[PRINT_DEVICE_FIRMWARE_TYPE] ?: @"";
                [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"H-V" value1:hv gap:gap key2:@"F-V" value2:fv]];
            }
          
//            JCRFIDDeviceModel *labelRFIDModel = JCBlUETOOTH_MANAGER.labelRFIDModel;
//            JCRFIDDeviceModel *carbonRFIDModel = JCBlUETOOTH_MANAGER.carbonRFIDModel;
//          
//            JCRFIDModel *seviceLabelRFIDModel = JCBlUETOOTH_MANAGER.seviceLabelRFIDModel;
//            JCRFIDModel *sevicecarbonRFIDModel = JCBlUETOOTH_MANAGER.sevicecarbonRFIDModel;
//          
//            // 3. BN、TN
//            NSString *bn = JCBlUETOOTH_MANAGER.labelRFIDModel.piciCode ?: @"";
//            NSString *tn = JCBlUETOOTH_MANAGER.carbonRFIDModel.piciCode ?: @"";
//            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"BN" value1:bn gap:gap key2:@"TN" value2:tn]];
//
//            // 4. R-ID、T-ID
//            NSString *rid = JCBlUETOOTH_MANAGER.labelRFIDModel.rfid ?: @"";
//            NSString *tid = JCBlUETOOTH_MANAGER.carbonRFIDModel.rfid ?: @"";
//            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"R-ID" value1:rid gap:gap key2:@"T-ID" value2:tid]];
//
//            // 5. BP、TP  TODO 软件统计张数？
//            NSString *bp1 = JCBlUETOOTH_MANAGER.seviceLabelRFIDModel.actual_number ?: @"";
//            NSString *bp2 = JCBlUETOOTH_MANAGER.labelRFIDModel.usedNumber ?: @"";
//            NSString *bp = ([bp1 length] || [bp2 length]) ? [NSString stringWithFormat:@"%@ / %@", bp1, bp2] : @"";
//            NSString *tp1 = JCBlUETOOTH_MANAGER.sevicecarbonRFIDModel.actual_number ?: @"";
//            NSString *tp2 = JCBlUETOOTH_MANAGER.carbonRFIDModel.usedNumber ?: @"";
//            NSString *tp = ([tp1 length] || [tp2 length]) ? [NSString stringWithFormat:@"%@ / %@", tp1, tp2] : @"";
//            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"BP" value1:bp gap:gap key2:@"TP" value2:tp]];

            // 6. BL、TL
//            NSString *bl = JCBlUETOOTH_MANAGER.labelRFIDModel.maxNumber ?: @"";
//            NSString *tl = JCBlUETOOTH_MANAGER.carbonRFIDModel.maxNumber ?: @"";
//            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"BL" value1:bl gap:gap key2:@"TL" value2:tl]];

            // 7. M、UID TODO 打印里程？SDK暂未提供
            NSString *mileage = @"";
            NSString *uid = m_userModel.displayUId ?: @"";
            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"M" value1:mileage gap:gap key2:@"UID" value2:uid]];

            // 8. A-V、BV
            NSBundle *bundle = [NSBundle mainBundle];
            NSString *av = [bundle objectForInfoDictionaryKey:@"CFBundleShortVersionString"] ?: @"";
            NSString *bv = [bundle objectForInfoDictionaryKey:(NSString *)kCFBundleVersionKey] ?: @"";
            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"A-V" value1:av gap:gap key2:@"BV" value2:bv]];

            // 9. S-V、Model
            NSString *sv = [UIDevice currentDevice].systemVersion ?: @"";
            NSString *modelStr = [XYTool deviceName] ?: @"";
            [deviceInfos addObject:[self formatKeyValuePairWithKey1:@"S-V" value1:sv gap:gap key2:@"Model" value2:modelStr]];

            // 10. D-ID（单独一行）
            NSString *did = [JCKeychainTool getDeviceIDInKeychain] ?: @"";
            [deviceInfos addObject:[NSString stringWithFormat:@"D-ID: %@", did]];

            result(deviceInfos);
        } else if([call.method isEqualToString:@"checkBatchPrintSources"]){
            NSString *printInfoStr = arguments;
            NSData *jsonData = [printInfoStr dataUsingEncoding:NSUTF8StringEncoding];
            id jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options: NSJSONReadingMutableContainers error:nil];
            if([jsonObject isKindOfClass:[NSArray class]]){
                NSMutableArray *ids = [NSMutableArray array];
                for (NSDictionary *printInfo in jsonObject) {
                    if(printInfo[@"id"] != nil){
                        [ids addObject:printInfo[@"id"]];
                    }
                }
                [[JCBatchPrintSourceManager sharedManager] checkBatchPrintSource:ids completion:^(BOOL sourceIsComplate) {
                    result(sourceIsComplate?@1:@0);
                }];
            }
        }
        else if ([call.method isEqualToString:_bindMainAccoutSuccess]){
            [weakSelf postNotification:ACCOUNT_BIND_SUCCESS];
        }else if([call.method isEqualToString:@"sendTrackingToNative"]){
            if ([arguments isKindOfClass:[NSDictionary class]]) {
                NSLog(@"原生接收flutter埋点%@",arguments);
                NSString *track = arguments[@"track"];
                NSString *posCode = arguments[@"posCode"];
                NSDictionary *extDic = (arguments[@"ext"] != nil && ![arguments[@"ext"] isKindOfClass:[NSNull class]])?arguments[@"ext"]:@{};
                JC_TrackWithparms(UN_NIL(track),UN_NIL(posCode),extDic);
            }
        } else if ([call.method isEqualToString:@"queryPrinterConnectedInfo"]) {
            if (JC_IS_CONNECTED_PRINTER) {
                NSString *bluetoothCode = JC_CURRENT_CONNECTED_PRINTER;
                [[JCPrintDevice shareDevice] getDeviceInfoDict:^(NSDictionary *info) {
                    if (info !=nil && info.allKeys.count > 0) {
                        if ([info.allKeys containsObject:PRINT_DEVICE_HARDWARE_TYPE] &&
                            [info.allKeys containsObject:PRINT_DEVICE_FIRMWARE_TYPE]) {
                            NSString *hardwareVersion = [info valueForKeyPath:PRINT_DEVICE_HARDWARE_TYPE];
                            NSString *firmwareVersion = [info valueForKeyPath:PRINT_DEVICE_FIRMWARE_TYPE];
                            result(@{
                                @"bluetoothCode": bluetoothCode ?: @"",
                                @"hardwareVersion": hardwareVersion ?: @"",
                                @"firmwareVersion": firmwareVersion ?: @""
                            });
                        }
                    }
                }];
            } else {
                result(@{});
            }
        }else if ([call.method isEqualToString:@"notifyLoginStatus"]){
            //登录成功重连设备

        }else if ([call.method isEqualToString:@"pasteboardSetting"]){
            NSString *pasteboardSetting = [JCKeychainTool load:@"pasteboardSettingSwitch"];
            result(@{@"pasteboardSetting":UN_NIL(pasteboardSetting)});
        }else if ([call.method isEqualToString:@"setPasteboardSwitch"]){
            if ([arguments isKindOfClass:[NSDictionary class]]) {
                NSLog(@"原生接收flutter埋点%@",arguments);
                NSString *switchValue = arguments[@"value"];
                [JCKeychainTool save:@"pasteboardSettingSwitch" data:switchValue];
            }
        }
        else if ([call.method isEqualToString:@"set_Language"]){

        }else if([call.method  isEqualToString:@"_flutterLog"]){

        } else if([call.method  isEqualToString:getDeviceVipState]){
            return result(@(jc_current_isDeviceVip));
        } else if([call.method  isEqualToString:getLocalUserInfoCache]){
            if(xy_isLogin){
                UserModel *user = m_userModel;
                NSDictionary *userInfo = user.toDictionary;
                result(userInfo);
            }else{
                result(nil);
            }
        } else if ([call.method isEqualToString:@"getVipStatus"]) {
            NSInteger vipStatus = m_user_vip?1:0;
            result(@(vipStatus));
        }else if([call.method isEqualToString:_shopInfoNotifyNumbers]){
            NSString *notifyNumbers  = arguments;;
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_SHOP_MALL_MSG_SHOW object:notifyNumbers];
        }else if ([call.method isEqualToString:@"getUseRecentTemplate"]) {

        }
        else if ([call.method isEqualToString:@"setSPData"]) {
            NSString *canvasType = arguments[@"cableEntry"];
            [[NSUserDefaults standardUserDefaults] setObject:UN_NIL(canvasType) forKey:@"CanvasType"];
        }else if([call.method isEqualToString:@"appFontPath"]){
            NSString *fontPath = [NSString stringWithFormat:@"%@/font", DocumentsFontPath];
            result(fontPath);
        }else if([call.method isEqualToString:@"getDensity"]){
            NSInteger density = 1;
            NSString *consumableCode = arguments;
            if(JC_IS_CONNECTED_PRINTER){
                NSString *printType = [[JCBluetoothManager sharedInstance] printerTypeFromPrinterName:JC_CURRENT_CONNECTED_PRINTER isNeedShowChildType:YES];
                NSString *wherString = [NSString stringWithFormat:@"where name = '%@'",printType];
                NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat:wherString];
                if(printerInfoArr.count > 0){
                    JCPrinterModel *printerModel = printerInfoArr[0];
                    NSString *solubilitySetType = printerModel.solubility_set_type;
                    if([solubilitySetType isEqualToString:@"2"]){
                        NSInteger consumableType = consumableCode.integerValue;
                        for (JCPrinterConsumableModel *consumableModel in printerModel.consumableModels) {
                            if([consumableModel.parentProperty.code isEqualToString:StringFromInt(consumableType)]){
                                NSLog(@"浓度设置：使用默认的耗材浓度");
                                if(consumableModel.parentProperty.density.integerValue != 0){
                                    density = consumableModel.parentProperty.density;
                                }
                                break;
                            }
                        }
                    }
                }
            }
            result(@(density));
        }
        else if([call.method isEqualToString:@"getCableDetail"]){
            NSString *barcode = arguments[@"barCode"];
            NSString *labelId = arguments[@"labelId"];
            [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:labelId barCode:barcode success:^(JCTemplateData *requestModel) {
                result(UN_NIL(requestModel.toJSONString));
            } field:^(id x) {
                [MBProgressHUD showToastWithMessageDarkColor:x];
                result(@"");
            }];
        }
        else if ([call.method isEqualToString:@"getUseRecentLabel"]) {
            // 获取最近使用的模版记录
            NSArray<NSString *> *machineIds = arguments[@"machineIds"];
            int limit = ((NSNumber *)arguments[@"limit"]).intValue;
            [weakSelf getUseRecentTemplateFromMachineIdNew:machineIds WithLimit:limit successBlock: ^(NSArray<JCTemplateData *> *templateDatas) {
                NSMutableArray *array = [NSMutableArray arrayWithCapacity:[templateDatas count]];
                [templateDatas enumerateObjectsUsingBlock:^(JCTemplateData * obj, NSUInteger idx, BOOL *stop) {
                    [array addObject:obj.toDictionary];
                }];
                result(array);
            }];
        } else if ([call.method isEqualToString:@"getPrinterLabelData"]) {
            // 获取当前打印机内标签纸信息
            JCTemplateData *data = nil;
            if(JC_IS_CONNECTED_PRINTER){
                data = [JCPrintManager sharedInstance].rfidTemplateData;
            }
            NSString *jsonStr = [data.toDictionary xy_toJsonString];
            result(data == nil ? @{} : data.toDictionary);
        } else if ([call.method isEqualToString:@"toRiskCheckDialog"]) {
            // 获取当前打印机内标签纸信息
            NSDictionary *parms = arguments;
            [JCRiskShieldHelper riskShieldResultParms:parms result:^(BOOL result) {

            }];
        }  else if ([call.method isEqualToString:@"getPrinterConnectState"]) {
            // 获取当前打印机连接状态
            NSMutableDictionary *printerInfoDic = [[JCBluetoothManager sharedInstance] getPrinterConnectState].mutableCopy;
            if(JC_IS_CONNECTED_PRINTER){
                NSInteger highQuality = [[JCBluetoothManager sharedInstance].connectedNetyModel.printQualitySupport containsObject:@0]?1:0;
                NSInteger highSpeed = [[JCBluetoothManager sharedInstance].connectedNetyModel.printQualitySupport containsObject:@1]?1:0;
                NSString *printTaskType = [JCBluetoothManager sharedInstance].connectedNetyModel.printQualityMode;
                JCPrinterModel *currentPrintModel = JC_CURRENT_CONNECTED_PRINTER_MODEL;
                printerInfoDic[@"machineName"] = JC_CURRENT_CONNECTED_PRINTER;
                printerInfoDic[@"connected"] = @1;
                printerInfoDic[@"printPriority"] = @(STR_IS_NIL(printTaskType)?1:printTaskType.integerValue);
                printerInfoDic[@"highQuality"] = @(highQuality);
                printerInfoDic[@"highSpeed"] = @(highSpeed);
                printerInfoDic[@"printAlign"] = @(currentPrintModel.print_align.integerValue);
                printerInfoDic[@"widthSetEnd"] = @(currentPrintModel.width_set_end.integerValue);
                printerInfoDic[@"widthSetStart"] = @(currentPrintModel.width_set_start.integerValue);
                NSArray *consumables = [currentPrintModel printerModelToDic][@"consumables"];
                printerInfoDic[@"consumables"] = [consumables yy_modelToJSONString];
                if((JC_CURRENT_PRINTER_RFID_STATUS == 2 || JC_CURRENT_PRINTER_RFID_STATUS == 3) && ([[JCBluetoothManager sharedInstance].colorRGBStr componentsSeparatedByString:@"."].count == 3 || [[JCBluetoothManager sharedInstance].colorRGBStr componentsSeparatedByString:@","].count == 2)){
                    printerInfoDic[@"isCarbon"] = @(true);
                }else{
                    printerInfoDic[@"isCarbon"] = @(false);
                }
//                unsigned int color = 0;
                NSString *colorStr = JCBlUETOOTH_MANAGER.colorRGBStr;
//                NSString *colorStr = @"230.0.18";
//                if(!STR_IS_NIL(colorStr)) color = [XYTool argbStringToHex:[NSString stringWithFormat:@"255.%@",colorStr]];
              printerInfoDic[@"carbonColors"] = UN_NIL(colorStr);
            }
            result(printerInfoDic);

        }else if ([call.method isEqualToString:@"setRfidColor"]){
            NSString *colorRGBStr = @"";
            NSString *paperColor = arguments[@"paperColor"];
            NSString *ribbonColor = arguments[@"ribbonColor"];
            NSString *printColor = @"";
            JCPrinterModel *currentConnectPrinter = JCBlUETOOTH_MANAGER.currentConnectPrinter;
            NSInteger rfidType = currentConnectPrinter.fake.integerValue;
            if(rfidType == 2 || rfidType == 3){
                colorRGBStr = ribbonColor;
            }else{
                colorRGBStr = paperColor;
            }
            if([colorRGBStr componentsSeparatedByString:@"."].count == 3){
                JCBlUETOOTH_MANAGER.colorRGBStr = colorRGBStr;
                [JCBlUETOOTH_MANAGER setPrintColorWithRGBValue:colorRGBStr];
            }else if([colorRGBStr componentsSeparatedByString:@","].count > 1){
                JCBlUETOOTH_MANAGER.colorRGBStr = colorRGBStr;
                JCBlUETOOTH_MANAGER.multipleColors = [colorRGBStr componentsSeparatedByString:@","];
                [JCBlUETOOTH_MANAGER setPrintColorWithRGBValue:rfidType == 3 ? colorRGBStr : @""];
            }else{
                JCBlUETOOTH_MANAGER.multipleColors = @[];
                JCBlUETOOTH_MANAGER.colorRGBStr = colorRGBStr;
                [JCBlUETOOTH_MANAGER setPrintColorWithRGBValue:@""];
            }
        }
        else if ([call.method isEqualToString:@"getDeviceSeries"]){
            if (result) {
                // 获取系列ID
                NSString *series = arguments[@"series"];
                NSArray *seriesModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat: [NSString stringWithFormat:@"where hardware_series_id = '%@'",series]];
                JCSelectDeviceModel *seriesModel = seriesModelArr.firstObject;
                // 通过,分割系列下的机型
                NSArray<NSString *> *machineIds = [seriesModel.machine_Id componentsSeparatedByString:@","];
                // 通过机型系列模型获取系列中第一个机型设备
                NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_DEFAULT] jc_lookupTable:TABLE_PRINTERINFO dicOrModel:[JCPrinterModel class] whereFormat: [NSString stringWithFormat:@"where xyid = '%@'",machineIds.firstObject]];
                JCPrinterModel *model = printerInfoArr.firstObject;
                result(model.toDictionary);
            }
        } else if ([call.method isEqualToString:@"closeHardWareConnected"]) {
            // 关闭连接
            NSLog(@"关闭打印机连接");
            [[JCBluetoothManager sharedInstance] closeConnectedByHand:YES deviceType:JCBluetoothNormal];
            result(@1);

        } else if ([call.method isEqualToString:@"labelRecord"]) {
            // 关闭连接
            NSDictionary *templateDataDic = call.arguments;
            if(templateDataDic.count > 0){
                JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:templateDataDic error:nil];
                if(templateData.profile.extrain.labelId.length > 0){
                    saveRecentlyUsedRecord(templateData.profile.extrain.labelId);
                }
            }
        } else if ([call.method isEqualToString:@"getImParams"]) {
            // 关闭连接
            NSDictionary *deviceInfoDic = [JCBluetoothManager sharedInstance].deviceDict;
            JCTemplateData *templateData = [JCPrintManager sharedInstance].rfidTemplateData;
            JCBluetoothModel *blueToothModel = [JCBluetoothManager sharedInstance].connectedModel;
            NSString *firmwareVersion = [deviceInfoDic objectForKey:PRINT_DEVICE_FIRMWARE_TYPE];
            NSString *hardwareVersion = [deviceInfoDic objectForKey:PRINT_DEVICE_HARDWARE_TYPE];
            NSString *printerMacAdd = [deviceInfoDic objectForKey:PRINT_DEVICE_MAC_ADDRESS];
            NSString *rfidStr = @"";
            NSString *consumableName = templateData.name;
            NSString *consumableBarcode = @"";
            if (JC_CURRENT_PRINTER_RFID_STATUS == 1 || JC_CURRENT_PRINTER_RFID_STATUS == 2) {
                rfidStr = [JCBluetoothManager sharedInstance].rfidModel.rfid;
                consumableBarcode = [JCBluetoothManager sharedInstance].rfidModel.rfidCode;
            } else if (JC_CURRENT_PRINTER_RFID_STATUS == 3) {
                rfidStr = [NSString stringWithFormat:@"%@,%@",[JCBluetoothManager sharedInstance].labelRFIDModel.rfid,[JCBluetoothManager sharedInstance].carbonRFIDModel.rfid];
                consumableBarcode = [JCBluetoothManager sharedInstance].rfidModel.rfidCode;
            }
            NSArray *printerInfoArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_MYDEVICES dicOrModel:[JCSelectDeviceModel class] whereFormat:nil];
            __block JCSelectDeviceModel *seriesModel;
            [printerInfoArr enumerateObjectsUsingBlock:^(JCSelectDeviceModel * model, NSUInteger idx, BOOL *stop) {
                if ([model.hardware_series_id isEqualToString:UN_NIL(blueToothModel.printer.seriesId)]) {
                    seriesModel = model;
                    *stop = true;
                }
            }];
            NSDictionary *imParams = @{
                @"hardwareSn": UN_NIL(blueToothModel.name), // 硬件序列号
                @"hardwareSeries": UN_NIL(seriesModel.series_name), // 硬件系列
                @"hardwareModel": UN_NIL(blueToothModel.printer.name), // 硬件型号
                @"hardwareVersion": UN_NIL(hardwareVersion), // 硬件版本号
                @"firmwareVersion": UN_NIL(firmwareVersion), // 固件版本号
                @"rfid": UN_NIL(rfidStr), // 耗材rfid编号
                @"consumableBarcode": UN_NIL(consumableBarcode), // 耗材条码
                @"consumableName": UN_NIL(consumableName), // 耗材名称
                //                @"sdkVersion": @"3.1.8-beta8", // sdk版本号
                //                @"imageVersion": @"3.0.0", // 图像库版本号
                @"phoneBrand": XYTool.deviceName, // 手机品牌
            };
            result(imParams);
        }
        else if ([call.method isEqualToString:@"getTemplateDetail"]) {
            //yc to do
            NSNumber * needLoadFonts = arguments[@"needLoadFonts"];
            NSNumber * needSveUsedRecord = arguments[@"saveRecord"];
            NSNumber * templateClass = arguments[@"templateClass"];
            NSString *templateId = arguments[@"templateId"];
            NSNumber *isBatchPrintTemplate = arguments[@"isBatchPrintTemplate"];
            if(templateClass.integerValue == 0){
                [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:templateId barCode:@""  success:^(JCTemplateData *labelInfo) {
                    NSString *jsonString = [labelInfo toJSONString];
                    if(needSveUsedRecord == nil || needSveUsedRecord.boolValue){
                        // 保存使用记录
                        saveRecentlyUsedRecord(labelInfo.idStr);
                    }
                    result(jsonString);
                } field:^(id msg) {
                    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                        if(NETWORK_STATE_ERROR){
                            [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                        }else{
                            [MBProgressHUD showToastWithMessageDarkColor:msg];
                        }
                    });
                    result(@"");
                }];
            }else{
                if(isBatchPrintTemplate.boolValue){
                    [JCTemplateDBManager db_queryTemplateDataById:templateId success:^(JCTemplateData *templateData) {
                        [templateData resetTemplateLocalPath];
                        if(![[NSFileManager defaultManager] fileExistsAtPath:templateData.localThumbnail]){
                          NSLog(@"模板缩略图不存在:%@",templateData.localThumbnail);
                        }
//                        NSMutableDictionary *templateDic = [templateData.toCache(YES) dataDict].mutableCopy;
                      NSString *jsonString = templateData.toJSONString;
                      result(UN_NIL(jsonString));
                    } failed:^(id msg) {
                        result(@"");
                    }];
                    return;
                }
              [JCTemplateFunctionHelper getTemplateDetailRequestById:templateId complate:^(JCTemplateData *templateData){
                if(templateData != nil){
                  [JCLabelInfoMangerHelper getServerLabelInfoWithTemplate:templateData success:^(JCTemplateData *labelInfo) {
                      NSString *jsonString = [templateData toJSONString];
                      if(needSveUsedRecord == nil || needSveUsedRecord.boolValue){
                          // 保存使用记录
                          saveRecentlyUsedRecord(templateData.idStr);
                      }
                      if([needLoadFonts boolValue] == true){
                          [[JCFontManager sharedManager] downLoadFontRequestWithTemplateData:templateData finishBlock:^(BOOL loaded) {
                              result(jsonString);
                          }];
                      }else{
                          result(jsonString);
                      }
                  } field:^(id msg) {
                      NSString *jsonString = [templateData toJSONString];
                      if(needSveUsedRecord == nil || needSveUsedRecord.boolValue){
                          // 保存使用记录
                          saveRecentlyUsedRecord(templateData.idStr);
                      }
                      if([needLoadFonts boolValue] == true){
                          [[JCFontManager sharedManager] downLoadFontRequestWithTemplateData:templateData finishBlock:^(BOOL loaded) {
                              result(jsonString);
                          }];
                      }else{
                          result(jsonString);
                      }
                  }];
                }else{
                  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                      if(NETWORK_STATE_ERROR){
                          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
                      }else{
                          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100000661", @"加载失败")];
                      }
                  });
                  result(@"");
                }
              } needLoading:NO];
            }
        } else if ([call.method isEqualToString:@"checkLoginStatus"]) {
            // 判断是否登录
            result(@(xy_isLogin));
        } else if ([call.method isEqualToString:@"getUserId"]) {
            // 获取用户ID
            NSNumberFormatter *formatter = [[NSNumberFormatter alloc] init];
            formatter.numberStyle = NSNumberFormatterDecimalStyle;
            result([formatter numberFromString:m_userModel.userid]);
        } else if ([call.method isEqualToString:@"getImportFileSocialList"]) {
            // 获取excel可以打开的第三方目录
            NSArray<JCThirdAppInfo *> *thirdAppInfos = [JCThirdAppTool thirdAppExcelCanOpen];
            NSMutableArray<NSString *> *_result = [NSMutableArray arrayWithCapacity:[thirdAppInfos count]];
            [thirdAppInfos enumerateObjectsUsingBlock:^(JCThirdAppInfo *info, NSUInteger idx, BOOL *stop) {
                [_result addObject: info.appType];
            }];
            result(_result);
        } else if ([call.method isEqualToString:@"importFileFromLocal"]) {
            // 从本地获取excel文件
            NSString *fileType = arguments;
            self.fileType = fileType;
            self.excelDataBlock = ^(NSString *fileName, NSData* data,NSString *filePath){
                FlutterStandardTypedData *typedData = [FlutterStandardTypedData typedDataWithBytes:data];
                NSDictionary *dic = nil;
                if([self.fileType isEqualToString:@"pdf"]){
                    dic = @{
                        @"fileName": fileName,
                        @"filePath": filePath,
                        @"fileSize": @(data.length)};
                }else{
                    dic = @{
                        @"fileName": fileName,
                        @"data": typedData};
                }
                result(dic);
            };
            [self openLocalFileWithType:fileType];
        } else if ([call.method isEqualToString:@"importExcelFromSocialApp"]) {
            // 从第三方获取excel文件
            self.fileType = arguments[@"fileType"];
            [self openThirdAppEvent:arguments[@"socialType"] withResult:result];
        }else if([call.method isEqualToString:@"toEtagPage"]){
            float delayTime = 0;
            if(!xy_isLogin) {
                delayTime = 1;
            }
            [[JCLoginManager sharedInstance] checkLogin:^{

            } viewController:[XYTool getCurrentVC] loginSuccessBlock:^{
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [JCToNativeRouteHelp toNativePageWith:JC_ETAG fromType:JC_AppCenter eventTitle:@""];
                });
            }];
        }else if ([call.method isEqualToString:@"getETagConnect"]) {
            // 电子价签连接状态
            [JCETagManager refreshETagConnectStatus];
        }else if ([call.method isEqualToString:@"eTagStatusListener"]) {
            // 电子价签状态监听
            if(arguments != nil && [arguments isKindOfClass:[NSNumber class]]){
                BOOL openETag = ((NSNumber *)arguments).boolValue;
                [JCETagManager etagFlutterAppStatus:openETag];
            }
        } else if ([call.method isEqualToString:@"etagDisConnect"]) {
            // 关闭电子价签连接
            [[JCBluetoothManager sharedInstance] closeConnectedByHand:NO deviceType:JCBluetoothETag];
        } else if ([call.method isEqualToString:@"goodsMixList"]) {
            // 电子价签商品信息、模板混入
            NSString *templateDataStr = arguments[@"goodsMixList"];
            if(STR_IS_NIL(templateDataStr)) return;
            [JCETagManager getET10GoodsMixImagesData:templateDataStr isBatchPreview:NO complate:result];
        }else if ([call.method isEqualToString:@"goodsMixListAll"]) {
            // 电子价签商品信息、模板混入批量预览
            NSString *templateDataStr = arguments[@"goodsMixList"];
            if(STR_IS_NIL(templateDataStr)) return;
            [JCETagManager getET10GoodsMixImagesData:templateDataStr isBatchPreview:YES complate:result];
        } else if ([call.method isEqualToString:@"writeScreenData"]) {
            // 将价签信息写入电子屏幕
            if([arguments isKindOfClass:[NSDictionary class]]){
                NSInteger index = ((NSNumber *)arguments[@"index"]).integerValue;
                NSDictionary *templateInfo = arguments[@"screenData"];
                if(index < 0 || templateInfo.count == 0){
#ifdef DEBUG
          [MBProgressHUD showToastWithMessageDarkColor:@"错误数据已复制到粘贴板"];
          [UIPasteboard generalPasteboard].string = ((NSDictionary *)arguments).xy_toJsonString;
#else
          [MBProgressHUD showToastWithMessageDarkColor:[NSString stringWithFormat:@"%@01",XY_LANGUAGE_TITLE_NAMED(@"app100001006", @"数据异常，同步终止")]];
#endif
                    result(@0);
                    return;
                }else{
                  JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:templateInfo error:nil];
                  [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
                      // Use the updated template data from the download result
                    [JCETagManager writeDataToEtagScreenWith:index data:resultData complate:result];
                  }];
                }
            }else{
                result(@0);
            }
        }else if([call.method isEqualToString:@"endSendPictures"]){
            //终止发送同步数据
            [JCETagManager cancelWriteDataToEtagScreen];
        }else if([call.method isEqualToString:@"shareTemplate"]){
          //分享模板
          NSString *templateStr = arguments[@"template"];
          if(!STR_IS_NIL(templateStr)){
            JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:templateStr error:nil];
            [JCShareTool shareUserTemplate:templateData];
          }
        } else if([call.method isEqualToString:@"resetEtagStatus"]){
            //重置电子价签屏幕状态
            [JCETagManager resetTagStatusComplete:^(NSInteger error) {
                NSLog(@"重置电子价签屏幕成功");
                result(@(error == 0));
            }];
        } else if([call.method isEqualToString:@"getEtagDeviceInfo"]){
          //重置电子价签屏幕状态
          NSDictionary *etagDeviceInfo = [JCBluetoothManager sharedInstance].deviceDict;
          result(etagDeviceInfo == nil?@{}:etagDeviceInfo);
      } else if ([call.method isEqualToString:@"getDeviceId"]) {
            // 获取设备ID
            result([JCKeychainTool getDeviceIDInKeychain]);
        } else if([call.method isEqualToString:@"get_app_config"]){
            NSMutableDictionary *appConfig = [NSMutableDictionary dictionary];
            NSString *userAgent = [[NSUserDefaults standardUserDefaults] objectForKey:@"niimbot-user-agent"];
            NSBundle *bundle = [NSBundle mainBundle];
            [appConfig setValue:ServerURL forKey:@"apiBaseUrl"];
            [appConfig setValue:UN_NIL(userAgent) forKey:@"userAgent"];
            [appConfig setValue:XY_JC_LANGUAGE_REAL forKey:@"language"];
            [appConfig setValue:UN_NIL(m_userModel.token) forKey:@"token"];
            [appConfig setValue:UN_NIL(m_userModel.displayUId) forKey:@"uid"];
            [appConfig setValue:[XYTool deviceName] forKey:@"phone"];
            [appConfig setValue:[JCKeychainTool getDeviceIDInKeychain] forKey:@"deviceId"];
            [appConfig setValue:[UIDevice currentDevice].systemVersion forKey:@"systemVersion"];
            [appConfig setValue:[bundle objectForInfoDictionaryKey:@"CFBundleShortVersionString"] forKey:@"appVserson"];
            [appConfig setValue:@"ios" forKey:@"os"];
            NSString * isAppStore = @"1";
            #ifdef DEBUG
             isAppStore = @"0";
            #endif
            [appConfig setValue:isAppStore forKey:@"isAppStore"];
            result(appConfig);
        }
        else if ([call.method isEqualToString:@"syncGrayConfig"]) {
            // 同步灰度策略
            NSString *grayConfig = arguments[@"grayConfig"];
            JSONModelError *error;
            JCGrayConfigModel *grayConfigModel = [[JCGrayConfigModel alloc] initWithString:grayConfig error:&error];
            [JCGrayManager sharedInstance].grayConfig = grayConfigModel;
            NSLog(@"grayConfig---%@", grayConfigModel);
        } else if ([call.method isEqualToString:@"isExistTemplate"]) {
            NSString *templateId = arguments[@"templateId"];
            [JCTemplateDBManager db_queryTemplateDataById:templateId success:^(JCTemplateData *data) {
                NSNumber *isExit = nil;
                if(data != nil && (STR_IS_NIL(m_userModel.userid) || [m_userModel.userid isEqualToString:data.profile.extrain.userId])){
                    isExit = [NSNumber numberWithBool:YES];
                }else{
                    isExit = [NSNumber numberWithBool:NO];
                }
                result(isExit);
            } failed:^(id msg) {
                result([NSNumber numberWithBool:NO]);
            }];
        } else if ([call.method isEqualToString:@"isExistEtagTemplate"]) {
            NSNumber *isExit = nil;
            NSString *templateId = arguments[@"templateId"];
            JCTemplateData* data = [[JCTemplateData alloc] initWithDictionary:[JCEtagFileSaveManager getDataWithIds:templateId] error:nil];
            if(data != nil && (STR_IS_NIL(m_userModel.userid) || [m_userModel.userid isEqualToString:data.profile.extrain.userId])){
                isExit = [NSNumber numberWithBool:YES];
            }else{
                isExit = [NSNumber numberWithBool:NO];
            }
            result(isExit);
        }else if([call.method isEqualToString:@"hideBottomBar"]){
            UIViewController *currentVC = [XYTool getCurrentVC];
            currentVC.tabBarController.tabBar.hidden = YES;
        }else if([call.method isEqualToString:@"showBottomBar"]){
            UIViewController *currentVC = [XYTool getCurrentVC];
            currentVC.tabBarController.tabBar.hidden = false;
        } else if ([call.method isEqualToString: @"postCatchedException"]) {
            NSString *crashDetail = call.arguments[@"crash_detail"];
            NSString *crashMessage = call.arguments[@"crash_message"];
            NSDictionary *data = call.arguments[@"crash_data"];
            [self postCatchedException:crashDetail crashMessage:crashMessage data:data];
            result(nil);
        }else if([call.method isEqualToString: @"isCanOpenHistoryTemplate"]){
            NSString *templateVersion = call.arguments[@"templateVersion"];
            BOOL canOpen = [JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:templateVersion];
            result(canOpen == true ? @"1" : @"0");
        }else if([call.method isEqualToString: @"closeNps"]){
            [[NSNotificationCenter defaultCenter] postNotificationName:JCCloseNps object:nil];
        } else if([call.method isEqualToString: @"createAdvanceQRCode"]){//通过画板元素面板闯将高级二维码
            NSString *codeType = arguments;
            NSString *appId = @"";
            NSString *tableName = @"";
            if([codeType isEqualToString:@"liveCode"]){
                appId = CAPAppTrackID_LiveCode;
                tableName = TABLE_LABEL_ACTIVITY_CODE_INFO;
            }else{
                appId = UniAppTrackID_FormCode;
                tableName = TABLE_LABEL_FORM_CODE_INFO;
            }
            if(![self isCanResiveEvent]) return;
            NSString *path = [NSString stringWithFormat:@"?codeId=%@&from=canvas",UN_NIL(@"")];
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_CLEAN
                                                                object:nil
                                                              userInfo:nil];
            [[NBCAPMiniAppManager sharedInstance] checkThenOpenMiniApp:appId
                                                             keepAlive:NO
                                                               subpath:path
                                                                 extra:@{}
                                                         replacedAppId:nil
                                                          dataReceived:^(NSDictionary *receiveData) {
                JCActivityCodeModel *model = receiveData[@"data"];
                if(model.deleted.integerValue == 1){
                    NSLog(@"高级二维码信息已被删除");
                }
                NSMutableDictionary *dataDic = model.toFlutterDictionary.mutableCopy;
                NSDictionary *resultData = @{@"type":receiveData[@"type"],@"data":dataDic == nil?@{}:dataDic};
                NSLog(@"小程序返回数据%@",resultData.xy_toJsonString);
                if([receiveData[@"type"] isEqualToString:@"changed"]){
                    NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:tableName dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",model.codeId]];
                    if(dbcodeModelArr.count == 0) return;
                }
                if ([JCAppEventChannel shareInstance].eventSink) {
                    [JCAppEventChannel shareInstance].eventSink(@{@"action":@"advanceQRCode",@"advanceQRCodeInfo":resultData});
                }
                //                result(resultData);
            }];
        }else if([call.method isEqualToString: @"updateAdvanceQRCodeToNative"]){//通过画板元素面板闯将高级二维码
          NSDictionary *liveCodeInfo = call.arguments;
          JCActivityCodeModel *codeModel = [[JCActivityCodeModel alloc] initWithDictionary:liveCodeInfo error:nil];
          NSString *dbName = TABLE_LABEL_ACTIVITY_CODE_INFO;
          if(codeModel.isForm.integerValue == 1){
            dbName = TABLE_LABEL_FORM_CODE_INFO;
          }
          [self updateLiveCodeToDB:codeModel dbName:dbName];
        }
        else if([call.method isEqualToString: @"changeAdvanceQRCode"]){//通过双击或属性区域更新高级二维码
            NSString *templateVersion = call.arguments[@"templateVersion"];
            BOOL canOpen = [JCExcelTransUtil isCanOpenCanvasWithTemplateVersion:templateVersion];
            result(canOpen == true ? @"1" : @"0");
        }
        else if([call.method isEqualToString: @"previewAdvanceQRCode"]){//通过属性区域预览高级二维码
            NSString *codeType = arguments[@"codeType"];
            NSString *codeId = arguments[@"codeId"];
            if(STR_IS_NIL(codeId) || STR_IS_NIL(codeType)) return;
            NSString *appId = @"";
            NSString *path = @"";
            if([codeType isEqualToString:@"liveCode"]){
                appId = CAPAppTrackID_LiveCode;
                path = [NSString stringWithFormat:@"preview/%@?from=canvas",UN_NIL(codeId)];
            }else{
                appId = UniAppTrackID_FormCode;
                path = [NSString stringWithFormat:@"edit/preview/%@?from=canvas",UN_NIL(codeId)];
            }
            [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_CLEAN
                                                                object:nil
                                                              userInfo:nil];
            [[NBCAPMiniAppManager sharedInstance] checkThenOpenMiniApp:appId
                                                             keepAlive:NO
                                                               subpath:path
                                                                 extra:@{}
                                                         replacedAppId:nil
                                                          dataReceived:^(NSDictionary *receiveData) {
                JCActivityCodeModel *model = receiveData[@"data"];
                NSMutableDictionary *dataDic = model.toFlutterDictionary.mutableCopy;
                NSDictionary *resultData = @{@"type":receiveData[@"type"],@"data":dataDic == nil?@{}:dataDic};
                NSLog(@"小程序返回数据%@",resultData.xy_toJsonString);
                result(resultData);
            }];
        }
        else if([call.method isEqualToString: @"getAdvanceQRCodeInfo"]){//通过ID获取高级二维码
            NSString *codeType = arguments[@"codeType"];
            NSString *codeId = arguments[@"codeId"];
            NSDictionary *advanceQRCodeInfo = @{};
            NSString *tableName = @"";
            if([codeType isEqualToString:@"form"]){
                tableName = TABLE_LABEL_FORM_CODE_INFO;
            }else{
                tableName = TABLE_LABEL_ACTIVITY_CODE_INFO;
            }
            NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:tableName dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeId]];
            if(dbcodeModelArr.count > 0){
                JCActivityCodeModel *codeInfoModel = dbcodeModelArr.firstObject;
                advanceQRCodeInfo = codeInfoModel.toFlutterDictionary;
            }
            result(advanceQRCodeInfo);
        }else if([call.method isEqualToString:@"getAdvanceQRCodeCaches"]){//根据遍历到的高级二维码ID 批量获取详情
            NSString *liveCodeIds = arguments[@"liveCodeIds"];
            NSString *formIds = arguments[@"formIds"];
            NSMutableArray *advanceQRCodeMapInfos = [NSMutableArray array];
            for (NSString *idStr in [liveCodeIds componentsSeparatedByString:@","]) {
                NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_ACTIVITY_CODE_INFO dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",idStr]];
                if(dbcodeModelArr.count > 0){
                    JCActivityCodeModel *codeInfoModel = dbcodeModelArr.firstObject;
                    [advanceQRCodeMapInfos addObject:codeInfoModel.toFlutterDictionary];
                }
            }
            for (NSString *idStr in [formIds componentsSeparatedByString:@","]) {
                NSArray *dbcodeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_FORM_CODE_INFO dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",idStr]];
                if(dbcodeModelArr.count > 0){
                    JCActivityCodeModel *codeInfoModel = dbcodeModelArr.firstObject;
                    [advanceQRCodeMapInfos addObject:codeInfoModel.toFlutterDictionary];
                }//
            }
            result(advanceQRCodeMapInfos);
        }else if([call.method isEqualToString:@"nativeTemplateNeedRefresh"]){
            [self postNotification:FLUTTER_TEMPLATE_CHANGED];
        }else if([call.method isEqualToString:@"checkDangerRecord"]){
          [self postNotification:@"checkLedgerNotification"];
        }else if([call.method isEqualToString:@"printCommodity"]){
          [self postNotification:JC_PRINT_COMPLATE];
        }
        else if([call.method isEqualToString:@"sendCapGenerateTemplatePreviewEvent"]){
          NSString *base64ImageStr = call.arguments;
          [self postNotification:@"JCPrintSettingAlertPreviewImageRefresh" withObject:base64ImageStr];
        }
        else if([call.method isEqualToString:@"downloadTemplateDetail"]){//静默加载批量打印模板资源
            NSArray *ids = arguments[@"templateids"];
            if(ids.count > 0){
                //TODO 多线程队列处理模板详情加载
                [[JCBatchPrintSourceManager sharedManager] downloadBatchPrintSource:ids];
            }

        }else if([call.method isEqualToString:@"cancelDownloadTemplateDetail"]){//取消未开始的静默加载
            [[JCBatchPrintSourceManager sharedManager] cancelDownloadWith:nil];
        }else if([call.method isEqualToString:@"checkTemplateInfoComplate"]){//检查模板资源完整性
            NSArray *ids = arguments;
           [[JCBatchPrintSourceManager sharedManager] checkBatchPrintSource:ids completion:^(BOOL sourceIsComplete) {
             result(@(sourceIsComplete?1:0));
          }];
        }
        else if([call.method isEqualToString:@"removeTemplateDetail"]){//移除下载
            NSArray *ids = arguments;
            [[JCBatchPrintSourceManager sharedManager] cancelDownloadWith:ids];
        }else if([call.method isEqualToString:@"unVipBatchPrintCount"]){//检查模板资源完整性
            NSString *userId = m_userModel.userId;
            NSString *batchPrintCountKey = [NSString stringWithFormat:@"batchPrintCout_%@",userId];
            NSInteger batchPrintCount = [[NSUserDefaults standardUserDefaults] integerForKey:batchPrintCountKey];
            result(@(batchPrintCount));
        }
        else if([call.method isEqualToString:@"nativePersonalTemplateSearchHistory"]){
            NSString *historyPath = [NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES).firstObject stringByAppendingPathComponent:@"myTagHistory.plist"];
            NSMutableArray *array  = [NSMutableArray arrayWithContentsOfFile:historyPath];
            [@[] writeToFile:historyPath atomically:YES];
            if(array == nil){
                result(@[]);
            }else{
                result(array);
            }
        }else if([call.method isEqualToString:@"toNpsAlert"]){
          if (![arguments isKindOfClass:[NSDictionary class]]) {
               return;
           }

           NSString *source = arguments[@"source"];
           id isUniAppValue = arguments[@"isUniApp"];
           NSString *uniAppId = arguments[@"uniAppId"];

           BOOL isUniApp = NO;
           if ([isUniAppValue respondsToSelector:@selector(boolValue)]) {
               isUniApp = [isUniAppValue boolValue];
           }

           // 安全转换为非空字符串
           NSString *safeSource = [source isKindOfClass:[NSString class]] ? source : @"";
           NSString *safeUniAppId = [uniAppId isKindOfClass:[NSString class]] ? uniAppId : @"";

           NSString *webKey = isUniApp ? NPSMiniWebKey : NPSWebKey;

           JCNPSWebviewAlert *alert = [[JCNPSWebviewAlert alloc] initWithFrame:CGRectZero
                                                                        webKey:webKey
                                                                           sku:@""
                                                                        source:safeSource];

           alert.uniAppId = safeUniAppId;

           // 确保 show 在主线程调用
           dispatch_async(dispatch_get_main_queue(), ^{
               [alert show];
           });
        }else if([call.method isEqualToString:@"getSPData"]){
            NSArray *requestKey = arguments;
            NSString *requestTime = [[XYCenter sharedInstance] getRequestServiceTime:[NSString stringWithFormat:@"flutter.%@",requestKey]];
            result(requestTime);
        }else if([call.method isEqualToString:@"getMaxSupportTemplateVersion"]){
            result(MaxSupportJSONVersion);
        }
        else if([call.method isEqualToString:@"finishFlutterPages"]){
            NSMutableArray *navPushControllers = [NSMutableArray array];
            for (UIViewController *viewController in [XYTool getCurrentVC].navigationController.viewControllers) {
                if(![viewController isKindOfClass:[FBFlutterViewContainer class]]){
                    if([viewController isKindOfClass:[self class]] && ![viewController isEqual:self]){
                        continue;
                    }else{
                        [navPushControllers addObject:viewController];
                    }
                    //最顶层的页面不需要关闭
                }else if([viewController isEqual:[XYTool getCurrentVC].navigationController.viewControllers.lastObject]){
                    [navPushControllers addObject:viewController];
                }else if([viewController isEqual:[XYTool getCurrentVC].navigationController.jk_rootViewController]){
                    [navPushControllers addObject:viewController];
                }
            }
            [XYTool getCurrentVC].navigationController.viewControllers = navPushControllers;
            result(@(true));
        } else if ([call.method isEqualToString:@"getAppInfo"]) {
            NSBundle *bundle = [NSBundle mainBundle];
            NSDictionary *rawInfo = @{
                @"APP Version": [bundle objectForInfoDictionaryKey:@"CFBundleShortVersionString"],
                @"Build Number": [bundle objectForInfoDictionaryKey:(NSString *)kCFBundleVersionKey],
                @"System Version": [UIDevice currentDevice].systemVersion,
                @"Model": [XYTool deviceName],
                @"DeviceID": [JCKeychainTool getDeviceIDInKeychain],
            };
            NSArray *keys = @[@"APP Version", @"Build Number", @"System Version", @"Model", @"DeviceID", @"Etag Version", @"Environment", @"Skia Version", @"UserID", @"Code Branch", @"BuildTime", @"UID"];
            NSMutableDictionary *info = [NSMutableDictionary dictionaryWithDictionary:rawInfo];
#ifdef DEBUG
      info[@"Etag Version"] = @"v1.0";
      info[@"Environment"] = [JCLaboratoryHelp getAppLaboratoryEnv] == 0 ? @"Online" : @"Test";
      NSString *skiaRenderVersion = [ImageLibraryBridge getSkiaRenderVersion];
      if (skiaRenderVersion != nil && skiaRenderVersion.length > 0) {
        info[@"Skia Version"] = skiaRenderVersion;
      }

      if ([XYCenter checkIsLogin]) {
        info[@"UserID"] = [XYCenter sharedInstance].userModel.userid;
      }
      static NSDictionary *dictionary = nil;
      if (dictionary == nil) {
        dictionary = [NSDictionary dictionaryWithContentsOfFile: [[NSBundle mainBundle] pathForResource: @"BuildInfo" ofType: @"plist"]];
      }
      NSString * branchInfo = [dictionary objectForKey:@"GitInfo"];
      if (branchInfo != nil && [branchInfo isKindOfClass:[NSString class]] && ![branchInfo containsString:@"GitInfo"] ) {
        NSString *result = [branchInfo stringByReplacingOccurrencesOfString:@"origin/" withString:@""];
        info[@"Code Branch"] = result;
      }
      NSString *buildTime = [dictionary objectForKey:@"BuildTime"];
      if (buildTime != nil && [buildTime isKindOfClass:[NSString class]] && ![buildTime containsString:@"BuildTime"]) {
        info[@"BuildTime"] = [dictionary objectForKey:@"BuildTime"];
      }
      info[@"RegistrationId"] = [[NSUserDefaults standardUserDefaults] objectForKey:@"jpushRegistrationID"];
#endif
            if ([XYCenter checkIsLogin]) {
                info[@"UID"] = [XYCenter sharedInstance].userModel.displayUId;
            }


            // 获取所有的keys插入新的key，保证存储顺序
            info[@"keys"] = [[info.allKeys sortedArrayUsingComparator:^NSComparisonResult(NSString *key1, NSString *key2) {
                if ([keys indexOfObject:key1] > [keys indexOfObject:key2]) {
                    return (NSComparisonResult)NSOrderedDescending;
                }

                if ([keys indexOfObject:key1] < [keys indexOfObject:key2]) {
                    return (NSComparisonResult)NSOrderedAscending;
                }
                return (NSComparisonResult)NSOrderedSame;
            }] componentsJoinedByString:@","];


            result(info);
        }else if([call.method isEqualToString:@"getIndustryTemplateDetail"]){
            NSDictionary *industryTemplateInfo = arguments[@"templateData"];
            NSString *templateId = industryTemplateInfo[@"id"];
            __block JCTemplateData *templateData = [[JCTemplateData alloc] initWithDictionary:arguments[@"templateData"] error:nil];
            if(!STR_IS_NIL(templateData.profile.extrain.labelId)){
              uploadMostRecentlyUsedTemplate(templateData.profile.extrain.labelId);
            }
            [JCTemplateImageManager downLoadImagesForData:templateData options:DownAll complete:^(JCTemplateData *resultData) {
                // Use the updated template data from the download result
                if (resultData) {
                    templateData = resultData;
                }
                [JCTemplateDBManager db_queryTemplateDataById:templateData.idStr success:^(JCTemplateData *model) {
                    [model resetTemplateLocalPath];
                    NSString *jsonString = [model toJSONString];
                    result(jsonString);
                } failed:^(id msg) {
                    result(@"");
                }];
            }];
        }else if([call.method isEqualToString:@"requestGeetestVerify"]){
            NSString *regionCode = arguments[@"regionCode"];
            NSString *phone = arguments[@"phone"];
            BOOL mustCheck = [arguments[@"mustCheck"] boolValue];
            BOOL needGeetestVerify = [[XYCenter sharedInstance] needGeetestVerify:regionCode];
            if((STR_IS_NIL(phone) || STR_IS_NIL(regionCode) || !needGeetestVerify) && !mustCheck){
                result(@{@"result":@true,@"response":@""});
            }else{
                NSMutableDictionary * resultInfo = [[NSMutableDictionary alloc]init];
                JCGTCaptchaTools * tools = [JCGTCaptchaTools shareInstance];
                [tools startVerify];
                [tools setResultBlock:^(NSDictionary * data) {
                    if(data != nil){
                        [resultInfo addEntriesFromDictionary:@{@"result":@true}];
                        [resultInfo addEntriesFromDictionary:@{@"response":data.xy_toJsonString}];
                        result(resultInfo);
                    }else{
                        //目前只有关闭图层会回调 验证失败不会回调
                        result(@{@"result":@false,@"response":@""});
                    }
                }];
            }
        }else if ([call.method isEqualToString:@"changePrintData"]){
            NSString *data = arguments[@"templateJson"];
            NSError *error;
            JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:data error:&error];
            [[NSNotificationCenter defaultCenter] postNotificationName:JCRefreshPrintSettingPage object:templateData];
        } else if ([call.method isEqualToString:@"getLabelShopLink"]){
            NSString *oneCode = arguments[@"oneCode"];
            NSArray *oneCodeArr = [oneCode componentsSeparatedByString:@","];
            if (IsNotEmptyArray(oneCodeArr)) {
                oneCode = [oneCodeArr firstObject];
            }
            NSString *jumpSource = arguments[@"jumpSource"];
            NSString *loadUrl;
            if(!STR_IS_NIL([XYCenter sharedInstance].shopHomeHost)) {
                loadUrl = [[XYCenter sharedInstance].shopHomeHost stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/purchase/:%@?machine_id=%@&type=%@" : @"#/detail?barCode=%@&machine_id=%@&type=%@", oneCode, JC_CURRENT_CONNECTED_PRINTER, @"1"];
            } else {
                loadUrl = [ShopURL stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/purchase/:%@?machine_id=%@&type=%@" : @"#/detail?barCode=%@&machine_id=%@&type=%@", oneCode, JC_CURRENT_CONNECTED_PRINTER, @"1"];
            }
            result(loadUrl);
        } else if ([call.method isEqualToString:@"notifyEnterC1Page"]) {
            // C1进入
            [[JCC1Manager sharedInstance] enterC1Page];
        } else if ([call.method isEqualToString:@"notifyBackFromC1Page"]) {
            // C1退出
            [[JCC1Manager sharedInstance] exitC1Page];
        } else if([call.method isEqualToString:@"toC1HomePage"]){
            float delayTime = 0;
            if(!xy_isLogin) {
                delayTime = 1;
            }
            [[JCLoginManager sharedInstance] checkLogin:^{

            } viewController:[XYTool getCurrentVC] loginSuccessBlock:^{
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [JCToNativeRouteHelp toNativePageWith:JC_C1 fromType:JC_AppCenter eventTitle:@""];
                });
            }];
        } else if ([call.method isEqualToString:@"discoverBluetoothPrinterFinished"]) {
//          // 搜索过程结束回调
           [JCBluetoothManager sharedInstance].isFirstBluetoothAlertSearching = NO;
        } else if ([call.method isEqualToString:@"discoverWifiPrinterFinished"]) {
            // WIFI搜索结束
            [[JCWifiManager shareInstance] discoverWifiPrinterFinished];
            // Call the fetchAndUpdatePrinterAliases method in JCBluetoothManager
            [[JCBluetoothManager sharedInstance] fetchAndUpdatePrinterAliases];
        } else if ([call.method isEqualToString:@"disconnectCallback"]) {
            // 此处的返回可能是主动断开也可能是被动，但是代表打印机断开事件处理
            // 断开设备处理、状态重置、通知
            [[JCBluetoothManager sharedInstance] connectFailedOrDisconnect:[JCBluetoothManager sharedInstance].connectedModel];
//            // 被动断开连接回调
//            [[JCBluetoothManager sharedInstance] closeConnectedByHand:NO deviceType:JCBluetoothNormal];
        } else if ([call.method isEqualToString:@"electricityCallback"]) {
            NSInteger intPower = [arguments integerValue];
            [[JCBluetoothManager sharedInstance] electricityCallback:intPower];
        } else if ([call.method isEqualToString:@"coverStatusCallback"]) {
            BOOL isCoverClosed = [arguments boolValue];
            [[JCBluetoothManager sharedInstance] coverStatusCallback:isCoverClosed];
        } else if ([call.method isEqualToString:@"paperStatusCallback"]) {
          BOOL isPaperInstalled = [arguments boolValue];
          [[JCBluetoothManager sharedInstance] paperStatusCallback:isPaperInstalled];
        } else if ([call.method isEqualToString:@"paperRfidStatusCallback"]) {
          BOOL isPaperRfid = [arguments boolValue];
          [[JCBluetoothManager sharedInstance] paperRfidStatusCallback:isPaperRfid];
        } else if ([call.method isEqualToString:@"ribbonStatusCallback"]) {
          BOOL isRibbonInstalled = [arguments boolValue];
          [[JCBluetoothManager sharedInstance] ribbonStatusCallback:isRibbonInstalled];
        } else if ([call.method isEqualToString:@"ribbonRfidStatusCallback"]) {
          BOOL isRibbonRfid = [arguments boolValue];
          [[JCBluetoothManager sharedInstance] ribbonRfidStatusCallback:isRibbonRfid];
        } else if ([call.method isEqualToString:@"syncOutOfService"]) {
            BOOL isOutOfService = [arguments[@"isOutOfService"] boolValue];
            if(isOutOfService) {
              m_currentServerState = ServerState_Stop;
              m_currentServerStateDetail = @"";
            }
        } else if([call.method isEqualToString:@"refreshNativeTemplateList"]){
          [self postNotification:TEMPLATE_CHANGED];
        } else if([call.method isEqualToString:@"downloadTemplateFonts"]){
          NSString *templateStrValue = arguments[@"template"];
          JCTemplateData *templateData = [[JCTemplateData alloc] initWithString:templateStrValue error:nil];
          [JCTemplateFunctionHelper checkFontDownload:templateData complate:^(){
            result(@YES);
          }];
        }  else if ([call.method isEqualToString:@"checkDebugMode"]) {
          BOOL isDebug = NO;
#ifdef DEBUG
      isDebug = YES;
#endif
      result(@(isDebug));
    } else if ([call.method isEqualToString:@"setPrinterReset"]) {
        // C1线号机恢复出厂设置
        [[JCC1Manager sharedInstance] setPrinterResetWithResult:^(int resetResult) {
            result(@(resetResult));
        }];
    } else if ([call.method isEqualToString:pushToRoute]) {
      [self pushToRoute:arguments];
    } else if ([call.method isEqualToString:@"updateLabelUsedRecord"]) {
      NSString *labelId = arguments[@"labelId"];
      uploadMostRecentlyUsedTemplate(labelId);
    }  else {
      result(FlutterMethodNotImplemented);
    }
  }];
}
static BOOL isResiveEvent = NO;
- (BOOL)isCanResiveEvent{
  if(!isResiveEvent){
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      isResiveEvent = NO;
    });
    isResiveEvent = YES;
    return YES;
  }else{
    return NO;
  }
}

- (void)postCatchedException:(NSString *)crashDetail crashMessage: (NSString *)crashMessage data:(NSDictionary *)data {
  if (crashDetail == nil || crashDetail == NULL) {
    crashMessage = @"";
  }
  if ([crashDetail isKindOfClass:[NSNull class]]) {
    crashMessage = @"";
  }
  NSArray *stackTraceArray = [crashDetail componentsSeparatedByString:@""];
  if(data == nil){
    data = [NSMutableDictionary dictionary];
  }

  [Bugly reportExceptionWithCategory:5 name:crashMessage reason:@" " callStack:stackTraceArray extraInfo:data terminateApp:NO];
}

- (void)goToNativePage:(FlutterResult)result arguments:(NSDictionary *)arguments  controller:(FlutterViewController *)controller {
  NSString *vcName = arguments[@"vcName"];
  UINavigationController *navi = (UINavigationController*)XY_KEYWindow.rootViewController;

  if ([vcName isEqualToString:@"ToSearchPage"]) {

  } else if ([vcName isEqualToString:@"DeviceDetail"]){

  } else if ([vcName isEqualToString:@"ToMallOrderPage"]){

  } else if ([vcName isEqualToString:@"ToNiimbotHomePage"]){
    NSNumber *needConnectPrinter = arguments[@"needConnectPrinter"];
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    UIViewController *currentVC = appDelegate.mainVC;
    [UIApplication sharedApplication].keyWindow.rootViewController = currentVC;
    [XYCenter sharedInstance].needGuideConnectPrinter = needConnectPrinter.boolValue;
  }else if ([vcName isEqualToString:@"sourcePage"]){
    UIViewController *currentVC = [XYTool getCurrentVC];
    XYNavigationController *nav = (XYNavigationController *)currentVC.navigationController;
    NSNumber *needConnectPrinter = arguments[@"needConnectPrinter"];
    if(needConnectPrinter.boolValue){
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:nav isFromHome:NO];
      });
    }
    NSString *deriction = arguments[@"popDeriction"];
    if (controller.navigationController == nil && [controller isKindOfClass:[FlutterViewController class]]) {
      [controller dismissViewControllerAnimated:YES completion:^{
        if ([controller isKindOfClass:[FBFlutterViewContainer class]] && [((FBFlutterViewContainer *)controller).name isEqualToString:@"login"]) {
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            //                        [[JCLoginManager sharedInstance] cleanLoginSuccessBlock];
          });
        }
      }];
    }else{
      if([deriction isEqualToString:@"left"]){
        [controller.navigationController popViewControllerAnimated:YES];
        //                [self.methodChannel setMethodCallHandler:nil];
      }else if([deriction isEqualToString:@"top"]){
        [controller.navigationController popViewControllerAnimated:NO];
        //                [self.methodChannel setMethodCallHandler:nil];
      }
    }
    NSNumber *needRefreshUser = arguments[@"needRefreshUser"];
    if(needRefreshUser != nil && needRefreshUser.boolValue){
      [[XYCenter sharedInstance] getNewUserModelInfo:^(__kindof YTKBaseRequest *request, id model) {

      } failure:^(NSString *msg, id model) {

      } isNeedRefreshLoginInfo:NO];
    }
  }
}

//首页轮播图点击
- (void)goToMessageDetail:(id)arguments {
  UIViewController *currentVC = [XYTool getCurrentVC];
  XYNavigationController *nav = (XYNavigationController *)currentVC.navigationController;
  NSNumber *linkRouteType = arguments[@"linkRouteType"];
  NSString *linkTitle = arguments[@"linkTitle"];
  NSString *linkUrl = arguments[@"linkUrl"];
  //        JC_TrackWithparms(@"click",@"003_081_001",(@{@"b_name":UN_NIL(model.name),@"b_banner_key":UN_NIL(model.xyid)}));
  if([linkRouteType isEqualToNumber:@3]){
    //        [JCRecordTool recordWithAction:ClickIndexBannerShop];
    if(NETWORK_STATE_ERROR){
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
      return ;
    }
    JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:linkUrl];
    vc.entrance_type_id = @"3";
    vc.jumpSource = @"";
    [nav pushViewController:vc animated:YES];
  }else if([linkRouteType isEqualToNumber:@2]){
    NSURL *url = [NSURL URLWithString:linkUrl];
    if([[UIApplication sharedApplication] canOpenURL:url]) {
      [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:^(BOOL success) {}];
    }else{
      NSURL *URL = [NSURL URLWithString:linkUrl];
      [[UIApplication sharedApplication] openURL:URL options:@{} completionHandler:^(BOOL success) {
        //  回调
      }];
    }
  }else if([linkTitle isEqualToString:@"意见反馈"]){
    JCQAViewController *vc = [[JCQAViewController alloc] init];
    [nav pushViewController:vc animated:YES];
  }else{
    if(linkUrl.length > 0){
      if([linkUrl hasPrefix:@"niimbot://app"]){
        //根据路由跳转到应用内界面
        [JCToNativeRouteHelp toNativePageWith:linkUrl fromType:JC_Home_Banner eventTitle:linkTitle];
      }else{
        if(NETWORK_STATE_ERROR){
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return ;
        }

        XYWKWebViewController *c = [[XYWKWebViewController alloc] initWithUrl:linkUrl];
        c.title = linkTitle;
        //                    c.isSupportShare = [model isCouldShare];
        [nav pushViewController:c animated:YES];
      }

    }
  }

}


- (void)pushToRoute:(id)arguments {
  // 判断 arguments 是否为 NSDictionary 类型
  if (![arguments isKindOfClass:[NSDictionary class]]) {
    NSLog(@"Invalid arguments: %@", arguments);
    return;
  }

  NSDictionary *argDict = (NSDictionary *)arguments;

  // 安全提取参数
  NSString *linkRouteType = [argDict[@"typeCode"] isKindOfClass:[NSString class]] ? argDict[@"typeCode"] : @"";
  NSString *linkUrl = [argDict[@"url"] isKindOfClass:[NSString class]] ? argDict[@"url"] : @"";
  NSString *linkTitle = [argDict[@"title"] isKindOfClass:[NSString class]] ? argDict[@"title"] : @"";

  // BOOL 类型判断，兼容 NSNumber 或字符串
  BOOL canShare = NO;
  id canShareValue = argDict[@"canShare"];
  if ([canShareValue isKindOfClass:[NSNumber class]]) {
    canShare = [canShareValue boolValue];
  } else if ([canShareValue isKindOfClass:[NSString class]]) {
    canShare = [canShareValue boolValue];
  }

  UIViewController *currentVC = [XYTool getCurrentVC];
  XYNavigationController *nav = (XYNavigationController *)currentVC.navigationController;
  

  if ([linkRouteType isEqualToString:@"3"]) {
    XYWeakSelf
    if (!xy_isLogin) {
      [[JCLoginManager sharedInstance] checkLogin:^{
        // 未登录回调
      } viewController:currentVC loginSuccessBlock:^{
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
          JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:linkUrl];
          vc.entrance_type_id = @"3";
          vc.jumpSource = @"y_page_main";
          [JCAppMethodChannel presentOrPushViewController:vc completion:nil];
        });
      }];
      return;
    }

    [JCRecordTool recordWithAction:ClickIndexBannerShop];

    if (NETWORK_STATE_ERROR) {
      [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
      return;
    }

    JCShopNormalVC *vc = [[JCShopNormalVC alloc] initWithShopAppointUrl:linkUrl];
    vc.entrance_type_id = @"3";
    vc.jumpSource = @"y_page_main_banner";
    [JCAppMethodChannel presentOrPushViewController:vc completion:nil];

  } else if ([linkRouteType isEqualToString:@"2"]) {
    NSURL *url = [NSURL URLWithString:linkUrl];
    if ([[UIApplication sharedApplication] canOpenURL:url]) {
      [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    }

  } else if ([linkTitle isEqualToString:@"固定资产小程序"]) {
    void (^gotoNMFAVC)(void) = ^{
      [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"getAuthCode" arguments:nil result:^(id value) {
        if (!STR_IS_NIL(value)) {
          NSString *loadUrl = [NSString stringWithFormat:@"%@?authCode=%@", linkUrl, value];
          NMFAWebViewController *webViewController = [[NMFAWebViewController alloc] init];
          webViewController.isSupportShare = NO;
          [webViewController loadUrl:loadUrl];
          [JCAppMethodChannel presentOrPushViewController:webViewController completion:nil];
        }
      }];
    };

    if (!xy_isLogin) {
      [[JCLoginManager sharedInstance] checkLogin:^{
      } viewController:currentVC loginSuccessBlock:^{
        if (!STR_IS_NIL(linkUrl)) {
          gotoNMFAVC();
        }
      }];
      return;
    }

    if (!STR_IS_NIL(linkUrl)) {
      gotoNMFAVC();
    }

  } else if ([linkTitle isEqualToString:@"意见反馈"]) {
    JCQAViewController *vc = [[JCQAViewController alloc] init];
    [nav pushViewController:vc animated:YES];

  } else {
    if (linkUrl.length > 0) {
      if ([linkUrl hasPrefix:@"niimbot://app"]) {
        if ([linkUrl hasPrefix:@"niimbot://app/uniapp"]) {
          NSURLComponents *components = [NSURLComponents componentsWithString:linkUrl];
          NSString *appId = nil;
          NSString *path = nil;

          for (NSURLQueryItem *item in components.queryItems) {
              if ([item.name isEqualToString:@"appId"]) {
                  appId = item.value;
              } else if ([item.name isEqualToString:@"path"]) {
                  path = item.value;
              }
          }

          if (appId.length > 0) {
              // 调用小程序 SDK 跳转逻辑
            [[NBCAPMiniAppManager sharedInstance] checkThenOpenMiniApp:appId
                                                             keepAlive:NO
                                                               subpath:path
                                                                 extra:@{}
                                                         replacedAppId:nil
                                                          dataReceived:^(NSDictionary *receiveData) {
            }];
          } else {
              [MBProgressHUD showToastWithMessageDarkColor:@"缺少小程序AppId"];
          }
        } else {
          [JCToNativeRouteHelp toNativePageWith:linkUrl fromType:JC_Home_Banner eventTitle:linkTitle];

        }
      } else {
        if (NETWORK_STATE_ERROR) {
          [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app01139", @"网络异常")];
          return;
        }

        XYWKWebViewController *webVC = [[XYWKWebViewController alloc] initWithUrl:linkUrl];
        webVC.title = linkTitle;
        webVC.isSupportShare = canShare;
        [JCAppMethodChannel presentOrPushViewController:webVC completion:nil];
      }
    }
  }
}


+ (void)presentOrPushViewController:(UIViewController *)targetVC
                         completion:(void(^_Nullable)(void))completion {

    UIViewController *currentVC = [XYTool getCurrentVC];
    UINavigationController *nav = currentVC.navigationController;
    if(nav == nil) {
      nav = [XYTool getCurrentNavVC];
    }

    // 如果 currentVC 本身就是导航控制器
    if ([currentVC isKindOfClass:[UINavigationController class]]) {
        nav = (UINavigationController *)currentVC;
    }

    if (nav) {
        [nav pushViewController:targetVC animated:YES];
        if (completion) {
            completion();
        }
    } else {
        XYNavigationController *presentNav = [[XYNavigationController alloc] initWithRootViewController:targetVC];
        presentNav.modalPresentationStyle = UIModalPresentationFullScreen;
        [currentVC presentViewController:presentNav animated:YES completion:completion];
    }
}

- (void)toShopMall
{

}

/// 搜索所有系列的打印机, 并将搜索结果通知flutter
- (void)searchDevices{
  self.serialSpecifiedDevices = nil;
  [JCBlUETOOTH_MANAGER autoConnectPrinterOrSearchDevices:nil completion:^(NSArray *devices) {
    for (JCBluetoothModel *model in devices) {
      NSLog(@"=====>>>>>>>%@",model.name);
      [JCAppEventChannel.shareInstance eventData:@{
        @"foundPrinter":model.name
      }];
    }
  } endScan:^{

  }];
}

- (void)handleAreNotificationsEnabled:(FlutterResult)result {
  // 检查通知授权状态
  [[UNUserNotificationCenter currentNotificationCenter] getNotificationSettingsWithCompletionHandler:^(UNNotificationSettings *settings) {
    BOOL notificationsEnabled = settings.authorizationStatus == UNAuthorizationStatusAuthorized || settings.authorizationStatus == UNAuthorizationStatusProvisional;
    result(@(notificationsEnabled));
  }];
}


- (void)checkIfContains:(JCBluetoothModel *)model {
  if (self.serialSpecifiedDevices.count == 0) {
    [self.serialSpecifiedDevices addObject:model];
    return;
  }
  NSArray *tempArr = [self.serialSpecifiedDevices copy];
  for (JCBluetoothModel *m in tempArr) {
    if (![m.name isEqualToString:model.name]) {
      [self.serialSpecifiedDevices addObject:model];
      break;
    }
  }
}



- (NSMutableArray *)serialSpecifiedDevices{
  if (!_serialSpecifiedDevices) {
    _serialSpecifiedDevices = [NSMutableArray array];
  }
  return  _serialSpecifiedDevices;
}
- (void)dealloc
{
  NSLog(@"%@---delloc",NSStringFromClass(self.class));
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

// MeProfile 事件汇总
-(void)presentMyDevice:(UIViewController *)currentVC{
  //    if(!JC_IS_CONNECTED_PRINTER){
  //        [[JCBluetoothManager sharedInstance] startBluetoothConnectFrom:(XYNavigationController *)currentVC.navigationController  isFromHome:NO];
  //    }else{
  //        kPreventRepeatClickTime(1);
  //        JCDeviceDetailViewController *vc = [[JCDeviceDetailViewController alloc] init];
  //        [currentVC.navigationController pushViewController:vc animated:YES];
  //    }
}

- (void)pushPrintHistory:(UIViewController *)currentVC{
  JC_TrackWithparms(@"click",@"006_012_162",@{});

  XYWeakSelf
  if(!xy_isLogin){
    [[JCLoginManager sharedInstance] checkLogin:^{
    } viewController:currentVC loginSuccessBlock:^{
      [self pushPrintHistory:currentVC];
    }];
  }else{

    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      NSDictionary *arguments = @{
        @"isPresent": @YES,
        @"token": m_userModel.token
      };
    });
  }
}

- (NSString *)formatKeyValuePairWithKey1:(NSString *)key1
                                  value1:(NSString *)value1
                                     gap:(NSString *)gap
                                   key2:(NSString *)key2
                                 value2:(NSString *)value2 {
    return [NSString stringWithFormat:@"%@: %@%@%@: %@",
            key1 ?: @"", value1 ?: @"", gap ?: @"  ", key2 ?: @"", value2 ?: @""];
}

// MARK: -- 获取最近的使用记录 --
/// 获取最近的标签纸使用记录
/// - Parameters:
///   - machineId: 机型ID
///   - count: 返回记录的数量
///   - successBlock: 回调的block
- (void)getUseRecentTemplateFromMachineId:(NSArray<NSString *> *)machineIds WithLimit:(int)count successBlock:(void(^)(NSArray<JCTemplateData *> *))successBlock {
    NSMutableArray *res = [NSMutableArray array];
    NSSet<NSString *> *machineIdSet = [NSSet setWithArray: machineIds];
    __block NSMutableArray *clouldTemplateModelArr = [NSMutableArray array];
    NSMutableArray *labelUsedIdArr = [NSMutableArray array];

    // Create dispatch group
    dispatch_group_t group = dispatch_group_create();

    // 从DB数据库获取按照时间排序的列表
    NSString *whereUseModel = [NSString stringWithFormat:@"ORDER BY useTime DESC"];
    NSArray *usedModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_USE_INFO dicOrModel:[JCLabelUseModel class] whereFormat:whereUseModel];
    NSMutableArray *usedModelArrReal = usedModelArr.mutableCopy;
    if(machineIds.count == 0){
      usedModelArrReal = [usedModelArr subarrayWithRange:NSMakeRange(0, count)].mutableCopy;
    }
    for (JCLabelUseModel *useInfoModel in usedModelArrReal) {
        dispatch_group_enter(group);

      [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetTemplateDetail" arguments:@{@"templateId" : useInfoModel.templateId} result:^(NSString *str) {
          if([str isKindOfClass:[NSString class]] && !STR_IS_NIL(str)){
            JCTemplateData *templateModel = [[JCTemplateData alloc] initWithString:str error:nil];
            NSSet<NSString *> *labelMachineIdSet = [NSSet setWithArray: [templateModel.profile.machineId componentsSeparatedByString: @","]];
            NSLog(@"系列包含机型:%@ 模版适配机型%@,是否存在交集:%@",machineIds,labelMachineIdSet,[labelMachineIdSet intersectsSet:machineIdSet]?@"是":@"否");
            if (templateModel != nil) {
                templateModel.useTime = useInfoModel.useTime;
                JCTemplateData *_templateModel; // 需要add到clouldTemplateModelArr中的模型
                NSString *labelId; // 标签纸ID
                NSSet<NSString *> *labelMachineIdSet = [NSSet setWithArray: [templateModel.profile.machineId componentsSeparatedByString: @","]];
                if (([templateModel.profile.extrain.templateClass isEqualToString:@"0"] ||
                     ([templateModel.profile.extrain.templateClass isEqualToString:@"1"] &&
                      !STR_IS_NIL(templateModel.profile.extrain.labelId))) &&
                    ((machineIdSet.count != 0 && [labelMachineIdSet intersectsSet:machineIdSet] ||
                      (machineIdSet.count == 0)))) { //  (如果是标签纸 || 模版类型但是存在标签纸) && (符合当前机型 || 不按机型筛选))
                    // 记录当前ID防止模版中存在标签纸重复
                    _templateModel = templateModel;
                    labelId = STR_IS_NIL(templateModel.profile.extrain.labelId) ? _templateModel.idStr : _templateModel.profile.extrain.labelId;
                }

                if (_templateModel) { // 当前是标签纸或者模版中存在标签纸的模型
                    if (![labelUsedIdArr containsObject: labelId]) { // 已包含当前遍历的模型
                        [labelUsedIdArr addObject: labelId];
                        [clouldTemplateModelArr addObject:_templateModel];
                    }
                }
            }
          }
          dispatch_group_leave(group);
        }];
//        [JCTemplateDBManager db_queryTemplateDataById:useInfoModel.templateId success:^(JCTemplateData *value) {
//            JCTemplateData *templateModel = value;
//            if (templateModel != nil) {
//                templateModel.useTime = useInfoModel.useTime;
//                JCTemplateData *_templateModel; // 需要add到clouldTemplateModelArr中的模型
//                NSString *labelId; // 标签纸ID
//                NSSet<NSString *> *labelMachineIdSet = [NSSet setWithArray: [templateModel.profile.machineId componentsSeparatedByString: @","]];
//                if (([templateModel.profile.extrain.templateClass isEqualToString:@"0"] ||
//                     ([templateModel.profile.extrain.templateClass isEqualToString:@"1"] &&
//                      !STR_IS_NIL(templateModel.profile.extrain.labelId))) &&
//                    ((machineIdSet.count != 0 && [labelMachineIdSet intersectsSet:machineIdSet] ||
//                      (machineIdSet.count == 0)))) { // (如果是标签纸 || 模版类型但是存在标签纸) && (符合当前机型 || 不按机型筛选))
//                    // 记录当前ID防止模版中存在标签纸重复
//                    _templateModel = templateModel;
//                    labelId = STR_IS_NIL(templateModel.profile.extrain.labelId) ? _templateModel.idStr : _templateModel.profile.extrain.labelId;
//                }
//
//                if (_templateModel) { // 当前是标签纸或者模版中存在标签纸的模型
//                    if (![labelUsedIdArr containsObject: labelId]) { // 已包含当前遍历的模型
//                        [labelUsedIdArr addObject: labelId];
//                        [clouldTemplateModelArr addObject:_templateModel];
//                    }
//                }
//            }
//            dispatch_group_leave(group);
//        } failed:^(id x) {
//            dispatch_group_leave(group);
//        }];
    }

    // Wait for all async operations to complete
    dispatch_group_notify(group, dispatch_get_main_queue(), ^{
        // 按照时间排序
        NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:@"useTime" ascending:NO];
        NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
        clouldTemplateModelArr = [clouldTemplateModelArr sortedArrayUsingDescriptors:sortDescriptors].mutableCopy;
      NSString *randomString = [XYTool randomElementId];

      dispatch_queue_t queue = dispatch_queue_create([randomString UTF8String],DISPATCH_QUEUE_CONCURRENT);
      // 3.创建一个数目为1的信号量，用于"卡"for循环，等上次循环结束在执行下一次的for循环
      dispatch_async(queue, ^{
        [clouldTemplateModelArr enumerateObjectsUsingBlock:^(JCTemplateData *  _Nonnull _data, NSUInteger idx, BOOL * _Nonnull stop) {
          // 寻找标签纸类型
          dispatch_semaphore_t sema = dispatch_semaphore_create(0);
          if ([_data.profile.extrain.templateClass isEqualToString:@"0"]) {
            _data.multipleBackIndex = 0;
            [res addObject: _data];
            // 满足count次数则提前退出
            dispatch_semaphore_signal(sema);
            if (res.count == count) {
              *stop = true;
            }
          } else if ([_data.profile.extrain.templateClass isEqualToString:@"1"] && !STR_IS_NIL(_data.profile.extrain.labelId)) {
            [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:_data.profile.extrain.labelId barCode:_data.requestBarCode success:^(JCTemplateData *labelData) {
              [res addObject: labelData];
              dispatch_semaphore_signal(sema);
              if (res.count == count) {
                *stop = true;
              }
            } field:^(id x) {
              dispatch_semaphore_signal(sema);
            }];
          }
          dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
          //            });
        }];
        successBlock(res);
      });
    });
}

- (void)getUseRecentTemplateFromMachineIdNew:(NSArray<NSString *> *)machineIds
                                   WithLimit:(int)count
                                successBlock:(void(^)(NSArray<JCTemplateData *> *))successBlock {
    NSMutableArray *res = [NSMutableArray array];
    NSSet<NSString *> *machineIdSet = [NSSet setWithArray: machineIds];
    __block NSMutableArray *clouldTemplateModelArr = [NSMutableArray array];
    NSMutableArray *labelUsedIdArr = [NSMutableArray array];
    __block BOOL shouldBreakFirstLoop = NO; // 新增：用于提前跳出

    // 从DB数据库获取按照时间排序的列表
    NSString *whereUseModel = [NSString stringWithFormat:@"ORDER BY useTime DESC"];
    NSArray *usedModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:TABLE_LABEL_USE_INFO dicOrModel:[JCLabelUseModel class] whereFormat:whereUseModel];
    NSMutableArray *usedModelArrReal = usedModelArr.mutableCopy;
    if(machineIds.count == 0){
        usedModelArrReal = [usedModelArr subarrayWithRange:NSMakeRange(0, count)].mutableCopy;
    }

    __block void (^processNext)(NSInteger);
    processNext = ^(NSInteger idx) {
        // 新增：提前跳出
        if (shouldBreakFirstLoop || idx >= usedModelArrReal.count) {
            // 进入第二个循环
            NSSortDescriptor*sorter=[[NSSortDescriptor alloc]initWithKey:@"useTime" ascending:NO];
            NSMutableArray *sortDescriptors=[[NSMutableArray alloc]initWithObjects:&sorter count:1];
            clouldTemplateModelArr = [clouldTemplateModelArr sortedArrayUsingDescriptors:sortDescriptors].mutableCopy;

            NSString *randomString = [XYTool randomElementId];
            dispatch_queue_t queue = dispatch_queue_create([randomString UTF8String],DISPATCH_QUEUE_CONCURRENT);
            dispatch_async(queue, ^{
                [clouldTemplateModelArr enumerateObjectsUsingBlock:^(JCTemplateData *  _Nonnull _data, NSUInteger idx, BOOL * _Nonnull stop) {
                    dispatch_semaphore_t sema = dispatch_semaphore_create(0);
                    if ([_data.profile.extrain.templateClass isEqualToString:@"0"]) {
                        _data.multipleBackIndex = 0;
                        [res addObject: _data];
                        dispatch_semaphore_signal(sema);
                        if (res.count == count) {
                            *stop = true;
                        }
                    } else if ([_data.profile.extrain.templateClass isEqualToString:@"1"] && !STR_IS_NIL(_data.profile.extrain.labelId)) {
                        [JCLabelInfoMangerHelper getServerLabelInfoWithLabelId:_data.profile.extrain.labelId barCode:_data.requestBarCode success:^(JCTemplateData *labelData) {
                            [res addObject: labelData];
                            dispatch_semaphore_signal(sema);
                            if (res.count == count) {
                                *stop = true;
                            }
                        } field:^(id x) {
                            dispatch_semaphore_signal(sema);
                        }];
                    }
                    dispatch_semaphore_wait(sema, DISPATCH_TIME_FOREVER);
                }];
                if (successBlock) {
                    successBlock(res);
                }
            });
            return;
        }
        JCLabelUseModel *useInfoModel = usedModelArrReal[idx];
        [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"nativeGetTemplateDetail" arguments:@{@"templateId" : useInfoModel.templateId} result:^(NSString *str) {
            if([str isKindOfClass:[NSString class]] && !STR_IS_NIL(str)){
                JCTemplateData *templateModel = [[JCTemplateData alloc] initWithString:str error:nil];
                NSSet<NSString *> *labelMachineIdSet = [NSSet setWithArray: [templateModel.profile.machineId componentsSeparatedByString: @","]];
                if (templateModel != nil) {
                    templateModel.useTime = useInfoModel.useTime;
                    JCTemplateData *_templateModel;
                    NSString *labelId;
                    BOOL hasIntersection = NO;
                    if (([templateModel.profile.extrain.templateClass isEqualToString:@"0"] ||
                         ([templateModel.profile.extrain.templateClass isEqualToString:@"1"] &&
                          !STR_IS_NIL(templateModel.profile.extrain.labelId))) &&
                        ((machineIdSet.count != 0 && [labelMachineIdSet intersectsSet:machineIdSet]) ||
                          (machineIdSet.count == 0))) {
                        _templateModel = templateModel;
                        labelId = STR_IS_NIL(templateModel.profile.extrain.labelId) ? _templateModel.idStr : _templateModel.profile.extrain.labelId;
                        hasIntersection = (machineIdSet.count == 0) ? YES : [labelMachineIdSet intersectsSet:machineIdSet];
                    }
                    if (_templateModel) {
                        if (![labelUsedIdArr containsObject: labelId]) {
                            [labelUsedIdArr addObject: labelId];
                            [clouldTemplateModelArr addObject:_templateModel];
                        }
                        // 新增：如果count==1且有交集，提前跳出
                        if (count == 1 && hasIntersection) {
                            shouldBreakFirstLoop = YES;
                        }
                    }
                }
            }
            // 串行进入下一个
            processNext(idx + 1);
        }];
    };
    processNext(0);
}
// MARK: -- 从本地获取excel文件以及从第三方获取excel文件 --

- (void)openThirdAppEvent:(NSString *)thirdAppSelected withResult:(FlutterResult)result {
  // 清除上一次的监听，防止二次监听
  if (self.fileObserver) {
    [[NSNotificationCenter defaultCenter] removeObserver:self.fileObserver];
    self.fileObserver = nil;
  }
  WEAK_SELF()
  NSURL *thirdUrl = [NSURL URLWithString:[NSString stringWithFormat:@"%@://",[thirdAppSelected isEqualToString:@"qq"] ? @"mqq" : thirdAppSelected]];
  [[UIApplication sharedApplication] openURL:thirdUrl options:@{} completionHandler:^(BOOL success) {}];
  NSNotificationCenter * __weak center = [NSNotificationCenter defaultCenter];
  self.fileObserver = [center addObserverForName:JCNOTICATION_EXCEL_UPLOAD object:nil queue:NSOperationQueue.mainQueue usingBlock:^(NSNotification * _Nonnull note) {
    [center removeObserver:weakSelf.fileObserver];
    weakSelf.fileObserver = nil;
    FlutterStandardTypedData *typedData = [FlutterStandardTypedData typedDataWithBytes:[NSData data]];
    // 判断文件名称是否符合xls,xlsx名称 PDF
    NSArray<NSString *> *components = [note.userInfo[@"fileName"] componentsSeparatedByString:@"."];
    NSArray *fileTypes = @[];
    if([self.fileType isEqualToString:@"pdf"]){
      fileTypes = @[@"pdf"];
    }else if([self.fileType isEqualToString:@"excel"]){
      fileTypes = @[@"xls", @"xlsx"];
    }
    NSString *filePath = note.userInfo[@"filePath"];
    NSInteger fileSize = 0;
    if ([fileTypes containsObject:[components.lastObject lowercaseString]]) {
      NSData *data = [[NSData alloc] initWithContentsOfURL:[NSURL fileURLWithPath:filePath isDirectory:NO]];
      fileSize = data.length;
      typedData = [FlutterStandardTypedData typedDataWithBytes:data];
    }
    NSDictionary *dic = nil;
    if([self.fileType isEqualToString:@"pdf"]){
      NSFileManager *fileManager = [NSFileManager defaultManager];
      NSString *resourcePath = [NSString stringWithFormat:@"%@/%@",RESOURCE_PDF_PATH,note.userInfo[@"fileName"]];
      [fileManager copyItemAtPath:filePath toPath:resourcePath error:nil];
      dic = @{
        @"fileName": note.userInfo[@"fileName"],
        @"filePath": resourcePath,
        @"fileSize": @(fileSize)};
    }else{
      dic = @{
        @"fileName": note.userInfo[@"fileName"],
        @"data": typedData};
    }
    result(dic);
  }];
}

// 二维码转换为高级二维码
- (void)updateLiveCodeToDB:(JCActivityCodeModel *)codeModel dbName:(NSString *)dbName{
  NSArray *codeModelArr = [[JCFMDB shareDatabase:DB_NAME] jc_lookupTable:dbName dicOrModel:[JCActivityCodeModel class] whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
  if (codeModelArr.count > 0) {
    [[JCFMDB shareDatabase:DB_NAME] jc_updateTable:dbName dicOrModel:codeModel whereFormat:[NSString stringWithFormat:@"where codeId = '%@'",codeModel.codeId]];
  } else {
    [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:dbName dicOrModel:codeModel];
  }
}

- (void)openLocalFileWithType:(NSString *)fileType {
  NSArray *documentTypes = @[@"com.microsoft.powerpoint.ppt",
                             @"com.microsoft.word.doc",
                             @"org.openxmlformats.wordprocessingml.document",
                             @"org.openxmlformats.presentationml.presentation",
                             @"public.mpeg-4",
                             @"com.adobe.pdf",
                             @"public.mp3"];
  if([fileType isEqualToString:@"pdf"]){
    documentTypes = @[@"com.adobe.pdf"];
  }else if([fileType isEqualToString:@"excel"]){
    documentTypes = @[@"org.openxmlformats.spreadsheetml.sheet", @"com.microsoft.excel.xls"];
  }
  UIDocumentPickerViewController *documentPickerViewController = [[UIDocumentPickerViewController alloc] initWithDocumentTypes:documentTypes inMode:UIDocumentPickerModeImport];
  documentPickerViewController.delegate = self;
  documentPickerViewController.modalPresentationStyle = UIModalPresentationFormSheet;
  [[XYTool getCurrentVC] presentViewController:documentPickerViewController animated:YES completion:^{
  }];
}

#pragma mark - UIDocumentPickerDelegate
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-implementations"
- (void)documentPicker:(UIDocumentPickerViewController *)controller didPickDocumentAtURL:(NSURL *)url {

  NSString *fileName = url.lastPathComponent;
  NSString *path = url.absoluteString;
  path = [path stringByRemovingPercentEncoding];
  NSMutableString *string = [[NSMutableString alloc] initWithString:path];
  if ([string hasPrefix:@"file://"]) {
    [string replaceOccurrencesOfString:@"file://" withString:@"" options:NSCaseInsensitiveSearch range:NSMakeRange(0, string.length)];
    NSDictionary *dict = @{@"fileName":UN_NIL(fileName),
                           @"filePath":UN_NIL(string)};
    NSData *data = [NSData data];
    // 判断文件名称是否符合xls,xlsx名称
    NSArray<NSString *> *components = [fileName componentsSeparatedByString:@"."];
    NSArray *fileTypes = @[];
    NSString *resourcePath = @"";
    if([self.fileType isEqualToString:@"pdf"]){
      fileTypes = @[@"pdf"];
      NSFileManager *fileManager = [NSFileManager defaultManager];
      resourcePath = [NSString stringWithFormat:@"%@/%@",RESOURCE_PDF_PATH,fileName];
      [fileManager copyItemAtPath:string toPath:resourcePath error:nil];
    }else if([self.fileType isEqualToString:@"excel"]){
      fileTypes = @[@"xls", @"xlsx"];
    }
    if ([fileTypes containsObject:[components.lastObject lowercaseString]] || fileTypes.count == 0) {
      // 从本地沙盒读取文件
      data = [[NSData alloc] initWithContentsOfURL:url];
    }
    self.excelDataBlock(fileName,data,resourcePath);
  }
}

- (BOOL)isSaveFromPrintSetting{
  BOOL isFromPrintSetting = NO;
  UIViewController *viewController = [XYTool getCurrentVC];
  if([viewController isKindOfClass:[FBFlutterViewContainer class]]){
    FBFlutterViewContainer *viewContainer = (FBFlutterViewContainer *)viewController;
    if([viewContainer.name isEqualToString:@"printSetting"]){
      isFromPrintSetting = YES;
    }
  }
  return isFromPrintSetting;
}

#pragma mark - RTL相关方法

+ (void)getRtlSwitchStatus:(NSString *)languageCode callback:(void(^)(BOOL status))callback {
    FlutterMethodChannel *channel = [[JCAppMethodChannel shareInstance] methodChannel];
    [channel invokeMethod:@"getDrawboardRtlSwitchStatus" arguments:languageCode result:^(id result) {
        if (callback) {
            BOOL status = [result boolValue];
            callback(status);
        }
    }];
}

+ (void)setRtlSwitchStatus:(BOOL)status {
    FlutterMethodChannel *channel = [[JCAppMethodChannel shareInstance] methodChannel];
    [channel invokeMethod:@"setDrawboardRtlSwitchStatus" arguments:@(status) result:^(id result) {
        // 设置完成后的回调处理
    }];
}

@end
