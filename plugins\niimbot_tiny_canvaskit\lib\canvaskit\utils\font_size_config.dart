import 'dart:math';

class FontSizeConfig {
  /// 小一
  final String title;

  /// 字号
  final double fontSize;

  /// mm
  final double mm;

  FontSizeConfig(this.title, this.fontSize, this.mm);
}

final List<FontSizeConfig> allFontSizeConfigList = [
  FontSizeConfig("4.0", 4.0, 1.5),
  FontSizeConfig("4.5", 4.5, 1.6),
  FontSizeConfig("5.0", 5.0, 1.8),
  FontSizeConfig("5.5", 5.5, 1.9),
  FontSizeConfig("6.0", 6.0, 2.1),
  FontSizeConfig("6.5", 6.5, 2.3),
  FontSizeConfig("7.0", 7.0, 2.5),
  FontSizeConfig("7.5", 7.5, 2.6),
  FontSizeConfig("8.0", 8.0, 2.8),
  FontSizeConfig("8.5", 8.5, 3.0),
  FontSizeConfig("9.0", 9.0, 3.2),
  FontSizeConfig("9.5", 9.5, 3.4),
  FontSizeConfig("10.0", 10.0, 3.5),
  FontSizeConfig("10.5", 10.5, 3.7),
  FontSizeConfig("11.0", 11.0, 3.9),
  FontSizeConfig("11.5", 11.5, 4.1),
  FontSizeConfig("12.0", 12.0, 4.2),
  FontSizeConfig("12.5", 12.5, 4.4),
  FontSizeConfig("13.0", 13.0, 4.6),
  FontSizeConfig("13.5", 13.5, 4.8),
  FontSizeConfig("14.0", 14.0, 4.9),
  FontSizeConfig("14.5", 14.5, 5.1),
  FontSizeConfig("15.0", 15.0, 5.3),
  FontSizeConfig("15.5", 15.5, 5.5),
  FontSizeConfig("16.0", 16.0, 5.6),
  FontSizeConfig("16.5", 16.5, 5.8),
  FontSizeConfig("17.0", 17.0, 6.0),
  FontSizeConfig("17.5", 17.5, 6.2),
  FontSizeConfig("18.0", 18.0, 6.4),
  FontSizeConfig("18.5", 18.5, 6.5),
  FontSizeConfig("19.0", 19.0, 6.7),
  FontSizeConfig("19.5", 19.5, 6.9),
  FontSizeConfig("20.0", 20.0, 7.1),
  FontSizeConfig("20.5", 20.5, 7.2),
  FontSizeConfig("21.0", 21.0, 7.4),
  FontSizeConfig("21.5", 21.5, 7.6),
  FontSizeConfig("22.0", 22.0, 7.8),
  FontSizeConfig("22.5", 22.5, 7.9),
  FontSizeConfig("23.0", 23.0, 8.1),
  FontSizeConfig("23.5", 23.5, 8.3),
  FontSizeConfig("24.0", 24.0, 8.5),
  FontSizeConfig("24.5", 24.5, 8.6),
  FontSizeConfig("25.0", 25.0, 8.8),
  FontSizeConfig("25.5", 25.5, 9.0),
  FontSizeConfig("26.0", 26.0, 9.2),
  FontSizeConfig("26.5", 26.5, 9.3),
  FontSizeConfig("27.0", 27.0, 9.5),
  FontSizeConfig("27.5", 27.5, 9.7),
  FontSizeConfig("28.0", 28.0, 9.9),
  FontSizeConfig("28.5", 28.5, 10.1),
  FontSizeConfig("29.0", 29.0, 10.2),
  FontSizeConfig("29.5", 29.5, 10.4),
  FontSizeConfig("30.0", 30.0, 10.6),
  FontSizeConfig("30.5", 30.5, 10.8),
  FontSizeConfig("31.0", 31.0, 10.9),
  FontSizeConfig("31.5", 31.5, 11.1),
  FontSizeConfig("32.0", 32.0, 11.3),
  FontSizeConfig("32.5", 32.5, 11.5),
  FontSizeConfig("33.0", 33.0, 11.6),
  FontSizeConfig("33.5", 33.5, 11.8),
  FontSizeConfig("34.0", 34.0, 12.0),
  FontSizeConfig("34.5", 34.5, 12.2),
  FontSizeConfig("35.0", 35.0, 12.3),
  FontSizeConfig("35.5", 35.5, 12.5),
  FontSizeConfig("36.0", 36.0, 12.7),
  FontSizeConfig("36.5", 36.5, 12.9),
  FontSizeConfig("37.0", 37.0, 13.1),
  FontSizeConfig("37.5", 37.5, 13.2),
  FontSizeConfig("38.0", 38.0, 13.4),
  FontSizeConfig("38.5", 38.5, 13.6),
  FontSizeConfig("39.0", 39.0, 13.8),
  FontSizeConfig("39.5", 39.5, 13.9),
  FontSizeConfig("40.0", 40.0, 14.1),
  FontSizeConfig("40.5", 40.5, 14.3),
  FontSizeConfig("41.0", 41.0, 14.5),
  FontSizeConfig("41.5", 41.5, 14.6),
  FontSizeConfig("42.0", 42.0, 14.8),
  FontSizeConfig("42.5", 42.5, 15.0),
  FontSizeConfig("43.0", 43.0, 15.2),
  FontSizeConfig("43.5", 43.5, 15.3),
  FontSizeConfig("44.0", 44.0, 15.5),
  FontSizeConfig("44.5", 44.5, 15.7),
  FontSizeConfig("45.0", 45.0, 15.9),
  FontSizeConfig("45.5", 45.5, 16.1),
  FontSizeConfig("46.0", 46.0, 16.2),
  FontSizeConfig("46.5", 46.5, 16.4),
  FontSizeConfig("47.0", 47.0, 16.6),
  FontSizeConfig("47.5", 47.5, 16.8),
  FontSizeConfig("48.0", 48.0, 16.9),
  FontSizeConfig("48.5", 48.5, 17.1),
  FontSizeConfig("49.0", 49.0, 17.3),
  FontSizeConfig("49.5", 49.5, 17.5),
  FontSizeConfig("50.0", 50.0, 17.6),
  FontSizeConfig("50.5", 50.5, 17.8),
  FontSizeConfig("51.0", 51.0, 18.0),
  FontSizeConfig("51.5", 51.5, 18.2),
  FontSizeConfig("52.0", 52.0, 18.3),
  FontSizeConfig("52.5", 52.5, 18.5),
  FontSizeConfig("53.0", 53.0, 18.7),
  FontSizeConfig("53.5", 53.5, 18.9),
  FontSizeConfig("54.0", 54.0, 19.1),
  FontSizeConfig("54.5", 54.5, 19.2),
  FontSizeConfig("55.0", 55.0, 19.4),
  FontSizeConfig("55.5", 55.5, 19.6),
  FontSizeConfig("56.0", 56.0, 19.8),
  FontSizeConfig("56.5", 56.5, 19.9),
  FontSizeConfig("57.0", 57.0, 20.1),
  FontSizeConfig("57.5", 57.5, 20.3),
  FontSizeConfig("58.0", 58.0, 20.5),
  FontSizeConfig("58.5", 58.5, 20.6),
  FontSizeConfig("59.0", 59.0, 20.8),
  FontSizeConfig("59.5", 59.5, 21.0),
  FontSizeConfig("60.0", 60.0, 21.2),
  FontSizeConfig("60.5", 60.5, 21.3),
  FontSizeConfig("61.0", 61.0, 21.5),
  FontSizeConfig("61.5", 61.5, 21.7),
  FontSizeConfig("62.0", 62.0, 21.9),
  FontSizeConfig("62.5", 62.5, 22.1),
  FontSizeConfig("63.0", 63.0, 22.2),
  FontSizeConfig("63.5", 63.5, 22.4),
  FontSizeConfig("64.0", 64.0, 22.6),
  FontSizeConfig("64.5", 64.5, 22.8),
  FontSizeConfig("65.0", 65.0, 22.9),
  FontSizeConfig("65.5", 65.5, 23.1),
  FontSizeConfig("66.0", 66.0, 23.3),
  FontSizeConfig("66.5", 66.5, 23.5),
  FontSizeConfig("67.0", 67.0, 23.6),
  FontSizeConfig("67.5", 67.5, 23.8),
  FontSizeConfig("68.0", 68.0, 24.0),
  FontSizeConfig("68.5", 68.5, 24.2),
  FontSizeConfig("69.0", 69.0, 24.3),
  FontSizeConfig("69.5", 69.5, 24.5),
  FontSizeConfig("70.0", 70.0, 24.7),
  FontSizeConfig("70.5", 70.5, 24.9),
  FontSizeConfig("71.0", 71.0, 25.0),
  FontSizeConfig("71.5", 71.5, 25.2),
  FontSizeConfig("72.0", 72.0, 25.4),
  FontSizeConfig("72.5", 72.5, 25.6),
  FontSizeConfig("73.0", 73.0, 25.8),
  FontSizeConfig("73.5", 73.5, 25.9),
  FontSizeConfig("74.0", 74.0, 26.1),
  FontSizeConfig("74.5", 74.5, 26.3),
  FontSizeConfig("75.0", 75.0, 26.5),
  FontSizeConfig("75.5", 75.5, 26.6),
  FontSizeConfig("76.0", 76.0, 26.8),
  FontSizeConfig("76.5", 76.5, 27.0),
  FontSizeConfig("77.0", 77.0, 27.2),
  FontSizeConfig("77.5", 77.5, 27.3),
  FontSizeConfig("78.0", 78.0, 27.5),
  FontSizeConfig("78.5", 78.5, 27.7),
  FontSizeConfig("79.0", 79.0, 27.9),
  FontSizeConfig("79.5", 79.5, 28.0),
  FontSizeConfig("80.0", 80.0, 28.2),
  FontSizeConfig("80.5", 80.5, 28.4),
  FontSizeConfig("81.0", 81.0, 28.6),
  FontSizeConfig("81.5", 81.5, 28.8),
  FontSizeConfig("82.0", 82.0, 28.9),
  FontSizeConfig("82.5", 82.5, 29.1),
  FontSizeConfig("83.0", 83.0, 29.3),
  FontSizeConfig("83.5", 83.5, 29.5),
  FontSizeConfig("84.0", 84.0, 29.6),
  FontSizeConfig("84.5", 84.5, 29.8),
  FontSizeConfig("85.0", 85.0, 30.0),
  FontSizeConfig("85.5", 85.5, 30.2),
  FontSizeConfig("86.0", 86.0, 30.3),
  FontSizeConfig("86.5", 86.5, 30.5),
  FontSizeConfig("87.0", 87.0, 30.7),
  FontSizeConfig("87.5", 87.5, 30.9),
  FontSizeConfig("88.0", 88.0, 31.0),
  FontSizeConfig("88.5", 88.5, 31.2),
  FontSizeConfig("89.0", 89.0, 31.4),
  FontSizeConfig("89.5", 89.5, 31.6),
  FontSizeConfig("90.0", 90.0, 31.8),
  FontSizeConfig("90.5", 90.5, 31.9),
  FontSizeConfig("91.0", 91.0, 32.1),
  FontSizeConfig("91.5", 91.5, 32.3),
  FontSizeConfig("92.0", 92.0, 32.5),
  FontSizeConfig("92.5", 92.5, 32.6),
  FontSizeConfig("93.0", 93.0, 32.8),
  FontSizeConfig("93.5", 93.5, 33.0),
  FontSizeConfig("94.0", 94.0, 33.2),
  FontSizeConfig("94.5", 94.5, 33.3),
  FontSizeConfig("95.0", 95.0, 33.5),
  FontSizeConfig("95.5", 95.5, 33.7),
  FontSizeConfig("96.0", 96.0, 33.9),
  FontSizeConfig("96.5", 96.5, 34.0),
  FontSizeConfig("97.0", 97.0, 34.2),
  FontSizeConfig("97.5", 97.5, 34.4),
  FontSizeConfig("98.0", 98.0, 34.6),
  FontSizeConfig("98.5", 98.5, 34.8),
  FontSizeConfig("99.0", 99.0, 34.9),
  FontSizeConfig("99.5", 99.5, 35.1),
  FontSizeConfig("100.0", 100.0, 35.3),
  FontSizeConfig("100.5", 100.5, 35.5),
  FontSizeConfig("101.0", 101.0, 35.6),
  FontSizeConfig("101.5", 101.5, 35.8),
  FontSizeConfig("102.0", 102.0, 36.0),
  FontSizeConfig("102.5", 102.5, 36.2),
  FontSizeConfig("103.0", 103.0, 36.3),
  FontSizeConfig("103.5", 103.5, 36.5),
  FontSizeConfig("104.0", 104.0, 36.7),
  FontSizeConfig("104.5", 104.5, 36.9),
  FontSizeConfig("105.0", 105.0, 37.0),
  FontSizeConfig("105.5", 105.5, 37.2),
  FontSizeConfig("106.0", 106.0, 37.4),
  FontSizeConfig("106.5", 106.5, 37.6),
  FontSizeConfig("107.0", 107.0, 37.7),
  FontSizeConfig("107.5", 107.5, 37.9),
  FontSizeConfig("108.0", 108.0, 38.1),
  FontSizeConfig("108.5", 108.5, 38.3),
  FontSizeConfig("109.0", 109.0, 38.5),
  FontSizeConfig("109.5", 109.5, 38.6),
  FontSizeConfig("110.0", 110.0, 38.8),
  FontSizeConfig("110.5", 110.5, 39.0),
  FontSizeConfig("111.0", 111.0, 39.2),
  FontSizeConfig("111.5", 111.5, 39.3),
  FontSizeConfig("112.0", 112.0, 39.5),
  FontSizeConfig("112.5", 112.5, 39.7),
  FontSizeConfig("113.0", 113.0, 39.9),
  FontSizeConfig("113.5", 113.5, 40.0),
  FontSizeConfig("114.0", 114.0, 40.2),
  FontSizeConfig("114.5", 114.5, 40.4),
  FontSizeConfig("115.0", 115.0, 40.6),
  FontSizeConfig("115.5", 115.5, 40.7),
  FontSizeConfig("116.0", 116.0, 40.9),
  FontSizeConfig("116.5", 116.5, 41.1),
  FontSizeConfig("117.0", 117.0, 41.3),
  FontSizeConfig("117.5", 117.5, 41.5),
  FontSizeConfig("118.0", 118.0, 41.6),
  FontSizeConfig("118.5", 118.5, 41.8),
  FontSizeConfig("119.0", 119.0, 42.0),
  FontSizeConfig("119.5", 119.5, 42.2),
  FontSizeConfig("120.0", 120.0, 42.3),
  FontSizeConfig("120.5", 120.5, 42.5),
  FontSizeConfig("121.0", 121.0, 42.7),
  FontSizeConfig("121.5", 121.5, 42.9),
  FontSizeConfig("122.0", 122.0, 43.0),
  FontSizeConfig("122.5", 122.5, 43.2),
  FontSizeConfig("123.0", 123.0, 43.4),
  FontSizeConfig("123.5", 123.5, 43.6),
  FontSizeConfig("124.0", 124.0, 43.7),
  FontSizeConfig("124.5", 124.5, 43.9),
  FontSizeConfig("125.0", 125.0, 44.1),
  FontSizeConfig("125.5", 125.5, 44.3),
  FontSizeConfig("126.0", 126.0, 44.5),
  FontSizeConfig("126.5", 126.5, 44.6),
  FontSizeConfig("127.0", 127.0, 44.8),
  FontSizeConfig("127.5", 127.5, 45.0),
  FontSizeConfig("128.0", 128.0, 45.2),
  FontSizeConfig("128.5", 128.5, 45.3),
  FontSizeConfig("129.0", 129.0, 45.5),
  FontSizeConfig("129.5", 129.5, 45.7),
  FontSizeConfig("130.0", 130.0, 45.9),
  FontSizeConfig("130.5", 130.5, 46.0),
  FontSizeConfig("131.0", 131.0, 46.2),
  FontSizeConfig("131.5", 131.5, 46.4),
  FontSizeConfig("132.0", 132.0, 46.6),
  FontSizeConfig("132.5", 132.5, 46.7),
  FontSizeConfig("133.0", 133.0, 46.9),
  FontSizeConfig("133.5", 133.5, 47.1),
  FontSizeConfig("134.0", 134.0, 47.3),
  FontSizeConfig("134.5", 134.5, 47.5),
  FontSizeConfig("135.0", 135.0, 47.6),
  FontSizeConfig("135.5", 135.5, 47.8),
  FontSizeConfig("136.0", 136.0, 48.0),
  FontSizeConfig("136.5", 136.5, 48.2),
  FontSizeConfig("137.0", 137.0, 48.3),
  FontSizeConfig("137.5", 137.5, 48.5),
  FontSizeConfig("138.0", 138.0, 48.7),
  FontSizeConfig("138.5", 138.5, 48.9),
  FontSizeConfig("139.0", 139.0, 49.0),
  FontSizeConfig("139.5", 139.5, 49.2),
  FontSizeConfig("140.0", 140.0, 49.4),
  FontSizeConfig("140.5", 140.5, 49.6),
  FontSizeConfig("141.0", 141.0, 49.7),
  FontSizeConfig("141.5", 141.5, 49.9),
  FontSizeConfig("142.0", 142.0, 50.1),
  FontSizeConfig("142.5", 142.5, 50.3),
  FontSizeConfig("143.0", 143.0, 50.5),
  FontSizeConfig("143.5", 143.5, 50.6),
  FontSizeConfig("144.0", 144.0, 50.8),
  FontSizeConfig("144.5", 144.5, 51.0),
  FontSizeConfig("145.0", 145.0, 51.2),
  FontSizeConfig("145.5", 145.5, 51.3),
  FontSizeConfig("146.0", 146.0, 51.5),
  FontSizeConfig("146.5", 146.5, 51.7),
  FontSizeConfig("147.0", 147.0, 51.9),
  FontSizeConfig("147.5", 147.5, 52.0),
  FontSizeConfig("148.0", 148.0, 52.2),
  FontSizeConfig("148.5", 148.5, 52.4),
  FontSizeConfig("149.0", 149.0, 52.6),
  FontSizeConfig("149.5", 149.5, 52.7),
  FontSizeConfig("150.0", 150.0, 52.9),
  FontSizeConfig("150.5", 150.5, 53.1),
  FontSizeConfig("151.0", 151.0, 53.3),
  FontSizeConfig("151.5", 151.5, 53.4),
  FontSizeConfig("152.0", 152.0, 53.6),
  FontSizeConfig("152.5", 152.5, 53.8),
  FontSizeConfig("153.0", 153.0, 54.0),
  FontSizeConfig("153.5", 153.5, 54.2),
  FontSizeConfig("154.0", 154.0, 54.3),
  FontSizeConfig("154.5", 154.5, 54.5),
  FontSizeConfig("155.0", 155.0, 54.7),
  FontSizeConfig("155.5", 155.5, 54.9),
  FontSizeConfig("156.0", 156.0, 55.0),
  FontSizeConfig("156.5", 156.5, 55.2),
  FontSizeConfig("157.0", 157.0, 55.4),
  FontSizeConfig("157.5", 157.5, 55.6),
  FontSizeConfig("158.0", 158.0, 55.7),
  FontSizeConfig("158.5", 158.5, 55.9),
  FontSizeConfig("159.0", 159.0, 56.1),
  FontSizeConfig("159.5", 159.5, 56.3),
  FontSizeConfig("160.0", 160.0, 56.4),
  FontSizeConfig("160.5", 160.5, 56.6),
  FontSizeConfig("161.0", 161.0, 56.8),
  FontSizeConfig("161.5", 161.5, 57.0),
  FontSizeConfig("162.0", 162.0, 57.2),
  FontSizeConfig("162.5", 162.5, 57.3),
  FontSizeConfig("163.0", 163.0, 57.5),
  FontSizeConfig("163.5", 163.5, 57.7),
  FontSizeConfig("164.0", 164.0, 57.9),
  FontSizeConfig("164.5", 164.5, 58.0),
  FontSizeConfig("165.0", 165.0, 58.2),
  FontSizeConfig("165.5", 165.5, 58.4),
  FontSizeConfig("166.0", 166.0, 58.6),
  FontSizeConfig("166.5", 166.5, 58.7),
  FontSizeConfig("167.0", 167.0, 58.9),
  FontSizeConfig("167.5", 167.5, 59.1),
  FontSizeConfig("168.0", 168.0, 59.3),
  FontSizeConfig("168.5", 168.5, 59.4),
  FontSizeConfig("169.0", 169.0, 59.6),
  FontSizeConfig("169.5", 169.5, 59.8),
  FontSizeConfig("170.0", 170.0, 60.0),
  FontSizeConfig("170.5", 170.5, 60.2),
  FontSizeConfig("171.0", 171.0, 60.3),
  FontSizeConfig("171.5", 171.5, 60.5),
  FontSizeConfig("172.0", 172.0, 60.7),
  FontSizeConfig("172.5", 172.5, 60.9),
  FontSizeConfig("173.0", 173.0, 61.0),
  FontSizeConfig("173.5", 173.5, 61.2),
  FontSizeConfig("174.0", 174.0, 61.4),
  FontSizeConfig("174.5", 174.5, 61.6),
  FontSizeConfig("175.0", 175.0, 61.7),
  FontSizeConfig("175.5", 175.5, 61.9),
  FontSizeConfig("176.0", 176.0, 62.1),
  FontSizeConfig("176.5", 176.5, 62.3),
  FontSizeConfig("177.0", 177.0, 62.4),
  FontSizeConfig("177.5", 177.5, 62.6),
  FontSizeConfig("178.0", 178.0, 62.8),
  FontSizeConfig("178.5", 178.5, 63.0),
  FontSizeConfig("179.0", 179.0, 63.2),
  FontSizeConfig("179.5", 179.5, 63.3),
  FontSizeConfig("180.0", 180.0, 63.5),
  FontSizeConfig("180.5", 180.5, 63.7),
  FontSizeConfig("181.0", 181.0, 63.9),
  FontSizeConfig("181.5", 181.5, 64.0),
  FontSizeConfig("182.0", 182.0, 64.2),
  FontSizeConfig("182.5", 182.5, 64.4),
  FontSizeConfig("183.0", 183.0, 64.6),
  FontSizeConfig("183.5", 183.5, 64.7),
  FontSizeConfig("184.0", 184.0, 64.9),
  FontSizeConfig("184.5", 184.5, 65.1),
  FontSizeConfig("185.0", 185.0, 65.3),
  FontSizeConfig("185.5", 185.5, 65.4),
  FontSizeConfig("186.0", 186.0, 65.6),
  FontSizeConfig("186.5", 186.5, 65.8),
  FontSizeConfig("187.0", 187.0, 66.0),
  FontSizeConfig("187.5", 187.5, 66.2),
  FontSizeConfig("188.0", 188.0, 66.3),
  FontSizeConfig("188.5", 188.5, 66.5),
  FontSizeConfig("189.0", 189.0, 66.7),
  FontSizeConfig("189.5", 189.5, 66.9),
  FontSizeConfig("190.0", 190.0, 67.0),
  FontSizeConfig("190.5", 190.5, 67.2),
  FontSizeConfig("191.0", 191.0, 67.4),
  FontSizeConfig("191.5", 191.5, 67.6),
  FontSizeConfig("192.0", 192.0, 67.7),
  FontSizeConfig("192.5", 192.5, 67.9),
  FontSizeConfig("193.0", 193.0, 68.1),
  FontSizeConfig("193.5", 193.5, 68.3),
  FontSizeConfig("194.0", 194.0, 68.4),
  FontSizeConfig("194.5", 194.5, 68.6),
  FontSizeConfig("195.0", 195.0, 68.8),
  FontSizeConfig("195.5", 195.5, 69.0),
  FontSizeConfig("196.0", 196.0, 69.1),
  FontSizeConfig("196.5", 196.5, 69.3),
  FontSizeConfig("197.0", 197.0, 69.5),
  FontSizeConfig("197.5", 197.5, 69.7),
  FontSizeConfig("198.0", 198.0, 69.9),
  FontSizeConfig("198.5", 198.5, 70.0),
  FontSizeConfig("199.0", 199.0, 70.2),
  FontSizeConfig("199.5", 199.5, 70.4),
  FontSizeConfig("200.0", 200.0, 70.6),
  FontSizeConfig("200.5", 200.5, 70.7),
  FontSizeConfig("201.0", 201.0, 70.9),
  FontSizeConfig("201.5", 201.5, 71.1),
  FontSizeConfig("202.0", 202.0, 71.3),
  FontSizeConfig("202.5", 202.5, 71.4),
  FontSizeConfig("203.0", 203.0, 71.6),
  FontSizeConfig("203.5", 203.5, 71.8),
  FontSizeConfig("204.0", 204.0, 72.0),
];
/**
 * templateWidth, templateHeight 为毫米
 * 根据模版宽高，获取最大字体
 */
List<FontSizeConfig> calcMaxFontSize(double? templateWidth, double? templateHeight) {
  double maxMM = 0;
  double maxEdge = max(templateWidth ?? 0, templateHeight ?? 0);
  double minEdge = min(templateWidth ?? 0, templateHeight ?? 0);
  if (maxEdge < 2 * minEdge) {
    maxMM = maxEdge / 2;
  }
  if (maxEdge >= 2 * minEdge) {
    maxMM = minEdge;
  }
  int left = 0;
  int right = allFontSizeConfigList.length - 1;

  int mid = left;
  while (left <= right) {
    mid = (left + right) ~/ 2;
    if (allFontSizeConfigList[mid].mm == maxMM) {
      left = mid;
      break;
    } else if (allFontSizeConfigList[mid].mm > maxMM) {
      right = mid - 1;
    } else {
      left = mid + 1;
    }
  }

  if (left >= allFontSizeConfigList.length) {
    left = allFontSizeConfigList.length - 1;
  } else if (left < 8) {
    left = 8;
  } else if ((left > 0 &&
      (maxMM - allFontSizeConfigList[left - 1].mm).abs() < (maxMM - allFontSizeConfigList[left].mm).abs())) {
    left--;
  }

  return allFontSizeConfigList.sublist(0, left + 1);
  // return allFontSizeConfigList;
}

extension ClosestValueExtension on List<FontSizeConfig> {
  int getClosestValueIndex(double target) {
    int low = 0;
    int high = length - 1;
    int closestIndex = -1;

    while (low <= high) {
      int mid = (low + high) ~/ 2;
      double midValue = this[mid].mm;

      if (midValue == target) {
        closestIndex = mid;
        break;
      }

      if (midValue < target) {
        closestIndex = mid;
        low = mid + 1;
      } else {
        high = mid - 1;
      }
    }
    // 检查前一个 double 是否更接近
    if (closestIndex > 0 && target - this[closestIndex - 1].mm < this[closestIndex].mm - target) {
      closestIndex -= 1;
    }

    return closestIndex;

  }

}