import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/macro/color.dart';
import 'package:text/utils/cachedImageUtil.dart';
import 'package:text/utils/svg_icon.dart';

import '../../../tools/to_Native_Method_Channel.dart';
import '../../../utils/common_fun.dart';
import '../../../utils/theme_color.dart';
import '../../../widget/state_widget.dart';
import 'label_category_list_model.dart';
import 'label_selector_controller.dart';

class LabelSearchPage extends StatefulWidget {
  final String? keywords;

  LabelSearchPage({Key? key, this.keywords}) : super(key: key);

  @override
  State<LabelSearchPage> createState() => _LabelSearchState();
}

class _LabelSearchState extends State<LabelSearchPage> {
  StateType _stateType = StateType.none;

  final _controller = Get.find<LabelSelectorController>();

  /// 模版滑动管理器
  final ScrollController _scrollController = ScrollController();

  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  int page = 1;
  ///是否展示示例图
  bool showExample = true;

  @override
  initState() {
    super.initState();
    // 添加通知，重置状态
    _controller.addListenerEvent((event) {});

    /// 清空上次搜索数据源
    _controller.searchKeywords = widget.keywords;
    _controller.listOfSearch.value = [];

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if ((widget.keywords ?? '').isNotEmpty) {
        _onRefresh();
      }
    });
  }

  _onRefresh({bool manual = false}) {
    if (manual == false) {
      setState(() {
        showExample = false;
        _stateType = StateType.loading;
      });
    }

    _controller.getListOfSearch(
        keywords: _controller.searchKeywords ?? '',
        includeVip: _controller.includeVip,
        resultClosure: (model) {
          setState(() {
            if (model == null) {
              _refreshController.refreshFailed();
              _stateType = StateType.error;
            } else if ((model.list ?? []).isEmpty) {
              _refreshController.refreshCompleted(resetFooterState: true);
              _stateType = StateType.empty4Search;
            } else {
              _refreshController.refreshCompleted(resetFooterState: true);
              _stateType = StateType.none;
            }
          });
        });

    // 重置状态
    // _controller.resetState(ResetTemplateState.ResetTemplateListState);
  }

  _onLoading() {
    // 拉取新一页的page
    _controller.getListOfSearch(
        keywords: _controller.searchKeywords ?? '',
        includeVip: _controller.includeVip,
        page: page += 1,
        isMoreData: true,
        resultClosure: (model) {
          if (model == null) {
            _refreshController.loadFailed();
          } else if ((model.list ?? []).isEmpty) {
            _refreshController.loadNoData();
          } else {
            _refreshController.loadComplete();
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: showExample? 0:MediaQuery.viewInsetsOf(context).bottom),
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.all(Radius.circular(12)),
      ),
      height: MediaQuery.sizeOf(context).height - 60,
      child: Column(
        children: [
          _AppBarContent(
            keywords: widget.keywords ?? '',
            onSearch: (String text) {
              /// 数据埋点
              if ((text ?? '').isNotEmpty) {
                ToNativeMethodChannel().sendTrackingToNative({
                  "track": "click",
                  "posCode": "071_171",
                  "ext": {'key_word': text}
                });
              }

              _controller.searchKeywords = text;
              _onRefresh();
            },
          ),
          if (_stateType != StateType.none)
            Expanded(
                child: StateWidget(
              type: _stateType,
              onClicked: _onRefresh,
            )),
          if (_stateType == StateType.none && showExample)
            _buildLabelExample(),
          if (_stateType == StateType.none)
            Expanded(
              child: Obx(() {
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: SmartRefresher(
                    enablePullUp: true,
                    enablePullDown: showExample ? false : true,
                    controller: _refreshController,
                    onRefresh: () {
                      _onRefresh(manual: true);
                    },
                    onLoading: _onLoading,
                    header: const ClassicHeader(
                      idleText: '',
                      refreshingText: '',
                      releaseText: '',
                      completeText: '',
                    ),
                    footer: const ClassicFooter(
                      idleText: '',
                      canLoadingText: '',
                      loadingText: '',
                      noDataText: '',
                      failedText: '',
                    ),
                    child: ListView.separated(
                      controller: _scrollController,
                      itemCount: _controller.listOfSearch.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                            behavior: HitTestBehavior.translucent,
                            onTap: () {
                              Item item = _controller.listOfSearch[index] as Item;

                              /// 数据埋点
                              ToNativeMethodChannel().sendTrackingToNative({
                                "track": "click",
                                "posCode": "071_175",
                                "ext": {
                                  'type': 2,
                                  'material_id': '${item.id ?? ''}',
                                  'pos': index + 1,
                                }
                              });

                              Navigator.of(context).pop(item.rawJson);
                            },
                            child: LabelSearchCell(model: _controller.listOfSearch[index]));
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(
                          height: 12,
                        );
                      },
                    ),
                  ),
                );
              }),
            ),
        ],
      ),
    );
  }

  ///标签纸示例图
  Widget _buildLabelExample(){
    return Container(
      height: 182,
      margin: const EdgeInsets.symmetric(horizontal: 17),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
            color: ThemeColor.border, width: 0.5),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        textDirection: TextDirection.ltr,
        children: [
          Image(
            height: 144,
            image: AssetImage('assets/images/icon_label_example.png'),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            textDirection: TextDirection.ltr,
            children: [
              Container(
                height: 53,
                constraints: BoxConstraints(maxWidth: 110),
                margin: EdgeInsets.only(top: 30),
                child: Text( intlanguage("app100001598", "标签纸名称"),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF595959))),
              ),
              Container(
                constraints: BoxConstraints(maxWidth: 110),
                child: Text( intlanguage("app100001599", "标签纸ID"),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF595959))),
              ),
            ],
          )
        ]

      ),
    );
  }
}

class LabelSearchCell extends StatelessWidget {
  final Item model;

  const LabelSearchCell({Key? key, required this.model}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 230,
      decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFEBEBEB), width: 0.5), borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Stack(
              fit: StackFit.expand,
              children: [
                Container(
                  decoration: const BoxDecoration(
                      color: Color(0xFFF7F7F7),
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12))),
                ),
                Positioned(
                    top: 16,
                    left: 37,
                    right: 37,
                    bottom: 16,
                    child: CacheImageUtil().netCacheImage(
                      width: 66,
                      height: 66,
                      imageUrl: model.previewImage ?? '',
                      fit: BoxFit.contain,
                      errorWidget: ThemeWidget.placeholder(
                          backgroundColor: Colors.transparent, padding: const EdgeInsets.all(30)),
                    )),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(model.name ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: Color(0xFF262626))),
                    const SizedBox(height: 2),
                    // Text(
                    //   '${model.width}x${model.height}mm',
                    //   style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                    // )
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${model.width}x${model.height}mm',
                      style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                    ),
                    if (model.profile?.extrain?.materialModelSn?.isNotEmpty ?? false) ...[
                      Text(
                        'ID:${model.profile?.extrain?.materialModelSn}',
                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400, color: Color(0xFF999999)),
                      )
                    ]
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class _AppBarContent extends StatefulWidget {
  final Function(String)? onSearch;
  final String? keywords;

  const _AppBarContent({Key? key, this.keywords, this.onSearch}) : super(key: key);

  @override
  State<_AppBarContent> createState() => _AppBarContentState();
}

class _AppBarContentState extends State<_AppBarContent> {
  late TextEditingController _textController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.keywords);
  }

  @override
  void dispose() {
    super.dispose();
    _textController.dispose();
  }

  /// 提交Action
  _submittedAction(String text) {
    if (text.isEmpty) {
      return;
    }
    widget.onSearch?.call(text);
  }

  /// 取消事件
  _backAction() {
    _textController.clear();
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(0, 7, 0, 11),
      padding: const EdgeInsetsDirectional.symmetric(vertical: 5.0),
      height: 44,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: _backAction,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(14, 5, 8, 5),
                child: const SvgIcon(
                  'assets/images/industry_template/search_template/search_bar_back.svg',
                  matchTextDirection: true,
                ),
              )),

          /// 搜索栏
          Expanded(
            flex: 2,
            child: CupertinoTextField(
              textInputAction: TextInputAction.search,
              autofocus: true,
              controller: _textController,
              cursorColor: KColor.RED,
              cursorHeight: 16,
              onSubmitted: _submittedAction,
              style: TextStyle(
                color: Color(0xFF262626),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              placeholder: intlanguage('app100000512', '请输入标签条码、名称'),
              placeholderStyle: const TextStyle(
                color: Color(0xFF999999),
                fontSize: 14,
                fontWeight: FontWeight.w400,
                fontFamily: 'PingFangSC-Regular',
              ),
              prefix: const Padding(
                padding: EdgeInsets.fromLTRB(14, 8, 8, 8),
                child: Image(image: AssetImage('assets/images/industry_template/home/<USER>')),
              ),
              clearButtonMode: OverlayVisibilityMode.editing,
              decoration: BoxDecoration(color: const Color(0xFFF7F7F7), borderRadius: BorderRadius.circular(20)),
            ),
          ),
          GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                _submittedAction(_textController.text);
                Get.focusScope?.unfocus();
              },
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(10, 5, 16, 5),
                child: Text(
                  intlanguage('app00226', '搜索'),
                  style: const TextStyle(fontSize: 15.0, fontWeight: FontWeight.w400, color: ThemeColor.brand),
                ),
              ))
        ],
      ),
    );
  }
}
