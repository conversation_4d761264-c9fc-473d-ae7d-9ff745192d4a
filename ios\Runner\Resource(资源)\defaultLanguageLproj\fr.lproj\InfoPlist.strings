/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "Il faut ouvrir l'appareil photo pour le scan de code-barres, la reconnaissance de texte et la prise de photos. Autoriser ?";
NSBluetoothPeripheralUsageDescription = "Il faut activer le Bluetooth pour se connecter à une imprimante. Autoriser ?";
NSBluetoothAlwaysUsageDescription = "Il faut activer le Bluetooth pour se connecter à une imprimante. Autoriser ?";
NSContactsUsageDescription = "Il faut ouvrir Contacts pour la lecture des contacts. Autoriser ?";
NSMicrophoneUsageDescription = "Il faut ouvrir le micro pour la reconnaissance vocale. Autoriser ?";
NSPhotoLibraryUsageDescription = "Cette autorisation est requise pour imprimer des images, reconnaître des codes-barres / QR codes / textes, définir un avatar personnalisé, etc. Veuillez autoriser l'accès à toutes les photos afin d'assurer le bon fonctionnement de NIIMBOT. Si vous utilsez [Sélectionner des photos…], toutes les photos non sélectionnées et ajoutées ultérieurement ne seront pas accessibles dans NIIMBOT.";
NSLocationWhenInUseUsageDescription = "Pour faciliter l'utilisation des réseaux Wi-Fi à proximité, lNIIMBOT vous demande l'accès à la localisation.";
NSLocationAlwaysUsageDescription = "Pour faciliter l'utilisation des réseaux Wi-Fi à proximité, lNIIMBOT vous demande l'accès à la localisation.";
NSLocationAlwaysAndWhenInUseUsageDescription = "Pour faciliter l'utilisation des réseaux Wi-Fi à proximité, lNIIMBOT vous demande l'accès à la localisation.";
NSSpeechRecognitionUsageDescription = "L'accès à la reconnaissance vocale nécessite votre autorisation. Autoriser ?";
NSLocalNetworkUsageDescription = "Cette application a besoin d'accéder au ​Réseau local (LAN)​​ pour les services de recherche d'appareils LAN et de configuration de réseau.";
"UILaunchStoryboardName" = "LaunchScreen";
