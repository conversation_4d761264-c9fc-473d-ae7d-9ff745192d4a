import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/label_method_interface.dart';
import 'package:text/pages/industry_template/select_label/label_category_list_model.dart';
import 'package:text/pages/industry_template/select_label/label_details_page.dart';

class LabelImportImpl implements LabelMethodInterface {
  @override
  void jumpLabelDetailPage(Map<String, dynamic>? templateData, String labelInfo,
      BuildContext context) {
    // Item item = Item();
    // item.name = labelInfo;
    // item.height = templateData["height"];
    // item.previewImage = templateData["thumbnail"];
    // item.width = templateData["width"];
    // item.paperType = templateData["paperType"];
    // item.rotate = templateData["rotate"];
    if(templateData == null){
      return;
    }
    Item item = Item.fromJson(templateData);
    item.rawJson = templateData;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelDetailsPage(item,source: 2);
      },
    ).then((value) {});
  }
}
