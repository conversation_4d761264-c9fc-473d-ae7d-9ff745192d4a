import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/utils/graphql_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/application.dart';
import 'package:text/network/dio_utils.dart';
import 'package:text/network/http_api.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_logic.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_category_list_model.dart';
import 'package:text/pages/industry_template/home/<USER>/industry_template_state.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/toast_util.dart';

import '../../../../utils/templateLayout/layout_helper.dart';

class SearchTemplateController extends GetxController {
  /// 模版搜索结果
  var templatesResult = [].obs;

  /// 展示搜索页还是历史记录
  var fade = CrossFadeState.showFirst.obs;

  /// 存储实例
  late SharedPreferences sp;

  ///搜索状态
  var searchState = IndustryHomeState.loading;

  /// 搜索历史
  late var searchList = Rxn<List<String>>([]);

  /// 传输流
  late final _streamController = StreamController();

  final IndustryTemplateLogic logic = Get.find<IndustryTemplateLogic>();

  var isSearching = false.obs;

  var currentSearchKey = "";
  var notifyState = false;

  /// 是否是标签条码
  bool get isLabelBarcode {
    if (templatesResult.length == 1 && templatesResult.first.profile?.extrain?.templateClass == 0) {
      return true;
    } else {
      return false;
    }
  }

  @override
  void onInit() async {
    //getHistroyList();
    super.onInit();
  }

  @override
  void onClose() {
    _streamController.close();

    if (Application.user != null) {
      searchList.value = [];
      sp.setStringList('search_history', searchList.value ?? []);
    }
    super.onClose();
  }

  /// 获取搜索结果
  getSearchResult(
      {int page = 1,
      int limit = 10,
      num width = 0,
      num height = 0,
      String searchKey = '',
      bool isMoreData = false,
      Function(IndustryTemplateCategoryListModel?)? resultClosure}) {
    searchState = IndustryHomeState.showContainer;
    currentSearchKey = searchKey;
    Map<String, dynamic> parameters = {
      'page': page,
      'limit': limit,
      'searchText': searchKey,
      'width': logic.state.interfaceSize.width,
      'height': logic.state.interfaceSize.height,
      'isCustomSize': false,
    };
    isSearching.value = true;
    DioUtils.instance.requestNetwork<IndustryTemplateCategoryListModel>(
        Method.post, IndustryTemplateApi.searchIndustryTemplate, needLogin: false, params: parameters, isList: false,
        onSuccess: (model) {
      isSearching.value = false;
      if (isMoreData) {
        templatesResult.addAll((model?.list)!);
      } else {
        templatesResult.value = (model?.list)!;
      }
      notifyState = false;
      searchState = IndustryHomeState.showContainer;
      resultClosure?.call(model);
    }, onError: (code, message) {
      isSearching.value = false;
      searchState = IndustryHomeState.error;
      resultClosure?.call(null);
    });
  }

  ///获取用户搜索历史
  updateHistroyList(state) async {
    sp = await SharedPreferences.getInstance();
    var histroyList = sp.getStringList('search_history') ?? [];
    if (histroyList.isNotEmpty) {
      uplpadHistroy(state, histroyList, isFirstUpload: true);
    } else {
      getHistroyList(state);
    }
  }

  ///获取搜索记录
  getHistroyList(state) {
    String gql = '''
          query queryIndustryTemplateSearchHistories{
            queryIndustryTemplateSearchHistories{
            content{
            name
            }
            id
            }
          }
          ''';
    GraphQLUtils.sharedInstance().setAuthorization(Application.token);
    GraphQLUtils.sharedInstance().query(gql, variables: {}, authorization: true).then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException && result.data!["queryIndustryTemplateSearchHistories"] != null) {
        List arguments = result.data!["queryIndustryTemplateSearchHistories"]["content"] as List;
        if (arguments.isNotEmpty) {
          searchList.value = [];
          sp.setStringList('search_history', searchList.value ?? []);

          arguments.forEach((element) {
            searchList.value!.add(element["name"]);
          });
        }
      }
      state.setState(() {});
    });
  }

  ///上传搜索记录
  uplpadHistroy(state, List<String>? value, {isFirstUpload = false}) {
    String gql = '''
        mutation uploadIndustryTemplateSearchHistories(\$input: UnloadIndustryTemplateSearchHistoryInput!){
          uploadIndustryTemplateSearchHistories(input:\$input){
          }
        }
        ''';
    GraphQLUtils.sharedInstance()
        .mutate(gql,
            variables: {
              "input": {"content": value}
            },
            authorization: true)
        .then((QueryResultWrapper wrapper) {
      var result = wrapper.queryResult;
      if (!result.hasException) {
        if (isFirstUpload) {
          getHistroyList(state);
        }
      } else {
        if (isFirstUpload) {
          searchList.value = sp.getStringList('search_history') ?? [];
        }
      }
    });
  }

  ///清空搜索记录
  clearHistroyList() {
    if (Application.user == null) {
      searchList.value = [];
      sp.setStringList('search_history', searchList.value ?? []);
    } else {
      String gql = '''
          mutation clearIndustryTemplateSearchHistories{
            clearIndustryTemplateSearchHistories{
            }
          }
          ''';
      GraphQLUtils.sharedInstance().setAuthorization(Application.token);
      GraphQLUtils.sharedInstance().mutate(gql, variables: {}, authorization: true).then((QueryResultWrapper wrapper) {
        var result = wrapper.queryResult;
        if (!result.hasException) {
          searchList.value = [];
          sp.setStringList('search_history', searchList.value ?? []);
        } else {
          var error = intlanguage('app01139', '网络异常');
          showToast(msg: error);
        }
      });
    }
  }
}

extension StreamEvent on SearchTemplateController {
  addEventListen(Function(dynamic event) onData) {
    _streamController.stream.listen(onData);
  }

  sendEvent(String text) {
    _streamController.sink.add(text);
  }
}

extension SaveAndDelete on SearchTemplateController {
  /// 存储搜索的key
  saveSearchText(state, String text) {
    /// 先进行去重
    searchList.value?.removeWhere((element) => element == text);

    /// 插入到最前面
    searchList.value?.insert(0, text);
    if (searchList.value?.length == 11) {
      searchList.value?.removeLast();
    }

    sp.setStringList('search_history', searchList.value ?? []);
    uplpadHistroy(state, [text]);
  }

  /// 清除所有历史记录
  deleteAllHistory() {
    clearHistroyList();
  }

  insertSearchTextToFront(int index) {
    searchList.value?.removeAt(index);
  }

  reportSceneInfo(Function success) {
    logic.reportSceneInfo(() {
      success.call();
    });
  }
}

/// 跳转原生
extension GotoNativePage on SearchTemplateController {
  /// 跳转原生画板
  gotoCanvasPage(TemplateData templateData) {
    if (isLabelBarcode) {
      // 返回标签纸元数据，作为尺寸进行筛选
      //   CustomNavigation.pop(result: {'labelData': templatesResult.first.rawData});
      toLabelCanvasPage();
    } else {
      if (logic.state.showTransitionPageState != 0) return;
      logic.goToCanvasPage(templateData, backFromSearch: true);
    }
  }

  toLabelCanvasPage({bool isCustomLabel = false, VoidCallback? completion}) async {
    templatesResult.first.rawData["name"] = intlanguage("app100000728", "未命名模板");
    Map<String, dynamic> args = {
      'labelData': templatesResult.first.rawData,
      'isCustomLabel': isCustomLabel,
      'isSearchTemplate': true,
    };
    Map<String, dynamic>? layoutTemplate = await LayoutHelper().getLayoutTemplate(templatesResult.first.rawData);
    if (layoutTemplate != null) {
      CustomNavigation.gotoNextPage('toCanvasPage', {'labelData': layoutTemplate, 'labelId': templatesResult.first.rawData["id"], "isCustomLabel": false})
          .then((value) {
        completion?.call();
        // CustomNavigation.popUtil(route: "industryTemplate");
      });
    }
    // CustomNavigation.gotoNextPage('toCanvasPage', args).then((value) {
    //   completion?.call();
    //   // CustomNavigation.popUtil(route: "industryTemplate");
    // });
  }
}
