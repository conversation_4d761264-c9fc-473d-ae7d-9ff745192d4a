import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:text/macro/color.dart';
import 'package:text/pages/message/controller/message_logic.dart';
import 'package:text/pages/message/controller/message_state.dart';
import 'package:text/pages/message/model/message_promotion_model.dart';
import 'package:text/pages/message/widget/common_empty_widget.dart';
import 'package:text/pages/message/widget/message_item_widget.dart';
import 'package:text/pages/message/widget/net_error_widget.dart';
import 'package:text/utils/event_bus.dart';
import 'package:text/utils/image_utils.dart';

import '../../application.dart';
import '../../tools/to_Native_Method_Channel.dart';
import '../../utils/common_fun.dart';
import '../../utils/toast_util.dart';
import 'model/message_type.dart';
import 'utils/message_util.dart';

/**
 * 子级消息列表页面
 */
class SubMessageListPage extends StatefulWidget {
  final String category;
  final int isPush;
  SubMessageListPage({Key? key, required this.category, required this.isPush}) : super(key: key);

  @override
  State<SubMessageListPage> createState() => SubMessageListPageState();
}

class SubMessageListPageState extends State<SubMessageListPage> {
  late MessageLogic logic;
  late MessageState state;
  late String title;

  @override
  void initState() {
    super.initState();
    logic = Get.put(MessageLogic());
    state = logic.state;
    title = MessageLogic.getMessageTabInfo(widget.category)?.messageTabTitle ?? "";
    logic.getSubMessageList(refreshController: state.subMessageRefreshController, category: widget.category);
    NiimbotEventBus.getDefault().register(this, (data) {
      if (data is String && data == "kBackToForeground") {
        if (!mounted) {
          return;
        }
        if (mounted) {
          logic.getSubMessageList(refreshController: state.subMessageRefreshController, category: widget.category);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        logic.batchUpdateMessageReadStatus(widget.category);
        return true;
      },
      child: Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            elevation: 0,
            centerTitle: true,
            title: Text(
              title,
              style: const TextStyle(color: KColor.title, fontSize: 17, fontWeight: FontWeight.w600),
            ),
            leading: InkWell(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16),
                child: Image(
                  image: ImageUtils.getAssetImage('icon_arrow_back'),
                  matchTextDirection: true,
                ),
              ),
              onTap: () async{
                logic.batchUpdateMessageReadStatus(widget.category);
                Navigator.of(context).pop();
              },
            ),
          ),
          body: GetBuilder<MessageLogic>(
            id: MessageLogic.updateSubMessageList,
            builder: (logic) {
              return Container(
                color: KColor.COLOR_F5F5F5,
                child: _buildContentWidget(),
              );
            },
          )),
    );
  }

  _buildContentWidget() {
    switch (state.subMessagePageState) {
      case MessagePageState.empty:
        return CommonEmptyWidget("emptyImagePath", intlanguage("app100001799", "暂无消息"));
      case MessagePageState.loading:
        return Container(
            child: Stack(
          children: [
            Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.only(bottom: 80),
              child: const CupertinoActivityIndicator(
                radius: 20,
                animating: true,
              ),
            ),
          ],
        ));
      case MessagePageState.data:
        return _buildListWidget();
      case MessagePageState.netError:
        return NetErrorWidget(
          retryCallback: () {
            logic.retryRequestSubMessages(widget.category);
          },
        );
    }
  }

  Widget _buildListWidget() {
    List<MessagePromotionModel> messageList = state.subMessageList;
    return SmartRefresher(
        controller: state.subMessageRefreshController,
        enablePullDown: false,
        enablePullUp: true,
        onRefresh: () {
          logic.getSubMessageList(refreshController: state.subMessageRefreshController, category: widget.category);
        },
        onLoading: () {
          logic.getSubMessageList(
              refreshController: state.subMessageRefreshController,
              category: widget.category,
              lastId: state.lastSubMessage?.id);
        },
        child: ListView.separated(
          padding: EdgeInsetsDirectional.symmetric(horizontal: 16),
          itemCount: messageList.length,
          itemBuilder: (context, index) {
            MessagePromotionModel messageModel = messageList[index];
            messageModel.category = widget.category;
            return GestureDetector(
              onTap: () {
                if (!Application.networkConnected) {
                  showToast(msg:intlanguage('app01139', '网络异常'));
                  return;
                }
                logic.updateMessageReadStatus(messageModel, isSubMessage: true);
                //根据category 和 type跳转到具体的页面
                if(messageModel.category == MessageType.serviceNotify){
                  if(messageModel.type == MessageSubType.serviceVipActivate || messageModel.type == MessageSubType.serviceVipExpired){
                    //跳转到会员中心，并掉起sku
                    messageModel.redirectUrl = "niimbot://app/vip?popSku=1";
                  }else{
                    showToast(msg:intlanguage('app100001824', '暂不支持此消息'));
                    return;
                  }
                }
                ToNativeMethodChannel().jumpToMessageDetail(intlanguage("app100001798", "消息详情"),
                    messageModel.redirectUrl ?? "", logic.getMessageJumpType(messageModel));
                ToNativeMethodChannel().sendTrackingToNative({
                  "track": "click",
                  "posCode": "130_375_342",
                  "ext":{"sort_name":getTrackingTitle(messageModel.category),"type_name":messageModel.type}
                });
              },
              child: Padding(
                padding: EdgeInsetsDirectional.only(
                    top: index == 0 ? 20 : 0, bottom: index == messageList.length - 1 ? 10 : 0),
                child: MessageItemWidget(
                  messageModel,
                  isSubMessage: true,
                ),
              ),
            );
          },
          separatorBuilder: (context, index) {
            return Divider(
              color: Colors.transparent,
              height: 10, // 第一个分隔符增加高度
            );
          },
        ));
  }

  _handleClear() {}

  _handleSetting() {}

  @override
  void dispose() {
    super.dispose();
    state.subMessagePageState = MessagePageState.loading;
    state.subMessageList?.clear();
    state.lastSubMessage = null;
    if(widget.isPush == 1) {
      Get.delete<MessageLogic>();
    }

  }
}
