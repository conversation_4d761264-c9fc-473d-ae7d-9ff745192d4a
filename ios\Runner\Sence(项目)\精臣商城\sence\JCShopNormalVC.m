//
//  JCShopProductVC.m
//  Runner
//
//  Created by xy on 2018/10/17.
//  Copyright © 2018年 xiaoyao. All rights reserved.
//

#import "JCShopNormalVC.h"
#import <AlipaySDK/AlipaySDK.h>
#import "QQLBXScanViewController.h"
#import "Global.h"
#import "StyleDIY.h"
#import "ONImagePickerController.h"

@interface JCShopNormalVC ()
@property (nonatomic,strong) NSString *shopChildPath;
@property (nonatomic,strong) NSDictionary *optionsDic;
@property (nonatomic,strong) NSString *baseUrl;
@property (nonatomic,strong) NSString *appointUrl;
@end

@implementation JCShopNormalVC


- (id)initWithShopChildPath:(NSString *)shopChildPath options:(NSDictionary *)optionsDic{
    self = [super init];
    if(self){
        self.shopChildPath = shopChildPath;
        self.optionsDic = optionsDic;
        self.baseUrl = [XYCenter sharedInstance].shopHomeHost;
    }
    return self;
}

- (id)initWithShopAppointUrl:(NSString *)appointUrl{
    self = [super init];
    if(self){
        self.appointUrl = appointUrl;
        self.baseUrl = [XYCenter sharedInstance].shopHomeHost;
    }
    return self;
}

- (void)viewWillAppear:(BOOL)animated{
    [super viewWillAppear:animated];
    if (@available(iOS 13.0, *)) {
        [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleDarkContent;
    } else {
        // Fallback on earlier versions
    }
}

- (void)viewWillDisappear:(BOOL)animated{
    [super viewWillDisappear:animated];
    [UIApplication sharedApplication].statusBarStyle = UIStatusBarStyleLightContent;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self shopDetailStateView];
    [self setupWebViewFrame];
    // 脚本消息处理器已在基类中注册，无需重复注册
    NSString *pathOptionString = @"";
    for (NSString *optionsKey in self.optionsDic.allKeys) {
        if(STR_IS_NIL(pathOptionString)){
            pathOptionString = [NSString stringWithFormat:@"%@=%@",optionsKey,self.optionsDic[optionsKey]];
        }else{
            pathOptionString = [NSString stringWithFormat:@"%@&%@=%@",pathOptionString,optionsKey,self.optionsDic[optionsKey]];
        }
        
    }
    if (STR_IS_NIL(self.appointUrl)) {
        NSString *url = [self.baseUrl stringByAppendingFormat: [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] ? @"h5_project/#/%@?%@" : @"/#/%@?%@",self.shopChildPath, pathOptionString];
        [self loadUrl:url];
    } else {
        [self loadUrl:self.appointUrl];
    }
    //调用JS方法
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(refreshWeb) name:@"payBackToRefrshWeb" object:nil];
}

-(void)refreshWeb{
    NSString *myStr = @"jcydy://com.suofang.jcbqdy/orderlist";
    NSURL *url = [NSURL URLWithString:myStr];
    NSURLRequest *req = [NSURLRequest requestWithURL:url ];
    [self.webView loadRequest:req];
}


#pragma mark - WKScriptMessageHandler
- (void)userContentController:(WKUserContentController *)userContentController didReceiveScriptMessage:(WKScriptMessage *)message
{
    // 处理特殊的返回逻辑
    if ([message.name isEqualToString:@"backHome"]) {
        if(self.navigationController == nil || self.navigationController.viewControllers.count == 1){
            [self dismissViewControllerAnimated:YES completion:^{
                            
            }];
        }else{
            [self.navigationController popViewControllerAnimated:YES];
        }
        if([m_userModel.is_FirstShop isEqualToString:@"1"]){
            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {
                
            }];
        }
        if(self.colseCurrentPageBlock){
            self.colseCurrentPageBlock();
        }
        return;
    }
    
    // 调用基类的消息处理方法处理其他消息
    [super userContentController:userContentController didReceiveScriptMessage:message];
}

// 扫码方法、图片选择方法等已在基类中实现，无需重复定义

@end
