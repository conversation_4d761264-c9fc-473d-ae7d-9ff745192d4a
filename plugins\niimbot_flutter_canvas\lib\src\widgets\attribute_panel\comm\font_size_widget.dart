import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';

import '/src/localization/localization_public.dart';
import '/src/localization/localization_translations.dart';
import '/src/model/element/bar_code_element.dart';
import '/src/widgets/attribute_panel/comm/font_size_config.dart';
import '/src/widgets/attribute_panel/comm/value_change_widget.dart';
import '/src/widgets/canvas/canvas_theme_widget.dart';
import '/src/utils/input_utils.dart';
import '/src/utils/loading_mix.dart';

class FontSizeWidget extends StatefulWidget {
  final ValueChanged<FontSizeConfig>? valueChanged;
  final double mm;
  final BarCodeElement barCodeElement;
  final List<FontSizeConfig> fontSizeConfigList;
  final bool enabled;

  const FontSizeWidget({
    super.key,
    this.valueChanged,
    required this.mm,
    required this.barCodeElement,
    required this.fontSizeConfigList,
    this.enabled = true,
  }) ;

  @override
  State<StatefulWidget> createState() {
    return FontSizeState();
  }
}

class FontSizeState extends State<FontSizeWidget> {
  int _fontSizeConfigIndex = 0;

  @override
  void initState() {
    super.initState();
    _fontSizeConfigIndex = widget.fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(widget.mm);
    if (_fontSizeConfigIndex < 0) {
      _fontSizeConfigIndex = 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          intlanguage('app01009', '字号'),
          style: CanvasTheme.of(context).attributeTitleTextStyle,
        ),
        Spacer(),
        Opacity(
          opacity: widget.enabled ? 1 : 0.5,
          child: ValueChangeWidget<int>(
            max: widget.fontSizeConfigList.length - 1,
            min: 0,
            enabled: widget.enabled,
            value: _fontSizeConfigIndex,
            stepValue: 1,
            displayValue: LocalizationTranslations.instance.isChinese
                ? widget.fontSizeConfigList[_fontSizeConfigIndex].title
                : widget.fontSizeConfigList[_fontSizeConfigIndex].fontSize
                    .toStringAsFixed(1),
            valueChanged: (int v) {
              int index = v.toInt();
              if (_fontSizeConfigIndex == index) {
                return;
              }
              if (index > _fontSizeConfigIndex) {
                //一维码文字宽或者高超出一维码限制判断
                double configFontSize =
                    widget.fontSizeConfigList[index].mm.mm2dp().toDouble();
                Size textSize = InputUtils.boundingTextSize(
                    widget.barCodeElement.value!,
                    TextStyle(fontSize: configFontSize),
                    maxLines: 1);
                if ((textSize.width + 1) >
                        widget.barCodeElement.width.mm2dp() ||
                    (configFontSize + 3.mm2dp()) >=
                        widget.barCodeElement.height.mm2dp()) {
                  LoadingMix.showToast(intlanguage("app01065", '请拉伸条码'));
                  setState(() {
                    //刷新加减控件初始值
                  });
                  return;
                }
              }

              setState(() {
                _fontSizeConfigIndex = index;
                widget.valueChanged
                    ?.call(widget.fontSizeConfigList[_fontSizeConfigIndex]);
              });
            },
          ),
        ),
      ],
    );
  }

  @override
  void didUpdateWidget(covariant FontSizeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    _fontSizeConfigIndex = widget.fontSizeConfigList.getFloorAcceptableFontSizeIndexBinary(widget.mm);
    if (_fontSizeConfigIndex <= 0) {
      _fontSizeConfigIndex = 0;
    }
  }
}
