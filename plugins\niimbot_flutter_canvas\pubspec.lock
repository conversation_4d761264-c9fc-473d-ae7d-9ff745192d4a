# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.6.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.11.0"
  auto_size_text:
    dependency: "direct main"
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: "7c1183e361e5c8b0a0f21a28401eecdbde252441106a9816400dd4c2b2424916"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.4.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "35814b016e37fbdc91f7ae18c8caf49ba5c88501813f73ce8a07027a395e2829"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.1"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "980842f4e8e2535b8dbd3d5ca0b1f0ba66bf61d14cc3a17a9b4788a3685ba062"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  camera:
    dependency: "direct main"
    description:
      name: camera
      sha256: dfa8fc5a1adaeb95e7a54d86a5bd56f4bb0e035515354c8ac6d262e35cec2ec8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.10.6"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: "3af7f0b55f184d392d2eec238aaa30552ebeef2915e5e094f5488bf50d6d7ca2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.10.9+3"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: c3038e6e72e284b14ad246a419f26908c08f8886d114cb8a2e351988439bfa68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.17+6"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: "953e7baed3a7c8fae92f7200afeb2be503ff1a17c3b4e4ed7b76f008c2810a31"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.9.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: "595f28c89d1fb62d77c73c633193755b781c6d2e0ebcd8dc25b763b514e6ba8f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.3.5"
  change:
    dependency: transitive
    description:
      name: change
      sha256: "65db7f966dc7e786687f49900a94c5f08b0eb9ca8c4a3e7eed3a55e980b455e2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.4"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  chewie:
    dependency: "direct main"
    description:
      name: chewie
      sha256: "335df378c025588aef400c704bd71f0daea479d4cd57c471c88c056c1144e7cd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.8.5"
  cider:
    dependency: transitive
    description:
      name: cider
      sha256: dfff70e9324f99e315857c596c31f54cb7380cfa20dfdfdca11a3631e05b7d3e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.8"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.18.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: b74247fad72c171381dbe700ca17da24deac637ab6d43c343b42867acb95c991
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: b30acd5944035672bc15c6b7a8b47d773e41e2f17de064350988c5d02adb1c68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.2"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: transitive
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.8"
  custom_pop_up_menu:
    dependency: "direct main"
    description:
      name: custom_pop_up_menu
      sha256: eeac484c6ddffffb25e803dc2a5cc9381e700a29f074e9fcc76fe36b62fde850
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.4"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.10"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "24a261d5d5c87e86c7651c417a5dbdf8bcd7080dd592533910e8d0505a279f21"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.2"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "5598aa796bbf4699afd5c67c0f5f6e2ed542afc956884b9cd58c306966efc260"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.7.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "33259a9276d6cea88774a0000cfae0d861003497755969c92faa223108620dc8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  drawboard_dart_sdk:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/flutter_update_new"
      resolved-ref: "2c07b6ea7b9c3642da88dfa6f536f568c2a67328"
      url: "https://git.jc-ai.cn/architect/kalimdor/drawboard-dart-sdk.git"
    source: git
    version: "1.2.2"
  enum_to_string:
    dependency: transitive
    description:
      name: enum_to_string
      sha256: "93b75963d3b0c9f6a90c095b3af153e1feccb79f6f08282d3274ff8d9eea52bc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.7"
  excel:
    dependency: transitive
    description:
      name: excel
      sha256: "1a15327dcad260d5db21d1f6e04f04838109b39a2f6a84ea486ceda36e468780"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.6"
  extended_image:
    dependency: "direct overridden"
    description:
      name: extended_image
      sha256: "85199f9233e03abc2ce2e68cbb2991648666af4a527ae4e6250935be8edfddae"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.1.0"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "9a94ec9314aa206cfa35f16145c3cd6e2c924badcc670eaaca8a3a8063a68cd7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.5"
  extension:
    dependency: transitive
    description:
      name: extension
      sha256: be3a6b7f8adad2f6e2e8c63c895d19811fcf203e23466c6296267941d0ff4f24
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: "direct overridden"
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.1"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "54cbbd957e1156d29548c7d9b9ec0c0ebb6de0a90452198683a7d23aed617a33"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.3+2"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: "271ab9986df0c135d45c3cdb6bd0faa5db6f4976d3e4b437cf7d0f258d941bfc"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.4+2"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: "8f5d2f6590d51ecd9179ba39c64f722edc15226cc93dcc8698466ad36a4a85a4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.3+3"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_boost:
    dependency: "direct main"
    description:
      path: "."
      ref: "v5.0.2"
      resolved-ref: b76986f6f541d8cf458b6d669bd75dec93cd43bf
      url: "https://git.jc-ai.cn/architect/flutter/flutter_boost.git"
    source: git
    version: "5.0.2"
  flutter_cache_manager:
    dependency: "direct main"
    description:
      name: flutter_cache_manager
      sha256: "400b6592f16a4409a7f2bb929a9a7e38c72cceb8ffb99ee57bbf2cb2cecf8386"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.4.1"
  flutter_canvas_plugins_interface:
    dependency: "direct main"
    description:
      path: "../flutter_canvas_plugins_interface"
      relative: true
    source: path
    version: "1.0.13"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  flutter_easyrefresh:
    dependency: "direct main"
    description:
      name: flutter_easyrefresh
      sha256: "5d161ee5dcac34da9065116568147d742dd25fb9bff3b10024d9054b195087ad"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.2"
  flutter_hooks:
    dependency: transitive
    description:
      name: flutter_hooks
      sha256: "6a126f703b89499818d73305e4ce1e3de33b4ae1c5512e3b8eab4b986f46774c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.18.6"
  flutter_image_compress:
    dependency: "direct main"
    description:
      name: flutter_image_compress
      sha256: f159d2e8c4ed04b8e36994124fd4a5017a0f01e831ae3358c74095c340e9ae5e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  flutter_image_compress_common:
    dependency: "direct main"
    description:
      name: flutter_image_compress_common
      sha256: "7cad12802628706655920089cfe9ee1d1098300e7f39a079eb160458bbc47652"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_macos:
    dependency: transitive
    description:
      name: flutter_image_compress_macos
      sha256: fea1e3d71150d03373916b832c49b5c2f56c3e7e13da82a929274a2c6f88251e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1"
  flutter_image_compress_platform_interface:
    dependency: "direct main"
    description:
      name: flutter_image_compress_platform_interface
      sha256: eb4f055138b29b04498ebcb6d569aaaee34b64d75fb74ea0d40f9790bf47ee9d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.3"
  flutter_image_compress_web:
    dependency: transitive
    description:
      name: flutter_image_compress_web
      sha256: "1c8cd505be95cb2e0573cdd9d236ac0ba39c166b8df06ef1823f2a17b60e1253"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.4"
  flutter_inappwebview:
    dependency: "direct main"
    description:
      name: flutter_inappwebview
      sha256: "80092d13d3e29b6227e25b67973c67c7210bd5e35c4b747ca908e31eb71a46d5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.5"
  flutter_inappwebview_android:
    dependency: transitive
    description:
      name: flutter_inappwebview_android
      sha256: "62557c15a5c2db5d195cb3892aab74fcaec266d7b86d59a6f0027abd672cddba"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview_internal_annotations:
    dependency: transitive
    description:
      name: flutter_inappwebview_internal_annotations
      sha256: "787171d43f8af67864740b6f04166c13190aa74a1468a1f1f1e9ee5b90c359cd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  flutter_inappwebview_ios:
    dependency: transitive
    description:
      name: flutter_inappwebview_ios
      sha256: "5818cf9b26cf0cbb0f62ff50772217d41ea8d3d9cc00279c45f8aabaa1b4025d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_macos:
    dependency: transitive
    description:
      name: flutter_inappwebview_macos
      sha256: c1fbb86af1a3738e3541364d7d1866315ffb0468a1a77e34198c9be571287da1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_platform_interface:
    dependency: transitive
    description:
      name: flutter_inappwebview_platform_interface
      sha256: cf5323e194096b6ede7a1ca808c3e0a078e4b33cc3f6338977d75b4024ba2500
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0+1"
  flutter_inappwebview_web:
    dependency: transitive
    description:
      name: flutter_inappwebview_web
      sha256: "55f89c83b0a0d3b7893306b3bb545ba4770a4df018204917148ebb42dc14a598"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  flutter_inappwebview_windows:
    dependency: transitive
    description:
      name: flutter_inappwebview_windows
      sha256: "8b4d3a46078a2cdc636c4a3d10d10f2a16882f6be607962dbfff8874d1642055"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  flutter_keyboard_visibility:
    dependency: "direct main"
    description:
      name: flutter_keyboard_visibility
      sha256: "98664be7be0e3ffca00de50f7f6a287ab62c763fc8c762e0a21584584a3ff4f8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.0.0"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_native_image:
    dependency: "direct main"
    description:
      name: flutter_native_image
      sha256: "0ff23d6222064259df8f85ea56925627ea1ec8658814672c5b6c23fc9174c65e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.6+1"
  flutter_net_signature:
    dependency: transitive
    description:
      name: flutter_net_signature
      sha256: "41061328952c181878d694c1be2d6dc4bfb383268a52efe6e0563d024944e0ce"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.12"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "615a505aef59b151b46bbeef55b36ce2b6ed299d160c51d84281946f0aa0ce0e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.24"
  flutter_screenutil:
    dependency: "direct main"
    description:
      name: flutter_screenutil
      sha256: "8239210dd68bee6b0577aa4a090890342d04a136ce1c81f98ee513fc0ce891de"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.9.3"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.1"
  flutter_staggered_grid_view:
    dependency: "direct main"
    description:
      name: flutter_staggered_grid_view
      sha256: "1312314293acceb65b92754298754801b0e1f26a1845833b740b30415bbbcf07"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.2"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "54900a1a1243f3c4a5506d853a2b5c2dbc38d5f27e52a52618a8054401431123"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.16"
  flutter_swipe_action_cell:
    dependency: "direct main"
    description:
      name: flutter_swipe_action_cell
      sha256: f09036bf30fea826d9ee7e86f72f040011f42783e8c629a1a549587b0e057c53
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "24467dc20bbe49fd63e57d8e190798c4d22cbbdac30e54209d153a15273721d1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.2.10"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.6.6"
  gql:
    dependency: transitive
    description:
      name: gql
      sha256: "650e79ed60c21579ca3bd17ebae8a8c8d22cde267b03a19bf3b35996baaa843a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315362"
  gql_dedupe_link:
    dependency: transitive
    description:
      name: gql_dedupe_link
      sha256: "10bee0564d67c24e0c8bd08bd56e0682b64a135e58afabbeed30d85d5e9fea96"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.4-alpha+1715521079596"
  gql_dio_link:
    dependency: transitive
    description:
      name: gql_dio_link
      sha256: "0a38185eb2eeabcc8d81c8322b8b6da9057af36553c7acb46e54fe035866ccef"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1+1"
  gql_error_link:
    dependency: transitive
    description:
      name: gql_error_link
      sha256: "93901458f3c050e33386dedb0ca7173e08cebd7078e4e0deca4bf23ab7a71f63"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0+1"
  gql_exec:
    dependency: transitive
    description:
      name: gql_exec
      sha256: "394944626fae900f1d34343ecf2d62e44eb984826189c8979d305f0ae5846e38"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.1-alpha+1699813812660"
  gql_http_link:
    dependency: transitive
    description:
      name: gql_http_link
      sha256: ef6ad24d31beb5a30113e9b919eec20876903cc4b0ee0d31550047aaaba7d5dd
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  gql_link:
    dependency: transitive
    description:
      name: gql_link
      sha256: c2b0adb2f6a60c2599b9128fb095316db5feb99ce444c86fb141a6964acedfa4
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1-alpha+1730759315378"
  gql_transform_link:
    dependency: transitive
    description:
      name: gql_transform_link
      sha256: "0645fdd874ca1be695fd327271fdfb24c0cd6fa40774a64b946062f321a59709"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  graphql:
    dependency: transitive
    description:
      name: graphql
      sha256: c715080993c8481087ce77b7929224222551823769fb150f5816f9bbbee9e589
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0-beta.10"
  graphql_flutter:
    dependency: transitive
    description:
      name: graphql_flutter
      sha256: "9de0365b58c8733130a706e9fddb33f6791d9bbbee45e1cd1bc2ca0920941a19"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.2.0-beta.3"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "1fc58edeaec4307368c60d59b7e15b9d658b57d7f3125098b6294153c75337ec"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.15.5"
  http:
    dependency: "direct overridden"
    description:
      name: http
      sha256: "761a297c042deedc1ffbb156d6e2af13886bb305c2a343a4d972504cd67dd938"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.0.2"
  image:
    dependency: "direct main"
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.3.0"
  image_editor:
    dependency: "direct main"
    description:
      name: image_editor
      sha256: "0fe70befea0dbaf24a7cacc32c28311a65118f66637997ad072e9063f59efdd8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.5.1"
  image_editor_common:
    dependency: transitive
    description:
      name: image_editor_common
      sha256: "93d2f5c8b636f862775dd62a9ec20d09c8272598daa02f935955a4640e1844ee"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  image_editor_ohos:
    dependency: transitive
    description:
      name: image_editor_ohos
      sha256: "06756859586d5acefec6e3b4f356f9b1ce05ef09213bcb9a0ce1680ecea2d054"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.0.9"
  image_editor_platform_interface:
    dependency: transitive
    description:
      name: image_editor_platform_interface
      sha256: "474517efc770464f7d99942472d8cfb369a3c378e95466ec17f74d2b80bd40de"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: "direct main"
    description:
      name: image_picker_android
      sha256: aa6f1280b670861ac45220cc95adc59bb6ae130259d36f980ccb62220dc5e59f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.8.12+19"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "717eb042ab08c40767684327be06a5d8dbb341fe791d514e4b92c7bbe1b7bb83"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.6"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "05da758e67bc7839e886b3959848aa6b44ff123ab4b28f67891008afe8ef9100"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.8.12+2"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "886d57f0be73c4b140004e78b9f28a8914a09e50c2d816bdd0520051a71236a0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.10.1"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.1+1"
  internet_file:
    dependency: "direct main"
    description:
      name: internet_file
      sha256: c3e6aa0c1cc6c08e701bb91019a7784fece1f64e18464f53df1200caa7598b68
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.19.0"
  isar:
    dependency: transitive
    description:
      name: isar
      sha256: "99165dadb2cf2329d3140198363a7e7bff9bbd441871898a87e26914d25cf1ea"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isar_flutter_libs:
    dependency: transitive
    description:
      name: isar_flutter_libs
      sha256: bc6768cc4b9c61aabff77152e7f33b4b17d2fc93134f7af1c3dd51500fe8d5e8
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0+1"
  isolate:
    dependency: "direct main"
    description:
      name: isolate
      sha256: "3554ab10fdeec965d27e0074c913ccb2229887633da080d2b35a6322da14938b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.9.0"
  keyboard_actions:
    dependency: "direct main"
    description:
      name: keyboard_actions
      sha256: d621d15d7626303798e451008a223642a86978d06fc4adff7461d77859834aa9
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.1.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "3f87a60e8c63aecc975dda1ceedbc8f24de75f09e4856ea27daf8958f2f0ce05"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.0.5"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "932549fb305594d82d7183ecd9fa93463e9914e1b67cacc34bc40906594a1806"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.5"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.7.0"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "935e23e1ff3bc02d390bad4d4be001208ee92cc217cb5b5a6c19bc14aaa318c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.3.0"
  marker:
    dependency: transitive
    description:
      name: marker
      sha256: "3dadd01f3b0ffae148ffb3b1bc04290a98e54a465cddbab59727bd2a9fe57750"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.6.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: d2323aa2060500f906aa31a895b4030b6da3ebdcc5619d14ce1aada65cd161cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.12.16+1"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.11.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: bdb68674043280c3428e9ec998512fb681678676b3c54e773629ffe74419f8c7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.15.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.6"
  mobile_scanner:
    dependency: "direct main"
    description:
      name: mobile_scanner
      sha256: b8c0e9afcfd52534f85ec666f3d52156f560b5e6c25b1e3d4fe2087763607926
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.1.1"
  music_visualizer:
    dependency: "direct main"
    description:
      name: music_visualizer
      sha256: d0eafdbe052021c7b2a2b3e1c87cc163e7262410a47246d570c568ad56935096
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.4"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  netal_plugin:
    dependency: transitive
    description:
      path: "."
      ref: "529903e1a74590a0d27c94e580c4a860bda8f033"
      resolved-ref: "529903e1a74590a0d27c94e580c4a860bda8f033"
      url: "https://git.jc-ai.cn/architect/dboard/netal_plugin.git"
    source: git
    version: "1.2.32"
  niimbot_cache_manager:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/6_3_3"
      resolved-ref: "8418509a091f9e8ed00949fff57dee24290495b1"
      url: "https://git.jc-ai.cn/print/foundation/niimbot_cache_manager.git"
    source: git
    version: "0.0.1"
  niimbot_excel:
    dependency: "direct main"
    description:
      name: niimbot_excel
      sha256: "6a673b5477ecffc7295f48a32cdc0c7f4c039f0d07602942a85920d36acf5fc6"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.15"
  niimbot_http:
    dependency: transitive
    description:
      name: niimbot_http
      sha256: "76d1bc25940bcac8464236f5084c692f0ac7dcc8275ab042a53274576ae94443"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.35"
  niimbot_intl:
    dependency: transitive
    description:
      name: niimbot_intl
      sha256: "7ed1b66cf1815042cecc9a64cdb9f8001370b1628e350952d1c6ee4793234c19"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.5"
  niimbot_lego:
    dependency: "direct main"
    description:
      name: niimbot_lego
      sha256: "2e6c51ed36af39f7bfbcf01abf193d3cd29fb33f5628540ae19e3a65967c3d0d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.6.6"
  niimbot_local_storage:
    dependency: transitive
    description:
      name: niimbot_local_storage
      sha256: f4dc495235b4677647f6069ac347ed1892b2a8092e6af7190d3ec076ade8597c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.5"
  niimbot_template:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/app_templateManager"
      resolved-ref: a08a7b970fc2e85509946ec0bf82bc18b03edcb2
      url: "https://git.jc-ai.cn/print/foundation/niimbot_template.git"
    source: git
    version: "0.1.45"
  nimbot_state_manager:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/flutter_update"
      resolved-ref: "0d976feaa2eee6e13e8c9425e78d9ff04d04b9b4"
      url: "https://git.jc-ai.cn/print/foundation/nimbot_state_manager.git"
    source: git
    version: "0.0.1"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.5.0"
  normalize:
    dependency: transitive
    description:
      name: normalize
      sha256: f78bf0552b9640c76369253f0b8fdabad4f3fbfc06bdae9359e71bee9a5b071b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.9.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "70c421fe9d9cc1a9a7f3b05ae56befd469fe4f8daa3b484823141a55442d858d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.1.2"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: a5ef9986efc7bf772f2696183a3992615baa76c1ffb1189318dd8803778fb05b
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.2"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "087ce49c3f0dc39180befefc60fdb4acd8f8620e5682fe2476afd0b3688bb4af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.0"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "4adf4fd5423ec60a29506c76581bc05854c55e3a0b72d35bb28d661c9686edf2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.15"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "4843174df4d288f5e29185bd6e72a6fbdf5a4a4602717eed565497429f179942"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.0"
  pdfx:
    dependency: "direct main"
    description:
      name: pdfx
      sha256: "16b0a931231b56283c1804738078b5cdd365ee2174b5345ea200d7cf380e1ea8"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.8.3"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "63e5216aae014a72fe9579ccd027323395ce7a98271d9defa9d57320d001af81"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.4.3"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.0.2"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: "91abfc4881ed8b97092ceb290fc5a591dcd53262052dd7a9dc7b3928e2bd4838"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.1"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b6015b67b32f345f57cf32c126f871bced2501236c405aafaefa885f7c821e4f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.15.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.9.1"
  popover:
    dependency: "direct main"
    description:
      path: "."
      ref: "feature/0.2.9"
      resolved-ref: "083f1f200bb1c0ef130065df945a3400c8a92e65"
      url: "https://git.jc-ai.cn/print/foundation/popover.git"
    source: git
    version: "0.2.8+5"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      sha256: "68645b24e0716782e58948f8467fd42a880f255096a821f9e7d0ec625b00c84d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.0"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.1.2"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "5bfcf68ca79ef689f8990d1160781b4bad40a3bd5e5218ad4076ddb7f4081585"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.0"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: "81876843eb50dc2e1e5b151792c9a985c5ed2536914115ed04e9c8528f6647b0"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.2.3"
  rfc_6901:
    dependency: transitive
    description:
      name: rfc_6901
      sha256: df1bbfa3d023009598f19636d6114c6ac1e0b7bb7bf6a260f0e6e6ce91416820
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "5c3004a4a8dbb94bd4bf5412a4def4acdaa12e12f269737a5751369e12d1a962"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.28.0"
  sensors_plus:
    dependency: transitive
    description:
      name: sensors_plus
      sha256: "6898cd4490ffc27fea4de5976585e92fae55355175d46c6c3b3d719d42f9e230"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.0.1"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: bc472d6cfd622acb4f020e726433ee31788b038056691ba433fec80e448a094f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: a752ce92ea7540fc35a0d19722816e04d0e72828a4200e83a98cf1a1eb524c9a
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.5"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "02a7d8a9ef346c9af715811b01fbd8e27845ad2c41148eefd31321471b41863d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "6a52cfcdaeac77cad8c97b539ff688ccfc458c007b4db12be584fbe5c0e49e03"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  sliding_up_panel:
    dependency: "direct main"
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.0+1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "2d7299468485dca85efeeadf5d38986909c5eb0cd71fd3db2c2f000e6c9454bb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1"
  sqflite_android:
    dependency: transitive
    description:
      name: sqflite_android
      sha256: "78f489aab276260cdd26676d2169446c7ecd3484bbd5fead4ca14f3ed4dd9ee3"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "761b9740ecbd4d3e66b8916d784e581861fd3c3553eda85e167bc49fdb68f709"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.5.4+6"
  sqflite_darwin:
    dependency: transitive
    description:
      name: sqflite_darwin
      sha256: "22adfd9a2c7d634041e96d6241e6e1c8138ca6817018afc5d443fef91dcefa9c"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.1+1"
  sqflite_platform_interface:
    dependency: transitive
    description:
      name: sqflite_platform_interface
      sha256: "8dd4515c7bdcae0a785b0062859336de775e8c65db81ae33dd5445f35be61920"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.4.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.3.0+3"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5b8a98dafc4d5c4c9c72d8b31ab2b23fc13422348d2997120294d3bac86b4ddb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.7.2"
  tuple:
    dependency: "direct main"
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: f9049c039ebfeb4cf7a7104a675823cd72dba8297f264b6637062516699fa006
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.4.0"
  universal_file:
    dependency: transitive
    description:
      name: universal_file
      sha256: d1a957fccaad2a32023b62fe435b273ee47aaf2eb804709795e4bf4afff50960
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.0"
  universal_platform:
    dependency: transitive
    description:
      name: universal_platform
      sha256: "64e16458a0ea9b99260ceb5467a214c1f298d647c659af1bff6d3bf82536b1ec"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "9d06212b1362abc2f0f0d78e6f09f726608c74e3b9462e8368bb03314aa8d603"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.1"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "6fc2f56536ee873eeb867ad176ae15f304ccccc357848b351f6f0d8d4a40d193"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "16a513b6c12bb419304e72ea0ae2ab4fed569920d1c7cb850263fe3acc824626"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.3.2"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "4e9ba368772369e3e08f231d2301b4ef72b9ff87c31192ef471b380ef29a4935"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "17ba2000b847f334f16626a574c702b196723af2a289e7a93ffcb79acff855c2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.2.2"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "44cf3aabcedde30f2dba119a9dea3b0f2672fbe6fa96e85536251d678216b3c4"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.3"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "27d5fefe86fb9aace4a9f8375b56b3c292b64d8c04510df230f849850d912cb7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.15"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: "99fd9fbd34d9f9a32efd7b6a6aae14125d8237b10403b422a6a6dfeac2806146"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.13"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "1b4b9e706a10294258727674a340ae0d6e64a7231980f9f9a3d12e4b42407aad"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.16"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.1.4"
  version_manipulation:
    dependency: transitive
    description:
      name: version_manipulation
      sha256: e90782d610bde19765d2808ec06bc8ed9e04640a4dd07d1a3d370728ce9dae7f
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.2.0"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: "4a8c3492d734f7c39c2588a3206707a05ee80cef52e8c7f3b2078d430c84bc17"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.9.2"
  video_player_android:
    dependency: "direct main"
    description:
      name: video_player_android
      sha256: "0fc42778d794465f12456ccdade3e729e4339c8a112f9e58d170dc00f17b75f2"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.11"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "33224c19775fd244be2d6e3dbd8e1826ab162877bd61123bf71890772119a2b7"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.6.5"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "229d7642ccd9f3dc4aba169609dd6b5f3f443bb4cc15b82f7785fcada5af9bbb"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.2.3"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "881b375a934d8ebf868c7fb1423b2bfaa393a0a265fa3f733079a86536064a10"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.3.3"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "5c5f338a667b4c644744b661f309fb8080bb94b18a7e91ef1dbd343bed00ed6d"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "14.2.5"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: "36c88af0b930121941345306d259ec4cc4ecca3b151c02e3a9e71aede83c615e"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.10"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "70e780bc99796e1db82fe764b1e7dcb89a86f1e5b3afb1db354de50f2e41eb7a"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.2.2"
  web:
    dependency: "direct overridden"
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  web_socket:
    dependency: transitive
    description:
      name: web_socket
      sha256: "3c12d96c0c9a4eec095246debcea7b86c0324f22df69893d538fcc6f1b8cce83"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "0.1.6"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: "9f187088ed104edd8662ca07af4b124465893caf063ba29758f97af57e61da8f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.1"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "392c1d83b70fe2495de3ea2c84531268d5b8de2de3f01086a53334d8b6030a88"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.0.4"
  webview_flutter_android:
    dependency: "direct main"
    description:
      name: webview_flutter_android
      sha256: a374702564762a562cb6bcd289655c1176ff7c52fcd484685c8487634b8838a3
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.8.8"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "812165e4e34ca677bdfbfa58c01e33b27fd03ab5fa75b70832d4b7d4ca1fa8cf"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.9.5"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: a5364369c758892aa487cbf59ea41d9edd10f9d9baf06a94e80f1bd1b4c7bbc0
      url: "https://pub.niimbot.info"
    source: hosted
    version: "2.9.5"
  wechat_assets_picker:
    dependency: "direct main"
    description:
      name: wechat_assets_picker
      sha256: "1d50105517eb6ca3406ea86cf534253d9f009376ecf41255fbfa7acecb2310af"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "8.4.4"
  wechat_camera_picker:
    dependency: "direct main"
    description:
      name: wechat_camera_picker
      sha256: "273912967f42c8e994e01efd73569bc4ff8d39b60484220863e099aaf86dd722"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "4.3.1"
  wechat_picker_library:
    dependency: "direct overridden"
    description:
      name: wechat_picker_library
      sha256: "4600530b1a166254533f1b10c3a296f46f92c6bade9aa5eb22adc317438a8c0f"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.0.4"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "154360849a56b7b67331c21f09a386562d88903f90a1099c5987afc1912e1f29"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "5.10.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "7a3f37b05d989967cdddcbb571f1ea834867ae2faa29725fd085180e0883aa15"
      url: "https://pub.niimbot.info"
    source: hosted
    version: "1.1.0"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.niimbot.info"
    source: hosted
    version: "6.5.0"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: b9da305ac7c39faa3f030eccd175340f968459dae4af175130b3fc47e40d76ce
      url: "https://pub.niimbot.info"
    source: hosted
    version: "3.1.3"
sdks:
  dart: ">=3.5.0 <4.0.0"
  flutter: ">=3.24.0"
