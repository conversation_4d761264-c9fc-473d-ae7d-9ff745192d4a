import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';

import '../../../utils/templateLayout/layout_helper.dart';
import 'label_category_list_model.dart';
import 'label_details_page.dart';

class LabelDetailContainerPage extends StatefulWidget {
  Item labelData;

  LabelDetailContainerPage(this.labelData, {Key? key}) : super(key: key);

  @override
  LabelDetailContainerPageState createState() => LabelDetailContainerPageState();
}

class LabelDetailContainerPageState extends State<LabelDetailContainerPage> with PageVisibilityObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      showBottomSheet(context);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    PageVisibilityBinding.instance.addObserver(this, ModalRoute.of(context) as Route);
  }

  @override
  void dispose() {
    PageVisibilityBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void onBackground() {
    super.onBackground();
    //修复后台进入前台底部变灰
    // close();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          bottom: false,
          top: false,
          child: Container(
            color: Colors.transparent,
            // color: Colors.yellow,
          ),
        ));
  }

  close() {
    CustomNavigation.pop(result: {"isAnimated": false});
  }

  showBottomSheet(BuildContext acontext) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
      builder: (BuildContext context) {
        return LabelDetailsPage(
          widget.labelData,
        );
      },
    ).then((value) async {
      close();
      Map<String, dynamic>? layoutTemplate = await LayoutHelper().getLayoutTemplate(value.rawJson);
      layoutTemplate!["name"] = intlanguage('app100000728', '未命名模板');
      if (value != null && value is Item) {
        CustomNavigation.gotoNextPage('toCanvasPage', {'labelData': layoutTemplate, "isCustomLabel": false});
      }
    });
  }
}
