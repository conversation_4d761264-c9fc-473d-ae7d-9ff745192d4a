import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter_canvas_plugins_interface/plugin/host_method_interface.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:text/app_config.dart';
import 'package:text/application.dart';
import 'package:text/business/app/offline_manager.dart';
import 'package:text/cap/meeting_manager.dart';
import 'package:text/network/http_api.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/hardware_manager.dart';
import 'package:text/business/app/app_config_manager.dart';

import '../../../network/dio_utils.dart';
import '../../../utils/common_fun.dart';
import '../../../utils/templateLayout/layout_helper.dart';

class HostMethodImpl implements HostMethodInterface {
  @override
  String getCurrentLanguageType() {
    return Application.currentAppLanguageType;
  }

  /// 获取缓存地址
  @override
  String getCurrentDeviceCachePath() {
    return Application.localCachePath;
  }

  @override
  String getI18nString(String stringCode, String defaultStr, {List<String>? param, bool isUseDefaultValue = true}) {
    return intlanguage(stringCode, defaultStr, param: param, isUseDefaultValue: isUseDefaultValue);
  }

  @override
  Future<bool> getIsShowRfid() {
    return HardWareManager.instance().isHardwareSupportRecordRfid();
  }

  @override
  Future<Map<String, dynamic>> requestOcr(String photoPath, int type) async {
    try {
      Dio dio = Dio();
      //DioUtils.instance.getDio().options.headers;
      dio.options.headers
          .addAll({"niimbot-user-agent": Application.agent, 'languageCode': Application.currentAppLanguageType});
      dio.options.connectTimeout = Duration(milliseconds: 20000);
      dio.options.sendTimeout = Duration(milliseconds: 20000);
      // 设置接收超时时间（单位：毫秒）
      dio.options.receiveTimeout = Duration(milliseconds: 20000);
      (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
        ///抓Https包设置
        client.badCertificateCallback = (X509Certificate cert, String host, int port) => Platform.isAndroid;
      };
      String path = OcrApi.ocrRecognition["path"] as String;
      String url = AppConfig.of(Application.allContext!)!.apiBaseUrl + path;
      print("requestOcr url $url");
      Map<String, dynamic> map = Map();
      map["photo"] = await MultipartFile.fromFile(photoPath);
      map["type"] = type;
      Response response = await dio.post(url, data: FormData.fromMap(map));
      var data = response.data;
      print("requestOcr data $data");
      if (data.containsKey("data")) {
        return data["data"];
      } else {
        return {};
      }
    } catch (e, stack) {
      print("error==$stack");
      return {};
    }
  }

  @override
  bool isProductEnv() {
    return Application.isProductEnv();
  }

  @override
  void toPrintSettingPage(Map<String, dynamic> arguments) {
    CustomNavigation.gotoNextPage('ToPrintSettingPage', arguments);
  }

  // @override
  // Future<int> showChangeContentExitAlert(BuildContext context, String canvasJson) {
  //   Completer<int> completer = Completer();
  //
  //   List<ExitSheetData> exitSheetDataList = [];
  //
  //   ///保存
  //   ExitSheetData saveSheet = ExitSheetData(
  //       title: intlanguage('app00017', '保存'),
  //       sheetClickAction: () {
  //         ToNativeMethodChannel().saveTemplate(canvasJson, isNeedAlert: false, isOtherSave: false).then((value) {
  //           completer.complete(1);
  //         });
  //       });
  //   exitSheetDataList.add(saveSheet);
  //
  //   ///不保存
  //   ExitSheetData notSaveSheet = ExitSheetData(
  //       title: intlanguage('app00111', '不保存'),
  //       saveType: SaveType.notSave,
  //       sheetClickAction: () {
  //         completer.complete(2);
  //       });
  //   exitSheetDataList.add(notSaveSheet);
  //
  //   showCustomListDialog(context, intlanguage('app00110', '是否保存后退出'), '', exitSheetDataList, (index) {
  //     if (index == -1) {
  //       completer.complete(0);
  //     } else {
  //       exitSheetDataList[index].sheetClickAction.call();
  //     }
  //   });
  //   return completer.future;
  // }
  //
  // @override
  // Future<String?> updateTemplate(String canvasJson) async {
  //   return await ToNativeMethodChannel().saveTemplate(canvasJson, isNeedAlert: false, isOtherSave: false);
  // }

  @override
  requestLiveCodeList(
      Map<String, dynamic> params, Function(Map data) success, Function(int errorCode, String errorMsg) fail) {
    Options options = new Options(
      receiveTimeout: Duration(seconds: 20),
      sendTimeout: Duration(seconds: 20),
    );
    DioUtils.instance.requestNetwork<Map<String, dynamic>>(Method.get, LiveCode.liveCodeList,
        queryParameters: params, options: options, onSuccess: (data) {
      if (data != null) {
        success(data);
      } else {
        fail(-1, "data解析失败");
      }
    }, onError: (code, message) {
      fail(code, message);
    });
  }

  @override
  Future<int> customCheckConnectivity() async {
    var result = await NiimbotNetworkManager().getStatus();
    return result.index;
  }

  @override
  Future<Map> getCapHeaderAndRowData() async {
    return await MeetingManager.instance.getHeadersAndRowData();
  }

  @override
  bool isAdvancedQrEntranceSupported(String currentLanguage) {
    return AppConfigManager().isAdvancedQrEntranceSupported(currentLanguage);
  }

  @override
  bool isCustomFormEntranceSupported(String currentLanguage) {
    return AppConfigManager().isCustomFormEntranceSupported(currentLanguage);
  }

  @override
  Future<bool?> jumpToTemplateFeedbackPage({withContainer = true}) async {
    final bool actualContainer = Platform.isIOS ? false : withContainer;
    bool? feedbackResult =
        await CustomNavigation.gotoPage("templateFeedback", {"source": "canvas"}, withContainer: actualContainer);
    return feedbackResult;
  }

  @override
  bool getRtlSwitchStatus() {
    return Application.getRtlSwitchStatus();
  }

  @override
  Future<Map<String, dynamic>?> getTemplateLayoutFronLabel(Map<String, dynamic> labelInfo) async {
    int source = 2;
    List? inputAreas = labelInfo['inputAreas'];
    if (inputAreas?.length == 1) {
      source = 4;
    }
    Map<String, dynamic>? templateInfo = await LayoutHelper().getLayoutTemplate(labelInfo, source: source);
    return templateInfo;
  }
}
