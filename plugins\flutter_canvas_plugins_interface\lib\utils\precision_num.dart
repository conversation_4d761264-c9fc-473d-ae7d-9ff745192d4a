import 'dart:math' as math;
import 'dart:ui';

import 'display_util.dart';

extension PrecisionDouble on num {
  /// 图像库图片 px 转换到 json 的 mm
  num px2mm() {
    return (this / DisplayUtil.pxRatio).digits(6);
  }

  /// 画板设计像素 dp 转换到 json 的 mm
  num dp2mm() {
    return (this / DisplayUtil.dpRatio).digits(6);
  }

  /// json mm 转换到 画板设计像素 dp
  num mm2dp() {
    return (this * DisplayUtil.dpRatio).digits(6);
  }

  /// 画板设计像素 dp 转换到图像库像素 px
  num dp2px() {
    return (this * DisplayUtil.devicePixelRatio).digits(6);
  }

  /// 保留小数位 (四舍五入)
  num digits(int n) {
    return double.parse(toStringAsFixed(n));
  }

  /// 保留小数位 (去尾法)
  num digitsRoundOff(n) {
    /// 取整截取后欢迎小数位
    num multiple = math.pow(10, n);
    double target = (this * multiple).floor() / multiple;
    return target;
  }
}

extension PrecisionSize on Size {
  /// 图像库图片 px 转换到 json 的 mm
  Size px2mm() {
    return Size(
      width.px2mm().toDouble(),
      height.px2mm().toDouble(),
    );
  }

  /// 画板设计像素 dp 转换到 json 的 mm
  Size dp2mm() {
    return Size(
      width.dp2mm().toDouble(),
      height.dp2mm().toDouble(),
    );
  }

  /// json mm 转换到 画板设计像素 dp
  Size mm2dp() {
    return Size(
      width.mm2dp().toDouble(),
      height.mm2dp().toDouble(),
    );
  }
}

extension PrecisionOffset on Offset {
  /// 图像库图片 px 转换到 json 的 mm
  Offset px2mm() {
    return Offset(
      dx.px2mm().toDouble(),
      dy.px2mm().toDouble(),
    );
  }

  /// 画板设计像素 dp 转换到 json 的 mm
  Offset dp2mm() {
    return Offset(
      dx.dp2mm().toDouble(),
      dy.dp2mm().toDouble(),
    );
  }

  /// json mm 转换到 画板设计像素 dp
  Offset mm2dp() {
    return Offset(
      dx.mm2dp().toDouble(),
      dy.mm2dp().toDouble(),
    );
  }
}

extension PrecisionRect on Rect {
  /// 图像库图片 px 转换到 json 的 mm
  Rect px2mm() {
    return Rect.fromLTWH(
      left.px2mm().toDouble(),
      top.px2mm().toDouble(),
      width.px2mm().toDouble(),
      height.px2mm().toDouble(),
    );
  }

  /// 画板设计像素 dp 转换到 json 的 mm
  Rect dp2mm() {
    return Rect.fromLTWH(
      left.dp2mm().toDouble(),
      top.dp2mm().toDouble(),
      width.dp2mm().toDouble(),
      height.dp2mm().toDouble(),
    );
  }

  /// json mm 转换到 画板设计像素 dp
  Rect mm2dp() {
    return Rect.fromLTWH(
      left.mm2dp().toDouble(),
      top.mm2dp().toDouble(),
      width.mm2dp().toDouble(),
      height.mm2dp().toDouble(),
    );
  }
}

extension PrecisionInt on int {
  /// 图像库图片 px 转换到 画板设计像素 dp
  double px2dp() {
    return double.parse(
        (this / DisplayUtil.pxRatio * DisplayUtil.dpRatio).toStringAsFixed(6));
  }
}
