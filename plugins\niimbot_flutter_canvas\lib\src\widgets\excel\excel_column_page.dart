import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/plugin/excel_data.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:niimbot_excel/models/data_source.dart';
import 'package:niimbot_excel/niimbot_excel_utils.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/canvas_element.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/column_preview_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/data_import_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/dynamic_source_data_manager.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/escape_utils.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_bind_info.dart';
import 'package:niimbot_flutter_canvas/src/model/excel/excel_transform_manager.dart';
import 'package:niimbot_flutter_canvas/src/utils/loading_mix.dart';
import 'package:niimbot_flutter_canvas/src/utils/theme_color.dart';
import 'package:niimbot_flutter_canvas/src/widgets/attribute_panel/comm/items_selector_popmenu_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/item_divider.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/svg_icon.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/excel_list_page.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/field_manager/field_manager_view.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/import_excel_helper.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/import_type_page.dart';
import 'package:niimbot_flutter_canvas/src/widgets/good_lib/good_field_manager.dart';
import 'package:tuple/tuple.dart';
/// Excel选择列
class ExcelColumnPage extends StatefulWidget {
  /// 1、导入数据tab栏已选列进入（不需要经过Excel列表界面进入）
  /// 2、画板已导入Excel的情况，支持Excel导入的元素未关联已导入的Excel进入（不需要经过Excel列表界面进入）
  /// 3、文本、一维码、二维码和表格内容切换列进入
  /// 0-其他情况进入（只能从Excel列表界面进入）
  final int businessType;
  final int bindDataType; //绑定数据类型 0 excel 1 商品库
  // final int excelId;
  final String excelMd5;
  final String excelName;
  TemplateBindInfo? templateBindInfo;
  bool isFromElement;
  bool isFromTableCell;
  String bindType;
  bool isFromExcelList = false;
  final JsonElement? jsonElement;

  //商品信息 无数据源的时候先选商品在选列会暂时保存
  Map<String, dynamic>? goods;

  ExcelColumnPage({
    super.key,
    required this.businessType,
    this.bindDataType = 0,
    // this.excelId,
    required this.excelMd5,
    required this.excelName,
    this.templateBindInfo = null,
    this.isFromElement = false,
    this.isFromTableCell = false,
    this.bindType = ElementItemType.text,
    this.isFromExcelList = false,
    this.jsonElement,
    this.goods,
  });

  @override
  State<StatefulWidget> createState() => ExcelColumnState();
}

class ExcelColumnState extends State<ExcelColumnPage> {
  int get _businessType => widget.businessType;

  TemplateBindInfo? get _templateBindInfo => widget.templateBindInfo;

  // ExternalData get _externalData => _templateBindInfo?.externalData;
  DataSource? get _dataSource => _templateBindInfo?.dataSource;

  List<ExcelBindInfo> get _selectColumns =>
      _templateBindInfo?.selectColumns ?? [];

  bool get _isFromElement => widget.isFromElement;

  bool get _isFromTableCell => widget.isFromTableCell;

  // int _excelId;
  late String _excelMd5;
  late String _excelName;
  bool _showHeaderSwitch = true;
  bool? _importWithHeader = true;
  List<ExcelBindInfo> _bindColumns = [];
  bool isSingleChoose = false;

  ///预览使用的字段
  TemplateData? copyTemplateData;
  late List<String> copyHeaders;
  late List<List<String>> copyRowData;
  String? sheet;
  bool sheetInit = false;
  bool isSupportRecordRfid = false;
  int rfidBindColumn = 0;
  bool needBuild = true;
  int _clickCounter = 0; // 用于生成点击顺序
  List<ExcelBindInfo> copyBindingColumns = [];

  @override
  void initState() {
    super.initState();
    DataImportUtils.clearBindingMap();
    _excelMd5 = widget.excelMd5;
    _excelName = widget.excelName;
    isSupportRecordRfid = RfidBindCheck.isSupportRecordRfid();
    if (_excelMd5 == _dataSource?.hash &&
        widget.templateBindInfo?.rfidBindColumn != null) {
      rfidBindColumn = widget.templateBindInfo!.rfidBindColumn! + 1;
    }
    _getBindColumns();
    WidgetsBinding.instance.addPostFrameCallback((_) {
       Future.delayed(const Duration(milliseconds: 200), () {
         DynamicSourceDataManager().readDynamicSourceData(_excelMd5).then((value) {
        if (value != null) {
          sheet = value.sheetName;
        }
        sheetInit = true;
        setState(() {});
      });
      });

    });

    //选择逻辑
    // 选择列--列列表逻辑
    // 从数据源面板导入
    // 1：画板没有数据源 ---多选模式，默认全选
    // 2 ：画板有数据源---多选模式，选择已经导入的列
    //
    // 从元素导入
    // 1：元素为绑定元素---单选，选中当前导入的列
    // 2：元素为非绑定元素---多选，默认勾选第一个
    //
    // 3：表格单元格元素为绑定元素---单选，选中当前导入的列
    // 4：表格单元格为非绑定元素---单选，默认勾选第一个
    //
    // 选择列--是否导入表头逻辑
    // 1：从数据源面板导入----显示是否显示表头控件
    // 2：从元素导入---只有文本元素和表格单元格元素显示表头控件
    if (widget.isFromElement) {
      if (widget.isFromTableCell) {
        if (widget.jsonElement != null &&
            widget.jsonElement!.isBindingElement()) {
          isSingleChoose = true;
          ExcelBindInfo outerBindInfo = _selectColumns[0];
          ExcelBindInfo? matchExcelBindInfo = _bindColumns.firstWhere(
              (bindInfo) => bindInfo.column == outerBindInfo.column);
          if (matchExcelBindInfo != null) {
            matchExcelBindInfo.select = true;
            matchExcelBindInfo.bindType = outerBindInfo.bindType;
            matchExcelBindInfo.barcodeType = outerBindInfo.barcodeType;
            matchExcelBindInfo.qrCodeType = outerBindInfo.qrCodeType;
          }
        } else {
          isSingleChoose = true;
        }
      } else {
        if (widget.jsonElement != null &&
            widget.jsonElement!.isBindingElement()) {
          isSingleChoose = true;
          ExcelBindInfo outerBindInfo = _selectColumns[0];
          ExcelBindInfo? matchExcelBindInfo = _bindColumns.firstWhereOrNull(
              (bindInfo) => bindInfo.column == outerBindInfo.column);
          if (matchExcelBindInfo != null) {
            matchExcelBindInfo.select = true;
            matchExcelBindInfo.bindType = outerBindInfo.bindType;
            matchExcelBindInfo.barcodeType = outerBindInfo.barcodeType;
            matchExcelBindInfo.qrCodeType = outerBindInfo.qrCodeType;
          }
        } else {
          isSingleChoose = false;
        }
        //从元素进来 类型优先以bindType为准备
        _bindColumns.forEach((column) {
          column.bindType = widget.bindType;
        });
      }
    } else {
      isSingleChoose = false;
      //处理选择模式
      configSelectedData();
    }
    //表头显示逻辑
    if ((_templateBindInfo?.selectColumns ?? []).isNotEmpty) {
      _importWithHeader = _templateBindInfo?.importWithHeader;
    } else if (widget.bindDataType == 1) {
      _importWithHeader = false;
    }
    //一维码，二维码 表头开关默认为false
    if (widget.isFromElement &&
        widget.jsonElement != null &&
        (widget.jsonElement!.type == ElementItemType.barcode ||
            widget.jsonElement!.type == ElementItemType.qrcode)) {
      _importWithHeader = false;
    }
    //方便生成预览数据 逻辑提前 商品库的数据指定特定的绑定类型
    if (widget.bindDataType == 1) {
      _configBindColumns();
    }
  }

  void configSelectedData() {
    if (_dataSource == null) {
      //新关联从数据源过来的商品库字段默认选中下面的集合
      if (widget.bindDataType == 1) {
        List<String> defaultGoods = [
          intlanguage("app01052", "商品名称"),
          intlanguage("app100000927", "商品条码"),
          intlanguage("app01054", "规格"),
          intlanguage("app01053", "产地"),
          intlanguage("app01056", "零售价")
        ];
        for (var bindInfo in _bindColumns) {
          if (defaultGoods.contains(bindInfo.columnHeader)) {
            bindInfo.select = true;
          }
        }
      } else {
        //Excel默认勾选前4列
        int index = 0;
        for (var bindInfo in _bindColumns) {
          if (index < 4) {
            bindInfo.select = true;
            index = index + 1;
          }
        }
      }
    } else {
      for (var i = 0; i < _bindColumns.length; i++) {
        ExcelBindInfo excelBindInfo = _bindColumns[i];
        ExcelBindInfo? matchExcelBindInfo = _selectColumns.firstWhereOrNull(
            (bindInfo) => bindInfo.column == excelBindInfo.column);
        if (matchExcelBindInfo != null) {
          excelBindInfo.select = true;
          if (i == 0 && widget.bindDataType == 1) {
            excelBindInfo.bindType = ElementItemType.barcode;
            excelBindInfo.barcodeType = BarcodeType.EAN13;
          } else {
            if (widget.bindDataType == 1 &&
                (matchExcelBindInfo.bindType != ElementItemType.text)) {
              excelBindInfo.bindType = ElementItemType.text;
            } else {
              excelBindInfo.bindType = matchExcelBindInfo.bindType;
            }
            excelBindInfo.barcodeType = matchExcelBindInfo.barcodeType;
            excelBindInfo.qrCodeType = matchExcelBindInfo.qrCodeType;
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // if(needBuild == false){
    //   return Container();
    // }
    isSupportRecordRfid = RfidBindCheck.isSupportRecordRfid();
    if (!isSupportRecordRfid) {
      rfidBindColumn = 0;
    }
    double marginBottom = 0;
    if (_showHeaderSwitch) {
      marginBottom = 96;
    }
    return ClipRRect(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
        child: Container(
            height: MediaQuery.sizeOf(context).height -
                MediaQuery.viewPaddingOf(context).top -
                52,
            color: ThemeColor.COLOR_F5F5F5,
            child: Stack(
              alignment: Alignment.bottomLeft,
              children: [
                Column(children: [
                  _buildTitleWidget(),
                  if (_ifShowRfidBind()) _buildRfidBindPreviewToWidget(),
                  _buildPreviewToWidget(),
                  Expanded(
                      child: SingleChildScrollView(
                          scrollDirection: Axis.vertical,
                          physics: ClampingScrollPhysics(),
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            margin: EdgeInsets.only(bottom: marginBottom),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (_ifShowRfidBind())
                                  _buildRfidBindSettingWidget(),
                                if (!_isRfidBindLegal())
                                  _buildRfidBindIllegalWidget(),
                                // const SizedBox(
                                //   height: 12,
                                // ),
                                if (_ifShowRfidBind())
                                  Container(
                                    margin:
                                        EdgeInsetsDirectional.only(start: 16),
                                    padding:
                                        EdgeInsetsDirectional.only(top: 12),
                                    child: Text(
                                        intlanguage('app100001237', '以下为打印内容'),
                                        style: TextStyle(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w400,
                                            color: ThemeColor.COLOR_999999)),
                                  ),
                                Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 10, 0, 0)),
                                _buildColumnListWidget(),
                                Padding(
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        0, 20, 0, 0)),
                              ],
                            ),
                          ))),
                ]),
                _buildHeaderSwitchWidget(),
              ],
            )));
  }

  Widget _buildHeaderSwitchWidget() {
    if (_showHeaderSwitch) {
      return Container(
        decoration: BoxDecoration(color: Colors.white, boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),//Color(0x0D000000),
            offset: const Offset(0, 4),
            blurRadius: 12,
          )
        ]),
        padding: EdgeInsetsDirectional.fromSTEB(
            16,
            12,
            16,
            MediaQuery.viewPaddingOf(context).bottom == 0
                ? 34
                : MediaQuery.viewPaddingOf(context).bottom),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: ThemeColor.COLOR_FAFAFA,
                  borderRadius: BorderRadius.all(Radius.circular(12))),
              child: Row(
                children: [
                  const SizedBox(
                    width: 16,
                  ),
                  Text(
                      widget.bindDataType == 1
                          ? intlanguage('app100001156', '显示字段名') //商品库扩展字段修改
                          : intlanguage('app100001157', '打印列名'),
                      style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w400,
                          color: ThemeColor.COLOR_262626)),
                  Spacer(),
                  CupertinoSwitch(
                    value: _importWithHeader ?? true,
                    onChanged: (bool on) {
                      _importWithHeader = on;
                      setState(() {});
                      int dataSource;
                      if (widget.bindDataType == 0) {
                        dataSource = 1;
                      } else {
                        dataSource = 2;
                      }
                      int clickType = on ? 1 : 0;
                      CanvasPluginManager()
                          .nativeMethodImpl
                          ?.sendTrackingToNative({
                        "track": "click",
                        "posCode": "108_072_211",
                        "ext": {
                          "click_type": clickType,
                          "data_source": dataSource,
                          "temp_id": ExcelTransformManager().templateData?.id,
                          "industry_temp_id": ExcelTransformManager().templateData?.cloudTemplateId ?? "",
                        }
                      });
                    },
                    activeColor: CanvasTheme.of(context).highlightColor,
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 6,
            ),
            Offstage(
              offstage: widget.bindDataType == 1,
              child: Row(
                children: [
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    child: Text(
                        intlanguage(
                            'app100000823', '示例：显示列名 【品名：农夫山泉】  不显示列名【农夫山泉】'),
                        style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w400,
                            color: ThemeColor.COLOR_999999)),
                  ),
                ],
              ),
            )
          ],
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _buildRfidBindPreviewToWidget() {
    String content = _getRfidBindPreviewContent();
    return Container(
      margin: EdgeInsetsDirectional.fromSTEB(20, 14, 20, 0),
      child: Row(
        children: [
          SvgIcon(
            'assets/excel/rfid_mark_with_text.svg',
            useDefaultColor: false,
          ),
          const SizedBox(
            width: 2,
          ),
          if (!_isRfidBindLegal())
            SvgIcon(
              'assets/excel/rfid_warn.svg',
              useDefaultColor: false,
            ),
          const SizedBox(
            width: 2,
          ),
          Expanded(
            child: Text(
              content,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                color: ThemeColor.COLOR_999999,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildRfidBindSettingWidget() {
    List<String> items = _getRfidBindPopMenuItems();
    return Container(
      height: 44,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(12))),
      child: Row(
        children: [
          const SizedBox(
            width: 12,
          ),
          SvgIcon(
            'assets/excel/rfid_mark_small.svg',
            useDefaultColor: false,
          ),
          const SizedBox(
            width: 8,
          ),
          Text(
            intlanguage('app100001193', 'RFID关联'),
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w400,
                color: ThemeColor.COLOR_262626),
          ),
          const SizedBox(
            width: 6,
          ),
          Expanded(
              child: ItemsSelectorPopUpWidget(
            anchorEdgeInsets: EdgeInsets.zero,
            popHeight: items.length <= 4 ? items.length * 50 : 230,
            items: items,
            initializeIndex: rfidBindColumn >= 0 ? rfidBindColumn : 0,
            itemsSelectedChanged: (int index) {
              rfidBindColumn = index;
              setState(() {});
            },
            dropDown: true,
            firstItemSpecialDivider: true,
          )),
          const SizedBox(
            width: 12,
          )
        ],
      ),
    );
  }

  Widget _buildRfidBindIllegalWidget() {
    return Container(
      margin: EdgeInsetsDirectional.fromSTEB(16, 8, 16, 0),
      child: Text(
        intlanguage('app100001196',
            '当前列不符合RFID规范（字符数在8~32位之间，且为4的倍数，由数字或字母A-F组成，不区分大小写）'),
        style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: ThemeColor.COLOR_F8473E),
      ),
    );
  }

  Widget _buildPreviewToWidget() {
    final canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    if (needBuild == false) {
      return Container();
    }
    bool showRfidBind = _ifShowRfidBind();
    double height = 132;
    return Offstage(
      offstage: isSingleChoose == true,
      child: Container(
        padding:
            EdgeInsetsDirectional.fromSTEB(16, showRfidBind ? 0 : 20, 16, 0),
        child: Column(
          children: [
            Container(
              alignment: Alignment.center,
              margin: EdgeInsetsDirectional.fromSTEB(
                51,
                5,
                51,
                5,
              ),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                  color: Colors.transparent),
              height: height,
              child: sheetInit ? buildPreviewWidget() : Center(child: CircularProgressIndicator(strokeWidth: 10,color: Colors.white,)),//SizedBox.shrink(),
            ),
            Offstage(
              offstage: isSingleChoose == true ||
                  EscapeUtils.isElementOverflow(copyTemplateData) == false,
              child: Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0, 4, 0, 3),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.fromSTEB(0, 2, 0, 0),
                      child: Image.asset(
                        'assets/excel/tip_icon.png',
                        package: 'niimbot_flutter_canvas',
                        fit: BoxFit.fill,
                        width: 14,
                        height: 14,
                        color: Color(0xFF999999),
                      ),
                    ),
                    SizedBox(
                      width: 1,
                    ),
                    Expanded(
                        child: Text(
                      intlanguage('app100001158', '部分勾选内容超出标签纸，点击“完成”后查看完整内容'),
                      maxLines: 5,
                      style: TextStyle(fontSize: 13, color: Color(0xFF999999)),
                    )),
                  ],
                ),
              ),
            ),
            Offstage(
              offstage:
                  !(widget.bindDataType == 1 && widget.isFromElement == false && canvasConfigIml!.isShowFieldManagerEntrance() == true),
              child: GestureDetector(
                onTap: () {
                  //缓存里面没有数据 同时没网 提示异常
                  if (CanvasPluginManager().fontPanelImpl!.isNetReachable() ==
                          false &&
                      GoodFieldManager().goodFieldList.length == 0) {
                    LoadingMix.showToast(intlanguage('app100000354', '网络异常'));
                    return;
                  }
                  _showFieldManagerPage();
                  CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
                    "track": "click",
                    "posCode": "108_310_275",
                    "ext": {}
                  });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      alignment: Alignment.centerRight,
                      child: Text(intlanguage('app100001557', '字段管理'),
                          style: TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                              color: ThemeColor.title)),
                    ),
                    SizedBox(
                      width: 2,
                    ),
                    const SvgIcon(
                      'assets/excel/field_manager_arrow.svg',
                      color: ThemeColor.title,
                      matchTextDirection: true,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildTitleWidget() {
    return Container(
        color: Colors.white,
        height: 46,
        child: Row(
          children: [
            Container(
              padding: Directionality.of(context) == TextDirection.rtl
                  ? EdgeInsets.only(right: 16)
                  : EdgeInsets.only(left: 16),
              width: 80,
              child: Align(
                alignment: Directionality.of(context) == TextDirection.rtl
                    ? Alignment.centerRight
                    : Alignment.centerLeft,
                child: GestureDetector(
                  onTap: () {
                    DataImportUtils.clearBindingMap();
                    Navigator.of(context).pop();
                  },
                  child: Text(intlanguage('app100000692', '取消'),
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: ThemeColor.COLOR_262626)),
                ),
              ),
            ),
            Expanded(
                child: Center(
              child: Text(
                  widget.bindDataType == 1
                      ? (_dataSource == null
                          ? intlanguage('app100001159', '选择字段到模版')
                          : intlanguage('app100001160', '选择商品字段'))
                      : intlanguage('app100001161', '选择打印内容'),
                  style: TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                      color: Colors.black)),
            )),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: _confirmImport,
              child: Container(
                padding: Directionality.of(context) == TextDirection.rtl
                    ? EdgeInsets.only(left: 16)
                    : EdgeInsets.only(right: 16),
                width: 80,
                child: Align(
                  alignment: Directionality.of(context) == TextDirection.rtl
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                  child: Text(
                      //新的数据源流程或者是 Excel切换商品库的时候会执行下面的逻辑
                      getRightTitle().item1,
                      style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: ThemeColor.COLOR_FB4B42)),
                ),
              ),
            ),
          ],
        ));
  }

  Tuple2 getRightTitle() {
    //Tuple2
    if (widget.bindDataType == 1) {
      return _dataSource == null
          ? Tuple2(intlanguage('app01031', '完成'), true)
          :  Tuple2(intlanguage('app00048', '确定'), false);
    } else {
      return Tuple2(intlanguage('app01031', '完成'), true);
    }
  }

  List<Widget> _buildExcelNameWidget() {
    Widget fileName = Flexible(
        child: Text(
      ExcelManager.sharedInstance()
          .getExcelDisplayNameByFullName(_excelName, type: 1),
      style: TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w400,
          color: ThemeColor.COLOR_262626),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    ));
    Widget fileNameSuffix = Text(
      ExcelManager.sharedInstance()
          .getExcelDisplayNameByFullName(_excelName, type: 2),
      style: TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w400,
          color: ThemeColor.COLOR_262626),
      textDirection: TextDirection.ltr,
    );
    if (Directionality.of(context) == TextDirection.rtl) {
      return [fileNameSuffix, fileName];
    } else {
      return [fileName, fileNameSuffix];
    }
  }

  Widget _buildColumnListWidget() {
    List<Widget> widgets = [];
    if (widget.bindDataType == 0) {
      Widget header = Container(
        height: 40,
        child: Row(
          children: [
            Expanded(
              child: Container(
                margin: EdgeInsetsDirectional.only(start: 40),
                child: Text(intlanguage('app100001153', 'Excel列'),
                    //'Excel列',
                    style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: ThemeColor.COLOR_999999)),
              ),
            ),
            widget.isFromTableCell
                ? SizedBox.shrink()
                : Container(
                    width: 112,
                    child: Text(intlanguage('app100001154', '转化为'),
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                            color: ThemeColor.COLOR_999999)),
                  ),
          ],
        ),
      );
      widgets.add(header);
      widgets.add(Container(
        margin: EdgeInsetsDirectional.only(start: 40),
        child: ItemDivider(),
      ));
    }
    for (int i = 0; i < _bindColumns.length; i++) {
      widgets.add(_buildColumnItem(_bindColumns[i]));
      if (i != _bindColumns.length - 1) {
        widgets.add(Container(
          margin: EdgeInsetsDirectional.only(start: 40),
          child: ItemDivider(),
        ));
      }
    }
    return Container(
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(12))),
        child: Column(children: widgets));
  }

  Widget _buildColumnItem(ExcelBindInfo excelBindInfo) {
    Widget checkBox;
    if (_isSingleChooseMode()) {
      if (excelBindInfo.select) {
        checkBox = Container(
            padding: EdgeInsets.all(8),
            child: SvgIcon(
              'assets/excel/tick.svg',
              width: 24,
              height: 24,
              useDefaultColor: false,
            ));
      } else {
        checkBox = const SizedBox(
          width: 40,
          height: 40,
        );
      }
    } else {
      checkBox = Container(
        padding: EdgeInsets.all(10),
        child: SvgIcon(
          'assets/excel/${excelBindInfo.select ? "check_box_checked" : "check_box_unchecked"}.svg',
          width: 20,
          height: 20,
          useDefaultColor: false,
        ),
      );
    }
    return Container(
      height: 44,
      child: Row(
        children: [
          Expanded(
              child: GestureDetector(
                  onTap: () {
                    if (_isSingleChooseMode()) {
                      if (!excelBindInfo.select) {
                        _bindColumns.forEach((element) {
                          element.select = false;
                        });
                        excelBindInfo.select = true;
                        setState(() {});
                      }
                    } else {
                      //防止点击过快 图片刷新不及时的问题 待调试
                      if (!preventQuickTap()) {
                        return;
                      }
                      excelBindInfo.select = !excelBindInfo.select;
                      if (excelBindInfo.select) {
                        excelBindInfo.clickOrder = ++_clickCounter;
                      }
                      copyBindingColumns.sort((a, b) => a.clickOrder.compareTo(b.clickOrder));
                      setState(() {});
                    }
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Row(children: [
                    checkBox,
                    Flexible(
                        child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        excelBindInfo.columnHeader,
                        overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontSize: 15,
                              fontWeight:
                                  _isSingleChooseMode() && excelBindInfo.select
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                              color: Colors.black)),
                    ))
                  ]))),
          if (widget.bindDataType == 0 &&
              excelBindInfo.select &&
              !widget.isFromTableCell)
            Container(
              margin: Directionality.of(context) == TextDirection.rtl
                  ? EdgeInsets.only(left: 12)
                  : EdgeInsets.only(right: 12),
              width: 100,
              height: 24,
              child: GestureDetector(
                  onTap: () => _showBindTypePage(excelBindInfo),
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                    decoration: BoxDecoration(
                        color: ThemeColor.COLOR_F5F5F5,
                        borderRadius: BorderRadius.all(Radius.circular(6))),
                    child: Row(
                      children: [
                        const SizedBox(
                          width: 6,
                        ),
                        Expanded(
                          child: Text(_getBindTypeDes(excelBindInfo.bindType),
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.w400,
                                  color: ThemeColor.COLOR_262626)),
                        ),
                        SvgIcon(
                          'assets/excel/click_arrow_down.svg',
                        ),
                        const SizedBox(
                          width: 6,
                        )
                      ],
                    ),
                  )),
            )
        ],
      ),
    );
  }

  _confirmImport() {
    if(getRightTitle().item2 == true){
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({"track":"click","posCode":"108_341_319","ext":{"refer":"2"}});
    }
    if (!_bindColumns.any((element) => element.select)) {
      LoadingMix.showToast(intlanguage('app100000843', '请选择导入数据'));
      return;
    }
    if (!_isRfidBindLegal()) {
      LoadingMix.showToast(intlanguage('app100001195', 'RFID来源不符合规范'));
      return;
    }
    // TemplateData originTemplateData = ExcelTransformManager().templateData;
    Function importFun = () {
      // 根据点击顺序排序
      _bindColumns.sort((a, b) => a.clickOrder.compareTo(b.clickOrder));
      Map<String, dynamic> map = {};
      map["importWithHeader"] = _importWithHeader;
      // map["excelId"] = _excelId;
      map["excelId"] = _excelMd5;
      map["excelName"] = _excelName;
      map["bindInfoList"] = _bindColumns;
      if (rfidBindColumn > 0) {
        if (_ifShowRfidBind()) {
          map["rfidBindColumn"] = rfidBindColumn - 1;
        } else {
          if (isSupportRecordRfid &&
              widget.bindDataType == 0 &&
              _excelMd5 == _dataSource?.hash) {
            map["rfidBindColumn"] = rfidBindColumn - 1;
          }
        }
      }
      Navigator.pop(context, map);
    };

    importFun();
  }

  clearDefaultData(TemplateData? data) {
    if (data?.canvasElements != null &&
        data?.canvasElements.length == 1 &&
        data?.canvasElements.first.data.type == ElementItemType.text &&
        (data?.canvasElements.first.data.value == "" ||
            data?.canvasElements.first.data.value ==
                intlanguage('app00364', '双击文本框编辑'))) {
      data?.canvasElements.clear();
    }
  }

  _configBindColumns() {
    for (int index = 0; index < _bindColumns.length; index++) {
      if (_bindColumns[index].column == 0) {
        // if (index == 0) {
        _bindColumns[index].bindType = ElementItemType.barcode;
        _bindColumns[index].barcodeType = BarcodeType.EAN13;
      } else {
        _bindColumns[index].bindType = ElementItemType.text;
      }
    }
  }

  _showFieldManagerPage() {
    // ignore: missing_return
    needBuild = false;
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        enableDrag: false,
        shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
        builder: (_) {
          return FieldManagerPage();
        }).then((value) {
      needBuild = true;
      //数据有变更 刷新ui
      if(value == true){
        _getBindColumns();
      if (widget.bindDataType == 1) {
        _configBindColumns();
      }
      initPreviewData();
      }
      setState(() {});
    });
  }

  _showExcelListPage() {
    final excelImportIml = CanvasPluginManager().excelImportImpl;
    if (excelImportIml == null) {
      // LoadingMix.showToast("ExcelImportInterface未实现");
      return;
    }
    excelImportIml.getSupportSocialList().then((value) {
      List<String> socialList = [];
      value.forEach((element) {
        socialList.add(element);
      });
      showModalBottomSheet(
          barrierColor: Colors.transparent,
          isScrollControlled: true,
          context: context,
          backgroundColor: Colors.transparent,
          enableDrag: false,
          builder: (_) {
            return ExcelListPage(
              businessType: 1,
              supportSocialList: socialList,
              templateBindInfo: _templateBindInfo,
              isFromElement: _isFromElement,
              isFromTableCell: _isFromTableCell,
            );
          }).then((value) {
        if (value != null && value is Map) {
          if (value["excelId"] != _excelMd5) {
            _excelMd5 = value["excelId"];
            _excelName = value["excelName"];
            if (_dataSource != null && _dataSource!.hash == _excelMd5) {
              _importWithHeader = _templateBindInfo!.importWithHeader;
            } else {
              _importWithHeader = true;
            }
            _getBindColumns();
            setState(() {});
          }
        }
      });
    });
  }

  _showBindTypePage(ExcelBindInfo excelBindInfo) {
    showModalBottomSheet(
        barrierColor: CanvasTheme.of(context).barrierColor,
        // isScrollControlled: true,
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        enableDrag: false,
        builder: (_) {
          return ImportTypePage(
            excelHeader: excelBindInfo.columnHeader,
            bindType: excelBindInfo.bindType,
            barcodeType: excelBindInfo.barcodeType,
            qrcodeType: excelBindInfo.qrCodeType,
          );
        }).then((value) {
      if (value != null && value is Map) {
        excelBindInfo.bindType = value["bindType"];
        excelBindInfo.barcodeType = value["barcodeType"];
        excelBindInfo.qrCodeType = value["qrcodeType"];
        setState(() {});
        String moduleName = "";
        int dataSource;
        if (widget.bindDataType == 0) {
          dataSource = 1;
        } else {
          dataSource = 2;
        }
        if (excelBindInfo.bindType == ElementItemType.text) {
          moduleName = "文本";
        } else if (excelBindInfo.bindType == ElementItemType.barcode) {
          moduleName = "一维码";
        } else if (excelBindInfo.bindType == ElementItemType.qrcode) {
          moduleName = "二维码";
        }
        CanvasPluginManager().nativeMethodImpl?.sendTrackingToNative({
          "track": "click",
          "posCode": "108_072_209",
          "ext": {
            "module_name": moduleName,
            "data_source": dataSource,
            "temp_id": ExcelTransformManager().templateData?.id,
            "industry_temp_id":
                ExcelTransformManager().templateData?.cloudTemplateId
          }
        });
      }
    });
  }

  static DateTime? _lastTime;

  preventQuickTap({int? interval}) {
    DateTime _nowTime = DateTime.now();
    if (_lastTime == null ||
        _nowTime.difference(_lastTime!) >
            Duration(milliseconds: interval ?? 300)) {
      _lastTime = _nowTime;
      return true;
    } else {
      _lastTime = _nowTime;
      return false;
    }
  }

  initPreviewData() {
    final originTemplateData = ExcelTransformManager().templateData;
    copyTemplateData = EscapeUtils.cloneTemplateData(originTemplateData!);
    //删除“双击文本框编辑”这个element
    if (widget.isFromElement != true) {
      clearDefaultData(copyTemplateData);
    }

    if (widget.isFromExcelList || widget.goods != null) {
      if (widget.goods != null && copyTemplateData != null) {
        //新关联数据源的场景下会需要初始化datasource header rowdata等数据
        ImportExcelHelper.getPreviewData(
            goods: widget.goods!, templateData: copyTemplateData!);
      }
      copyHeaders = [
        ...ExcelManager.sharedInstance().dataSourceWrapper?.headers ?? []
      ];
      copyRowData = [
        ...ExcelManager.sharedInstance().dataSourceWrapper?.rowData ?? []
      ];
      copyTemplateData?.dataSource = [
        EscapeUtils.cloneDataSource(
            ExcelManager.sharedInstance().dataSourceWrapper?.dataSource)
      ];
      copyTemplateData?.dataSource?.first.type = widget.bindDataType == 0
          ? DataSourceType.excel
          : DataSourceType.commodity;
    } else {
      copyHeaders = [...ExcelTransformManager().headers ?? []];
      copyRowData = [...ExcelTransformManager().rowData ?? []];
      refreshExcelTransformManagerInfo();
    }
    ColumnPreviewManager().initData(copyTemplateData, copyHeaders, copyRowData,
        sheet, _excelMd5 == _dataSource?.hash);
  }

  //对于数据源已经灌入的场景下直接从画板点击选列 进入字段管理页面返回后 需要刷新 header rowData
  void refreshExcelTransformManagerInfo() {
    if (widget.bindDataType == 1) {
      copyHeaders = TemplateData.goodsInfnFieldDescName();
      if (copyRowData.length > 0 && copyRowData[0].isNotEmpty &&
          copyRowData[0].length - 3 < copyHeaders.length) {
        var diff = copyHeaders.length - (copyRowData[0].length - 3);
        for (var element in copyRowData) {
          for (var i = 0; i < diff; i++) {
            element.insert(copyHeaders.length - 1 + i, '');
          }
        }
      }
      ExcelManager.sharedInstance().dataSourceWrapper?.headers = [...copyHeaders];
      ExcelManager.sharedInstance().dataSourceWrapper?.rowData = [...copyRowData];
    }
    ExcelTransformManager().headers = [...copyHeaders];
    ExcelTransformManager().rowData = [...copyRowData];
    CanvasPluginManager().goodsImportImpl?.notifyGoodsFieldUpdate();
  }

  Widget buildPreviewWidget() {
    initPreviewData();
    List<ExcelBindPair> bindPairs = [];

    copyBindingColumns.forEach((element) {
      int codeType = 0;
      if (element.bindType == ElementItemType.barcode) {
        codeType = element.barcodeType;
      } else if (element.bindType == ElementItemType.qrcode) {
        codeType = element.qrCodeType;
      }
      ExcelBindPair excelBindPair = ExcelBindPair(
          columnIndex: element.column,
          open: element.select,
          bindElementType: element.bindType,
          codeType: codeType);
      bindPairs.add(excelBindPair);
    });

    CanvasElement? anchorCanvasElement;
    if (widget.jsonElement != null) {
      anchorCanvasElement = copyTemplateData?.canvasElements.firstWhereOrNull(
          (element) => element.data.id == widget.jsonElement?.id);
    }
    //根据比例换算宽高
    double reqWidth = MediaQuery.sizeOf(context).width - 134;
    double reqHeight = 122;
    double templateWidth = ExcelTransformManager().templateData!.designWidth ?? 0;
    double templateHeight = ExcelTransformManager().templateData!.designHeight ?? 0;
    double ratio = 1;

    if (templateHeight > reqHeight || templateWidth > reqWidth) {
      // 计算出实际宽高和目标宽高的比率
      double heightRatio = templateHeight / reqHeight;
      double widthRatio = templateWidth / reqWidth;
      ratio = heightRatio < widthRatio ? widthRatio : heightRatio;
      templateHeight = templateHeight / ratio;
      templateWidth = templateWidth / ratio;
    }

    return ColumnPreviewManager().buildPreviewWidget(
        context,
        anchorCanvasElement,
        bindPairs,
        _importWithHeader,
        _dataSource?.hash,
        templateWidth,
        templateHeight);
  }

  _getBindColumns() {
    List<int> selectColumns = _bindColumns
        .where((info) => info.select == true)
        .map((e) => e.column)
        .toList();
    _bindColumns.clear();
    // List<String> headers = ExcelManager.sharedInstance().getExcelHeaders(_excelId, externalData: _externalData);
    List<String> headers = [];
    List<List<String>> rowData = [];
    if (widget.bindDataType == 0) {
      if (widget.isFromExcelList) {
        headers =
            ExcelManager.sharedInstance().dataSourceWrapper?.headers ?? [];
        rowData =
            ExcelManager.sharedInstance().dataSourceWrapper?.rowData ?? [];
      } else {
        headers = ExcelTransformManager().headers ?? [];
        rowData = ExcelTransformManager().rowData ?? [];
      }
    } else {
      headers = TemplateData.goodsInfnFieldDescName();
      GoodFieldManager()
          .goodFieldList
          .sort((a, b) => (a.columnIndex ?? 0).compareTo(b.columnIndex ?? 0));
    }
    if (headers.isNotEmpty) {
      for (int i = 0; i < headers.length; i++) {
        if (widget.bindDataType == 1) {
          //过滤删除列
          if (GoodFieldManager().goodFieldList[i].isDeleted == true) {
            continue;
          }
        }
        //过滤空列
        int emptyColumnCount = 0;
        for (int j = 0; j < rowData.length; j++) {
          if (rowData[j][i].isNotEmpty) {
            break;
          } else {
            emptyColumnCount++;
          }
        }
        //整列为空（包含列名和内容都为空才算整列为空）
        if (emptyColumnCount == rowData.length &&
            headers[i].isEmpty &&
            widget.bindDataType == 0) {
          continue;
        }

        ExcelBindInfo excelBindInfo = ExcelBindInfo();
        // excelBindInfo.excelId = _excelId;
        excelBindInfo.excelMd5 = _excelMd5;
        excelBindInfo.column = i;
        if (headers[i].isEmpty) {
          String columnLetter = NiimbotExcelUtils.indexToLetters(i + 1);
          excelBindInfo.columnHeader =
              "${intlanguage("app100001121", "列")}${columnLetter}";
          // excelBindInfo.columnHeader = "列名${i + 1}";
        } else {
          excelBindInfo.columnHeader = headers[i];
        }
        if (widget.bindDataType == 1 && i == 0) {
          excelBindInfo.bindType = ElementItemType.barcode;
          excelBindInfo.barcodeType = BarcodeType.EAN13;
        }
        _bindColumns.add(excelBindInfo);
      }
    }

    ///商品库模版对_bindColumns 按sortIndex进行排序
    if (widget.bindDataType == 1) {
      Map<String, int> fieldsSortInfoMap =
          GoodFieldManager().getFieldsSortInfoMap();
      List<String> fieldKeys = TemplateData.goodsInfnFieldName();

      ///预处理templateGoodFields，增加排序信息
      for (var i = 0; i < _bindColumns.length; i++) {
        print("_bindColumns column ${_bindColumns[i].column}");
        ExcelBindInfo field = _bindColumns[i];
        if (selectColumns.contains(field.column)) {
          field.select = true;
        }
        // String fieldKey = fieldKeys[i];
        String fieldKey = fieldKeys[field.column];
        field.sortIndex = fieldsSortInfoMap[fieldKey] ?? 0;
      }

      ///对所有字段按sortIndex进行排序
      _bindColumns.sort((a, b) {
        return (a.sortIndex ?? 0).compareTo(b.sortIndex ?? 0);
      });
    }

    // 创建副本列表
    copyBindingColumns = [..._bindColumns];

    for (var item in copyBindingColumns) {
      if (item.select) {
        item.clickOrder = _clickCounter++; // 设置点击顺序
      }
    }
  }

  String _getBindTypeDes(String bindType) {
    if (bindType == ElementItemType.barcode) {
      return intlanguage("app00003", "一维码");
    }
    if (bindType == ElementItemType.qrcode) {
      return intlanguage("app00004", "二维码");
    }
    return intlanguage("app00002", "文本");
  }

  bool _isSingleChooseMode() {
    return isSingleChoose;
  }

  bool _ifShowRfidBind() {
    //同时满足3个条件：设备型号为B32R，Excel导入流程，多选模式
    return isSupportRecordRfid && widget.bindDataType == 0 && !isSingleChoose;
  }

  List<String> _getRfidBindPopMenuItems() {
    List<String> items = [];
    items.add(intlanguage('app100001194', '不关联'));
    items.addAll(_bindColumns.map((e) => e.columnHeader).toList());
    return items;
  }

  String _getRfidBindPreviewContent() {
    String? content;
    if (rfidBindColumn > 0) {
      if (_dataSource != null && _excelMd5 == _dataSource?.hash) {
        content =
            ExcelTransformManager.sharedInstance().getCellValue(rfidBindColumn);
      } else {
        content = ExcelManager.sharedInstance().dataSourceWrapper?.rowData[0]
            [rfidBindColumn - 1];
      }
    }
    return content ?? '';
  }

  bool _isRfidBindLegal() {
    //不用绑定rfid的情况认为合法
    if (!_ifShowRfidBind()) {
      return true;
    }
    //没有绑定rfid的情况认为合法
    if (rfidBindColumn <= 0) {
      return true;
    }
    String content = _getRfidBindPreviewContent();
    return RfidBindCheck.isRfidContentLegal(content);
  }
}
