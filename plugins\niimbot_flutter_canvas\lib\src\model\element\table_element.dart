import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/shared/element_attribute_def.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:flutter_canvas_plugins_interface/utils/precision_num.dart';
import 'package:niimbot_flutter_canvas/src/model/element/json_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/table_cell_element.dart';
import 'package:niimbot_flutter_canvas/src/utils/template_utils.dart';

Logger _logger = Logger("CellPosition", on: kDebugMode);

class CellPosition {
  int columnIndex;
  int rowIndex;

  CellPosition(this.columnIndex, this.rowIndex);

  @override
  String toString() {
    return 'CellPosition(columnIndex:$columnIndex, rowIndex:$rowIndex)';
  }
}

class TableElement extends JsonElement {
  int? lineType;
  double lineWidth;
  int row;
  int column;
  List<double> rowHeight;
  List<double> columnWidth;
  List<TableCellElement> cells;
  List<TableCellElement> combineCells;
  bool allowFreeZoom;
  int lineColorChannel = 0;
  int contentColorChannel = 0;
  List<int>? lineColor = [255, 0, 0, 0];
  List<int>? contentColor = [255, 0, 0, 0];
  int? lineMode;

  clearFocused() {
    cells.forEach((element) {
      element.focused = false;
    });
    combineCells.forEach((element) {
      element.focused = false;
    });
  }

  /// 返回当前选中的单元格和合并的单元格
  List<TableCellElement> getFocusedCells() {
    List<TableCellElement> focusedCells =
        cells.where((element) => element.focused == true).toList();
    focusedCells.addAll(
        combineCells.where((element) => element.focused == true).toList());
    return focusedCells;
  }

  /// 获取cell在表格中的偏移
  Offset getCellOffset(int columnIndex, int rowIndex) {
    double dx = 0.0;
    double dy = 0.0;
    cells.forEach((element) {
      if (element.columnIndex < columnIndex && element.rowIndex == rowIndex)
        dx += element.width;
      if (element.rowIndex < rowIndex && element.columnIndex == columnIndex)
        dy += element.height;
    });
    _logger.log("cell的偏移毫米数---》 dx: $dx, dy: $dy");
    return Offset(dx.mm2dp().toDouble(), dy.mm2dp().toDouble());
  }

  /// 判断 > 1 个单元格则可以合并
  bool mergeCellsEnable() {
    List<TableCellElement> focusCells = getFocusedCells();
    Set<String> mergeSet = focusCells.map((e) => e.id).toSet();
    bool mergeCellsEnable = mergeSet.length > 1;
    return mergeCellsEnable;
  }

  /// 返回当前选中的单元格, 合并的单元格若选中, 则查找其包含的子单元格
  List<TableCellElement> _getFocusedAtomicCells() {
    List<TableCellElement> focusedAtomicCells =
        cells.where((element) => element.focused == true).toList();
    combineCells.forEach((combineElement) {
      if (combineElement.focused == true) {
        focusedAtomicCells.addAll(
            cells.where((element) => element.combineId == combineElement.id));
      }
    });
    return focusedAtomicCells;
  }

  ///查找选中单元格的右下角位置
  CellPosition findFocusedCellsBottomRight() {
    List<TableCellElement> focusAtomicCells = _getFocusedAtomicCells();

    /// 查找选中单元格的右下坐标
    CellPosition bottomRight = CellPosition(0, 0);
    focusAtomicCells.forEach((element) {
      if (element.columnIndex > bottomRight.columnIndex) {
        bottomRight.columnIndex = element.columnIndex;
      }
      if (element.rowIndex > bottomRight.rowIndex) {
        bottomRight.rowIndex = element.rowIndex;
      }
    });
    return bottomRight;
  }

  ///查找选中单元格的左上角位置
  CellPosition findFocusedCellsTopLeft() {
    List<TableCellElement> focusAtomicCells = _getFocusedAtomicCells();

    /// 查找选中单元格的左上坐标
    CellPosition topLeft = CellPosition(this.column - 1, this.row - 1);
    focusAtomicCells.forEach((element) {
      if (element.columnIndex < topLeft.columnIndex) {
        topLeft.columnIndex = element.columnIndex;
      }
      if (element.rowIndex < topLeft.rowIndex) {
        topLeft.rowIndex = element.rowIndex;
      }
    });
    return topLeft;
  }

  /// 合并单元格
  TableCellElement? mergeFocusedCells(
      Function(String, String) cloneSyncExcelTaskFun) {
    List<TableCellElement> focusAtomicCells = _getFocusedAtomicCells();

    /// 查找选中单元格的左上、右下坐标
    CellPosition topLeft = CellPosition(this.column - 1, this.row - 1);
    CellPosition bottomRight = CellPosition(0, 0);
    focusAtomicCells.forEach((element) {
      if (element.columnIndex < topLeft.columnIndex) {
        topLeft.columnIndex = element.columnIndex;
      }
      if (element.columnIndex > bottomRight.columnIndex) {
        bottomRight.columnIndex = element.columnIndex;
      }

      if (element.rowIndex < topLeft.rowIndex) {
        topLeft.rowIndex = element.rowIndex;
      }
      if (element.rowIndex > bottomRight.rowIndex) {
        bottomRight.rowIndex = element.rowIndex;
      }
    });

    /// 将矩形中的所有单元格子合并，合并内容为左上第一个单元格的内容
    String combineId = JsonElement.generateId();
    var firstCell = this.cells.singleWhereOrNull((element) =>
        element.columnIndex == topLeft.columnIndex &&
        element.rowIndex == topLeft.rowIndex);
    if ((firstCell?.combineId.length ?? 0) > 0) {
      /// 第一个单元格为合并单元格，则取合并单元格的内容
      firstCell = this
          .combineCells
          .singleWhereOrNull((element) => element.id == firstCell?.combineId);
    }

    if (firstCell != null) {
      TableCellElement combineCell =
          firstCell.clone(isTableCell: true) as TableCellElement;
      combineCell.id = combineId;
      combineCell.focused = true;
      combineCell.firstCellOfCombine = firstCell;
      if (firstCell.isBindingExcel()) {
        cloneSyncExcelTaskFun.call(firstCell.id, combineCell.id);
      }

      this.combineCells.add(combineCell);

      /// 取左上、右下矩形中的所有 cell 赋值 combineId
      this
          .cells
          .where((element) => (element.columnIndex >= topLeft.columnIndex &&
              element.columnIndex <= bottomRight.columnIndex &&
              element.rowIndex >= topLeft.rowIndex &&
              element.rowIndex <= bottomRight.rowIndex))
          .forEach((element) {
        if (element.combineId.length > 0) {
          /// 清理已存在的合并单元格
          this.combineCells.removeWhere(
              (combineElement) => combineElement.id == element.combineId);
        }
        element.combineId = combineId;
        element.focused = false;
      });

      return combineCell;
    }
    return null;
  }

  /// 合并单元格清除关联的Excel文件（合并的单元格中，如果有单元格不是左上角第一个单元格并且关联了Excel)
  mergeFocusedCellsClearImportExcel() {
    List<TableCellElement> focusAtomicCells = _getFocusedAtomicCells();

    /// 查找选中单元格的左上、右下坐标
    CellPosition topLeft = CellPosition(this.column - 1, this.row - 1);
    CellPosition bottomRight = CellPosition(0, 0);
    focusAtomicCells.forEach((element) {
      if (element.columnIndex < topLeft.columnIndex) {
        topLeft.columnIndex = element.columnIndex;
      }
      if (element.columnIndex > bottomRight.columnIndex) {
        bottomRight.columnIndex = element.columnIndex;
      }

      if (element.rowIndex < topLeft.rowIndex) {
        topLeft.rowIndex = element.rowIndex;
      }
      if (element.rowIndex > bottomRight.rowIndex) {
        bottomRight.rowIndex = element.rowIndex;
      }
    });

    focusAtomicCells
        .where((cell) =>
            cell.columnIndex != topLeft.columnIndex ||
            cell.rowIndex != topLeft.rowIndex)
        .forEach((cell) {
      if (cell.isBindingElement()) {
        // cell.isBinding = 0;
        cell.bindingColumn = -1;
        // cell.isTitle = false;
        // cell.contentTitle = null;
        cell.dataBind = null;
        cell.value = "";
        TemplateUtils.changeElementModify(
            cell, ElementModifyType.modifyUseTitle,
            displayHeader: false);
      }
    });
  }

  bool clearContentEnable() {
    List<TableCellElement> focusCells = getFocusedCells();
    return focusCells.length > 0 &&
        focusCells.where((element) {
              if (element.isCombine()) {
                // return element.value.isNotEmpty || element.firstCellOfCombine.value.isNotEmpty || element.firstCellOfCombine.isBinding == 1;
                return (element.value?.isNotEmpty ?? false) ||
                    (element.firstCellOfCombine?.value?.isNotEmpty ?? false) ||
                    (element.firstCellOfCombine?.isBindingElement() ?? false);
              } else {
                // return element.value.isNotEmpty || element.isBinding == 1;
                return (element.value?.isNotEmpty ?? false) ||
                    element.isBindingElement();
              }
            }).length >
            0;
  }

  bool splitCellsEnable() {
    List<TableCellElement> focusCells = getFocusedCells();

    bool splitCellsEnable =
        focusCells.length == 1 && focusCells.first.isCombine();
    return splitCellsEnable;
  }

  /// 是否可以删除行/列
  int deleteColumnEnable() {
    int result = -1;
    if (column == 1) {
      return result;
    }
    CellPosition bottomRight = findFocusedCellsBottomRight();
    CellPosition topLeft = findFocusedCellsTopLeft();
    _logger.log(
        "====bottomRight.rowIndex: ${bottomRight.rowIndex} ,topLeft.rowIndex: ${topLeft.rowIndex}");
    _logger.log(
        "====bottomRight.columnIndex: ${bottomRight.columnIndex} ,topLeft.columnIndex: ${topLeft.columnIndex}");
    _logger.log("====row: ${row} ,column: ${column}");

    ///如果删除行数覆盖整个表格，则不处理
    if (bottomRight.columnIndex - topLeft.columnIndex + 1 == column) {
      return result;
    }
    result = bottomRight.columnIndex - topLeft.columnIndex + 1;
    _logger.log("====deleteItemEnable: true");
    return result;
  }

  int deleteRowEnable() {
    int result = -1;
    if (row == 1) {
      return result;
    }
    CellPosition bottomRight = findFocusedCellsBottomRight();
    CellPosition topLeft = findFocusedCellsTopLeft();

    ///如果删除行数覆盖整个表格，则不处理
    if (bottomRight.rowIndex - topLeft.rowIndex + 1 == row) {
      return result;
    }
    result = bottomRight.rowIndex - topLeft.rowIndex + 1;
    return result;
  }

  /// 删除合并单元格、重置被合并单元格的 combineId
  void splitFocusedCell() {
    List<TableCellElement> focusCells = getFocusedCells();

    String combineId = focusCells.first.id;
    this.cells.forEach((element) {
      if (element.combineId == combineId) {
        element.combineId = '';
      }
    });

    focusCells.first.firstCellOfCombine?.focused = true;
    this.combineCells.removeWhere((element) => focusCells.first == element);
  }

  ///获取表格最右下角单元格，用于添加行列时属性继承
  TableCellElement getBottomRightCell() {
    TableCellElement cell = cells.firstWhere((element) =>
        element.rowIndex == row - 1 && element.columnIndex == column - 1);
    return cell;
  }

  TableElement({
    required super.id,
    required super.x,
    required super.y,
    required super.width,
    required super.height,
    super.zIndex,
    super.value,
    super.rotate,
    super.isLock,
    super.mirrorId,
    super.isOpenMirror,
    super.mirrorType,
    this.lineType = 1,
    required this.lineWidth,
    required this.row,
    required this.column,
    required this.rowHeight,
    required this.columnWidth,
    required this.cells,
    required this.combineCells,
    this.allowFreeZoom = false,
    this.lineColorChannel = 0,
    this.contentColor = const [255, 0, 0, 0],
    this.lineColor = const [255, 0, 0, 0],
    this.contentColorChannel = 0,
    this.lineMode = 4,
  }) : super(type: ElementItemType.table);

  factory TableElement.fromJson(Map<String, dynamic> json) {
    List<double> parseStringToListDouble(String fieldParam){
      List<double> result = [];
      try {
        result = (jsonDecode(fieldParam ?? '[]') as List)
            .map<double>((e) => (e as num).toDouble())
            .toList();
      } catch (e) {
      }
      return result;
    }
    final table = TableElement(
      id: JsonElement.elementIdFromJson(json),
      x: json['x'],
      y: json['y'],
      width: json['width'],
      height: json['height'],
      zIndex: _parseStringToInt(json['zIndex']),
      rotate: json['rotate'],
      isLock: json['isLock'] ?? 0,
      mirrorId: json['mirrorId'] ?? "",
      isOpenMirror: json['isOpenMirror'] ?? 0,
      mirrorType: json['mirrorType'] ?? 0,
      lineType: json['lineType'],
      lineWidth: (json['lineWidth'] is int)
          ? json['lineWidth'].toDouble()
          : json['lineWidth'],
      row: json['row'],
      column: json['column'],
      rowHeight: json['rowHeight'] is String
          ? parseStringToListDouble(json['rowHeight'])
          : json['rowHeight']
          ?.map((e) {
            if (e is int) {
              return e.toDouble();
            } else {
              return e;
            }
          })
          ?.toList()
          ?.cast<double>(),
      columnWidth:json['columnWidth'] is String
          ? parseStringToListDouble(json['columnWidth'])
          : json['columnWidth']
          ?.map((e) {
            if (e is int) {
              return e.toDouble();
            } else {
              return e;
            }
          })
          ?.toList()
          ?.cast<double>(),
      lineColorChannel: json['lineColorChannel'] ?? 0,
      contentColorChannel: json['contentColorChannel'] ?? 0,
      lineColor: json['lineColor'] == null
          ? [255, 0, 0, 0]
          : List<int>.from(json['lineColor']),
      contentColor: json['contentColor'] == null
          ? [255, 0, 0, 0]
          : List<int>.from(json['contentColor']),
      cells: [],
      combineCells: [],
      allowFreeZoom: json['allowFreeZoom'] ?? false,
      lineMode: json['lineMode'],
    );
    // table.cells = json['cells']
    //     .map<TableCellElement>((e) => TableCellElement.fromJson(e)..tableElement = table).toList();
    // table.combineCells = json['combineCells']
    //     .map<TableCellElement>((e) => TableCellElement.fromJson(e)..tableElement = table).toList();
    table.cells = <TableCellElement>[];
    if (json['cells'] != null) {
      if (json['cells'] is String) {
        jsonDecode(json['cells']).forEach((v) {
          table.cells.add(TableCellElement.fromJson(v)..tableElement = table);
        });
      } else {
        json['cells'].forEach((v) {
          table.cells.add(TableCellElement.fromJson(v)..tableElement = table);
        });
      }
    }

    table.combineCells = <TableCellElement>[];
    if (json['combineCells'] != null) {
      if (json['combineCells'] is String) {
        jsonDecode(json['combineCells']).forEach((v) {
          table.combineCells.add(TableCellElement.fromJson(v)..tableElement = table);
        });
      } else {
        json['combineCells'].forEach((v) {
          table.combineCells.add(TableCellElement.fromJson(v)..tableElement = table);
        });
      }
    }
    return table;
  }


  @override
  Map<String, dynamic> toJson() {
    /// 根据单元格宽高校正 table 宽高
    _correctionTableFrame();

    final Map<String, dynamic> data = new Map<String, dynamic>();
    data.addAll(super.toJson());

    data['lineType'] = this.lineType ?? 1;
    data['lineWidth'] = this.lineWidth.digits(2);
    data['row'] = this.row;
    data['column'] = this.column;
    data['rowHeight'] = this.rowHeight.map((e) => e.digits(6)).toList();
    data['columnWidth'] = this.columnWidth.map((e) => e.digits(6)).toList();
    data['cells'] = this.cells.map((v) {
      v.escapeValue = this.escapeValue;
      v.escapeBindingMethod = this.escapeBindingMethod;
      return v.toJson();
    }).toList();
      data['combineCells'] = this.combineCells.map((v) {
      v.escapeValue = this.escapeValue;
      v.escapeBindingMethod = this.escapeBindingMethod;
      return v.toJson();
    }).toList();
      data['allowFreeZoom'] = this.allowFreeZoom;
    data['lineColor'] = this.lineColor ?? [255, 0, 0, 0];
    data['contentColor'] = this.contentColor ?? [255, 0, 0, 0];
    data['lineColorChannel'] = this.lineColorChannel;
    data['contentColorChannel'] = this.contentColorChannel;
    return data;
  }

 static int _parseStringToInt(dynamic field) {
   return field == null ? 0 : ((field is String) ? int.parse(field) : field);
  }

  @override
  Map<String, dynamic> antiEscapeValueToJson() {
    Map<String, dynamic> data = toJson();
    data['cells'] = this.cells.map((e) => e.antiEscapeValueToJson()).toList();
      data['combineCells'] =
        this.combineCells.map((e) => e.antiEscapeValueToJson()).toList();
      return data;
  }

  _correctionTableFrame() {
    double height = 0.0;
    this.rowHeight.forEach((element) {
      height += element;
    });

    height += (this.rowHeight.length + 1) * lineWidth;

    double width = 0.0;
    this.columnWidth.forEach((element) {
      width += element;
    });
    width += (this.columnWidth.length + 1) * this.lineWidth;

    this.height = height.digits(6);
    this.width = width.digits(6);
  }

  resetCellsImageCache() {
    this.cells.forEach((element) {
      element.resetImageCache();
    });
    this.combineCells.forEach((element) {
      element.resetImageCache();
    });
  }

  @override
  bool hasVipSource() {
    ///只要有一个单元格有vip字体就返回true
      for (var cell in cells) {
        if (cell.combineId.isEmpty) {
          if (cell.hasVipSource()) {
            return true;
          }
        }
      }

      for (var cell in combineCells) {
        if (cell.hasVipSource()) {
          return true;
        }
      }
      return false;
  }


  bool hasFocusedCellVipSource() {
    /// 如果有选中单元格的话，则判定选中单元格中是否有vip字体;无选中的话则遍历cell,只要有一个单元格有vip字体就返回true
    if (getFocusedCells().length > 0) {
      for (var cell in getFocusedCells()) {
        if (cell.hasVipSource()) {
          return true;
        }
      }
      return false;
    } else {
      for (var cell in cells) {
        if (cell.combineId.isEmpty) {
          if (cell.hasVipSource()) {
            return true;
          }
        }
      }

      for (var cell in combineCells) {
        if (cell.hasVipSource()) {
          return true;
        }
      }
      return false;
    }
  }

  @override
  double getItemDisplayMinWidth(BuildContext context) {
    ///所有单元格最低列宽+线宽+误差值
    return (column + (column + 1) * lineWidth).mm2dp().toDouble();
  }

  @override
  double getItemDisplayMinHeight() {
    return (row + (row + 1) * lineWidth).mm2dp().toDouble();
  }

  @override
  double getItemDisplayMaxWidth() {
    return (column * TableCellElement.cellWidthMax + (column + 1) * lineWidth).mm2dp().toDouble();
  }

  @override
  double getItemDisplayMaxHeight() {
    return (row * TableCellElement.cellHeightMax + (row + 1) * lineWidth).mm2dp().toDouble();
  }

  @override
  bool isBindingExcel() {
    return getBindingExcelCells().isNotEmpty ||
        getBindingExcelCombineCells().isNotEmpty;
  }
  @override
  bool isBindingCommodity(){
    return getBindingCommodidyCells().isNotEmpty || getBindingCommodidyCombineCells().isNotEmpty;
  }

  List<TableCellElement> getBingCommodityCells() {
    return cells.where((element) => element.isBindingCommodity()).toList();
  }

   List<TableCellElement> getBingCommodityCombineCells() {
    return combineCells.where((element) => element.isBindingCommodity()).toList();
  }

  List<TableCellElement> getBindingExcelCells() {
    return cells.where((element) => element.isBindingExcel()).where((element) => element.combineId==null || element.combineId.isEmpty).toList();
  }

  List<TableCellElement> getBindingExcelCombineCells() {
    return combineCells.where((element) => element.isBindingExcel()).toList();
  }

  List<TableCellElement> getBindingCommodidyCells() {
    return cells.where((element) => element.isBindingCommodity()).toList();
  }

  List<TableCellElement> getBindingCommodidyCombineCells() {
    return combineCells.where((element) => element.isBindingCommodity()).toList();
  }

  @override
  bool isExcelModifyIdMatch(String modifyId) {
    return getBindingExcelCells().any((cell) => cell.id == modifyId) ||
        getBindingExcelCombineCells().any((cell) => cell.id == modifyId);
  }
}
