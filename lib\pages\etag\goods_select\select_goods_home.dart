import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_config_interface.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:get/get.dart';
import 'package:niim_login/login_plugin/widget/jc_custom_toast_util.dart';
//lib/src/utils/canvas_event_bus.dart
//import 'package:niimbot_flutter_canvas/scr/utils/canvas_event_bus.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/widgets/assist/scan/scan_camera_widget.dart';
import 'package:niimbot_flutter_canvas/src/widgets/components/custom_dialog.dart';
import 'package:niimbot_flutter_canvas/src/widgets/excel/import_excel_helper.dart';
import 'package:text/application.dart';
import 'package:text/pages/canvas/impl/goods_import_impl.dart';
import 'package:text/pages/etag/goods_select/add_good_page.dart';
import 'package:text/pages/etag/goods_select/checked_goods_page.dart';
import 'package:text/pages/etag/goods_select/controller/goods_lib_logic.dart';
import 'package:text/pages/etag/goods_select/controller/goods_lib_state.dart';
import 'package:text/pages/etag/goods_select/good_list_widget.dart';
import 'package:text/pages/etag/goods_select/model/good_category.dart';
import 'package:text/pages/etag/goods_select/model/good_model.dart';
import 'package:text/pages/etag/home/<USER>/tag_base_logic.dart';
import 'package:text/tools/to_Native_Method_Channel.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/plane_button.dart';
import 'package:text/utils/screen_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';
import 'package:text/widget/CustomTabIndicator.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

import 'custom_popup_menu_item.dart';
import 'goods_search_widget.dart';
//List<Map<String, dynamic>> list
typedef SelectedResultCallBack = void Function(List<Map<String, dynamic>> value);
///选择商品主页
class SelectGoodsHome extends StatefulWidget {
  List? lastSelectGoods;
  int? busynessType;
  Map<String, dynamic>? templateData;
  SelectedResultCallBack? resultCallBack;
  bool isNeedPrint = false;
  SelectGoodsHome({Key? key, this.busynessType, this.lastSelectGoods, this.templateData,this.resultCallBack,this.isNeedPrint = false}) : super(key: key);

  @override
  _SelectGoodsHomeState createState() => _SelectGoodsHomeState();
}

class _SelectGoodsHomeState extends State<SelectGoodsHome> with TickerProviderStateMixin {
  late GoodsLibLogic logic;
  late GoodsLibState state;
  late TabController _tabController;
  List<GoodCategory> goodCategories = [];
  List <GoodModel> modifyGoods = [];

  @override
  void initState() {
    super.initState();
    ToNativeMethodChannel().sendTrackingToNative({"track":"view","posCode":"108_341_317","ext":getExtData()});
    logic = Get.put(GoodsLibLogic(busynessType: widget.busynessType));
    state = logic.state;
    if (widget.busynessType == 1) {
      state.checkedGoodsList.addAll((widget.lastSelectGoods == null || widget.lastSelectGoods!.length == 0) ? [] : (widget.lastSelectGoods as List<GoodModel>));
    }
    _tabController = TabController(initialIndex: 0, length: goodCategories.length, vsync: this);
    _tabController.addListener(_handleTabChange);

    logic.getGoodsCategory().then((value) {
      _tabController = TabController(initialIndex: 0, length: value.length, vsync: this);
      _tabController.addListener(_handleTabChange);
      setState(() {
        goodCategories = value;
      });
    });
    CanvasEventBus.getDefault().register(this, (data) {
       if(data == "closeSelectGoodsPage"){
         if(preventQuickTap(interval: 1000)){
           _closePage();
         }
          return;
        }
       if(data is Map && data.containsKey("refreshGoodsData")){
          if(logic.state.isSearch){
            logic.getGoodList(1, isSearch: true, userCategoryId: -1);
          }else{
            _refreshTabData();
          }
          refreshSelectedGoods(data['refreshGoodsData']);
        }
        if(data is Map && data.containsKey("refreshCategoryData")){
          if(logic.state.isSearch){
            logic.getGoodList(1, isSearch: true, userCategoryId: -1);
          }else{
            _refreshCategoryData();
          }
        }
    });
  }

  Map<String,dynamic> getExtData(){
    CanvasItemConfigInterface? canvasConfigIml = CanvasPluginManager().canvasConfigImpl;
    bool isEtag = canvasConfigIml?.currentConfigMode() == CanvasCurrentConfigMode.etag;
    return {"refer":isEtag ? "1" : widget.isNeedPrint == true ? "3" : "2"};
  }

  static DateTime? _lastPopTime;

  preventQuickTap({int? interval}) {
    DateTime _nowTime = DateTime.now();
    if (_lastPopTime == null ||
        _nowTime.difference(_lastPopTime!) >
            Duration(milliseconds: interval ?? 300)) {
      _lastPopTime = _nowTime;
      return true;
    } else {
      _lastPopTime = _nowTime;
      return false;
    }
  }

  void refreshSelectedGoods(Map<String,dynamic> goodInfo){
   bool isNeedAdd = true;
   GoodModel modifyGood =  GoodModel.fromJson(goodInfo);
   for (var i = 0; i < modifyGoods.length; i++) {
     if(modifyGoods[i].id == modifyGood.id){
       modifyGoods[i] = modifyGood;
       isNeedAdd = false;
       break;
     }
   }
   if(isNeedAdd){
     modifyGoods.add(modifyGood);
   }
  }

  void mergeNewGoodsData(List<GoodModel> goods){
  for (var i = 0; i < goods.length; i++) {
    for (var j = 0; j < modifyGoods.length; j++){
      if(goods[i].id == modifyGoods[j].id){
         goods[i] = modifyGoods[j];
       }
    }
  }
  }

  //关闭页面
  void _closePage(){
    if(mounted && logic.hasPop == false){
      logic.hasPop = true;
      Navigator.pop(context);
    }
  }

  // 处理索引变化事件
  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      logic.changeCategory(goodCategories[_tabController.index].id!);
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    CanvasEventBus.getDefault().unregister(this);
    GoodsImportImpl.goodCategories = goodCategories;
    GoodsImportImpl.goodCategories.removeWhere((element) => element.id == 0 || element.id == -1);
    super.dispose();
    Get.delete<GoodsLibLogic>();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      //padding: EdgeInsets.only(bottom: ScreenUtil().viewInsets.bottom),
      decoration: const BoxDecoration(
        color: ThemeColor.background,
        borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0)),
      ),
      height: ScreenUtil().size.height - 60,
      child: GetBuilder<GoodsLibLogic>(
          id: GoodsLibLogic.refreshSearchMode,
          builder: (logic) {
            return Column(
              mainAxisSize: MainAxisSize.max,
              // alignment: Alignment.topLeft,
              children: [
                logic.state.isSearch
                    ? Expanded(
                        child: GoodsSearchWidget(templateData: widget.templateData,extData: getExtData()),
                      )
                    : Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              height: 48,
                              //  padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 0),
                              decoration: const BoxDecoration(
                                color: ThemeColor.background,
                                borderRadius:
                                    BorderRadius.only(topLeft: Radius.circular(12), topRight: Radius.circular(12)),
                              ),
                              child: Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsetsDirectional.only(start: 16),//EdgeInsets.only(left: 18),
                                    child: PlaneButton(
                                      child: Text(
                                        intlanguage('app100000692', '取消'),
                                        style: const TextStyle(
                                            color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
                                      ),
                                      onTap: () {
                                        ToNativeMethodChannel().sendTrackingToNative({"track":"click","posCode":"108_341_318","ext":getExtData()});
                                        Navigator.of(context).pop();
                                      }
                                    ),
                                  ),
                                  const Spacer(),
                                  ConstrainedBox(
                                    constraints: BoxConstraints(maxWidth: ScreenUtil().width - 180),
                                    child: Text(
                                      widget.isNeedPrint ? intlanguage('app100001667', '选择商品并打印') :
                                      widget.busynessType == 1
                                          ? intlanguage('app100001134', '选择商品')
                                          : intlanguage('app100000690', '选择商品'),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                          color: ThemeColor.mainTitle, fontSize: 17, fontWeight: FontWeight.w600),
                                    ),
                                  ),
                                  const Spacer(),
                                  buildPopupMenuButton(),
                                ],
                              ),
                            ),
                            _buildSearchWidget(),
                            const ItemDivider(),
                            Container(
                              height: 45,
                              padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
                              width: double.infinity,
                              //height: 55,
                              color: Colors.white,
                              child: TabBar(
                                padding: const EdgeInsets.only(top: 0, bottom: 0),
                                controller: _tabController,
                                tabs: goodCategories.map((e) => Tab(child: Text(e.name ?? ""))).toList(),
                                indicatorSize: TabBarIndicatorSize.label,
                                indicatorPadding: EdgeInsets.only(bottom: 2),
                                indicator: CustomTabIndicator(
                                    width: 24, borderSide: BorderSide(width: 2.0, color: ThemeColor.brand)),
                                labelPadding: const EdgeInsets.only(left: 16, right: 16),
                                labelColor: ThemeColor.title,
                                labelStyle:
                                    const TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: ThemeColor.title),
                                unselectedLabelColor: ThemeColor.COLOR_595959,
                                unselectedLabelStyle: const TextStyle(
                                    fontSize: 15, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_595959),
                                isScrollable: true,
                              ),
                            ),
                            Expanded(
                                child: TabBarView(
                                    physics: NeverScrollableScrollPhysics(),
                                    controller: _tabController,
                                    children: goodCategories.map((e) => _buildTabBarPanel(e)).toList())),
                          ],
                        ),
                      ),
                _buildBottomBar(),
              ],
            );
          }),
    );
  }

  Widget _buildTabBarPanel(GoodCategory tab) {
    return GoodListWidget(tab.id!,templateData: widget.templateData,extData: getExtData());
  }

  Widget _buildBottomBar() {
    double bottomPadding = Platform.isAndroid ? ScreenUtil().viewInsets.bottom : 24;
    return Align(
      alignment: Alignment.bottomCenter,
      child: GetBuilder<GoodsLibLogic>(builder: (logic) {
        return logic.state.isOpenKeyboard
            ? SizedBox.shrink()
            : Container(
              decoration: BoxDecoration(
          color: Colors.white, // 底色
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.03), // Shadow color (with opacity)
              offset: Offset(0, -6), // Vertical offset (upwards)
              blurRadius:5, // Blur radius
              spreadRadius: 0, // Spr
            ),
          ],
        ),
                padding: EdgeInsetsDirectional.only(end:16,bottom: bottomPadding),
                height: (80-24)+ bottomPadding,
                alignment: Alignment.centerLeft,
                //color: Colors.white,
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        logic.changeGoodSelectAll(!logic.getSelectAllState());
                      },
                      child: Container(
                        color: ThemeColor.background,
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.only(left: 16, right: 6),
                              child: logic.getSelectAllState()
                                  ? const SvgIcon(
                                      'assets/images/goods_lib/icon_good_check.svg',
                                      width: 20,
                                      height: 20,
                                    )
                                  : const SvgIcon(
                                      'assets/images/goods_lib/icon_good_nocheck.svg',
                                      width: 20,
                                      height: 20,
                                    ),
                            ),
                            Container(
                              constraints: BoxConstraints(
                                maxWidth: 60,
                              ),
                              child: Text(intlanguage('app00506', '全选'),
                                  maxLines: 3,
                                  style:
                                      TextStyle(fontSize: 13, fontWeight: FontWeight.w600, color: ThemeColor.subtitle)),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 13,
                    ),
                    GestureDetector(
                        onTap: () {
                          if (state.checkedGoodsList.length <= 0) {
                            return;
                          }
                          showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            shape: const RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
                            builder: (BuildContext context) {
                              return CheckedGoodsPage(
                                goods: state.checkedGoodsList.toList(),
                              );
                            },
                          );
                        },
                        child: Container(
                          // width: 90,
                          child: Row(
                            children: [
                              Container(
                                constraints: BoxConstraints(
                                  maxWidth: 85,
                                ),
                                child: Text(
                                    intlanguage('app100001052', '已选\$条',
                                        param: [state.checkedGoodsList.length.toString()]),
                                    maxLines: 4,
                                    style: const TextStyle(
                                        fontSize: 13, fontWeight: FontWeight.w600, color: ThemeColor.brand)),
                              ),
                              state.checkedGoodsList.length <= 0
                                  ? Container()
                                  : SvgIcon(
                                      'assets/images/goods_lib/icon_arrow_up.svg',
                                      width: 18,
                                      height: 18,
                                    ),
                            ],
                          ),
                        )
                        ),
                    const Spacer(),
                    Container(
                      margin: EdgeInsets.only(left: 10),
                      child: PlaneButton(
                        width: 84,
                        height: 36,
                        backgroundColor: ThemeColor.brand,
                        borderRadius: const BorderRadius.all(Radius.circular(18)),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 3),
                          child: Text(
                            widget.isNeedPrint == true ? intlanguage('app01031', '完成') : widget.resultCallBack != null ? intlanguage("app00761", "下一步") : intlanguage('app00048', '确定'),
                            textAlign: TextAlign.center,
                            style: const TextStyle(color: Colors.white, fontSize: 15, fontWeight: FontWeight.w600),
                            maxLines: 2,
                          ),
                        ),
                        onTap: () {
                          //下一步不需要埋点
                          if(widget.isNeedPrint == true || widget.resultCallBack == null){
                             ToNativeMethodChannel().sendTrackingToNative({"track":"click","posCode":"108_341_319","ext":getExtData()});
                          }
                          List<Map<String, dynamic>> list = [];
                          if (widget.busynessType == 0 || widget.busynessType == null) {
                            if(Get.isRegistered<TagBaseLogic>()){
                               Get.find<TagBaseLogic>().state.GoodsPageCheckedList.clear();
                               Get.find<TagBaseLogic>().state.GoodsPageCheckedList.addAll(state.checkedGoodsList);
                            }
                          }
                          List<GoodModel> goods = state.checkedGoodsList.toList();
                          mergeNewGoodsData(goods);
                          if(goods.length == 0){
                            showToast(msg: intlanguage("app100001652", "商品不能为空"));
                            return;
                          }
                          for (var i = 0; i < goods.length; i++) {
                            list.add(goods[i].toJson());
                          }
                          if(widget.resultCallBack != null){
                            widget.resultCallBack!(list);
                          }else{
                            Navigator.pop(context, list);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              );
      }),
    );
  }

  ///顶部搜索框
  _buildSearchWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
      child: Container(
        height: 32,
        decoration: BoxDecoration(
          color: ThemeColor.listBackground,
          borderRadius: BorderRadius.circular(16),
        ),
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            logic.changeMode(true);
          },
          child: Row(
            children: [
              const SizedBox(
                width: 12,
              ),
              const SvgIcon(
                'assets/images/industry_template/replace_label/search_icon.svg',
                matchTextDirection: true,
              ),
              Expanded(
                  child: Container(
                      padding: const EdgeInsetsDirectional.only(start: 8),
                      child: Text(
                        intlanguage('app100001047', '搜索商品条码或名称'),
                        style: TextStyle(
                          color: ThemeColor.subtitle,
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ))),
              // const SizedBox(
              //   width: 40,
              // ),
            ],
          ),
        ),
      ),
    );
  }

  ///新增下拉菜单
  PopupMenuButton<int> buildPopupMenuButton() {
    return PopupMenuButton<int>(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 4.0,
      offset: const Offset(0, 10),
      padding: const EdgeInsets.all(0),
      itemBuilder: (BuildContext context) {
        return <PopupMenuEntry<int>>[
          CustomPopupMenuItem<int>(
            icon: 'assets/images/goods_lib/icon_scan.svg',
            text: intlanguage('app100001053', '扫描新增'),
            value: 0,
          ),
          const PopupMenuItem<int>(
            enabled: false,
            height: 0.5, // 控制分割线高度
            child: Divider(color: ThemeColor.divider),
          ),
          CustomPopupMenuItem<int>(
            icon: 'assets/images/goods_lib/icon_excel.svg',
            text: intlanguage('app100001054', '从Excel导入'),
            value: 1,
          ),
          const PopupMenuItem<int>(
            enabled: false,
            height: 0.5, // 控制分割线高度
            child: Divider(color: ThemeColor.divider),
          ),
          CustomPopupMenuItem<int>(
            icon: 'assets/images/goods_lib/icon_handwrite.svg',
            text: intlanguage('app100001055', '手动录入'),
            value: 2,
          ),
        ];
      },
      position: PopupMenuPosition.under,
      child: Container(
        alignment: AlignmentDirectional.centerEnd,
        color: Colors.transparent,
        width: 70,
        padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 16, 0),
        child: Text(
          maxLines: 2,
          intlanguage('app100000691', '新增'),
          style: const TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
      onOpened: () {
        String id = "";
        String cloudTemplateId = "";
        int refer = 1;
        if(widget.isNeedPrint == true){
          refer = 3;
        }else if(widget.templateData != null && widget.templateData!.isNotEmpty){
          refer = 2;
          id = widget.templateData!['id'] == null ? "" : widget.templateData!['id'].toString();
          cloudTemplateId = widget.templateData!['cloudTemplateId'] ?? "";
        }

        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "110_217",
          "ext": {"refer": refer, "temp_id": id, "industry_temp_id": cloudTemplateId}
        });
      },
      onSelected: (int value) {
        var bName = "";
        switch (value) {
          case 0:
            bName = intlanguage('app100001053', '扫描新增');
            break;
          case 1:
            bName = intlanguage('app100001054', '从Excel导入');
            break;
          case 2:
            bName = intlanguage('app100001055', '手动录入');
            break;
          default:
        }
        String id = "";
        String cloudTemplateId = "";
        int refer = 1;
        if(widget.isNeedPrint == true){
          refer = 3;
        }else if(widget.templateData != null && widget.templateData!.isNotEmpty){
          refer = 2;
          id = widget.templateData!['id'] == null ? "" : widget.templateData!['id'].toString();
          cloudTemplateId = widget.templateData!['cloudTemplateId'] ?? "";
        }
        ToNativeMethodChannel().sendTrackingToNative({
          "track": "click",
          "posCode": "110_218",
          "ext": {"b_name": bName, "refer": refer, "temp_id": id, "industry_temp_id": cloudTemplateId}
        });
        if (value == 0) {
          _handleScanClicked(context).then((value) {
            _processScanResult(value);
          });
        } else if (value == 1) {
          ImportExcelHelper.showGoodsLibExcelListPage(context, (importResult) {
            if (importResult != null && importResult is String) {
              if (importResult.isNotEmpty) {
                showToast(msg: importResult);
              }
              //导入成功，则刷新列表
              _refreshTabData();
            }
          });
        } else if (value == 2) {
          if(TemplateData.goodsInfnFieldDescName().isEmpty){
            showToast(msg: intlanguage("app100000345", "系统异常"));
            return;
          }
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            isDismissible: false,
            enableDrag: false,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            builder: (BuildContext context) {
              return AddGoodPage();
            },
          ).then((value) {
            _refreshTabData();
          });
        }
      },
    );
  }

  _processScanResult(Map<String, dynamic> value) {
    if (value.isNotEmpty) {
      String result = value['result'];
      String elementType = value['elementType'];
      if (elementType == 'barcode' && result.isNotEmpty) {
        ///根据条码获取商品信息，然后跳转至添加商品页面
        JCCustomToastUtil().showProgress(context);
        logic.getGoodInfoByBarcode(result).then((good) {
          JCCustomToastUtil().closeProgress(context);
          good.barcode = result;
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topLeft: Radius.circular(12.0), topRight: Radius.circular(12.0))),
            builder: (BuildContext context) {
              return AddGoodPage(
                good: good.toJson(),
                fromScan: true,
              );
            },
          ).then((value) {
            if (value != null && value is bool && value) {
              _handleScanClicked(context).then((result) {
                _processScanResult(result);
                _refreshTabData();
              });
            } else {
              _refreshTabData();
            }
          });
        }).catchError((error){
          JCCustomToastUtil().closeProgress(context);
          if(Application.networkConnected == false) {
             showToast(msg: intlanguage("app100000354", "网络异常"));
           }else{
             showToast(msg: intlanguage('app100000803', '识别失败, 请重试'));
           }
        });
      }
    }
  }

  Future<Map<String, dynamic>> _handleScanClicked(BuildContext context) async {
    Completer<Map<String, dynamic>> completer = Completer();

    // 扫码
    Future<Map<String, dynamic>> getCode() {
      Navigator.push(
        context,
        BottomAnimatedRoute(page: ScanCameraWidget(requiredElementType: true)),
      ).then((value) {
        if (value != null && value is Map<String, dynamic>) {
          completer.complete(value);
        } else {
          completer.complete({});
        }
      });
      return completer.future;
    }

    // 首次会获取摄像头权限，判断摄像头权限
    final bool psState = await PermissionUtils.checkCameraGranted();
    if (psState) {
      return getCode();
    } else {
      // 展示alert
      showCupertinoAlert(context,
          title: intlanguage("app100000865", '相机权限未开启'),
          content: intlanguage("app100000864", '无法使用相机，请前往“设置>精臣云打印>相机”中打开相机权限。'),
          cancelDes: intlanguage("app100000866", '暂不'),
          confirmDes: intlanguage("app01308", '去设置'), cancelAction: () {
        completer.complete({});
      }, confirmAction: () async {
        // 跳转设置页
        await PhotoManager.openSetting();
        completer.complete({});
      });
      return completer.future;
    }
  }

  _refreshTabData() {
    _tabController.index = 0;
    bool hasModifyCategory = logic.checkGoodsCategoryChange();
    if (hasModifyCategory) {
      goodCategories = logic.getCurrentCat();
      setState(() {});
    }
    logic.getGoodsCategory().then((cats) {
      _tabController = TabController(initialIndex: 0, length: cats.length, vsync: this);
      _tabController.addListener(_handleTabChange);
      goodCategories = cats;
      setState(() {});
      logic.getGoodList(1, isSearch: false, userCategoryId: -1);
    });
  }

  _refreshCategoryData(){
   logic.getGoodsCategory().then((cats) {
      _tabController = TabController(initialIndex: 0, length: cats.length, vsync: this);
      _tabController.addListener(_handleTabChange);
      goodCategories = cats;
      setState(() {});
      logic.getGoodList(1, isSearch: false, userCategoryId: -1);
    });
  }
}

class BottomAnimatedRoute extends PageRouteBuilder {
  final Widget page;

  BottomAnimatedRoute({required this.page})
      : super(
          pageBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
          ) =>
              page,
          transitionsBuilder: (
            BuildContext context,
            Animation<double> animation,
            Animation<double> secondaryAnimation,
            Widget child,
          ) =>
              SlideTransition(
            position: Tween(
              begin: Offset(0, 1),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
}
