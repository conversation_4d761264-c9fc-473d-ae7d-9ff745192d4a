package com.gengcon.connectui.helper

import android.animation.ValueAnimator
import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.Window
import android.widget.EditText
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.Switch
import android.widget.TextView
import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.KeyboardUtils
import com.blankj.utilcode.util.LogUtils
import com.gengcon.connect.DeviceC1Helper
import com.gengcon.connect.JCConnectionManager
import com.gengcon.connect.bean.NiimbotPrinter
import com.gengcon.connectui.BuildConfig
import com.gengcon.connectui.DeviceConfigureWifiActivity
import com.gengcon.connectui.DeviceWifiInfoActivity
import com.gengcon.connectui.R
import com.gengcon.connectui.TubeCalibrationLengthFilter
import com.gengcon.connectui.bean.KeyFunctionItemBean
import com.gengcon.connectui.bean.MachineCascadeDetailResponseData
import com.gengcon.connectui.bean.PackedDeviceDetailBean
import com.gengcon.connectui.bean.PaperTypeItem
import com.gengcon.connectui.bean.ShutdownTime
import com.gengcon.print.draw.module.event.SHOP_SOURCE_LABEL_PAPER_REMAINING
import com.gengcon.print.draw.module.event.SHOP_SOURCE_RIBBON_REMAINING
import com.gengcon.print.draw.module.print.PDeviceInfo
import com.gengcon.print.draw.module.print.PrinterKeyProperties
import com.gengcon.print.draw.module.print.PrinterKeyProperty
import com.gengcon.print.draw.module.print.PrinterVoiceInfo
import com.gengcon.print.draw.proxy.RFIDConnectionProxyManager
import com.jc.repositories.webview.library.shop.ShopManager
import com.jc.repositories.webview.library.shop.ShopType
import com.niimbot.appframework_library.expand.dismissAllowingStateLoss
import com.niimbot.appframework_library.expand.gone
import com.niimbot.appframework_library.expand.setOnNotDoubleClickListener
import com.niimbot.appframework_library.expand.visible
import com.niimbot.appframework_library.utils.AppUtils
import com.niimbot.appframework_library.utils.NetworkUtils
import com.niimbot.appframework_library.utils.ToastInstance
import com.niimbot.appframework_library.utils.showToast
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeConnectManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.okgolibrary.okgo.DokitOkGo
import com.niimbot.okgolibrary.okgo.callback.JsonCallback
import com.niimbot.okgolibrary.okgo.model.Response
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.json2Any
import com.niimbot.utiliylibray.util.logI
import com.qyx.languagelibrary.LanguageTextView
import com.qyx.languagelibrary.utils.LanguageUtil
import com.qyx.languagelibrary.utils.LocaleUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.JCApi
import melon.south.com.baselibrary.util.SizeUtils
import java.lang.Integer.min
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

/**
 * 设备详情辅助处理类
 *
 * <AUTHOR>
 * @since 2023/9/8
 */
object DeviceDetailsHelper {

    var updateBean: MachineCascadeDetailResponseData? = null
    private var getPackedDeviceInfoJob: Job? = null

    fun recycle(rootView: View) {
        DeviceDetailsPopWinHelper.dismissPopWin()
        DeviceDetailsBottomDialogHelper.dismissDialog()
        DeviceUpgradeHelper.dismissUpgradeDialog()
        recycleConsumableRemainAnimator(rootView)
        getPackedDeviceInfoJob?.cancel()
        getPackedDeviceInfoJob = null
        updateBean = null
    }

    fun clearMachineCascadeDetail() {
        updateBean = null
    }

    fun connectRequestMachineCascadeDetail() {
        MainScope().launch {
            RFIDConnectionProxyManager.connectedDevice?.let { device ->
                if (device.hardwareVersion.isEmpty() || device.softwareVersion.isEmpty()) {
                    withContext(Dispatchers.IO) {
                        val deviceName = device.name
                        if (DeviceETagHelper.getInstance().isDeviceEtag(deviceName)) {
                            delay(1000)
                            if (deviceName == RFIDConnectionProxyManager.connectedDevice?.deviceName) {
                                DeviceETagHelper.getInstance().getDeviceInfo(device)
                                if(deviceName == RFIDConnectionProxyManager.connectedDevice?.deviceName){
                                    RFIDConnectionProxyManager.latestConnectHardwareVersion = device.hardwareVersion
                                    RFIDConnectionProxyManager.latestConnectSoftwareVersion = device.softwareVersion
                                }
                            }
                        } else {
                            delay(2000)
                            if (deviceName == RFIDConnectionProxyManager.connectedDevice?.deviceName) {
                                RFIDConnectionProxyManager.requestDeviceVersionInfo()
                            }
                        }
                    }
                }
                if (device.hardwareVersion.isEmpty() || device.softwareVersion.isEmpty()) {
                    return@launch
                }
                if (device.deviceName == RFIDConnectionProxyManager.connectedDevice?.deviceName) {
                    requestMachineCascadeDetail()
                }
            }
        }
    }

    fun connectGetPackedDeviceInfo() {
        if(RFIDConnectionProxyManager.connectedDevice?.getPackedDeviceInfoFlag == true){
            return
        }
        getPackedDeviceInfoJob = GlobalScope.launch(Dispatchers.IO) {
            RFIDConnectionProxyManager.connectedDevice?.let { device ->
                device.getPackedDeviceInfoFlag = true
                if (DeviceETagHelper.getInstance().isDeviceEtag(device.name)) {
                    DeviceETagHelper.getInstance().getDeviceVoice(device)
                    DeviceETagHelper.getInstance().getShutdownTime(device)
                    device.showDeviceDetailFlag = true
                    MainScope().launch {
                        RFIDConnectionProxyManager.updateDeviceSettingListener?.invoke()
                    }
                }
                else {
                    val packedDeviceDetailJson = withContext(Dispatchers.Main){
                        FlutterMethodInvokeConnectManager.getPackedPrinterInfo()
                    }
                    if(packedDeviceDetailJson != null) {
                        val packedDeviceDetailBean = json2Any(packedDeviceDetailJson, PackedDeviceDetailBean::class.java)!!
                        device.shutdownTimeGear = packedDeviceDetailBean.autoShutDownTimeLevel
                        val deviceVoiceInfo = PrinterVoiceInfo()
                        val deviceVoice = packedDeviceDetailBean.deviceVoice ?: -1
                        if(deviceVoice >= 0){
                            deviceVoiceInfo.supportVoiceConfig = true
                            deviceVoiceInfo.voiceOpen = deviceVoice > 0
                        }
                        device.deviceVoiceInfo = deviceVoiceInfo
                        val bluetoothVoiceInfo = PrinterVoiceInfo()
                        val bluetoothVoice = packedDeviceDetailBean.bluetoothVoice ?: -1
                        if(bluetoothVoice >= 0){
                            bluetoothVoiceInfo.supportVoiceConfig = true
                            bluetoothVoiceInfo.voiceOpen = bluetoothVoice > 0
                        }
                        device.bluetoothVoiceInfo = bluetoothVoiceInfo
                        packedDeviceDetailBean.printerKeyProperties?.firstOrNull { it.functionSupport.isNotEmpty() }?.let {
                            val printerKeyProperties = PrinterKeyProperties()
                            printerKeyProperties.key = it.keyIndex
                            printerKeyProperties.functionCodes.addAll(it.functionSupport)
                            device.printerKeyProperties = printerKeyProperties
                        }
                        packedDeviceDetailBean.printerKeyFunctions?.firstOrNull()?.let {
                            val printerKeyPropertyNew = PrinterKeyProperty()
                            printerKeyPropertyNew.key = it.keyIndex
                            printerKeyPropertyNew.functionCode = it.functionValue
                            device.printerKeyProperty = printerKeyPropertyNew
                        }
                    }
                    device.showDeviceDetailFlag = true
                    MainScope().launch {
                        RFIDConnectionProxyManager.updateDeviceSettingListener?.invoke()
                        if(device.isSupportKeyPrintRealTime()){
                            FlutterMethodInvokeConnectManager.setPrinterRealTime()
                        }
                    }
                }
            }
        }
    }

    fun requestMachineCascadeDetailIfNot() {
        if (updateBean != null) {
            return
        }
        MainScope().launch {
            if (updateBean == null) {
                RFIDConnectionProxyManager.connectedDevice?.let { device ->
                    if (device.hardwareVersion.isEmpty() || device.softwareVersion.isEmpty()) {
                        withContext(Dispatchers.IO) {
                            if (DeviceETagHelper.getInstance().isDeviceEtag(device.name)) {
                                DeviceETagHelper.getInstance().getDeviceInfo(device)
                            } else {
                                RFIDConnectionProxyManager.requestDeviceVersionInfo()
                            }
                        }
                    }
                    if (device.hardwareVersion.isNotEmpty() && device.softwareVersion.isNotEmpty()) {
                        requestMachineCascadeDetail()
                    }
                }
            }
        }
    }

    private fun requestMachineCascadeDetail() {
        if (!NetworkUtils.isConnected()) {
            return
        }
        RFIDConnectionProxyManager.connectedDevice?.let { device ->
            val params = device.getRequestMachineCascadeDetailParams()
            DokitOkGo.post<String>(JCApi.GET_MACHINE_CASCADE_DETAIL)
                .params("machineType", params["machineType"])
                .params("hardVersion", params["hardVersion"])
                .params("firmVersion", params["firmVersion"])
                .params("machineName", params["machineName"])
                .params("machineSno", params["machineSno"])
                .headers("need_encrypt", "1")
                .execute(object : JsonCallback<MachineCascadeDetailResponseData>() {
                    override fun onSuccess(response: Response<MachineCascadeDetailResponseData>) {
                        if (device.deviceName == RFIDConnectionProxyManager.connectedDevice?.deviceName) {
                            updateBean = response.body()
                            RFIDConnectionProxyManager.machineCascadeDetailListener?.invoke()
                            RFIDConnectionProxyManager.updateDeviceSettingListener?.invoke()
                        }
                    }

                    override fun onError(response: Response<MachineCascadeDetailResponseData>?) {
                        val error = response?.errorMessage()
                        logI("DeviceDetailsHelper", "requestMachineCascadeDetail failed: error = $error")
                    }
                })
        }
    }

    /**
     * 按键功能配置项
     */
    private fun getKeyFunctionConfigItems(printerKeyProperties: PrinterKeyProperties): ArrayList<KeyFunctionItemBean> {
        val keyFunctionItemBeans = arrayListOf<KeyFunctionItemBean>()
        val key = printerKeyProperties.key
        printerKeyProperties.functionCodes.forEach {
            val keyFunctionItemBean = KeyFunctionItemBean(key, it, getKeyFunctionDes(it))
            keyFunctionItemBeans.add(keyFunctionItemBean)
        }
        return keyFunctionItemBeans
    }

    /**
     * 按键功能描述
     */
    fun getKeyFunctionDes(functionCode: Int?): String {
        if (functionCode == null) {
            return ""
        }
        return when (functionCode) {
            1 -> LanguageUtil.findLanguageString("app100001030")
            2 -> LanguageUtil.findLanguageString("app100001031")
            3 -> LanguageUtil.findLanguageString("app100001032")
            else -> "按键功能：$functionCode"
        }
    }

    fun setClickListener(rootView: View, toConfigureWifi: ()->Unit, cancelCheckToMain: ()->Unit) {
        val firmwareUpgrade = rootView.findViewById<View>(R.id.firmware_upgrade_tv)
        val shopPaper = rootView.findViewById<View>(R.id.shop_paper_rl)
        val shopRibbon = rootView.findViewById<View>(R.id.shop_ribbon_rl)
        val printerStartupActionConfig = rootView.findViewById<View>(R.id.printer_startup_action_ll)
        val printerStartupActionPopAnchor =
            rootView.findViewById<View>(R.id.printer_startup_action_pop_anchor)
        val wifiConfig = rootView.findViewById<View>(R.id.wifi_config_ll)
        val paperCalibration = rootView.findViewById<View>(R.id.paper_calibration_ll)
        val autoShutdownConfig = rootView.findViewById<View>(R.id.auto_shutdown_config_ll)
        val autoShutdownConfigPopAnchor =
            rootView.findViewById<View>(R.id.auto_shutdown_config_pop_anchor)
        val tubeCalibrationTv = rootView.findViewById<View>(R.id.tube_calibration_tv)
        firmwareUpgrade.setOnNotDoubleClickListener {
            val connectedDevice =
                RFIDConnectionProxyManager.connectedDevice ?: return@setOnNotDoubleClickListener
            val activity = rootView.context as Activity
            val firmwareVersion = connectedDevice.softwareVersion
            val hardwareVersion = connectedDevice.hardwareVersion
            DeviceUpgradeHelper.showFirmwareUpgradeDialog(
                activity,
                firmwareVersion,
                hardwareVersion,
                updateBean!!,
                connectedDevice.isForbiddenPowerInUpgrade()
            )
            cancelCheckToMain.invoke()
            BuriedHelper.trackEvent("click", "127_353")
        }
        shopPaper.setOnNotDoubleClickListener {
            toConsumablePurchasePage(shopPaper, SHOP_SOURCE_LABEL_PAPER_REMAINING)
            BuriedHelper.trackEvent("click", "127_345_322", hashMapOf(Pair("jumpsource", "label_paper_remaining")))
        }
        shopRibbon.setOnNotDoubleClickListener {
            toConsumablePurchasePage(shopRibbon, SHOP_SOURCE_RIBBON_REMAINING)
            BuriedHelper.trackEvent("click", "127_346_323", hashMapOf(Pair("jumpsource", "ribbon_remaining")))
        }
        printerStartupActionConfig.setOnNotDoubleClickListener {
            val activity = rootView.context as Activity
            val data =
                getKeyFunctionConfigItems(RFIDConnectionProxyManager.connectedDevice!!.printerKeyProperties!!)
            DeviceDetailsPopWinHelper.showStartupActionConfigPopWin(
                activity,
                printerStartupActionPopAnchor,
                data
            )
            cancelCheckToMain.invoke()
        }
        wifiConfig.setOnNotDoubleClickListener {
            val activity = rootView.context as Activity
            activity.startActivity(Intent().apply {
                if (wifiConfig.tag == "-1" || wifiConfig.tag == null) {
                    setClass(activity, DeviceConfigureWifiActivity::class.java)
                } else {
                    setClass(activity, DeviceWifiInfoActivity::class.java)
                }
                this.putExtra("fromDetailPage", true)
                //todo 连接业务接口调整
                val bluetoothDevice = JCConnectionManager.getInstance().getConnectedDevice()
                this.putExtra("printer", GsonUtils.toJson(bluetoothDevice?.let { it1 ->
                    NiimbotPrinter.create(it1)
                }))
            })
            toConfigureWifi.invoke()
            BuriedHelper.trackEvent("click", "127_349")
        }
        paperCalibration.setOnNotDoubleClickListener {
            if(paperCalibration.tag == null){
                return@setOnNotDoubleClickListener
            }
            val paperList = paperCalibration.tag as List<PaperTypeItem>
            val activity = rootView.context as Activity
            DeviceDetailsBottomDialogHelper.showPaperCalibrationDialog(activity, paperList)
            cancelCheckToMain.invoke()
            BuriedHelper.trackEvent("click", "127_348")
        }
        autoShutdownConfig.setOnNotDoubleClickListener {
            val activity = rootView.context as Activity
            val data = updateBean!!.shutdownTimes.map { it ->
                ShutdownTime().apply {
                    this.gear = it.gear.toString()
                    this.minute = it.time
                }
            }
            DeviceDetailsPopWinHelper.showAutoShutdownTimeConfigPopWin(
                activity,
                autoShutdownConfigPopAnchor,
                data
            )
            cancelCheckToMain.invoke()
            BuriedHelper.trackEvent("click", "127_350")
        }
        tubeCalibrationTv.setOnNotDoubleClickListener {
            val clickBuried = {
                BuriedHelper.trackEvent("click", "127_370")
            }
            if (DeviceC1Helper.tubeUninstalled || DeviceC1Helper.ribbonStatus == 4) {
                showToast(LanguageUtil.findLanguageString("app100001464"))
                clickBuried.invoke()
                return@setOnNotDoubleClickListener
            }
            //先发送走管指令
            FlutterMethodInvokeConnectManager.setTubeCalibration {
                if(it == 0){
                    //再发送校准指令
                    showSetTubeCalibrationLengthDialog(rootView)
                }
                else{
                    showToast(LanguageUtil.findLanguageString("app100001469"))
                }
            }
            clickBuried.invoke()
        }
    }

    /**
     * 设置走管校准长度弹窗
     */
    private fun showSetTubeCalibrationLengthDialog(rootView: View) {
        val activity = rootView.context as Activity
        val dialog = Dialog(activity, R.style.Theme_LightDialog)
        dialog.setCanceledOnTouchOutside(false)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        val view = LayoutInflater.from(activity).inflate(R.layout.dialog_tube_calibration, null)
        val editLength = view.findViewById<EditText>(R.id.edLength)
        val tvCancel = view.findViewById<TextView>(R.id.tvCancel)
        val tvConfirm = view.findViewById<TextView>(R.id.tvConfirm)
        dialog.setContentView(view)
        if (LocaleUtils.isRtl(activity)) {
            editLength.gravity = Gravity.RIGHT or Gravity.CENTER_VERTICAL
        }
        editLength.filters = arrayOf(TubeCalibrationLengthFilter())
        editLength.isFocusable = true
        editLength.isFocusableInTouchMode = true
        editLength.requestFocus()
        editLength.postDelayed({ KeyboardUtils.showSoftInput(editLength) }, 100)
        tvCancel.setOnClickListener {
            KeyboardUtils.hideSoftInput(editLength)
            dialog.dismissAllowingStateLoss()
        }
        tvConfirm.setOnClickListener {
            var length = 0f
            try {
                length = editLength.text.toString().removeSuffix(".").toFloat()
            } catch (e: Exception) {

            }
            if (length < 50f) {
                melon.south.com.baselibrary.util.showToast(LanguageUtil.findLanguageString("app100001671"))
                return@setOnClickListener
            }
            KeyboardUtils.hideSoftInput(editLength)
            dialog.dismissAllowingStateLoss()
            FlutterMethodInvokeConnectManager.setTubeAdjustLength(length){
                if (it == 0) {
                    ToastInstance.INSTANCE.showSuccessToast(activity, LanguageUtil.findLanguageString("app100001473"))
                    val tubeCalibrationStatusTv = rootView.findViewById<LanguageTextView>(R.id.tube_calibration_status_tv)
                    if (length.toInt().toFloat() == length) {
                        tubeCalibrationStatusTv.text = length.toInt().toString() + "mm"
                    } else {
                        tubeCalibrationStatusTv.text = length.toString() + "mm"
                    }
                    val deviceName = RFIDConnectionProxyManager.connectedDevice!!.deviceName
                    DeviceC1Helper.saveTubeCalibrationLength(deviceName, length)
                } else {
                    showToast(LanguageUtil.findLanguageString("app100001474"))
                }
            }
        }
        dialog.show()
        dialog.window?.attributes?.width = AppUtils.getScreenWidth(activity) * 4 / 5
        dialog.window?.setGravity(Gravity.CENTER)
    }

    fun updateDeviceSetting(rootView: View) {
        val connectedDevice = RFIDConnectionProxyManager.connectedDevice ?: return
        updateDeviceVersionInfo(rootView, connectedDevice)
        updateConsumableInfo(rootView)
        updateDeviceOtherInfo(rootView)
        updateWifiStatus(rootView)
        updateTubeCalibration(rootView)
    }

    private fun updateDeviceVersionInfo(rootView: View, connectedDevice: PDeviceInfo) {
        val firmwareVersion = rootView.findViewById<TextView>(R.id.firmware_version_tv)
        val hardwareVersion = rootView.findViewById<TextView>(R.id.hardware_version_tv)
        val serialTv = rootView.findViewById<TextView>(R.id.serial_tv)
        val upgradeTv = rootView.findViewById<TextView>(R.id.firmware_upgrade_tv)
        firmwareVersion.text = connectedDevice.softwareVersion
        hardwareVersion.text = connectedDevice.hardwareVersion
        serialTv.text = connectedDevice.serialNo
        if (!updateBean?.url.isNullOrEmpty()) {
            if(upgradeTv.visibility == View.GONE) {
                BuriedHelper.trackEvent("show", "127_353")
            }
            upgradeTv.visible()
        } else {
            upgradeTv.gone()
        }
        rootView.findViewById<View>(R.id.device_version_info_ll).visible()
    }

    private var paperRemainAnimator: ValueAnimator? = null
    private var ribbonRemainAnimator: ValueAnimator? = null
    private fun updateConsumableInfo(rootView: View) {
        val paperInfoRl = rootView.findViewById<View>(R.id.paper_info_rl)
        val paperTotal = rootView.findViewById<View>(R.id.paper_total)
        val paperRemain = rootView.findViewById<View>(R.id.paper_remain)
        val paperUseUp = rootView.findViewById<View>(R.id.paper_use_up_tv)
        val shopPaperRl = rootView.findViewById<View>(R.id.shop_paper_rl)
        val ribbonInfoRl = rootView.findViewById<View>(R.id.ribbon_info_rl)
        val ribbonTotal = rootView.findViewById<View>(R.id.ribbon_total)
        val ribbonRemain = rootView.findViewById<View>(R.id.ribbon_remain)
        val ribbonUseUp = rootView.findViewById<View>(R.id.ribbon_use_up_tv)
        val shopRibbonRl = rootView.findViewById<View>(R.id.shop_ribbon_rl)
        val rfidSpace = rootView.findViewById<View>(R.id.rfid_space)
        shopPaperRl.tag = null
        shopRibbonRl.tag = null
        val rfidPaper = RFIDConnectionProxyManager.getRFIDPaper()
        val rfidRibbon = RFIDConnectionProxyManager.getRFIDRibbon()
        val connectedDevice = RFIDConnectionProxyManager.connectedDevice
        if (connectedDevice != null && DeviceETagHelper.getInstance().isDeviceEtag(connectedDevice.name)) {
            paperInfoRl.gone()
            ribbonInfoRl.gone()
            rfidSpace.gone()
            recycleConsumableRemainAnimator(rootView)
            return
        }
        val rfidPaperActualNum = RFIDConnectionProxyManager.getRFIDPaperActualNum()
        val rfidRibbonActualNum = RFIDConnectionProxyManager.getRFIDRibbonActualNum()
        if(rfidPaper != null){
            LogUtils.iTag("DeviceDetailsRfidInfo", "DeviceDetailsRfidInfo show, rfidPaper = ${any2Json(rfidPaper)}")
            LogUtils.iTag("DeviceDetailsRfidInfo", "DeviceDetailsRfidInfo show, rfidPaperActualNum = $rfidPaperActualNum")
        }
        if(rfidRibbon != null){
            LogUtils.iTag("DeviceDetailsRfidInfo", "DeviceDetailsRfidInfo show, rfidRibbon = ${any2Json(rfidRibbon)}")
            LogUtils.iTag("DeviceDetailsRfidInfo", "DeviceDetailsRfidInfo show, rfidRibbonActualNum = $rfidRibbonActualNum")
        }
        var showShop: Boolean
        if(ShopManager.shopType == ShopType.None){
            showShop = false
            shopPaperRl.gone()
            shopRibbonRl.gone()
        }
        else{
            showShop = true
            shopPaperRl.visible()
            shopRibbonRl.visible()
            if(rfidPaper != null){
                shopPaperRl.tag = rfidPaper.one_code
            }
            if(rfidRibbon != null){
                shopRibbonRl.tag = rfidRibbon.one_code
            }
        }
        val paperRemainLevel = rfidPaper?.remainLevel(rfidPaperActualNum) ?: 0
        val ribbonRemainLevel = rfidRibbon?.remainLevel(rfidRibbonActualNum) ?: 0
        var totalLength: Int
        if(BuildConfig.DEBUG) {
            val paperInfoTv = rootView.findViewById<LanguageTextView>(R.id.paper_info_tv)
            val ribbonInfoTv = rootView.findViewById<LanguageTextView>(R.id.ribbon_info_tv)
            paperInfoTv.text =
                LanguageUtil.findLanguageString("app100001743") + "(等级：$paperRemainLevel)"
            ribbonInfoTv.text =
                LanguageUtil.findLanguageString("app01222") + "(等级：$ribbonRemainLevel)"
        }
        if (rfidPaper != null && rfidRibbon != null) {
            totalLength = if(showShop) (0.5 * (SizeUtils.getWidth() - SizeUtils.dp2px(220f))).toInt() else (0.5 * (SizeUtils.getWidth() - SizeUtils.dp2px(108f))).toInt()
            val paperRemainWidth = totalLength * paperRemainLevel / 10
            val ribbonRemainWidth = totalLength * ribbonRemainLevel / 10
            if(paperInfoRl.visibility == View.GONE){
                BuriedHelper.trackEvent("show", "127_345")
                if(ShopManager.shopType != ShopType.None){
                    BuriedHelper.trackEvent("show", "127_345_322")
                }
            }
            if(ribbonInfoRl.visibility == View.GONE){
                BuriedHelper.trackEvent("show", "127_346")
                if(ShopManager.shopType != ShopType.None){
                    BuriedHelper.trackEvent("show", "127_346_323")
                }
            }
            paperInfoRl.visible()
            ribbonInfoRl.visible()
            rfidSpace.visible()
            if (paperRemainLevel == 0) {
                paperUseUp.visible()
                paperTotal.gone()
                recyclePaperRemainAnimator(rootView)
            } else {
                paperTotal.visible()
                paperUseUp.gone()
                (paperTotal.layoutParams as RelativeLayout.LayoutParams).width = totalLength
                showPaperRemain(rootView, paperRemainWidth)
                if (paperRemainLevel > 4) {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_high)
                } else if (paperRemainLevel > 2) {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_middle)
                } else {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_low)
                }
            }
            if (ribbonRemainLevel == 0) {
                ribbonUseUp.visible()
                ribbonTotal.gone()
                recycleRibbonRemainAnimator(rootView)
            } else {
                ribbonTotal.visible()
                ribbonUseUp.gone()
                (ribbonTotal.layoutParams as RelativeLayout.LayoutParams).width = totalLength
                showRibbonRemain(rootView, ribbonRemainWidth)
                if (ribbonRemainLevel > 4) {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_high)
                } else if (ribbonRemainLevel > 2) {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_middle)
                } else {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_low)
                }
            }
        } else if (rfidPaper != null) {
            totalLength = if(showShop) SizeUtils.getWidth() - SizeUtils.dp2px(120f) else SizeUtils.getWidth() - SizeUtils.dp2px(64f)
            val paperRemainWidth = totalLength * paperRemainLevel / 10
            if(paperInfoRl.visibility == View.GONE){
                BuriedHelper.trackEvent("show", "127_345")
                if(ShopManager.shopType != ShopType.None){
                    BuriedHelper.trackEvent("show", "127_345_322")
                }
            }
            paperInfoRl.visible()
            ribbonInfoRl.gone()
            rfidSpace.gone()
            recycleRibbonRemainAnimator(rootView)
            if (paperRemainLevel == 0) {
                paperUseUp.visible()
                paperTotal.gone()
                recyclePaperRemainAnimator(rootView)
            } else {
                paperTotal.visible()
                paperUseUp.gone()
                (paperTotal.layoutParams as RelativeLayout.LayoutParams).width = totalLength
                showPaperRemain(rootView, paperRemainWidth)
                if (paperRemainLevel > 4) {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_high)
                } else if (paperRemainLevel > 2) {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_middle)
                } else {
                    paperRemain.setBackgroundResource(R.drawable.consumable_remain_low)
                }
            }
        } else if (rfidRibbon != null) {
            totalLength = if(showShop) SizeUtils.getWidth() - SizeUtils.dp2px(120f) else SizeUtils.getWidth() - SizeUtils.dp2px(64f)
            val ribbonRemainWidth = totalLength * ribbonRemainLevel / 10
            if(ribbonInfoRl.visibility == View.GONE){
                BuriedHelper.trackEvent("show", "127_346")
                if(ShopManager.shopType != ShopType.None){
                    BuriedHelper.trackEvent("show", "127_346_323")
                }
            }
            ribbonInfoRl.visible()
            paperInfoRl.gone()
            rfidSpace.gone()
            recyclePaperRemainAnimator(rootView)
            if (ribbonRemainLevel == 0) {
                ribbonUseUp.visible()
                ribbonTotal.gone()
                recycleRibbonRemainAnimator(rootView)
            } else {
                ribbonTotal.visible()
                ribbonUseUp.gone()
                (ribbonTotal.layoutParams as RelativeLayout.LayoutParams).width = totalLength
                showRibbonRemain(rootView, ribbonRemainWidth)
                if (ribbonRemainLevel > 4) {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_high)
                } else if (ribbonRemainLevel > 2) {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_middle)
                } else {
                    ribbonRemain.setBackgroundResource(R.drawable.consumable_remain_low)
                }
            }
        } else {
            paperInfoRl.gone()
            ribbonInfoRl.gone()
            rfidSpace.gone()
            recycleConsumableRemainAnimator(rootView)
        }
    }

    private var paperRemainValue: Int = 0
    private fun showPaperRemain(rootView: View, paperRemainWidth: Int){
        if(paperRemainWidth == paperRemainValue) {
            return
        }
        val delay = if(paperRemainValue == 0) 300L else 0L
        paperRemainValue = paperRemainWidth
        val paperRemain = rootView.findViewById<View>(R.id.paper_remain)
        if(paperRemainValue != 0){
            paperRemainAnimator = ValueAnimator.ofInt(0, paperRemainValue)
            paperRemainAnimator?.startDelay = delay
            paperRemainAnimator?.addUpdateListener {
                val animatorValue = it.animatedValue as Int
                val layoutParams = paperRemain.layoutParams as RelativeLayout.LayoutParams
                layoutParams.width = animatorValue
                paperRemain.layoutParams = layoutParams
            }
            paperRemainAnimator?.start()
        }
        else {
            recyclePaperRemainAnimator(rootView)
            (paperRemain.layoutParams as RelativeLayout.LayoutParams).width = 0
        }
//        (paperRemain.layoutParams as RelativeLayout.LayoutParams).width = paperRemainWidth
    }

    private var ribbonRemainValue = 0
    private fun showRibbonRemain(rootView: View, ribbonRemainWidth: Int){
        if(ribbonRemainWidth == ribbonRemainValue) {
            return
        }
        val delay = if(ribbonRemainValue == 0) 300L else 0L
        ribbonRemainValue = ribbonRemainWidth
        val ribbonRemain = rootView.findViewById<View>(R.id.ribbon_remain)
        if(ribbonRemainValue != 0){
            ribbonRemainAnimator = ValueAnimator.ofInt(0, ribbonRemainValue)
            ribbonRemainAnimator?.startDelay = delay
            ribbonRemainAnimator?.addUpdateListener {
                val animatorValue = it.animatedValue as Int
                val layoutParams = ribbonRemain.layoutParams as RelativeLayout.LayoutParams
                layoutParams.width = animatorValue
                ribbonRemain.layoutParams = layoutParams
            }
            ribbonRemainAnimator?.start()
        }
        else {
            recycleRibbonRemainAnimator(rootView)
            (ribbonRemain.layoutParams as RelativeLayout.LayoutParams).width = 0
        }
//        (ribbonRemain.layoutParams as RelativeLayout.LayoutParams).width = ribbonRemainWidth
    }

    private fun toConsumablePurchasePage(view: View, jumpSource: String){
        if(!NetworkUtils.isConnected()){
            showToast("app01139")
            return
        }
        val activity = view.context as Activity
        val oneCode = view.tag as String
        val machineId = RFIDConnectionProxyManager.connectedDevice?.deviceName ?: ""
        LoginDataEnum.loginCheck(activity){
            var url: String
            if(ShopManager.shopType == ShopType.China) {
                if (machineId.isNotEmpty()) {
                    url =
                        ShopManager.SHOP_PURCHASE + "/$oneCode?machine_id=$machineId&jumpSource=$jumpSource"
                } else {
                    url = ShopManager.SHOP_PURCHASE + "/$oneCode?jumpSource=$jumpSource"
                }
            }
            else{
                if (machineId.isNotEmpty()) {
                    url =
                        ShopManager.SHOP_PURCHASE.replace("purchase", "detail") + "?barCode=$oneCode&machine_id=$machineId&jumpSource=$jumpSource"
                } else {
                    url = ShopManager.SHOP_PURCHASE.replace("purchase", "detail") + "?barCode=$oneCode&jumpSource=$jumpSource"
                }
            }
            url = ShopManager.getFinalUrl(url)
            LogUtils.iTag("DeviceDetailsRfidInfo", "DeviceDetailsRfidInfo shop, url = $url")
            NiimbotGlobal.gotoShopWeb(activity, url)
        }
    }

    private fun updateDeviceOtherInfo(rootView: View) {
        val printerStartupActionConfigLL =
            rootView.findViewById<View>(R.id.printer_startup_action_ll)
        val printerStartupActionConfigTv =
            rootView.findViewById<LanguageTextView>(R.id.printer_startup_action_tv)
        val deviceOtherInfoLL = rootView.findViewById<View>(R.id.device_other_info_ll)
        val wifiConfigLL = rootView.findViewById<View>(R.id.wifi_config_ll)
        val wifiConfigTv = rootView.findViewById<TextView>(R.id.wifi_config_tv)
        val paperCalibrationDivider = rootView.findViewById<View>(R.id.paper_calibration_divider)
        val paperCalibrationLL = rootView.findViewById<View>(R.id.paper_calibration_ll)
        paperCalibrationLL.tag = null
        val paperCalibrationArrowIv =
            rootView.findViewById<ImageView>(R.id.paper_calibration_arrow_iv)
        val autoShutdownConfigDivider =
            rootView.findViewById<View>(R.id.auto_shutdown_config_divider)
        val autoShutdownConfigLL = rootView.findViewById<View>(R.id.auto_shutdown_config_ll)
        val autoShutdownConfigTv =
            rootView.findViewById<LanguageTextView>(R.id.auto_shutdown_config_tv)
        val startupShutdownVoiceDivider =
            rootView.findViewById<View>(R.id.startup_shutdown_voice_divider)
        val startupShutdownVoiceLL = rootView.findViewById<View>(R.id.startup_shutdown_voice_ll)
        val startupShutdownVoiceSw = rootView.findViewById<Switch>(R.id.startup_shutdown_voice_sw)
        val bluetoothConnectedVoiceDivider =
            rootView.findViewById<View>(R.id.bluetooth_connected_voice_divider)
        val bluetoothConnectedVoiceLL =
            rootView.findViewById<View>(R.id.bluetooth_connected_voice_ll)
        val bluetoothConnectedVoiceSw =
            rootView.findViewById<Switch>(R.id.bluetooth_connected_voice_sw)
        val connectedDevice = RFIDConnectionProxyManager.connectedDevice
        if (connectedDevice == null || !connectedDevice.showDeviceDetailFlag) {
            printerStartupActionConfigLL.gone()
            deviceOtherInfoLL.gone()
            return
        }
        val functionCodes = connectedDevice.printerKeyProperties?.functionCodes
        if (!functionCodes.isNullOrEmpty()) {
            printerStartupActionConfigLL.visible()
            printerStartupActionConfigTv.text = getKeyFunctionDes(
                connectedDevice.printerKeyProperty?.functionCode ?: functionCodes.first()
            )
        } else {
            printerStartupActionConfigLL.gone()
        }
        deviceOtherInfoLL.gone()
        var otherDeviceItemShow = false
        var isSupportWifi = false
        if (!DeviceETagHelper.getInstance().isDeviceEtag(connectedDevice.name)) {
            isSupportWifi = connectedDevice.is_wifi == 1
        }
        if (isSupportWifi) {
            otherDeviceItemShow = true
            wifiConfigLL.visible()
        } else {
            wifiConfigLL.gone()
        }
        val isSupportPaperCalibration =
            if (DeviceETagHelper.getInstance().isDeviceEtag(connectedDevice.name) || RFIDConnectionProxyManager.connectedDevice?.isC1() == true) {
                false
            } else {
                updateBean?.isSupportPaperCalibration() ?: false
            }
        if (isSupportPaperCalibration) {
            if (otherDeviceItemShow) {
                paperCalibrationDivider.visible()
            } else {
                paperCalibrationDivider.gone()
            }
            otherDeviceItemShow = true
            paperCalibrationLL.visible()
            val paperTypeNameList = updateBean!!.paperTypeNames
            val paperTypeList = updateBean!!.paperType
            val size = min(paperTypeNameList.size, paperTypeList.size)
            val paperList = arrayListOf<PaperTypeItem>()
            for(i in 0 until size){
                if(paperList.none { it.type == paperTypeList[i] }){
                    val paper = PaperTypeItem(paperTypeList[i], paperTypeNameList[i])
                    paperList.add(paper)
                }
            }
            if(paperList.isNotEmpty()){
                paperCalibrationLL.tag = paperList
            }
        } else {
            paperCalibrationDivider.gone()
            paperCalibrationLL.gone()
        }
        val shutdownTimeBean =
            if (updateBean?.isAutoShutdown == true && connectedDevice.shutdownTimeGear != null) updateBean?.shutdownTimes?.firstOrNull {
                it.gear == connectedDevice.shutdownTimeGear
            } else null
        if (shutdownTimeBean != null) {
            if (otherDeviceItemShow) {
                autoShutdownConfigDivider.visible()
            } else {
                autoShutdownConfigDivider.gone()
            }
            otherDeviceItemShow = true
            autoShutdownConfigLL.visible()
            if (shutdownTimeBean.time == "永不") {
                autoShutdownConfigTv.text = "app100000111"
            } else {
                autoShutdownConfigTv.text =
                    "${shutdownTimeBean.time}${LanguageUtil.findLanguageString("app100000110")}"
            }
        } else {
            autoShutdownConfigDivider.gone()
            autoShutdownConfigLL.gone()
        }
        if (connectedDevice.deviceVoiceInfo?.supportVoiceConfig == true) {
            startupShutdownVoiceSw.setOnCheckedChangeListener(null)
            startupShutdownVoiceSw.isChecked = connectedDevice.deviceVoiceInfo?.voiceOpen == true
            startupShutdownVoiceSw.setOnCheckedChangeListener { buttonView, isChecked ->
                if (DeviceETagHelper.getInstance().isDeviceEtag(connectedDevice.name)) {
                    MainScope().launch {
                        val state = if (isChecked) 1 else 0
                        val result = withContext(Dispatchers.IO) {
                            DeviceETagHelper.getInstance().setDeviceVoice(state, state)
                        }
                        if (result) {
                            connectedDevice.deviceVoiceInfo?.voiceOpen = isChecked
                        } else {
                            showToast("app100000624")
                            startupShutdownVoiceSw.isChecked = !startupShutdownVoiceSw.isChecked
                        }
                    }
                } else {
                    MainScope().launch {
                        val result = withContext(Dispatchers.Main) {
                            suspendCoroutine { callback ->
                                FlutterMethodInvokeConnectManager.setDeviceVoice(if(isChecked) 1 else 0){
                                    callback.resume(it == 0)
                                }
                            }
                        }
                        if (result) {
                            connectedDevice.deviceVoiceInfo?.voiceOpen = isChecked
                        } else {
                            showToast("app100000624")
                            startupShutdownVoiceSw.isChecked = !startupShutdownVoiceSw.isChecked
                        }
                    }
                }
                BuriedHelper.trackEvent("click", "127_351")
            }
            if (otherDeviceItemShow) {
                startupShutdownVoiceDivider.visible()
            } else {
                startupShutdownVoiceDivider.gone()
            }
            otherDeviceItemShow = true
            startupShutdownVoiceLL.visible()
        } else {
            startupShutdownVoiceDivider.gone()
            startupShutdownVoiceLL.gone()
        }
        if (connectedDevice.bluetoothVoiceInfo?.supportVoiceConfig == true) {
            bluetoothConnectedVoiceSw.setOnCheckedChangeListener(null)
            bluetoothConnectedVoiceSw.isChecked =
                connectedDevice.bluetoothVoiceInfo?.voiceOpen == true
            bluetoothConnectedVoiceSw.setOnCheckedChangeListener { buttonView, isChecked ->
                MainScope().launch {
                    val result = withContext(Dispatchers.Main) {
                        suspendCoroutine { callback ->
                            FlutterMethodInvokeConnectManager.setBluetoothVoice(if(isChecked) 1 else 0){
                                callback.resume(it == 0)
                            }
                        }
                    }
                    if (result) {
                        connectedDevice.bluetoothVoiceInfo?.voiceOpen = isChecked
                    } else {
                        showToast("app100000624")
                        startupShutdownVoiceSw.isChecked = !startupShutdownVoiceSw.isChecked
                    }
                }
                BuriedHelper.trackEvent("click", "127_352")
            }
            if (otherDeviceItemShow) {
                bluetoothConnectedVoiceDivider.visible()
            } else {
                bluetoothConnectedVoiceDivider.gone()
            }
            otherDeviceItemShow = true
            bluetoothConnectedVoiceLL.visible()
        } else {
            bluetoothConnectedVoiceDivider.gone()
            bluetoothConnectedVoiceLL.gone()
        }
        if (otherDeviceItemShow) {
            deviceOtherInfoLL.visible()
        }
    }

    private fun updateWifiStatus(rootView: View) {
        val deviceOtherInfoLL = rootView.findViewById<View>(R.id.device_other_info_ll)
        val wifiConfigLL = rootView.findViewById<View>(R.id.wifi_config_ll)
        val wifiConfigTv = rootView.findViewById<TextView>(R.id.wifi_config_tv)
        wifiConfigLL.tag = null
        if (deviceOtherInfoLL.visibility == View.GONE || wifiConfigLL.visibility == View.GONE) {
            return
        }
        MainScope().launch {
            FlutterMethodInvokeConnectManager.getDeviceConfigWifi {
                val name = it?.trim() ?: ""
                wifiConfigLL.tag = name
                if(it == "-1"){
                    wifiConfigTv.text = "app100000897"
                }
                else{
                    wifiConfigTv.text = name
                }
            }
        }
    }

    /**
     * C1线号机走管校准
     */
    private fun updateTubeCalibration(rootView: View){
        val tubeCalibrationLL = rootView.findViewById<View>(R.id.tube_calibration_ll)
        val tubeCalibrationStatusTv = rootView.findViewById<LanguageTextView>(R.id.tube_calibration_status_tv)
        if(RFIDConnectionProxyManager.connectedDevice?.isC1() == true){
            tubeCalibrationLL.visible()
            val deviceName = RFIDConnectionProxyManager.connectedDevice!!.deviceName
            val length = DeviceC1Helper.getTubeCalibrationLength(deviceName)
            if (length == 0f) {
                tubeCalibrationStatusTv.text = LanguageUtil.findLanguageString("app100001468")
            } else {
                if (length.toInt().toFloat() == length) {
                    tubeCalibrationStatusTv.text = length.toInt().toString() + "mm"
                } else {
                    tubeCalibrationStatusTv.text = length.toString() + "mm"
                }
            }
        }
        else{
            tubeCalibrationLL.gone()
        }
    }

    fun hideDeviceSetting(rootView: View) {
        rootView.findViewById<View>(R.id.paper_info_rl).gone()
        rootView.findViewById<View>(R.id.ribbon_info_rl).gone()
        rootView.findViewById<View>(R.id.printer_startup_action_ll).gone()
        rootView.findViewById<View>(R.id.device_other_info_ll).gone()
        rootView.findViewById<View>(R.id.device_version_info_ll).gone()
        rootView.findViewById<TextView>(R.id.firmware_upgrade_tv).gone()
        rootView.findViewById<View>(R.id.tube_calibration_ll).gone()
        recycleConsumableRemainAnimator(rootView)
    }

    private fun recycleConsumableRemainAnimator(rootView: View){
        recyclePaperRemainAnimator(rootView)
        recycleRibbonRemainAnimator(rootView)
    }

    private fun recyclePaperRemainAnimator(rootView: View){
        paperRemainValue = 0
        paperRemainAnimator?.cancel()
        paperRemainAnimator = null
        val paperRemain = rootView.findViewById<View>(R.id.paper_remain)
        (paperRemain.layoutParams as RelativeLayout.LayoutParams).width = 0
    }

    private fun recycleRibbonRemainAnimator(rootView: View){
        ribbonRemainValue = 0
        ribbonRemainAnimator?.cancel()
        ribbonRemainAnimator = null
        val ribbonRemain = rootView.findViewById<View>(R.id.ribbon_remain)
        (ribbonRemain.layoutParams as RelativeLayout.LayoutParams).width = 0
    }

    fun handleSettingResult(result: Int, customErrorToast: String? = null, successAction: (() -> Unit)? = null) {
        when(result) {
            0 -> successAction?.invoke()
            //打印机忙碌
            -2 -> showToast("app01200")
            //打印机不支持
            -3 -> showToast("app100000464")
            //断开连接
            -4 -> showToast("app00720")
            -1 -> showToast( customErrorToast ?: "app01203")
        }
    }
}
