import 'dart:convert';
import 'dart:io';

import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:text/database/folder/folder_db_utils.dart';
import 'package:text/database/folder/folder_model.dart' as db_folder;
import 'package:text/database/folder/folder_model.g.dart';
import 'package:text/database/isar_db_manager.dart';
import 'package:text/pages/my_template/controller/folder_manager.dart';
import 'package:text/pages/my_template/model/folder_model.dart';
import 'package:text/template/util/template_detail_db_utils.dart';
import 'package:text/template/util/template_transform_utils.dart';
import 'package:text/utils/niimbot_template_db_manager.dart';
import 'package:niimbot_template/models/template_data.dart' as NiimbotTemplateData;
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
abstract class MigrationHandler<T> {
  // 新增唯一迁移标识符（格式：表名_版本号）
  String get migrationKey;

  Future<int> get sourceCount;

  Future<List<T>> getBatch(int offset, int limit);

  Future<void> migrateItem(T item);

  Future<bool> get isMigrated async {
    final sp = await SharedPreferences.getInstance();
    return sp.getBool(migrationKey) ?? false;
  }

  Future<void> markAsMigrated() async {
    final sp = await SharedPreferences.getInstance();
    await sp.setBool(migrationKey, true);
  }
}

class TemplateMigrationHandler extends MigrationHandler<NiimbotTemplateData.TemplateData> {
  @override
  Future<int> get sourceCount async {
    int count = await NiimbotTemplateDbManager.dbQueryTemplateCount();
    LogUtil.d('MigrationManager模板总数${count}');
    NiimbotLogTool.writeLogToFile({"MigrationManager": 'MigrationManager模板总数${count}'});
    return count;
  }

  @override
  Future<List<NiimbotTemplateData.TemplateData>> getBatch(int offset, int limit) async {
    List<TemplateData> canvasTemplates =
        await NiimbotTemplateDbManager.dbQueryTemplateDataWhere(limit: limit, offset: offset);
    NiimbotLogTool.writeLogToFile({"MigrationManager": '迁移:待迁移用户模板: ${canvasTemplates.length}'});
    List<NiimbotTemplateData.TemplateData> niimbotTemplates = [];
    for (int i = 0; i < canvasTemplates.length; i++) {
      TemplateData canvasT = canvasTemplates[i];
      //处理userId为空的情况(ios)
      if ((canvasT.profile.extrain.userId ?? "").isEmpty) {
        canvasT.profile.extrain.userId = "0";
      }
      if (Platform.isIOS) {
        await canvasT.resetTemplateLocalPathFlutterIOS();
      }
      String canvasJson = jsonEncode(canvasT);
      debugPrint("迁移:模板缩略图地址 id: ${canvasT.id} 地址: ${canvasT.localThumbnail}");
      NiimbotLogTool.writeLogToFile({"MigrationManager": '迁移:模板缩略图地址 id: ${canvasT.id} 地址: ${canvasT.localThumbnail}'});
      NiimbotTemplateData.TemplateData niimbotT =
          await TemplateTransformUtils.canvasJsonToNiimbotTemplate(canvasJson, needParseElements: false);
      if (Platform.isIOS) {
        niimbotT = niimbotT.copyWith(localThumbnail: canvasT.localThumbnail);
      }
      debugPrint("迁移:模板缩略图地址11 id: ${niimbotT.id} 地址: ${niimbotT.localThumbnail}");
      NiimbotLogTool.writeLogToFile({"MigrationManager": '迁移:模板缩略图地址11 id: ${niimbotT.id} 地址: ${niimbotT.localThumbnail}'});
      niimbotTemplates.add(niimbotT);
    }
    return niimbotTemplates;
  }

  @override
  Future<void> migrateItem(NiimbotTemplateData.TemplateData item) async {
    try {
      await TemplateDetailDBUtils.migrationTemplateData(item);

      ///迁移成功后删除原始表中对应的模版
      await NiimbotTemplateDbManager.dbDeleteTemplateDataById(item.id!);
    } catch (e) {
      LogUtil.d('MigrationManager模板${item.id}迁移失败 error=${e.toString()}');
      NiimbotLogTool.writeLogToFile({"MigrationManager Error": 'MigrationManager模板${item.id}迁移失败 error=${e.toString()}'});
    }
  }

  @override
  String get migrationKey => "template_v2";
}

class TemplateCloudMigrationHandler extends MigrationHandler<NiimbotTemplateData.TemplateData> {
  @override
  Future<int> get sourceCount async {
    int count = await NiimbotTemplateDbManager.dbQueryTemplateCount(isCloudTemplate: true);
    LogUtil.d('MigrationManager云模板总数${count}');
    NiimbotLogTool.writeLogToFile({"MigrationManager": 'MigrationManager云模板总数${count}'});
    return count;
  }

  @override
  Future<List<NiimbotTemplateData.TemplateData>> getBatch(int offset, int limit) async {
    List<TemplateData> canvasTemplates =
        await NiimbotTemplateDbManager.dbQueryTemplateDataWhere(limit: limit, offset: offset);
    NiimbotLogTool.writeLogToFile({"MigrationManager": '迁移:待迁移云模板: ${canvasTemplates.length}'});
    List<NiimbotTemplateData.TemplateData> niimbotTemplates = [];
    for (int i = 0; i < canvasTemplates.length; i++) {
      TemplateData canvasT = canvasTemplates[i];
      //处理userId为空的情况(ios)
      if ((canvasT.profile.extrain.userId ?? "").isEmpty) {
        canvasT.profile.extrain.userId = "0";
      }
      if (Platform.isIOS) {
        await canvasT.resetTemplateLocalPathFlutterIOS();
      }
      String canvasJson = jsonEncode(canvasT);
      NiimbotTemplateData.TemplateData niimbotT = await TemplateTransformUtils.canvasJsonToNiimbotTemplate(canvasJson);
      if (Platform.isIOS) {
        niimbotT = niimbotT.copyWith(localThumbnail: canvasT.localThumbnail);
      }
      debugPrint("迁移:模板缩略图地址id: ${niimbotT.id} 22: ${niimbotT.localThumbnail}");
      NiimbotLogTool.writeLogToFile({"MigrationManager": '迁移:模板缩略图地址id: ${niimbotT.id} 22: ${niimbotT.localThumbnail}'});
      niimbotTemplates.add(niimbotT);
    }
    return niimbotTemplates;
  }

  @override
  Future<void> migrateItem(NiimbotTemplateData.TemplateData item) async {
    try {
      await TemplateDetailDBUtils.migrationTemplateData(item);

      ///迁移成功后删除原始表中对应的模版
      await NiimbotTemplateDbManager.dbDeleteTemplateDataById(item.id!);
    } catch (e) {
      LogUtil.d('MigrationManager云模板${item.id}迁移失败 error=${e.toString()}');
      NiimbotLogTool.writeLogToFile({"MigrationManager Error": 'MigrationManager云模板${item.id}迁移失败 error=${e.toString()}'});
    }
  }

  @override
  String get migrationKey => "template_cloud_v2";
}

/// 文件夹迁移处理器
/// 从原生数据库迁移文件夹数据到新的Isar数据库
class FolderMigrationHandler extends MigrationHandler<FolderModel> {
  // 使用FolderManager来获取文件夹数据，保持封装性
  final FolderManager _folderManager = FolderManager();
  List<FolderModel>? _allFolders; // 缓存所有文件夹数据

  @override
  Future<int> get sourceCount async {
    int count = await _folderManager.getOldDbFolderCount();
    LogUtil.d('MigrationManager文件夹总数${count}');
    return count;
  }

  @override
  Future<List<FolderModel>> getBatch(int offset, int limit) async {
    // 由于文件夹数量较少，第一次调用时获取所有数据并缓存
    if (_allFolders == null) {
      _allFolders = await _folderManager.getAllOldDbFolders();
      LogUtil.d('MigrationManager获取所有文件夹数据: 总数=${_allFolders!.length}');
    }

    // 从缓存的数据中返回指定范围的数据
    int startIndex = offset;
    int endIndex = (offset + limit).clamp(0, _allFolders!.length);

    if (startIndex >= _allFolders!.length) {
      return [];
    }

    List<FolderModel> batchFolders = _allFolders!.sublist(startIndex, endIndex);
    LogUtil.d('MigrationManager获取文件夹批次数据: offset=$offset, limit=$limit, 实际获取=${batchFolders.length}');
    return batchFolders;
  }

  @override
  Future<void> migrateItem(FolderModel item) async {
    try {
      LogUtil.d('MigrationManager开始迁移文件夹: id=${item.id}, name=${item.name}, userId=${item.userId}');

      // 检查新数据库中是否已存在该文件夹
      List<FolderModel> existingFolders = await FolderDbUtils.queryFoldersByIdAndType(
        id: item.id.toString(),
      );

      // 如果新表中已经存在则不处理
      if (existingFolders.isNotEmpty) {
        LogUtil.d('MigrationManager文件夹${item.id}已存在，跳过迁移');
        return;
      }

      // 验证必要字段
      if (item.id == null) {
        LogUtil.d('MigrationManager文件夹ID为空，跳过迁移: $item');
        return;
      }

      // 直接插入到新数据库，避免嵌套事务问题
      // 注意：这里不能调用 FolderDbUtils.batchInsertFolders，因为它内部有事务
      // 而 MigrationEngine 已经在事务中调用了这个方法
      final isar = DBManagerUtil.instance.isar;
      final isarFolderModel = FolderDbUtils.folderModelToIsarModel(item);

      LogUtil.d(
          'MigrationManager转换后的Isar模型: id=${isarFolderModel.id}, name=${isarFolderModel.name}, userId=${isarFolderModel.userId}');

      await isar.folderModels.put(isarFolderModel);
      LogUtil.d('MigrationManager文件夹${item.id}插入新数据库成功');

      // 迁移成功后删除原始表中对应的文件夹
      // 使用FolderManager的封装方法删除原始数据
      bool deleteSuccess = await _folderManager.deleteOldDbFolder(item.id.toString());

      if (deleteSuccess) {
        LogUtil.d('MigrationManager文件夹${item.id}迁移成功');
      } else {
        LogUtil.d('MigrationManager文件夹${item.id}迁移成功，但删除原始数据失败');
      }
    } catch (e, stackTrace) {
      LogUtil.d('MigrationManager文件夹${item.id}迁移失败 error=${e.toString()}');
      LogUtil.d('MigrationManager文件夹迁移失败堆栈: $stackTrace');
    }
  }

  @override
  String get migrationKey => "folder_v2";
}
