import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:flutter_canvas_plugins_interface/plugin/font_panel_interface.dart';
import 'package:flutter_canvas_plugins_interface/user_center/canvas_user_center.dart';
import 'package:flutter_canvas_plugins_interface/utils/Logger.dart';
import 'package:intl/intl.dart' as intl;
import 'package:niimbot_cache_manager/niimbot_cache_manager.dart';
import 'package:niimbot_flutter_canvas/src/localization/localization_public.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_category.dart';
import 'package:niimbot_flutter_canvas/src/model/font/font_item.dart';
import 'package:shared_preferences/shared_preferences.dart';

Logger _logger = Logger("FontManager", on: kDebugMode);

/// 字体管理
class FontManager {
  static const String KEY_FONT_CATEGORY = "key_font_category";
  static const String KEY_ALL_FONT_LIST = "key_all_font_list1";
  static const String KEY_USER_FONT_LIST = "key_user_font_list";
  static const String LANGUAGE_EN = "en";
  static const String TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
  static const String FONT_DEFAULT_KEY = "fontDefault";
  static const String DEFAULT_FONT_CODE = "ZT001";
  static const String DEFAULT_FONT_CODE_ARIAL = "ZT063";
  static const String DEFAULT_FONT_CODE_JAPANESE = "ZT825";
  static const String DEFAULT_FONT_LOCAL_FILE_NAME = "ZT001.ttf";
  static const String DEFAULT_FONT_ARIAL_LOCAL_FILE_NAME = "ZT063.ttf";
  static const String DEFAULT_FONT_JAPANESE_LOCAL_FILE_NAME = "ZT825.ttf";

  /// 单例公开访问点
  factory FontManager() => sharedInstance();

  /// 静态私有成员，没有初始化
  static FontManager? _instance;

  /// 静态、同步、私有访问点
  static FontManager sharedInstance() {
    if (_instance == null) {
      // 单例初始化
      _instance = new FontManager._internal();
    }
    return _instance!;
  }

  FontManager._internal() {}

  SharedPreferences? sp;
  bool alreadyUpdateFontList = false;

  ///标记是否成功从服务器更新过字体分类
  bool alreadyUpdateFontCategory = false;

  ///标记是否成功从服务器更新过用户字体
  bool alreadyUpdateUserFontList = false;

  FontPanelInterface? fontPanelImpl = CanvasPluginManager().fontPanelImpl;

  /// 字体分类（不包括最近使用, 只包含接口数据的干净数据）
  List<FontCategory> commonCategories = [];

  /// 字体显示分类（包括最近使用）
  List<FontCategory> categories = [];

  /// 所有字体列表,接口返回的全的
  List<FontItem> fontList = [];

  /// 所有字体列表，组装接口返回外加默认字体
  List<FontItem> constructAllFontList = [];

  /// 最近使用字体列表
  List<FontItem> userfontList = [];

  Map<String, List<FontItem>> fontGroup = <String, List<FontItem>>{};

  updateDownloadTime(String fontCode) {
    final item = fontList.singleWhereOrNull((element) => element.code == fontCode);
    if (item != null) {
      item.downloadTime = DateTime.now().millisecondsSinceEpoch;
      setSpData(FontManager.KEY_ALL_FONT_LIST, json.encode(fontList));
    }
  }

  getConstructAllFontList() {
    List<FontItem?> fonts = [];
    fonts.addAll(fontList);
    fonts.add(getDefaultAppFont());
    fonts.add(getDefaultArialFont());
    fonts.add(getDefaultJapaneseFont());
    return fonts;
  }

  //处理分类数据
  processClassifyData() {
    categories.clear();
    categories.addAll(commonCategories);
    //查找当前语言所在的分类，如果没有匹配到的话，则默认英文分类----并插入到头部，
    // int index = categories.indexWhere((element) => element.languageCode == fontPanelImpl?.getCurrentLanguageCode());
    // if (index < 0) {
    //   index = categories.indexWhere((element) => element.languageCode == LANGUAGE_EN);
    // }
    // if (index > 0) {
    //   categories.insert(0, categories.removeAt(index));
    // }
  }

  List<FontCategory> getDisplayCategories() {
    List<FontCategory> result = [];

    //集合头部插入最近使用分类
    FontCategory recentUseCategory = FontCategory();
    recentUseCategory.id = "-1";
    recentUseCategory.name = intlanguage('app01276', '最近使用');
    result.add(recentUseCategory);
    if (categories.isNotEmpty) {
      result.addAll(categories);
    }
    return result;
  }

  void setSpData(String key, String value) async {
    // 实例化
    if (sp == null) {
      sp = await SharedPreferences.getInstance();
    }
    await sp!.setString(key, value);
  }

  Future<String?> getSpData(String key) async {
    // 实例化
    if (sp == null) {
      sp = await SharedPreferences.getInstance();
    }
    final str = sp!.getString(key);
    return str;
  }

  void updateUserFont(FontItem item) {
    if (userfontList.contains(item)) {
      int index = userfontList.indexOf(item);
      FontItem font = userfontList[index];
      font.usageDatetime = intl.DateFormat(TIME_FORMAT).format(DateTime.now());
      userfontList.insert(0, userfontList.removeAt(index));
    } else {
      item.usageDatetime = intl.DateFormat(TIME_FORMAT).format(DateTime.now());
      userfontList.insert(0, item);
    }
    // 更新本地使用缓存
    NiimbotCacheManager()
        .updateUserFont(userID: CanvasUserCenter().userId.toString(), value: json.encode(userfontList));
    //上报最近使用,默认字体不上报
    if (item.code == DEFAULT_FONT_CODE ||
        item.code == DEFAULT_FONT_CODE_ARIAL ||
        item.code == DEFAULT_FONT_CODE_JAPANESE) {
      return;
    }
    List<dynamic> list = [];
    list.add(item.toJson());
    if ((fontPanelImpl?.isNetReachable() ?? false) && CanvasUserCenter().userId != 0) {
      fontPanelImpl!.requestRecordUserFont(list, () => null, (p0, p1) {
        _logger.log("$p1");
        return null;
      });
    }
  }

  bool isDefaultFont(String fontCode) {
    if (fontCode == DEFAULT_FONT_CODE ||
        fontCode == DEFAULT_FONT_CODE_ARIAL ||
        fontCode == DEFAULT_FONT_CODE_JAPANESE) {
      return true;
    }
    return false;
  }

  Future<List<FontCategory>> getFontClassify({bool isFromLocal = false}) async {
    //判断是否有网
    if ((fontPanelImpl?.isNetReachable() ?? false) && !isFromLocal) {
      List<FontCategory> result = await requestFontClassifyFromNet();
      if (result == null) {
        List<FontCategory> localResult = await loadLocalFontClassify();
        return localResult;
      } else {
        return result;
      }
    } else {
      // 从本地取缓存数据
      List<FontCategory> localResult = await loadLocalFontClassify();
      return localResult;
    }
  }

  Future<List<FontItem>> getAllFontList() async {
    // 判断是否有网
    if ((fontPanelImpl?.isNetReachable() ?? false)) {
      return requestFontListFromNet();
    } else {
      // 从本地取缓存数据
      return loadLocalAllFontList();
    }
  }

  Future<List<FontItem>> getUserFontList() async {
    //有网并且登录情况下服务端获取用户最近使用
    if ((fontPanelImpl?.isNetReachable() ?? false) && CanvasUserCenter().userId != 0) {
      List<FontItem> result = await requestUserFontListFromNet();
      if (result == null) {
        List<FontItem> localResult = await loadLocalUserFontList();
        return localResult;
      } else {
        return result;
      }
    } else {
      // 从本地取缓存数据
      List<FontItem> localResult = await loadLocalUserFontList();
      return localResult;
    }
  }

  void assembleFontCategory(List<FontCategory> fontCategories) {
    commonCategories.clear();
    categories.clear();
    commonCategories.addAll(fontCategories);
    processClassifyData();
  }

  void assembleFontList(List<FontItem> fontItems) {
    fontList.clear();
    fontGroup.clear();
    fontItems.forEach((item) {
      fontList.add(item);
      if ((item.classifyId?.isNotEmpty ?? false) && item.status != 0) {
        List<FontItem> itemList = fontGroup.putIfAbsent(item.classifyId!, () => <FontItem>[]);
        itemList.add(item);
      }
    });
  }

  void assembleUserFontList(List<FontItem> fontItems) {
    userfontList.clear();
    userfontList.addAll(fontItems);

    // 更新每个用户字体的 showSystemName
    for (var userFont in userfontList) {
      final match = fontList.firstWhereOrNull((font) => font.code == userFont.code);
      if (match != null) {
        userFont.showSystemName = match.showSystemName;
      }
    }

    //添加默认字体
    if (userfontList.contains(getDefaultAppFont())) {
      userfontList.removeWhere((element) => element.code == DEFAULT_FONT_CODE);
      userfontList.add(getDefaultAppFont());
    } else {
      userfontList.add(getDefaultAppFont());
    }
    if (userfontList.contains(getDefaultArialFont())) {
      userfontList.removeWhere((element) => element.code == DEFAULT_FONT_CODE_ARIAL);
      userfontList.add(getDefaultArialFont());
    } else {
      userfontList.add(getDefaultArialFont());
    }
    if (userfontList.contains(getDefaultJapaneseFont())) {
      userfontList.removeWhere((element) => element.code == DEFAULT_FONT_CODE_JAPANESE);
      userfontList.add(getDefaultJapaneseFont());
    } else {
      userfontList.add(getDefaultJapaneseFont());
    }

    //按时间倒序
    userfontList.sort((a, b) {
      int aTime = ((a.usageDatetime?.isEmpty ?? true))
          ? 0
          : intl.DateFormat(TIME_FORMAT).parse(a.usageDatetime!).millisecondsSinceEpoch;
      int bTime = ((b.usageDatetime?.isEmpty ?? true))
          ? 0
          : intl.DateFormat(TIME_FORMAT).parse(b.usageDatetime!).millisecondsSinceEpoch;
      return bTime - aTime;
    });
    fontGroup["-1"] = userfontList;
  }

  FontItem getDefaultArialFont() {
    FontItem item = FontItem(
        id: 1000,
        code: DEFAULT_FONT_CODE_ARIAL,
        name: intlanguage("app01417", "Markazi Text"),
        path: DEFAULT_FONT_ARIAL_LOCAL_FILE_NAME);
    return item;
  }

  FontItem getDefaultJapaneseFont() {
    FontItem item = FontItem(
        id: 1008,
        code: DEFAULT_FONT_CODE_JAPANESE,
        name: intlanguage("app100001929", "日语默认字体"),
        path: DEFAULT_FONT_JAPANESE_LOCAL_FILE_NAME);
    return item;
  }

  FontItem getDefaultAppFont() {
    FontItem item = FontItem(
        id: -1, code: DEFAULT_FONT_CODE, name: intlanguage("app01533", "鸿蒙"), path: DEFAULT_FONT_LOCAL_FILE_NAME);
    return item;
  }

  bool isFontVip(String fontCode) {
    if (fontCode == DEFAULT_FONT_CODE ||
        fontCode == DEFAULT_FONT_CODE_ARIAL ||
        fontCode == DEFAULT_FONT_CODE_JAPANESE) {
      return false;
    }
    if (fontList.isNotEmpty) {
      final isVip = fontList.firstWhereOrNull((element) => element.code == fontCode)?.isVip;
      return (isVip != null) && (isVip == true);
    } else {
      return false;
    }
  }
}

/// 本地缓存字体管理，无网络情况下数据装配
extension LocalFontManager on FontManager {
  Future<List<FontCategory>> loadLocalFontClassify() async {
    // 从缓存管理器中取出本地字体分类数据
    return NiimbotCacheManager()
        .loadLocalFontCategory<List<FontCategory>>(
            languageCode: currentLanguageCode(),
            transformer: (dynamic rawValue) {
              List<FontCategory> result = [];
              if (rawValue is List<dynamic>) {
                for (var e in rawValue) {
                  result.add(FontCategory.fromJson(e));
                }
              }
              return result;
            })
        .then((fontCategories) {
      if (fontCategories != null) {
        assembleFontCategory(fontCategories);
      }
      return fontCategories ?? [];
    });
  }

  Future<List<FontItem>> loadLocalAllFontList() async {
    // 从缓存管理器中取出本地字体数据
    return NiimbotCacheManager().loadLocalAllFontList<List<FontItem>>(transformer: (dynamic rawValue) {
      List<FontItem> result = [];
      if (rawValue is List<dynamic>) {
        for (var e in rawValue) {
          result.add(FontItem.fromJson(e));
        }
      }
      return result;
    }).then((fontItems) {
      if (fontItems != null) {
        assembleFontList(fontItems);
        alreadyUpdateFontList = false;
      }
      return fontItems ?? [];
    });
  }

  Future<List<FontItem>> loadLocalUserFontList() async {
    // 从缓存管理器中取出用户本地字体数据
    return NiimbotCacheManager()
        .loadLocalUserFontList<List<FontItem>>(
            userID: CanvasUserCenter().userId.toString(),
            transformer: (dynamic rawValue) {
              List<FontItem> result = [];
              if (rawValue is List<dynamic>) {
                for (var e in rawValue) {
                  result.add(FontItem.fromJson(e));
                }
              }
              return result;
            })
        .then((fontItems) {
      if (fontItems != null) {
        assembleUserFontList(fontItems);
        alreadyUpdateFontList = false;
      }
      return fontItems ?? [];
    });
  }

  clearFontCache() {
    alreadyUpdateFontCategory = false;
    alreadyUpdateFontList = false;
    alreadyUpdateUserFontList = false;
    commonCategories.clear();
    categories.clear();
    fontList.clear();
    constructAllFontList.clear();
    userfontList.clear();
    fontGroup.clear();
    NiimbotCacheManager().clearFontCache();
  }

  clearFontListCache() {
    commonCategories.clear();
    categories.clear();
    fontList.clear();
    constructAllFontList.clear();
    userfontList.clear();
    fontGroup.clear();
    NiimbotCacheManager().clearFontListCache();
  }

  clearFontCategoryCache() {
    commonCategories.clear();
    categories.clear();
    fontList.clear();
    constructAllFontList.clear();
    userfontList.clear();
    fontGroup.clear();
    NiimbotCacheManager().clearFontCategoryCache();
  }
}

/// 网络字体管理，包括获取字体分类以及所有字体列表
extension NetWorkFontManager on FontManager {
  /// 从服务端获取字体分类
  Future<List<FontCategory>> requestFontClassifyFromNet() async {
    // 从缓存管理器请求字体分类数据
    return NiimbotCacheManager()
        .requestFontCategoryFromNet<List<FontCategory>>(
            languageCode: currentLanguageCode(),
            transformer: (dynamic rawValue) {
              List<FontCategory> result = [];
              if (rawValue is List<dynamic>) {
                for (var e in rawValue) {
                  result.add(FontCategory.fromJson(e));
                }
              }
              return result;
            })
        .then((fontCategories) {
      if (fontCategories != null) {
        alreadyUpdateFontCategory = true;
        assembleFontCategory(fontCategories);
      }
      return fontCategories ?? [];
    });
  }

  String currentLanguageCode() {
    String language = "en";
    if (fontPanelImpl?.getCurrentLanguageCode() != null) {
      language = fontPanelImpl!.getCurrentLanguageCode();
      if (language == "zh-cn") {
        language = "zh_CN";
      } else if (language == "zh-cn-t") {
        language = "zh_TW";
      }
    }
    return language;
  }

  Future<List<FontItem>> requestFontListFromNet({bool needVip = true, bool justVip = false}) async {
    Map<String, dynamic> params = {};
    params["platformCodes"] = "CP001Mobile";
    // 入参：status：0：表示下架； 1或者不传该字段：上架 ;现在要支持查询上架 + 下架, 就传2
    params["status"] = "2";
    if (justVip) {
      params["queryVersion"] = "2";
      params["isVip"] = true;
    } else {
      if (needVip) {
        params["queryVersion"] = "2";
      }
    }

    // 从缓存管理器中请求字体数据
    return NiimbotCacheManager()
        .requestFontListFromNet<List<FontItem>>(
            params: params,
            transformer: (dynamic rawValue) {
              List<FontItem> result = [];
              if (rawValue is List<dynamic>) {
                for (var e in rawValue) {
                  result.add(FontItem.fromJson(e));
                }
              }
              return result;
            },
            updateTransformer: ({localValue, newValue}) {
              if (localValue == null || localValue.isEmpty) {
                return newValue;
              } else {
                newValue?.forEach((element) {
                  element.downloadTime = localValue.firstWhere((font) => font.code == element.code, orElse: () {
                        return element;
                      }).downloadTime ??
                      0;
                });
                return newValue;
              }
            })
        .then((fontItems) {
      if (fontItems != null) {
        alreadyUpdateFontList = true;
        assembleFontList(fontItems);
      } else {
        loadLocalAllFontList();
      }
      return fontItems ?? [];
    });
  }

  /// 从服务端获取最近使用字体列表
  Future<List<FontItem>> requestUserFontListFromNet() async {
    return NiimbotCacheManager()
        .requestUserFontListFromNet<List<FontItem>>(
            userID: CanvasUserCenter().userId.toString(),
            transformer: (dynamic rawValue) {
              List<FontItem> result = [];
              if (rawValue is List<dynamic>) {
                for (var e in rawValue) {
                  result.add(FontItem.fromJson(e));
                }
              }
              return result;
            })
        .then((fontItems) {
      if (fontItems != null) {
        alreadyUpdateUserFontList = true;
        assembleUserFontList(fontItems);
      }
      return fontItems ?? [];
    });
  }

  Future<bool> checkFontList() async {
    if (fontList.isNotEmpty) {
      return Future.value(true);
    }
    List<FontItem> result = await requestFontListFromNet();
    if (result.isNotEmpty) {
      return true;
    } else {
      return false;
    }
  }
}
