import 'dart:convert';
import 'dart:io';

import 'package:text/application.dart';
import 'package:text/pages/my_template/model/owner_Profile_model.dart';

import '../../../utils/niimbot_db_model.dart';

/// name : 文件夹名称
/// id : 文件夹ID
/// gmtCreated : 文件夹更新时间
/// gmtModified : 文件夹创建时间
/// userId : 用户ID
/// /// isAdd : 是否新增文件夹类型
/// /// isSelected : 是否选中
/// /// isEnableEdit : 是否可编辑
/// /// isDefault :是否默认文件夹

class FolderModel extends NiimbotDbModel {
  FolderModel(
      {String? name,
      num? id,
      String? gmtCreated,
      String? gmtModified,
      num? userId,
      bool? isAdd,
      bool? isSelected,
      bool? isEnableEdit,
      bool? isDefault,
      int? templateSize,
      String? sharedAt,
      num? templateCount,
      int? type,
      bool? vip,
      OwnerProfileModel? ownerProfile,
      List<String>? sharedUserIds,
      Map<String, dynamic>? shared}) {
    _name = name;
    _id = id;
    _gmtCreated = gmtCreated;
    _gmtModified = gmtModified;
    _userId = userId;
    _isAdd = isAdd;
    _isSelected = isSelected;
    _isEnableEdit = isEnableEdit;
    _isDefault = isDefault;
    _templateSize = templateSize;
    _sharedAt = sharedAt;
    _templateCount = templateCount;
    _type = type; //模板类型：0：默认文件夹，1：分享给我，2：我分享的
    _vip = vip;
    _ownerProfile = ownerProfile;
    _sharedUserIds = sharedUserIds;
    _shared = shared;
  }

  FolderModel.fromJson(dynamic json) : super.fromJson(json) {
    _name = json['name'];
    _id = (json['id'] is String) ? num.parse(json['id']) : json['id'];
    _gmtCreated = json['gmtCreated'] ?? "";
    _gmtModified = json['gmtModified'] ?? "";
    _userId = (json['userId'] is String) ? num.parse(json['userId']) : json['userId'];

    _isAdd = (json['isAdd'] is String) ? json['isAdd'] == 1 : json['isAdd'];
    _isSelected = (json['isSelected'] is String) ? json['isSelected'] == 1 : json['isSelected'];
    _isEnableEdit = (json['isEnableEdit'] is String) ? json['isEnableEdit'] == 1 : json['isEnableEdit'];
    _isDefault = (json['isDefault'] is String) ? json['isDefault'] == 1 : json['isDefault'];
    _sharedAt = json['sharedAt'] ?? "";
    _templateCount = json['templateCount'] ?? 0;
    _type = json['type'] ?? 0;
    _vip = json['vip'] ?? false;
    _ownerProfile = json['ownerProfile'] == null ? null : OwnerProfileModel.fromJson(json['ownerProfile']);
    _sharedUserIds = json['sharedUserIds'] == null ? [] : json['sharedUserIds'];
    _shared = json['shared'] == null ? null : json['shared'];
  }

  FolderModel.fromDBJson(dynamic json) : super.fromJson(json) {
    if (Platform.isIOS) {
      _name = json['name'];
      _id = (json['xyid'] is String) ? num.parse(json['xyid']) : json['xyid'];
      _gmtCreated = json['gmtCreated'] ?? "";
      _gmtModified = json['gmtModified'] ?? "";

      // 修复 iOS user_id 字段处理
      var userIdValue = json['user_id'];
      if (userIdValue == null || userIdValue == "null" || userIdValue == "") {
        _userId = Application.user?.userId ?? 0; // 使用当前用户ID作为默认值
      } else if (userIdValue is String) {
        try {
          _userId = num.parse(userIdValue);
        } catch (e) {
          _userId = Application.user?.userId ?? 0;
        }
      } else {
        _userId = userIdValue;
      }

      _isAdd = (json['isAdd'] is String) ? json['isAdd'] == 1 : json['isAdd'];
      _isSelected = (json['isSelected'] is String) ? json['isSelected'] == 1 : json['isSelected'];
      _isEnableEdit = (json['isEnableEdit'] is String) ? json['isEnableEdit'] == 1 : json['isEnableEdit'];
      _isDefault = (json['isDefault'] is String) ? json['isDefault'] == 1 : json['isDefault'];
      _sharedAt = json['sharedAt'] ?? "";
      _type = json['type'] ?? 0;
      _vip = json['vip'] == null ? false : ((json['vip'] is int) ? json['vip'] == 1 : json['vip']);
      _ownerProfile =
          json['ownerProfile'] == null ? null : OwnerProfileModel.fromJson(jsonDecode(json['ownerProfile']));
      _templateCount = json['templateCount'] == null
          ? 0
          : (json['templateCount'] is String)
              ? int.parse(json['templateCount'])
              : json['templateCount'];
      _sharedUserIds = json['sharedUserIds'] == null ? [] : json['sharedUserIds'].split(",");
      _shared = (json['shared'] == null ? null : jsonDecode(json['shared']));
    } else {
      _name = json['NAME'];
      _id = (json['ID'] is String) ? num.parse(json['ID']) : json['ID'];
      _gmtCreated = json['GMT_CREATED'] ?? "";
      _gmtModified = json['GMT_MODIFIED'] ?? "";

      // 修复 Android USER_ID 字段处理，与iOS保持一致
      var userIdValue = json['USER_ID'];
      if (userIdValue == null || userIdValue == "null" || userIdValue == "") {
        _userId = Application.user?.userId ?? 0; // 使用当前用户ID作为默认值
      } else if (userIdValue is String) {
        try {
          _userId = num.parse(userIdValue);
        } catch (e) {
          _userId = Application.user?.userId ?? 0;
        }
      } else {
        _userId = userIdValue;
      }

      _isAdd = (json['IS_ADD'] is String) ? json['IS_ADD'] == "1" : (json['IS_ADD'] == 1);
      _isSelected = (json['IS_SELECTED'] is String) ? json['IS_SELECTED'] == "1" : (json['IS_SELECTED'] == 1);
      _isEnableEdit =
          (json['IS_ENABLE_EDIT'] is String) ? json['IS_ENABLE_EDIT'] == "1" : (json['IS_ENABLE_EDIT'] == 1);
      _isDefault = (json['IS_DEFAULT'] is String) ? json['IS_DEFAULT'] == "1" : (json['IS_DEFAULT'] == 1);
      _sharedAt = json['SHARED_AT'] ?? "";
      _type = json['TYPE'] ?? 0;
      _vip = json['VIP'] == null ? false : ((json['VIP'] is int) ? json['VIP'] == 1 : json['VIP']);
      _ownerProfile = json['OWNER_PROFILE'] == null || json['OWNER_PROFILE'] == ""
          ? null
          : OwnerProfileModel.fromJson(jsonDecode(json['OWNER_PROFILE']));
      _templateCount = json['TEMPLATE_COUNT'] == null
          ? 0
          : (json['TEMPLATE_COUNT'] is String)
              ? int.parse(json['TEMPLATE_COUNT'])
              : json['TEMPLATE_COUNT'];
      _sharedUserIds = json['SHARED_USER_IDS'] == null ? [] : json['SHARED_USER_IDS'].toString().split(",");
      _shared = (json['SHARED'] == null ? null : jsonDecode(json['SHARED']));
    }
  }

  String? _name;
  num? _id;
  String? _gmtCreated;
  String? _gmtModified;
  num? _userId;
  bool? _isAdd;
  bool? _isSelected;
  bool? _isEnableEdit;
  bool? _isDefault;
  int? _templateSize;
  String? _sharedAt;
  num? _templateCount;
  int? _type;
  bool? _vip;
  OwnerProfileModel? _ownerProfile;
  List<String>? _sharedUserIds;
  Map<String, dynamic>? _shared;

  FolderModel copyWith(
          {String? name,
          num? id,
          String? gmtCreated,
          String? gmtModified,
          num? userId,
          bool? isAdd,
          bool? isSelected,
          bool? isEnableEdit,
          bool? isDefault,
          int? templateSize,
          String? sharedAt,
          num? templateCount,
          int? type,
          bool? vip,
          OwnerProfileModel? ownerProfile,
          List<String>? sharedUserIds,
          Map<String, dynamic>? shared}) =>
      FolderModel(
        name: name ?? _name,
        id: id ?? _id,
        gmtCreated: gmtCreated ?? _gmtCreated,
        gmtModified: gmtModified ?? _gmtModified,
        userId: userId ?? _userId,
        isAdd: isAdd ?? _isAdd,
        isSelected: isSelected ?? _isSelected,
        isEnableEdit: isEnableEdit ?? _isEnableEdit,
        isDefault: isDefault ?? _isDefault,
        templateSize: templateSize ?? _templateSize,
        sharedAt: sharedAt ?? _sharedAt,
        templateCount: templateCount ?? _templateCount,
        type: type ?? _type,
        vip: vip ?? _vip,
        ownerProfile: ownerProfile ?? _ownerProfile,
        sharedUserIds: sharedUserIds ?? _sharedUserIds,
        shared: shared ?? _shared,
      );

  String? get name => _name;

  num? get id => _id;

  String? get gmtCreated => _gmtCreated;

  String? get gmtModified => _gmtModified;

  num? get userId => _userId;

  bool? get isAdd => _isAdd == null ? false : _isAdd;

  bool? get isSelected => _isSelected == null ? false : _isSelected;

  bool? get isEnableEdit => _isEnableEdit == null ? false : _isEnableEdit;

  bool? get isDefault => _isDefault == null ? false : _isDefault;

  int? get templateSize => _templateSize == null ? 0 : _templateSize;

  String? get sharedAt => _sharedAt;

  num? get templateCount => _templateCount;

  int? get type => _type == null ? 0 : _type;

  bool? get vip => _vip == null ? false : _vip;

  OwnerProfileModel? get ownerProfile => _ownerProfile;

  List<String>? get sharedUserIds => _sharedUserIds ?? [];

  Map<String, dynamic>? get shared => _shared;

  set templateSize(int? value) {
    _templateSize = value;
  }

  set sharedUserIds(List<String>? value) {
    _sharedUserIds = value;
  }

  set type(int? value) {
    type = value;
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['name'] = _name;
    map['id'] = _id;
    map['gmtCreated'] = _gmtCreated;
    map['gmtModified'] = _gmtModified;
    map['userId'] = _userId;
    map['isAdd'] = _isAdd;
    map['isSelected'] = _isSelected;
    map['isEnableEdit'] = _isEnableEdit;
    map['isDefault'] = _isDefault;
    map['templateSize'] = _templateSize;
    map['sharedAt'] = _sharedAt;
    map['templateCount'] = _templateCount;
    map['type'] = _type;
    map['vip'] = _vip;
    map['ownerProfile'] = _ownerProfile?.toJson();
    map['sharedUserIds'] = _sharedUserIds;
    map['shared'] = _shared;
    return map;
  }

  Map<String, dynamic> toDBJson() {
    if (Platform.isIOS) {
      final map = <String, dynamic>{};
      map['name'] = _name;
      map['xyid'] = _id.toString();
      map['gmtCreated'] = _gmtCreated ?? "";
      map['gmtModified'] = _gmtModified ?? "";
      map['user_id'] = _userId.toString();
      map['isAdd'] = (_isAdd != null && _isAdd!) ? "1" : "0";
      map['isSelected'] = (_isSelected != null && _isSelected!) ? "1" : "0";
      map['isEdit'] = (_isEnableEdit != null && _isEnableEdit!) ? "1" : "0";
      map['isDefault'] = (_isDefault != null && _isDefault!) ? "1" : "0";
      map['sharedAt'] = _sharedAt ?? "";
      map['templateCount'] = _templateCount ?? 0;
      map['type'] = _type ?? 0;
      map['vip'] = _vip ?? false;
      map['ownerProfile'] = _ownerProfile == null ? null : jsonEncode(_ownerProfile?.toJson());
      map['sharedUserIds'] = _sharedUserIds == null ? [] : sharedUserIds?.join(",");
      map['shared'] = _shared == null ? null : jsonEncode(_shared);
      return map;
    } else {
      final map = <String, dynamic>{};
      map['NAME'] = _name;
      map['ID'] = _id;
      map['GMT_CREATED'] = _gmtCreated ?? "";
      map['GMT_MODIFIED'] = _gmtModified ?? "";
      map['USER_ID'] = _userId;
      map['IS_ADD'] = (_isAdd != null && _isAdd!) ? 1 : 0;
      map['IS_SELECTED'] = (_isSelected != null && _isSelected!) ? 1 : 0;
      map['IS_ENABLE_EDIT'] = (_isEnableEdit != null && _isEnableEdit!) ? 1 : 0;
      map['IS_DEFAULT'] = (_isDefault != null && _isDefault!) ? 1 : 0;
      map['SHARED_AT'] = _sharedAt ?? "";
      map['TEMPLATE_COUNT'] = _templateCount ?? 0;
      map['TYPE'] = _type ?? 0;
      map['VIP'] = (_vip != null && _vip!) ? 1 : 0;
      map['OWNER_PROFILE'] = _ownerProfile == null ? "" : jsonEncode(_ownerProfile?.toJson());
      map['SHARED_USER_IDS'] = sharedUserIds?.join(",");
      map['SHARED'] = _shared == null ? null : jsonEncode(_shared);
      return map;
    }
  }
}
