//
//  JCBluetoothConnectAlert+DeviceInfo.m
//  Runner
//
//  Created by Chance on 2024/8/23.
//

#import "JCBluetoothConnectAlert+DeviceInfo.h"
#import "YLButton.h"
#import "JCPrinterProperty.h"
#import "JCBluetoothConnectAlert+DeviceInfoLogic.h"
#import "JCShopProductVC.h"
#import "JCPipeLineCalibrationView.h"

typedef NS_ENUM(NSUInteger, RFIDProgressStatus) {
    RFIDProgressLow,
    RFIDProgressHigh,
};

#import <UIKit/UIKit.h>

@interface CustomProgressBar : UIView

@property (nonatomic, assign) CGFloat progress; // 进度（0.0 - 1.0）
@property (nonatomic, strong) UIColor *progressTintColor;
@property (nonatomic, strong) UIColor *trackTintColor;

- (void)setProgress:(CGFloat)progress animated:(BOOL)animated;

@end


@interface CustomProgressBar ()

@property (nonatomic, strong) UIView *progressView;

@end

@implementation CustomProgressBar

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        // 初始化背景颜色
        self.trackTintColor = [UIColor lightGrayColor];
        self.progressTintColor = [UIColor blueColor];
        
        // 背景
        self.backgroundColor = self.trackTintColor;
        self.layer.cornerRadius = frame.size.height / 2;
        self.clipsToBounds = YES;
        
        // 进度条视图
        self.progressView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, frame.size.height)];
        self.progressView.backgroundColor = self.progressTintColor;
        self.progressView.layer.cornerRadius = frame.size.height / 2;
        self.progressView.clipsToBounds = YES;
        [self addSubview:self.progressView];
    }
    return self;
}

- (void)setTrackTintColor:(UIColor *)trackTintColor {
    // 背景
    _trackTintColor = trackTintColor;
    self.backgroundColor = self.trackTintColor;
}

- (void)setProgressTintColor:(UIColor *)progressTintColor {
    _progressTintColor = progressTintColor;
    self.progressView.backgroundColor = progressTintColor;
}

- (void)setProgress:(CGFloat)progress {
    [self setProgress:progress animated:NO];
}

- (void)setProgress:(CGFloat)progress animated:(BOOL)animated {
    // 限制进度范围在 0 到 1 之间
    progress = MIN(MAX(progress, 0.0), 1.0);
    _progress = progress;
    
    // 更新进度条宽度
    CGFloat progressWidth = self.frame.size.width * progress;
    
    if (animated && !self.isRTL) {
        [UIView animateWithDuration:0.35 delay:0.0 options:UIViewAnimationOptionCurveEaseInOut animations:^{
            self.progressView.frame = CGRectMake(0, 0, progressWidth, self.frame.size.height);
            self.progressView.leading = 0;
        } completion:nil];
    } else {
        self.progressView.frame = CGRectMake(0, 0, progressWidth, self.frame.size.height);
        self.progressView.leading = 0;
    }
}

@end


@interface JCRFIDStatusView : UIView

@property (nonatomic, strong) UIView *backgroundView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) CustomProgressBar *progressView;
@property (nonatomic, strong) UIView *cartButton;
@property (nonatomic, strong) UILabel *rfidStatusLabel;
@property (nonatomic, copy) void (^actionBlock)(RFIDShowStausType rfidShowType);
@property (nonatomic, assign) RFIDShowStausType rfidShowType;

@end

@implementation JCRFIDStatusView

- (instancetype)initWithFrame:(CGRect)frame withType:(RFIDShowStausType)rfidShowType {
    self = [super initWithFrame:frame];
    if (self) {
        self.rfidShowType = rfidShowType;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self addSubview:self.backgroundView];
    [self.backgroundView addSubview:self.titleLabel];
    [self.backgroundView addSubview:self.progressView];
    [self.backgroundView addSubview:self.cartButton];
    [self.backgroundView addSubview:self.rfidStatusLabel];
    [self.cartButton setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self);
    }];

    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.backgroundView).offset(16);
        make.top.equalTo(self.backgroundView).offset(12);
    }];

    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(12);
        make.trailing.equalTo(self.cartButton.mas_leading).offset(-16);
        make.height.mas_equalTo(5);
    }];

    [self.cartButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.backgroundView).inset(16);
        make.bottom.equalTo(self.backgroundView).inset(9);
    }];
    
    [self.rfidStatusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(5);
    }];
}

- (void)refreshProgressView:(NSInteger)surplusFloat {
    if (surplusFloat == 0) {
        [self.rfidStatusLabel setHidden:NO];
        [self.progressView setHidden:YES];
        // 商城按钮状态
        self.cartButton.hidden = [[XYCenter sharedInstance] isShowShop] ? NO : YES;
        if (!self.cartButton.isHidden) {
            // 埋点使用
            JC_TrackWithparms(@"show",self.rfidShowType == RFIDShowTypeLabel ? @"127_345_322" : @"127_346_323", @{});
        }
        return;
    } else if (surplusFloat <= 2) {
        // 红色
        self.progressView.progressTintColor = HEX_RGB(0xFB4B42);
    } else if (surplusFloat <= 4) {
        // 橙色
        self.progressView.progressTintColor = HEX_RGB(0xFFAF37);
    } else if (surplusFloat <= 10) {
        // 绿色
        self.progressView.progressTintColor = HEX_RGB(0x34C759);
    }
    [self.rfidStatusLabel setHidden:YES];
    [self.progressView setHidden:NO];
    [self.progressView setProgress:surplusFloat / 10.0 animated:YES];
    
    // 商城按钮状态
    self.cartButton.hidden = [[XYCenter sharedInstance] isShowShop] ? NO : YES;
    if (!self.cartButton.isHidden) {
        // 埋点使用
        JC_TrackWithparms(@"show",self.rfidShowType == RFIDShowTypeLabel ? @"127_345_322" : @"127_346_323", @{});
    }
    
#if DEBUG
    switch (self.rfidShowType) {
        case RFIDShowTypeLabel: {
            self.titleLabel.text = [NSString stringWithFormat:@"%@(档位:%ld)",XY_LANGUAGE_TITLE_NAMED(@"app100001743", @"标签纸余量"), surplusFloat];
        }
            break;
        case RFIDShowTypeCarbon: {
            self.titleLabel.text = [NSString stringWithFormat:@"%@(档位:%ld)",XY_LANGUAGE_TITLE_NAMED(@"app01222", @"碳带余量"), surplusFloat];
        }
        default:
            break;
    }
#endif
}

- (void)refreshProgressViewLengthStatus:(RFIDProgressStatus)status {
    [self.progressView setNeedsLayout];
    [self.progressView layoutIfNeeded];
    // 重新刷新UI
    [self.progressView setProgress:self.progressView.progress animated:YES];
}

- (void)toShopMallAction:(UITapGestureRecognizer *)tapGesture {
    if (self.actionBlock) {
        // 埋点使用
        JC_TrackWithparms(@"click",self.rfidShowType == RFIDShowTypeLabel ? @"127_345_322" : @"127_346_323", @{
            @"jumpsource": self.rfidShowType == RFIDShowTypeLabel ? @"label_paper_remaining" : @"ribbon_remaining"
        });
        self.actionBlock(self.rfidShowType);
    }
}

- (void)setHidden:(BOOL)hidden {
    [super setHidden:hidden];
    if (!hidden) {
        // 埋点使用
        JC_TrackWithparms(@"show",self.rfidShowType == RFIDShowTypeLabel ? @"127_345" : @"127_346", @{});
    }
}

// MARK: -- Lazy Load --

- (UIView *)backgroundView {
    if (!_backgroundView) {
        _backgroundView = [[UIView alloc] initWithFrame:CGRectZero];
        _backgroundView.backgroundColor = [UIColor whiteColor];
        _backgroundView.layer.cornerRadius = 12;
    }
    return _backgroundView;
}

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001743", @"标签纸余量");
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(13);
    }
    return _titleLabel;
}

- (CustomProgressBar *)progressView {
    if (!_progressView) {
        _progressView = [[CustomProgressBar alloc] initWithFrame:CGRectMake(0, 0, 45, 5)];
        _progressView.progressTintColor = HEX_RGB(0x34C759);
        _progressView.trackTintColor = HEX_RGBA(0x3C3C43, 0.15);
    }
    return _progressView;
}

- (UIView *)cartButton {
    if (!_cartButton) {
        _cartButton = [[UIView alloc] initWithFrame:CGRectZero];
        _cartButton.backgroundColor = [UIColor colorWithWhite:0.95 alpha:1.0];
        _cartButton.layer.cornerRadius = 12; // 圆形按钮
        
        UIImageView *imageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"shop_cart"]];
        [_cartButton addSubview:imageView];
        [imageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsMake(5, 10, 5, 10));
        }];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(toShopMallAction:)];
        [_cartButton addGestureRecognizer:tapGesture];
    }
    return _cartButton;
}

- (UILabel *)rfidStatusLabel {
    if (!_rfidStatusLabel) {
        _rfidStatusLabel = [[UILabel alloc] init];
        _rfidStatusLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app100001749", @"已耗尽");
        _rfidStatusLabel.textColor = HEX_RGB(0xFB4B42);
        _rfidStatusLabel.font = MY_FONT_Regular(12);
    }
    return _rfidStatusLabel;
}

@end

@interface JCDeviceInfoBaseCell : UITableViewCell

@property (nonatomic, strong) JCDeviceInfoModel *model;

@property (nonatomic, nullable, copy) void (^actionBlock)(JCDeviceInfoModel *model, CGPoint summonPoint);

@end

@implementation JCDeviceInfoBaseCell

@end


@interface JCDeviceInfoCell : JCDeviceInfoBaseCell

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *numberLabel;

@end

@implementation JCDeviceInfoCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.numberLabel];

    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
    }];

    [self.numberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(16);
        make.centerY.equalTo(self.contentView);
    }];
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.titleLabel.text = model.title;
    NSString *state = @"    ";
    if ([model.state isKindOfClass:[NSString class]]) {
        // 正常展示
        NSString *_state = (NSString *)model.state;
        state = STR_IS_NIL(_state) ? @"" : _state;
    }
    self.numberLabel.text = state;
}

// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"硬件版本";
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UILabel *)numberLabel {
    if (!_numberLabel) {
        _numberLabel = [[UILabel alloc] init];
        _numberLabel.text = @"3.0.5";
        _numberLabel.textColor = XY_HEX_RGB(0x999999);
        _numberLabel.font = MY_FONT_Regular(15);
    }
    return _numberLabel;
}

@end

@interface JCDevicePopCell : JCDeviceInfoBaseCell

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *popUpLabel;
@property (nonatomic, strong) UIView *popUpButton;
@end

@implementation JCDevicePopCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.popUpButton];

    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.popUpButton setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    
    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
        make.trailing.lessThanOrEqualTo(self.popUpButton.mas_leading).inset(13);
    }];

    [self.popUpButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(16);
        make.centerY.equalTo(self.contentView);
        make.top.lessThanOrEqualTo(self.contentView);
        make.bottom.greaterThanOrEqualTo(self.contentView);
    }];
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.titleLabel.text = model.title;
    NSString *state = @"";
    if ([model.state isKindOfClass:[NSString class]]) {
        // 正常展示
        NSString *_state = (NSString *)model.state;
        state = STR_IS_NIL(_state) ? @"" : _state;
    } else if ([model.state isKindOfClass:[JCKeyFountionCode class]]) {
        // 自定义开机键
        state = ((JCKeyFountionCode *)model.state).name;
    } else if ([model.state isKindOfClass:[JCShutdownModel class]]) {
        // 自动关机
        JCShutdownModel *shutdownModel = (JCShutdownModel *)model.state;
        NSString *title = shutdownModel.minute;
        if ([title isEqualToString:@"永不"]) {
            title = XY_LANGUAGE_TITLE_NAMED(@"app100000111", @"永不");
        } else {
            title = [NSString stringWithFormat:@"%@%@",title,XY_LANGUAGE_TITLE_NAMED(@"app100000110", @"分钟")];
        }
        state = title;
    }
    self.popUpLabel.text = state;
}

- (void)deviceInfoAction:(UITapGestureRecognizer *)gestureRecognizer {
    CGPoint summonPoint = CGPointMake(self.popUpButton.frame.origin.x, self.popUpButton.frame.origin.y + self.popUpButton.frame.size.height);
    summonPoint =  [[UIApplication sharedApplication].keyWindow convertPoint:summonPoint fromView:self];
    self.actionBlock(self.model, summonPoint);
}

// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"自定义开机键";
        _titleLabel.numberOfLines = 2;
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UILabel *)popUpLabel {
    if (!_popUpLabel) {
        UILabel *textLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        textLabel.text = @"打印上一张";
        textLabel.numberOfLines = 2;
        textLabel.textAlignment = self.isRTL ? NSTextAlignmentJustified : NSTextAlignmentRight;
        textLabel.textColor = XY_HEX_RGB(0x999999);
        textLabel.font = MY_FONT_Regular(15);
        _popUpLabel = textLabel;
    }
    return _popUpLabel;
}

- (UIView *)popUpButton {
    if (!_popUpButton) {
        UIView *popUpButton = [[UIView alloc] initWithFrame:CGRectZero];
        UIImageView *selectImageView = [[UIImageView alloc] initWithImage:[UIImage imageNamed:@"setting_multiple_select"]];
        [popUpButton addSubview:selectImageView];
        [popUpButton addSubview:self.popUpLabel];
        [self.popUpLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultHigh forAxis:UILayoutConstraintAxisHorizontal];
        [selectImageView setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [selectImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(popUpButton.mas_trailing);
            make.centerY.equalTo(self.popUpLabel);
            make.size.mas_equalTo(CGSizeMake(16, 16));
        }];
        [self.popUpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(popUpButton);
            make.trailing.equalTo(selectImageView.mas_leading).inset(5);
            make.top.bottom.equalTo(popUpButton);
            make.width.mas_greaterThanOrEqualTo(72);
        }];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(deviceInfoAction:)];
        [popUpButton addGestureRecognizer:tapGesture];
        _popUpButton = popUpButton;
    }
    return _popUpButton;
}

@end

@interface JCDeviceModalCell : JCDeviceInfoBaseCell

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *popUpLabel;
@property (nonatomic, strong) UIImageView *selectImageView;
@property (nonatomic, strong) UIView *popUpButton;
@property (nonatomic, strong) UIView *tapGestureRecognizerView;
@end

@implementation JCDeviceModalCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.popUpButton];
    [self.contentView addSubview:self.tapGestureRecognizerView];
    
    [self.titleLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.popUpButton setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];

    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
        make.trailing.lessThanOrEqualTo(self.popUpButton.mas_leading).inset(13);
    }];

    [self.popUpButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(14);
        make.centerY.equalTo(self.contentView);
        make.top.lessThanOrEqualTo(self.contentView);
        make.bottom.greaterThanOrEqualTo(self.contentView);
    }];
    
    [self.tapGestureRecognizerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.titleLabel.text = model.title;
    NSString *state = @"";
    if ([model.state isKindOfClass:[NSString class]]) {
        // 正常展示
        NSString *_state = (NSString *)model.state;
        state = STR_IS_NIL(_state) ? @"" : _state;
    }
    self.popUpLabel.text = state;
    
    // 整行可点击
    self.tapGestureRecognizerView.hidden = STR_IS_NIL(state) ? NO : YES;
}

- (void)deviceInfoAction:(UITapGestureRecognizer *)gestureRecognizer {
    CGPoint summonPoint = CGPointMake(self.popUpButton.frame.origin.x, self.popUpButton.frame.origin.y + self.popUpButton.frame.size.height);
    summonPoint =  [[UIApplication sharedApplication].keyWindow convertPoint:summonPoint fromView:self];
    self.actionBlock(self.model, summonPoint);
}

// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"走纸校准 || WIFI配置";
        _titleLabel.numberOfLines = 2;
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UILabel *)popUpLabel {
    if (!_popUpLabel) {
        UILabel *textLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        textLabel.text = @"NIIMBOT";
        textLabel.numberOfLines = 2;
        textLabel.textAlignment = self.isRTL ? NSTextAlignmentJustified : NSTextAlignmentRight;
        textLabel.textColor = XY_HEX_RGB(0x999999);
        textLabel.font = MY_FONT_Regular(15);
        _popUpLabel = textLabel;
    }
    return _popUpLabel;
}

- (UIImageView *)selectImageView {
    if (!_selectImageView) {
        UIImageView *selectImageView = [[UIImageView alloc] initWithImage:[[UIImage imageNamed:@"calibration_arrow"] imageFlippedForRightToLeftLayoutDirection]];
        _selectImageView = selectImageView;
    }
    return _selectImageView;
}

- (UIView *)tapGestureRecognizerView {
    if (!_tapGestureRecognizerView) {
        _tapGestureRecognizerView = [[UIView alloc] initWithFrame:CGRectZero];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(deviceInfoAction:)];
        [_tapGestureRecognizerView addGestureRecognizer:tapGesture];
        _tapGestureRecognizerView.hidden = YES;
    }
    return _tapGestureRecognizerView;
}


- (UIView *)popUpButton {
    if (!_popUpButton) {
        UIView *popUpButton = [[UIView alloc] initWithFrame:CGRectZero];
        [popUpButton addSubview:self.selectImageView];
        [popUpButton addSubview:self.popUpLabel];
        [self.popUpLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
        [self.selectImageView setContentHuggingPriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
        [self.popUpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.leading.equalTo(popUpButton);
            make.trailing.equalTo(self.selectImageView.mas_leading).inset(5);
            make.top.bottom.equalTo(popUpButton);
            make.width.mas_greaterThanOrEqualTo(72);
        }];
        [self.selectImageView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(popUpButton.mas_trailing);
            make.centerY.equalTo(self.popUpLabel);
            make.size.mas_equalTo(CGSizeMake(16, 16));
        }];
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(deviceInfoAction:)];
        [popUpButton addGestureRecognizer:tapGesture];
        _popUpButton = popUpButton;
    }
    return _popUpButton;
}

@end

@interface JCDeviceSwitchCell : JCDeviceInfoBaseCell

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UISwitch *switchButton;
@end

@implementation JCDeviceSwitchCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.switchButton];

    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
    }];

    [self.switchButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(16);
        make.centerY.equalTo(self.contentView);
    }];
}

// 监听 UISwitch 值更改的方法
- (void)switchValueChanged:(UISwitch *)sender {
    self.actionBlock(self.model, CGPointZero);
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.titleLabel.text = model.title;
    BOOL isOn = NO;
    if ([model.state isKindOfClass:[NSString class]]) {
        NSString *state = (NSString *)model.state;
        if (!STR_IS_NIL(state) && state.integerValue == 1) {
            isOn = YES;
        }
    }
    [self.switchButton setOn:isOn animated:NO];
}

// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"开关机提示音";
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UISwitch *)switchButton {
    if (!_switchButton) {
        UISwitch *switchButton = [[UISwitch alloc] initWithFrame:CGRectZero];
        switchButton.onTintColor = HEX_RGB(0xFB4B42);
        [switchButton addTarget:self
                              action:@selector(switchValueChanged:)
                    forControlEvents:UIControlEventValueChanged];
        switchButton.transform = CGAffineTransformMakeScale(0.80, 0.80);
        _switchButton = switchButton;
    }
    return _switchButton;
}

@end

@interface JCDeviceUpgradeCell : JCDeviceInfoBaseCell

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *popUpLabel;
@property (nonatomic, strong) UIView *popUpButton;
@end

@implementation JCDeviceUpgradeCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    self = [super initWithStyle:style reuseIdentifier:reuseIdentifier];
    if (self) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        [self setUp];
    }
    return self;
}

- (void)setUp {
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.popUpLabel];
    [self.contentView addSubview:self.popUpButton];
    [self setUpConstraints];
}

- (void)setUpConstraints {
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(self.contentView).offset(16);
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.popUpLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.popUpButton.mas_leading).inset(6);
        make.centerY.equalTo(self.popUpButton);
    }];
    
    float titleWidth = [XY_LANGUAGE_TITLE_NAMED(@"app100001745", @"升级") jk_sizeWithFont:MY_FONT_Regular(13) constrainedToHeight:1000].width;
    [self.popUpButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.trailing.equalTo(self.contentView).inset(16);
        make.centerY.equalTo(self.contentView);
        make.height.mas_equalTo(@(26));
        make.width.mas_equalTo(@(titleWidth + 24));
    }];
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.titleLabel.text = model.title;
    NSString *state = @"";
    BOOL isUpdate = NO;
    if ([model.state isKindOfClass:[NSDictionary class]]) {
        NSDictionary *_state = (NSDictionary *)model.state;
        state = STR_IS_NIL(_state[@"firmwareVersion"]) ? @"" : _state[@"firmwareVersion"];
        isUpdate = ((NSNumber *)_state[@"isUpdate"]).boolValue;
    }
    self.popUpLabel.text = state;
    self.popUpButton.hidden = !isUpdate;
    
    // 更新约束
    if (isUpdate) {
        [self.popUpLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.popUpButton.mas_leading).inset(6);
            make.centerY.equalTo(self.popUpButton);
        }];
    } else {
        [self.popUpLabel mas_remakeConstraints:^(MASConstraintMaker *make) {
            make.trailing.equalTo(self.contentView).inset(16);
            make.centerY.equalTo(self.popUpButton);
        }];
    }
}

- (void)deviceInfoAction:(UITapGestureRecognizer *)gestureRecognizer {
    CGPoint summonPoint = CGPointMake(self.popUpButton.frame.origin.x, self.popUpButton.frame.origin.y + self.popUpButton.frame.size.height);
    summonPoint =  [[UIApplication sharedApplication].keyWindow convertPoint:summonPoint fromView:self];
    self.actionBlock(self.model, summonPoint);
}


// MARK: -- Lazy Load --

- (UILabel *)titleLabel {
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"固件版本";
        _titleLabel.textColor = HEX_RGB(0x262626);
        _titleLabel.font = MY_FONT_Regular(15);
    }
    return _titleLabel;
}

- (UILabel *)popUpLabel {
    if (!_popUpLabel) {
        UILabel *textLabel = [[UILabel alloc] initWithFrame:CGRectZero];
        textLabel.text = @"3.0.5";
        textLabel.numberOfLines = 0;
        textLabel.textColor = XY_HEX_RGB(0x999999);
        textLabel.font = MY_FONT_Regular(15);
        _popUpLabel = textLabel;
    }
    return _popUpLabel;
}

- (UIView *)popUpButton {
    if (!_popUpButton) {
        // 断开
        UIButton *popUpButton = [YLButton buttonWithType:UIButtonTypeCustom];
        [popUpButton setTitleColor:HEX_RGB(0xFB4B42) forState:UIControlStateNormal];
        [popUpButton setTitle:XY_LANGUAGE_TITLE_NAMED(@"app100001745", @"升级") forState:UIControlStateNormal];
        [popUpButton addTarget:self action:@selector(deviceInfoAction:) forControlEvents:UIControlEventTouchUpInside];
        popUpButton.titleLabel.font = MY_FONT_Regular(13);
        popUpButton.backgroundColor = HEX_RGB(0xF7F7FA);
        popUpButton.layer.cornerRadius = 13;
        _popUpButton = popUpButton;
    }
    return _popUpButton;
}

@end

@interface JCDevicePipeLineCalibrationCell : JCDeviceInfoBaseCell

@property(nonatomic,strong) UILabel *mainTitle;

@property(nonatomic,strong) UILabel *detailTitle;

@property(nonatomic,strong) UIView *calibrationView;

@end

@implementation JCDevicePipeLineCalibrationCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier
{
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        
        [self setupUI];
        
    }
    return self;
}

- (void)setupUI
{
    UILabel *mainTitle = [UILabel new];
    mainTitle.textColor = HEX_RGB(0x262626);
    mainTitle.font = MY_FONT_Regular(15);
    mainTitle.textAlignment = NSTextAlignmentLeft;
    mainTitle.text = XY_LANGUAGE_TITLE_NAMED(@"app100001463", @"走管校准");
    mainTitle.numberOfLines = 0;
    mainTitle.lineBreakMode = NSLineBreakByCharWrapping;
    [self.contentView addSubview:mainTitle];
    self.mainTitle = mainTitle;
    
    [self.contentView addSubview:self.detailTitle];
    
    UIView *calibrationView = [[UIView alloc] initWithFrame:CGRectZero];
    calibrationView.backgroundColor = HEX_RGB(0xFFF6F6);
    calibrationView.layer.cornerRadius = 16;
    [calibrationView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(calibration:)];
    [calibrationView addGestureRecognizer:tapGesture];
    [self.contentView addSubview:calibrationView];
    self.calibrationView = calibrationView;
    
    UILabel *calibrationTitle = [UILabel new];
    calibrationTitle.textColor = HEX_RGB(0xFB4B42);
    calibrationTitle.font = MY_FONT_Regular(13);
    calibrationTitle.text = XY_LANGUAGE_TITLE_NAMED(@"app00931",@"校准");
    [calibrationTitle setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [calibrationView addSubview:calibrationTitle];
 
    XYWeakSelf
    
    [mainTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(weakSelf.contentView).offset(15);
        make.top.equalTo(weakSelf.contentView).offset(12);
        make.trailing.lessThanOrEqualTo(calibrationView.mas_leading).offset(-10);
    }];
    
    
    [self.detailTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.leading.equalTo(mainTitle);
        make.top.equalTo(mainTitle.mas_bottom).offset(4);
        make.bottom.equalTo(weakSelf.contentView).inset(12);
    }];
    
    [calibrationTitle mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(calibrationView).insets(UIEdgeInsetsMake(6, 16, 6, 16));
    }];
    
    [calibrationView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(weakSelf.contentView);
        make.trailing.equalTo(weakSelf.contentView).offset(-15);
    }];
    
}

- (UILabel *)detailTitle {
    if (!_detailTitle) {
        _detailTitle = [[UILabel alloc] initWithFrame:CGRectZero];
        _detailTitle.textColor = HEX_RGBA(0x3C3C43, 0.6);
        _detailTitle.font = MY_FONT_Regular(12);
        _detailTitle.text = XY_LANGUAGE_TITLE_NAMED(@"app100001468", @"未校准");
        NSString *deviceName = [[NSUserDefaults standardUserDefaults] valueForKey:LASTCONNECTC1NAME];
        if (!STR_IS_NIL(deviceName)) {
            NSString *calibrationLength = [[NSUserDefaults standardUserDefaults] valueForKey:deviceName];
            if (!STR_IS_NIL(calibrationLength)) {
                _detailTitle.text = calibrationLength;
            }
        }
    }
    return _detailTitle;
}

- (void)setModel:(JCDeviceInfoModel *)model {
    [super setModel:model];
    self.mainTitle.text = model.title;
}

- (void)calibration:(UITapGestureRecognizer *)tapGestureRecognizer {
    JC_TrackWithparms(@"click",@"127_370",@{});
    // 是否放入耗材, 需要判断管子以及碳带是否安装
    if (![JCBluetoothManager sharedInstance].isConsumablesInstalled || ![JCBluetoothManager sharedInstance].hasRibbom) {
        [MBProgressHUD showToastWithMessageDarkColor:XY_LANGUAGE_TITLE_NAMED(@"app100001464", @"请先安装耗材")];
        return;
    }
    // 先出管长度
    [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"setTubeCalibration"
                                                           arguments:nil result:^(NSNumber   * _Nullable code) {
      if (code.integerValue == 0) {
          JCPipeLineCalibrationView* pipeLineCalibrationView = [[JCPipeLineCalibrationView alloc] initWithFrame:CGRectZero];
          pipeLineCalibrationView.clickViewBtnBlock1 = ^(UIButton *sender,NSString *text) {
              if (!STR_IS_NIL(text)) {
                  NSString *calibrationLength = [text stringByAppendingString:@"mm"];
                  self.detailTitle.text = calibrationLength;
                  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"setTubeAdjustLength"
                                                                         arguments:@([text floatValue]) result:^(NSNumber   * _Nullable code) {
                      if (code.integerValue == 0){
                        // 存储当前的校准值
                        // 当前连接机器的C1名称
                        NSString *deviceName = [[NSUserDefaults standardUserDefaults] valueForKey:LASTCONNECTC1NAME];
                        [[NSUserDefaults standardUserDefaults] setObject:calibrationLength forKey:deviceName];
                        [MBProgressHUD showSuccessToast:XY_LANGUAGE_TITLE_NAMED(@"app100001473", @"校准成功") icon:@"copySuccess"];
                      } else{
                        [MBProgressHUD showSuccessToast:XY_LANGUAGE_TITLE_NAMED(@"app100001474", @"校准失败") icon:@"warning"];
                      }
                  }];
              }
          };
          [pipeLineCalibrationView show];
      } else {
          // 出管失败
          [MBProgressHUD showSuccessToast:XY_LANGUAGE_TITLE_NAMED(@"app100001469", @"走管失败！") icon:@"warning"];
      }
    }];
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated {
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

@end


@implementation JCBluetoothConnectAlert (DeviceInfo)

- (UIView *)deviceInfoView {
    if (!self.deviceInfoViewC) {
        UIView *view = [[UIView alloc] initWithFrame:CGRectZero];
        
        // RFID状态
        UIStackView *rfidStatusStackView = [self rfidStatusStackView];
        [view addSubview:rfidStatusStackView];
        
        [rfidStatusStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(view);
            make.leading.trailing.equalTo(view).inset(15);
            make.height.mas_equalTo(66);
        }];
        
        // 设备控制
        GroupShadowTableView *tableView = [self deviceInfoGroupTableView];
        [view addSubview:tableView];
        [tableView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(rfidStatusStackView.mas_bottom);
            make.leading.trailing.bottom.equalTo(view);
        }];
        self.deviceInfoViewC = view;
        self.deviceInfoViewC.hidden = !JC_IS_CONNECTED_PRINTER;
    }
    return self.deviceInfoViewC;
}


- (UIStackView *)rfidStatusStackView {
    // 添加RFID状态
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisHorizontal; // 设置为垂直方向
    stackView.spacing = 12; // 设置子视图之间的间距
    stackView.alignment = UIStackViewAlignmentFill; // 设置对齐方式
    stackView.distribution = UIStackViewDistributionFillEqually; // 设置分布方式
    stackView.tag = 2024082600;
    // 标签纸余量
    JCRFIDStatusView *labelStatusView = [[JCRFIDStatusView alloc] initWithFrame:CGRectZero withType:RFIDShowTypeLabel];
    labelStatusView.tag = 2024082601;
    XYWeakSelf
    labelStatusView.actionBlock = ^(RFIDShowStausType rfidShowType){
        [weakSelf toShopMall:rfidShowType];
    };
    [labelStatusView setHidden:YES];
    // 碳带余量
    JCRFIDStatusView *carbonStatusView = [[JCRFIDStatusView alloc] initWithFrame:CGRectZero withType:RFIDShowTypeCarbon];
    carbonStatusView.titleLabel.text = XY_LANGUAGE_TITLE_NAMED(@"app01222", @"碳带余量");
    carbonStatusView.tag = 2024082602;
    carbonStatusView.actionBlock = ^(RFIDShowStausType rfidShowType){
        [weakSelf toShopMall:rfidShowType];
    };
    [carbonStatusView setHidden:YES];
    [stackView addArrangedSubview:labelStatusView];
    [stackView addArrangedSubview:carbonStatusView];
    return stackView;
}

- (GroupShadowTableView *)deviceInfoGroupTableView {
    GroupShadowTableView *tableView = [[GroupShadowTableView alloc] initWithFrame:CGRectZero style:UITableViewStyleGrouped];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    [tableView registerClass:[JCDeviceInfoCell class] forCellReuseIdentifier:@"JCDeviceInfoCell"];
    [tableView registerClass:[JCDevicePopCell class] forCellReuseIdentifier:@"JCDevicePopCell"];
    [tableView registerClass:[JCDeviceModalCell class] forCellReuseIdentifier:@"JCDeviceModalCell"];
    [tableView registerClass:[JCDeviceSwitchCell class] forCellReuseIdentifier:@"JCDeviceSwitchCell"];
    [tableView registerClass:[JCDeviceUpgradeCell class] forCellReuseIdentifier:@"JCDeviceUpgradeCell"];
    [tableView registerClass:[JCDevicePipeLineCalibrationCell class] forCellReuseIdentifier:@"JCDevicePipeLineCalibrationCell"];
    tableView.groupShadowDelegate = self;
    tableView.groupShadowDataSource = self;
    tableView.showSeparator = YES;
    tableView.separatorColor = HEX_RGB(0xEBEBEB);
    tableView.addShadow = NO;
    tableView.backgroundColor = nil;
    tableView.tag = 2024082603;
    if (!iPhoneX) {
        tableView.tableFooterView = [[UIView alloc] initWithFrame:CGRectMake(0, 0, 0, 20)];
    }
    return tableView;
}


// MARK: -- 刷新标签纸｜｜ 碳带状态 --
- (UIStackView *)rfidStackView {
    return [self viewWithTag:2024082600];
}
// 标签纸余量View
- (JCRFIDStatusView *)labelStatusView {
    return [self viewWithTag:2024082601];
}

// 碳带余量View
- (JCRFIDStatusView *)carbonStatusView {
    return [self viewWithTag:2024082602];
}

/// 是否展示标签纸余量
/// - Parameter isShow: 是否展示
- (void)refreshDeviceLabelShowOrHide:(BOOL)isShow {
    if (isShow) {
        if (self.labelStatusView) {
            [self.rfidStackView insertArrangedSubview: self.labelStatusView atIndex:0];
            [self.labelStatusView setHidden:NO];
        }
    } else {
        if (self.labelStatusView) {
            [self.rfidStackView removeArrangedSubview:self.labelStatusView];
            [self.labelStatusView setHidden:YES];
        }
    }
}

/// 是否展示碳带余量
/// - Parameter isShow: 是否展示
- (void)refreshDeviceCarbonShowOrHide:(BOOL)isShow {
    if (isShow) {
        if (self.carbonStatusView) {
            [self.rfidStackView addArrangedSubview:self.carbonStatusView];
            [self.carbonStatusView setHidden:NO];
        }
    } else {
        if (self.carbonStatusView) {
            [self.rfidStackView removeArrangedSubview:self.carbonStatusView];
            [self.carbonStatusView setHidden:YES];
        }
    }
}


/// 刷新碳带余量值
/// - Parameter surplusFloat: 档位值
- (void)refreshDeviceLabelState:(NSInteger)surplusFloat {
    if (self.labelStatusView) {
        [self.labelStatusView refreshProgressView:surplusFloat];
    }
}

/// 刷新标签纸余量值
/// - Parameter surplusFloat: 档位值
- (void)refreshDeviceCarbonState:(NSInteger)surplusFloat {
    if (self.carbonStatusView) {
        [self.carbonStatusView refreshProgressView:surplusFloat];
    }
}

/// 刷新RFID单双排列显示
- (void)refreshDeviceRFIDStackState {
    if (self.rfidStackView.arrangedSubviews.count == 1) {
        // 单个RFID撑满
        JCRFIDStatusView *rfidView = self.rfidStackView.arrangedSubviews.firstObject;
        [rfidView refreshProgressViewLengthStatus:RFIDProgressHigh];
    } else {
        [self.rfidStackView.arrangedSubviews enumerateObjectsUsingBlock:^(__kindof JCRFIDStatusView * _Nonnull rfidView, NSUInteger idx, BOOL * _Nonnull stop) {
            [rfidView refreshProgressViewLengthStatus:RFIDProgressLow];
        }];
    }
    
    // 在动画块中刷新布局
    [UIView animateWithDuration:0.25 animations:^{
        // 判断是否显示RFIDStackView
        if (self.rfidStackView.arrangedSubviews.count == 0) {
            [self.deviceInfoTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.deviceInfoViewC).inset(-12);
                make.leading.trailing.bottom.equalTo(self.deviceInfoViewC);
            }];
        } else {
            [self.deviceInfoTableView mas_remakeConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(self.rfidStackView.mas_bottom);
                make.leading.trailing.bottom.equalTo(self.deviceInfoViewC);
            }];
        }
        [self.deviceInfoTableView layoutIfNeeded]; // 触发动画更新
    }];
}

// MARK: -- 刷新机器信息 --
- (GroupShadowTableView *)deviceInfoTableView {
    return [self viewWithTag:2024082603];
}

- (void)refreshDeviceDetailInfo:(NSArray<NSArray<JCDeviceInfoModel *> *> *)deviceDetailInfo {
    self.deviceDetailInfo = deviceDetailInfo;
    [self.deviceInfoTableView reloadData];
}

// MARK: - UITableViewDataSource
- (NSInteger)numberOfSectionsInGroupShadowTableView:(GroupShadowTableView *)tableView {
    return self.deviceDetailInfo.count;
}

- (NSInteger)groupShadowTableView:(GroupShadowTableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.deviceDetailInfo[section].count;
}

- (UITableViewCell *)groupShadowTableView:(GroupShadowTableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    JCDeviceInfoModel *model = self.deviceDetailInfo[indexPath.section][indexPath.row];
    JCDeviceInfoBaseCell *cell;
    switch (model.type) {
        case JCDeviceKeyFunction: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDevicePopCell"];
        }
            break;
        case JCDeviceWifi: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceModalCell"];
        }
            break;
        case JCDeviceCalibratePaper: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceModalCell"];
        }
            break;
        case JCDeviceAutoCloseDevice: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDevicePopCell"];
        }
            break;
        case JCDevicePowerVoiceState: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceSwitchCell"];
        }
            break;
        case JCDeviceBlueToothVoiceState: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceSwitchCell"];
        }
            break;
        case JCDeviceFirmwareVersion: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceUpgradeCell"];
        }
            break;
        case JCDeviceSerialNumber: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceInfoCell"];
        }
            break;
        case JCDeviceHardwareVersion: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDeviceInfoCell"];
            break;
        }
        case JCDevicePipelineCalibration: {
            cell = [tableView dequeueReusableCellWithIdentifier:@"JCDevicePipeLineCalibrationCell"];
            break;
        }
        default:
            return nil;
    }
    cell.model = model;
    __weak typeof(self) weakSelf = self;
    cell.actionBlock = ^(JCDeviceInfoModel *model, CGPoint summonPoint) {
        // 自定义开机键、走纸校准、自动关机、固件升级，其他的照常收起
        if (model.type == JCDeviceKeyFunction || model.type == JCDeviceCalibratePaper || model.type == JCDeviceAutoCloseDevice || model.type == JCDeviceFirmwareVersion) {
            weakSelf.isShouldHideAlert = NO;
        }
        [weakSelf deviceInfoAction:model onSummonPoint:summonPoint];
    };
    return cell;
}

// MARK: - UITableViewDelegate
- (UIView *)groupShadowTableView:(GroupShadowTableView *)tableView viewForFooterInSection:(NSInteger)section{
    UIView *footerView = [[UIView alloc]initWithFrame:CGRectMake(0, 0, SCREEN_WIDTH, 1)];
    footerView.backgroundColor = [UIColor clearColor];
    return footerView;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForFooterInSection:(NSInteger)section{
    return 1;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForHeaderInSection:(NSInteger)section{
    return 12;
}

- (CGFloat)groupShadowTableView:(GroupShadowTableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    if (self.deviceDetailInfo[indexPath.section][indexPath.row].type == JCDevicePipelineCalibration) {
        // 判断当前语言是否为简中或繁中
        if ([XY_JC_LANGUAGE_REAL isEqualToString:@"zh-cn"] || [XY_JC_LANGUAGE_REAL isEqualToString:@"zh-tw"]) {
            // 简中或繁中（zh-Hans, zh-Hant 都以 zh 开头）
            return 65;
        } else {
            // 其他外语需要换行显示，确保走管校准文字完整显示
            return 85;
        }
    }
    return 44;
}

- (void)groupShadowTableView:(GroupShadowTableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    
}

@end
