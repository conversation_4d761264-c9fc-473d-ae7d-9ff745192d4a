import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:niimbot_log_plugin/niimbot_log_plugin.dart';
import 'package:path/path.dart' as path;
import 'package:sprintf/sprintf.dart';
import 'package:text/application.dart';
import 'package:text/macro/color.dart';
import 'package:text/pages/canvas/niimbot_canvas_page.dart';
import 'package:text/routers/fluro_navigator.dart';
import 'package:text/utils/input_field.dart';
import 'package:text/utils/screen_utils.dart';
import 'package:text/utils/svg_icon.dart';
import 'package:text/utils/theme_color.dart';
import 'package:text/utils/toast_util.dart';

import '../routers/custom_navigation.dart';
import 'log_utils.dart';

String intlanguage(String key, String placeholder, {List<String>? param, bool isUseDefaultValue = true}) {
  String languageValue = isUseDefaultValue ? placeholder : "";
  if (key.isNotEmpty) {
    if (Application.currentLanguageData[key] == null) {
      Log.e("未匹配到翻译资源$key $placeholder");
    } else {
      Map languageDetail = Application.currentLanguageData[key];
      String value = languageDetail.isEmpty ? placeholder : languageDetail["value"];
      if (languageDetail.isNotEmpty && value.isNotEmpty) {
        languageValue = languageDetail["value"];
      } else {
        Log.e("未匹配到翻译资源$key $placeholder");
      }
    }
  }
  if (null != param) {
    languageValue = languageValue.replaceAll("\$S", "%s");
    languageValue = languageValue.replaceAll("\$", "%s");
    languageValue = sprintf(languageValue, param);
    for (int i = 0; i < param.length; i++) {
      languageValue = languageValue.replaceAll("${i + 1}S", "");
    }
    return languageValue;
  } else {
    return languageValue;
  }
}

bool containsSimplifiedChinese(String text) {
  return RegExp(r'[\u4E00-\u9FFF]').hasMatch(text);
}

///即时上传日志信息
///[logInfo]日志信息
///[topic]日志话题/功能模块
Future<bool> uploadLogInstantTime(Map<String, dynamic> logInfo, {String topic = ""}) async {
  bool uploadSuccess = await NiimbotLogTool.uploadLogInstantTime(logInfo, topic: topic);
  return Future.value(uploadSuccess);
}

///写入日志信息至本地日志文件
///[logInfo]日志信息
Future<bool> writeLogToFile(Map<String, dynamic> logInfo) async {
  bool uploadSuccess = await NiimbotLogTool.writeLogToFile(logInfo);
  return Future.value(uploadSuccess);
}

///写入日志信息至本地日志文件
///[topic]日志话题/全局日志appLog、SDK日志SDKLog
///[localPath]日志文件地址，默认全局日志用不传，SDK日志需传入日志文件地址
Future<Map> uploadLogFileToSls({String topic = "appLog", String? localPath}) async {
  Completer<Map> completer = Completer();
  NiimbotLogTool.uploadLogManual(
      success: (p0) {
        completer.complete({"writeLogInfoSuccess": true, "url": p0});
      },
      faild: (p0, p1) {
        completer.complete({"writeLogInfoSuccess": false, "errorMsg": p1});
      },
      topic: topic,
      filePath: localPath);
  return completer.future;
}

// Function to check if a string contains traditional Chinese characters
bool containsTraditionalChinese(String text) {
  return RegExp(r'[\u4E00-\u9FA5]').hasMatch(text);
}

void showMsgDialogOfSubmit(BuildContext context, String title, String msg, String submit, Function define,
    {Function? cancel}) {
  showDialog(
      useRootNavigator: false,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            if (cancel != null) cancel();
            return true; // 返回 true 允许页面返回
          },
          child: SimpleDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(5),
            ),
            contentPadding: EdgeInsets.fromLTRB(0, 16, 0, 0),
            title: Center(
              child: Text(
                "$title",
                style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 17),
              ),
            ),
            children: <Widget>[
              Container(
                padding: EdgeInsets.fromLTRB(20, 0, 20, 25),
                child: Text(
                  "$msg",
                  style: TextStyle(color: Color.fromRGBO(99, 99, 99, 1.0), fontSize: 15),
                ),
                decoration: BoxDecoration(
                    border: Border(
                  bottom: Divider.createBorderSide(context, width: 0.6),
                )),
              ),
              Container(
                child: Row(
                  children: <Widget>[
                    Expanded(
                      flex: 1,
                      child: Material(
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5)),
                        child: InkWell(
                          onTap: () {
                            Navigator.of(context).pop(true);
                            if (cancel != null) cancel();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 44,
                            child: Text(
                              "取消",
                              style: TextStyle(color: KColor.COLOR_PRIMARY, fontSize: 15),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Material(
                        borderRadius: BorderRadius.only(bottomRight: Radius.circular(5)),
                        color: KColor.COLOR_PRIMARY,
                        child: InkWell(
                          onTap: () {
                            Navigator.of(context).pop(true);
                            define();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 44,
                            child: Text(
                              submit,
                              style: TextStyle(color: KColor.WHITE, fontSize: 15),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        );
      });
}

void showEditTextDialog(BuildContext context, String title, String? text, int maxLength, Function define,
    {Function? cancel,
    String editTextHint = "",
    String editTextEmptyToast = "",
    String subTitle = "",
    TextInputType? keyboardType = null,
    TextInputFormatter? textInputFormat = null,
    selectAll = false,
    barrierDismissible = true}) {
  Widget titleBar;
  if (subTitle.isEmpty) {
    titleBar = Center(
      child: Text(
        "$title",
        style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 16, fontWeight: FontWeight.w600),
      ),
    );
  } else {
    titleBar = Column(
      children: [
        Text(
          "$title",
          style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 16, fontWeight: FontWeight.w600),
        ),
        SizedBox(height: 6),
        Text(
          "$subTitle",
          style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 14, fontWeight: FontWeight.w400),
        )
      ],
    );
  }
  final TextEditingController _editingController = TextEditingController();
  final FocusNode node = FocusNode();
  if (text != null) {
    _editingController.text = text;
  }
  showDialog(
      useRootNavigator: false,
      context: context,
      barrierDismissible: barrierDismissible,
      useSafeArea: false,
      routeSettings: RouteSettings(name: "editTextDialog"),
      barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
          return WillPopScope(
            onWillPop: () async {
              if (!barrierDismissible) {
                return false;
              }
              if (cancel != null) cancel();
              return true; // 返回 true 允许页面返回
            },
            child: SimpleDialog(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: EdgeInsets.fromLTRB(0, 16, 0, 0),
              title: titleBar,
              children: <Widget>[
                Container(
                  height: 40,
                  width: MediaQuery.sizeOf(context).width - 200,
                  margin: EdgeInsets.fromLTRB(20, 0, 20, 0),
                  padding: EdgeInsetsDirectional.only(start: 10),
                  child: Center(
                      child: Row(
                    children: [
                      Expanded(
                        child: Padding(
                          padding: EdgeInsetsDirectional.only(top: 2),
                          child: InputField(
                            focusNode: node,
                            textEditingController: _editingController,
                            maxLength: maxLength,
                            height: 21,
                            textAlign: TextAlign.start,
                            contentPadding: EdgeInsets.zero,
                            hintText: editTextHint,
                            keyboardType: keyboardType,
                            inputFormatters: textInputFormat == null ? [] : [textInputFormat],
                            onChanged: (text) {
                              setState(() {});
                            },
                            textStyle: const TextStyle(
                              color: ThemeColor.title,
                              fontSize: 13,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: _editingController.text != "",
                        child: GestureDetector(
                            onTap: () {
                              _editingController.text = "";
                              setState(() {});
                            },
                            child: Padding(
                              padding: EdgeInsetsDirectional.only(end: 10),
                              child: const SvgIcon('assets/images/industry_template/search_template/search_clear.svg'),
                            )),
                      )
                    ],
                  )),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: ThemeColor.COLOR_D9D9D9,
                      width: 0.5,
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsetsDirectional.only(top: 19),
                  child: Divider(
                    height: 1,
                    color: KColor.disable_text,
                  ),
                ),
                Container(
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop(true);
                            if (cancel != null) cancel();
                          },
                          child: Container(
                            height: 45,
                            color: Colors.transparent,
                            alignment: Alignment.center,
                            child: Text(
                              intlanguage('app100000692', '取消'),
                              style: TextStyle(color: KColor.pinglunTitle, fontSize: 17, fontWeight: FontWeight.w400),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        // width: 20,
                        height: 45,
                        child: VerticalDivider(
                          width: 0.5,
                          color: KColor.disable_text,
                          thickness: 0.5,
                        ),
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            if (_editingController.text.isEmpty) {
                              showCenterToast(context, editTextEmptyToast);
                              return;
                            }
                            Navigator.of(context).pop(true);
                            define(_editingController.text);
                          },
                          child: Container(
                            height: 45,
                            alignment: Alignment.center,
                            color: Colors.transparent,
                            child: Text(
                              intlanguage('app00048', '确定'),
                              style: TextStyle(color: KColor.pingAtTitle, fontSize: 17, fontWeight: FontWeight.w400),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          );
        });
      });

  // 添加监听，等待界面构建完成后执行回调
  WidgetsBinding.instance.addPostFrameCallback((_) {
    node.requestFocus();
    if (_editingController.text.isNotEmpty && selectAll) {
      _editingController.selection = TextSelection(
        baseOffset: 0,
        extentOffset: _editingController.text.length,
      );
    }
  });
}

showCupertinoBottomSheet(List<String> tiles, BuildContext context, Function(int)? onItemClicked,
    {TextStyle? tileStyle}) {
  showModalBottomSheet(
      useRootNavigator: false,
      barrierColor: const Color.fromARGB(90, 0, 0, 0),
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                child: ListView.separated(
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    return ListTile(
                      title: Text(
                        tiles[index],
                        textAlign: TextAlign.center,
                        style: tileStyle ?? const TextStyle(color: ThemeColor.title),
                      ),
                      onTap: () {
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (onItemClicked != null) {
                            onItemClicked(index);
                          }
                        });
                        Navigator.pop(context);
                      },
                    );
                  },
                  itemCount: tiles.length,
                  shrinkWrap: true,
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider(indent: 0, height: 1.0, color: ThemeColor.divider);
                  },
                ),
              ),
              Container(
                margin: const EdgeInsets.fromLTRB(15, 10, 15, 15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: Colors.white,
                ),
                child: ListTile(
                  title: Text(intlanguage('app00030', '取消'),
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: ThemeColor.mainTitle, fontSize: 18, fontWeight: FontWeight.w600)),
                  onTap: () {
                    Navigator.pop(context, intlanguage('app00030', '取消'));
                  },
                ),
              ),
            ],
          ),
        );
      });
}

void showListStringDialog(BuildContext context, String title, List<String> dataList, ValueChanged<String> onClickBack) {
  if (dataList.isEmpty) {
    return;
  }
  showDialog<void>(
    useRootNavigator: false,
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return SimpleDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          contentPadding: EdgeInsets.fromLTRB(0, 10, 0, 0),
          title: Center(
            child: Text(
              title,
              style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 16),
            ),
          ),
          children: dataList
              .map(
                (itemStr) => InkWell(
                  onTap: () {
                    onClickBack(itemStr);
                    NavigatorUtils.goBack(context);
                  },
                  child: Container(
                    width: double.infinity,
                    height: 50,
                    alignment: Alignment.center,
                    child: Text(
                      itemStr,
                      style: TextStyle(fontSize: 15, color: Color.fromRGBO(52, 52, 52, 1.0)),
                    ),
                    decoration: BoxDecoration(border: Border(bottom: Divider.createBorderSide(context, width: 0.6))),
                  ),
                ),
              )
              .toList());
    },
  );
}

void showCustomListDialog(
  BuildContext context,
  String title,
  String content,
  List<ExitSheetData> sheetTiles,
  Function(int)? onItemClicked, {
  int maxLine = -1,
}) async {
  List<String> tiles = sheetTiles.map((e) => e.title).toList();
  int? highlightIndex = sheetTiles.indexWhere((element) {
    return element.saveType != null && element.saveType == SaveType.notSave;
  });
  showModalBottomSheet(
      useRootNavigator: false,
      context: context,
      barrierColor: Color(0xFF000000).withOpacity(0.35),
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return WillPopScope(
          onWillPop: () async {
            BoostNavigator.instance.pop();
            return true;
          },
          child: Container(
            // color: Colors.red,
            width: MediaQuery.sizeOf(context).width,
            margin: EdgeInsets.fromLTRB(12, 0, 12, Platform.isAndroid? ScreenUtil().viewInsets.bottom+10 : 30),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: getAllAlertBtns(tiles, onItemClicked, context, highlightIndex),
            ),
          ),
        );
      });
}

List<Widget> getAllAlertBtns(
    List<String> tiles, Function(int)? onItemClicked, BuildContext context, int? highlightIndex) {
  List<Widget> totals = [];
  Widget topContainer = ClipRRect(
    borderRadius: BorderRadius.circular(12.0),
    child: Column(
      children: getAllListTiles(tiles, onItemClicked, context, highlightIndex),
    ),
  );
  totals.add(topContainer);
  Widget gap = Container(height: 12);

  Widget cancelItem = getCancelWidget(onItemClicked, context);
  totals.add(gap);
  totals.add(cancelItem);
  return totals;
}

Widget getCancelWidget(Function(int)? onItemClicked, BuildContext context) {
  Widget cancelWidget = GestureDetector(
    onTap: () {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (onItemClicked != null) {
          onItemClicked(-1);
        }
      });
      Navigator.pop(context, intlanguage('app00030', '取消'));
    },
    child: Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(0, 18, 0, 18),
      child: Center(
        child: Text(
          intlanguage('app00030', '取消'),
          style: const TextStyle(color: ThemeColor.mainTitle, fontSize: 16, fontWeight: FontWeight.w400),
        ),
      ),
    ),
  );

  Widget bottomContainer = ClipRRect(borderRadius: BorderRadius.circular(12.0), child: cancelWidget);
  return bottomContainer;
}

List<Widget> getAllListTiles(
    List<String> tiles, Function(int)? onItemClicked, BuildContext context, int? highlightIndex) {
  List<Widget> widgets = [];
  for (int index = 0; index < tiles.length; index++) {
    Widget listTile = GestureDetector(
      onTap: () {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (onItemClicked != null) {
            onItemClicked(index);
          }
        });
        Navigator.pop(context, index);
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.fromLTRB(0, 18, 0, 18),
        child: Center(
          child: Text(
            tiles[index],
            style: TextStyle(
                color: (highlightIndex != null && highlightIndex == index) ? Colors.red : ThemeColor.mainTitle,
                fontSize: 16,
                fontWeight: FontWeight.w400),
          ),
        ),
      ),
    );

    widgets.add(listTile);
    if (index != tiles.length - 1) {
      widgets.add(Container(
        height: 1,
        color: Color(0xFFEBEBEB),
      ));
    }
  }
  return widgets;
}

void showCustomDialog(BuildContext context, String title, String content,
    {bool justSureButton = false,
    bool dismissOutSideTouch = true,
    int maxLine = -1,
    String leftFunStr = '取消',
    String rightFunStr = '确定',
    VoidCallback? leftFunCall,
    VoidCallback? rightFunCall,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    Color? rightTextColor,
    bool willPopWrapper = false}) async {
  Color? rightColor = rightTextColor;
  if (leftFunStr == '取消') {
    leftFunStr = intlanguage("app00030", "取消");
  }
  if (rightFunStr == "确定") {
    rightFunStr = intlanguage("app00048", "确定");
  }
  if (rightColor == null) {
    if ([intlanguage('app00063', "删除")].contains(rightFunStr)) {
      rightColor = KColor.RED;
    } else if ([
      "解绑",
      "立即设置",
      "跳转到淘宝",
      "跳转到天猫",
      "跳转到京东",
      intlanguage('app01108', '确认'),
      intlanguage('app01521', '开通VIP')
    ].contains(rightFunStr)) {
      rightColor = KColor.link_color;
    } else {
      rightColor = Color(0xFF000000);
    }
  }
  Widget contentWidget = content.isEmpty
      ? Container()
      : Text(
          "$content",
          textAlign: TextAlign.center,
          style: contentTextStyle == null
              ? TextStyle(color: Color.fromRGBO(99, 99, 99, 1.0), fontSize: 15)
              : contentTextStyle,
        );
  if (maxLine > 0) {
    contentWidget = Text(
      "$content",
      textAlign: TextAlign.left,
      maxLines: maxLine,
      style: TextStyle(color: ThemeColor.mainTitle, fontSize: 14),
    );
  }
  showDialog(
      useRootNavigator: false,
      context: context,
      barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
      barrierDismissible: dismissOutSideTouch,
      builder: (BuildContext context) {
        return Center(
            child: Container(
                constraints: BoxConstraints(minHeight: 0, minWidth: 180),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                width: MediaQuery.of(context).size.width - 104,
                child:
                    Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.stretch, children: [
                  Container(
                    padding: title.isEmpty
                        ? EdgeInsetsDirectional.zero
                        : EdgeInsetsDirectional.fromSTEB(16, 20, 16, content.isEmpty ? 20 : 6),
                    child: title.isEmpty
                        ? Container()
                        : Text(
                            "$title",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 16.0,
                                color: Color(0xFF000000),
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.none),
                          ),
                  ),
                  Container(
                    padding: content.isEmpty
                        ? EdgeInsetsDirectional.zero
                        : EdgeInsetsDirectional.fromSTEB(16, title.isEmpty ? 20 : 0, 16, 20),
                    child: content.isEmpty
                        ? Container()
                        : Text(
                            content,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                                fontSize: 14.0,
                                color: Color(0xFF000000),
                                fontWeight: FontWeight.w400,
                                decoration: TextDecoration.none),
                          ),
                    decoration: BoxDecoration(
                        border: Border(
                      bottom: Divider.createBorderSide(context, width: 0.6),
                    )),
                  ),
                  Container(
                    child: Row(
                      children: <Widget>[
                        justSureButton
                            ? Container()
                            : Expanded(
                                flex: 1,
                                child: Material(
                                  borderRadius: const BorderRadiusDirectional.only(bottomStart: Radius.circular(12)),
                                  color: KColor.WHITE,
                                  child: InkWell(
                                    onTap: () {
                                      Navigator.of(context).pop(true);
                                      if (leftFunCall != null) {
                                        leftFunCall();
                                      }
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 50,
                                      child: Text(
                                        leftFunStr,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            color: KColor.pinglunTitle, fontSize: 17, fontWeight: FontWeight.w400),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                        Expanded(
                          flex: 1,
                          child: Material(
                            borderRadius: BorderRadiusDirectional.only(
                                bottomEnd: const Radius.circular(12),
                                bottomStart: Radius.circular(justSureButton ? 12 : 0)),
                            color: KColor.WHITE,
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop(true);
                                if (rightFunCall != null) {
                                  rightFunCall();
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    border: BorderDirectional(start: Divider.createBorderSide(context, width: 0.5))),
                                alignment: Alignment.center,
                                height: 50,
                                child: Text(
                                  rightFunStr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: rightColor, fontSize: 16, fontWeight: FontWeight.w400),
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  )
                ])));
      });
}

Future<void> showNimmbotDialog(BuildContext context,
    {String? title,
    String content = '',
    String cancelDes = '',
    String confirmDes = '',
    Color confirmDesColor = ThemeColor.brand,
    bool useRootNavigator = false,
    VoidCallback? cancelAction,
    VoidCallback? confirmAction}) {
  return showDialog(
      context: context,
      useRootNavigator: useRootNavigator,
      barrierColor: ThemeColor.mainTitle.withOpacity(0.35),
      builder: (BuildContext context) {
        return WillPopScope(
            onWillPop: () async => true,
            child: Container(
                // color: ThemeColor.mainTitle.withOpacity(0.35),
                child: Center(
                    child: Container(
              width: 270,
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(14.0), color: ThemeColor.background),
              child: Column(mainAxisSize: MainAxisSize.min, children: [
                // 标题
                Padding(
                  padding: const EdgeInsets.fromLTRB(16, 20, 16, 6),
                  child: Text(
                    title ?? "",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        color: ThemeColor.mainTitle,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        decoration: TextDecoration.none),
                  ),
                ),
                content.isEmpty
                    ? const SizedBox(
                        height: 16,
                      )
                    : // 描述
                    Padding(
                        padding: const EdgeInsets.fromLTRB(16, 0, 16, 22),
                        child: Text(
                          content,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: ThemeColor.mainTitle,
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              decoration: TextDecoration.none),
                        ),
                      ),
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
                  child: SizedBox(
                    height: 44,
                    child: DefaultTextStyle(
                      style: TextStyle(color: ThemeColor.COLOR_262626, fontSize: 17, fontWeight: FontWeight.w400),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context, rootNavigator: useRootNavigator).pop(true);
                                if (cancelAction != null) {
                                  cancelAction();
                                }
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: ThemeColor.COLOR_F5F5F5, borderRadius: BorderRadius.circular(10)),
                                alignment: Alignment.center,
                                child: Text(
                                  cancelDes.isEmpty ? intlanguage("app00030", "取消") : cancelDes,
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context, rootNavigator: useRootNavigator).pop(true);
                                if (confirmAction != null) {
                                  confirmAction();
                                }
                              },
                              child: Container(
                                decoration:
                                    BoxDecoration(color: confirmDesColor, borderRadius: BorderRadius.circular(10)),
                                alignment: Alignment.center,
                                child: Text(
                                  confirmDes.isEmpty ? intlanguage("app00048", "确定") : confirmDes,
                                  style: TextStyle(color: ThemeColor.background),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ]),
            ))));
      });
}

void showNoPermissionDialog(BuildContext context) {
  showCustomDialog(context, "无操作权限", "当前功能，您没有操作权限，请联系管理员开通。", justSureButton: true);
}

Future<bool> showOfflineTipDialog(BuildContext context,String title,String content,String okStr) async{
  return await showDialog(
      useRootNavigator: false,
      context: context,
      barrierColor: Color.fromRGBO(0, 0, 0, 0.35),
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Center(
            child: Container(
                constraints: BoxConstraints(minHeight: 0, minWidth: 180),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color: Colors.white,
                ),
                width: MediaQuery.of(context).size.width - 104,
                child:
                Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.stretch, children: [
                  Container(
                    padding:  EdgeInsetsDirectional.fromSTEB(16, 20, 16, 6),
                    child:Text(
                      "$title",
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 16.0,
                          color: Color(0xFF000000),
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.none),
                    ),
                  ),
                  Container(
                    padding:  EdgeInsetsDirectional.fromSTEB(16,  0, 16, 20),
                    child:  Text(
                      content,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                          fontSize: 14.0,
                          color: Color(0xFF000000),
                          fontWeight: FontWeight.w400,
                          decoration: TextDecoration.none),
                    ),
                    decoration: BoxDecoration(
                        border: Border(
                          bottom: Divider.createBorderSide(context, width: 0.6),
                        )),
                  ),
                  Container(
                    child: Row(
                      children: <Widget>[

                        Expanded(
                          flex: 1,
                          child: Material(
                            borderRadius: BorderRadiusDirectional.only(
                                bottomEnd: const Radius.circular(12),
                                bottomStart: Radius.circular(12)),
                            color: KColor.WHITE,
                            child: InkWell(
                              onTap: () {
                                Navigator.of(context).pop(true);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    border: BorderDirectional(start: Divider.createBorderSide(context, width: 0.5))),
                                alignment: Alignment.center,
                                height: 50,
                                child: Text(
                                  okStr,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color:  KColor.RED, fontSize: 16, fontWeight: FontWeight.w400),
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  )
                ])));
      });
}

void showStockLockSubmitDialog(BuildContext context, String msg, {VoidCallback? sureFunc}) {
  showCustomDialog(context, "提示", '$msg${"请等待盘点结束后，再进行出入库等相关业务操作。"}', dismissOutSideTouch: false, justSureButton: true,
      rightFunCall: () {
    if (sureFunc != null) {
      sureFunc();
    }
  });
}

void showProxyDialog(BuildContext context, Function callback, {String message = "设置代理"}) {
  showDialog(
      useRootNavigator: false,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        var controller = TextEditingController();
        return SimpleDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5),
          ),
          contentPadding: EdgeInsets.fromLTRB(0, 16, 0, 0),
          title: Center(
            child: Text(
              message,
              style: TextStyle(color: Color.fromRGBO(52, 52, 52, 1.0), fontSize: 17),
            ),
          ),
          children: <Widget>[
            Container(
              alignment: Alignment.topCenter,
              padding: EdgeInsets.fromLTRB(20, 0, 20, 25),
              child: TextField(
                controller: controller,
                style: TextStyle(color: Color.fromRGBO(99, 99, 99, 1.0), fontSize: 15),
              ),
            ),
            Container(
              child: Row(
                children: <Widget>[
                  Expanded(
                    flex: 1,
                    child: Material(
                      borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5)),
                      child: InkWell(
                        onTap: () {
                          NavigatorUtils.goBack(context);
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          child: Text(
                            "取消",
                            style: TextStyle(color: KColor.title, fontSize: 15),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Material(
                      borderRadius: BorderRadius.only(bottomRight: Radius.circular(5)),
                      color: KColor.COLOR_PRIMARY,
                      child: InkWell(
                        onTap: () {
                          NavigatorUtils.goBack(context);
                          callback(controller.text.toString());
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 44,
                          child: Text(
                            "确定",
                            style: TextStyle(color: KColor.title, fontSize: 15),
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            )
          ],
        );
      });
}

void showImagePickerSheet(
  BuildContext context, {
  Function? pickerImageFromCamera,
  Function? pickerImageFromGallery,
}) {
  // showModalBottomSheet(context: context, builder: builder)
  showModalBottomSheet(
      useRootNavigator: false,
      context: context,
      backgroundColor: Colors.transparent,
      // topRadius: Radius.zero,
      builder: (context) {
        return Container(
          // color: Colors.transparent,
          padding: EdgeInsets.fromLTRB(16, 0, 16, 0),
          height: 220,
          child: Column(
            children: [
              Container(
                height: 121,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  color: Colors.white,
                ),
                child: Column(
                  children: [
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        NavigatorUtils.goBack(context);
                        if (pickerImageFromCamera != null) {
                          pickerImageFromCamera();
                        }
                      },
                      child: Container(
                          height: 60,
                          child: Center(
                            child: Text(
                              "拍照",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontWeight: FontWeight.w400, fontSize: 18, color: KColor.curptino_blue),
                            ),
                          )),
                    ),
                    Divider(height: 0.5),
                    GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        NavigatorUtils.goBack(context);
                        if (pickerImageFromGallery != null) {
                          pickerImageFromGallery();
                        }
                      },
                      child: Container(
                          height: 60,
                          child: Center(
                            child: Text(
                              "从手机相册中选取",
                              textAlign: TextAlign.center,
                              style: TextStyle(fontWeight: FontWeight.w400, fontSize: 18, color: KColor.curptino_blue),
                            ),
                          )),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 9),
              GestureDetector(
                onTap: () {
                  NavigatorUtils.goBack(context);
                },
                child: Container(
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(12)),
                      color: Colors.white,
                    ),
                    child: Center(
                      child: Text(
                        "取消",
                        textAlign: TextAlign.center,
                        style: TextStyle(fontWeight: FontWeight.w400, fontSize: 18, color: Colors.black),
                      ),
                    )),
              ),
            ],
          ),
        );
      });
}

// List<ButtonBuilder> toolbarButtons() {
//   return [
//     (node) {
//       return GestureDetector(
//         onTap: () => node.unfocus(),
//         child: Container(
//           padding: EdgeInsets.all(8.0),
//           child: Text(
//             "完成",
//             style: TextStyle(color: Colors.blue),
//           ),
//         ),
//       );
//     }
//   ];
// }

/*服务端返回的格式都为: yyyy-MM-dd HH:mm
* 特殊情况:
* 1. 广东粤语(yue-hant-cn)不支持时间格式化
*
* 返回值为格式化好之后的本地时间字符串
* */
String? utcToLocal(String utcDateString, {String? format}) {
  String formatString = "yyyy-MM-dd HH:mm";
  if (format != null) {
    formatString = format;
  }
  if (utcDateString != null) {
    if (Platform.isAndroid) {
      int milliseconds = DateFormat(formatString).parse(utcDateString, true).millisecondsSinceEpoch +
          Application.currentTimeZone! * 3600 * 1000;
      DateTime utcCreateTime = DateTime.fromMillisecondsSinceEpoch(milliseconds, isUtc: true);
      return DateFormat(formatString).format(utcCreateTime);
    } else {
      try {
        DateTime utcCreateTime = DateFormat(formatString).parse(utcDateString, true).toLocal();
        return DateFormat(formatString).format(utcCreateTime);
      } catch (e) {
        if (Application.deviceRegion.toLowerCase() == 'cn') {
          //目前只发现广东话会异常
          DateTime utcCreateTime = DateFormat(formatString, "zh_CN").parse(utcDateString, true).toLocal();
          return DateFormat(formatString, "zh_CN").format(utcCreateTime);
        } else {
          Log.e('$e');
        }
      }
    }
  }
  return null;
}

/*直接使用DateFormat生成格式化某些语言会有异常*/
String? formatFromDate(DateTime date, {String? format}) {
  String formatString = "yyyy-MM-dd HH:mm";
  if (format != null) {
    formatString = format;
  }
  try {
    return DateFormat(formatString).format(date);
  } catch (e) {
    if (Application.deviceRegion.toLowerCase() == 'cn') {
      //目前只发现广东话会异常
      return DateFormat(formatString, "zh_CN").format(date);
    } else {
      Log.e('$e');
    }
  }
  return null;
}

/*直接使用DateFormat生成格式化某些语言会有异常*/
DateTime? formatToDate(String dateStr, {String? format}) {
  if (dateStr.isEmpty) {
    return null;
  }
  String formatString = "yyyy-MM-dd HH:mm";
  if (format != null) {
    formatString = format;
  }
  try {
    return DateFormat(formatString).parse(dateStr);
  } catch (e) {
    if (Application.deviceRegion.toLowerCase() == 'cn') {
      //目前只发现广东话会异常
      return DateFormat(formatString, "zh_CN").parse(dateStr);
    } else {
      Log.e('$e');
    }
  }
  return null;
}

///VIP开通引导弹窗
Future<void> showCustomOpenVipGuideAlert(BuildContext context,
    {String headImagePath = '',
    String unVipRightDesc = '',
    String vipRightDesc = '',
    String title = '',
    String subTile = '',
    String cancelDes = "",
    String confirmDes = "",
    List<Color> confirmDesColor = const [Color(0xFFF6CA91), Color(0xFFF6CA91)],
    Color confirmDesTitleColor = ThemeColor.COLOR_744B16,
    Color titleColor = ThemeColor.COLOR_78430E,
    bool isShowClose = false,
    bool useRootNavigator = false,
    VoidCallback? closeCallBack,
    VoidCallback? cancelCallBack,
    VoidCallback? confirmCallBack,
    bool needBackPressPop = false,
    bool needCancelPop = true,
    Widget? extraWidget}) {
  if (cancelDes.isEmpty) {
    cancelDes = intlanguage("app100000188", '再想想');
  }
  if (confirmDes.isEmpty) {
    confirmDes = intlanguage("app01511", '立即开通');
  }
  String extension = path.extension(headImagePath);
  return showCupertinoDialog(
      context: context,
      useRootNavigator: useRootNavigator,
      builder: (_) {
        return WillPopScope(
          onWillPop: () async {
            if (needBackPressPop) {
              Navigator.of(context, rootNavigator: useRootNavigator).pop();
              closeCallBack?.call();
            }
            return true;
          },
          child: Container(
            color: ThemeColor.mainTitle.withOpacity(0.35),
            child: Center(
              child: Container(
                width: 270,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0), color: Colors.white),
                clipBehavior: Clip.antiAlias,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Stack(
                      children: [
                        extension == ".png"
                            ? Image.asset(
                                headImagePath,
                                // fit: BoxFit.contain,
                              )
                            : SvgIcon(
                                headImagePath,
                              ),
                        if (isShowClose)
                          PositionedDirectional(
                              top: 10,
                              end: 10,
                              child: GestureDetector(
                                onTap: () {
                                  CustomNavigation.pop();
                                  closeCallBack?.call();
                                },
                                child: SvgIcon(
                                  'assets/images/try_out_vip/try_out_vip_close.svg',
                                ),
                              )),
                        Positioned(
                          left: 0,
                          top: 95,
                          child: Container(
                            width: 135,
                            child: Text(
                              unVipRightDesc,
                              textAlign: TextAlign.center,
                              style:
                                  TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_FF952C),
                            ),
                          ),
                        ),
                        Positioned(
                          left: extension == ".png" ? 125 : 135,
                          top: 95,
                          child: Container(
                            width: 135,
                            child: Text(
                              vipRightDesc,
                              textAlign: TextAlign.center,
                              style:
                                  TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_FF952C),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Padding(
                      padding: const EdgeInsetsDirectional.symmetric(horizontal: 17),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            title,
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 15, fontWeight: FontWeight.w600, color: titleColor),
                          ),
                          if (subTile != '') ...[
                            const SizedBox(
                              height: 5,
                            ),
                            Text(
                              subTile,
                              textAlign: TextAlign.center,
                              style:
                                  TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_999999),
                            )
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            if (needCancelPop) {
                              CustomNavigation.pop();
                            }
                            // 防止动画中立马Push新的页面导致的返回视觉停留
                            Future.delayed(const Duration(milliseconds: 250), () {
                              cancelCallBack?.call();
                            });
                          },
                          child: Container(
                            width: 107,
                            height: 44,
                            decoration: BoxDecoration(
                                color: ThemeColor.COLOR_F5F5F5, borderRadius: BorderRadius.all(Radius.circular(6))),
                            child: Center(
                              child: Text(
                                cancelDes,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w500, color: ThemeColor.COLOR_595959),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        GestureDetector(
                          onTap: () {
                            CustomNavigation.pop();
                            confirmCallBack?.call();
                          },
                          child: Container(
                            width: 107,
                            height: 44,
                            decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                  colors: confirmDesColor,
                                ),
                                borderRadius: BorderRadius.all(Radius.circular(6))),
                            child: Center(
                              child: Text(
                                confirmDes,
                                textAlign: TextAlign.center,
                                style:
                                    TextStyle(fontSize: 16, fontWeight: FontWeight.w500, color: confirmDesTitleColor),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    extraWidget == null
                        ? const SizedBox.shrink()
                        : Padding(
                            padding: const EdgeInsets.only(top: 20),
                            child: extraWidget,
                          ),
                    const SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      });
}

///VIP续费引导弹窗
showCustomRenewVipGuideAlert(
    BuildContext context, String title, VoidCallback cancelCallBack, VoidCallback confirmCallBack,
    {String headImagePath = 'assets/images/my_template/batch_print_vip_expired.svg',
    String subTile = "",
    String cancelDes = "",
    String confirmDes = ""}) {
  if (cancelDes.isEmpty) {
    cancelDes = intlanguage("app00030", '取消');
  }
  if (confirmDes.isEmpty) {
    confirmDes = intlanguage("app01512", '立即续费');
  }
  String extension = path.extension(headImagePath);
  showCupertinoDialog(
      useRootNavigator: false,
      context: context,
      builder: (_) {
        return WillPopScope(
          onWillPop: () async => true,
          child: Container(
            color: ThemeColor.mainTitle.withOpacity(0.35),
            child: Center(
              child: Container(
                width: 270,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(12.0), color: ThemeColor.background),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Stack(
                      children: [
                        Positioned.fill(
                          child: SvgIcon(
                            headImagePath,
                            fit: BoxFit.fill,
                          ),
                        ),
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                Text(
                                  title,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: 16, fontWeight: FontWeight.w600, color: ThemeColor.COLOR_262626),
                                ),
                                const SizedBox(
                                  height: 16,
                                ),
                                subTile.isNotEmpty
                                    ? Text(
                                        subTile,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontSize: 14, fontWeight: FontWeight.w400, color: ThemeColor.COLOR_262626),
                                      )
                                    : Container(),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 24,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context, rootNavigator: false).pop();
                            cancelCallBack.call();
                          },
                          child: Container(
                            width: 107,
                            height: 44,
                            decoration: BoxDecoration(
                                color: ThemeColor.COLOR_F5F5F5, borderRadius: BorderRadius.all(Radius.circular(6))),
                            child: Center(
                              child: Text(
                                cancelDes,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w500, color: ThemeColor.COLOR_595959),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 16,
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context, rootNavigator: false).pop();
                            confirmCallBack.call();
                          },
                          child: Container(
                            width: 107,
                            height: 44,
                            decoration: BoxDecoration(
                                color: ThemeColor.COLOR_F6CA91, borderRadius: BorderRadius.all(Radius.circular(6))),
                            child: Center(
                              child: Text(
                                confirmDes,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w500, color: ThemeColor.COLOR_744B16),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      });
}
