import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_canvas_plugins_interface/plugin/canvas_plugin_manager.dart';
import 'package:niimbot_flutter_canvas/niimbot_flutter_canvas.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element.dart';
import 'package:niimbot_flutter_canvas/src/model/element/text_element_style.dart';
import 'package:text/utils/common_fun.dart';

import '../../application.dart';
import '../../tools/to_Native_Method_Channel.dart';
import 'template_convert_utls.dart';
import 'layout_util.dart';
import 'package:layout_forge/layout_forge_api.dart';
import 'package:uuid/uuid.dart';

class LayoutHelper {
  static final LayoutHelper _instance = LayoutHelper._internal();
  factory LayoutHelper() {
    return _instance;
  }

  LayoutHelper._internal();

  /// 获取布局模板
  /// [template] 模板数据，包含inputAreas、width、height等字段
  /// 返回处理后的模板数据
  Future<Map<String, dynamic>?> getLayoutTemplate(Map<dynamic, dynamic>? template,{int source = 1}) async {
    try {
      // 判空处理
      if (template == null) {
        debugPrint('Template cannot be null');
        return null;
      }
      Map<String, dynamic> templateNew = Map<String, dynamic>.from(template);
      String templateId = DateTime.now().millisecondsSinceEpoch.toString();
      templateNew['id'] = templateId;
      // 1. 从template中取出inputAreas，width，height字段
      final List<dynamic>? inputAreas = LayoutUtil.getInputAreasFromTemplate(templateNew);
      final double width = _parseDoubleValue(templateNew['width']);
      final double height = _parseDoubleValue(templateNew['height']);

      if (width <= 0 || height <= 0) {
        debugPrint('Width and height must be positive values');
        return null;
      }
      Map<String, dynamic> processedTemplate = Map<String, dynamic>.from(templateNew);
      bool isGoodsLabelTemplate =
          templateNew['profile']['extrain']['industryId'].toString() == TemplateConvertUtils.goodsLabelCategaryId;
                  //获取语言方向
      int textDirection = 0;
      String languageCode = Application.currentAppLanguageType;
      if (languageCode.toLowerCase() == "en" || languageCode.toLowerCase() == "ar") {
        bool? status = Application.sp.getBool("drawboard_rtl_switch_status");
        textDirection = (status ?? languageCode.toLowerCase() == "ar") ? 1 : 0;
      }
      if (inputAreas?.isNotEmpty ?? false) {
        if (!isGoodsLabelTemplate) {
          for (Map<String, dynamic> inputArea in inputAreas!) {
            inputArea['productField'] = "";
          }
        }
        // 2. 调用LayoutUtil.initLabelInputAreas获取转换后的区域对象 json
        final Map<String, dynamic> editRegion = LayoutUtil.initLabelInputAreas(inputAreas, width, height);
        debugPrint("版式输入区域:inputAreas:${jsonEncode(inputAreas)},宽:$width 高:$height");
        debugPrint("版式参数:${jsonEncode(editRegion)}");
        // 3. 调用LayoutForgeApi.parseAreaToTemplate，参数为B、width、height获取模板数据result，并将result['elements']赋值到template['elements']
        final UnifiedResponse result = await LayoutForgeAPI.parseAreaToTemplate(
          areaData: AreaData.fromMap(editRegion),
        );
        if (result.isSuccess) {
          Map<String, dynamic> templateJson = jsonDecode(result.templateJson);
          for (var element in templateJson['elements']) {
            element['id'] = Uuid().v4();
            if(element['type'] == 'text'){
              element['textDirection'] = textDirection;
            }
          }
          processedTemplate['elements'] = templateJson['elements'];
          debugPrint("版式输出jsonElement:${jsonEncode(templateJson['elements'])}");
          ToNativeMethodChannel().sendTrackingToNative({
            "track": "show",
            "posCode": "108_460",
            "ext": {'source': source}
          });
        } else {
          Map<String, dynamic> defaultElement = _createDefaultTextElement(
            width,
            height,
            textDirection,
          );
          processedTemplate['elements'] = [defaultElement];
          return processedTemplate;
        }
      } else {
        Map<String, dynamic> defaultElement = _createDefaultTextElement(
          width,
          height,
          textDirection,
        );
        processedTemplate['elements'] = [defaultElement];
        return processedTemplate;
      }
      TemplateData templateData =
          await TemplateConvertUtils.staticTemplateToGoodsDataSourceTemplate(processedTemplate, inputAreas!);
      if (templateData.isGoodsLabelTemplate() || isGoodsLabelTemplate) {
        return templateData.toJson();
      } else if (inputAreas.isNotEmpty) {
        if(inputAreas.length > 1){
          for(JsonElement element in templateData.elements){
            if (element is TextElement) {
              element.textAlignHorizontal = 0;
              element.textAlignVertical = 2;
              element.fontSize = 2.8;
            }
          }
        }else{
          for (JsonElement element in templateData.elements) {
            if (element is TextElement) {
              element.textAlignHorizontal = 1;
              element.textAlignVertical = 2;
              element.fontSize = 4.2;
            }
          }
        }
        return templateData.toJson();
      }
    } catch (e) {
      // 异常处理，返回原始模板或默认模板
      debugPrint('Error in getLayoutTemplate: $e');
      return template != null ? Map<String, dynamic>.from(template) : <String, dynamic>{};
    }
  }

  /// 生成默认文本元素（复用现有逻辑）
  static Map<String, dynamic> _createDefaultTextElement(double width, double height, int textDirection) {
    // 复用现有的文本元素创建逻辑
    double templateWidth = width;
    double templateHeight = height;

    /// 模板宽度不足小五号字显示换行，导致退出时对比判定错误
    double elementHeight = 5.76;
    double x = 1;
    double y = (templateHeight - elementHeight) / 2;
    double elementWidth = templateWidth - x * 2;
    double fontSize = 4.2;
    // String defaultTextValue = intlanguage('app00364', '双击编辑');
    String textStyle = TextElementTextStyle.minimized.value;
    String boxStyle = TextElementBoxStyle.fixedWidthHeight.value;
    Map<String, dynamic> defaultTextElement = {};
    defaultTextElement["id"] = JsonElement.generateId();
    defaultTextElement["x"] = x;
    defaultTextElement["y"] = y;
    defaultTextElement["width"] = elementWidth;
    defaultTextElement["height"] = elementHeight;
    defaultTextElement["zIndex"] = 0;
    defaultTextElement["type"] = "text";
    defaultTextElement["rotate"] = 0;
    defaultTextElement["isLock"] = 0;
    defaultTextElement["fieldName"] = "";
    defaultTextElement["mirrorId"] = "";
    defaultTextElement["isOpenMirror"] = 0;
    defaultTextElement["mirrorType"] = 0;
    defaultTextElement["isBinding"] = 0;
    defaultTextElement["bindingColumn"] = 0;
    defaultTextElement["value"] = "";
    defaultTextElement["typesettingMode"] = 1;
    defaultTextElement["typesettingParam"] = [0, 180];
    defaultTextElement["textAlignHorizonral"] = 1;
    defaultTextElement["textAlignVertical"] = 2; // 6.0.4版本更换为顶对齐
    defaultTextElement["lineMode"] = 2;
    defaultTextElement["wordSpacing"] = 0.0;
    defaultTextElement["letterSpacing"] = 0.0;
    defaultTextElement["lineSpacing"] = 0.0;
    defaultTextElement["fontStyle"] = [];
    defaultTextElement["fontSize"] = fontSize;
    defaultTextElement["fontColor"] = null;
    defaultTextElement["isTitle"] = false;
    defaultTextElement["contentTitle"] = "";
    defaultTextElement["lineBreakMode"] = 1;
    defaultTextElement["textDirection"] = textDirection;
    defaultTextElement["elementColor"] = [255, 0, 0, 0];
    defaultTextElement["boxStyle"] = boxStyle;
    defaultTextElement["textStyle"] = [textStyle];
    return defaultTextElement;
  }

  List<EditableRegion> getEditableRegion(List<dynamic>? inputAreas) {
    final List<EditableRegion> editableRegions = [];
    if (inputAreas == null || inputAreas.isEmpty) {
      return editableRegions;
    }
    for (var item in inputAreas) {
      if (item["name"] == null) {
        item["name"] = intlanguage('app00364', '双击编辑');
      }
      editableRegions.add(EditableRegion.fromMap(Map<String, dynamic>.from(item)));
    }
    return editableRegions;
  }

  Future<List<String>> getGoodFieldInfoList() async {
    final goodsImportImpl = CanvasPluginManager().goodsImportImpl;
    Completer<List<String>> completer = Completer<List<String>>();
    goodsImportImpl?.getGoodsField(null, (data) {
      try {
        // 将数据转换为Map列表
        List<Map<String, dynamic>> fieldInfoList = data.map((e) => Map<String, dynamic>.from(e)).toList();

        // 按columnIndex字段正序排序
        fieldInfoList.sort((a, b) {
          int columnIndexA = a['columnIndex'] ?? 0;
          int columnIndexB = b['columnIndex'] ?? 0;
          return columnIndexA.compareTo(columnIndexB);
        });

        // 提取fieldName字段组成数组
        List<String> fieldNames = fieldInfoList
            .map((item) => intlanguage(item['fieldName']?.toString() ?? '', ''))
            .where((fieldName) => fieldName.isNotEmpty)
            .toList();

        completer.complete(fieldNames);
      } catch (e) {
        debugPrint('解析商品字段信息失败: $e');
        completer.complete([]);
      }
    }, (errorCode, errorMsg) {
      debugPrint('获取商品字段信息失败: $errorCode - $errorMsg');
      completer.complete([]);
    });
    return completer.future;
  }

  /// 解析动态值为double类型
  /// [value] 待解析的值
  /// 返回解析后的double值，如果解析失败返回0.0
  double _parseDoubleValue(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? 0.0;
    }
    return 0.0;
  }
}
