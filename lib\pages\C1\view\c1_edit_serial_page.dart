import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:niimbot_template/models/elements/element_enum.dart';
import 'package:niimbot_template/models/values/serial_value_type.dart';
import 'package:niimbot_template/models/values/template_value_type.dart';
import 'package:text/pages/C1/common/add_subtract_text_group.dart';
import 'package:text/pages/C1/common/items_selector_popmenu_widget.dart';
import 'package:text/pages/C1/common/toast_util.dart';
import 'package:text/routers/custom_navigation.dart';
import 'package:text/utils/common_fun.dart';
import 'package:text/utils/item_divider.dart';
import 'package:text/utils/theme_color.dart';

typedef CheckAddEditCondition = bool Function(int serialCounter, BuildContext context);

class C1EditSerialPage extends StatefulWidget {
  final SerialValueType serialValueType;
  final bool adjustKeyboard;
  final CheckAddEditCondition checkAddEditCondition;

  const C1EditSerialPage(
      {super.key, required this.serialValueType, required this.adjustKeyboard, required this.checkAddEditCondition});

  @override
  State<C1EditSerialPage> createState() => _C1EditSerialPageState();
}

class _C1EditSerialPageState extends State<C1EditSerialPage> {
  final List<String> _formatItems = ["0~999", "00~99", "000~999", "A~Z", "a~z"];
  late SerialValueType _serialValueType;
  late TextEditingController _startNumberTextController;
  late TextEditingController _endNumberTextController;
  late TextEditingController _incrementValueTextController;
  late DigitalInputFormat _digitalInputFormat;
  CapitalLetterInputFormat _capitalLetterInputFormat = CapitalLetterInputFormat();
  SmallLetterInputFormat _smallLetterInputFormat = SmallLetterInputFormat();
  late IncrementValueInputFormat _incrementValueInputFormat;
  FocusNode _startNumberFocusNode = FocusNode();
  bool _serialValid = false;
  bool _adjustKeyboard = false;

  @override
  void initState() {
    super.initState();
    _adjustKeyboard = widget.adjustKeyboard;
    _serialValueType = SerialValueType.fromJson(widget.serialValueType.toJson());
    _serialValid = _checkSerialValid();
    _initTextController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _adjustKeyboard = true;
      _startNumberFocusNode.requestFocus();
    });
  }

  _initTextController() {
    int? incrementValue = _serialValueType.incrementValue;
    _startNumberTextController = TextEditingController(text: _getFormatStartNumber());
    _endNumberTextController = TextEditingController(text: _getFormatEndNumber());
    _incrementValueTextController = TextEditingController(text: incrementValue?.toString() ?? "");
    _digitalInputFormat = DigitalInputFormat(_currentSerialValueType);
    _incrementValueInputFormat = IncrementValueInputFormat(_currentSerialValueType);
  }

  String _getFormatStartNumber() {
    int? startNumber = _serialValueType.startNumber;
    String? format = _serialValueType.format;
    String fixValue = _serialValueType.fixValue?.toString() ?? "";
    int fixLength = _serialValueType.fixLength ?? 0;
    if (startNumber == null) {
      return "";
    }
    if (format?.isNotEmpty == true) {
      return String.fromCharCode(startNumber);
    } else {
      if (fixLength > 0 && fixValue.isNotEmpty) {
        return startNumber.toString().padLeft(fixLength, fixValue);
      } else {
        return startNumber.toString();
      }
    }
  }

  String _getFormatEndNumber() {
    int? endNumber = _serialValueType.endNumber;
    String? format = _serialValueType.format;
    String fixValue = _serialValueType.fixValue?.toString() ?? "";
    int fixLength = _serialValueType.fixLength ?? 0;
    if (endNumber == null) {
      return "";
    }
    if (format?.isNotEmpty == true) {
      return String.fromCharCode(endNumber);
    } else {
      if (fixLength > 0 && fixValue.isNotEmpty) {
        return endNumber.toString().padLeft(fixLength, fixValue);
      } else {
        return endNumber.toString();
      }
    }
  }

  SerialValueType _currentSerialValueType() {
    return _serialValueType;
  }

  TextInputFormatter get currentInputFormat {
    String? format = _serialValueType.format;
    if (format == SerialElementFormat.capitalLetter.name) {
      return _capitalLetterInputFormat;
    }
    if (format == SerialElementFormat.smallLetter.name) {
      return _smallLetterInputFormat;
    }
    return _digitalInputFormat;
  }

  @override
  void dispose() {
    super.dispose();
    _startNumberTextController.dispose();
    _incrementValueTextController.dispose();
    _endNumberTextController.dispose();
    _startNumberFocusNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // double bottom;
    // if (_adjustKeyboard) {
    //   bottom = min(MediaQuery.of(context).viewInsets.bottom, 200);
    // } else {
    //   bottom = 200;
    // }
    return PopScope(
      canPop: false,
      child: KeyboardVisibilityBuilder(builder: (_, bool isKeyboardVisible) {
        return Container(
          color: ThemeColor.background,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _barWidget(),
              ItemDivider(),
              Container(
                  child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _serialType(),
                  _serialValue(),
                  _serialSample(),
                  SizedBox(
                    height: isKeyboardVisible ? MediaQuery.viewInsetsOf(context).bottom : 100,
                  )
                ],
              ))
            ],
          ),
        );
      }),
    );
  }

  Widget _barWidget() {
    return Container(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Text(
              intlanguage('app100000692', '取消'),
              style: TextStyle(color: ThemeColor.title, fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Center(
              child: Padding(
                padding: EdgeInsetsDirectional.symmetric(horizontal: 6),
                child: Text(
                  intlanguage('app100001619', '设置序号'),
                  style: TextStyle(
                    fontSize: 17,
                    color: ThemeColor.title,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              if (!_checkSerialValid(showToast: true)) {
                return;
              }
              //插入或编辑序号后，该段落所有序号的排列组合不能大于1000段
              int serialCounter = _serialValueType.getSerialCount();
              if(!widget.checkAddEditCondition(serialCounter, context)) {
                return;
              }
              String format = _formatItems[_getSerialFormatIndex()];
              CustomNavigation.pop(result: {"serial": _serialValueType, "format": format});
            },
            child: Text(
              intlanguage('app00048', '确定'),
              style: TextStyle(color: ThemeColor.brand, fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  ///序号类型
  Widget _serialType() {
    return Container(
      decoration: BoxDecoration(color: ThemeColor.greyBackground, borderRadius: BorderRadius.all(Radius.circular(12))),
      margin: EdgeInsetsDirectional.only(start: 16, top: 10, end: 16),
      child: Padding(
        padding: const EdgeInsetsDirectional.symmetric(horizontal: 12),
        child: Row(
          children: [
            Text(
              intlanguage('app100001698', '序号类型'),
              style: TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
            ),
            Expanded(
              child: ItemsSelectorPopUpWidget(
                  popHeight: 50 * _formatItems.length,
                  dropDown: true,
                  anchorEdgeInsets: EdgeInsetsDirectional.symmetric(horizontal: 0, vertical: 12),
                  checkKeyboardStatus: true,
                  initKeyboardStatus: true,
                  items: _formatItems,
                  initializeIndex: _getSerialFormatIndex(),
                  itemsSelectedChanged: (int selectedIndex) async {
                    if (selectedIndex == _getSerialFormatIndex()) {
                      return;
                    }
                    String? id = _serialValueType.id;
                    String? elementId = _serialValueType.elementId;
                    TemplateValueType? type = _serialValueType.type;
                    int incrementValue = 1;
                    int startNumber;
                    int? fixValue;
                    int? fixLength;
                    String? format;
                    if (selectedIndex == 4) {
                      startNumber = 'a'.codeUnitAt(0);
                      format = SerialElementFormat.smallLetter.name;
                    } else if (selectedIndex == 3) {
                      startNumber = 'A'.codeUnitAt(0);
                      format = SerialElementFormat.capitalLetter.name;
                    } else {
                      startNumber = 1;
                      fixValue = 0;
                      if (selectedIndex == 2) {
                        fixLength = 3;
                      } else if (selectedIndex == 1) {
                        fixLength = 2;
                      } else {
                        fixLength = 1;
                      }
                    }
                    _serialValueType = SerialValueType(
                        id: id,
                        elementId: elementId,
                        type: type,
                        startNumber: startNumber,
                        incrementValue: incrementValue,
                        fixValue: fixValue,
                        fixLength: fixLength,
                        format: format);
                    _serialValid = _checkSerialValid();
                    _startNumberTextController.text = _getFormatStartNumber();
                    _incrementValueTextController.text = incrementValue.toString();
                    _endNumberTextController.text = "";
                    setState(() {});
                  }),
            ),
          ],
        ),
      ),
    );
  }

  ///起始值、递增值和结束值
  Widget _serialValue() {
    String format = _serialValueType.format ?? "";
    int fixLength = _serialValueType.fixLength ?? 0;
    int maxValue = format.isNotEmpty || fixLength > 1 ? _serialValueType.getIncrementValueMax() : 999;
    return Container(
      decoration: BoxDecoration(color: ThemeColor.greyBackground, borderRadius: BorderRadius.all(Radius.circular(12))),
      margin: EdgeInsetsDirectional.only(start: 16, top: 10, end: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    intlanguage('app01069', '起始值'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 14.0, color: ThemeColor.COLOR_161616, fontWeight: FontWeight.w400),
                  ),
                ),
                // Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(vertical: 5),
                  width: 149,
                  child: Container(
                    height: 34,
                    decoration: BoxDecoration(
                      color: Color(0xFFECECEC),
                      borderRadius: BorderRadius.all(Radius.circular(6)),
                    ),
                    child: Center(
                      child: TextField(
                        style: const TextStyle(
                            fontSize: 14.0, color: ThemeColor.COLOR_262626, fontWeight: FontWeight.w400),
                        decoration: InputDecoration(
                            hintText: intlanguage('app100001694', '请输入起始值'),
                            hintStyle: TextStyle(fontSize: 14.0, color: ThemeColor.COLOR_FFBFBFBF),
                            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                            counterText: '',
                            enabledBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(6)), borderSide: BorderSide.none),
                            focusedBorder: const OutlineInputBorder(
                                borderRadius: BorderRadius.all(Radius.circular(6)), borderSide: BorderSide.none)),
                        maxLength: format.isNotEmpty ? 1 : (fixLength == 2 ? 2 : 3),
                        maxLengthEnforcement: MaxLengthEnforcement.enforced,
                        keyboardType: format.isNotEmpty ? TextInputType.visiblePassword : TextInputType.number,
                        textCapitalization: format == SerialElementFormat.capitalLetter.name
                            ? TextCapitalization.characters
                            : TextCapitalization.none,
                        cursorColor: ThemeColor.brand,
                        focusNode: _startNumberFocusNode,
                        controller: _startNumberTextController,
                        inputFormatters: [currentInputFormat],
                        onChanged: (text) {
                          int? startNumber;
                          if (_serialValueType.format?.isNotEmpty == true) {
                            if (text.isNotEmpty) {
                              startNumber = text.codeUnitAt(0);
                            }
                          } else {
                            startNumber = int.tryParse(text);
                          }
                          _serialValueType = SerialValueType(
                              id: _serialValueType.id,
                              elementId: _serialValueType.elementId,
                              type: _serialValueType.type,
                              startNumber: startNumber,
                              incrementValue: _serialValueType.incrementValue,
                              endNumber: _serialValueType.endNumber,
                              fixValue: _serialValueType.fixValue,
                              fixLength: _serialValueType.fixLength,
                              format: _serialValueType.format);
                          _serialValid = _checkSerialValid();
                          setState(() {});
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          ItemDivider(),
          Padding(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    intlanguage('app00509', '递增值'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 14.0, color: ThemeColor.COLOR_161616, fontWeight: FontWeight.w400),
                  ),
                ),
                AddSubTractTextGroup(
                  style: const TextStyle(color: ThemeColor.title, fontSize: 14, fontWeight: FontWeight.w400),
                  defaultValue: 1,
                  minValue: 1,
                  maxValue: maxValue,
                  maxLength: 3,
                  textEditingController: _incrementValueTextController,
                  inputFormatters: [_incrementValueInputFormat],
                  onTextChanged: (text) {
                    int? incrementValue = int.tryParse(text);
                    _serialValueType = SerialValueType(
                        id: _serialValueType.id,
                        elementId: _serialValueType.elementId,
                        type: _serialValueType.type,
                        startNumber: _serialValueType.startNumber,
                        incrementValue: incrementValue,
                        endNumber: _serialValueType.endNumber,
                        fixValue: _serialValueType.fixValue,
                        fixLength: _serialValueType.fixLength,
                        format: _serialValueType.format);
                    _serialValid = _checkSerialValid();
                    setState(() {});
                  },
                )
              ],
            ),
          ),
          ItemDivider(),
          Padding(
            padding: EdgeInsetsDirectional.symmetric(horizontal: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    intlanguage('app100001699', '结束值'),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(fontSize: 14.0, color: ThemeColor.COLOR_161616, fontWeight: FontWeight.w400),
                  ),
                ),
                Container(
                    padding: EdgeInsets.symmetric(vertical: 5),
                    width: 149,
                    child: Container(
                      height: 34,
                      decoration: BoxDecoration(
                        color: Color(0xFFECECEC),
                        borderRadius: BorderRadius.all(Radius.circular(6)),
                      ),
                      child: Center(
                        child: TextField(
                          style: const TextStyle(
                              fontSize: 14.0, color: ThemeColor.COLOR_262626, fontWeight: FontWeight.w400),
                          decoration: InputDecoration(
                              hintText: intlanguage('app100001695', '请输入结束值'),
                              hintStyle: TextStyle(fontSize: 14.0, color: ThemeColor.COLOR_FFBFBFBF),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                              counterText: '',
                              enabledBorder: const OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(6)), borderSide: BorderSide.none),
                              focusedBorder: const OutlineInputBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(6)), borderSide: BorderSide.none)),
                          maxLength: format.isNotEmpty ? 1 : (fixLength == 2 ? 2 : 3),
                          maxLengthEnforcement: MaxLengthEnforcement.enforced,
                          keyboardType: format.isNotEmpty ? TextInputType.visiblePassword : TextInputType.number,
                          textCapitalization: format == SerialElementFormat.capitalLetter.name
                              ? TextCapitalization.characters
                              : TextCapitalization.none,
                          cursorColor: ThemeColor.brand,
                          controller: _endNumberTextController,
                          inputFormatters: [currentInputFormat],
                          onChanged: (text) {
                            int? endNumber;
                            if (_serialValueType.format?.isNotEmpty == true) {
                              if (text.isNotEmpty) {
                                endNumber = text.codeUnitAt(0);
                              }
                            } else {
                              endNumber = int.tryParse(text);
                            }
                            _serialValueType = SerialValueType(
                                id: _serialValueType.id,
                                elementId: _serialValueType.elementId,
                                type: _serialValueType.type,
                                startNumber: _serialValueType.startNumber,
                                incrementValue: _serialValueType.incrementValue,
                                endNumber: endNumber,
                                fixValue: _serialValueType.fixValue,
                                fixLength: _serialValueType.fixLength,
                                format: _serialValueType.format);
                            _serialValid = _checkSerialValid();
                            setState(() {});
                          },
                        ),
                      ),
                    )),
              ],
            ),
          )
        ],
      ),
    );
  }

  ///生成序列号示例
  Widget _serialSample() {
    String text = "";
    if (_serialValid) {
      List<String> values = _getSerialValues();
      String count = intlanguage("app100001700", "共生成\$个序列号：", param: [values.length.toString()]);
      String detail = "";
      int length = values.length;
      if (length <= 6) {
        for (int i = 0; i < length; i++) {
          if (i != length - 1) {
            detail = detail + "${values[i]}、";
          } else {
            detail = detail + values[i];
          }
        }
      } else {
        detail =
            "${values[0]}、${values[1]}、${values[2]} ... ${values[length - 3]}、${values[length - 2]}、${values[length - 1]}";
      }
      text = count + detail;
    }
    return Container(
      margin: EdgeInsetsDirectional.symmetric(horizontal: 28, vertical: 8),
      constraints: BoxConstraints(minHeight: 20),
      child:
          Text(text, style: const TextStyle(fontSize: 12.0, color: ThemeColor.subtitle, fontWeight: FontWeight.w400)),
    );
  }

  List<String> _getSerialValues() {
    List<String> values = [];
    int serialCount = _serialValueType.getSerialCount();
    for (int i = 1; i <= serialCount; i++) {
      String value = _serialValueType.generateElementValue(index: i)!;
      values.add(value);
    }
    return values;
  }

  int _getSerialFormatIndex() {
    String? serialFormat = _serialValueType.format;
    if (serialFormat == SerialElementFormat.capitalLetter.name) {
      return 3;
    }
    if (serialFormat == SerialElementFormat.smallLetter.name) {
      return 4;
    }
    if (_serialValueType.fixLength == 3) {
      return 2;
    } else if (_serialValueType.fixLength == 2) {
      return 1;
    }
    return 0;
  }

  bool _checkSerialValid({bool showToast = false}) {
    int? startNumber = _serialValueType.startNumber;
    int? endNumber = _serialValueType.endNumber;
    int? incrementValue = _serialValueType.incrementValue;
    if (startNumber == null) {
      if (showToast) {
        ToastUtil.showToast(context, intlanguage('app100001694', '请输入起始值'));
      }
      return false;
    }
    if (endNumber == null) {
      if (showToast) {
        ToastUtil.showToast(context, intlanguage('app100001695', '请输入结束值'));
      }
      return false;
    }
    if (startNumber > endNumber) {
      if (showToast) {
        ToastUtil.showToast(context, intlanguage('app100001697', '起始值不能大于结束值'));
      }
      return false;
    }
    if (incrementValue == null) {
      if (showToast) {
        ToastUtil.showToast(context, intlanguage('app100001696', '请输入递增值'));
      }
      return false;
    }
    return true;
  }
}

class DigitalInputFormat extends TextInputFormatter {
  final SerialValueType Function() currentSerialValueType;

  DigitalInputFormat(this.currentSerialValueType);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      int? digital = int.tryParse(text);
      if (digital == null) {
        return oldValue;
      }
      int? fixLength = currentSerialValueType().fixLength;
      if (fixLength == 2) {
        if (text.length > 2) {
          return oldValue;
        }
      } else {
        if (text.length > 3) {
          return oldValue;
        }
      }
    }
    return newValue;
  }
}

class CapitalLetterInputFormat extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      if (text.length == 1) {
        if (RegExp('[A-Z]').hasMatch(text)) {
          return newValue;
        }
        if (RegExp('[a-z]').hasMatch(text)) {
          String newText = text.toUpperCase();
          TextEditingValue textEditingValue =
              TextEditingValue(text: newText, selection: TextSelection.collapsed(offset: newText.length));
          return textEditingValue;
        }
      }
      return oldValue;
    }
    return newValue;
  }
}

class SmallLetterInputFormat extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      if (text.length == 1) {
        if (RegExp('[a-z]').hasMatch(text)) {
          return newValue;
        }
        if (RegExp('[A-Z]').hasMatch(text)) {
          String newText = text.toLowerCase();
          TextEditingValue textEditingValue =
              TextEditingValue(text: newText, selection: TextSelection.collapsed(offset: newText.length));
          return textEditingValue;
        }
      }
      return oldValue;
    }
    return newValue;
  }
}

class IncrementValueInputFormat extends TextInputFormatter {
  final SerialValueType Function() currentSerialValueType;

  IncrementValueInputFormat(this.currentSerialValueType);

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String text = newValue.text;
    if (text.length > 0) {
      try {
        int num = int.parse(text);
        String format = currentSerialValueType().format ?? "";
        int fixLength = currentSerialValueType().fixLength ?? 0;
        int incrementValueMax = format.isNotEmpty || fixLength > 1 ? currentSerialValueType().getIncrementValueMax() : 999;
        if (num >= 1 && num <= incrementValueMax) {
          return newValue;
        }
      } catch (e) {}
      return oldValue;
    }
    return newValue;
  }
}
