#import "AppDelegate.h"
#import "JCShareTool.h"
#import "YKWoodpecker.h"
#import "JCStartViewController.h"
#import <AlipaySDK/AlipaySDK.h>
//百度地图///
#import "ImageLibraryBridge.h"
#import "ZXCountDownDefine.h"
#import "JGOneKeyLoginManager.h"
#import <CoreTelephony/CTCarrier.h>
#import <CoreTelephony/CTTelephonyNetworkInfo.h>
#import "GeneratedPluginRegistrant.h"
#import "JCNotificationMessageModel.h"
#import "JCAppEventChannel.h"
#import "JCAppBasicMessageChannel.h"
#define APPID_VALUE           @"5aaf2c59"
#import "JCFlutterManager.h"
#import "IFlyMSC/IFlyMSC.h"
#import "JCAppStartConfig.h"
#import "WXApi.h"
#import <TencentOpenAPI/TencentOAuth.h>
#import <TencentOpenAPI/QQApiInterfaceObject.h>
#import <TencentOpenAPI/QQApiInterface.h>
#import "AFHTTPSessionManager+JCCertificate.h"
#import <flutter_boost/FlutterBoost.h>
#import "Runner-swift.h"
#import "NBCAPMiniAppEngine.h"
#import "AppDelegate+Sentry.h"
#import "NSString+MessageType.h"
#import "FlutterNetSignatureUrlHeaderFilter.h"
#import <JailBrokenDetection/JailBrokenDetection.h>
#import "JCOfflineTimeCheckHelper.h"
#import "JCWebviewPreloadManager.h"
#import "JCPrintDoneAdManager.h"
#import "FlutterBoostUtility.h"
#import "JCLoginManager.h"
// 引入 JPush 功能所需头文件
# import "JPUSHService.h"
// iOS10 注册 APNs 所需头文件
# ifdef NSFoundationVersionNumber_iOS_9_x_Max
# import <UserNotifications/UserNotifications.h>
# endif
#import <PushKit/PushKit.h>
#import <Sentry/Sentry.h>
#import "JCBluetoothManager+Connect.h"
#import "JCTemplateDBManager.h"
#import "JCTemplateData4DBManager.h"

#define SentryDSN @"https://<EMAIL>/4508849207574608"

@interface NBAppDelegate () <BuglyDelegate,WXApiDelegate,TencentSessionDelegate,JPUSHRegisterDelegate,PKPushRegistryDelegate>
{
    NSInteger count;
}
@property(strong, nonatomic) NSTimer *mTimer;
@property(strong, nonatomic) NSString *outSechemUrl;
@property(assign, nonatomic) UIBackgroundTaskIdentifier backIden;
// 如果用户显示打开显示广告页，但是从推送点击进来打开应用，则也不显示广告页
@property (nonatomic, assign) BOOL needShowAd;
@end

@implementation NBAppDelegate

- (BOOL)application:(UIApplication *)application
didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    self.outSechemUrl = nil;
    self.appLaunched = NO;
    self.needShowAd = YES;

    // 重置手动断开连接标志，以便新会话能够自动连接
    [[NSUserDefaults standardUserDefaults] removeObjectForKey:MANUAL_DISCONNECT_FLAG];

    NSDictionary *remoteNotification = [launchOptions objectForKey: UIApplicationLaunchOptionsRemoteNotificationKey];
    self.pendingNotification = remoteNotification;
    if(remoteNotification) {
        self.needShowAd = NO;
    }
    self.engines = [[FlutterEngineGroup alloc] initWithName:@"multiple-flutters" project:nil];
    //    [FlutterSingleton shared];
    [self initHttpsCertificateConfig];
    JCLaunchScreenManager * manager = [JCLaunchScreenManager shareInstance];
    [self netWorkConfig];
    [self appLocalConfig];
    [self setUIRTLConfig];
    [self initSDWebImageConfig];
    [self initAppStartPageRoute:launchOptions];
    self.application = application;
    self.launchOptions = launchOptions;
    [self initThirdLogin];//
    [self thirdAppConfig:application didFinishLaunchingWithOptions:launchOptions];
    [self initUniAppWith:application didFinishLaunchingWithOptions:launchOptions];
    [JCRecordTool recordWithAction: AppStart withContent:@"app_start" isClickEvent:YES];
    JC_TrackWithparms(@"appstart",@"001",@{});
    BOOL isUserAgreedAuthorization = [TencentOAuth isUserAgreedAuthorization];
    // 更新到SDK中，用户是否同意隐私协议
    [TencentOAuth setIsUserAgreedAuthorization:!isUserAgreedAuthorization];
    [[TencentOAuth alloc] initWithAppId:@"101553333" andUniversalLink:@"https://www.niimbot.com" andDelegate:self]; //QQ注册
    //创建代理，做初始化操作
    BoostDelegate *boostDelegate = [[BoostDelegate alloc] init];
    [[FlutterBoost instance] setup:application delegate:boostDelegate callback:^(FlutterEngine *engine) {
        self.flutterEngine = engine;
        [[JCAppEventChannel shareInstance] registerNativeChannelByEngine:engine];
        [[JCAppMethodChannel shareInstance] registerChannelByEngine:engine];
        [[JCAppBasicMessageChannel shareInstance] registerNativeChannelByEngine:engine];
    }];
    // 等待通道建立完成
    [self sendTrackAndPrintData];
    [WXApi registerApp:@"wxece69872671e1254" universalLink:@"https://www.niimbot.com"];

    // 监听语言切换，对于LTR和RTL处理
    XYWeakSelf
    [[NSNotificationCenter defaultCenter] addObserverForName:@"isNeedFreshGLobalAttribute" object:nil queue:NSOperationQueue.mainQueue usingBlock:^(NSNotification * _Nonnull note) {
        // 保存当前导航栈状态
        UITabBarController *tabBarController = (UITabBarController *)weakSelf.window.rootViewController;
        UINavigationController *mainNav = tabBarController.selectedViewController;
        NSMutableArray *savedVCClassNames = [NSMutableArray array];
        
        // 保存当前选中的导航控制器的根控制器类名
        NSString *rootVCClassName = NSStringFromClass([mainNav.viewControllers.firstObject class]);
        
        // 从第二个控制器开始保存类名
        for (NSInteger i = 1; i < mainNav.viewControllers.count; i++) {
            UIViewController *vc = mainNav.viewControllers[i];
            [savedVCClassNames addObject:NSStringFromClass([vc class])];
        }
        
        // 销毁当前的 TabBarController
        weakSelf.mainVC = nil;
        
        // 恢复之前的状态
        dispatch_async(dispatch_get_main_queue(), ^{
          weakSelf.window.rootViewController = weakSelf.mainVC;
            UITabBarController *newTabBarController = (UITabBarController *)weakSelf.window.rootViewController;
            
            // 根据根控制器类名找到对应的导航控制器
            for (NSInteger i = 0; i < newTabBarController.viewControllers.count; i++) {
                UIViewController *vc = newTabBarController.viewControllers[i];
                if ([vc isKindOfClass:[UINavigationController class]]) {
                    UINavigationController *nav = (UINavigationController *)vc;
                    if (nav.viewControllers.count > 0 && 
                        [NSStringFromClass([nav.viewControllers.firstObject class]) isEqualToString:rootVCClassName]) {
                        newTabBarController.selectedIndex = i;
                        
                        // 重新创建并添加保存的视图控制器
                        NSMutableArray *newViewControllers = [NSMutableArray arrayWithArray:nav.viewControllers];
                        for (NSString *className in savedVCClassNames) {
                            Class vcClass = NSClassFromString(className);
                            if (vcClass) {
                                UIViewController *newVC = [[vcClass alloc] init];
                                [newViewControllers addObject:newVC];
                            }
                        }
                        
                        // 先设置第一个控制器，确保生命周期方法被调用
                        [nav setViewControllers:@[newViewControllers.firstObject] animated:NO];
                        
                        // 然后逐个添加其他控制器，确保每个控制器的生命周期方法都被调用
                        for (NSInteger i = 1; i < newViewControllers.count; i++) {
                            [nav pushViewController:newViewControllers[i] animated:NO];
                        }
                    }
                }
            }
        });
    }];

    [self checkJailbroken];
    // 商城站点信息初始化获取
    [self initShopSite];

    // 极光推送初始化
    [self initJPushHandle:launchOptions];

//    [self syncFileToisar];

    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

- (void)initJPushHandle:(NSDictionary *)launchOptions {
    NSString *isAgreeUserSecret = [[NSUserDefaults standardUserDefaults] objectForKey:@"isAgreeUserSecret"];
    if([isAgreeUserSecret isEqualToString:@"1"]) {
        [self initJPush:launchOptions];
    }
}

/// 初始化极光推送
- (void)initJPush:(NSDictionary *)launchOptions {
    //Required
    //notice: 3.0.0 及以后版本注册可以这样写，也可以继续用之前的注册方式
    JPUSHRegisterEntity * entity = [[JPUSHRegisterEntity alloc] init];
    //    entity.types = JPAuthorizationOptionAlert|JPAuthorizationOptionBadge|JPAuthorizationOptionSound|JPAuthorizationOptionProvidesAppNotificationSettings;
//    entity.types = JPAuthorizationOptionAlert|JPAuthorizationOptionSound/*|JPAuthorizationOptionProvidesAppNotificationSettings*/;
    if ([[UIDevice currentDevice].systemVersion floatValue] >= 8.0) {
        // 可以添加自定义 categories
        // NSSet<UNNotificationCategory *> *categories for iOS10 or later
        // NSSet<UIUserNotificationCategory *> *categories for iOS8 and iOS9
    }
    [JPUSHService registerForRemoteNotificationConfig:entity delegate:self];
//
    #if DEBUG
    [JPUSHService setupWithOption:launchOptions appKey:@"f3725414ce59593f7ca894e1"
                          channel:@"developer-default"
                 apsForProduction:NO
            advertisingIdentifier:@""];
    #endif
    //初始化极光推送服务，调用了本 API 后，开启 JPush 推送服务，将会开始收集上报 SDK 业务功能所必要的用户个人信息
    [JPUSHService setupWithOption:launchOptions appKey:@"f3725414ce59593f7ca894e1"
                          channel:@"developer-default"
                 apsForProduction:YES
            advertisingIdentifier:@""];
}
# pragma mark- JPUSHRegisterDelegate
/// Required - 注册 DeviceToken
- (void)application:(UIApplication *)application
didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    [JPUSHService registerDeviceToken:deviceToken];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error {
    //Optional
    NSLog(@"did Fail To Register For Remote Notifications With Error: %@", error);
}

/// 系统返回VoipToken,上报给极光服务器
- (void)pushRegistry:(PKPushRegistry *)registry didUpdatePushCredentials:(PKPushCredentials *)pushCredentials forType:(PKPushType)type{
    [JPUSHService registerVoipToken:pushCredentials.token];
}

// iOS 12 Support
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center openSettingsForNotification:(UNNotification *)notification{
    if (notification && [notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
        //从通知界面直接进入应用
    }else{
        //从通知设置界面进入应用
    }
}
- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    // Required, iOS 7 Support
    [JPUSHService handleRemoteNotification:userInfo];
    completionHandler(UIBackgroundFetchResultNewData);
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo {
    // Required, For systems with less than or equal to iOS 6
    [JPUSHService handleRemoteNotification:userInfo];
}

///App 在前台运行
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(NSInteger options))completionHandler {
    NSDictionary *userInfo = notification.request.content.userInfo;
    // 判断是否来自极光
    if ([userInfo objectForKey:@"_j_business"]) {
        // 应用处于前台时拦截通知
        completionHandler(UNNotificationPresentationOptionNone);
    } else {
        // 允许系统弹出通知
        completionHandler(UNNotificationPresentationOptionAlert |
                          UNNotificationPresentationOptionSound);
    }

}

//App 在后台时（需要点击通知才能触发回调）
- (void)jpushNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void(^)(void))completionHandler {
    JCNotificationMessageModel *model = [JCNotificationMessageModel modelFromDictionary:response.notification.request.content.userInfo];
    /*
     活动优惠：跳到一级列表，再点击跳到后台配的链接ii.用户调研：跳到用户调研的二级列表，再点击跳到后台配的链接
     用户调研：跳到用户调研的二级列表，再点击跳到后台配的链接
     服务提醒：跳到服务提醒的二级列表，再点击跳到对应的续费页面（会员中心/线缆小程序）
     */
    MessageType messageType = [model.params.category toMessageType];
    if(self.pendingNotification) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self handlePageNavigationWithMessageType:messageType currentVC:[XYTool getCurrentVC] needToDelay:YES];
            self.needShowAd = YES;
            self.pendingNotification = nil;
        });
    }else {
        [self handlePageNavigationWithMessageType:messageType currentVC:[XYTool getCurrentVC] needToDelay:NO];
    }
    completionHandler();
}

- (void)handlePageNavigationWithMessageType:(MessageType)messageType currentVC:(UIViewController *)currentVC needToDelay:(BOOL)delay{
    XYWeakSelf
    if (!xy_isLogin) {
        [[JCLoginManager sharedInstance] checkLogin:^{
        } viewController:nil loginSuccessBlock:^{
            [self goToMessageCenterWithMessageType:messageType needToDelay:YES];
        }];
        return;
    }
    // 登录后处理页面跳转
    if ([currentVC isKindOfClass:[FBFlutterViewContainer class]]) {
        FBFlutterViewContainer *flutterVC = (FBFlutterViewContainer *)currentVC;

        if ([flutterVC.name isEqualToString:@"subMessageListPage"]) {
            // 当前是 subMessageListPage
            if (messageType == MessageTypeServiceNotice || messageType == MessageTypeCustomerResearch) {
                // 目标页面，无需跳转
                return;
            } else {
                // 退出当前 Flutter 页面
                [FlutterBoostUtility goOutFlutterPage:flutterVC.uniqueId];
            }
        } else if ([flutterVC.name isEqualToString:@"messageCenter"]) {
            // 当前是 messageCenter
            if (messageType == MessageTypePromotion) {
                // 目标页面，无需跳转
                return;
            } else {
                [weakSelf goToMessageCenterWithMessageType:messageType needToDelay:delay];
            }
        } else {
            // 当前为其他页面
            [weakSelf goToMessageCenterWithMessageType:messageType needToDelay:delay];
        }
    } else {
        // 当前为非 Flutter 页面
        [weakSelf goToMessageCenterWithMessageType:messageType needToDelay:delay];
    }
}


- (void)goToMessageCenterWithMessageType:(MessageType)messageType needToDelay:(BOOL)delay{
    NSDictionary *arguments;
    NSString *pageName;
    // 根据不同的页面，设置不同的参数
    if (messageType == MessageTypePromotion) {
        arguments = @{@"isPresent" : @NO};
        pageName = @"messageCenter";
    } else if (messageType == MessageTypeCustomerResearch) {
        arguments = @{@"category" : @"CUSTOMER_RESEARCH",
                      @"isPush" : @1,
                      @"isPresent" : @NO};
        pageName = @"subMessageListPage";
    } else if (messageType == MessageTypeServiceNotice) {
        arguments = @{@"category" : @"SERVICE_NOTICE",
                      @"isPush" : @1,
                      @"isPresent" : @NO};
        pageName = @"subMessageListPage";
    }
    // 调用 FlutterBoost 跳转页面
    if(delay) {
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [FlutterBoostUtility gotoFlutterPage:pageName
                                       arguments:arguments
                                  onPageFinished:^(NSDictionary *dic) {
                // 页面跳转完成后的回调
            }];
       });
    }else {
        [FlutterBoostUtility gotoFlutterPage:pageName
                                   arguments:arguments
                              onPageFinished:^(NSDictionary *dic) {
            // 页面跳转完成后的回调
        }];
    }


}


- (void)networkDidReceiveMessage:(NSNotification *)notification {
    NSDictionary * userInfo = [notification userInfo];
    NSString *content = [userInfo valueForKey:@"content"];
    NSString *messageID = [userInfo valueForKey:@"_j_msgid"];
    NSDictionary *extras = [userInfo valueForKey:@"extras"];
    NSString *customizeField1 = [extras valueForKey:@"customizeField1"]; //服务端传递的 Extras 附加字段，key 是自己定义的
}


- (void)checkJailbroken {
    [JailBrokenDetection detectWithTitle:XY_LANGUAGE_TITLE_NAMED(@"app100001168",@"您的设备为越狱状态，建议您在安全的系统环境下安装使用") confirmBtn:XY_LANGUAGE_TITLE_NAMED(@"app01134",@"继续使用") cancelBtn: XY_LANGUAGE_TITLE_NAMED(@"app100000994",@"退出") mainQueue:NO completion:^(BOOL confirm) {
        if (!confirm) {
            exit(0);
        }
    }];
}


- (void)initThirdLogin{
    [[JGOneKeyLoginManager sharedManager] regiseAuthSDKInfo:@"+6s23kenO0qAg71++xOSMvxLey7mv4i1/kRDyoEqvI9SRbhb+N1vOc4IFC1LqXELwBeT2LEtXltIXgE1QAUViK3tIg9Zh/SlRAV4SVgH2c1/K42koljnaIoNpHyO7z74D6U4i4MXFavu7GXh7ahkTWbjvUY6YFClOjbLap/jHUJs0HxvudHLdjePWm2tmF1XmEcTfskUQKx3wzmsMqkKLkxcxePMRcRjG4ki840hDMvOup3C01UeG3D0B3Dz/djVibC2FMpMnno=" complete:^(NSDictionary * _Nonnull resultDic) {
        NSString *code = [resultDic objectForKey:@"resultCode"];
        if ([@"600000" isEqualToString:code]) {

        }
     }];
}

-(void)setUIRTLConfig
{
    // 强制指定布局 必须放到初始化完app内部语言环境以后
    if([XY_JC_LANGUAGE_REAL isEqualToString:@"ar"]) {
        UIView.appearance.semanticContentAttribute = UISemanticContentAttributeForceRightToLeft;
    } else {
        UIView.appearance.semanticContentAttribute = UISemanticContentAttributeForceLeftToRight;
    }
}

/**
 初始化小程序配置
 */
- (void)initUniAppWith:(UIApplication *)application
didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    // 配置参数
    NSMutableDictionary *options = [NSMutableDictionary dictionaryWithDictionary:launchOptions];
    // 设置 debug YES 会在控制台输出 js log，默认不输出 log，注：需要引入 liblibLog.a 库
#ifdef DEBUG
    [options setObject:[NSNumber numberWithBool:YES] forKey:@"debug"];
#else
    [options setObject:[NSNumber numberWithBool:NO] forKey:@"debug"];
#endif
}

/**
 SDWebImage配置
 */
- (void)initSDWebImageConfig{
    [SDImageCache sharedImageCache].config.shouldCacheImagesInMemory = NO;
    [SDImageCache sharedImageCache].config.diskCacheReadingOptions = NSDataReadingMappedIfSafe;
}

- (void)initAppStartPageRoute:(NSDictionary *)launchOptions{
    XYWeakSelf
    self.window = [[UIWindow alloc] initWithFrame:UIScreen.mainScreen.bounds];
    XYNavigationController *nav = [[XYNavigationController alloc] initWithRootViewController:JC_FlTVC];
    self.window.rootViewController = nav;
    self.window.backgroundColor = [UIColor whiteColor];
    //首个页面需要启动原生,但是原生的跟控制器已经有继承, oc不支持多继承,所以如此处理
    JCStartViewController *startVC = [[JCStartViewController alloc] init];
    startVC.needShowAd = self.needShowAd;
    startVC.completionBlock = ^(bool result) {
        [weakSelf initJPush:launchOptions];
    };
    [nav pushViewController:startVC animated:false];
    NSMutableArray *navStack = [NSMutableArray arrayWithArray:nav.viewControllers];
    if (navStack.count > 1) {
        [navStack removeObjectAtIndex:0];
    }
    nav.viewControllers = [navStack copy];
    [self.window makeKeyAndVisible];

}

- (void)initShopSite {
    // 获取商城海外域名以及站点
    [[XYCenter sharedInstance] getAbroadShopHostSite];
}

- (void)appLocalConfig{
    NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
    NSLog(@"本机deviceId：%@",deviceId);
    UIStatusBarStyle statusBarStyle = UIStatusBarStyleDefault;
    if (@available(iOS 13.0, *)) {
        statusBarStyle = UIStatusBarStyleDarkContent;
    }
    [[UIApplication sharedApplication] setIdleTimerDisabled:YES];
    [UIApplication sharedApplication].statusBarStyle = statusBarStyle;
    UITableView.appearance.estimatedRowHeight = 0;
    UITableView.appearance.estimatedSectionFooterHeight = 0;
    UITableView.appearance.estimatedSectionHeaderHeight = 0;
    // 初始化语言系统
    [NSBundle initLanguageSwitchSystem];
    [JCLoginManager sharedInstance];
    [JCAppStartConfig initLocalPathAndResource];
    [[JCActivityAdPopManager shareInstance] getLaunchAdConfig];

    // 初始化 Capacitor 小程序环境
    [[NBCAPMiniAppEngine sharedInstance] prepareEngine];

    NSString *pasteboardSetting = [JCKeychainTool load:@"pasteboardSettingSwitch"];
    if(STR_IS_NIL(pasteboardSetting)){
        pasteboardSetting = @"1";
        [JCKeychainTool save:@"pasteboardSettingSwitch" data:pasteboardSetting];
    }

//    // 复制数据库文件到沙盒
//    [self copyDatabaseToSandbox];
}

- (void)copyDatabaseToSandbox {
    NSFileManager *fileManager = [NSFileManager defaultManager];

    // 源数据库文件路径（应用包内的数据库文件）
    NSString *sourcePath = [[NSBundle mainBundle] pathForResource:@"JCPrint_zh-cn" ofType:@"sqlite"];

    // 目标数据库文件路径（沙盒中的数据库文件路径）
    NSString *dbPath = RESOURCE_DB_PATH;
    NSString *destinationPath = [dbPath stringByAppendingPathComponent:@"JCPrint_zh-cn.sqlite"];

    // 确保数据库目录存在
    if (![fileManager fileExistsAtPath:dbPath]) {
        [fileManager createDirectoryAtPath:dbPath withIntermediateDirectories:YES attributes:nil error:nil];
    }

    if (sourcePath) {
        NSError *error = nil;

        // 如果目标文件已存在，先删除
        if ([fileManager fileExistsAtPath:destinationPath]) {
            [fileManager removeItemAtPath:destinationPath error:&error];
            if (error) {
                NSLog(@"删除已存在的数据库文件失败: %@", error.localizedDescription);
                return;
            }
        }

        // 强制复制数据库文件，直接覆盖
        BOOL success = [fileManager copyItemAtPath:sourcePath toPath:destinationPath error:&error];
        if (success) {
            NSLog(@"成功将数据库文件复制到沙盒: %@", destinationPath);
        } else {
            NSLog(@"复制数据库文件失败: %@", error.localizedDescription);
        }
    } else {
        NSLog(@"源数据库文件不存在");
    }
}

- (void)syncFileToisar {
  // 检查是否已经迁移过
  NSString *isMigrationCompleted = [[NSUserDefaults standardUserDefaults] objectForKey:@"templateMigrationCompleted"];
  if ([isMigrationCompleted isEqualToString:@"1"]) {
    NSLog(@"模板数据已经迁移过，跳过迁移过程");
    return;
  }

  // 获取所有模板数据并重置本地路径
  // 计时开始
  NSDate *startTime = [NSDate date];
  NSLog(@"开始处理模板数据: %@", [self getCurrentDateAndTime]);

  // 获取所有模板数据
  NSDate *startTemplatesTime = [NSDate date];
  NSArray<JCTemplateData *> *templates = [JCTemplateDBManager getAllDBTemplateData];
  // 计算最终耗时
  NSTimeInterval totalTimeIntervalTemplates = [[NSDate date] timeIntervalSinceDate:startTemplatesTime];
  NSLog(@"总共获取到 %lu 个模板, 总耗时: %.4f 秒", (unsigned long)templates.count, totalTimeIntervalTemplates);

  // 如果模板为空，则不需要迁移
  if (templates.count == 0) {
    NSLog(@"没有模板数据需要迁移");
    // 标记迁移已完成
    [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"templateMigrationCompleted"];
    [[NSUserDefaults standardUserDefaults] synchronize];
    return;
  }

  // 遍历处理每个模板
  __block NSInteger processedCount = 0;
  __block NSDate *batchStartTime = [NSDate date];
  __block NSTimeInterval totalItemProcessingTime = 0; // 所有项目处理的累计时间

  [templates enumerateObjectsUsingBlock:^(JCTemplateData * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
      NSDate *itemStartTime = [NSDate date];

      // 重置模板本地路径
      [obj resetTemplateLocalPath];

      // 将处理后的数据重新写回数据库
      JCTemplateData4DB *data4DB = [JCTemplateData4DBManager dbDataWith:obj];
      [[JCFMDB shareDatabase:DB_NAME] jc_insertTable:TABLE_TEMPLATE_NEW dicOrModel:data4DB];

      NSTimeInterval itemTimeInterval = [[NSDate date] timeIntervalSinceDate:itemStartTime];
      totalItemProcessingTime += itemTimeInterval;

      processedCount++;
      NSLog(@"处理模板 #%ld 耗时: %.4f 秒", (long)idx + 1, itemTimeInterval);

    if (processedCount % 100 == 0) {
        NSTimeInterval batchTimeInterval = [[NSDate date] timeIntervalSinceDate:batchStartTime];
        NSTimeInterval totalTimeInterval = [[NSDate date] timeIntervalSinceDate:startTime];
        NSLog(@"已处理 %ld 个模板，本批次耗时: %.4f 秒，平均每个模板: %.4f 秒，累计耗时: %.4f 秒",
              (long)processedCount, batchTimeInterval, batchTimeInterval/100.0, totalTimeInterval);
          batchStartTime = [NSDate date]; // 重置批次开始时间
      }
  }];

  // 计算最终耗时
  NSTimeInterval totalTimeInterval = [[NSDate date] timeIntervalSinceDate:startTime];
  NSLog(@"模板数据处理完成，共处理 %lu 个模板，总耗时: %.4f 秒，平均每个模板耗时: %.4f 秒",
        (unsigned long)templates.count, totalTimeInterval, templates.count > 0 ? totalItemProcessingTime/templates.count : 0);
  NSLog(@"纯处理时间: %.4f 秒，占总耗时比例: %.2f%%",
        totalItemProcessingTime, templates.count > 0 ? (totalItemProcessingTime/totalTimeInterval)*100 : 0);

  // 调用Flutter端的数据迁移方法并记录耗时
  NSLog(@"开始执行Flutter端数据迁移...");
  NSDate *migrationStartTime = [NSDate date];

  // 调用Flutter端的setupDataMigration方法
  [[[JCAppMethodChannel shareInstance] methodChannel] invokeMethod:@"setupDataMigration" arguments:nil result:^(id value) {
      NSTimeInterval migrationTimeInterval = [[NSDate date] timeIntervalSinceDate:migrationStartTime];
      NSLog(@"Flutter数据迁移完成，耗时: %.4f 秒", migrationTimeInterval);

      // 标记迁移已完成
      [[NSUserDefaults standardUserDefaults] setObject:@"1" forKey:@"templateMigrationCompleted"];
      [[NSUserDefaults standardUserDefaults] synchronize];
      NSLog(@"已标记模板数据迁移完成状态");
  }];
}

- (void)thirdAppConfig:(UIApplication *)application
didFinishLaunchingWithOptions:(NSDictionary *)launchOptions{
//    [JCAPI setIsJCUser:YES];//新SDK联调注释
    #ifdef DEBUG
//    [[JCAPI new] setValue:@"63" forKey:@"YMY_Log_Lever"];//新SDK联调注释
    [JCSDKManager_Etag shareManager].debugLevel = 3;
    [[YKWoodpeckerManager sharedInstance] show];
    #endif
    //bugly监控
    [self configBugly];
    //增加sentry监控
    [self configSentry];
    [IFlySetting setLogFile:LVL_ALL];
    //打开输出在console的log开关
    [IFlySetting showLogcat:NO];
    //设置sdk的工作路径
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSCachesDirectory, NSUserDomainMask, YES);
    NSString *cachePath = [paths objectAtIndex:0];
    [IFlySetting setLogFilePath:cachePath];
    //创建语音配置,appid必须要传入，仅执行一次则可
    NSString *initString = [[NSString alloc] initWithFormat:@"appid=%@",APPID_VALUE];
    // 这里执行创建数据库,以后的shareDatabase系列都属于获取当前的数据库引用
    [IFlySpeechUtility createUtility:initString];
    [JCIAPHelper sharedInstance];
    id track = [[JCTrackingServiceHelp sharedInstance] initTrackingConfigWithOptions:launchOptions];
    NSLog(@"%@",track);
}

- (void)netWorkConfig{
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    config.baseUrl = ServerURL;
    // 签名拦截
    [config addUrlFilter:[[FlutterNetSignatureUrlHeaderFilter alloc] init]];
    [IQKeyboardManager sharedManager].enable = YES;
    [IQKeyboardManager sharedManager].enableAutoToolbar = YES;
    [[$ rac_didNetworkChanges]
     subscribeNext:^(NSNumber *status) {
         AFNetworkReachabilityStatus networkStatus = [status intValue];
         switch (networkStatus) {
             case AFNetworkReachabilityStatusUnknown:
             case AFNetworkReachabilityStatusNotReachable:
                 jc_is_connected_wifi = NO;
                 jc_is_connected_cellular_network = NO;
                 JCNCPost(JCNOTICATION_SYNCH_CHANGE);
                 break;
             case AFNetworkReachabilityStatusReachableViaWWAN:
                 jc_is_connected_wifi = NO;
                 jc_is_connected_cellular_network = YES;
                 JCNCPost(JCNOTICATION_SYNCH_CHANGE);
                 break;
             case AFNetworkReachabilityStatusReachableViaWiFi:
                 jc_is_connected_wifi = YES;
                 jc_is_connected_cellular_network = YES;
                 JCNCPost(JCNOTICATION_SYNCH_CHANGE);
                 JCNCPost(JCNOTICATION_WIFI_STATE_CHANGE);
                 break;
         }
     }];
}

- (XYTabBarController *)mainVC
{
    if (!_mainVC) {
        XYTabBarController *vc = [[XYTabBarController alloc] init];
        _mainVC = vc;
    }
    return _mainVC;
}

- (void)sendTrackAndPrintData{
//    [[XYCenter sharedInstance] sendStatistics:^(){}];
    [[XYCenter sharedInstance] sendRecordData];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
    if([userActivity.webpageURL.absoluteString containsString:@"oauth?"]){
        return [super application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
    }else if([userActivity.webpageURL.absoluteString containsString:@"wxece69872671e1254"]){
        return [WXApi handleOpenUniversalLink:userActivity delegate:self];
    }else if ([userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]){
        return [super application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
    }else{
        return [super application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
    }
}

//其他应用返回回调
-(BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options{
    if (self.window) {
        if (url) {
            //支付宝开始
            //如果极简开发包不可用，会跳转支付宝钱包进行支付，需要将支付宝钱包的支付结果回传给开发包
            [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {

            }];
            if([url.absoluteString containsString:@"jcydy://com.suofang.jcbqdy"] || [url.absoluteString containsString:@"JCYDY://safepay"] || [url.absoluteString containsString:[NSString stringWithFormat:@"%@://iwantbacktoapp/",SchemesWX]]){
                [[NSNotificationCenter defaultCenter] postNotificationName:@"payBackToRefrshWeb" object:nil];
            }
            else if ([url.host isEqualToString:@"safepay"]) {
                [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:^(NSDictionary *resultDic) {
                    //【由于在跳转支付宝客户端支付的过程中，商户app在后台很可能被系统kill了，所以pay接口的callback就会失效，请商户对standbyCallback返回的回调结果进行处理,就是在这个方法里面处理跟callback一样的逻辑】
                    NSLog(@"result = %@",resultDic);
                }];
            } else if ([url.host isEqualToString:@"platformapi"]){//支付宝钱包快登授权返回authCode
                [[AlipaySDK defaultService] processAuthResult:url standbyCallback:^(NSDictionary *resultDic) {
                    //【由于在跳转支付宝客户端支付的过程中，商户app在后台很可能被系统kill了，所以pay接口的callback就会失效，请商户对standbyCallback返回的回调结果进行处理,就是在这个方法里面处理跟callback一样的逻辑】
                    NSLog(@"result = %@",resultDic);
                }];
            }else if([url.absoluteString isEqualToString:@"wxece69872671e1254://platformId=wechat"]){
                return YES;
            }
            else if([url.absoluteString containsString:@"wxece69872671e1254"] || [url.absoluteString containsString:@"tencent101553333"] || [url.absoluteString containsString:@"QQ60d94b5"]){
                return [super application:app openURL:url options:options];
            }else if([url.absoluteString hasPrefix:@"niimbot://"]){
                self.outSechemUrl = url.absoluteString;
                [self outAppToNiimbot];
            }
            //支付宝结束
            else{
                NSString *fileName = url.lastPathComponent;
                NSString *path = url.absoluteString;
                path = [path stringByRemovingPercentEncoding];
                NSMutableString *string = [[NSMutableString alloc] initWithString:path];
                if ([path hasPrefix:@"file://"]) {
                    [string replaceOccurrencesOfString:@"file://" withString:@"" options:NSCaseInsensitiveSearch range:NSMakeRange(0, path.length)];
                    NSDictionary *dict = @{@"fileName":UN_NIL(fileName),
                                           @"filePath":UN_NIL(string)};
                    [[NSNotificationCenter defaultCenter] postNotificationName:JCNOTICATION_EXCEL_UPLOAD object:nil userInfo:dict];
                    return YES;
                }else{
                    return [super application:app openURL:url options:options];
                }
            }
        }
    }
    return YES;
}

- (void)outAppToNiimbot{
    if(self.appLaunched && !STR_IS_NIL(self.outSechemUrl)){
        NSString *sechemUrl = self.outSechemUrl;
        if([sechemUrl hasPrefix:@"niimbot://web/"]){
            sechemUrl = [sechemUrl stringByReplacingOccurrencesOfString:@"niimbot://web/" withString:@"https://"];
            [JCToNativeRouteHelp bannberJumpWith:@"4" routeString:sechemUrl title:@"" sourceVC:[XYTool getCurrentVC]];
        }else if([sechemUrl hasPrefix:@"niimbot://app"]){
            [JCToNativeRouteHelp toNativePageWith:sechemUrl fromType:JC_Out_App eventTitle:@""];
        }
        self.outSechemUrl = nil;
    }
}

- (BOOL)needToAppointPage{
    return !STR_IS_NIL(self.outSechemUrl);
}


//进入后台
- (void)applicationDidEnterBackground:(UIApplication *)application {
    [[JCAppEventChannel shareInstance] eventData:@"kEnterBackground"];
    [[JCActivityAdPopManager shareInstance] refreshAdStatus];
    [[NSNotificationCenter defaultCenter] postNotificationName:JCApplicationBackForegroundSwitch object:@0];
    _mTimer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(countAction) userInfo:nil repeats:YES];
    [[NSRunLoop currentRunLoop] addTimer:_mTimer forMode:NSRunLoopCommonModes];
    [self beginTask];
    JC_TrackWithparms(@"appback",@"001",@{});
}

//进入前台
- (void)applicationWillEnterForeground:(UIApplication *)application {
    // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
    NSLog(@"进入前台");
    UIViewController *currentVC = [XYTool getCurrentVC];
    if([currentVC isKindOfClass:[NSClassFromString(@"JCPrintSettingViewController") class]]){
        NSLog(@"offline:后台进入前台 当处于打印设置页时处理离线弹窗");
        [JCOfflineTimeCheckHelper checkAppOfflineTimeEvent];
    }
    [[JCAppEventChannel shareInstance] eventData:@"kBackToForeground"];
    [self endBack];
    [self detectConnected];
    [[NSNotificationCenter defaultCenter] postNotificationName:JCApplicationBackForegroundSwitch object:@1];
    [JCShareTool getInvitationPush:YES];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [[XYCenter sharedInstance] sendStatistics:^(){}];
        [[XYCenter sharedInstance] sendRecordData];
        [[XYCenter sharedInstance] firstShopRequestSuccess:^(id x) {

        }];
        JC_TrackWithparms(@"appfront",@"001",@{});
        // 重置手动断开连接标志，以便新会话能够自动连接
        [[NSUserDefaults standardUserDefaults] removeObjectForKey:MANUAL_DISCONNECT_FLAG];
        NSDictionary *peripheralInfo = [JCBluetoothManager printerConnectStatusCache];
        if(IsNotEmptyDictionary(peripheralInfo) && self.appLaunched && !JC_IS_CONNECTED_PRINTER){
            [JCBluetoothManager sharedInstance].connectOcca = 2;
            [[JCBluetoothManager sharedInstance] autoConnectPrinter];
        }
    });
}

//进入前台判断熔断状态
- (void)detectConnected {
  if(m_currentServerState == ServerState_Stop) {
    NSString *urlString = [NSString stringWithFormat:@"%@system/user/stgStrategyConfig", ServerURL];
    XYBaseRequest *requestapi = [[XYBaseRequest alloc] initWithRequsetParameters:nil Path:urlString ModelType:nil image:nil formKey:nil];
    @try {
        [requestapi getWithCompletionBlockWithSuccess:^(__kindof YTKBaseRequest *request, NSString* responsCode) {
          m_currentServerState = ServerState_Normal;
        } failure:^(NSString *msg,NSString *responsCode) {

        }];
    } @catch (NSException *exception) {
      NSLog(@"-------------------------------网络异常-------------------------------");
    }
  }
}

//计时
-(void)countAction{

}

//申请后台
-(void)beginTask
{
    NSLog(@"begin=============");
    _backIden = [[UIApplication sharedApplication] beginBackgroundTaskWithExpirationHandler:^{
        //在时间到之前会进入这个block，一般是iOS7及以上是3分钟。按照规范，在这里要手动结束后台，你不写也是会结束的（据说会crash）
        NSLog(@"将要挂起=============");
        [self endBack];
    }];
}

//注销后台
-(void)endBack
{
    NSLog(@"end=============");
    [[UIApplication sharedApplication] endBackgroundTask:_backIden];
    _backIden = UIBackgroundTaskInvalid;
}


- (void)applicationDidBecomeActive:(UIApplication *)application {
  // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
//  [SentrySDK captureMessage:@"App restarted after crash"];
}


- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    [[JCIAPHelper sharedInstance] removeTransactionObserver];
    [JCAppEventChannel shareInstance].eventSink = nil;
}


- (void)applicationDidReceiveMemoryWarning:(UIApplication *)application {
    NSLog(@"====> 内存警告⚠️");
}

- (void)configBugly {
    BuglyConfig * config = [[BuglyConfig alloc] init];
//    非正常退出事件(SIGKILL)
    config.unexpectedTerminatingDetectionEnable = YES;
    //上传时改为NO
    #if DEBUG
    config.debugMode = YES;//开启SDK日志
    #endif
    config.reportLogLevel = BuglyLogLevelInfo;
    //卡顿监控
    config.blockMonitorEnable = YES;
    // Set the STUCK THRESHOLD time, when STUCK time > THRESHOLD it will record an event and report data when the app launched next time.
    // Default value is 3.5 second.
    config.blockMonitorTimeout = 1.5;
    //渠道标识
    config.channel = @"Niimbot";
    config.delegate = self;
    //进程内还原符号
    config.symbolicateInProcessEnable = YES;
    [Bugly startWithAppId:@"c8c5c27fa6"
                   config:config];
    [Bugly setTag:1799];
    [self updateListenUserId:1];
    [Bugly setUserValue:[NSProcessInfo processInfo].processName forKey:@"Process"];
//    [Bugly reportException:[NSException exceptionWithName:@"HttpError" reason:@"Http请求错误" userInfo:@{@"errorStr":@"21321321"}]];
    [[NSNotificationCenter defaultCenter] addObserverForName:FLUTTER_LOGIN_SUCCESS object:nil queue:nil usingBlock:^(NSNotification * _Nonnull note) {
      [self updateListenUserId:0];
    }];

}

// 初始化 Sentry
- (void)configSentry{
    NSDictionary *sentryConfig = [[NSUserDefaults standardUserDefaults] valueForKey:@"niimbot_sentry_config"];
    NSNumber *sampleRateNumber = sentryConfig[@"CloudPrintFlutterSentrySampleRate"] != nil?sentryConfig[@"CloudPrintFlutterSentrySampleRate"]:@0.0001;
    NSNumber *tracesSampleRateNumber = sentryConfig[@"CloudPrintFlutterSentryTracesSampleRate"] != nil?sentryConfig[@"CloudPrintFlutterSentryTracesSampleRate"]:@0.0001;
    [SentrySDK startWithConfigureOptions:^(SentryOptions *options) {
      options.dsn = SentryDSN;  // 替换成你的 Sentry DSN
//      options.debug = YES;  // 在开发模式下查看日志
      options.enabled = YES; // 确保 SDK 启用
      options.enableCrashHandler = YES; // 启用崩溃捕获（默认开启）
      options.attachStacktrace = YES; // 附加堆栈信息
      options.enableAppHangTracking = YES; // 监测应用卡顿
      options.enableAutoSessionTracking = YES; // 自动会话跟踪
      options.sessionTrackingIntervalMillis = 60000; // 会话间隔
      options.enablePersistingTracesWhenCrashing = YES;
      options.sampleRate = sampleRateNumber;
      options.tracesSampleRate = tracesSampleRateNumber;
      NSLog(@"Sentry sampleRate: %@ tracesSampleRate:%@", sampleRateNumber,tracesSampleRateNumber);
      // 定义过滤条件
      NSArray<NSString *> *ignoredURLs = @[@"https://print.jc-test.cn",@"https://print.niimbot.com"];
      NSArray<NSNumber *> *ignoredStatusCodes = @[@401, @404, @502, @503];
      options.beforeSend = ^SentryEvent *(SentryEvent *event) {
          NSLog(@"Sentry 捕获的事件: requestURL: %@", event.request.url);
        // 按 URL 过滤
          NSString *requestURL = event.request.url;
          if (requestURL) {
              for (NSString *pattern in ignoredURLs) {
                  if ([requestURL containsString:pattern]) {
                      return nil;
                  }
              }
          }
          NSLog(@"Sentry 捕获的事件: StatusCode:%@", event.context[@"response"][@"status_code"]);
          // 按状态码过滤
          NSNumber *statusCode = event.context[@"response"][@"status_code"];
          if (statusCode && [ignoredStatusCodes containsObject:statusCode]) {
              return nil;
          }

          // 按错误类型过滤
          for (SentryException *exception in event.exceptions) {
              NSLog(@"Sentry 捕获的事件: exceptionType:%@", exception.type);
              if ([exception.type isEqualToString:@"NSURLErrorDomain"] || [exception.type isEqualToString:@"NetworkError"]) {
                  return nil;
              }
          }
          return event; // 确保事件不会被丢弃、
      };
      NSInteger appEnv = 0;
      #ifdef  JCTest
            appEnv = app_laboratory_env;
      #elif   Dev
            appEnv = 2;
      #endif
      if(appEnv == 0){
        options.environment = @"production";
      }else{
        options.environment = @"test";
      }
      options.maxBreadcrumbs = 50;
    }];
  [SentrySDK configureScope:^(SentryScope * _Nonnull scope) {
    NSString *deviceId = [JCKeychainTool getDeviceIDInKeychain];
    [scope setTagValue:deviceId forKey:@"device_id"];
    [self updateListenUserId:2];
  }];
}

//listenType   1 bugly监控   2 sentry监控   0 全部处理
- (void)updateListenUserId:(NSInteger)listenType{
    NSString *userId = [XYCenter sharedInstance].userModel.displayUId;
    if (userId == nil || userId.length <= 0) {
        userId = [JCKeychainTool getDeviceIDInKeychain];
    }
    if(listenType == 1){
      [Bugly setUserIdentifier:[NSString stringWithFormat:@"%@", userId]];
    }else if (listenType == 2){
      [SentrySDK setUser:[[SentryUser alloc] initWithUserId:userId]];
    }else{
      [Bugly setUserIdentifier:[NSString stringWithFormat:@"%@", userId]];
      [SentrySDK setUser:[[SentryUser alloc] initWithUserId:userId]];
    }
}

- (void)updateSentryUserId{
    NSString *userId = [XYCenter sharedInstance].userModel.displayUId;
    if (userId == nil || userId.length <= 0) {
        userId = [JCKeychainTool getDeviceIDInKeychain];
    }
    [Bugly setUserIdentifier:[NSString stringWithFormat:@"%@", userId]];
    [SentrySDK setUser:[[SentryUser alloc] initWithUserId:userId]];
}


- (NSString *)attachmentForException:(NSException *)exception {
    NSLog(@"异常事件代理");
    return [NSString stringWithFormat:@"TEST: %@",exception.userInfo];
}

- (void)dealwithCrashMessage:(NSNotification *)note {
    //不论在哪个线程中导致的crash，这里都是在主线程

    //注意:所有的信息都在userInfo中
    //你可以在这里收集相应的崩溃信息进行相应的处理(比如传到自己服务器)
    //详细讲解请查看 https://github.com/chenfanfang/AvoidCrash
}

/**
    配置Https证书，防止抓包及非法代理      debug 模式下关闭证书校验，可通过个人中心设置页开关控制
 */

-(void)initHttpsCertificateConfig{
#ifdef DEBUG
    NSString *encryptedSwitch = [[NSUserDefaults standardUserDefaults] objectForKey:@"encryptedSwitch"];
    if(STR_IS_NIL(encryptedSwitch)){
        [[NSUserDefaults standardUserDefaults] setObject:@"0" forKey:@"encryptedSwitch"];
    }
    else{
        if(encryptedSwitch.integerValue == 0){
            return;
        }
    }//
    return;

#else
    YTKNetworkConfig *config = [YTKNetworkConfig sharedConfig];
    NSString *testCerPath = [[NSBundle mainBundle] pathForResource:@"niimbot-test" ofType:@"cer"];//证书的路径
    NSData *testCertData = [NSData dataWithContentsOfFile:testCerPath];
    NSString *testCerPath1 = [[NSBundle mainBundle] pathForResource:@"niimbot-test1" ofType:@"cer"];//证书的路径
    NSData *testCertData1 = [NSData dataWithContentsOfFile:testCerPath1];
    NSString *proCerPath = [[NSBundle mainBundle] pathForResource:@"niimbot" ofType:@"cer"];//证书的路径
    NSData *proCertData = [NSData dataWithContentsOfFile:proCerPath];
    NSString *proCerPath1 = [[NSBundle mainBundle] pathForResource:@"niimbot1" ofType:@"cer"];//生产证书1的路径
    NSData *proCertData1 = [NSData dataWithContentsOfFile:proCerPath1];
    // 公钥锁定
    AFSecurityPolicy *securityPolicy = [AFSecurityPolicy policyWithPinningMode:AFSSLPinningModePublicKey];
    securityPolicy.allowInvalidCertificates = YES;
    securityPolicy.validatesDomainName = NO;
    securityPolicy.pinnedCertificates = [NSSet setWithObjects:testCertData,proCertData,proCertData1,testCertData1,nil];
    [config setSecurityPolicy:securityPolicy];
#endif

}



// qq分享结果的回调
- (void)onResp:(QQBaseResp *)resp
{
    if ([resp isKindOfClass:[SendMessageToQQResp class]] && resp.type == ESENDMESSAGETOQQRESPTYPE)
    {
        SendMessageToQQResp* sendReq = (SendMessageToQQResp*)resp;
        // sendReq.result->0分享成功 -4取消分享
        if ([sendReq.result integerValue] == 0) {
            NSLog(@"qq share success");
        }else{
            NSLog(@"qq share failed");
        }
    }
}

- (NSString *)getCurrentDateAndTime {
    // 获取当前日期
    NSDate *currentDate = [NSDate date];
    // 创建日期格式化器
    NSDateFormatter *dateFormatter = [[NSDateFormatter alloc] init];
    [dateFormatter setDateFormat:@"yyyy-MM-dd HH:mm:ss"];

    // 将日期格式化为字符串
    return [dateFormatter stringFromDate:currentDate];
}

- (void)setupTabBarController {
    XYTabBarController *tabBarController = [[XYTabBarController alloc] init];
    self.window.rootViewController = tabBarController;
    [self.window makeKeyAndVisible];
}

@end
