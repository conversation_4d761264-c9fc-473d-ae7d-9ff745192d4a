/* 
  InfoPlist.strings
  XYFrameWork

  Created by <PERSON><PERSON><PERSON> on 2018/8/30.
  Copyright © 2018年 xiaoyao. All rights reserved.
*/
CFBundleName = "NIIMBOT";
CFBundleDisplayName = "NIIMBOT";
NSCameraUsageDescription = "แอปนี้จะเข้าถึงสิทธิ์ของกล้องในฟังก์ชันของการสแกนบาร์โค้ดการระบุฟ้อนต์ และการถ่ายรูป อนุญาตให้เปิดกล้องหรือไม่?";
NSBluetoothPeripheralUsageDescription = "แอปนี้จะเข้าถึงสิทธิ์ของบลูทูธในบริการที่เชื่อมต่อกับเครื่องพิมพ์อนุญาตให้เปิดบลูทูธหรือไม่?";
NSBluetoothAlwaysUsageDescription = "แอปนี้จะเข้าถึงสิทธิ์ของบลูทูธในบริการที่เชื่อมต่อกับเครื่องพิมพ์อนุญาตให้เปิดบลูทูธหรือไม่?";
NSContactsUsageDescription = "แอปนี้จะเข้าถึงสิทธิ์ของรายชื่อติดต่อในบริการที่อ่านผู้ติดต่อ อนุญาตให้เปิดรายชื่อติดต่อหรือไม่?";
NSMicrophoneUsageDescription = "แอปนี้จะเข้าถึงสิทธิ์ของไมโครโฟนในการระบุเสียง อนุญาตให้เปิดไมโครโฟนหรือไม่?";
NSPhotoLibraryUsageDescription = "การอนุญาตเข้าถึงนี้จะถูกใช้เพื่อพิมพ์ฉาก เช่น วัสดุรูปภาพ การระบุบาร์โค้ด การระบุคิวอาร์โค้ด การระบุฟ้อนต์ และการตั้งค่ารูปโปรไฟล์ที่กำหนดเอง โปรด \"อนุญาตการเข้าถึงรูปภาพทั้งหมด” เพื่อให้แน่ใจว่าสามารถเข้าถึงอัลบั้มในเครื่องพิมพ์ NIIMBOT ได้ หากคุณใช้ \"เลือกรูปภาพ...\" รูปภาพที่ไม่ได้เลือกทั้งหมดรวมถึงรูปภาพที่เพิ่มจะไม่สามารถเข้าถึงได้ในเครื่องพิมพ์ NIIMBOT";
NSLocationWhenInUseUsageDescription = "เพื่อให้คุณใช้เครือข่าย Wi-Fi ใกล้เคียงได้ง่ายขึ้น เครื่องพิมพ์ NIIMBOT จะขออนุญาตสิทธิ์การกำหนดตำแหน่งจากคุณ";
NSLocationAlwaysUsageDescription = "เพื่อให้คุณใช้เครือข่าย Wi-Fi ใกล้เคียงได้ง่ายขึ้น เครื่องพิมพ์ NIIMBOT จะขออนุญาตสิทธิ์การกำหนดตำแหน่งจากคุณ";
NSLocationAlwaysAndWhenInUseUsageDescription = "เพื่อให้คุณใช้เครือข่าย Wi-Fi ใกล้เคียงได้ง่ายขึ้น เครื่องพิมพ์ NIIMBOT จะขออนุญาตสิทธิ์การกำหนดตำแหน่งจากคุณ";
NSSpeechRecognitionUsageDescription = "แอปนี้ต้องการการยินยอมของคุณถึงจะเข้าถึงการระบุเสียงได้ อนุญาตให้เปิดการระบุเสียงหรือไม่?";
NSLocalNetworkUsageDescription = "แอปนี้จำเป็นต้องเข้าถึงระบบเครือข่ายท้องถิ่น (LAN)​​ สำหรับบริการค้นหาอุปกรณ์ LAN และการกำหนดค่าเครือข่าย";
"UILaunchStoryboardName" = "LaunchScreen";
