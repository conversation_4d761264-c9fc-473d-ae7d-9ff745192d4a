package com.niimbot.viplibrary.repository

import com.blankj.utilcode.util.GsonUtils
import com.blankj.utilcode.util.LogUtils
import com.niimbot.appframework_library.expand.safeLet
import com.niimbot.appframework_library.expand.toJson
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.googlepay.GooglePayHelper
import com.niimbot.baselibrary.googlepay.GoogleSkuDetail
import com.niimbot.okgolibrary.okgo.callback.JsonCallback
import com.niimbot.okgolibrary.okgo.model.Response
import com.niimbot.utiliylibray.util.PreferencesUtils
import com.niimbot.viplibrary.CurrencySymbol2CountryUtil
import com.niimbot.viplibrary.VipHelper
import com.niimbot.viplibrary.VipRetentionPromotionHelper
import com.niimbot.viplibrary.VipType
import com.niimbot.viplibrary.bean.PriceBean
import com.niimbot.viplibrary.bean.VipBean
import com.niimbot.viplibrary.bean.VipPackageDTO
import com.niimbot.viplibrary.event.GoogleVerifyEvent
import com.niimbot.viplibrary.service.VipService
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.math.BigDecimal

/**
 * @ClassName: VipRepository
 * @Author: Liuxiaowen
 * @Date: 2021/9/2 11:10
 * @Description:
 */
object VipRepository {

    var vipPackageDTO: VipPackageDTO? = null
    var vipBean: VipBean? = null
    var regionCode = PreferencesUtils.getString("googleRegionCode", "CN")

    fun clearCache() {
        vipPackageDTO = null
        vipBean = null
    }

    fun initGooglePlay() {
        getVipPackageDTO(isInit = true) { vipDto ->
            vipDto?.let { vipPackage ->
                var inappSkuList = ArrayList<String>()
                var subsSkuList = ArrayList<String>()
                vipPackage.vipPackageDTO?.prices?.forEach {
                    if (!it.productId.isNullOrEmpty()) {
                        it.productId?.let { sku ->
                            if (it.autoRenewal) subsSkuList.add(sku) else inappSkuList.add(
                                sku
                            )
                        }
                    }
                }
                if (inappSkuList.isNotEmpty() || subsSkuList.isNotEmpty()) {
                    GooglePayHelper.initPayHelper(
                        inappSkuList.toTypedArray(),
                        subsSkuList.toTypedArray(),
                        inappSkuList.toTypedArray(),
                        { packageName, purchaseToken, orderId, sku, price, region, niimbotOrderNo ->
                            VipService.googlePayVerify(
                                packageName,
                                purchaseToken,
                                orderId,
                                sku,
                                price,
                                region,
                                niimbotOrderNo
                            )
                        },
                        { packageName, purchaseToken, orderId, sku, price, region, niimbotOrderNo ->
                            VipService.googlePayVerify(
                                packageName,
                                purchaseToken,
                                orderId,
                                sku,
                                price,
                                region,
                                niimbotOrderNo
                            )
                        },
                        { packageName, purchaseToken, orderId, sku, price, region, niimbotOrderNo, listener ->
                            VipService.googlePayVerify(
                                packageName,
                                purchaseToken,
                                orderId,
                                sku,
                                price,
                                region,
                                niimbotOrderNo,
                                true,
                                listener
                            )
                        },
                        { startSuccess ->
                            if (!startSuccess) EventBus.getDefault()
                                .post(GoogleVerifyEvent(startSuccess))
                        },
                        {
                            GooglePayHelper.setSkuDetailsList(inappSkuList,subsSkuList).let {
                                GooglePayHelper.getSkuDetailsList { skuList ->
                                    if (!skuList.isNullOrEmpty()) {
                                        vipPackage.vipPackageDTO.prices =
                                            processGoogleBilling(vipPackage, skuList)
                                    }
                                }
                            }
                        }
                    )
                }
            }
        }
    }

    /**
     * 获取vip会员页面的打包数据
     * @param listener Function1<VipPackageDTO?, Unit>
     */
    fun getVipPackageDTO(isInit: Boolean = false,
                         vipType: VipType = VipType.NORMAL,
                         listener: (VipPackageDTO?) -> Unit) {

        if(vipType == VipType.NORMAL){
            vipPackageDTO?.let {
                if (isInit) {
                    listener.invoke(vipPackageDTO)
                } else {
                    processVipPrice(it,listener)
                }
            } ?: VipService.fetchVipBean(object : JsonCallback<String>() {
                override fun onSuccess(response: Response<String>) {
                    try {
                        vipPackageDTO = GsonUtils.fromJson(response.body(), VipPackageDTO::class.java)
                        vipPackageDTO!!.vipPackageDTO = VipBean(
                            id = 0,
                            prices = vipPackageDTO!!.prices,
                            name = "",
                            privileges = listOf()
                        )
                        if (isInit) {
                            listener.invoke(vipPackageDTO)
                        } else {
                            processVipPrice(vipPackageDTO!!){
                                VipRetentionPromotionHelper.checkRetentionPromotionStatus()
                                listener.invoke(it)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                        listener.invoke(null)
                    }
                }

                override fun onError(response: Response<String>) {
                    listener.invoke(null)
                }
            })
        }else if(vipType==VipType.CABLE){
            VipService.fetchVipBean(object : JsonCallback<String>() {
                override fun onSuccess(response: Response<String>) {
                    try {
                        var vipPackageDTO = GsonUtils.fromJson(response.body(), VipPackageDTO::class.java)
                        vipPackageDTO!!.prices.forEach {
                            it.hasActivity = true
                            it.vipType = "cable"
                            it.showVipActivity = it.hasServerActivityStart()
                        }
                        vipPackageDTO!!.vipPackageDTO = VipBean(
                            id = 0,
                            prices = vipPackageDTO!!.prices,
                            name = "",
                            privileges = listOf(),

                        )
                        listener.invoke(vipPackageDTO)
                    } catch (e: Exception) {
                        e.printStackTrace()
                        listener.invoke(null)
                    }
                }

                override fun onError(response: Response<String>) {
                    listener.invoke(null)
                }
            },vipType=vipType)
        }

    }

    fun refreshVipData(listener: (VipPackageDTO?) -> Unit){
        VipService.fetchVipBean(object : JsonCallback<String>() {
            override fun onSuccess(response: Response<String>) {
                try {
                    vipPackageDTO = GsonUtils.fromJson(response.body(), VipPackageDTO::class.java)
                    vipPackageDTO!!.vipPackageDTO = VipBean(
                        id = 0,
                        prices = vipPackageDTO!!.prices,
                        name = "",
                        privileges = listOf()
                    )
                    processVipPrice(vipPackageDTO!!){
                        VipRetentionPromotionHelper.checkRetentionPromotionStatus()
                        MainScope().launch {
                            listener.invoke(it)
                        }
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                    listener.invoke(null)
                }
            }

            override fun onError(response: Response<String>) {
                listener.invoke(null)
            }
        })
    }

    private fun processVipPrice(vipPackageDTO: VipPackageDTO, listener: (VipPackageDTO?) -> Unit) {
        vipPackageDTO?.let { vipPackage ->
                if (GooglePayHelper.canPayWithGoogle()) {
                    var inappSkuList = arrayListOf<String>()
                    var subsSkuList = arrayListOf<String>()
                    vipPackage.prices?.forEach {
                        if (!it.productId.isNullOrEmpty()) {
                            it.productId?.let { sku ->
                                if (it.autoRenewal) subsSkuList.add(sku) else inappSkuList.add(
                                    sku
                                )
                            }
                        }
                    }
                    GooglePayHelper.setSkuDetailsList(inappSkuList,subsSkuList).let {
                        GooglePayHelper.getSkuDetailsList { skuList ->
                            if (!skuList.isNullOrEmpty()) {
                                vipPackage.vipPackageDTO.prices =
                                    processGoogleBilling(vipPackage, skuList)
                            }
                            vipBean = vipPackage.vipPackageDTO
                            listener.invoke(vipPackage)
                        }
                    }

                } else {
                    //非大陆用户且非大陆渠道，默认显示为美元价格
                    if (VipHelper.showOverseaPrice() && !NiimbotGlobal.isMainChannel()) {
//                    if (VipHelper.showOverseaPrice()) {
                        vipPackage.vipPackageDTO?.prices?.forEach { it.setOverseaPrice() }
                        vipBean = vipPackage.vipPackageDTO
                        listener.invoke(vipPackage)
                        BuriedHelper.trackEvent("show", "029_307")
                    } else {
                        vipPackage.vipPackageDTO.prices = processAlipayPrice(vipPackage)
                        vipBean = vipPackage.vipPackageDTO
                        listener.invoke(vipPackage)
                    }
                }
        }
    }

    /**
     * 从google play获取价格
     * @param vipPackageDTO VipPackageDTO
     * @param listener Function1<VipPackageDTO?, Unit>
     */
    private fun processGoogleBilling(
        vipPackageDTO: VipPackageDTO,
        skuDetailMap: Map<String, GoogleSkuDetail>?
    ): List<PriceBean> {
        var priceList = arrayListOf<PriceBean>()
        safeLet(vipPackageDTO, skuDetailMap) { vipDto, skuMap ->
            vipDto.prices.forEach { priceBean ->
                skuMap.get(priceBean.productId)?.let { skuDetail ->
//                    LogUtils.e("priceBean: ${priceBean.toJson()}, skuDetails: ${skuDetail.toJson()}")
                    regionCode = CurrencySymbol2CountryUtil.getCountryCode(skuDetail.priceCurrencyCode, TextHookUtil.getInstance().getSystemLocal().country)
                    recordRegionCode(regionCode)
                    if (skuDetail.isAutoRenewal() && skuDetail.introductoryPriceAmountMicros > 0 && skuDetail.introductoryPriceAmountMicros != skuDetail.priceAmountMicros) {
                        priceBean.salePrice = BigDecimal(skuDetail.introductoryPriceAmountMicros).divide(BigDecimal(1000000))
                        priceBean.originalPrice = BigDecimal(skuDetail.priceAmountMicros).divide(BigDecimal(1000000))
                        priceBean.googleOriginalPrice = priceBean.originalPrice.toFloat()
                        priceBean.showVipActivity = true
                    } else {
                        var rate = priceBean.getRate()
                        priceBean.salePrice = BigDecimal(skuDetail.priceAmountMicros).divide(BigDecimal(1000000))
                        priceBean.originalPrice = if(rate > BigDecimal(0)) priceBean.salePrice / rate else priceBean.salePrice
                        priceBean.googleOriginalPrice = priceBean.salePrice.toFloat()
                        priceBean.showVipActivity = priceBean.hasServerActivityStartExceptFirstPurchase()
                    }
                    priceBean.googlePriceSymbol = skuDetail.priceCurrencyCode
                    priceList.add(priceBean)
                }
            }
        }
        return priceList
    }

    private fun processAlipayPrice(vipPackageDTO: VipPackageDTO): List<PriceBean> {
        val priceList = arrayListOf<PriceBean>()
        vipPackageDTO.vipPackageDTO.prices.forEach { priceBean ->
            priceBean.showVipActivity = priceBean.hasServerActivityStart()
            priceList.add(priceBean)
        }
        return priceList
    }

    private fun recordRegionCode(regionCode: String) {
        PreferencesUtils.put("googleRegionCode", regionCode)
    }

}
