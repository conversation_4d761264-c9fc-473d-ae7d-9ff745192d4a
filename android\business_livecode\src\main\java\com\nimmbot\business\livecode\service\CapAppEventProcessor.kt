package com.nimmbot.business.livecode.service

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.core.app.ActivityCompat.finishAfterTransition
import com.blankj.utilcode.util.ActivityUtils
import com.blankj.utilcode.util.ImageUtils
import com.blankj.utilcode.util.LogUtils
import com.blankj.utilcode.util.ScreenUtils
import com.gengcon.connect.JCConnectionManager
import com.gengcon.print.draw.DrawIntentParams
import com.gengcon.print.draw.module.event.CloseCapPrintSettingEvent
import com.gengcon.print.draw.module.event.LargeTemplateSendEvent
import com.gengcon.print.draw.module.review.ReviewRequest
import com.gengcon.print.draw.print.PrintTaskExecutor
import com.gengcon.print.draw.print.event.UpdatePrintTemplateEvent
import com.gengcon.print.draw.util.ImageSdkUtils
import com.getcapacitor.JSArray
import com.getcapacitor.JSObject
import com.niimbot.appframework_library.BaseApplication
import com.niimbot.appframework_library.common.module.NiimbotDrawData
import com.niimbot.appframework_library.common.module.template.TemplateModuleLocal
import com.niimbot.appframework_library.common.util.permission.PermissionDialogUtils
import com.niimbot.appframework_library.common.util.permission.RequestCode
import com.niimbot.appframework_library.common.util.permission.XPermissionUtils
import com.niimbot.appframework_library.expand.safeLet
import com.niimbot.appframework_library.expand.startActivity
import com.niimbot.appframework_library.expand.toJson
import com.niimbot.appframework_library.messagebus.config.LeMessageIds
import com.niimbot.appframework_library.messagebus.manager.LeMessageManager
import com.niimbot.appframework_library.messagebus.message.LeMessage
import com.niimbot.appframework_library.protocol.connectui.DeviceConnectActivityConfig
import com.niimbot.appframework_library.protocol.template.NewScanActivityConfig
import com.niimbot.baselibrary.BuriedHelper
import com.niimbot.baselibrary.FlutterMethodInvokeManager
import com.niimbot.baselibrary.NiimbotGlobal
import com.niimbot.baselibrary.loading.GlobalLoadingHelper
import com.niimbot.baselibrary.user.LoginDataEnum
import com.niimbot.okgolibrary.okgo.utils.HttpTokenUtils
import com.niimbot.utiliylibray.util.SuperUtils
import com.niimbot.utiliylibray.util.SystemUtil
import com.niimbot.utiliylibray.util.any2Json
import com.niimbot.utiliylibray.util.json2Array
import com.niimbot.viplibrary.VipDialog
import com.nimmbot.business.livecode.CapAppEventHandler
import com.nimmbot.business.livecode.CapAppHelper
import com.nimmbot.business.livecode.CapEntryHelper
import com.nimmbot.business.livecode.GenerateImageWithLoadingActivity
import com.nimmbot.business.livecode.R
import com.qyx.languagelibrary.utils.TextHookUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import melon.south.com.baselibrary.local.module.GoodsBean
import melon.south.com.baselibrary.util.showToast
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import org.json.JSONArray


/**
 * @ClassName: UniappEventProcessor
 * @Author: Liuxiaowen
 * @Date: 2022/8/1 14:06
 * @Description:
 */
class CapAppEventProcessor : CapAppEventHandler.OnCapAppEventHandler() {
    /**唤起购买vip界面*/
    private val EVENT_OPEN_VIP_PAGE = "open-bug-vip"

    /**上传埋点数据*/
    private val EVENT_EVENT_TRACK = "event-track"

    /**小程序关闭事件*/
    private val EVENT_UNIAPP_CLOSE = "uniapp-close"
    /**调用原生文件上传*/
    private val EVENT_UPLOAD_FILE = "upload-file"

    /**打开其他小程序*/
    private val EVENT_OPEN_OTHER_UNIAPP = "navigate-to-min-program"
    /**小程序启动配置*/
    private val EVENT_GET_LAUNCH_CONFIG = "get-launch-configuration"
    /**小程序启动配置*/
    private val EVENT_DOWNLOAD_FILE = "download-file"
    /**安装小程序*/
    private val EVENT_INSTALL = "install"
    /**重启小程序*/
    private val EVENT_RESTART = "restart"
    /**选择使用高级二维码*/
    private val EVENT_SELECT_LIVE_CODE = "select-live-code"
    /**二维码信息发生改变*/
    private val EVENT_LIVE_CODE_INFO_CHANGED = "live-code-info-changed"
    /**操作事件 用于埋点等其它相关操作*/
    private val EVENT_LIVE_CODE_OPERATE = "live-code-operate-event"
    /**二维码转换为高级二维码*/
    private val EVENT_QRCODE_TO_LIVE_CODE = "qrcode-to-live-code"
    /**通过其他小程序消息关闭小程序*/
    private val EVENT_KILL_UNIAPP = "kill-uniapp"
    /**选择使用表单*/
    private val EVENT_SELECT_SHEET = "select-sheet"
    /**表单信息发生改变*/
    private val EVENT_SHEET_INFO_CHANGED = "sheet-info-changed"
    /**操作事件 用于埋点等其它相关操作*/
    private val EVENT_SHEET_OPERATE = "sheet-operate-event"
    /**唤起扫一扫页面*/
    private val EVENT_OPEN_SCAN_PAGE = "open-scan-page"
    /**唤起打印页面*/
    private val EVENT_OPEN_PRINT_PAGE = "open-print-page"
    /**断开打印机连接事件*/
    private val EVENT_disconnect_printer = "disconnect-printer"
    /**刷新正在打印的模板数据*/
    private val EVENT_UPDATE_PRINT_TEMPLATE = "update-print-template"
    /**生成wifi码预览图*/
    private val EVENT_GENERATE_WIFI_CODE_PREVIEW = "generate-wifi-code"

    private val EVENT_GENERATE_TEMPLATE_PREVIEW_WITH_LAYOUT = "generate-template-preview-with-layout"
    private val EVENT_JUMPTO_DRAWING_BOARD = "jump-to-drawing-board"
    private val EVENT_OPEN_PRINT_PAGE_WITH_LAYOUT = "open-print-page-with-layout"
    private val EVENT_CLOSE_PRINT_PAGE = "close-print-page"
    //餐饮效期小程序批量获取预览图
    private val EVENT_BATCH_GENERATE_PREVIEW_WITH_LAYOUT = "batch-generate-preview-with-layout"

    //餐饮效期小程序批量获取预览图
    private val EVENT_BATCH_GENERATE_PREVIEW_WITH_LAYOUTS = "batch-generate-preview-with-layouts"
    //会议小程序数据源模版打印
    private val EVENT_START_BATCH_PRINT = "start-batch-print"
    //会议小程序签到模版生成
    private val EVENT_GENERATE_TEMPLATE_WITH_LAYOUT = "generate-template-with-layout"
    var checkPrinterConnectStatus: ((String) -> Boolean)? = null

    companion object {
        var imageDataForMiniAppCatering: ((JSObject) -> Unit)? = null
    }

    override fun handleEvent(appId: String?, event: String?, data: Any?, listener: ((String,Boolean) -> Unit)?) {
        appId?.let {
            val value = data ?: "{}"
            when (event) {
                EVENT_OPEN_VIP_PAGE -> openVipPage(appId, value)
                EVENT_EVENT_TRACK -> eventTrack(appId, value)
                EVENT_UNIAPP_CLOSE -> ""
                EVENT_UPLOAD_FILE -> ""
                EVENT_OPEN_OTHER_UNIAPP -> openOtherUniapp(appId, value)
                EVENT_GET_LAUNCH_CONFIG -> getLaunchConfig(appId, value, listener)
                EVENT_DOWNLOAD_FILE -> ""
                EVENT_INSTALL -> ""
                EVENT_RESTART -> restartCapApp()

                EVENT_SELECT_LIVE_CODE -> selectLiveCode(appId, value)
                EVENT_LIVE_CODE_INFO_CHANGED -> liveCodeInfoChanged(appId, value)
                EVENT_LIVE_CODE_OPERATE -> liveCodeOperateReport(appId, value)
                EVENT_QRCODE_TO_LIVE_CODE -> qrcode2LiveCode(appId, value)
                EVENT_KILL_UNIAPP -> killUniapp(appId)
                EVENT_SELECT_SHEET -> selectSheet(appId, value)
                EVENT_SHEET_INFO_CHANGED -> sheetInfoChanged(appId, value)
                EVENT_SHEET_OPERATE -> sheetOperateReport(appId, value)
                EVENT_OPEN_SCAN_PAGE         -> openScanPage(appId, value)
                EVENT_OPEN_PRINT_PAGE         -> openPrintPage(appId, value)
                EVENT_disconnect_printer         -> disconnectBT(appId, value)
                EVENT_UPDATE_PRINT_TEMPLATE  -> updatePrintTemplate(appId, value)
                EVENT_GENERATE_WIFI_CODE_PREVIEW  -> generateWifiCodePreviewImage(appId, value)
                EVENT_OPEN_PRINT_PAGE_WITH_LAYOUT         -> openPrintPageWithLayout(appId, value)
                EVENT_JUMPTO_DRAWING_BOARD         -> jumpToDrawingBoard(appId, value)
                EVENT_GENERATE_TEMPLATE_PREVIEW_WITH_LAYOUT         -> generateSchemeTemplatePreviewImage(appId, value)
                EVENT_CLOSE_PRINT_PAGE         -> closeCapPrintSettingPage(appId, value)
                EVENT_BATCH_GENERATE_PREVIEW_WITH_LAYOUT -> batchGeneratePreviewImage(appId, value,listener)
                EVENT_BATCH_GENERATE_PREVIEW_WITH_LAYOUTS -> batchGeneratePreviewImageByTemplates(appId, value,listener)
                EVENT_START_BATCH_PRINT -> startBatchPrint(appId, value)
                EVENT_GENERATE_TEMPLATE_WITH_LAYOUT -> generateTemplateWithLayout(appId, value,listener)
                else                                             -> ""
            }
        }
    }

    private fun closeCapPrintSettingPage(appId: String, data: Any) {
        //通知flutter关闭掉打印设置弹窗页面
        val event = com.niimbot.fastjson.JSONObject()
        event["action"] = "closePrintSettingDialog"
        EventBus.getDefault().post(any2Json(event))
    }

    private fun disconnectBT(appId: String, data: Any) {
        JCConnectionManager.getInstance().closeConnectAndClearAutoConnect()
    }

    private fun openPrintPage(appId: String, data: Any) {
        debounceClick(scope = MainScope(), param = data) {
            try {
                var theme = (data as JSONObject).getString("theme")
                if(theme=="red"){
                    theme="#FB4B42"
                }
                PrintTaskExecutor.capTaskId = data.getString("taskId")
                val printData = NiimbotDrawData()
                val templateData = data.getString("templateData")
                GlobalScope.launch(Dispatchers.Main) {
                    withContext(Dispatchers.Default){
                        var result = FlutterMethodInvokeManager.downloadTemplateResSuspend(templateData)
                        result?.let {
                            printData.niimbotTemplate =
                                TemplateModuleLocal.fromJson(it)!!
                        }

                    }
                    var printScene = "mini_app"
                    if(appId=="__CAP__DE09E3D"||appId=="__CAP__7863BFB"){
                        printScene="mini_app"
                    }
                    var appName=  NiimbotGlobal.getUniappTitle(appId)
                    var niimbotDrawDataJson = any2Json(printData)
                    NiimbotGlobal.gotoFlutterPage(
                        "printSettingDialog",
                        hashMapOf(
                            "printScene" to printScene,
                            "showRfid" to true,
                            "niimbotTemplate" to niimbotDrawDataJson,
                            "uniAppInfo" to hashMapOf("uniAppId" to appId, "uniAppName" to appName,"meetingId" to "",
                                "themeColor" to theme),
                        ), isTransParent = true
                    )

                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun openPrintPageWithLayout(appId: String, data: Any) {
        debounceClick(delayMillis = 1500L,scope = MainScope(), param = data){
            try{
                val theme = (data as JSONObject).getString("theme")
                val layoutScheme = (data as JSONObject).getString("layoutSchema") ?: ""
                val formData = data.getString("formData") ?: ""
                val isRePrint = data.optBoolean("isRePrint") ?: false
                val labelId = data.getString("labelId") ?: ""
                //根据layoutScheme版式信息生成版式模版
                //再用formData表单数据填充到版式模版中
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeTemplate(layoutScheme,formData, labelId = labelId, isPreview = true,{
                    val printData = NiimbotDrawData()
                    printData.niimbotTemplate = TemplateModuleLocal.fromJson(it)!!

                    val result = com.niimbot.fastjson.JSONObject()
                    result.put("theme", theme)
                    result.put("uniappId", appId)
                    result.put("isRePrint", isRePrint)
                    result.put("labelId", labelId)
                    result.put("template", it)
                    EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                        put("action", "openPrintPageWithLayout")
                        put("data", result)
                    }.toJSONString())
                },{})

            } catch(e: Exception) {e.printStackTrace()}
        }
    }

    private fun jumpToDrawingBoard(appId: String, data: Any) {
        debounceClick(scope = MainScope(), param = data){
            try{
                GlobalLoadingHelper.showLoading(ActivityUtils.getTopActivity())
                val layoutScheme = (data as JSONObject).getString("layoutSchema") ?: ""
                val formData = data.getString("formData") ?: ""
                val labelId = data.getString("labelId") ?: ""

                //根据layoutScheme版式信息生成版式模版
                //再用formData表单数据填充到版式模版中
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeTemplate(layoutScheme,formData,labelId=labelId, isPreview = false,{
                    val result = com.niimbot.fastjson.JSONObject()
                    result.put("template", it)
                    result.put("labelId", labelId)
                    result.put("fromUniApp", true)
                    EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                        put("action", "jumpToCanvas")
                        put("data", result)
                    }.toJSONString())
                },{})

            } catch(e: Exception) {
                e.printStackTrace()
                GlobalLoadingHelper.dismissLoading()
            }
        }
    }

    private fun generateSchemeTemplatePreviewImage(appId: String, data: Any) {
        debounceClick(scope = MainScope(), param = data){
            try{
                val layoutScheme = (data as JSONObject).getString("layoutSchema") ?: ""
                val formData = data.getString("formData") ?: ""
                //根据layoutScheme版式信息生成版式模版
                //再用formData表单数据填充到版式模版中
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeTemplate(layoutScheme, formData, labelId = "", isPreview = true,{
                    val templateModuleLocal = TemplateModuleLocal.fromJson(it)!!
                    val miniRate = ScreenUtils.getAppScreenWidth()/templateModuleLocal.width
                    ReviewRequest.requestData(templateModuleLocal, 1,  1, miniRate){
                        GlobalScope.launch(Dispatchers.Main) {
                            val bitmap = it.data
                            val imageData = if(bitmap != null) ImageUtils.bitmap2Bytes(bitmap) else ByteArray(0)
                            val event = com.niimbot.fastjson.JSONObject()
                            event["action"] = "imageDataForIonic"
                            event["imageData"] = android.util.Base64.encodeToString(imageData, android.util.Base64.DEFAULT)
                            EventBus.getDefault().post(any2Json(event))
                        }
                    }
                },{})

            } catch(e: Exception) {e.printStackTrace()}
        }
    }

    private fun startBatchPrint(appId: String, data: Any) {
        debounceClick(scope = MainScope(), param = data) {
            try{
                if(appId == "__CAP__SPR889G") {
                    if(checkPrinterConnectStatus?.invoke(appId) == false) {
                        return@debounceClick
                    }
                }
//                com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading()
                val layoutJson = (data as JSONObject).getString("layoutJson") ?: ""
                // 使用方式
                var colNames = arrayOf<String>()
                var cateringColumnNames = arrayListOf<HashMap<String, Any>>()
                var isCateringCap = false
                if(appId == "__CAP__SPR889G") {
                    val jsonArray = data["colNames"] as JSONArray
                    for(i in 0 until jsonArray.length()) {
                        val item = jsonArray[i] as JSONObject
                        val map = HashMap<String, Any>()
                        map["type"] = item.getString("type")
                        map["name"] = item.getString("name")
                        map["alias"] = item.getString("alias")
                        cateringColumnNames.add(map)
                    }
                    isCateringCap = true
                }
                else {
                    colNames = data.getStringArray("colNames")
                }
                val datasourceId = (data as JSONObject).getString("datasourceId") ?: ""
                val rowDataIds = data.getStringArray("rowDataIds")
                val labelId = (data as JSONObject).getString("labelId") ?: ""
                var theme = "green"
                if((data as JSONObject).has("theme")) {
                    theme = (data as JSONObject).getString("theme") ?: "green"
                }
                var isDirectPrint = false
                if((data as JSONObject).has("isDirectPrint")) {
                    isDirectPrint = (data as JSONObject).getBoolean("isDirectPrint")
                }
                val codeId = data.getString("codeId") ?: ""

                val params = mutableMapOf<String, Any>().apply {
                    put("appId", appId)
                    put("layoutJson", layoutJson)
                    put("colNames", colNames)
                    put("isCateringCap", isCateringCap)
                    put("cateringColumnNames", cateringColumnNames)
                    put("datasourceId", datasourceId)
                    put("rowDataIds", rowDataIds)
                    put("labelId", labelId)
                }
                if(isDirectPrint) {
                    com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading()
                    com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeDataSourceTemplate(params = com.niimbot.utiliylibray.util.any2Json(params),
                        {
                            val result = com.niimbot.fastjson.JSONObject()
                            result.put("theme", theme)
                            result.put("uniappId", appId)
                            result.put("labelId", labelId)
                            result.put("codeId", codeId)
                            result.put("template", it)
                            EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                                put("action", "directStartDatasourceTemplatePrint")
                                put("data", result)
                            }.toJSONString())
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        }, {
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        })
                }
                else {
                    com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeDataSourceTemplate(
                        params = com.niimbot.utiliylibray.util.any2Json(params),
                        {
//                    val templateModuleLocal = TemplateModuleLocal.fromJson(it)!!

                            val printData = NiimbotDrawData()
                            printData.niimbotTemplate = TemplateModuleLocal.fromJson(it)!!

                            val result = com.niimbot.fastjson.JSONObject()
                            result.put("theme", theme)
                            result.put("uniappId", appId)
                            result.put("labelId", labelId)
                            result.put("codeId", codeId)
                            result.put("template", it)
                            EventBus.getDefault().post(com.niimbot.fastjson.JSONObject().apply {
                                put("action", "startDatasourceTemplatePrint")
                                put("data", result)
                            }.toJSONString())
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        },
                        {
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        })
                }

            } catch(e: Exception) {
                e.printStackTrace()
            }
        }
    }
    private fun generateTemplateWithLayout(appId: String, data: Any,listener: ((String,Boolean) -> Unit)? = null) {
        debounceClick(scope = MainScope(), param = data){
            try{
//                com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading()
                val layoutJson = (data as JSONObject).getString("layoutJson") ?: ""
                // 使用方式
                val colNames = data.getStringArray("colNames")
                val labelId = (data as JSONObject).getString("labelId") ?: ""

                val params = mutableMapOf<String, Any>().apply {
                    put("appId", appId)
                    put("layoutJson", layoutJson)
                    put("colNames", colNames)
                    put("labelId", labelId)
                }
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generateTemplateWithLayout(params = com.niimbot.utiliylibray.util.any2Json(params),{
//                    val templateModuleLocal = TemplateModuleLocal.fromJson(it)!!
                    val params = JSObject()
                        .apply {
                            put("result", 1)
                            put("template", it)
                        }
                    listener?.invoke(params.toString(),true)
                    com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()

                },{
                    listener?.invoke("生成版式预览图失败",false)
                    com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                })

            } catch(e: Exception) {
                e.printStackTrace()
                listener?.invoke("生成版式预览图失败",false)
                com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
            }
        }
    }

    private fun batchGeneratePreviewImage(appId: String, data: Any,listener: ((String,Boolean) -> Unit)? = null) {
        debounceClick(scope = MainScope(), param = data){
            try{
//                com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading()
                val areaLayoutSchema = (data as JSONObject).getString("areaLayoutSchema") ?: ""
                val layoutJson = data.getString("layoutJson") ?: ""
                // 使用方式
                val colNames = data.getStringArray("colNames")
                val datasourceId = data.getString("datasourceId") ?: ""
                val rowDataIds = data.getStringArray("rowDataIds")
                val labelId = data.getString("labelId") ?: ""
                val layoutId = data.getString("layoutId") ?: ""

                val params = mutableMapOf<String, Any>().apply {
                    put("appId", appId)
                    put("areaLayoutSchema", areaLayoutSchema)
                    put("layoutJson", layoutJson)
                    put("colNames", colNames)
                    put("datasourceId", datasourceId)
                    put("rowDataIds", rowDataIds)
                    put("labelId", labelId)
                }
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generateLayoutSchemeDataSourceTemplate(params = com.niimbot.utiliylibray.util.any2Json(params),{
                    val templateModuleLocal = TemplateModuleLocal.fromJson(it)!!
                    val miniRate = ScreenUtils.getAppScreenWidth()/templateModuleLocal.width
                    GlobalScope.launch(Dispatchers.IO) {
                        val buffers = JSArray()
                        for (i in rowDataIds.indices){
                            val bitmap = ImageSdkUtils.generateThumbnailImage(templateModuleLocal, currentIndex = i+1, previewRate = miniRate)
                            val imageData = if(bitmap != null) ImageUtils.bitmap2Bytes(bitmap) else ByteArray(0)
                            val iamgeDataBase64 = android.util.Base64.encodeToString(
                                imageData,
                                android.util.Base64.DEFAULT
                            )
                            var imageItem = JSObject()
                            imageItem.put("id", rowDataIds.get(i))
                            imageItem.put("base64", iamgeDataBase64)
                            buffers.put(imageItem)
                        }
                        val params = JSObject()
                            .apply {
                                put("appId", appId)
                                put("template", it)
                                put("previewImages", buffers)
                                put("layoutId", layoutId)
                            }
                        withContext(Dispatchers.Main){
                            listener?.invoke(params.toString(),true)
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        }
                    }
                },{
                    listener?.invoke("生成批量预览图失败",false)
                    com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                })

            } catch(e: Exception) {
                e.printStackTrace()
                listener?.invoke("生成批量预览图失败",false)
                com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
            }
        }
    }
    private fun batchGeneratePreviewImageByTemplates(appId: String, data: Any,listener: ((String,Boolean) -> Unit)? = null) {
        debounceClick(scope = MainScope(), param = data){
            try{
//                com.niimbot.baselibrary.loading.GlobalLoadingHelper.showLoading()
                val resultMap = mutableMapOf<String, Any>()

                // 解析基础字段
                resultMap["labelId"] = (data as JSONObject).optString("labelId", "")

                // 解析 layoutJsonList
                val layoutList = data.optJSONArray("layoutJsonList")?.let { jsonArray ->
                    List(jsonArray.length()) { index ->
                        jsonArray.optJSONObject(index)?.let { obj ->
                            mapOf(
                                "id" to obj.optString("id", ""),
                                "layoutJson" to obj.optString("layoutJson", "")
                            )
                        } ?: emptyMap<String, String>()
                    }
                } ?: emptyList()
                resultMap["layoutJsonList"] = layoutList

                val cateringColumnNames = arrayListOf<HashMap<String, Any>>()
                if(appId == "__CAP__SPR889G") {
                    val jsonArray = data["colNames"] as JSONArray
                    for(i in 0 until jsonArray.length()) {
                        val item = jsonArray[i] as JSONObject
                        val map = HashMap<String, Any>()
                        map["type"] = item.getString("type")
                        map["name"] = item.getString("name")
                        map["alias"] = item.getString("alias")
                        cateringColumnNames.add(map)
                    }
                    resultMap["cateringColumnNames"] = cateringColumnNames
                    resultMap["isCateringCap"] = true
                    resultMap["colNames"] = arrayOf<String>()
                }
                else {
                    resultMap["colNames"] = data.getStringArray("colNames")
                    resultMap["cateringColumnNames"] = cateringColumnNames
                    resultMap["isCateringCap"] = false
                }
                resultMap["rowData"] = data.getStringArray("rowData")
                com.niimbot.baselibrary.FlutterMethodInvokeManager.generatePreviewTemplateWithLayouts(params = com.niimbot.utiliylibray.util.any2Json(resultMap),{

                    GlobalScope.launch(Dispatchers.IO) {
                        val buffers = JSArray()
                        for(item in it){
                            val templateModuleLocal = TemplateModuleLocal.fromJson(item)!!
                            val miniRate = ScreenUtils.getAppScreenWidth()/templateModuleLocal.width
                            val previewImage = ImageSdkUtils.generateThumbnailImage(templateModuleLocal, currentIndex = 1, previewRate = miniRate)

                            val bitmap = previewImage
                            val imageData = if(bitmap != null) ImageUtils.bitmap2Bytes(bitmap) else ByteArray(0)
                            val iamgeDataBase64 = android.util.Base64.encodeToString(
                                imageData,
                                android.util.Base64.DEFAULT
                            )
                            var imageItem = JSObject()
                            imageItem.put("id",  layoutList
                                .getOrNull(it.indexOf(item))
                                ?.get("id")
                                ?: ""   )
                            imageItem.put("base64", iamgeDataBase64)
                            buffers.put(imageItem)
                        }
                        val params = JSObject()
                            .apply {
                                put("appId", appId)
                                put("previewImages", buffers)
                            }
                        withContext(Dispatchers.Main){
//                            imageDataForMiniAppCatering?.invoke(params)
                            listener?.invoke(params.toString(),true)
                            com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                        }

                    }



                },{
                    listener?.invoke("生成多版式预览图失败",false)
                    com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
                })

            } catch(e: Exception) {
                e.printStackTrace()
                listener?.invoke("生成多版式预览图失败",false)
                com.niimbot.baselibrary.loading.GlobalLoadingHelper.dismissLoading()
            }
        }
    }
    fun JSONObject.getStringArray(key: String): Array<String> {
        return optJSONArray(key)?.let { jsonArray ->
            Array(jsonArray.length()) { index ->
                jsonArray.optString(index, "") // 设置默认值
            }
        } ?: emptyArray()
    }

    private fun restartCapApp() {
        try {
            BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                it.runOnUiThread {
                    CapAppHelper.restartCapApp(it)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getLaunchConfig(appId: String, data: Any, listener: ((String,Boolean) -> Unit)? = null) {
        val json = JSONObject()
        json.put("appId", appId)
        json.put("appKey", CapAppHelper.capApp?.appKey)
        json.put("userId", LoginDataEnum.id)
        json.put("niimbotUserAgent", SystemUtil.getUserAgent(SuperUtils.superContext))
        json.put("appSecret", CapAppHelper.capApp?.appSecret)
        json.put("languageCode", TextHookUtil.getInstance().languageName)
        json.put("token", HttpTokenUtils.getToken())
        listener?.invoke(json.toString(),true)
    }

    private fun openVipPage(appId: String, data: Any) {
        debounceClick(scope = MainScope(), param = data){
            var source = try{
                (data as JSONObject).optString("source")
            } catch(e: Exception) {
                e.printStackTrace()
                data.toString()
            }
            try{
                BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                    VipDialog.showVipPayDialog(it, "", fromUnimp = true)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun eventTrack(appId: String?, data: Any?) {
        try{
            val jsonObject = data as JSONObject
            val params: JSONObject? = jsonObject.optJSONObject("params") ?: null
            BuriedHelper.trackEventWithJson(
                jsonObject.optString("eventCode"),
                jsonObject.optString("posCode"),
                params
            )
        } catch(e: Exception) {e.printStackTrace()}
    }


    private fun openOtherUniapp(appId: String, data: Any) {
        try{
            val jsonObject = data as JSONObject
            val uniappId = jsonObject.optString("targetAppId")
            val directPath = jsonObject.optString("path")
            val closeCurrentUnimp = jsonObject.optBoolean("replace") ?: false
            if (uniappId.isNullOrBlank()) {
                LogUtils.e("跳转的小程序id为空")
            } else {
                NiimbotGlobal.gotoUniapp(
                    uniappId,
                    directPath,
                    closeCurrentUnimp = closeCurrentUnimp
                )
                if (closeCurrentUnimp) {
                    CapAppHelper.closeCurrentApp()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun selectLiveCode(appId: String, data: Any) {
        try {
            LogUtils.e("invoke-->selectLiveCode: $data")
            val params = data as JSONObject
            if (null != params) {
                val jsonObject = com.niimbot.fastjson.JSONObject()
                jsonObject["action"] = "tag_create"
                jsonObject["data"] = params.toString()
                EventBus.getDefault().post(any2Json(jsonObject))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun liveCodeInfoChanged(appId: String, data: Any) {
        LogUtils.e("invoke-->liveCodeInfoChanged: $data")
        try{
            val params = data as JSONObject
            if (null != params) {
                val jsonObject = com.niimbot.fastjson.JSONObject()
                jsonObject["action"] = "tag_fresh"
                jsonObject["data"] = params.toString()
                EventBus.getDefault().post(any2Json(jsonObject))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    private fun liveCodeOperateReport(appId: String, data: Any) {
        LogUtils.e("invoke-->liveCodeOperateReport: $data")

    }

    private fun qrcode2LiveCode(appId: String, data: Any) {
        LogUtils.e("invoke-->qrcode2LiveCode: $data")
        try{
            val params = data as JSONObject
            if (null != params) {
                val jsonObject = com.niimbot.fastjson.JSONObject()
                jsonObject["action"] = "tag_create"
                jsonObject["data"] = params.toString()
                EventBus.getDefault().post(any2Json(jsonObject))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun selectSheet(appId: String, data: Any) {
        try {
            LogUtils.e("invoke-->selectSheet: $data")
            val params = data as JSONObject
            if (null != params) {
                val jsonObject = com.niimbot.fastjson.JSONObject()
                jsonObject["action"] = "tag_sheet_create"
                jsonObject["data"] = params.toString()
                EventBus.getDefault().post(any2Json(jsonObject))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sheetInfoChanged(appId: String, data: Any) {
        LogUtils.e("invoke-->sheetInfoChanged: $data")
        try {
            val params = data as JSONObject
            if (null != params) {
                val jsonObject = com.niimbot.fastjson.JSONObject()
                jsonObject["action"] = "tag_sheet_fresh"
                jsonObject["data"] = params.toString()
                EventBus.getDefault().post(any2Json(jsonObject))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sheetOperateReport(appId: String, data: Any) {
        LogUtils.e("invoke-->sheetOperateReport: $data")

    }

    private fun updatePrintTemplate(appId: String, data: Any) {
        try {
            val printData = NiimbotDrawData()
            printData.niimbotTemplate = TemplateModuleLocal.fromJson((data as JSONObject).getString("template"))!!
            val event = UpdatePrintTemplateEvent()
            event.templateId = printData.niimbotTemplate.id
            event.batchGoodsList = printData.batchGoodsList
            event.templateModuleLocal = printData.niimbotTemplate
            EventBus.getDefault().post(event)
        } catch(e: Exception) {
            e.printStackTrace()
        }
    }

    private fun generateWifiCodePreviewImage(appId: String, data: Any) {
        LogUtils.e("invoke-->generateWifiCodePreviewImage: $data")
        try{
            var params = data as JSONObject
            var labelId = params.getString("labelId")
            var templateId = params.getString("templateId")
            var templateJson = params.getString("templateJson")
            var title = params.getString("title")
            var wifiCodeSceneId = params.getString("appletCodeId")
            safeLet(wifiCodeSceneId, labelId){tp, cs ->
                BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                    it.startActivity(Intent().apply {
                        putExtra("wifiCode", true)
                        putExtra("templateId", templateId)
                        putExtra("labelId", labelId)
                        putExtra("wifiCodeSceneId", wifiCodeSceneId)
                        putExtra("title", title)
                        setClass(it, GenerateImageWithLoadingActivity::class.java)//跳转宿主构建的activity
                    })
                }
            }
        } catch(e: Exception) {e.printStackTrace()}
    }

    private fun openScanPage(appId: String, data: Any) {
        try {
            debounceClick(scope = MainScope(), param = data) {
                BaseApplication.getInstance().mCurShowActivityReference?.get()?.let {
                    PermissionDialogUtils.showCameraPermissionDialog(
                        it,
                        RequestCode.MORE,
                        object : XPermissionUtils.OnPermissionListener {
                            override fun onPermissionGranted() {
                                LeMessageManager.getInstance().dispatchMessage(
                                    LeMessage(
                                        LeMessageIds.MSG_ACTION_GO_ACTIVITY,
                                        NewScanActivityConfig(it).apply {
                                            var scanType = 0
                                            var customTips = ""
                                            try{
                                                val scanTypeStr = (data as JSONObject).getString("type")
                                                if(scanTypeStr == "scanResult"){
                                                    scanType = 2
                                                }else if(scanTypeStr == "label"){
                                                    scanType = 1
                                                }else{
                                                    scanType = 0
                                                }
//                                                scanType = if( scanTypeStr== "label") 1 else 0
                                                customTips = data.getString("title")
                                            } catch (e: Exception) {
                                                e.printStackTrace()
                                                0
                                            }
                                            this.intent.putExtra("scanType", scanType)
                                            this.intent.putExtra("customTips", customTips)
                                            this.intent.putExtra("trackSource", 5)
                                            this.intent.putExtra("fromUnimp", true)
                                        }
                                    )
                                )
                            }

                            override fun onPermissionDenied(
                                deniedPermissions: Array<String>,
                                alwaysDenied: Boolean
                            ) {
                                showToast("app01309")
                            }
                        })
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun killUniapp(appId: String?) {
        if (appId != null) {
            CapEntryHelper.getEntryActivity(appId)?.finishAfterTransition()
        }
    }
}
